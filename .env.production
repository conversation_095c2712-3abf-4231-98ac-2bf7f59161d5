# Qalb Healing Backend Environment Variables
# For testing purposes - replace with actual values for production

# Server Configuration
PORT=3333
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Supabase Configuration (Test values - replace with actual)
SUPABASE_URL=https://test-project.supabase.co
SUPABASE_ANON_KEY=test-anon-key-replace-with-actual

# Redis Configuration (Optional for caching)
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=test-jwt-secret-replace-with-actual

# OpenAI Configuration (for AI service integration)
OPENAI_API_KEY=test-openai-key-replace-with-actual

# Logging
LOG_LEVEL=info


# This was inserted by `prisma init`:
[object Promise]