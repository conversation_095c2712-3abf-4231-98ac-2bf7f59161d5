# Onboarding Session Management Fix

## Problem
Every time a user hits `http://localhost:8081/onboarding`, a fresh onboarding session was being created instead of resuming the existing incomplete session. This caused users to lose their progress and start from the beginning each time.

## Root Cause
The `OnboardingService.startOnboarding()` method was always creating a new session without checking for existing incomplete sessions for the user.

## Solution
Modified the onboarding flow to:

1. **Check for existing incomplete sessions** before creating new ones
2. **Resume existing sessions** when found
3. **Provide explicit restart functionality** when users want to start fresh

## Changes Made

### 1. Updated `OnboardingService.startOnboarding()`
- Now checks for existing incomplete sessions (where `completedAt` and `abandonedAt` are null)
- Returns existing session if found, or creates new one if none exists
- Added `forceRestart` parameter to explicitly abandon existing sessions and start fresh
- Returns both session data and a `resumed` flag to indicate whether session was resumed

### 2. Enhanced API Endpoints

#### `/api/onboarding/start` (Modified)
- Now resumes existing incomplete sessions by default
- Added optional `forceRestart` parameter to force a new session
- Returns `resumed: boolean` in response to indicate if session was resumed

#### `/api/onboarding/restart` (New)
- Explicitly abandons any existing incomplete sessions
- Always starts a fresh onboarding session
- Useful when users want to restart their onboarding from scratch

#### `/api/onboarding/session-status` (New)
- Checks if user has any incomplete onboarding sessions
- Returns session details if incomplete session exists
- Useful for frontend to determine whether to show \"Resume\" or \"Start\" options

### 3. Added Session Management Methods
- `resetOnboardingSession(userId)`: Marks all incomplete sessions as abandoned
- `abandonSession(sessionId, reason)`: Abandons a specific session with reason

## API Usage Examples

### Start/Resume Onboarding (Default Behavior)
```bash
POST /api/onboarding/start
Authorization: Bearer <token>
Content-Type: application/json

{
  \"deviceInfo\": {
    \"platform\": \"web\",
    \"browser\": \"Chrome\"
  }
}
```

Response:
```json
{
  \"status\": \"success\",
  \"data\": {
    \"session\": {
      \"sessionId\": \"onb_1234567890_abc123\",
      \"startedAt\": \"2024-01-15T10:30:00Z\",
      \"currentStep\": \"mental_health_awareness\"
    },
    \"question\": { /* current question data */ },
    \"resumed\": true
  }
}
```

### Force Restart Onboarding
```bash
POST /api/onboarding/start
Authorization: Bearer <token>
Content-Type: application/json

{
  \"forceRestart\": true,
  \"deviceInfo\": {
    \"platform\": \"web\",
    \"browser\": \"Chrome\"
  }
}
```

### Explicit Restart (Alternative)
```bash
POST /api/onboarding/restart
Authorization: Bearer <token>
Content-Type: application/json

{
  \"deviceInfo\": {
    \"platform\": \"web\",
    \"browser\": \"Chrome\"
  }
}
```

### Check Session Status
```bash
GET /api/onboarding/session-status
Authorization: Bearer <token>
```

Response:
```json
{
  \"status\": \"success\",
  \"data\": {
    \"hasIncompleteSession\": true,
    \"session\": {
      \"sessionId\": \"onb_1234567890_abc123\",
      \"currentStep\": \"mental_health_awareness\",
      \"startedAt\": \"2024-01-15T10:30:00Z\",
      \"totalTimeSpent\": 120
    }
  }
}
```

## Database Schema
The fix utilizes existing fields in the `onboarding_sessions` table:
- `completedAt`: NULL for incomplete sessions
- `abandonedAt`: NULL for active sessions, timestamp when abandoned
- `abandonmentReason`: Reason for abandonment (e.g., 'admin_reset', 'user_abandoned')

## Frontend Integration Recommendations

1. **Check session status** before showing onboarding UI
2. **Show appropriate buttons** based on session state:
   - \"Continue Onboarding\" if incomplete session exists
   - \"Start Onboarding\" if no session exists
   - \"Restart Onboarding\" as an option for users who want to start fresh

3. **Handle resumed sessions** by showing current progress and allowing continuation from the current step

## Testing
To test the fix:

1. Start onboarding: `POST /api/onboarding/start`
2. Complete a few steps: `POST /api/onboarding/respond`
3. Navigate away and return
4. Hit onboarding endpoint again: `POST /api/onboarding/start`
5. Verify that the same session is resumed with the correct current step

## Backward Compatibility
This change is backward compatible. Existing API calls will work as before, but now with improved session management.