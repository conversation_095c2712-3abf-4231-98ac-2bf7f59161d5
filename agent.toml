# Version of the agent configuration standard
version = "1.0"

# Model to use for agent execution
model = "claude-4-sonnet"

# Import individual agent configuration files
imports = [
    "agents/bonus-joker.toml",
    "agents/bonus-studio-coach.toml",
    "agents/customer-success-manager.toml",
    "agents/data-scientist.toml",
    "agents/database-administrator.toml",
    "agents/design-brand-guardian.toml",
    "agents/design-ui-designer.toml",
    "agents/design-ux-researcher.toml",
    "agents/design-visual-storyteller.toml",
    "agents/design-whimsy-injector.toml",
    "agents/engineering-ai-engineer.toml",
    "agents/engineering-backend-architect.toml",
    "agents/engineering-devops-automator.toml",
    "agents/engineering-frontend-developer.toml",
    "agents/engineering-infrastructure-maintainer.toml",
    "agents/engineering-mobile-app-builder.toml",
    "agents/engineering-rapid-prototyper.toml",
    "agents/engineering-security-auditor.toml",
    "agents/engineering-test-writer-fixer.toml",
    "agents/fundraising-pitch-deck-crafter.toml",
    "agents/marketing-app-store-optimizer.toml",
    "agents/marketing-content-creator.toml",
    "agents/marketing-growth-hacker.toml",
    "agents/marketing-instagram-curator.toml",
    "agents/marketing-reddit-community-builder.toml",
    "agents/marketing-tiktok-strategist.toml",
    "agents/marketing-twitter-engager.toml",
    "agents/operations-analytics-reporter.toml",
    "agents/operations-finance-tracker.toml",
    "agents/operations-legal-compliance-checker.toml",
    "agents/operations-support-responder.toml",
    "agents/product-feedback-synthesizer.toml",
    "agents/product-manager.toml",
    "agents/product-marketing-manager.toml",
    "agents/product-sprint-prioritizer.toml",
    "agents/product-trend-researcher.toml",
    "agents/project-management-experiment-tracker.toml",
    "agents/project-management-project-shipper.toml",
    "agents/project-management-studio-producer.toml",
    "agents/qa-engineer.toml",
    "agents/release-manager.toml",
    "agents/sales-outreach-specialist.toml",
    "agents/security-engineer.toml",
    "agents/studio-operations-talent-sourcer.toml",
    "agents/technical-writer.toml",
    "agents/testing-api-tester.toml",
    "agents/testing-performance-benchmarker.toml",
    "agents/testing-test-results-analyzer.toml",
    "agents/testing-tool-evaluator.toml",
    "agents/testing-workflow-optimizer.toml"
]
