#!/usr/bin/env node

/**
 * Test script to verify onboarding session management fix
 * 
 * This script tests that:
 * 1. Starting onboarding creates a new session
 * 2. Starting onboarding again resumes the existing session
 * 3. Force restart creates a new session
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8081/api';
const TEST_TOKEN = 'your-test-token-here'; // Replace with actual test token

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function testOnboardingSessionManagement() {
  console.log('🧪 Testing Onboarding Session Management...\n');

  try {
    // Test 1: Start fresh onboarding
    console.log('1️⃣ Starting fresh onboarding...');
    const startResponse = await api.post('/onboarding/start', {
      deviceInfo: {
        platform: 'web',
        browser: 'Chrome'
      }
    });
    
    const firstSessionId = startResponse.data.data.session.sessionId;
    const firstResumed = startResponse.data.data.resumed;
    
    console.log(`   ✅ Session created: ${firstSessionId}`);
    console.log(`   ✅ Resumed: ${firstResumed} (should be false)`);
    console.log(`   ✅ Current step: ${startResponse.data.data.session.currentStep}\n`);

    // Test 2: Check session status
    console.log('2️⃣ Checking session status...');
    const statusResponse = await api.get('/onboarding/session-status');
    const hasIncompleteSession = statusResponse.data.data.hasIncompleteSession;
    const sessionFromStatus = statusResponse.data.data.session;
    
    console.log(`   ✅ Has incomplete session: ${hasIncompleteSession} (should be true)`);
    console.log(`   ✅ Session ID matches: ${sessionFromStatus.sessionId === firstSessionId}\n`);

    // Test 3: Start onboarding again (should resume)
    console.log('3️⃣ Starting onboarding again (should resume)...');
    const resumeResponse = await api.post('/onboarding/start', {
      deviceInfo: {
        platform: 'web',
        browser: 'Chrome'
      }
    });
    
    const resumedSessionId = resumeResponse.data.data.session.sessionId;
    const secondResumed = resumeResponse.data.data.resumed;
    
    console.log(`   ✅ Session ID: ${resumedSessionId}`);
    console.log(`   ✅ Same session resumed: ${resumedSessionId === firstSessionId} (should be true)`);
    console.log(`   ✅ Resumed flag: ${secondResumed} (should be true)\n`);

    // Test 4: Force restart
    console.log('4️⃣ Force restarting onboarding...');
    const restartResponse = await api.post('/onboarding/start', {
      forceRestart: true,
      deviceInfo: {
        platform: 'web',
        browser: 'Chrome'
      }
    });
    
    const newSessionId = restartResponse.data.data.session.sessionId;
    const restartResumed = restartResponse.data.data.resumed;
    
    console.log(`   ✅ New session ID: ${newSessionId}`);
    console.log(`   ✅ Different from first: ${newSessionId !== firstSessionId} (should be true)`);
    console.log(`   ✅ Resumed flag: ${restartResumed} (should be false)\n`);

    // Test 5: Use explicit restart endpoint
    console.log('5️⃣ Using explicit restart endpoint...');
    const explicitRestartResponse = await api.post('/onboarding/restart', {
      deviceInfo: {
        platform: 'web',
        browser: 'Chrome'
      }
    });
    
    const explicitNewSessionId = explicitRestartResponse.data.data.session.sessionId;
    const explicitRestarted = explicitRestartResponse.data.data.restarted;
    
    console.log(`   ✅ Explicit restart session ID: ${explicitNewSessionId}`);
    console.log(`   ✅ Different from previous: ${explicitNewSessionId !== newSessionId} (should be true)`);
    console.log(`   ✅ Restarted flag: ${explicitRestarted} (should be true)\n`);

    console.log('🎉 All tests passed! Onboarding session management is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Tip: Make sure to replace TEST_TOKEN with a valid authentication token');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testOnboardingSessionManagement();
}

module.exports = { testOnboardingSessionManagement };