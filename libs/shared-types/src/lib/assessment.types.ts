/**
 * Assessment Types for Feature 1: Understanding Your Inner Landscape
 * Shared types across backend, frontend, and AI service
 */

import { z } from 'zod';

// Soul Layer Schema
export const SoulLayerSchema = z.enum([
  'jism',  // Physical body
  'nafs',  // Ego/emotions
  'aql',   // Mind/intellect
  'qalb',  // Heart/spiritual heart
  'ruh'    // Soul/spirit
]);

// Symptom Category Schema
export const SymptomCategorySchema = z.enum([
  'physical',
  'emotional',
  'mental',
  'spiritual',
  'behavioral'
]);

// Severity Level Schema
export const SeverityLevelSchema = z.enum([
  'mild',
  'moderate',
  'severe',
  'critical'
]);

// Crisis Level Schema
export const CrisisLevelSchema = z.enum([
  'none',
  'low',
  'moderate',
  'high',
  'critical'
]);

// Assessment Question Schema
export const AssessmentQuestionSchema = z.object({
  id: z.string(),
  category: SymptomCategorySchema,
  layer: SoulLayerSchema,
  question: z.string(),
  description: z.string().optional(),
  type: z.enum(['single_choice', 'multiple_choice', 'scale', 'text']),
  options: z.array(z.object({
    id: z.string(),
    text: z.string(),
    value: z.union([z.string(), z.number()]),
    description: z.string().optional()
  })).optional(),
  required: z.boolean().default(true),
  order: z.number(),
  conditionalLogic: z.object({
    showIf: z.string().optional(),
    hideIf: z.string().optional()
  }).optional()
});

// Assessment Response Schema
export const AssessmentResponseSchema = z.object({
  questionId: z.string(),
  response: z.union([z.string(), z.number(), z.array(z.string())]),
  timeSpent: z.number().optional(), // seconds
  confidence: z.number().min(1).max(5).optional()
});

// Assessment Session Schema
export const AssessmentSessionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  status: z.enum(['started', 'in_progress', 'completed', 'abandoned']),
  responses: z.array(AssessmentResponseSchema),
  currentQuestionIndex: z.number().default(0),
  totalQuestions: z.number(),
  startedAt: z.string(),
  completedAt: z.string().optional(),
  lastActiveAt: z.string(),
  metadata: z.object({
    userAgent: z.string().optional(),
    deviceType: z.string().optional(),
    sessionDuration: z.number().optional()
  }).optional()
});

// Layer Analysis Schema
export const LayerAnalysisSchema = z.object({
  layer: SoulLayerSchema,
  severity: SeverityLevelSchema,
  confidence: z.number().min(0).max(1),
  symptoms: z.array(z.string()),
  indicators: z.array(z.string()),
  recommendations: z.array(z.string()),
  healingPriority: z.number().min(1).max(5),
  estimatedHealingTime: z.number(), // days
  islamicPerspective: z.string(),
  modernPerspective: z.string()
});

// Assessment Results Schema
export const AssessmentResultsSchema = z.object({
  id: z.string(),
  sessionId: z.string(),
  userId: z.string(),
  
  // Overall Assessment
  overallSeverity: SeverityLevelSchema,
  crisisLevel: CrisisLevelSchema,
  confidence: z.number().min(0).max(1),
  
  // Layer Analysis
  primaryLayer: SoulLayerSchema,
  secondaryLayers: z.array(SoulLayerSchema),
  layerAnalysis: z.array(LayerAnalysisSchema),
  
  // Recommendations
  immediateActions: z.array(z.string()),
  recommendedJourneyType: z.string(),
  estimatedHealingDuration: z.number(), // days
  
  // Insights
  keyInsights: z.array(z.string()),
  spiritualSpotlight: z.string(),
  personalizedMessage: z.string(),
  
  // Crisis Management
  crisisIndicators: z.array(z.string()).optional(),
  crisisResponse: z.object({
    level: CrisisLevelSchema,
    actions: z.array(z.string()),
    resources: z.array(z.string()),
    followUpRequired: z.boolean()
  }).optional(),
  
  // Metadata
  generatedAt: z.string(),
  aiModel: z.string(),
  processingTime: z.number() // milliseconds
});

// Assessment Analytics Schema
export const AssessmentAnalyticsSchema = z.object({
  assessmentId: z.string(),
  userId: z.string(),
  
  // Completion Metrics
  completionTime: z.number(), // minutes
  responseQuality: z.number().min(0).max(1),
  engagementLevel: z.number().min(0).max(1),
  
  // Response Patterns
  responseConsistency: z.number().min(0).max(1),
  layerDistribution: z.record(z.number()),
  severityProgression: z.array(z.number()),
  
  // Accuracy Indicators
  selfAwarenessLevel: z.number().min(0).max(1),
  responseHonesty: z.number().min(0).max(1),
  culturalAlignment: z.number().min(0).max(1),
  
  // Follow-up Recommendations
  reassessmentRecommended: z.boolean(),
  reassessmentTimeframe: z.number().optional(), // days
  additionalScreeningNeeded: z.boolean(),
  
  generatedAt: z.string()
});

// Personalized Welcome Schema
export const PersonalizedWelcomeSchema = z.object({
  userId: z.string(),
  assessmentId: z.string(),
  welcomeMessage: z.string(),
  personalizedGreeting: z.string(),
  culturalContext: z.string().optional(),
  professionalContext: z.string().optional(),
  spiritualLevel: z.string(),
  encouragement: z.string(),
  nextSteps: z.array(z.string()),
  islamicQuote: z.object({
    arabic: z.string(),
    transliteration: z.string(),
    translation: z.string(),
    reference: z.string()
  }).optional()
});

// Type exports
export type SoulLayer = z.infer<typeof SoulLayerSchema>;
export type SymptomCategory = z.infer<typeof SymptomCategorySchema>;
export type SeverityLevel = z.infer<typeof SeverityLevelSchema>;
export type CrisisLevel = z.infer<typeof CrisisLevelSchema>;
export type AssessmentQuestion = z.infer<typeof AssessmentQuestionSchema>;
export type AssessmentResponse = z.infer<typeof AssessmentResponseSchema>;
export type AssessmentSession = z.infer<typeof AssessmentSessionSchema>;
export type LayerAnalysis = z.infer<typeof LayerAnalysisSchema>;
export type AssessmentResults = z.infer<typeof AssessmentResultsSchema>;
export type AssessmentAnalytics = z.infer<typeof AssessmentAnalyticsSchema>;
export type PersonalizedWelcome = z.infer<typeof PersonalizedWelcomeSchema>;
