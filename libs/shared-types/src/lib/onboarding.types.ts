/**
 * Onboarding Types for Feature 0: Adaptive Onboarding & User Profiling
 * Shared types across backend, frontend, and AI service
 */

import { z } from 'zod';

// Onboarding Status Schema
export const OnboardingStatusSchema = z.enum([
  'not_started',
  'in_progress',
  'completed',
  'skipped',
  'abandoned'
]);

// Question Type Schema
export const QuestionTypeSchema = z.enum([
  'welcome',
  'single_choice',
  'multiple_choice',
  'adaptive_flow',
  'multi_section',
  'text_input',
  'scale',
  'demographic'
]);

// User Pathway Schema
export const UserPathwaySchema = z.enum([
  'crisis_support',
  'clinical_islamic_integration',
  'traditional_modern_bridge',
  'gentle_introduction',
  'advanced_development',
  'maintenance_program'
]);

// Awareness Level Schema
export const AwarenessLevelSchema = z.enum([
  'unaware',
  'symptom_aware',
  'seeking_help',
  'clinically_aware',
  'spiritual_optimizer',
  'new_muslim'
]);

// Ruqya Familiarity Schema
export const RuqyaFamiliaritySchema = z.enum([
  'unaware',
  'heard_of_it',
  'basic_knowledge',
  'experienced',
  'practitioner',
  'expert',
  'skeptical'
]);

// Onboarding Question Schema
export const OnboardingQuestionSchema = z.object({
  id: z.string(),
  type: QuestionTypeSchema,
  title: z.string(),
  subtitle: z.string().optional(),
  content: z.string().optional(),
  options: z.array(z.object({
    id: z.string(),
    text: z.string(),
    icon: z.string().optional(),
    description: z.string().optional(),
    value: z.union([z.string(), z.number()]).optional()
  })).optional(),
  sections: z.array(z.object({
    id: z.string(),
    question: z.string(),
    type: QuestionTypeSchema,
    options: z.array(z.object({
      id: z.string(),
      text: z.string(),
      value: z.union([z.string(), z.number()])
    })).optional()
  })).optional(),
  followUps: z.record(z.array(z.any())).optional(),
  required: z.boolean().default(true),
  order: z.number(),
  adaptiveLogic: z.object({
    showIf: z.string().optional(),
    skipIf: z.string().optional(),
    nextQuestion: z.string().optional()
  }).optional()
});

// Onboarding Response Schema
export const OnboardingResponseSchema = z.object({
  questionId: z.string(),
  response: z.union([
    z.string(),
    z.number(),
    z.array(z.string()),
    z.record(z.any())
  ]),
  timeSpent: z.number().optional(), // seconds
  confidence: z.number().min(1).max(5).optional(),
  metadata: z.object({
    hesitation: z.boolean().optional(),
    changedAnswer: z.boolean().optional(),
    skipReason: z.string().optional()
  }).optional()
});

// Onboarding Session Schema
export const OnboardingSessionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  status: OnboardingStatusSchema,
  currentStep: z.string(),
  progress: z.number().min(0).max(100),
  responses: z.array(OnboardingResponseSchema),
  deviceInfo: z.object({
    platform: z.string(),
    browser: z.string().optional(),
    screenSize: z.string().optional(),
    version: z.string().optional(),
    locale: z.string().optional()
  }),
  startedAt: z.string(),
  completedAt: z.string().optional(),
  lastActiveAt: z.string(),
  totalTimeSpent: z.number().optional(), // seconds
  skipReason: z.string().optional()
});

// User Profile Schema
export const UserProfileSchema = z.object({
  userId: z.string(),
  
  // Basic Demographics
  name: z.string().optional(),
  age: z.number().optional(),
  gender: z.string().optional(),
  location: z.string().optional(),
  
  // Islamic Background
  awarenessLevel: AwarenessLevelSchema,
  ruqyaFamiliarity: RuqyaFamiliaritySchema,
  islamicKnowledgeLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  practiceLevel: z.enum(['new', 'occasional', 'regular', 'devoted']).optional(),
  
  // Professional Context
  profession: z.string().optional(),
  workEnvironment: z.string().optional(),
  professionalStressLevel: z.number().min(1).max(10).optional(),
  
  // Cultural Context
  culturalBackground: z.string().optional(),
  primaryLanguage: z.string().optional(),
  preferredContent: z.enum(['arabic_heavy', 'transliterated', 'translated', 'mixed']).optional(),
  
  // Lifestyle
  timeAvailability: z.enum(['very_limited', 'limited', 'moderate', 'flexible', 'abundant']).optional(),
  learningStyle: z.enum(['visual', 'auditory', 'kinesthetic', 'reading', 'mixed']).optional(),
  devicePreference: z.enum(['mobile', 'tablet', 'desktop', 'mixed']).optional(),
  
  // Preferences
  communityPreference: z.boolean().optional(),
  privacyLevel: z.enum(['private', 'semi_private', 'community_focused']).optional(),
  notificationPreference: z.enum(['minimal', 'moderate', 'frequent']).optional(),
  
  // Health Context
  mentalHealthAwareness: z.enum(['unaware', 'aware', 'seeking', 'receiving_care']).optional(),
  previousTherapy: z.boolean().optional(),
  currentMedication: z.boolean().optional(),
  crisisHistory: z.boolean().optional(),
  
  // Completion Status
  completionStatus: z.enum(['incomplete', 'complete', 'needs_update']),
  completedAt: z.string().optional(),
  lastUpdated: z.string(),
  
  // AI-Generated Insights
  recommendedPathway: UserPathwaySchema.optional(),
  personalizationTags: z.array(z.string()).default([]),
  riskFactors: z.array(z.string()).default([]),
  strengths: z.array(z.string()).default([])
});

// Crisis Detection Schema
export const CrisisDetectionSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  level: z.enum(['none', 'low', 'moderate', 'high', 'critical']),
  confidence: z.number().min(0).max(1),
  indicators: z.array(z.string()),
  urgency: z.enum(['low', 'moderate', 'urgent', 'immediate']),
  recommendedActions: z.array(z.string()),
  reasoning: z.string(),
  detectedAt: z.string(),
  
  // Crisis Response
  response: z.object({
    level: z.enum(['none', 'low', 'moderate', 'high', 'critical']),
    message: z.string(),
    actions: z.array(z.object({
      id: z.string(),
      text: z.string(),
      primary: z.boolean().optional(),
      urgent: z.boolean().optional(),
      emergency: z.boolean().optional(),
      phone: z.string().optional(),
      url: z.string().optional()
    })),
    urgency: z.enum(['low', 'moderate', 'urgent', 'immediate']),
    indicators: z.array(z.string()).optional()
  }).optional()
});

// Onboarding Analytics Schema
export const OnboardingAnalyticsSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  
  // Completion Metrics
  completionRate: z.number().min(0).max(100),
  totalTimeSpent: z.number(), // seconds
  averageTimePerQuestion: z.number(),
  abandonmentPoint: z.string().optional(),
  
  // Engagement Metrics
  engagementLevel: z.number().min(0).max(1),
  responseQuality: z.number().min(0).max(1),
  hesitationPoints: z.array(z.string()),
  
  // Pathway Prediction
  pathwayConfidence: z.number().min(0).max(1),
  alternativePathways: z.array(UserPathwaySchema),
  personalizationSuccess: z.number().min(0).max(1),
  
  // Risk Assessment
  crisisRisk: z.number().min(0).max(1),
  supportNeeds: z.array(z.string()),
  followUpRecommended: z.boolean(),
  
  generatedAt: z.string()
});

// Type exports
export type OnboardingStatus = z.infer<typeof OnboardingStatusSchema>;
export type QuestionType = z.infer<typeof QuestionTypeSchema>;
export type UserPathway = z.infer<typeof UserPathwaySchema>;
export type AwarenessLevel = z.infer<typeof AwarenessLevelSchema>;
export type RuqyaFamiliarity = z.infer<typeof RuqyaFamiliaritySchema>;
export type OnboardingQuestion = z.infer<typeof OnboardingQuestionSchema>;
export type OnboardingResponse = z.infer<typeof OnboardingResponseSchema>;
export type OnboardingSession = z.infer<typeof OnboardingSessionSchema>;
export type UserProfile = z.infer<typeof UserProfileSchema>;
export type CrisisDetection = z.infer<typeof CrisisDetectionSchema>;
export type OnboardingAnalytics = z.infer<typeof OnboardingAnalyticsSchema>;
