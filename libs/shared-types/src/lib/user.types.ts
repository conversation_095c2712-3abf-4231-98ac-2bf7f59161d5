/**
 * User Types - Shared across all features
 * Core user and authentication types
 */

import { z } from 'zod';

// User Role Schema
export const UserRoleSchema = z.enum([
  'user',
  'mentor',
  'counselor',
  'admin',
  'super_admin'
]);

// User Status Schema
export const UserStatusSchema = z.enum([
  'active',
  'inactive',
  'suspended',
  'pending_verification',
  'deleted'
]);

// Authentication Provider Schema
export const AuthProviderSchema = z.enum([
  'email',
  'google',
  'apple',
  'facebook'
]);

// User Schema
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  emailVerified: z.boolean().default(false),
  
  // Profile Information
  firstName: z.string(), // Now required
  lastName: z.string().optional(),
  displayName: z.string().optional(), // Potentially derived from firstName and lastName
  avatar: z.string().optional(),
  
  // Authentication
  authProvider: AuthProviderSchema,
  providerId: z.string().optional(),
  lastLoginAt: z.string().optional(),
  
  // Status and Permissions
  role: UserRoleSchema.default('user'),
  status: UserStatusSchema.default('active'),
  permissions: z.array(z.string()).default([]),
  
  // Platform Progress
  onboardingCompleted: z.boolean().default(false),
  assessmentCompleted: z.boolean().default(false),
  currentJourneyId: z.string().optional(),
  
  // Preferences
  preferences: z.object({
    language: z.string().default('en'),
    timezone: z.string().optional(),
    notifications: z.object({
      email: z.boolean().default(true),
      push: z.boolean().default(true),
      sms: z.boolean().default(false)
    }).default({}),
    privacy: z.object({
      profileVisibility: z.enum(['private', 'community', 'public']).default('community'),
      dataSharing: z.boolean().default(false),
      analyticsOptOut: z.boolean().default(false)
    }).default({})
  }).default({}),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
  lastActiveAt: z.string().optional(),
  deletedAt: z.string().optional()
});

// User Session Schema
export const UserSessionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  token: z.string(),
  refreshToken: z.string().optional(),
  deviceInfo: z.object({
    userAgent: z.string().optional(),
    platform: z.string().optional(),
    deviceId: z.string().optional(),
    ipAddress: z.string().optional()
  }).optional(),
  expiresAt: z.string(),
  createdAt: z.string(),
  lastUsedAt: z.string().optional(),
  isActive: z.boolean().default(true)
});

// User Activity Schema
export const UserActivitySchema = z.object({
  id: z.string(),
  userId: z.string(),
  activityType: z.enum([
    'login',
    'logout',
    'onboarding_started',
    'onboarding_completed',
    'assessment_started',
    'assessment_completed',
    'journey_started',
    'journey_completed',
    'practice_completed',
    'community_interaction',
    'crisis_detected',
    'support_accessed'
  ]),
  details: z.record(z.any()).optional(),
  metadata: z.object({
    platform: z.string().optional(),
    version: z.string().optional(),
    sessionId: z.string().optional()
  }).optional(),
  timestamp: z.string()
});

// User Analytics Schema
export const UserAnalyticsSchema = z.object({
  userId: z.string(),
  
  // Engagement Metrics
  totalSessions: z.number().default(0),
  totalTimeSpent: z.number().default(0), // minutes
  averageSessionDuration: z.number().default(0),
  lastActiveDate: z.string().optional(),
  streakDays: z.number().default(0),
  
  // Feature Usage
  onboardingCompletionRate: z.number().min(0).max(100).default(0),
  assessmentAccuracy: z.number().min(0).max(100).default(0),
  journeyAdherence: z.number().min(0).max(100).default(0),
  communityEngagement: z.number().min(0).max(100).default(0),
  
  // Progress Metrics
  healingProgress: z.number().min(0).max(100).default(0),
  spiritualGrowth: z.number().min(0).max(100).default(0),
  practiceConsistency: z.number().min(0).max(100).default(0),
  
  // Risk Indicators
  crisisRiskLevel: z.enum(['none', 'low', 'moderate', 'high', 'critical']).default('none'),
  supportNeeded: z.boolean().default(false),
  lastCrisisCheck: z.string().optional(),
  
  // Personalization Data
  contentPreferences: z.array(z.string()).default([]),
  effectivePractices: z.array(z.string()).default([]),
  challengingAreas: z.array(z.string()).default([]),
  
  generatedAt: z.string(),
  updatedAt: z.string()
});

// User Notification Schema
export const UserNotificationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  type: z.enum([
    'welcome',
    'reminder',
    'achievement',
    'community',
    'crisis_support',
    'system',
    'marketing'
  ]),
  title: z.string(),
  message: z.string(),
  data: z.record(z.any()).optional(),
  
  // Delivery
  channels: z.array(z.enum(['push', 'email', 'sms', 'in_app'])),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  
  // Status
  status: z.enum(['pending', 'sent', 'delivered', 'read', 'failed']).default('pending'),
  sentAt: z.string().optional(),
  readAt: z.string().optional(),
  
  // Scheduling
  scheduledFor: z.string().optional(),
  expiresAt: z.string().optional(),
  
  createdAt: z.string(),
  updatedAt: z.string()
});

// User Support Ticket Schema
export const UserSupportTicketSchema = z.object({
  id: z.string(),
  userId: z.string(),
  type: z.enum([
    'technical',
    'content',
    'crisis',
    'feedback',
    'billing',
    'general'
  ]),
  priority: z.enum(['low', 'normal', 'high', 'urgent', 'critical']),
  status: z.enum(['open', 'in_progress', 'resolved', 'closed']),
  
  // Content
  subject: z.string(),
  description: z.string(),
  category: z.string().optional(),
  tags: z.array(z.string()).default([]),
  
  // Assignment
  assignedTo: z.string().optional(),
  assignedAt: z.string().optional(),
  
  // Resolution
  resolution: z.string().optional(),
  resolvedAt: z.string().optional(),
  resolvedBy: z.string().optional(),
  
  // Metadata
  userAgent: z.string().optional(),
  deviceInfo: z.record(z.any()).optional(),
  attachments: z.array(z.string()).default([]),
  
  createdAt: z.string(),
  updatedAt: z.string()
});

// Type exports
export type User = z.infer<typeof UserSchema>;
export type UserRole = z.infer<typeof UserRoleSchema>;
export type UserStatus = z.infer<typeof UserStatusSchema>;
export type AuthProvider = z.infer<typeof AuthProviderSchema>;
export type UserSession = z.infer<typeof UserSessionSchema>;
export type UserActivity = z.infer<typeof UserActivitySchema>;
export type UserAnalytics = z.infer<typeof UserAnalyticsSchema>;
export type UserNotification = z.infer<typeof UserNotificationSchema>;
export type UserSupportTicket = z.infer<typeof UserSupportTicketSchema>;
