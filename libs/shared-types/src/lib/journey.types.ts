/**
 * Journey Types for Feature 2: Personalized Healing Journeys
 * Shared types across backend, frontend, and AI service
 */

import { z } from 'zod';

// Journey Types and Enums
export const JourneyTypeSchema = z.enum([
  'tranquil_mind',
  'heart_purification', 
  'ego_purification',
  'spiritual_optimization',
  'crisis_recovery',
  'maintenance_program'
]);

export const JourneyStatusSchema = z.enum([
  'created',
  'active',
  'paused',
  'completed',
  'abandoned',
  'evolved'
]);

export const PracticeTypeSchema = z.enum([
  'dhikr',
  'prayer',
  'reflection',
  'study',
  'community',
  'ruqya',
  'mindfulness',
  'gratitude',
  // Feature 2: 5 Daily Components
  'MorningCheckIn',
  'NameOfAllahSpotlight',
  'QuranicVerseReflection',
  'PersonalReflectionJournaling',
  'SunnahPractice'
]);

export const LayerFocusSchema = z.enum([
  'jism',
  'nafs', 
  'aql',
  'qalb',
  'ruh'
]);

// Journey Configuration Schema
export const JourneyConfigSchema = z.object({
  duration: z.number().min(7).max(90), // 7-90 days
  dailyTimeCommitment: z.number().min(5).max(60), // 5-60 minutes
  primaryLayer: LayerFocusSchema,
  secondaryLayers: z.array(LayerFocusSchema),
  ruqyaIntegrationLevel: z.enum(['none', 'basic', 'intermediate', 'advanced']),
  communityIntegration: z.boolean(),
  professionalContext: z.string().optional(),
  culturalAdaptations: z.array(z.string()).optional(),
  crisisSupport: z.boolean().default(false)
});

// Daily Practice Schema
export const DailyPracticeSchema = z.object({
  id: z.string(),
  type: PracticeTypeSchema,
  title: z.string(),
  description: z.string(),
  duration: z.number(), // minutes
  instructions: z.string(),
  arabicText: z.string().optional(),
  transliteration: z.string().optional(),
  translation: z.string().optional(),
  benefits: z.array(z.string()),
  layerFocus: LayerFocusSchema,
  difficultyLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  ruqyaComponent: z.boolean().default(false),
  professionalContext: z.string().optional(),
  culturalNotes: z.string().optional(),
  // To hold specific data for each of the 5 component types
  componentDetails: z.record(z.string(), z.any()).optional()
});

// Journey Day Schema
export const JourneyDaySchema = z.object({
  dayNumber: z.number(),
  theme: z.string(),
  learningObjective: z.string(),
  practices: z.array(DailyPracticeSchema),
  reflectionPrompts: z.array(z.string()),
  communityActivity: z.string().optional(),
  progressMilestone: z.string().optional(),
  adaptiveContent: z.object({
    forNewMuslims: z.string().optional(),
    forProfessionals: z.string().optional(),
    forRuqyaExperts: z.string().optional()
  }).optional()
});

// Main Journey Schema
export const JourneySchema = z.object({
  id: z.string(),
  userId: z.string(),
  assessmentId: z.string(), // Links to Feature 1 assessment
  type: JourneyTypeSchema,
  status: JourneyStatusSchema,
  configuration: JourneyConfigSchema,
  
  // Journey Content
  title: z.string(),
  description: z.string(),
  personalizedWelcome: z.string(),
  days: z.array(JourneyDaySchema),
  
  // Progress Tracking
  currentDay: z.number().default(1),
  completedDays: z.array(z.number()).default([]),
  totalProgress: z.number().min(0).max(100).default(0),
  
  // Personalization Data
  userProfile: z.object({
    awarenessLevel: z.string(),
    ruqyaFamiliarity: z.string(),
    profession: z.string(),
    culturalBackground: z.string(),
    timeAvailability: z.string(),
    learningStyle: z.string()
  }),
  
  // Community Integration
  communityGroup: z.string().optional(),
  mentorId: z.string().optional(),
  peerConnections: z.array(z.string()).default([]),
  
  // Adaptive Features
  aiRecommendations: z.array(z.string()).default([]),
  adaptiveAdjustments: z.array(z.object({
    date: z.string(),
    reason: z.string(),
    adjustment: z.string(),
    impact: z.string()
  })).default([]),
  
  // Crisis Management
  crisisFlags: z.array(z.object({
    date: z.string(),
    level: z.enum(['low', 'moderate', 'high', 'critical']),
    indicators: z.array(z.string()),
    response: z.string()
  })).default([]),
  
  // Timestamps
  createdAt: z.string(),
  startedAt: z.string().optional(),
  completedAt: z.string().optional(),
  lastActiveAt: z.string().optional()
});

// Journey Progress Schema
export const JourneyProgressSchema = z.object({
  id: z.string(),
  journeyId: z.string(),
  userId: z.string(),
  dayNumber: z.number(),
  date: z.string(),
  
  // Practice Completion
  practicesCompleted: z.array(z.object({
    practiceId: z.string(),
    completedAt: z.string(),
    duration: z.number(), // actual time spent
    rating: z.number().min(1).max(5).optional(),
    notes: z.string().optional(),
    difficulty: z.enum(['too_easy', 'just_right', 'too_hard']).optional()
  })),
  
  // Daily Metrics
  overallRating: z.number().min(1).max(5).optional(),
  moodBefore: z.number().min(1).max(10).optional(), // From Morning Check-in
  moodAfter: z.number().min(1).max(10).optional(),  // From Morning Check-in or end-of-day reflection
  energyLevelBefore: z.number().min(1).max(10).optional(), // From Morning Check-in
  spiritualStateBefore: z.string().optional(), // From Morning Check-in (could be a selection or short text)
  dailyIntention: z.string().optional(), // From Morning Check-in (Niyyah)
  spiritualConnection: z.number().min(1).max(10).optional(), // General feeling or could be tied to a specific practice
  stressLevel: z.number().min(1).max(10).optional(), // General or tied to practices
  
  // Reflection
  dailyReflection: z.string().optional(), // From PersonalReflectionJournaling
  gratitude: z.array(z.string()).default([]),
  challenges: z.array(z.string()).default([]),
  insights: z.array(z.string()).default([]),
  
  // Community Engagement
  communityParticipation: z.boolean().default(false),
  communityContribution: z.string().optional(),
  peerSupport: z.boolean().default(false),
  
  // Adaptive Feedback
  contentRelevance: z.number().min(1).max(5).optional(),
  practiceEffectiveness: z.number().min(1).max(5).optional(),
  timeAppropriate: z.boolean().optional(),
  suggestedAdjustments: z.string().optional(),
  
  createdAt: z.string(),
  updatedAt: z.string()
});

// Journey Analytics Schema
export const JourneyAnalyticsSchema = z.object({
  journeyId: z.string(),
  userId: z.string(),
  
  // Completion Metrics
  completionRate: z.number().min(0).max(100),
  averageDailyRating: z.number().min(1).max(5).optional(),
  practiceAdherence: z.number().min(0).max(100),
  communityEngagement: z.number().min(0).max(100),
  
  // Healing Outcomes
  symptomImprovement: z.object({
    primaryLayer: z.number().min(-100).max(100), // percentage change
    secondaryLayers: z.record(z.number()),
    overallWellness: z.number().min(-100).max(100)
  }),
  
  // Spiritual Growth
  spiritualDevelopment: z.object({
    islamicPracticeIntegration: z.number().min(0).max(100),
    spiritualConnection: z.number().min(0).max(100),
    ruqyaKnowledge: z.number().min(0).max(100).optional(),
    communityConnection: z.number().min(0).max(100)
  }),
  
  // Personalization Effectiveness
  personalizationSuccess: z.object({
    contentRelevance: z.number().min(1).max(5),
    culturalAdaptation: z.number().min(1).max(5),
    professionalIntegration: z.number().min(1).max(5),
    timeManagement: z.number().min(1).max(5)
  }),
  
  // Recommendations for Future
  nextStepRecommendations: z.array(z.string()),
  graduationReadiness: z.boolean(),

  // New field for Healing Wheel data
  layerProgress: z.object({
    jism: z.object({ progressValue: z.number().min(0).max(100), color: z.string().optional(), iconName: z.string().optional() }),
    nafs: z.object({ progressValue: z.number().min(0).max(100), color: z.string().optional(), iconName: z.string().optional() }),
    aql: z.object({ progressValue: z.number().min(0).max(100), color: z.string().optional(), iconName: z.string().optional() }),
    qalb: z.object({ progressValue: z.number().min(0).max(100), color: z.string().optional(), iconName: z.string().optional() }),
    ruh: z.object({ progressValue: z.number().min(0).max(100), color: z.string().optional(), iconName: z.string().optional() }),
  }).optional(), // Optional for now, as backend might not provide it immediately
  
  generatedAt: z.string()
});

// Type exports
export type Journey = z.infer<typeof JourneySchema>;
export type JourneyConfig = z.infer<typeof JourneyConfigSchema>;
export type DailyPractice = z.infer<typeof DailyPracticeSchema>;
export type JourneyDay = z.infer<typeof JourneyDaySchema>;
export type JourneyProgress = z.infer<typeof JourneyProgressSchema>;
export type JourneyAnalytics = z.infer<typeof JourneyAnalyticsSchema>;
export type JourneyType = z.infer<typeof JourneyTypeSchema>;
export type JourneyStatus = z.infer<typeof JourneyStatusSchema>;
export type PracticeType = z.infer<typeof PracticeTypeSchema>;
export type LayerFocus = z.infer<typeof LayerFocusSchema>;
