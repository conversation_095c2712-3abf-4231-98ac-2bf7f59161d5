# Repository Tour

## 🎯 What This Repository Does

Qalb Healing is a comprehensive Islamic mental wellness platform that provides authentic healing solutions based on Quranic verses, Prophetic traditions, and Islamic spiritual practices. The platform focuses on the Islamic understanding of the human soul's five layers (Jism, Nafs, <PERSON>ql, <PERSON>al<PERSON>, <PERSON>uh) and avoids Western psychology frameworks.

**Key responsibilities:**
- Provides AI-powered Islamic healing analysis across the 5-layer soul model
- Offers Quranic verses, Names of Allah, and <PERSON><PERSON><PERSON><PERSON> for spiritual healing
- Delivers crisis support through "Qalb Rescue" emergency mode
- Tracks healing journeys and progress with Islamic-focused monitoring
- Facilitates community support through "Heart Circles"

---

## 🏗️ Architecture Overview

### System Context
```
[Mobile App (React Native)] → [Backend API (Express.js)] → [Database (Supabase + Prisma)]
                                        ↓
                              [AI Service (FastAPI + Python)]
                                        ↓
                              [External AI APIs (OpenAI)]
```

### Key Components
- **Mobile App (React Native + Expo)** - Cross-platform iOS/Android app with Islamic UI/UX
- **Backend API (Node.js + Express)** - RESTful API server handling business logic and authentication
- **AI Service (Python + FastAPI)** - Specialized AI analysis for Islamic healing recommendations
- **Database Layer (Supabase + Prisma)** - PostgreSQL database with real-time capabilities
- **Shared Libraries** - Common TypeScript types, validation schemas, and Islamic content

### Data Flow
1. **User interacts with mobile app** - Symptom input, journey tracking, emergency support
2. **Mobile app calls backend API** - Authentication, data persistence, business logic
3. **Backend processes requests** - Validates input, applies Islamic business rules
4. **AI service analyzes symptoms** - Provides Islamic healing recommendations based on 5-layer model
5. **Results returned to user** - Quranic verses, Names of Allah, spiritual practices, progress tracking

---

## 📁 Project Structure [Partial Directory Tree]

```
qalb-healing-workspace/
├── apps/                           # Main applications
│   ├── mobile-app-v3/             # React Native + Expo mobile app
│   │   ├── src/                   # Source code
│   │   │   ├── app/               # Expo Router app directory
│   │   │   ├── components/        # Reusable UI components
│   │   │   ├── screens/           # Screen components
│   │   │   ├── services/          # API and business logic services
│   │   │   ├── state/             # Context providers and state management
│   │   │   └── utils/             # Utility functions
│   │   └── __tests__/             # Test files (unit, integration, e2e)
│   ├── backend/                   # Node.js + Express API server
│   │   ├── src/                   # Source code
│   │   │   ├── controllers/       # Route handlers and business logic
│   │   │   ├── services/          # Business logic services
│   │   │   ├── routes/            # API route definitions
│   │   │   ├── middleware/        # Express middleware (auth, security, etc.)
│   │   │   ├── models/            # Data models and types
│   │   │   └── config/            # Configuration (database, Supabase, etc.)
│   │   ├── prisma/                # Database schema and migrations
│   │   └── __tests__/             # Backend tests
│   └── ai-service/                # Python + FastAPI AI service
│       ├── ai_service/            # Main Python package
│       │   ├── main.py            # FastAPI application entry point
│       │   ├── endpoints/         # API endpoint handlers
│       │   ├── processors/        # AI processing logic
│       │   └── auth/              # Authentication dependencies
│       ├── __tests__/             # Python tests
│       └── requirements.txt       # Python dependencies
├── libs/                          # Shared libraries
│   ├── islamic-content/           # Quranic verses, Names of Allah, Islamic resources
│   ├── shared-types/              # Common TypeScript interfaces and types
│   └── validation/                # Input validation schemas (Zod)
├── docs/                          # Documentation
├── scripts/                       # Build and deployment scripts
└── package.json                   # Workspace configuration and scripts
```

### Key Files to Know

| File | Purpose | When You'd Touch It |
|------|---------|---------------------|
| `package.json` | Workspace configuration and scripts | Adding workspace-level dependencies or scripts |
| `nx.json` | NX monorepo configuration | Configuring build targets and project dependencies |
| `apps/mobile-app-v3/src/app/_layout.tsx` | Main app layout and navigation | Changing app-wide navigation or layout |
| `apps/backend/src/main.ts` | Backend server entry point | Adding middleware or changing server configuration |
| `apps/backend/prisma/schema.prisma` | Database schema definition | Adding new tables or modifying database structure |
| `apps/ai-service/ai_service/main.py` | AI service entry point | Adding new AI endpoints or changing service configuration |
| `libs/shared-types/src/index.ts` | Common type definitions | Adding new shared types across apps |
| `libs/islamic-content/src/index.ts` | Islamic content data | Adding new Quranic verses or Islamic resources |

---

## 🔧 Technology Stack

### Core Technologies
- **Language:** TypeScript (Frontend/Backend), Python (AI Service) - Type safety and modern development
- **Mobile Framework:** React Native + Expo (~53.0.11) - Cross-platform mobile development with native capabilities
- **Backend Framework:** Node.js + Express.js (^4.18.2) - Mature, fast web framework for APIs
- **AI Framework:** Python + FastAPI (>=0.104.1) - High-performance async API framework for AI services
- **Database:** Supabase (PostgreSQL) + Prisma ORM (^6.10.1) - Real-time database with type-safe ORM

### Key Libraries

**Mobile App:**
- **@expo/vector-icons** - Islamic and general iconography
- **expo-router** - File-based routing system
- **react-native-reanimated** - Smooth animations for Islamic UI elements
- **@react-native-async-storage/async-storage** - Local data persistence

**Backend:**
- **@supabase/supabase-js** - Database client and real-time subscriptions
- **express-validator** - Input validation and sanitization
- **winston** - Structured logging
- **helmet** - Security middleware
- **express-rate-limit** - API rate limiting

**AI Service:**
- **openai** - AI model integration for Islamic analysis
- **langchain** - AI workflow orchestration
- **redis** - Caching and session management
- **pydantic** - Data validation and serialization

### Development Tools
- **NX** - Monorepo management and build orchestration
- **Jest** - Testing framework for all JavaScript/TypeScript code
- **Pytest** - Testing framework for Python AI service
- **ESLint + TypeScript** - Code quality and type checking
- **Prisma Studio** - Database management interface

---

## 🌐 External Dependencies

### Required Services
- **Supabase** - Primary database, authentication, and real-time features (PostgreSQL backend)
- **OpenAI API** - AI model access for Islamic healing analysis and recommendations
- **Redis** - Caching layer for AI service and session management

### Optional Integrations
- **Expo Push Notifications** - Prayer reminders and healing journey notifications
- **Sentry** - Error monitoring and performance tracking (configured in AI service)

### Environment Variables

```bash
# Backend (.env)
SUPABASE_URL=              # Supabase project URL
SUPABASE_ANON_KEY=         # Supabase anonymous key
SUPABASE_SERVICE_ROLE_KEY= # Supabase service role key (backend only)
JWT_SECRET=                # JWT signing secret
REDIS_URL=                 # Redis connection string

# AI Service (.env)
OPENAI_API_KEY=            # OpenAI API key for AI analysis
OPENAI_MODEL=              # AI model to use (default: gpt-4)
OPENAI_MAX_TOKENS=         # Token limit for AI responses
AI_SERVICE_PORT=           # Service port (default: 8000)
LOG_LEVEL=                 # Logging level (default: INFO)

# Mobile App (.env)
EXPO_PUBLIC_API_URL=       # Backend API base URL
EXPO_PUBLIC_AI_SERVICE_URL= # AI service base URL
```

---

## 🔄 Common Workflows

### Symptom Analysis & Islamic Healing Recommendation
1. **User submits symptoms** through mobile app assessment interface
2. **Backend validates and stores** symptom data in Supabase database
3. **AI service analyzes symptoms** using 5-layer Islamic soul model (Jism, Nafs, Aql, Qalb, Ruh)
4. **AI generates recommendations** including Quranic verses, Names of Allah, and spiritual practices
5. **Results delivered to user** with personalized Islamic healing journey

**Code path:** `mobile-app/screens/Assessment` → `backend/controllers/symptoms` → `ai-service/processors/spiritual_analysis` → `database/symptoms & recommendations`

### Emergency Crisis Support (Qalb Rescue)
1. **Crisis detection** through user input or automatic analysis
2. **Immediate Islamic resources** provided (emergency duas, Quranic verses)
3. **Professional help routing** if needed with Islamic counseling options
4. **Session tracking** for follow-up care and progress monitoring

**Code path:** `mobile-app/screens/Emergency` → `backend/controllers/emergency` → `ai-service/endpoints/crisis` → `database/emergency_sessions`

### Daily Islamic Healing Journey
1. **User checks daily progress** and receives personalized Islamic content
2. **Journey analytics** track progress across the 5 soul layers
3. **Adaptive recommendations** based on user engagement and progress
4. **Community features** connect users through Heart Circles for support

**Code path:** `mobile-app/screens/Journey` → `backend/controllers/journey` → `ai-service/processors/adaptive_journey` → `database/journeys & analytics`

---

## 📈 Performance & Scale

### Performance Considerations
- **Caching:** Redis caching for AI responses and frequently accessed Islamic content
- **Database optimization:** Prisma ORM with optimized queries and connection pooling
- **Mobile optimization:** Expo optimizations for bundle size and startup time

### Monitoring
- **Metrics:** API response times, AI service latency, user engagement analytics
- **Alerts:** Error rates, database connection issues, AI service availability
- **Logging:** Structured logging with Winston (backend) and Python logging (AI service)

---

## 🚨 Things to Be Careful About

### 🔒 Security Considerations
- **Authentication:** Supabase Row Level Security (RLS) policies protect user data
- **Data handling:** All Islamic content and user data encrypted at rest and in transit
- **External APIs:** Rate limiting and API key rotation for OpenAI integration
- **Input validation:** Comprehensive validation using express-validator and Pydantic

### 📖 Islamic Content Integrity
- **Scholarly verification:** All Quranic verses and Islamic content verified by qualified scholars
- **Authentic sources:** Only authentic Hadith and Islamic teachings included
- **Cultural sensitivity:** Content reviewed for cultural appropriateness across Muslim communities
- **Translation accuracy:** Multiple verified translations for non-Arabic content

### 🔧 Technical Considerations
- **AI model reliability:** Fallback mechanisms for AI service failures
- **Database migrations:** Careful schema changes with Prisma migrations
- **Mobile platform differences:** iOS/Android specific considerations for Islamic calendar and prayer times
- **Offline functionality:** Critical Islamic content available offline for emergency situations

*Updated at: 2025-01-27 UTC*