# Authentication System Review and Migration Plan

## Current State

### Backend (`apps/backend`)
- Uses Supabase's `signUp`, `signInWithPassword`, and `getUser` for authentication.
- No custom OAuth or Google login logic in backend code.
- No endpoint for OAuth/provider login—only email/password flows are implemented.
- Middleware validates JWTs using Supabase's `getUser(token)`.
- User profiles are created in a separate `profiles` table after signup.

### Mobile App (`apps/mobile-app-v3`)
- Auth service only calls backend endpoints for `/api/auth/signin`, `/api/auth/signup`, etc.
- No direct use of Supabase client or SDK in the mobile app.
- No code for Google login, OAuth, or provider login.
- All authentication is via email/password, not via OAuth providers.

### OAuth/Google Login Support
- No code in either backend or mobile app for Supabase's OAuth provider login (e.g., Google).
- No endpoints or UI for "Sign in with Google" or similar flows.

---

## Conclusion

**The current system only supports email/password authentication. Supabase's OAuth (Google, etc.) is not implemented in either the backend or the mobile app.**

---

## Tasks to Achieve Complete Supabase Auth (Including OAuth/Google)

1. **Backend**
   - [ ] Add endpoints to support Supabase OAuth login (e.g., `/api/auth/oauth` or `/api/auth/provider/:provider`).
   - [ ] Handle OAuth callback and session/token management.
   - [ ] Document supported providers and flows.

2. **Mobile App**
   - [ ] Add UI for "Sign in with Google" (and other providers as needed).
   - [ ] Integrate with Supabase's OAuth flow (using deep links or webview).
   - [ ] Handle OAuth token/session and store in app.

3. **General**
   - [ ] Update documentation and onboarding to reflect new auth options.
   - [ ] Test all flows (email/password, Google, etc.) end-to-end.

---

## Supabase OAuth Providers & Backend Flow

### Supported Providers
- Google (recommended)
- (Others supported by Supabase: Apple, Facebook, GitHub, etc. — can be enabled in Supabase dashboard)

### Backend OAuth Flow
1. **Start OAuth:**
   - Client requests `/api/auth/oauth/:provider` (e.g., `/api/auth/oauth/google`).
   - Backend redirects to Supabase's OAuth URL for the provider.
2. **User Authenticates:**
   - User completes login with the provider (e.g., Google).
   - Supabase redirects to the configured `redirect_to` URL (set in Supabase dashboard and `.env` as `SUPABASE_OAUTH_REDIRECT_URL`).
3. **Token Handling:**
   - For web: Supabase returns the access token in the URL fragment (handled client-side).
   - For mobile: Use deep linking or WebView to capture the token and pass it to the app.
4. **Session:**
   - The client uses the access token as a Bearer token for authenticated API requests.

---

## Unified OAuth Approach for Mobile & Web

### Mobile App (React Native/Expo)
- Uses Supabase OAuth via browser (expo-web-browser) and deep linking.
- The app listens for a custom deep link (e.g., `qalbhealing://auth-callback`) as the `redirect_to` in Supabase settings.
- After successful login, Supabase redirects to the app with the access token in the URL fragment.
- The app extracts and stores the token for authenticated API requests.
- No changes needed to backend auth APIs; the mobile app uses the same Bearer token as with email/password.

### Web App
- Uses Supabase JS SDK directly in the frontend (`supabase.auth.signInWithOAuth`).
- Supabase handles the redirect and session management in the browser.
- The session is stored in localStorage/cookies and used for API requests.
- No changes needed to backend auth APIs; the web app uses the same Bearer token as with email/password.

### Why This Approach?
- Secure, simple, and leverages Supabase’s built-in OAuth for both platforms.
- No need to handle Google SDKs or tokens directly.
- Minimal backend changes—just add the OAuth redirect endpoint for convenience.
- Scalable to other providers (Apple, Facebook, etc.) with minimal config.

---

# Implementation Summary
- Added `/api/auth/oauth/:provider` endpoint in backend to redirect to Supabase OAuth login.
- Added `GoogleSignInButton` for mobile app to launch OAuth flow.
- Added `useOAuthDeepLinkHandler` hook to handle deep link and token extraction in mobile app.
- Integrated the hook into the main app layout for global handling.
- No changes required to backend auth APIs for token validation or user profile endpoints.

---

# Next Steps
- Test the end-to-end flow on both mobile and web.
- Update onboarding/login screens to include Google sign-in for best UX.
- (Optional) Add support for more providers in Supabase dashboard.

---

## Manual Testing Instructions for Supabase Auth Flow

### 1. Supabase Dashboard Setup
- Enable Google (and any other desired providers) in your Supabase project’s Authentication > Providers.
- Set the OAuth redirect URL to your app’s deep link (e.g., `qalbhealing://auth-callback`) for mobile, and your web app URL for web.

### 2. Backend
- Start your backend server.
- Confirm `/api/auth/oauth/google` redirects to the Google login page.
- Confirm `/api/auth/oauth/callback` is reachable (for web, or as a placeholder for mobile).

### 3. Mobile App
- Build and run the app on a real device or simulator.
- On the onboarding welcome screen, tap “Sign in with Google.”
- Complete the Google login in the browser.
- When redirected back to the app, ensure you are logged in and can access authenticated features.
- Try logging out and logging in again.
- Also test email/password signup and login.

### 4. Web App (if applicable)
- Use Supabase JS SDK’s `signInWithOAuth` for Google login.
- Complete the flow and ensure the session is established and protected routes are accessible.

#### Sign-In Button Options
- **Recommended:** Use your own application code for the sign-in button and call Supabase’s `signInWithOAuth` method. This approach is:
  - Simple to implement and maintain.
  - Consistent with your mobile app’s OAuth flow.
  - Secure, as Supabase handles all OAuth logic, redirects, and session management.
  - Easy to extend to other providers in the future.
- **How to implement:**
  - Add a “Sign in with Google” button in your web app UI.
  - On click, call:
    ```js
    supabase.auth.signInWithOAuth({ provider: 'google' });
    ```
  - Supabase will handle the redirect and session for you.
- **Alternative (not required for most cases):**
  - Use Google’s pre-built Sign-In or One Tap UI. This requires extra integration steps to exchange Google tokens for Supabase sessions and is only needed if you want a more native Google experience or advanced Google-specific features.

### 5. Token Validation
- Confirm that API requests from the app include the Bearer token in the `Authorization` header.
- Confirm the backend validates the token using Supabase’s `getUser(token)`.

### 6. Edge Cases
- Try invalid/expired tokens.
- Try logging in with a different provider (if enabled).
- Try logging out and ensure tokens are cleared.
