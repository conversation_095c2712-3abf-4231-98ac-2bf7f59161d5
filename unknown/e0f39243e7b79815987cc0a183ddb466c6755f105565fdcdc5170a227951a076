import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock emergency types
interface EmergencyContact {
  id: string;
  name: string;
  phone: string;
}

interface EmergencyResource {
  id: string;
  title: string;
  description: string;
}

const EMERGENCY_STORAGE_KEY = 'qalb_emergency_data';

export async function loadEmergencyData(): Promise<{
  contacts: EmergencyContact[];
  resources: EmergencyResource[];
  hasCompletedSetup?: boolean;
}> {
  try {
    const data = await AsyncStorage.getItem(EMERGENCY_STORAGE_KEY);
    if (data) {
      return JSON.parse(data);
    }
    return { contacts: [], resources: [], hasCompletedSetup: false };
  } catch (error) {
    console.error('Failed to load emergency data from storage:', error);
    return { contacts: [], resources: [], hasCompletedSetup: false };
  }
}

export async function saveEmergencyData(
  contacts: EmergencyContact[],
  resources: EmergencyResource[],
  hasCompletedSetup: boolean
): Promise<void> {
  try {
    await AsyncStorage.setItem(
      EMERGENCY_STORAGE_KEY,
      JSON.stringify({ contacts, resources, hasCompletedSetup })
    );
  } catch (error) {
    console.error('Failed to save emergency data to storage:', error);
  }
}
