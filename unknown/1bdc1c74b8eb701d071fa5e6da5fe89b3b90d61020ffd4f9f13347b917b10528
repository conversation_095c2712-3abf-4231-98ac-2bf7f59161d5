import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  LayoutAnimation,
  Platform,
  UIManager,
  Animated,
} from 'react-native';
import { colors } from "../../constants/Colors";

// Enable LayoutAnimation for Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

// Duration options for symptoms
export const DURATION_OPTIONS = [
  { label: 'Less than a week', value: 'less_than_week' },
  { label: '1-4 weeks', value: '1-4_weeks' },
  { label: '1-3 months', value: '1-3_months' },
  { label: '3-6 months', value: '3-6_months' },
  { label: '6-12 months', value: '6-12_months' },
  { label: 'More than a year', value: 'more_than_year' },
];

interface DurationSelectorProps {
  /**
   * Currently selected duration value
   */
  value: string;

  /**
   * Called when a duration option is selected
   */
  onChange: (value: string) => void;

  /**
   * Optional title for the selector
   */
  title?: string;

  /**
   * Optional custom styles
   */
  style?: object;
}

/**
 * A component for selecting symptom duration
 */
export default function DurationSelector({
  value,
  onChange,
  title = 'How long have you been experiencing these symptoms?',
  style,
}: DurationSelectorProps) {
  const styles = createStyles(colors);

  // Animation values for selection feedback
  const [animValues] = useState(() =>
    DURATION_OPTIONS.reduce((acc, option) => {
      acc[option.value] = new Animated.Value(option.value === value ? 1 : 0);
      return acc;
    }, {} as Record<string, Animated.Value>)
  );

  // Handle selecting a duration
  const handleSelect = (optionValue: string) => {
    // Configure layout animation
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);

    // Animate previous selection out
    if (value !== optionValue && animValues[value]) {
      Animated.timing(animValues[value], {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }

    // Animate new selection in
    Animated.timing(animValues[optionValue], {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Call onChange with the new value
    onChange(optionValue);
  };

  return (
    <View style={[styles.container, style]}>
      {title && <Text style={styles.title}>{title}</Text>}

      <View style={styles.optionsContainer}>
        {DURATION_OPTIONS.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.option,
              value === option.value && styles.optionSelected,
            ]}
            onPress={() => handleSelect(option.value)}
            activeOpacity={0.7}
            accessible={true}
            accessibilityLabel={`Duration option: ${option.label}`}
            accessibilityState={{ selected: value === option.value }}
            accessibilityHint="Selects this duration for your symptoms"
          >
            <Text
              style={[
                styles.optionText,
                value === option.value && styles.optionTextSelected,
              ]}
            >
              {option.label}
            </Text>

            {value === option.value && (
              <Animated.View
                style={[
                  styles.selectedIndicator,
                  {
                    opacity: animValues[option.value],
                    transform: [
                      {
                        scale: animValues[option.value].interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.8, 1],
                        }),
                      },
                    ],
                  },
                ]}
              />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginVertical: 16,
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 12,
    },
    optionsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginHorizontal: -4,
    },
    option: {
      flexGrow: 1,
      flexBasis: '45%',
      margin: 4,
      paddingVertical: 14,
      paddingHorizontal: 16,
      borderWidth: 1,
      borderColor: '#DDDDDD',
      borderRadius: 8,
      backgroundColor: 'white',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      overflow: 'hidden',
    },
    optionSelected: {
      borderColor: colors.primary,
      backgroundColor: colors.primaryLight,
    },
    optionText: {
      fontSize: 14,
      color: colors.text,
      textAlign: 'center',
    },
    optionTextSelected: {
      fontWeight: '600',
      color: colors.primary,
    },
    selectedIndicator: {
      position: 'absolute',
      top: 6,
      right: 6,
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: colors.primary,
    },
  });
