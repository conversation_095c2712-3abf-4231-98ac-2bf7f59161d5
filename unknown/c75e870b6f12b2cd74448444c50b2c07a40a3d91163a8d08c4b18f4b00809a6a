import { Link, Stack } from 'expo-router';
import { StyleSheet } from 'react-native';

import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Not Found' }} />
      <View style={styles.container}>
        <Text variant="heading2" style={styles.title}>
          This screen doesn't exist
        </Text>
        <Text variant="body" style={styles.message}>
          The page you are looking for could not be found. It might have been
          removed, renamed, or is temporarily unavailable.
        </Text>

        <Link href="/" style={[styles.link, { color: colors.primary }]}>
          <Text variant="subtitle" color="primary">
            Go to home screen
          </Text>
        </Link>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Theme.spacing.l,
  },
  title: {
    marginBottom: Theme.spacing.m,
    textAlign: 'center',
  },
  message: {
    marginBottom: Theme.spacing.l,
    textAlign: 'center',
  },
  link: {
    paddingVertical: Theme.spacing.s,
  },
});
