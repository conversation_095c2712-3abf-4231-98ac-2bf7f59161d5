/**
 * Global state management for Qalb Healing app
 * Provides context for symptoms, user data, and app state
 */

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { SymptomCategory, SymptomSubmission } from '../services/api/types';
import serviceRegistry from '../services/ServiceRegistry';

// State interfaces
interface SymptomsState {
  categories: SymptomCategory[];
  selectedSymptoms: Record<string, string[]>;
  intensity: Record<string, number>;
  duration: string;
  isLoading: boolean;
  error: string | null;
}

interface UserState {
  isAuthenticated: boolean;
  profile: any | null;
  preferences: any | null;
}

interface AppState {
  symptoms: SymptomsState;
  user: UserState;
  theme: 'light' | 'dark';
}

// Action types
type Action =
  | { type: 'SET_SYMPTOM_CATEGORIES'; payload: SymptomCategory[] }
  | { type: 'SELECT_SYMPTOM'; payload: { category: string; symptomId: string } }
  | {
      type: 'DESELECT_SYMPTOM';
      payload: { category: string; symptomId: string };
    }
  | { type: 'SET_INTENSITY'; payload: { symptomId: string; intensity: number } }
  | { type: 'SET_DURATION'; payload: string }
  | { type: 'SET_SYMPTOMS_LOADING'; payload: boolean }
  | { type: 'SET_SYMPTOMS_ERROR'; payload: string | null }
  | { type: 'RESET_SYMPTOMS' }
  | { type: 'SET_USER_AUTHENTICATED'; payload: boolean }
  | { type: 'SET_USER_PROFILE'; payload: any }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' };

// Initial state
const initialState: AppState = {
  symptoms: {
    categories: [],
    selectedSymptoms: {},
    intensity: {},
    duration: '',
    isLoading: false,
    error: null,
  },
  user: {
    isAuthenticated: false,
    profile: null,
    preferences: null,
  },
  theme: 'light',
};

// Reducer
function appReducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'SET_SYMPTOM_CATEGORIES':
      return {
        ...state,
        symptoms: {
          ...state.symptoms,
          categories: action.payload,
        },
      };

    case 'SELECT_SYMPTOM': {
      const { category, symptomId } = action.payload;
      const currentSelected = state.symptoms.selectedSymptoms[category] || [];
      return {
        ...state,
        symptoms: {
          ...state.symptoms,
          selectedSymptoms: {
            ...state.symptoms.selectedSymptoms,
            [category]: [...currentSelected, symptomId],
          },
        },
      };
    }

    case 'DESELECT_SYMPTOM': {
      const { category: cat, symptomId: id } = action.payload;
      const selected = state.symptoms.selectedSymptoms[cat] || [];
      return {
        ...state,
        symptoms: {
          ...state.symptoms,
          selectedSymptoms: {
            ...state.symptoms.selectedSymptoms,
            [cat]: selected.filter((s) => s !== id),
          },
        },
      };
    }

    case 'SET_INTENSITY':
      return {
        ...state,
        symptoms: {
          ...state.symptoms,
          intensity: {
            ...state.symptoms.intensity,
            [action.payload.symptomId]: action.payload.intensity,
          },
        },
      };

    case 'SET_DURATION':
      return {
        ...state,
        symptoms: {
          ...state.symptoms,
          duration: action.payload,
        },
      };

    case 'SET_SYMPTOMS_LOADING':
      return {
        ...state,
        symptoms: {
          ...state.symptoms,
          isLoading: action.payload,
        },
      };

    case 'SET_SYMPTOMS_ERROR':
      return {
        ...state,
        symptoms: {
          ...state.symptoms,
          error: action.payload,
        },
      };

    case 'RESET_SYMPTOMS':
      return {
        ...state,
        symptoms: {
          ...initialState.symptoms,
          categories: state.symptoms.categories, // Keep categories
        },
      };

    case 'SET_USER_AUTHENTICATED':
      return {
        ...state,
        user: {
          ...state.user,
          isAuthenticated: action.payload,
        },
      };

    case 'SET_USER_PROFILE':
      return {
        ...state,
        user: {
          ...state.user,
          profile: action.payload,
        },
      };

    case 'SET_THEME':
      return {
        ...state,
        theme: action.payload,
      };

    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<Action>;
} | null>(null);

// Provider component
export function RootProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hooks
export function useAppState() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppState must be used within RootProvider');
  }
  return context;
}

export function useSymptoms() {
  const { state, dispatch } = useAppState();

  const selectSymptom = (category: string, symptomId: string) => {
    dispatch({ type: 'SELECT_SYMPTOM', payload: { category, symptomId } });
  };

  const deselectSymptom = (category: string, symptomId: string) => {
    dispatch({ type: 'DESELECT_SYMPTOM', payload: { category, symptomId } });
  };

  const setIntensity = (symptomId: string, intensity: number) => {
    dispatch({ type: 'SET_INTENSITY', payload: { symptomId, intensity } });
  };

  const setDuration = (duration: string) => {
    dispatch({ type: 'SET_DURATION', payload: duration });
  };

  const resetSymptoms = () => {
    dispatch({ type: 'RESET_SYMPTOMS' });
  };

  const fetchSymptoms = async () => {
    try {
      dispatch({ type: 'SET_SYMPTOMS_LOADING', payload: true });
      const symptomService = await serviceRegistry.getSymptoms();
      const categories = await symptomService.getSymptomCategories();
      dispatch({ type: 'SET_SYMPTOM_CATEGORIES', payload: categories });
      dispatch({ type: 'SET_SYMPTOMS_ERROR', payload: null });
    } catch (error) {
      dispatch({
        type: 'SET_SYMPTOMS_ERROR',
        payload: 'Failed to fetch symptoms',
      });
    } finally {
      dispatch({ type: 'SET_SYMPTOMS_LOADING', payload: false });
    }
  };

  const submitSymptoms = async (symptoms: any) => {
    try {
      dispatch({ type: 'SET_SYMPTOMS_LOADING', payload: true });
      const symptomService = await serviceRegistry.getSymptoms();
      const result = await symptomService.submitSymptoms(symptoms);
      dispatch({ type: 'SET_SYMPTOMS_ERROR', payload: null });
      return result;
    } catch (error) {
      dispatch({
        type: 'SET_SYMPTOMS_ERROR',
        payload: 'Failed to submit symptoms',
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_SYMPTOMS_LOADING', payload: false });
    }
  };

  return {
    symptomsState: state.symptoms,
    selectSymptom,
    deselectSymptom,
    setIntensity,
    setDuration,
    resetSymptoms,
    fetchSymptoms,
    submitSymptoms,
    dispatch,
  };
}

export function useUser() {
  const { state, dispatch } = useAppState();

  const setAuthenticated = (authenticated: boolean) => {
    dispatch({ type: 'SET_USER_AUTHENTICATED', payload: authenticated });
  };

  const setProfile = (profile: any) => {
    dispatch({ type: 'SET_USER_PROFILE', payload: profile });
  };

  return {
    userState: state.user,
    setAuthenticated,
    setProfile,
  };
}

export function useTheme() {
  const { state, dispatch } = useAppState();

  const setTheme = (theme: 'light' | 'dark') => {
    dispatch({ type: 'SET_THEME', payload: theme });
  };

  return {
    theme: state.theme,
    setTheme,
  };
}
