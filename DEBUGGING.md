# Debugging Setup for Qalb Healing Workspace

This document explains how to use the debugging setup for all three applications in the Qalb Healing workspace:
1. Backend (Express.js)
2. Mobile App (Expo React Native)
3. AI Service (Python FastAPI)

## VS Code Setup

### Prerequisites

1. Install the following VS Code extensions:
   - JavaScript Debugger
   - Python
   - React Native Tools
   - Nx Console (optional but recommended)

## Starting Applications in Debug Mode

### Option 1: Using VS Code Tasks

1. Open the Command Palette (`Cmd+Shift+P` on macOS)
2. Type "Tasks: Run Task" and select it
3. Choose one of the following:
   - "Start Backend"
   - "Start AI Service (Debug Mode)"
   - "Start Mobile App (Debug Mode)"
   - "Start All Services" (to start all three at once)

### Option 2: Using NPM Scripts

```bash
# Start backend in debug mode
npm run serve:backend

# Start mobile app in debug mode
npm run serve:mobile-app-v3:debug

# Start AI service in debug mode
npm run start:ai-service:debug

# Alternative: Start all using individual terminal windows
```

## Attaching Debuggers

### Backend Debugging

1. Once the backend is running in debug mode, go to the "Run and Debug" sidebar in VS Code (`Cmd+Shift+D` on macOS)
2. Select "Debug Backend" from the dropdown
3. Click the green play button

The debugger will attach to port 9229 where the Node.js inspector is running.

### Mobile App Debugging

1. Start the Expo app in debug mode using one of the methods above
2. In the "Run and Debug" sidebar, select "Debug Mobile App (Expo)"
3. Click the green play button

For device-specific debugging:
- Select "Debug Mobile App (iOS)" for iOS simulator/device
- Select "Debug Mobile App (Android)" for Android emulator/device

### AI Service Debugging

1. Start the AI service in debug mode using one of the methods above
2. In the "Run and Debug" sidebar, select "Debug AI Service"
3. Click the green play button

The debugger will attach to port 5678 where the Python debugger is running.

### Debugging Multiple Services at Once

You can debug multiple services simultaneously by:

1. Using the "Debug All" compound launch configuration
2. OR starting each service individually and then attaching debuggers one by one

## Setting Breakpoints

- Set breakpoints in your code by clicking in the gutter (left margin) of the editor
- Hover over variables during debugging to see their values
- Use the Debug Console to evaluate expressions

## Best Practices for NX-based Workspace

### Where to Run Scripts From

1. **Root Directory (Recommended)**: 
   - Always run scripts from the root of the workspace 
   - Use the scripts in the root `package.json`
   - This ensures proper dependency resolution within the NX workspace

2. **Individual App Directories (Advanced Use Cases Only)**:
   - Only use for app-specific development tasks
   - May not properly resolve workspace dependencies
   - Useful for focused development on a single app

### Script Naming Conventions

Our scripts follow these patterns:
- `serve:*` - Start an application in development mode
- `dev:*` - Start an application with watch mode
- `build:*` - Build an application
- `test:*` - Run tests
- `lint:*` - Run linting

## Troubleshooting

### Backend Debugging Issues
- Ensure port 9229 is available and not used by another process
- Check that sourcemaps are enabled in tsconfig.app.json
- Verify the backend is running with `--inspect` flag

### Mobile App Debugging Issues
- For Expo debugging, ensure you have the Expo Development client installed
- Try restarting the Metro bundler with `--clear` flag
- For device debugging, ensure the device is connected to the same network

### AI Service Debugging Issues
- Verify Python debugpy is installed (`pip install debugpy`)
- Ensure port 5678 is available
- Check path mappings in launch.json if breakpoints aren't hitting

## Additional Resources

- [Node.js Debugging Guide](https://nodejs.org/en/docs/guides/debugging-getting-started/)
- [Expo Debugging Documentation](https://docs.expo.dev/debugging/tools/)
- [Python debugpy Documentation](https://github.com/microsoft/debugpy)
- [NX Documentation](https://nx.dev/getting-started/intro)
