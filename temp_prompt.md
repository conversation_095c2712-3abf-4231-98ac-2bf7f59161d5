```
You are a senior product manager and UX designer tasked with finalizing the product specifications and creating the complete set of wireframes for the core features of the "Qalb Healing" app. Your goal is to ensure the product specs are clear, complete, and consistent, and that the wireframes are detailed, user-friendly, and strictly adhere to the project's Islamic design principles.

**Primary Objective:** Review, refine, and finalize the product specifications and create comprehensive wireframes for the core features of the Qalb Healing app.

**Reference Documents:**

You must base all your work on the following documents:

1.  **Core App Concept (The Single Source of Truth):**
    *   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/Qalb_Healing_App_Complete_Concept.md`

2.  **Design & Visual Principles (Strict Adherence Required):**
    *   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/wireframes/Islamic_Visual_Design_Guidelines.md`
    *   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/wireframes/Visual_Consistency_Standards.md`

**Tasks & Deliverables:**

**Part 1: Product Specification Finalization**

Review and finalize the following product specification documents. Ensure they are complete, consistent with the Core App Concept, and ready for the development team. This includes refining user stories, acceptance criteria, and feature details.

*   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/features/Feature_00_Adaptive_Onboarding_and_User_Profiling.md`
*   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/features/Feature_01_Islamic_Mental_Health_Assessment_v7_PRODUCT.md`
*   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/features/Feature_02_Integrated_Islamic_Healing_Journeys_v6.md`
*   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/features/Feature_03_Qalb_Rescue.md`
*   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/features/Feature_04_Simple_Daily_Dashboard_v2.md`
*   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/features/Feature_06A_Islamic_Knowledge_Hub.md`

**Part 2: Wireframe Creation & Review**

Your task is to create and review detailed wireframes for all the features listed above. The wireframes should be presented in Markdown format, clearly illustrating screen layouts, user flows, and key UI components.

1.  **Review and Finalize Existing Wireframes:**
    *   Review and update the following document to align with the final product spec and design guidelines:
        *   `@/Users/<USER>/Documents/Workspace/qalb-healing/qalb-healing-workspace/docs/qalb-healing-docs/wireframes/Feature_01_Islamic_Mental_Health_Assessment_v7_Wireframes.md`

2.  **Create New Wireframes:**
    *   Create new, separate wireframe documents for the remaining features. Ensure each wireframe document corresponds to its respective feature spec and meticulously follows the `Islamic_Visual_Design_Guidelines.md` and `Visual_Consistency_Standards.md`. The new files should be named logically (e.g., `Feature_00_Adaptive_Onboarding_Wireframes.md`).
        *   Feature 00: Adaptive Onboarding and User Profiling
        *   Feature 02: Integrated Islamic Healing Journeys
        *   Feature 03: Qalb Rescue
        *   Feature 04: Simple Daily Dashboard
        *   Feature 06A: Islamic Knowledge Hub

**Final Instruction:**
Ensure that the final product specifications and the corresponding wireframes are perfectly aligned with each other and with the overarching vision detailed in the `Qalb_Healing_App_Complete_Concept.md`. The output should be a complete, cohesive, and developer-ready set of documentation.
```