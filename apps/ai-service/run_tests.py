#!/usr/bin/env python3
"""
Comprehensive Test Runner for Qalb Healing AI Service
Runs all test suites with proper reporting and Islamic authenticity validation
"""

import subprocess
import sys
import os
import time
from pathlib import Path


class TestRunner:
    """Test runner for AI Service with Islamic authenticity validation"""
    
    def __init__(self):
        self.test_dir = Path(__file__).parent / "tests"
        self.results = {}
        
    def run_command(self, command, description):
        """Run a command and capture results"""
        print(f"\n{'='*60}")
        print(f"🧪 {description}")
        print(f"{'='*60}")
        
        start_time = time.time()
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                cwd=Path(__file__).parent
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ PASSED ({duration:.2f}s)")
                if result.stdout:
                    print(result.stdout)
                self.results[description] = {"status": "PASSED", "duration": duration}
            else:
                print(f"❌ FAILED ({duration:.2f}s)")
                if result.stderr:
                    print("STDERR:", result.stderr)
                if result.stdout:
                    print("STDOUT:", result.stdout)
                self.results[description] = {"status": "FAILED", "duration": duration}
                
        except Exception as e:
            print(f"💥 ERROR: {str(e)}")
            self.results[description] = {"status": "ERROR", "duration": 0}
    
    def run_core_tests(self):
        """Run core functionality tests"""
        print("\n🕌 RUNNING CORE ISLAMIC AI TESTS")
        
        # Spiritual Analysis Tests
        self.run_command(
            "poetry run pytest tests/test_spiritual_analysis.py -v --tb=short",
            "Spiritual Analysis (5-Layer Framework)"
        )
        
        # Symptom Analyzer Tests
        self.run_command(
            "poetry run pytest tests/test_symptom_analyzer.py -v --tb=short",
            "Symptom Analysis (AI Integration)"
        )
        
        # Crisis Detection Tests
        self.run_command(
            "poetry run pytest tests/test_crisis_detection.py -v --tb=short",
            "Crisis Detection (Islamic Context)"
        )
        
        # Journey Generation Tests
        self.run_command(
            "poetry run pytest tests/test_journey_generation.py -v --tb=short",
            "Journey Generation (Personalization)"
        )
        
        # Content Recommendation Tests
        self.run_command(
            "poetry run pytest tests/test_content_recommendations.py -v --tb=short",
            "Content Recommendations (Islamic Matching)"
        )
    
    def run_islamic_authenticity_tests(self):
        """Run Islamic authenticity and cultural sensitivity tests"""
        print("\n☪️ RUNNING ISLAMIC AUTHENTICITY TESTS")
        
        self.run_command(
            "poetry run pytest tests/test_islamic_context.py -v --tb=short",
            "Islamic Context & Cultural Sensitivity"
        )
    
    def run_api_tests(self):
        """Run API endpoint tests"""
        print("\n🌐 RUNNING API ENDPOINT TESTS")
        
        self.run_command(
            "poetry run pytest tests/test_api_endpoints.py -v --tb=short",
            "FastAPI Endpoints & Authentication"
        )
    
    def run_integration_tests(self):
        """Run integration tests"""
        print("\n🔗 RUNNING INTEGRATION TESTS")
        
        self.run_command(
            "poetry run pytest tests/test_integration.py -v --tb=short",
            "Component Integration & Data Flow"
        )
    
    def run_performance_tests(self):
        """Run performance and algorithm tests"""
        print("\n⚡ RUNNING PERFORMANCE TESTS")
        
        self.run_command(
            "poetry run pytest tests/test_performance.py -v --tb=short",
            "Performance & Algorithm Accuracy"
        )
    
    def run_coverage_tests(self):
        """Run tests with coverage reporting"""
        print("\n📊 RUNNING COVERAGE ANALYSIS")
        
        self.run_command(
            "poetry run pytest --cov=ai_service --cov-report=html --cov-report=term --cov-report=xml",
            "Test Coverage Analysis"
        )
    
    def run_parallel_tests(self):
        """Run tests in parallel for performance"""
        print("\n🚀 RUNNING PARALLEL TESTS")
        
        self.run_command(
            "poetry run pytest -n auto --tb=short",
            "Parallel Test Execution"
        )
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🕌 QALB HEALING AI SERVICE - COMPREHENSIVE TEST SUITE")
        print("=" * 60)
        print("Testing Islamic Mental Health AI with Cultural Authenticity")
        print("=" * 60)
        
        # Check if we're in the right directory
        if not (Path.cwd() / "ai_service").exists():
            print("❌ Error: Please run from the ai-service directory")
            sys.exit(1)
        
        # Run test suites
        self.run_core_tests()
        self.run_islamic_authenticity_tests()
        self.run_api_tests()
        self.run_integration_tests()
        self.run_performance_tests()
        self.run_coverage_tests()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test execution summary"""
        print("\n" + "="*60)
        print("📋 TEST EXECUTION SUMMARY")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r["status"] == "PASSED")
        failed_tests = sum(1 for r in self.results.values() if r["status"] == "FAILED")
        error_tests = sum(1 for r in self.results.values() if r["status"] == "ERROR")
        
        total_duration = sum(r["duration"] for r in self.results.values())
        
        print(f"📊 Total Test Suites: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"💥 Errors: {error_tests}")
        print(f"⏱️  Total Duration: {total_duration:.2f}s")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n📝 Detailed Results:")
        for test_name, result in self.results.items():
            status_emoji = "✅" if result["status"] == "PASSED" else "❌" if result["status"] == "FAILED" else "💥"
            print(f"  {status_emoji} {test_name}: {result['status']} ({result['duration']:.2f}s)")
        
        # Islamic authenticity check
        islamic_tests = [name for name in self.results.keys() if "Islamic" in name or "Spiritual" in name or "Crisis" in name]
        islamic_passed = sum(1 for name in islamic_tests if self.results[name]["status"] == "PASSED")
        
        print(f"\n☪️ Islamic Authenticity: {islamic_passed}/{len(islamic_tests)} tests passed")
        
        if failed_tests > 0 or error_tests > 0:
            print("\n⚠️  Some tests failed. Please review the output above.")
            print("🔍 For detailed error information, run individual test files.")
            sys.exit(1)
        else:
            print("\n🎉 ALL TESTS PASSED! The AI service is ready for Islamic mental health support.")
            print("🕌 Islamic authenticity and cultural sensitivity validated.")


def main():
    """Main test runner function"""
    runner = TestRunner()
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "core":
            runner.run_core_tests()
        elif test_type == "islamic":
            runner.run_islamic_authenticity_tests()
        elif test_type == "api":
            runner.run_api_tests()
        elif test_type == "integration":
            runner.run_integration_tests()
        elif test_type == "performance":
            runner.run_performance_tests()
        elif test_type == "coverage":
            runner.run_coverage_tests()
        elif test_type == "parallel":
            runner.run_parallel_tests()
        elif test_type == "all":
            runner.run_all_tests()
        else:
            print("Usage: python run_tests.py [core|islamic|api|integration|performance|coverage|parallel|all]")
            sys.exit(1)
    else:
        runner.run_all_tests()
    
    runner.print_summary()


if __name__ == "__main__":
    main()
