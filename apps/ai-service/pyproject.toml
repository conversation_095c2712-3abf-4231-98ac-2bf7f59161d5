[tool.coverage.run]
branch = true
source = [ "ai_service" ]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v"

[tool.poetry]
name = "ai-service"
version = "1.0.0"
description = "Qalb Healing AI Service - Islamic Mental Wellness AI Processing"
authors = ["Qalb Healing Team"]
license = 'Proprietary'
readme = 'README.md'

  [[tool.poetry.packages]]
  include = "ai_service"

  [tool.poetry.dependencies]
  python = ">=3.9,<3.11"
  fastapi = "^0.104.1"
  uvicorn = "^0.24.0"
  pydantic = "^2.5.0"
  openai = "^1.3.0"
  python-dotenv = "^1.0.0"
  httpx = "^0.25.0"
  redis = "^5.0.0"
  supabase = "^2.0.0"
  langchain = "^0.0.350"
  langchain-openai = "^0.0.2"
  numpy = "^1.24.0"
  pandas = "^2.1.0"
  scikit-learn = "^1.3.0"
  python-multipart = "^0.0.6"
  pyjwt = "^2.8.0"
  cryptography = "^41.0.0"

  [tool.poetry.group.dev.dependencies]
  autopep8 = "2.3.1"
  flake8 = "7.1.1"
  pytest = "8.3.4"
  pytest-sugar = "1.0.0"
  pytest-cov = "6.0.0"
  pytest-html = "4.1.1"
  pytest-asyncio = "^0.23.0"
  pytest-mock = "^3.12.0"
  pytest-xdist = "^3.5.0"
  pytest-benchmark = "^4.0.0"
  httpx = "^0.25.0"
  faker = "^22.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
