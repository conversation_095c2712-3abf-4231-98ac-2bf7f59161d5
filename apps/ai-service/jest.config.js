/**
 * Jest Configuration for Qalb Healing AI Service
 * Comprehensive testing setup for AI algorithms with Islamic context validation
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup.ts'],
  
  testMatch: [
    '<rootDir>/__tests__/**/*.test.{js,ts}',
    '<rootDir>/src/**/__tests__/**/*.{js,ts}',
    '<rootDir>/src/**/*.{test,spec}.{js,ts}',
  ],
  
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/**/*.d.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.module.ts',
    '!src/main.ts',
  ],
  
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    './src/services/crisis-detection.service.ts': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    './src/services/assessment-ai.service.ts': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    './src/services/islamic-context.service.ts': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@models/(.*)$': '<rootDir>/src/models/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@types/(.*)$': '<rootDir>/src/types/$1',
    '^@config/(.*)$': '<rootDir>/src/config/$1',
  },
  
  transform: {
    '^.+\\.(ts|js)$': 'ts-jest',
  },
  
  moduleFileExtensions: ['ts', 'js', 'json'],
  
  testTimeout: 15000, // AI operations may take longer
  verbose: true,
  bail: false,
  maxWorkers: '50%',
  
  // AI and Islamic content validation
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
    },
    __AI_MODEL_VALIDATION__: true,
    __ISLAMIC_CONTENT_VALIDATION__: true,
    __ARABIC_TEXT_VALIDATION__: true,
    __QURAN_REFERENCE_VALIDATION__: true,
    __CRISIS_DETECTION_VALIDATION__: true,
  },
  
  // Test reporting
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: './coverage',
        outputName: 'junit.xml',
        ancestorSeparator: ' › ',
        uniqueOutputName: 'false',
        suiteNameTemplate: '{filepath}',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
      },
    ],
  ],
  
  // Performance monitoring for AI operations
  detectOpenHandles: false,
  detectLeaks: false,
  logHeapUsage: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Cache configuration
  cacheDirectory: '<rootDir>/.jest-cache',
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  
  // AI model mocking
  setupFiles: ['<rootDir>/__tests__/ai-mocks.ts'],
};
