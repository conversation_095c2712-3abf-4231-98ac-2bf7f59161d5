{"name": "ai-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/ai-service/ai_service", "targets": {"lock": {"executor": "@nxlv/python:lock", "options": {"update": false}}, "sync": {"executor": "@nxlv/python:sync", "options": {}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "{projectRoot}/dist", "publish": false, "lockedVersions": true, "bundleLocalDependencies": true}, "cache": true}, "lint": {"executor": "@nxlv/python:flake8", "outputs": ["{workspaceRoot}/reports/{projectRoot}/pylint.txt"], "options": {"outputFile": "reports/{projectRoot}/pylint.txt"}, "cache": true}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/{projectRoot}/unittests", "{workspaceRoot}/coverage/{projectRoot}"], "options": {"command": "poetry run pytest tests/", "cwd": "{projectRoot}"}, "cache": true}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "cacheDir": ".cache/pypoetry", "verbose": false, "debug": false}}}, "tags": [], "release": {"version": {"generator": "@nxlv/python:release-version"}}}