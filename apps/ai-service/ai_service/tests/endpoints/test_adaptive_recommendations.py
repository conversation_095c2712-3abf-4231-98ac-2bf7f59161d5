import unittest
from unittest.mock import patch
from fastapi.testclient import TestClient
import sys
import os

# Add the parent directory of 'ai_service' to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
ai_service_dir = os.path.abspath(os.path.join(current_dir, '..', '..')) # Moves up two levels
sys.path.insert(0, ai_service_dir)

# Import the FastAPI app instance from main
from ai_service.main import app

class TestAdaptiveRecommendations(unittest.TestCase):

    def setUp(self):
        self.client = TestClient(app)
        # Mock the authentication dependency for these tests
        # This assumes 'verify_token' is the name of the dependency function in main.py
        app.dependency_overrides[self.get_verify_token_dependency()] = lambda: {"user_id": "test_user"}


    def get_verify_token_dependency(self):
        # Helper to get the actual dependency function object if it's not directly importable
        # This is a bit of a workaround if verify_token is defined in main.py and not easily imported.
        # It iterates over route dependencies. A more robust way would be to ensure verify_token is importable.
        for route in app.routes:
            if hasattr(route, "dependencies"):
                for dep in route.dependencies:
                    if dep.call.__name__ == "verify_token":
                        return dep.call
        return None # Should not happen if verify_token is used

    def tearDown(self):
        # Clear the dependency override after tests
        app.dependency_overrides = {}

    def test_low_overall_rating(self):
        """Test adaptive recommendation for low overall rating."""
        progress_data = {
            "overallRating": 1, # Low rating (1-5 scale)
            "moodBefore": 5, "moodAfter": 4, # Other data
        }
        request_payload = {
            "userId": "user123",
            "journeyId": "journey456",
            "progress": progress_data,
            "context": "daily_submission"
        }
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertIn("adjustments", data)
        self.assertTrue(any(adj['type'] == 'DIFFICULTY_ADJUSTMENT' and adj['value'] == 'decrease' for adj in data['adjustments']))
        self.assertTrue(any(adj['type'] == 'ADD_MOTIVATIONAL_MESSAGE' for adj in data['adjustments']))
        self.assertIn("Low overall satisfaction reported.", data['reasoning'])

    def test_low_mood_after_practices(self):
        """Test adaptive recommendation for low mood after practices."""
        progress_data = {
            "overallRating": 3,
            "moodBefore": 7, # Higher mood before
            "moodAfter": 2,  # Low mood after (1-10 scale)
        }
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertTrue(any(adj['type'] == 'CONTENT_THEME_ADJUSTMENT' and adj['preferredThemes'] for adj in data['adjustments']))
        self.assertIn("Low mood reported after practices.", data['reasoning'])

    def test_time_management_challenge(self):
        """Test adaptive recommendation for time management challenges."""
        progress_data = {
            "overallRating": 4,
            "moodAfter": 6,
            "challengesFaced": "I found it hard to find enough time for all the practices today."
        }
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertTrue(any(adj['type'] == 'PRACTICE_DURATION_ADJUSTMENT' for adj in data['adjustments']))
        self.assertIn("Time management challenges reported", data['reasoning'])

    def test_difficulty_understanding_challenge(self):
        """Test adaptive recommendation for difficulty understanding content."""
        progress_data = {
            "overallRating": 3,
            "moodAfter": 5,
            "challengesFaced": "Some of the concepts were difficult to grasp."
        }
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(any(adj['type'] == 'CONTENT_COMPLEXITY_ADJUSTMENT' for adj in data['adjustments']))
        self.assertIn("Difficulty in understanding content", data['reasoning'])

    def test_negative_practice_specific_feedback(self):
        """Test recommendation for negative feedback on a specific practice."""
        progress_data = {
            "overallRating": 3,
            "moodAfter": 5,
            "practiceSpecificFeedback": [
                {"practiceId": "qvr_abc", "practiceType": "QuranicVerseReflection", "rating": 1}
            ]
        }
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(any(adj['type'] == 'PRACTICE_VARIATION_REQUEST' and adj['targetPracticeId'] == "qvr_abc" for adj in data['adjustments']))
        self.assertIn("Low rating on practice qvr_abc triggered a variation request.", data['reasoning'])


    def test_high_overall_rating(self):
        """Test adaptive recommendation for high overall rating."""
        progress_data = {
            "overallRating": 5, # High rating
            "moodAfter": 9,
        }
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(any(adj['type'] == 'CONTENT_DEPTH_ADJUSTMENT' for adj in data['adjustments']))
        self.assertIn("High overall satisfaction suggests readiness for deeper content.", data['reasoning'])

    def test_no_specific_triggers(self):
        """Test adaptive recommendation when no specific triggers are met."""
        progress_data = {
            "overallRating": 4,
            "moodAfter": 7,
            "challengesFaced": "None today, alhamdulillah."
        }
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertTrue(len(data['adjustments']) == 0, "Expected no adjustments for neutral feedback.")
        self.assertIn("Current progress appears stable. Standard encouragement provided.", data['reasoning'])
        self.assertTrue(len(data['recommendations']) > 0) # Should still get some textual recommendation

    # --- New/Enhanced Tests for AdaptiveJourneyProcessor logic via endpoint ---

    @patch('ai_service.processors.adaptive_journey_processor.AdaptiveJourneyProcessor.process_adaptive_recommendations')
    def test_historical_mood_decline_triggers_wellbeing_check(self, mock_process_adaptive):
        """Test (mocked) historical mood decline leads to wellbeing check."""
        # Configure the mock to return a specific response simulating the declining mood logic
        mock_process_adaptive.return_value = {
            "recommendations": ["We've noticed your mood trend has been a bit lower recently..."],
            "adjustments": [{
                'type': 'WELLBEING_CHECK_SUGGESTION',
                'message': 'It might be helpful to reflect...',
                'details': {'suggested_action': 'journal_on_feelings', 'offer_support_options': True},
                'reason': 'Declining mood trend observed over recent period.'
            }],
            "reasoning": "Analysis of progress for daily_submission: Trend analysis suggests a period of declining mood."
        }

        progress_data = {"overallRating": 3, "moodAfter": 3} # Data that itself might not trigger decline
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        mock_process_adaptive.assert_called_once()
        self.assertTrue(any(adj['type'] == 'WELLBEING_CHECK_SUGGESTION' for adj in data['adjustments']))
        self.assertIn("Trend analysis suggests a period of declining mood.", data['reasoning'])

    def test_practice_variation_request_includes_specific_suggestions(self):
        """Test PRACTICE_VARIATION_REQUEST includes specific suggestions."""
        progress_data = {
            "overallRating": 2,
            "practiceSpecificFeedback": [{
                "practiceId": "qvr_001", # Assumed to be QuranicVerseReflection, theme 'guidance' in stub
                "practiceType": "QuranicVerseReflection",
                "practiceTheme": "guidance", # Provide theme for better suggestion
                "rating": 1
            }]
        }
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()

        variation_requests = [adj for adj in data['adjustments'] if adj['type'] == 'PRACTICE_VARIATION_REQUEST']
        self.assertTrue(len(variation_requests) > 0)

        # Based on current _suggest_alternative_for_practice stub logic:
        # QuranicVerseReflection "guidance" (if heavy) -> NameOfAllahSpotlight "mercy"
        # Or if "guidance" not in ["accountability", "struggle"], it might suggest SunnahPractice "gratitude"
        # Let's check for presence of suggestion keys
        self.assertIn('suggested_alternative_type', variation_requests[0])
        self.assertIn('suggested_alternative_theme', variation_requests[0])
        # Example check (might need adjustment based on exact stub logic outcome)
        self.assertIn(variation_requests[0]['suggested_alternative_type'], ["NameOfAllahSpotlight", "SunnahPractice"])


    def test_motivational_message_type_in_adjustment(self):
        """Verify correct messageType in ADD_MOTIVATIONAL_MESSAGE adjustment."""
        progress_data = {"overallRating": 1} # Triggers 'encouragement_after_difficulty'
        request_payload = {"userId": "user123", "journeyId": "journey456", "progress": progress_data, "context": "daily_submission"}
        response = self.client.post("/journey/adaptive-recommendations", json=request_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()

        motivational_messages = [adj for adj in data['adjustments'] if adj['type'] == 'ADD_MOTIVATIONAL_MESSAGE']
        self.assertTrue(len(motivational_messages) > 0)
        self.assertEqual(motivational_messages[0]['messageType'], 'encouragement_after_difficulty')


if __name__ == '__main__':
    # Need to make sure the verify_token dependency can be found by get_verify_token_dependency
    # This might require running tests in a specific way or ensuring main.py structures verify_token for easier access.
    # For now, assuming it can be found or will be adjusted if there's an issue during test execution.
    unittest.main()
