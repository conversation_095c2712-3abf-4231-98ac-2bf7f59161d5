import unittest
from unittest.mock import patch, MagicMock, PropertyMock
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory of 'ai_service' to the Python path
# This is to ensure that 'from ai_service.processors.journey_generation import JourneyGenerationProcessor' works
current_dir = os.path.dirname(os.path.abspath(__file__))
ai_service_dir = os.path.abspath(os.path.join(current_dir, '..', '..')) # Moves up two levels
sys.path.insert(0, ai_service_dir)

from ai_service.processors.journey_generation import JourneyGenerationProcessor

class TestJourneyGenerationProcessor(unittest.TestCase):

    def setUp(self):
        """Set up for test methods."""
        self.processor = JourneyGenerationProcessor()
        # Reset cache manually before each test for isolation
        self.processor._practice_library_cache = None
        self.processor._practice_library_cache_timestamp = None

        # Keep a reference to the original _load_practice_library if needed, or mock it.
        # For most tests below, we'll directly set self.processor.practice_library
        # to self.mock_practice_library to bypass actual loading/caching for simplicity
        # when testing selection logic. Caching tests will handle _load_practice_library specifically.

        # Mock the practice library for focused testing of selection logic
        self.mock_practice_library = {
            "NameOfAllahSpotlight": [
                {"id": "noa_1", "baseLayerFocus": "qalb", "themeTags": ["mercy"], "difficultyLevel": "beginner", "componentDetails": {"name":"Ar-Rahman"}},
                {"id": "noa_2", "baseLayerFocus": "qalb", "themeTags": ["peace"], "difficultyLevel": "beginner", "componentDetails": {"name":"As-Salam"}},
                {"id": "noa_3", "baseLayerFocus": "aql", "themeTags": ["knowledge"], "difficultyLevel": "intermediate", "componentDetails": {"name":"Al-Aleem"}},
            ],
            "QuranicVerseReflection": [
                {"id": "qvr_1", "baseLayerFocus": "ruh", "themeTags": ["guidance"], "difficultyLevel": "beginner", "componentDetails": {"surah":1}},
                {"id": "qvr_2", "baseLayerFocus": "qalb", "themeTags": ["patience"], "difficultyLevel": "intermediate", "componentDetails": {"surah":2}},
            ],
            "SunnahPractice": [
                {"id": "sp_1", "baseLayerFocus": "jism", "themeTags": ["purification"], "difficultyLevel": "beginner", "componentDetails": {"category":"Physical"}},
                {"id": "sp_2", "baseLayerFocus": "qalb", "themeTags": ["kindness"], "difficultyLevel": "beginner", "componentDetails": {"category":"Social"}},
            ]
        }
        self.processor.practice_library = self.mock_practice_library

        self.mock_user_profile = {"awarenessLevel": "symptom_aware", "name": "Test User"}
        self.mock_assessment_data = {"severityScore": 5, "primaryLayer": "qalb"} # Moderate severity
        self.mock_journey_config = {"duration": 7, "type": "heart_purification", "primaryLayer": "qalb"}


    def test_generate_day_practices_v2_structure(self):
        """Test that _generate_day_practices_v2 returns 5 components and used IDs."""
        practices, used_ids = self.processor._generate_day_practices_v2(
            day_num=1,
            theme="mercy",
            primary_layer_focus="qalb",
            user_profile=self.mock_user_profile,
            assessment_data=self.mock_assessment_data,
            journey_config=self.mock_journey_config,
            previously_used_ids=set()
        )
        self.assertIsInstance(practices, list)
        self.assertTrue(3 <= len(practices) <= 5, f"Expected 3-5 practices, got {len(practices)}") # Can be less if library is small
        self.assertIsInstance(used_ids, set)

        component_types = [p['type'] for p in practices]
        self.assertIn("MorningCheckIn", component_types)
        self.assertIn("PersonalReflectionJournaling", component_types)

        # Check if at least one of each selectable type is present (if available in library)
        if self.mock_practice_library["NameOfAllahSpotlight"]:
            self.assertTrue(any(p['type'] == "NameOfAllahSpotlight" for p in practices))
        if self.mock_practice_library["QuranicVerseReflection"]:
            self.assertTrue(any(p['type'] == "QuranicVerseReflection" for p in practices))
        if self.mock_practice_library["SunnahPractice"]:
            self.assertTrue(any(p['type'] == "SunnahPractice" for p in practices))


    # --- Tests for select_practice method (refactored and enhanced) ---
    def test_select_practice_basic_match(self):
        """Test select_practice for a basic match without feedback."""
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight",
            target_layer="qalb",
            current_theme="mercy",
            user_prof=self.mock_user_profile,
            assessment=self.mock_assessment_data,
            journey_conf=self.mock_journey_config,
            excluded_ids=set(),
            available_practices=self.mock_practice_library["NameOfAllahSpotlight"]
        )
        self.assertIsNotNone(selected)
        self.assertEqual(selected['id'], "noa_1")

    def test_select_practice_exclusion(self):
        """Test select_practice correctly excludes IDs."""
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight",
            target_layer="qalb",
            current_theme="mercy",
            user_prof=self.mock_user_profile,
            assessment=self.mock_assessment_data,
            journey_conf=self.mock_journey_config,
            excluded_ids={"noa_1"}, # Exclude the best match
            available_practices=self.mock_practice_library["NameOfAllahSpotlight"]
        )
        self.assertIsNotNone(selected)
        self.assertEqual(selected['id'], "noa_2") # Should pick next best

    def test_select_practice_difficulty_targeting_basic(self):
        """Test select_practice targets difficulty based on user profile."""
        intermediate_user_profile = {"awarenessLevel": "spiritual_optimizer"} # Should target intermediate
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight",
            target_layer="aql",
            current_theme="knowledge",
            user_prof=intermediate_user_profile,
            assessment=self.mock_assessment_data, # Default assessment
            journey_conf=self.mock_journey_config,
            excluded_ids=set(),
            available_practices=self.mock_practice_library["NameOfAllahSpotlight"]
        )
        self.assertIsNotNone(selected)
        self.assertEqual(selected['id'], "noa_3") # noa_3 is intermediate

    @patch('ai_service.processors.journey_generation.logger')
    def test_select_practice_with_ongoing_progress_difficulty_adjustment(self, mock_logger):
        """Test ongoing_progress_data (struggled_difficulty) adjusts target_difficulty."""
        # User profile would normally target 'intermediate'
        user_prof = {"awarenessLevel": "spiritual_optimizer", "name": "Test User"}
        # Assessment that also allows for intermediate
        assessment = {"severityScore": 4, "primaryLayer": "aql"}

        # Library has beginner 'noa_1' (qalb/mercy) and intermediate 'noa_3' (aql/knowledge)
        # Let's make 'noa_3' the only viable option by theme/layer first
        # And then see if struggle makes it pick nothing or a beginner if one existed for aql.
        # For this test, let's assume a simpler library for NameOfAllahSpotlight:
        temp_library = [
            {"id": "noa_aql_beg", "baseLayerFocus": "aql", "themeTags": ["wisdom"], "difficultyLevel": "beginner"},
            {"id": "noa_aql_int", "baseLayerFocus": "aql", "themeTags": ["wisdom"], "difficultyLevel": "intermediate"},
        ]

        ongoing_progress = {"struggled_difficulty": "intermediate"}
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight", target_layer="aql", current_theme="wisdom",
            user_prof=user_prof, assessment=assessment, journey_conf=self.mock_journey_config,
            excluded_ids=set(), available_practices=temp_library,
            ongoing_progress=ongoing_progress
        )
        self.assertIsNotNone(selected)
        self.assertEqual(selected['id'], "noa_aql_beg") # Should downgrade to beginner
        mock_logger.info.assert_any_call("Adjusting target difficulty to beginner due to struggle for NameOfAllahSpotlight.")

    def test_select_practice_with_ongoing_progress_practice_rating_boost(self):
        """Test a 'helpful' rating boosts a practice's score."""
        # noa_1 (mercy, qalb, beginner) vs noa_2 (peace, qalb, beginner)
        # Make noa_2 seem slightly better by theme initially if possible, then boost noa_1
        ongoing_progress = {"practice_ratings": {"noa_1": "helpful"}}
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight", target_layer="qalb", current_theme="mercy", # Target mercy
            user_prof=self.mock_user_profile, assessment=self.mock_assessment_data, journey_conf=self.mock_journey_config,
            excluded_ids=set(), available_practices=self.mock_practice_library["NameOfAllahSpotlight"],
            ongoing_progress=ongoing_progress
        )
        self.assertIsNotNone(selected)
        self.assertEqual(selected['id'], "noa_1") # noa_1 boosted by rating

    def test_select_practice_with_layer_feedback_prefer_layer(self):
        """Test layer_feedback_data (prefer_layer) influences layer selection."""
        layer_feedback = {"prefer_layer": "aql"} # User wants to focus on AQL
        # Original target is qalb/mercy (noa_1)
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight", target_layer="qalb", current_theme="mercy",
            user_prof=self.mock_user_profile, assessment=self.mock_assessment_data, journey_conf=self.mock_journey_config,
            excluded_ids=set(), available_practices=self.mock_practice_library["NameOfAllahSpotlight"],
            layer_feedback=layer_feedback
        )
        self.assertIsNotNone(selected)
        # noa_3 is the only AQL practice. Its theme is knowledge, difficulty intermediate.
        # User profile is beginner. This tests if layer preference overrides other factors if score is high enough.
        # Current scoring: layer match +5, theme match +3, diff match +2.
        # noa_1 (qalb/mercy/beg): target_layer=aql (no match:0), theme=mercy (no match:0), diff=beg (match: +2) -> Score 2
        # noa_3 (aql/know/int): target_layer=aql (match: +5), theme=mercy (no match:0), diff=beg (no match:0) -> Score 5
        self.assertEqual(selected['id'], "noa_3")


    def test_select_practice_with_layer_feedback_ineffective_ids(self):
        """Test ineffective_practice_ids in layer_feedback_data are excluded."""
        layer_feedback = {"ineffective_practice_ids": ["noa_1"]}
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight", target_layer="qalb", current_theme="mercy",
            user_prof=self.mock_user_profile, assessment=self.mock_assessment_data, journey_conf=self.mock_journey_config,
            excluded_ids=set(), available_practices=self.mock_practice_library["NameOfAllahSpotlight"],
            layer_feedback=layer_feedback
        )
        self.assertIsNotNone(selected)
        self.assertNotEqual(selected['id'], "noa_1")
        self.assertEqual(selected['id'], "noa_2") # noa_2 becomes the best choice for qalb/beginner

    def test_select_practice_no_candidates_after_feedback(self):
        """Test fallback when feedback makes all candidates have non-positive scores or excluded."""
        # All practices are for qalb or aql. If we prefer 'ruh' and exclude others.
        layer_feedback = {"prefer_layer": "ruh", "ineffective_practice_ids": ["noa_1", "noa_2", "noa_3"]}
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight", target_layer="qalb", current_theme="mercy",
            user_prof=self.mock_user_profile, assessment=self.mock_assessment_data, journey_conf=self.mock_journey_config,
            excluded_ids=set(), available_practices=self.mock_practice_library["NameOfAllahSpotlight"],
            layer_feedback=layer_feedback
        )
        # Since all are excluded by ineffective_practice_ids, should return None as per current select_practice.
        # If ineffective_practice_ids was empty, but prefer_layer was 'ruh', score for all would be 0, then fallback.
        self.assertIsNone(selected)

    def test_format_practice(self):
        """Test the _format_practice method."""
        template = {"id": "noa_1", "title": "TestNOA", "description": "Desc", "duration": 5,
                    "baseLayerFocus": "qalb", "difficultyLevel": "beginner",
                    "componentDetails": {"name": "Ar-Rahman", "meaning": "Merciful"}}
        formatted = self.processor._format_practice(template, "default_qalb", "NameOfAllahSpotlight")

        self.assertEqual(formatted['id'], "noa_1")
        self.assertEqual(formatted['type'], "NameOfAllahSpotlight")
        self.assertEqual(formatted['title'], "TestNOA")
        self.assertEqual(formatted['layerFocus'], "qalb")
        self.assertEqual(formatted['componentDetails']['name'], "Ar-Rahman")

    # --- Tests for Caching in _load_practice_library ---
    @patch('ai_service.processors.journey_generation.logger') # Mock logger to check log messages
    def test_load_practice_library_initial_load_and_cache(self, mock_logger):
        """Test that on first call, library is loaded (from hardcoded) and cached."""
        # Ensure cache is clear (done in setUp, but good for clarity)
        self.processor._practice_library_cache = None
        self.processor._practice_library_cache_timestamp = None

        # Action
        loaded_library = self.processor._load_practice_library()

        # Assertions
        self.assertIsNotNone(loaded_library)
        self.assertTrue(len(loaded_library.get("NameOfAllahSpotlight", [])) > 0, "Loaded library should not be empty")
        self.assertIsNotNone(self.processor._practice_library_cache, "Cache should be populated")
        self.assertIsInstance(self.processor._practice_library_cache_timestamp, datetime, "Cache timestamp should be set")
        self.assertEqual(loaded_library, self.processor._practice_library_cache, "Returned library should be the cached one")

        # Check logs (example, adjust based on actual log messages)
        mock_logger.info.assert_any_call("Practice library cache miss or expired. Attempting to fetch from backend.")
        mock_logger.info.assert_any_call("Using hardcoded practice library as fallback or initial data.")

    @patch('ai_service.processors.journey_generation.logger')
    def test_load_practice_library_cache_hit(self, mock_logger):
        """Test that a subsequent call within cache duration returns the cached version."""
        # First call to populate cache
        initial_library = self.processor._load_practice_library()
        initial_timestamp = self.processor._practice_library_cache_timestamp

        # Make a small, identifiable change to the cached object if we want to ensure it's the *exact same object*
        # For this test, just ensuring timestamp doesn't change and log indicates hit is enough.

        # Action: Second call
        cached_library = self.processor._load_practice_library()

        # Assertions
        self.assertEqual(cached_library, initial_library, "Should return the same cached library instance")
        self.assertEqual(self.processor._practice_library_cache_timestamp, initial_timestamp, "Cache timestamp should not change on hit")
        mock_logger.info.assert_called_with("Practice library cache hit. Returning cached version.")

    @patch('ai_service.processors.journey_generation.logger')
    def test_load_practice_library_cache_expired(self, mock_logger):
        """Test that after cache duration expires, the library is reloaded."""
        # First call to populate cache
        self.processor._load_practice_library()

        # Expire the cache: Access internal attribute for testing purposes
        cache_duration = self.processor.CACHE_DURATION_SECONDS if hasattr(self.processor, 'CACHE_DURATION_SECONDS') else 3600 # Fallback if not exposed
        self.processor._practice_library_cache_timestamp = datetime.now() - timedelta(seconds=cache_duration + 60)

        # Clear previous logger calls if necessary, or check for specific sequence
        mock_logger.reset_mock()

        # Action: Call again after cache "expiration"
        reloaded_library = self.processor._load_practice_library()

        # Assertions
        self.assertIsNotNone(reloaded_library)
        # Check that the timestamp was updated (i.e., it's recent)
        self.assertTrue((datetime.now() - self.processor._practice_library_cache_timestamp).total_seconds() < 60)

        mock_logger.info.assert_any_call("Practice library cache miss or expired. Attempting to fetch from backend.")
        # It will again fall back to hardcoded and re-cache it
        mock_logger.info.assert_any_call("Using hardcoded practice library as fallback or initial data.")

    # --- Test for Placeholder Logic ---
    @patch.object(JourneyGenerationProcessor, 'select_practice') # Mock the class method
    @patch('ai_service.processors.journey_generation.logger') # Mock logger
    def test_generate_day_practices_uses_placeholder_when_selection_fails(self, mock_logger, mock_select_practice):
        """Test _generate_day_practices_v2 uses placeholders if select_practice returns None."""
        mock_select_practice.return_value = None # Ensure select_practice always fails for this test

        mock_placeholder = {"id": "test_placeholder_noa", "type": "NameOfAllahSpotlight", "isPlaceholder": True, "componentDetails": {}}
        # To specifically check if _get_placeholder_practice is called with correct args,
        # we can patch it too, but for now, let's verify its effect.
        # We expect 3 calls to select_practice (for NoA, QVR, SP) and thus 3 placeholders.

        with patch.object(self.processor, '_get_placeholder_practice', return_value=mock_placeholder) as mock_get_placeholder:
            practices, used_ids = self.processor._generate_day_practices_v2(
                day_num=1, theme="any_theme", primary_layer_focus="qalb",
                user_profile=self.mock_user_profile, assessment_data=self.mock_assessment_data,
                journey_config=self.mock_journey_config, previously_used_ids=set(),
                ongoing_progress_data=None, layer_feedback_data=None
            )

        self.assertEqual(mock_select_practice.call_count, 3) # For NameOfAllah, QuranicVerse, SunnahPractice
        self.assertEqual(mock_get_placeholder.call_count, 3)

        # Check that practices list contains placeholders (plus MorningCheckIn and Journaling)
        placeholder_count = sum(1 for p in practices if p.get("isPlaceholder"))
        self.assertEqual(placeholder_count, 3)
        self.assertTrue(all(p.get("isPlaceholder") for p in practices if p["type"] in ["NameOfAllahSpotlight", "QuranicVerseReflection", "SunnahPractice"]))

        # Verify logger warning for each failed selection that leads to a placeholder
        self.assertEqual(mock_logger.warning.call_count, 3)
        mock_logger.warning.assert_any_call("No suitable NameOfAllahSpotlight practice found for day 1, theme any_theme. Using placeholder.")

    def test_select_practice_empty_available_practices(self):
        """Test select_practice returns None if available_practices is empty."""
        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight",
            target_layer="qalb",
            current_theme="mercy",
            user_prof=self.mock_user_profile,
            assessment=self.mock_assessment_data,
            journey_conf=self.mock_journey_config,
            excluded_ids=set(),
            available_practices=[] # Empty list
        )
        self.assertIsNone(selected, "Should return None when available_practices is empty.")

    def test_select_practice_all_practices_excluded_no_fallback_possible(self):
        """Test select_practice returns None if all practices are in excluded_ids and no other logic picks them."""
        # Get all IDs from a portion of the mock library to ensure they are all excluded
        practice_list = self.mock_practice_library["NameOfAllahSpotlight"]
        all_ids_to_exclude = {p['id'] for p in practice_list}

        selected = self.processor.select_practice(
            practice_type_key="NameOfAllahSpotlight",
            target_layer="qalb",
            current_theme="any_theme",
            user_prof=self.mock_user_profile,
            assessment=self.mock_assessment_data,
            journey_conf=self.mock_journey_config,
            excluded_ids=all_ids_to_exclude, # Exclude all available practices
            available_practices=practice_list, # Pass the list of practices to be excluded
            ongoing_progress=None,
            layer_feedback=None
        )
        self.assertIsNone(selected, "Should return None if all available practices are excluded and no other logic selects one.")

    # --- Tests for Data Transformation Methods ---
    def test_transform_name_of_allah_data(self):
        """Test the _transform_name_of_allah_data method."""
        backend_data = [
            {"id": "noa_bk_001", "name": "Ar-Rahman", "arabicScript": "الرحمن", "meaning": "The Merciful",
             "significance": "Very merciful", "reflectionPrompt": "Reflect mercy",
             "layerFocus": ["qalb", "ruh"], "benefits": ["hope"], "isActive": True, "dhikrCount": 33},
            {"id": "noa_bk_002", "name": "Al-Ghaffar", "meaning": "The Forgiver", "significance": "Very forgiving",
             "layerFocus": ["nafs"], "benefits": ["repentance"], "isActive": True, "dhikrCount": 100, "audioUrl": "path/audio.mp3"}, # Missing arabicScript, reflectionPrompt
            {"id": "noa_bk_003", "name": "Inactive Name", "meaning":"...", "significance":"...", "layerFocus":["qalb"], "benefits":[], "isActive": False},
            {"id": "noa_bk_004", "name": "Al-Quddus", "meaning": "The Holy", "significance": "Very holy",
             "layerFocus": [], "benefits": ["purity"], "isActive": True} # Missing dhikrCount, layerFocus empty
        ]

        transformed_data = self.processor._transform_name_of_allah_data(backend_data)
        self.assertEqual(len(transformed_data), 3) # Expect 3 active items

        # Check item 1 (Ar-Rahman)
        item1 = next(item for item in transformed_data if item["id"] == "noa_bk_001")
        self.assertEqual(item1["title"], "Ar-Rahman")
        self.assertEqual(item1["description"], "Very merciful")
        self.assertEqual(item1["duration"], 33 // 10 + 5) # 3 + 5 = 8
        self.assertEqual(item1["baseLayerFocus"], "qalb")
        self.assertIn("hope", item1["themeTags"])
        self.assertIn("qalb", item1["themeTags"])
        self.assertEqual(item1["difficultyLevel"], "beginner")
        self.assertEqual(item1["componentDetails"]["originalContentId"], "noa_bk_001")
        self.assertEqual(item1["componentDetails"]["name"], "Ar-Rahman")
        self.assertEqual(item1["componentDetails"]["arabicScript"], "الرحمن")
        self.assertIsNone(item1["componentDetails"].get("audioUrl")) # Was not in input

        # Check item 2 (Al-Ghaffar) - testing optional fields
        item2 = next(item for item in transformed_data if item["id"] == "noa_bk_002")
        self.assertEqual(item2["title"], "Al-Ghaffar")
        self.assertEqual(item2["duration"], 100 // 10 + 5) # 10 + 5 = 15
        self.assertEqual(item2["baseLayerFocus"], "nafs")
        self.assertIsNone(item2["componentDetails"].get("arabicScript"))
        self.assertIsNone(item2["componentDetails"].get("reflectionPrompt"))
        self.assertEqual(item2["componentDetails"]["audioUrl"], "path/audio.mp3")

        # Check item 4 (Al-Quddus) - testing missing dhikrCount and empty layerFocus
        item4 = next(item for item in transformed_data if item["id"] == "noa_bk_004")
        self.assertEqual(item4["title"], "Al-Quddus")
        self.assertEqual(item4["duration"], 5) # Default duration if dhikrCount missing (0 // 10 + 5)
        self.assertEqual(item4["baseLayerFocus"], "qalb") # Default if layerFocus is empty
        self.assertIn("purity", item4["themeTags"])
        self.assertIn("qalb", item4["themeTags"]) # Default layerFocus added to themeTags

    def test_transform_quranic_verse_data(self):
        """Test the _transform_quranic_verse_data method."""
        backend_data = [
            {"id": "qvr_bk_001", "surahNumber": 1, "ayahNumber": 1, "arabicText": "بِسْمِ اللَّهِ",
             "translationEn": "In the name of Allah", "contextualExplanation": "Basmalah",
             "reflectionPrompts": ["Reflect"], "layerFocus": ["ruh"], "themes": ["beginning"], "isActive": True},
            {"id": "qvr_bk_002", "surahNumber": 2, "ayahNumber": 255, "arabicText": "اللَّهُ لَا إِلَٰهَ",
             "isActive": False}, # Inactive
            {"id": "qvr_bk_003", "surahNumber": 114, "ayahNumber": 1, "arabicText": "قُلْ أَعُوذُ",
             "layerFocus": [], "themes": ["seeking_refuge"], "isActive": True, "audioUrl": "path/audio.mp3"} # Optional fields missing
        ]
        transformed_data = self.processor._transform_quranic_verse_data(backend_data)
        self.assertEqual(len(transformed_data), 2)

        item1 = next(item for item in transformed_data if item["id"] == "qvr_bk_001")
        self.assertEqual(item1["title"], "Surah 1:1")
        self.assertEqual(item1["description"], "Basmalah")
        self.assertEqual(item1["duration"], 10) # Default
        self.assertEqual(item1["baseLayerFocus"], "ruh")
        self.assertIn("beginning", item1["themeTags"])
        self.assertEqual(item1["difficultyLevel"], "beginner")
        self.assertEqual(item1["componentDetails"]["originalContentId"], "qvr_bk_001")
        self.assertEqual(item1["componentDetails"]["surahNumber"], 1)
        self.assertIsNone(item1["componentDetails"].get("audioUrl"))

        item3 = next(item for item in transformed_data if item["id"] == "qvr_bk_003")
        self.assertEqual(item3["title"], "Surah 114:1")
        self.assertEqual(item3["baseLayerFocus"], "ruh") # Default if layerFocus empty
        self.assertEqual(item3["componentDetails"]["audioUrl"], "path/audio.mp3")
        self.assertEqual(item3["componentDetails"].get("translationEn"), None) # Optional, not provided

    def test_transform_sunnah_practice_data(self):
        """Test the _transform_sunnah_practice_data method."""
        backend_data = [
            {"id": "sp_bk_001", "title": "Mindful Wudu", "category": "Physical", "description": "Perform Wudu",
             "benefits": ["purification"], "estimatedDuration": 7, "difficultyLevel": "Beginner",
             "layerFocus": ["jism", "qalb"], "isActive": True},
            {"id": "sp_bk_002", "title": "Inactive Practice", "isActive": False},
            {"id": "sp_bk_003", "title": "Miswak", "category": "Physical",
             "layerFocus": [], "benefits": ["oral_hygiene"], "isActive": True} # Missing duration, difficulty, desc
        ]
        transformed_data = self.processor._transform_sunnah_practice_data(backend_data)
        self.assertEqual(len(transformed_data), 2)

        item1 = next(item for item in transformed_data if item["id"] == "sp_bk_001")
        self.assertEqual(item1["title"], "Mindful Wudu")
        self.assertEqual(item1["description"], "Perform Wudu")
        self.assertEqual(item1["duration"], 7)
        self.assertEqual(item1["baseLayerFocus"], "jism")
        self.assertIn("purification", item1["themeTags"])
        self.assertIn("physical", item1["themeTags"]) # Category added to themeTags
        self.assertEqual(item1["difficultyLevel"], "beginner") # Lowercased
        self.assertEqual(item1["componentDetails"]["originalContentId"], "sp_bk_001")

        item3 = next(item for item in transformed_data if item["id"] == "sp_bk_003")
        self.assertEqual(item3["title"], "Miswak")
        self.assertEqual(item3["description"], "") # Default if missing
        self.assertEqual(item3["duration"], 5) # Default if missing
        self.assertEqual(item3["baseLayerFocus"], "jism") # Default if layerFocus empty
        self.assertEqual(item3["difficultyLevel"], "beginner") # Default if missing

if __name__ == '__main__':
    unittest.main()
