from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, Any
import logging
from datetime import datetime
from .content_templates.professions import PROFESSION_SPECIFIC_CONTENT

logger = logging.getLogger(__name__)

# Pydantic Models for Onboarding Profile Generation
class OnboardingProfileRequest(BaseModel):
    """Request model for onboarding profile generation"""
    userId: str = Field(..., description="User ID")
    responses: Dict[str, Any] = Field(..., description="All onboarding responses")
    sessionId: str = Field(..., description="Onboarding session ID")

class OnboardingProfileResponse(BaseModel):
    """Response model for onboarding profile generation"""
    profileData: Dict[str, Any] = Field(..., description="Generated profile data")
    recommendedPathway: str = Field(..., description="Recommended user pathway")
    personalizationSettings: Dict[str, Any] = Field(..., description="Personalization settings")
    confidence: float = Field(..., description="Profile generation confidence")

profile_router = APIRouter(
    prefix="/profiles",
    tags=["User Profiling"]
)

# Placeholder crisis analysis functions if not imported correctly from elsewhere
def analyze_crisis_keywords(text: str) -> dict:
    logger.debug(f"Placeholder: Analyzing crisis keywords for text: {text[:100]}...")
    keywords_found = {}
    if "crisis" in text.lower(): keywords_found["crisis"] = 1
    if "help me" in text.lower(): keywords_found["help_me"] = 1
    # This is a simplified placeholder
    return {"keywords_found": keywords_found, "overall_sentiment": "neutral"}

def calculate_crisis_level(keyword_analysis: dict, responses: dict) -> tuple[str, float, list]:
    logger.debug(f"Placeholder: Calculating crisis level. Keywords: {keyword_analysis.get('keywords_found')}. MHA Response: {responses.get('mental_health_awareness')}")
    level = "none"
    confidence = 0.9
    indicators = []
    if keyword_analysis.get("keywords_found", {}).get("crisis"):
        level = "moderate"
        indicators.append("crisis_keyword_detected_in_text")

    mha_response = responses.get('mental_health_awareness')
    if isinstance(mha_response, str) and mha_response == 'crisis':
        level = "high" # Prioritize direct crisis selection
        indicators.append("mha_direct_crisis_selection")

    welcome_response = responses.get('welcome')
    if isinstance(welcome_response, str) and welcome_response == 'emergency':
        level = "critical" # Prioritize direct emergency selection
        indicators.append("welcome_direct_emergency_selection")

    return level, confidence, list(set(indicators)) # Ensure unique indicators


@profile_router.post("/generate", response_model=OnboardingProfileResponse)
async def generate_user_profile_endpoint(request: OnboardingProfileRequest):
    """
    Generate comprehensive user profile from onboarding responses
    """
    try:
        logger.info(f"Generating profile for user: {request.userId}, session: {request.sessionId}")
        responses = request.responses
        logger.debug(f"Received responses for user {request.userId}: {responses}")

        # --- 1. Comprehensive Crisis Assessment ---
        response_texts = []
        for key, value in responses.items():
            if isinstance(value, str):
                response_texts.append(value)
            elif isinstance(value, list) and all(isinstance(item, str) for item in value):
                response_texts.extend(value)
            elif isinstance(value, dict):
                for sub_value in value.values():
                    if isinstance(sub_value, str):
                        response_texts.append(sub_value)
        full_response_text = " ".join(response_texts)

        keyword_analysis_result = analyze_crisis_keywords(full_response_text)
        crisis_level, crisis_confidence, crisis_indicators_list = calculate_crisis_level(keyword_analysis_result, responses)

        mha_direct_response_for_crisis = responses.get('mental_health_awareness')
        welcome_direct_response_for_crisis = responses.get('welcome')
        immediate_help_mha = isinstance(mha_direct_response_for_crisis, str) and mha_direct_response_for_crisis == 'crisis'
        immediate_help_welcome = isinstance(welcome_direct_response_for_crisis, str) and welcome_direct_response_for_crisis == 'emergency'

        profile_crisis_indicators = {
            "level": crisis_level,
            "indicators": crisis_indicators_list,
            "immediateHelpRequested": immediate_help_mha or immediate_help_welcome,
            "confidence": crisis_confidence
        }

        # --- 2. Extract Detailed Profile Data ---
        mha_primary_response = responses.get('mental_health_awareness', 'symptom_aware')
        mha_conditions_response = responses.get('mha_conditions', [])
        mha_therapy_response = responses.get('mha_therapy')
        mha_experiences_response = responses.get('mha_experiences', [])
        mha_concepts_familiarity_response = responses.get('mha_concepts_familiarity')

        rk_primary_response = responses.get('ruqya_knowledge', 'unaware')
        rk_expert_aspects_response = responses.get('rk_expert_aspects', [])
        rk_expert_tools_response = responses.get('rk_expert_tools', [])
        rk_practitioner_duration_response = responses.get('rk_practitioner_duration')
        rk_unaware_openness_response = responses.get('rk_unaware_openness')
        rk_unaware_comfort_response = responses.get('rk_unaware_comfort')

        pc_primary_response = responses.get('professional_context', 'other_professional_pc')
        if isinstance(pc_primary_response, dict):
            pc_primary_response = pc_primary_response.get('professional_context', 'other_professional_pc')
        pc_work_challenges_response = responses.get('pc_work_challenges', [])
        pc_healthcare_details_response = responses.get('pc_healthcare_details')
        pc_education_details_response = responses.get('pc_education_details')
        pc_technology_details_response = responses.get('pc_technology_details')
        pc_business_details_response = responses.get('pc_business_details')
        pc_creative_details_response = responses.get('pc_creative_details')
        pc_service_details_response = responses.get('pc_service_details')
        pc_science_details_response = responses.get('pc_science_details')

        soc_response = responses.get('spiritual_optimizer_clinical', {})
        sot_response = responses.get('spiritual_optimizer_traditional', {})

        demographics_response = responses.get('demographics', {})
        life_circumstances_selected = responses.get('life_circumstances', [])

        professional_context = {
            "field": pc_primary_response,
            "workStressors": pc_work_challenges_response,
        }
        if pc_primary_response == 'healthcare_pc' and pc_healthcare_details_response:
            professional_context['healthcare'] = {
                "role": pc_healthcare_details_response.get('role', 'other'),
                "isStudent": pc_healthcare_details_response.get('role') == 'student_resident'
            }
        if pc_primary_response == 'education_pc' and pc_education_details_response:
            professional_context['education'] = {
                "role": pc_education_details_response.get('role', 'other'),
                "level": pc_education_details_response.get('level', 'other')
            }
        if pc_primary_response == 'technology_pc' and pc_technology_details_response:
            professional_context['technology'] = {
                "role": pc_technology_details_response.get('role', 'other'),
                "isFounder": pc_technology_details_response.get('role') == 'entrepreneurship'
            }
        if pc_primary_response == 'business_finance_pc' and pc_business_details_response:
            professional_context['business'] = {
                "role": pc_business_details_response.get('role', 'other')
            }
        if pc_primary_response == 'creative_arts_pc' and pc_creative_details_response:
            professional_context['creative'] = {
                "role": pc_creative_details_response.get('role', 'other')
            }
        if pc_primary_response == 'service_care_pc' and pc_service_details_response:
            professional_context['service'] = {
                "role": pc_service_details_response.get('role', 'other')
            }
        if pc_primary_response == 'science_research_pc' and pc_science_details_response:
            professional_context['science'] = {
                "role": pc_science_details_response.get('role', 'other')
            }

        profile_data = {
            "userId": request.userId,
            "mentalHealthAwareness": {
                "level": mha_primary_response,
                "conditions": mha_conditions_response if mha_primary_response == 'clinical_aware' else [],
                "previousTherapy": mha_therapy_response if mha_primary_response == 'clinical_aware' else None,
                "experiences": mha_experiences_response if mha_primary_response == 'symptom_aware' else [],
                "conceptsFamiliarity": mha_concepts_familiarity_response if mha_primary_response == 'symptom_aware' else None,
            },
            "ruqyaKnowledge": {
                "level": rk_primary_response,
                "expertAspects": rk_expert_aspects_response if rk_primary_response == 'expert' else [],
                "expertTools": rk_expert_tools_response if rk_primary_response == 'expert' else [],
                "practitionerDuration": rk_practitioner_duration_response if rk_primary_response == 'practitioner' else None,
                "unawareOpenness": rk_unaware_openness_response if rk_primary_response == 'unaware' else None,
                "unawareComfort": rk_unaware_comfort_response if rk_primary_response == 'unaware' else None,
            },
            "spiritualOptimizer": None,
            "professionalContext": professional_context,
            "demographics": {
                "ageRange": demographics_response.get('age_range_section', 'prefer_not_to_say_age'),
                "gender": demographics_response.get('gender_section', 'prefer_not_to_say_gender'),
                "familyStatus": demographics_response.get('family_status_section', 'prefer_not_to_say_fs'),
            },
            "lifeCircumstances": {
                "situations": life_circumstances_selected,
            },
            "crisisIndicators": profile_crisis_indicators,
            "preferences": {
                "contentStyle": "moderate", "islamicTerminology": "basic", "learningPace": "moderate",
                "communityEngagement": "low", "timeAvailability": "10-20"
            },
            "featureAccessibility": {},
            "profileVersion": "1.0.5",
            "createdAt": responses.get("session_start_time", datetime.utcnow().isoformat()),
            "updatedAt": datetime.utcnow().isoformat(),
            "completionStatus": "complete",
            "learningHistory": {"completedAssessments": [], "behaviorPatterns": {}, "progressMetrics": {}, "lastUpdated": datetime.utcnow().isoformat()},
            "privacySettings": {"dataSharing": False, "communityVisibility": "private", "analyticsConsent": False, "crisisInterventionConsent": True}
        }

        if mha_primary_response == "clinical_integration" and isinstance(soc_response, dict):
            profile_data["spiritualOptimizer"] = {
                "type": "clinical_integration",
                "goals": soc_response.get("goals", []),
                "islamicPsychologyLevel": soc_response.get("islamicPsychologyLevel", "basic")
            }
        elif mha_primary_response == "traditional_bridge" and isinstance(sot_response, dict):
            profile_data["spiritualOptimizer"] = {
                "type": "traditional_bridge",
                "goals": sot_response.get("goals", []),
                "clinicalComfort": sot_response.get("clinicalComfort", "limited")
            }

        # --- 3. Determine Recommended Pathway ---
        recommended_pathway = "standard_healing_journey"
        if profile_crisis_indicators["level"] in ["high", "critical"]:
            recommended_pathway = "crisis_support"
        elif profile_data["spiritualOptimizer"]:
            if profile_data["spiritualOptimizer"]["type"] == "clinical_integration":
                recommended_pathway = "clinical_islamic_integration"
            elif profile_data["spiritualOptimizer"]["type"] == "traditional_bridge":
                recommended_pathway = "traditional_modern_bridge"
        elif mha_primary_response == "new_muslim" or 'new_muslim_lc' in life_circumstances_selected:
            recommended_pathway = "gentle_introduction"
        elif profile_data["mentalHealthAwareness"]["level"] == "clinical_aware" and \
             profile_data["ruqyaKnowledge"]["level"] == "expert":
            recommended_pathway = "advanced_development"
        elif 'ptsd' in profile_data["mentalHealthAwareness"].get("conditions", []):
             recommended_pathway = "trauma_informed_healing"
        elif profile_data["mentalHealthAwareness"]["level"] == "symptom_aware" and \
             profile_data["ruqyaKnowledge"]["level"] == "unaware":
            recommended_pathway = "gentle_introduction"

        # --- 4. Generate Personalization Settings ---
        personalization_settings = profile_data["preferences"].copy()
        if profile_data["mentalHealthAwareness"]["level"] == "clinical_aware" or \
           profile_data["ruqyaKnowledge"]["level"] == "expert" or \
           professional_context.get('healthcare', {}).get('role') == 'physician':
            personalization_settings["contentStyle"] = "detailed"
            personalization_settings["islamicTerminology"] = "extensive"
        elif mha_primary_response == "new_muslim" or \
             profile_data["ruqyaKnowledge"]["level"] == "unaware" or \
             professional_context.get('healthcare', {}).get('role') == 'student_resident':
            personalization_settings["contentStyle"] = "simple"

        mha_familiarity = profile_data["mentalHealthAwareness"].get("conceptsFamiliarity")
        if mha_familiarity == 'very_familiar_concepts':
            if personalization_settings["contentStyle"] != "detailed":
                 personalization_settings["contentStyle"] = "moderate"
            if personalization_settings["islamicTerminology"] not in ["extensive", "moderate"]:
                 personalization_settings["islamicTerminology"] = "moderate"
        elif mha_familiarity == 'prefer_not_to_use_terms':
            personalization_settings["contentStyle"] = "simple"
            personalization_settings["islamicTerminology"] = "minimal"

        if profile_data["ruqyaKnowledge"]["level"] == "unaware":
            rk_unaware_comfort = profile_data["ruqyaKnowledge"].get("unawareComfort")
            if rk_unaware_comfort == 'rk_comfort_simple':
                personalization_settings["islamicTerminology"] = "basic"
            elif rk_unaware_comfort == 'rk_comfort_minimal':
                personalization_settings["islamicTerminology"] = "minimal"

        if 'new_muslim_lc' in life_circumstances_selected:
            personalization_settings["contentStyle"] = "simple"
            personalization_settings["islamicTerminology"] = "minimal"
        if 'academic_work_pressure_lc' in life_circumstances_selected or \
           'heavy_workload_pc' in profile_data["professionalContext"].get("workStressors", []):
            personalization_settings["timeAvailability"] = "short_bursts"
        if 'social_isolation_lc' in life_circumstances_selected:
            personalization_settings["communityEngagement"] = "moderate"
        if 'seeking_islamic_knowledge_lc' in life_circumstances_selected:
            if personalization_settings["islamicTerminology"] == "minimal":
                personalization_settings["islamicTerminology"] = "basic"
            if personalization_settings["contentStyle"] == "simple":
                personalization_settings["contentStyle"] = "moderate"
        if 'pregnancy_new_parent_lc' in life_circumstances_selected:
             personalization_settings["learningPace"] = "flexible_self_paced"

        age_range = profile_data["demographics"].get("ageRange")
        if age_range in ['under_18_age', '18_25_age']:
            if personalization_settings["learningPace"] == "moderate":
                personalization_settings["learningPace"] = "fast"
        elif age_range in ['over_65_age']:
             if personalization_settings["learningPace"] == "moderate":
                personalization_settings["learningPace"] = "gentle"

        # Add profession-specific content
        field = professional_context.get("field", "").replace("_pc", "")
        role = ""
        if field in professional_context and isinstance(professional_context[field], dict):
            role = professional_context[field].get("role", "")

        if field in PROFESSION_SPECIFIC_CONTENT and role in PROFESSION_SPECIFIC_CONTENT[field]:
            profile_data["professionalContext"]["content"] = PROFESSION_SPECIFIC_CONTENT[field][role]

        profile_data["preferences"] = personalization_settings
        profile_confidence = 0.80
        if not responses or len(responses) < 3:
            profile_confidence = 0.60

        logger.info(f"Successfully generated profile for {request.userId} with pathway {recommended_pathway}")

        return OnboardingProfileResponse(
            profileData=profile_data,
            recommendedPathway=recommended_pathway,
            personalizationSettings=personalization_settings,
            confidence=profile_confidence
        )

    except Exception as e:
        logger.error(f"Error generating onboarding profile for user {request.userId}, session {request.sessionId}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Profile generation failed: {str(e)}")