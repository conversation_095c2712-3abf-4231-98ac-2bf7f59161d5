"""
Crisis Analysis Endpoint for Feature 0: Adaptive Onboarding
Crisis detection and intervention system
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import re
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/crisis-analysis", tags=["Crisis Analysis"])

# Pydantic Models for Crisis Analysis
class CrisisAnalysisRequest(BaseModel):
    """Request model for crisis analysis"""
    response: Dict[str, Any] = Field(..., description="User response data")
    stepId: str = Field(..., description="Current onboarding step")
    context: str = Field(default="onboarding", description="Analysis context")
    userId: Optional[str] = Field(None, description="User ID for logging")

class CrisisAnalysisResponse(BaseModel):
    """Response model for crisis analysis"""
    level: str = Field(..., description="Crisis level: none, low, moderate, high, critical")
    confidence: float = Field(..., description="Confidence score 0-1")
    indicators: List[str] = Field(..., description="List of detected crisis indicators")
    urgency: str = Field(..., description="Urgency level: low, moderate, urgent, immediate")
    recommended_actions: List[str] = Field(..., description="Recommended intervention actions")
    reasoning: str = Field(..., description="AI reasoning for the assessment")

# OnboardingProfileRequest and OnboardingProfileResponse moved to profile_generation.py

# Crisis Keywords Configuration
CRISIS_KEYWORDS = {
    'danger': [
        'suicide', 'kill myself', 'end my life', 'want to die', 'better off dead',
        'harm myself', 'hurt myself', 'self harm', 'cutting', 'cut myself',
        'overdose', 'pills', 'jump', 'hanging', 'gun', 'knife',
        'razor', 'blade', 'poison', 'bridge', 'roof', 'take my life',
        'end it all', 'not worth living', 'world without me', 'final solution',
        'permanent solution', 'escape this pain', 'stop existing'
    ],
    'severe_distress': [
        'can\'t go on', 'no point', 'hopeless', 'worthless', 'useless',
        'everyone would be better', 'burden', 'can\'t take it', 'give up',
        'nothing matters', 'end the pain', 'no way out', 'trapped',
        'unbearable', 'can\'t cope', 'breaking point', 'overwhelmed',
        'drowning', 'suffocating', 'crushing weight', 'too much pain',
        'can\'t breathe', 'falling apart', 'completely broken',
        'lost all hope', 'nothing left', 'empty inside', 'numb',
        'struggling with identity', 'torn between worlds', 'cultural conflict',
        'family shame', 'community rejection', 'bringing shame',
        'family will disown me', 'disown me', 'identity as a muslim',
        'struggling with my identity', 'muslim in the west',
        'torn between my family', 'family expectations', 'community pressure',
        'shame to my family', 'bringing shame to islam', 'face the community'
    ],
    'crisis_situation': [
        'emergency', 'crisis', 'immediate help', 'right now', 'urgent',
        'can\'t wait', 'desperate', 'breaking down', 'falling apart',
        'losing control', 'scared', 'terrified', 'help me',
        'need help now', 'someone help', 'please help', 'crying for help',
        'reaching out', 'last resort', 'nowhere to turn', 'at my limit'
    ],
    'mental_health_crisis': [
        # Panic attacks and anxiety crises
        'panic attack', 'panic attacks', 'having a panic attack', 'panic episode',
        'can\'t breathe', 'hyperventilating', 'heart racing', 'chest tight',
        'feeling like dying', 'going crazy', 'losing my mind', 'out of control',
        'dizzy', 'nauseous', 'shaking uncontrollably', 'sweating profusely',
        'choking sensation', 'tunnel vision', 'derealization', 'depersonalization',

        # Severe anxiety and phobias
        'severe anxiety', 'crippling anxiety', 'anxiety attack', 'social anxiety',
        'agoraphobia', 'claustrophobia', 'afraid to leave house', 'fear of crowds',
        'fear of judgment', 'performance anxiety', 'test anxiety', 'anxious',
        'feeling anxious', 'anxiety issues', 'worry constantly', 'nervous',

        # Depression episodes
        'major depression', 'severe depression', 'depressive episode',
        'clinical depression', 'can\'t get out of bed', 'sleeping all day',
        'no energy', 'no motivation', 'everything is dark', 'deep sadness',
        'crying spells', 'emotional numbness', 'anhedonia',

        # Bipolar and mood disorders
        'manic episode', 'hypomanic', 'mood swings', 'bipolar', 'rapid cycling',
        'elevated mood', 'grandiose thoughts', 'racing thoughts', 'pressured speech',
        'decreased need for sleep', 'risky behavior', 'spending spree',

        # PTSD and trauma responses
        'flashbacks', 'nightmares', 'trauma response', 'triggered',
        'dissociation', 'hypervigilance', 'startle response', 'avoidance',
        'intrusive thoughts', 're-experiencing', 'emotional numbing',

        # Eating disorders
        'eating disorder', 'anorexia', 'bulimia', 'binge eating', 'purging',
        'body dysmorphia', 'fear of food', 'obsessed with weight', 'restricting',

        # OCD and compulsions
        'obsessive thoughts', 'compulsions', 'repetitive behaviors', 'checking',
        'washing hands', 'counting', 'arranging', 'intrusive images',

        # Psychosis and severe mental health
        'hearing voices', 'seeing things', 'hallucinations', 'delusions',
        'paranoid thoughts', 'thought insertion', 'reality distortion',

        # Islamic mental health context
        'waswas', 'obsessive doubts about wudu', 'doubts about prayer',
        'scrupulosity', 'religious ocd', 'excessive guilt about sins',
        'fear of shirk', 'doubts about faith', 'spiritual anxiety'
    ],
    'spiritual_crisis': [
        'allah hates me', 'god abandoned me', 'lost faith', 'cursed',
        'unforgivable', 'hell bound', 'no mercy', 'spiritual darkness',
        'evil inside', 'possessed', 'jinns attacking', 'black magic',
        'allah abandoned me', 'allah has abandoned me', 'prayers meaningless',
        'prayers are meaningless', 'questioning purpose', 'lost my faith',
        'duas not answered', 'allah doesn\'t hear me', 'spiritual emptiness',
        'feeling unworthy', 'allah will never forgive', 'going to hell',
        'faith crisis', 'questioning islam', 'doubting allah',
        'prayers feel empty', 'disconnected from allah', 'spiritual attack',
        'evil eye', 'sihr', 'hasad', 'jinn possession',
        'allah doesn\'t love me', 'rejected by allah', 'spiritual despair',
        'lost connection', 'faith shattered', 'religious doubt',
        'questioning my faith', 'struggling with faith', 'faith issues',
        'lost faith completely', 'faith completely', 'abandoned by allah',
        'سبحانه وتعالى has abandoned', 'allah سبحانه وتعالى has abandoned',
        'jinn are attacking', 'jinns are attacking', 'duas not answered',
        'duas are not answered', 'can\'t find peace in salah', 'salah anymore',
        'peace in salah', 'duas not being answered'
    ]
}

# Crisis Analysis Functions
def analyze_crisis_keywords(text: str) -> Dict[str, List[str]]:
    """Analyze text for crisis-related keywords"""
    text_lower = text.lower()

    detected = {
        'danger': [],
        'severe_distress': [],
        'crisis_situation': [],
        'spiritual_crisis': [],
        'mental_health_crisis': []
    }

    # Check for keywords in each category
    for category, keywords in CRISIS_KEYWORDS.items():
        for keyword in keywords:
            if keyword in text_lower:
                detected[category].append(keyword)

    return detected

def calculate_crisis_level(keyword_analysis: Dict[str, List[str]], response_data: Dict[str, Any]) -> tuple:
    """Calculate crisis level based on keyword analysis and response data"""

    # Check for explicit crisis indicators
    if response_data.get('mental_health_primary') == 'crisis':
        return 'critical', 0.95, ['explicit_crisis_selection']

    if response_data.get('immediate_help') is True:
        return 'high', 0.90, ['immediate_help_requested']

    # Analyze keyword patterns
    danger_count = len(keyword_analysis['danger'])
    distress_count = len(keyword_analysis['severe_distress'])
    crisis_count = len(keyword_analysis['crisis_situation'])
    spiritual_count = len(keyword_analysis['spiritual_crisis'])
    mental_health_count = len(keyword_analysis['mental_health_crisis'])

    indicators = []

    # Critical level - immediate danger
    if danger_count > 0:
        indicators.extend([f"danger_keyword: {kw}" for kw in keyword_analysis['danger']])
        return 'critical', 0.95, indicators

    # High level - severe mental health crisis (panic attacks, psychosis, etc.)
    severe_mental_health_keywords = [
        'panic attack', 'having a panic attack', 'hearing voices', 'seeing things',
        'hallucinations', 'delusions', 'psychotic episode', 'manic episode',
        'severe depression', 'major depression', 'can\'t get out of bed'
    ]
    has_severe_mental_health = any(kw in severe_mental_health_keywords
                                  for kw in keyword_analysis['mental_health_crisis'])

    if has_severe_mental_health or mental_health_count >= 3:
        indicators.extend([f"mental_health_crisis: {kw}" for kw in keyword_analysis['mental_health_crisis']])
        return 'high', 0.90, indicators

    # High level - severe distress + crisis situation
    if distress_count >= 2 and crisis_count > 0:
        indicators.extend([f"severe_distress: {kw}" for kw in keyword_analysis['severe_distress']])
        indicators.extend([f"crisis_situation: {kw}" for kw in keyword_analysis['crisis_situation']])
        return 'high', 0.85, indicators

    # High level - spiritual crisis + distress
    if spiritual_count >= 2 and distress_count > 0:
        indicators.extend([f"spiritual_crisis: {kw}" for kw in keyword_analysis['spiritual_crisis']])
        indicators.extend([f"severe_distress: {kw}" for kw in keyword_analysis['severe_distress']])
        return 'high', 0.80, indicators

    # Check for serious spiritual crisis keywords that should trigger moderate
    serious_spiritual_keywords = ['allah has abandoned me', 'allah abandoned me', 'allah hates me']
    has_serious_spiritual = any(kw in serious_spiritual_keywords for kw in keyword_analysis['spiritual_crisis'])

    # Moderate level - multiple distress indicators, spiritual crisis, or mental health issues
    if (distress_count >= 2 or (distress_count > 0 and crisis_count > 0) or
        spiritual_count >= 2 or (spiritual_count >= 1 and distress_count >= 1) or
        has_serious_spiritual or mental_health_count >= 1):
        indicators.extend([f"distress: {kw}" for kw in keyword_analysis['severe_distress']])
        if crisis_count > 0:
            indicators.extend([f"crisis_situation: {kw}" for kw in keyword_analysis['crisis_situation']])
        if spiritual_count >= 1:
            indicators.extend([f"spiritual_crisis: {kw}" for kw in keyword_analysis['spiritual_crisis']])
        if mental_health_count >= 1:
            indicators.extend([f"mental_health_crisis: {kw}" for kw in keyword_analysis['mental_health_crisis']])
        return 'moderate', 0.70, indicators

    # Low level - single indicators
    if distress_count > 0 or crisis_count > 0 or spiritual_count > 0 or mental_health_count > 0:
        if distress_count > 0:
            indicators.extend([f"mild_distress: {kw}" for kw in keyword_analysis['severe_distress'][:1]])
        if crisis_count > 0:
            indicators.extend([f"mild_crisis: {kw}" for kw in keyword_analysis['crisis_situation'][:1]])
        if spiritual_count > 0:
            indicators.extend([f"spiritual_concern: {kw}" for kw in keyword_analysis['spiritual_crisis'][:1]])
        if mental_health_count > 0:
            indicators.extend([f"mental_health_concern: {kw}" for kw in keyword_analysis['mental_health_crisis'][:1]])
        return 'low', 0.50, indicators

    return 'none', 0.10, []

def determine_urgency_and_actions(level: str, indicators: List[str]) -> tuple:
    """Determine urgency level and recommended actions"""

    if level == 'critical':
        urgency = 'immediate'
        actions = [
            'emergency_services',
            'crisis_hotline',
            'immediate_safety_plan',
            'family_notification',
            'continuous_monitoring',
            'islamic_crisis_counselor'
        ]
    elif level == 'high':
        urgency = 'urgent'
        actions = [
            'crisis_counselor',
            'mental_health_professional',
            'safety_planning',
            'enhanced_monitoring',
            'islamic_counselor',
            'family_support',
            'community_support',
            'panic_attack_techniques',
            'grounding_exercises'
        ]
    elif level == 'moderate':
        urgency = 'elevated'
        actions = [
            'professional_support',
            'mental_health_counselor',
            'islamic_counselor',
            'community_support',
            'regular_check_ins',
            'spiritual_guidance',
            'enhanced_monitoring',
            'coping_strategies',
            'breathing_exercises'
        ]
    elif level == 'low':
        urgency = 'standard'
        actions = [
            'peer_support',
            'educational_resources',
            'wellness_check',
            'community_connection'
        ]
    else:
        urgency = 'routine'
        actions = [
            'standard_support',
            'preventive_resources'
        ]

    return urgency, actions

@router.post("/analyze", response_model=CrisisAnalysisResponse)
async def analyze_crisis_indicators(request: CrisisAnalysisRequest):
    """
    Analyze user responses for crisis indicators during onboarding
    """
    try:
        logger.info(f"Analyzing crisis indicators for step: {request.stepId}")

        # Convert response to text for analysis
        response_text = str(request.response)

        # Analyze keywords
        keyword_analysis = analyze_crisis_keywords(response_text)

        # Calculate crisis level
        level, confidence, indicators = calculate_crisis_level(keyword_analysis, request.response)

        # Determine urgency and actions
        urgency, recommended_actions = determine_urgency_and_actions(level, indicators)

        # Generate reasoning
        reasoning = f"Analysis based on {request.stepId} response. "
        if indicators:
            reasoning += f"Detected indicators: {', '.join(indicators[:3])}. "
        reasoning += f"Confidence: {confidence:.2f}"

        response = CrisisAnalysisResponse(
            level=level,
            confidence=confidence,
            indicators=indicators,
            urgency=urgency,
            recommended_actions=recommended_actions,
            reasoning=reasoning
        )

        # Log crisis detection
        if level in ['high', 'critical']:
            logger.warning(f"Crisis detected - Level: {level}, User: {request.userId}, Step: {request.stepId}")

        return response

    except Exception as e:
        logger.error(f"Error analyzing crisis indicators: {str(e)}")
        # Return safe default for critical systems
        return CrisisAnalysisResponse(
            level='moderate',
            confidence=0.5,
            indicators=['analysis_error'],
            urgency='moderate',
            recommended_actions=['enhanced_support', 'manual_review'],
            reasoning='Analysis failed - defaulting to moderate risk for safety'
        )

# generate_onboarding_profile function has been moved to profile_generation.py

@router.get("/health")
async def crisis_analysis_health():
    """Health check for crisis analysis system"""
    return {
        "status": "healthy",
        "service": "crisis_analysis",
        "version": "1.0.0",
        "capabilities": [
            "keyword_analysis",
            "crisis_level_detection",
            "urgency_assessment",
            "action_recommendation"
        ]
    }
