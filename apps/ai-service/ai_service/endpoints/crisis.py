from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import List

from ..processors.crisis_detection import detect_crisis_indicators

router = APIRouter()

class CrisisDetectionRequest(BaseModel):
    user_input: str = Field(..., description="User's textual input or expression of distress.")
    behavior_patterns: List[str] = Field(default_factory=list, description="List of observed behavioral indicators.")

class CrisisDetectionResponse(BaseModel):
    crisis_level: str = Field(..., description="Assessed crisis level: immediate_intervention, enhanced_support, or standard_support.")

@router.post("/detect-crisis", response_model=CrisisDetectionResponse)
async def detect_crisis(request: CrisisDetectionRequest):
    """
    Detects the level of crisis based on user input and behavioral patterns.
    """
    try:
        crisis_level = detect_crisis_indicators(request.user_input, request.behavior_patterns)
        return CrisisDetectionResponse(crisis_level=crisis_level)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Crisis detection failed: {str(e)}")
