from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

from ..processors.content_personalization import content_personalization_processor

# Define models locally instead of importing from main to avoid circular imports
class QuranVerseOutput(BaseModel):
    id: str
    arabic_text: str
    translation_en: str
    surah_name_en: str
    ayah_number: int
    theme: str
    audio_url: Optional[str] = None

class DhikrPhraseOutput(BaseModel):
    id: str
    arabic_text: str
    transliteration_en: str
    translation_en: str
    recommended_count: Optional[int] = None
    theme: str
    audio_url: Optional[str] = None

class PersonalizedContentRequest(BaseModel):
    """Request model for personalized content recommendations for Qalb Rescue"""
    user_id: str = Field(..., description="User ID")
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences (e.g., preferred qari, language)")
    past_interactions: List[Dict[str, Any]] = Field(default_factory=list, description="History of user interactions with Qalb Rescue")
    crisis_indicators: List[str] = Field(default_factory=list, description="Real-time crisis indicators detected")

class PersonalizedContentResponse(BaseModel):
    """Response model for personalized content for Qalb Rescue"""
    quran_verses: List[QuranVerseOutput] = Field(default_factory=list, description="Recommended Quranic verses")
    dhikr_phrases: List[DhikrPhraseOutput] = Field(default_factory=list, description="Recommended Dhikr phrases")
    reasoning: str = Field(..., description="Reasoning for the personalized content")

# Import verify_token from auth module
from ..auth.dependencies import verify_token

router = APIRouter(prefix="/content-personalization", tags=["Content Personalization"])

@router.post("/recommend", response_model=PersonalizedContentResponse)
async def recommend_personalized_content(
    request: PersonalizedContentRequest,
    user: Dict[str, Any] = Depends(verify_token)
):
    """
    Recommends personalized Quranic verses and Dhikr based on user data.
    """
    try:
        result = content_personalization_processor.personalize_content(
            user_preferences=request.user_preferences,
            past_interactions=request.past_interactions,
            crisis_indicators=request.crisis_indicators
        )
        return PersonalizedContentResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to personalize content: {str(e)}")
