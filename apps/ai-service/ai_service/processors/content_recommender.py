"""
Content Recommendation Processor
AI-powered Islamic content recommendations based on user state and healing needs
"""

try:
    import openai
except ImportError:
    openai = None
from typing import Dict, List, Any, Optional
import json
import logging
from dataclasses import dataclass
import os
# import numpy as np
# from sklearn.metrics.pairwise import cosine_similarity

@dataclass
class ContentRecommendation:
    """Content recommendation result"""
    content_ids: List[str]
    reasoning: str
    priority_order: List[str]
    confidence_scores: List[float]
    personalization_factors: List[str]

logger = logging.getLogger(__name__)

@dataclass
class ContentRecommendation:
    """Content recommendation result"""
    content_ids: List[str]
    reasoning: str
    priority_order: List[str]
    confidence_scores: Dict[str, float]
    personalization_factors: List[str]

class ContentRecommendationProcessor:
    """
    AI-powered content recommender for Islamic healing content
    """

    def __init__(self):
        if openai:
            self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        else:
            self.openai_client = None
        self.content_database = self._initialize_content_database()
        self.content_library = list(self.content_database.values())
        self.user_embeddings = {}  # Cache for user preference embeddings

    def _initialize_content_database(self) -> Dict[str, Any]:
        """Initialize content database with Islamic healing content"""
        return {
            "dhikr_morning": {
                "id": "dhikr_morning",
                "title": "Morning Dhikr Collection",
                "type": "audio",
                "soul_layers": ["qalb", "ruh"],
                "healing_focus": ["spiritual_connection", "daily_protection"],
                "duration": 15,
                "difficulty": "beginner",
                "tags": ["morning", "protection", "dhikr", "daily_routine"]
            },
            "quran_healing": {
                "id": "quran_healing",
                "title": "Healing Verses from Quran",
                "type": "audio",
                "soul_layers": ["qalb", "ruh", "jism"],
                "healing_focus": ["spiritual_healing", "physical_healing"],
                "duration": 25,
                "difficulty": "beginner",
                "tags": ["quran", "healing", "recitation", "ruqyah"]
            },
            "istighfar_practice": {
                "id": "istighfar_practice",
                "title": "Istighfar for Soul Purification",
                "type": "practice",
                "soul_layers": ["nafs", "qalb"],
                "healing_focus": ["purification", "forgiveness", "emotional_healing"],
                "duration": 20,
                "difficulty": "beginner",
                "tags": ["istighfar", "forgiveness", "purification", "emotional"]
            },
            "names_allah_reflection": {
                "id": "names_allah_reflection",
                "title": "99 Names of Allah Reflection",
                "type": "video",
                "soul_layers": ["aql", "qalb", "ruh"],
                "healing_focus": ["spiritual_knowledge", "contemplation", "divine_connection"],
                "duration": 30,
                "difficulty": "intermediate",
                "tags": ["names_of_allah", "reflection", "contemplation", "knowledge"]
            },
            "breathing_dhikr": {
                "id": "breathing_dhikr",
                "title": "Breathing with Dhikr",
                "type": "practice",
                "soul_layers": ["jism", "qalb"],
                "healing_focus": ["anxiety_relief", "mindfulness", "physical_calm"],
                "duration": 10,
                "difficulty": "beginner",
                "tags": ["breathing", "dhikr", "anxiety", "calm", "mindfulness"]
            },
            "salawat_collection": {
                "id": "salawat_collection",
                "title": "Salawat for Spiritual Elevation",
                "type": "audio",
                "soul_layers": ["qalb", "ruh"],
                "healing_focus": ["spiritual_elevation", "prophet_connection", "blessing"],
                "duration": 18,
                "difficulty": "beginner",
                "tags": ["salawat", "prophet", "blessing", "elevation"]
            }
        }

    async def recommend_content(
        self,
        user_id: str,
        healing_focus: List[str],
        current_mood: Optional[str] = None,
        time_available: Optional[int] = None,
        content_types: Optional[List[str]] = None,
        soul_layers: Optional[List[str]] = None
    ) -> ContentRecommendation:
        """
        Generate personalized content recommendations
        """
        try:
            # Get user preferences and history
            user_profile = await self._get_user_profile(user_id)

            # Filter content based on constraints
            filtered_content = self._filter_content(
                time_available=time_available,
                content_types=content_types,
                soul_layers=soul_layers
            )

            # Score content based on relevance
            scored_content = await self._score_content(
                filtered_content,
                healing_focus,
                current_mood,
                user_profile
            )

            # Generate AI-powered reasoning
            reasoning = await self._generate_reasoning(
                healing_focus,
                current_mood,
                scored_content[:3]  # Top 3 recommendations
            )

            # Extract recommendations
            content_ids = [item["id"] for item in scored_content[:5]]
            priority_order = content_ids.copy()
            confidence_scores = [item["score"] for item in scored_content[:5]]

            personalization_factors = self._identify_personalization_factors(
                healing_focus,
                current_mood,
                user_profile
            )

            return ContentRecommendation(
                content_ids=content_ids,
                reasoning=reasoning,
                priority_order=priority_order,
                confidence_scores=confidence_scores,
                personalization_factors=personalization_factors
            )

        except Exception as e:
            logger.error(f"Error in content recommendation: {str(e)}")
            return self._create_fallback_recommendations(healing_focus)

    async def _get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile and preferences"""
        # TODO: Implement database lookup
        return {
            "preferred_content_types": ["audio", "practice"],
            "completed_content": [],
            "favorite_topics": ["dhikr", "quran"],
            "skill_level": "beginner",
            "time_preferences": {"morning": 15, "evening": 20}
        }

    def _get_content_library(self) -> List[Dict[str, Any]]:
        """Get content library"""
        return list(self.content_database.values())





    def _calculate_mood_relevance(self, content: Dict[str, Any], mood: str) -> float:
        """Calculate how relevant content is for current mood"""
        mood_mappings = {
            "anxious": ["calm", "peace", "trust", "anxiety"],
            "sad": ["hope", "comfort", "mercy", "depression"],
            "peaceful": ["mindfulness", "presence", "meditation"],
            "confused": ["guidance", "clarity", "wisdom"],
            "angry": ["patience", "forgiveness", "calm"],
            "fearful": ["protection", "trust", "courage"]
        }

        if mood not in mood_mappings:
            return 0.1  # Default low relevance

        relevant_terms = mood_mappings[mood]
        content_focus = content.get("healing_focus", [])

        overlap = len(set(relevant_terms) & set(content_focus))
        return min(0.5, overlap * 0.2)  # Max 0.5 relevance from mood

    async def _generate_reasoning(self, healing_focus: List[str], current_mood: str,
                                top_recommendations: List[Dict[str, Any]]) -> str:
        """Generate reasoning for recommendations"""
        reasoning = f"Based on your focus on {', '.join(healing_focus)} and current mood of {current_mood}, "

        if top_recommendations:
            rec_titles = [rec.get("title", rec.get("id", "content")) for rec in top_recommendations[:2]]
            reasoning += f"I recommend starting with {' and '.join(rec_titles)}. "

        reasoning += "These selections align with Islamic healing principles and your personal needs."
        return reasoning

    def _identify_personalization_factors(self, healing_focus: List[str],
                                        current_mood: str,
                                        user_profile: Dict[str, Any]) -> List[str]:
        """Identify factors used for personalization"""
        factors = []

        if healing_focus:
            factors.append(f"Healing focus: {', '.join(healing_focus)}")

        if current_mood:
            factors.append(f"Current mood: {current_mood}")

        skill_level = user_profile.get("skill_level")
        if skill_level:
            factors.append(f"Skill level: {skill_level}")

        preferred_types = user_profile.get("preferred_content_types", [])
        if preferred_types:
            factors.append(f"Preferred content: {', '.join(preferred_types)}")

        return factors

    def _filter_content(
        self,
        time_available: Optional[int] = None,
        content_types: Optional[List[str]] = None,
        soul_layers: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Filter content based on constraints"""
        filtered = list(self.content_database.values())

        if time_available:
            filtered = [c for c in filtered if c["duration"] <= time_available]

        if content_types:
            filtered = [c for c in filtered if c["type"] in content_types]

        if soul_layers:
            filtered = [
                c for c in filtered
                if any(layer in c["soul_layers"] for layer in soul_layers)
            ]

        return filtered

    async def _score_content(
        self,
        content_list: List[Dict[str, Any]],
        healing_focus: List[str],
        current_mood: Optional[str],
        user_profile: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Score content based on relevance to user needs"""
        scored_content = []

        for content in content_list:
            score = 0.0

            # Healing focus alignment
            focus_overlap = len(set(healing_focus) & set(content["healing_focus"]))
            score += focus_overlap * 0.4

            # User preference alignment
            if content["type"] in user_profile.get("preferred_content_types", []):
                score += 0.2

            # Difficulty level match
            if content["difficulty"] == user_profile.get("skill_level", "beginner"):
                score += 0.1

            # Mood-based scoring
            if current_mood:
                mood_score = self._calculate_mood_relevance(content, current_mood)
                score += mood_score * 0.2

            # Novelty bonus (not completed recently)
            if content["id"] not in user_profile.get("completed_content", []):
                score += 0.1

            scored_content.append({
                **content,
                "score": min(score, 1.0)  # Cap at 1.0
            })

        # Sort by score descending
        return sorted(scored_content, key=lambda x: x["score"], reverse=True)

    def _calculate_mood_relevance(self, content: Dict[str, Any], mood: str) -> float:
        """Calculate how relevant content is for current mood"""
        mood_mappings = {
            "anxious": ["breathing", "dhikr", "calm"],
            "sad": ["istighfar", "healing", "comfort"],
            "grateful": ["dhikr", "names_of_allah", "reflection"],
            "seeking_guidance": ["quran", "reflection", "knowledge"],
            "peaceful": ["contemplation", "reflection", "elevation"]
        }

        relevant_tags = mood_mappings.get(mood.lower(), [])
        tag_overlap = len(set(relevant_tags) & set(content["tags"]))

        return tag_overlap / len(relevant_tags) if relevant_tags else 0.0

    async def _generate_reasoning(
        self,
        healing_focus: List[str],
        current_mood: Optional[str],
        top_content: List[Dict[str, Any]]
    ) -> str:
        """Generate AI-powered reasoning for recommendations"""
        if not self.openai_client:
            # Fallback reasoning when OpenAI is not available
            return f"These recommendations focus on {', '.join(healing_focus)} to support your current healing needs through authentic Islamic practices."

        try:
            prompt = f"""
As an Islamic healing expert, explain why these content recommendations are suitable:

HEALING FOCUS: {', '.join(healing_focus)}
CURRENT MOOD: {current_mood or 'Not specified'}

RECOMMENDED CONTENT:
{json.dumps([{k: v for k, v in content.items() if k in ['title', 'type', 'healing_focus', 'tags']} for content in top_content], indent=2)}

Provide a brief, warm explanation (2-3 sentences) of why these recommendations will support the user's healing journey from an Islamic perspective.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a compassionate Islamic healing guide. Provide warm, encouraging explanations."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=200
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error generating reasoning: {str(e)}")
            return f"These recommendations focus on {', '.join(healing_focus)} to support your current healing needs through authentic Islamic practices."

    def _identify_personalization_factors(
        self,
        healing_focus: List[str],
        current_mood: Optional[str],
        user_profile: Dict[str, Any]
    ) -> List[str]:
        """Identify factors used for personalization"""
        factors = []

        if healing_focus:
            factors.append(f"Healing focus: {', '.join(healing_focus)}")

        if current_mood:
            factors.append(f"Current mood: {current_mood}")

        if user_profile.get("preferred_content_types"):
            factors.append(f"Preferred types: {', '.join(user_profile['preferred_content_types'])}")

        if user_profile.get("skill_level"):
            factors.append(f"Skill level: {user_profile['skill_level']}")

        return factors

    def _create_fallback_recommendations(self, healing_focus: List[str]) -> ContentRecommendation:
        """Create fallback recommendations when AI fails"""
        # Default safe recommendations
        default_content = ["dhikr_morning", "istighfar_practice", "breathing_dhikr"]

        return ContentRecommendation(
            content_ids=default_content,
            reasoning=f"These foundational Islamic practices support {', '.join(healing_focus)} through authentic spiritual methods.",
            priority_order=default_content,
            confidence_scores=[0.7] * len(default_content),
            personalization_factors=["Default recommendations", "Safe Islamic practices"]
        )

# Singleton instance
content_recommender = ContentRecommendationProcessor()
