"""
Journey Generation Processor for Feature 2: Personalized Healing Journeys
AI-powered journey creation and personalization
"""

from typing import Dict, List, Any, Optional, TypedDict
import json
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# --- Pydantic-like Models for Backend Data (using TypedDict for simplicity) ---
# These represent the structure of data fetched from backend (based on Prisma schema)

class BackendNameOfAllahContent(TypedDict, total=False):
    id: str
    name: str
    arabicScript: Optional[str]
    audioUrl: Optional[str]
    meaning: str
    significance: str
    reflectionPrompt: Optional[str]
    practicalApplication: Optional[str]
    dhikrCount: Optional[int]
    layerFocus: List[str] # Assuming LayerFocus enum translates to string list
    benefits: List[str]
    sourceReference: Optional[str]
    isActive: bool

class BackendQuranicVerseContent(TypedDict, total=False):
    id: str
    surahNumber: int
    ayahNumber: int
    arabicText: str
    audioUrl: Optional[str]
    translationEn: Optional[str]
    tafsirSource: Optional[str]
    tafsirEn: Optional[str]
    contextualExplanation: Optional[str]
    reflectionPrompts: List[str]
    practicalApplication: Optional[str]
    layerFocus: List[str]
    themes: List[str]
    isActive: bool

class BackendSunnahPracticeContent(TypedDict, total=False):
    id: str
    title: str
    category: str # Enum: Physical, Spiritual, Social, Mental, Emotional
    description: str
    stepsJson: Optional[Any] # Prisma Json maps to Any
    duasJson: Optional[Any]
    intention: Optional[str]
    reflection: Optional[str]
    benefits: List[str]
    estimatedDuration: Optional[int]
    difficultyLevel: Optional[str]
    sourceReference: Optional[str]
    layerFocus: List[str]
    isActive: bool

# --- End of Backend Data Models ---


class JourneyGenerationProcessor:
    """AI processor for generating personalized healing journeys"""
    
    CACHE_DURATION_SECONDS = 3600  # Cache for 1 hour

    def __init__(self):
        self.journey_templates = self._load_journey_templates()
        self._practice_library_cache: Optional[Dict[str, List[Dict[str, Any]]]] = None
        self._practice_library_cache_timestamp: Optional[datetime] = None
        self.practice_library = self._load_practice_library()
        self.personalization_rules = self._load_personalization_rules()
    
    # ... (other methods like generate_journey_parameters, generate_journey_content, etc. remain unchanged) ...
    # For brevity, I will omit the methods that are not directly part of this change.
    # Assume all other methods from the previous version of the file are still here.

    def _determine_journey_type(self, assessment: Dict, user_profile: Dict) -> str:
        primary_layer = assessment.get('primaryLayer', 'qalb')
        crisis_level = assessment.get('crisisLevel', 'none')
        awareness_level = user_profile.get('awarenessLevel', 'symptom_aware')
        if crisis_level in ['high', 'critical']: return 'crisis_recovery'
        layer_mapping = {
            'aql': 'tranquil_mind', 'qalb': 'heart_purification',
            'nafs': 'ego_purification', 'ruh': 'spiritual_optimization',
            'jism': 'tranquil_mind'
        }
        base_type = layer_mapping.get(primary_layer, 'heart_purification')
        if awareness_level == 'spiritual_optimizer': return 'spiritual_optimization'
        return base_type
    
    def _calculate_duration(self, assessment: Dict, user_profile: Dict, preferences: Dict) -> int:
        severity_score = assessment.get('severityScore', 0)
        if isinstance(assessment.get('severity'), str):
            severity_map_to_score = {'crisis': 2, 'mild': 3, 'moderate': 5, 'severe': 8}
            severity_score = severity_map_to_score.get(assessment.get('severity', 'mild').lower(), severity_score)
        if severity_score >= 8: base_duration = 40
        elif severity_score >= 6: base_duration = 21
        elif severity_score >= 4: base_duration = 14
        else: base_duration = 7
        if preferences.get('duration'):
            user_selected_duration = preferences.get('duration')
            if isinstance(user_selected_duration, int):
                return min(max(user_selected_duration, 7), 90)
            return base_duration
        return base_duration
    
    def _calculate_time_commitment(self, user_profile: Dict, preferences: Dict) -> int:
        time_availability = user_profile.get('timeAvailability', 'moderate')
        time_mapping = {'very_limited': 10, 'limited': 15, 'moderate': 20, 'flexible': 30, 'abundant': 45}
        base_time = time_mapping.get(time_availability, 20)
        profession = user_profile.get('profession', '')
        if any(busy_prof in profession.lower() for busy_prof in ['doctor', 'nurse', 'teacher', 'lawyer']):
            base_time = min(base_time, 15)
        if preferences.get('dailyTimeCommitment'):
            return min(max(preferences['dailyTimeCommitment'], 5), 60)
        return base_time
    
    def _determine_ruqya_level(self, user_profile: Dict) -> str:
        ruqya_familiarity = user_profile.get('ruqyaFamiliarity', 'unaware')
        level_mapping = {'expert': 'advanced', 'practitioner': 'intermediate', 'aware': 'basic', 'skeptical': 'none', 'unaware': 'none'}
        return level_mapping.get(ruqya_familiarity, 'none')

    def _recommend_community_support(self, user_profile: Dict, assessment: Dict) -> bool:
        if assessment.get('crisisLevel') in ['moderate', 'high', 'critical']: return True
        if user_profile.get('awarenessLevel') == 'new_muslim': return True
        if any(prof in user_profile.get('profession', '').lower() for prof in ['healthcare', 'teacher', 'counselor']): return True
        return user_profile.get('communityPreference', True)

    def _determine_cultural_adaptations(self, user_profile: Dict) -> List[str]:
        adaptations = []
        cultural_background = user_profile.get('culturalBackground', '')
        if 'south_asian' in cultural_background.lower(): adaptations.extend(['urdu_terminology', 'south_asian_examples'])
        elif 'arab' in cultural_background.lower(): adaptations.extend(['arabic_emphasis', 'middle_eastern_examples'])
        elif 'african' in cultural_background.lower(): adaptations.extend(['african_islamic_traditions'])
        elif 'western' in cultural_background.lower(): adaptations.extend(['western_context', 'convert_friendly'])
        return adaptations

    def _generate_initial_recommendations(self, assessment: Dict, user_profile: Dict, journey_type: str) -> List[str]:
        recommendations = []
        if assessment.get('crisisLevel') != 'none': recommendations.extend(["Enhanced crisis monitoring", "Regular check-ins"])
        if 'healthcare' in user_profile.get('profession', '').lower(): recommendations.extend(["Stress management integration", "Healthcare Islamic practices"])
        ruqya_familiarity = user_profile.get('ruqyaFamiliarity', 'unaware')
        if ruqya_familiarity == 'expert': recommendations.append("Advanced ruqya integration")
        elif ruqya_familiarity == 'unaware': recommendations.append("Gentle intro to Islamic healing")
        return recommendations

    def _generate_journey_title(self, config: Dict, user_profile: Dict) -> str:
        journey_type = config.get('type', 'heart_purification')
        profession = user_profile.get('profession', '')
        title_map = {
            'tranquil_mind': 'Tranquil Mind: Finding Peace in Allah', 'heart_purification': 'Pure Heart: Spiritual Cleansing Journey',
            'ego_purification': 'Humble Soul: Ego Purification Path', 'spiritual_optimization': 'Advanced Spiritual Development Journey',
            'crisis_recovery': 'Emergency Healing: Crisis Recovery', 'maintenance_program': 'Sustained Wellness Program'
        }
        if 'healthcare' in profession.lower():
            title_map.update({
                'tranquil_mind': 'Healing the Healer: Mental Clarity', 'heart_purification': 'Compassionate Care: Heart Purification',
                'ego_purification': 'Humble Service: Professional Excellence', 'spiritual_optimization': 'Advanced Islamic Psychology for Healthcare'
            })
        elif 'teacher' in profession.lower():
            title_map.update({
                'tranquil_mind': 'Peaceful Educator: Mind Clarity', 'heart_purification': 'Inspiring Hearts: Spiritual Growth',
                'ego_purification': 'Humble Leadership: Teaching Excellence', 'spiritual_optimization': 'Advanced Spiritual Development for Educators'
            })
        return title_map.get(journey_type, 'Personalized Islamic Healing Journey')

    def _generate_journey_description(self, config: Dict, user_profile: Dict, assessment: Dict) -> str:
        desc = f"A {config.get('duration', 21)}-day journey for {config.get('primaryLayer', 'qalb')} healing. "
        if user_profile.get('profession'): desc += f"Adapted for {user_profile['profession']}. "
        return desc + "Combines Islamic wisdom with wellness approaches."

    def _generate_personalized_welcome(self, config: Dict, user_profile: Dict) -> str:
        name = user_profile.get('name', 'Dear User')
        return f"Assalamu Alaikum {name},\n\nWelcome to your personalized healing journey. May Allah grant you healing. Ameen."

    def _generate_daily_content(self, config: Dict, user_profile: Dict, assessment: Dict) -> List[Dict]:
        duration = config.get('duration', 21)
        primary_layer = config.get('primaryLayer', 'qalb')
        time_commitment = config.get('dailyTimeCommitment', 20)
        days = []
        all_previously_used_ids = set()
        ongoing_progress_data_for_journey = assessment.get("ongoingProgressData")
        layer_feedback_data_for_journey = assessment.get("layerFeedbackData")

        for day_num in range(1, duration + 1):
            day_content_tuple = self._generate_single_day_content(
                day_num, duration, primary_layer, time_commitment, user_profile, assessment, config,
                all_previously_used_ids, ongoing_progress_data_for_journey, layer_feedback_data_for_journey
            )
            if isinstance(day_content_tuple, tuple) and len(day_content_tuple) == 2:
                day_content, used_on_this_day_ids = day_content_tuple
                days.append(day_content)
                all_previously_used_ids.update(used_on_this_day_ids)
            else:
                logger.error(f"Unexpected return type from _generate_single_day_content: {type(day_content_tuple)}")
                days.append({"dayNumber": day_num, "theme": "Error", "practices": [], "error": "Content generation failed"})
        return days

    def _generate_single_day_content(self, day_num: int, total_days: int, primary_layer: str, 
                                   time_commitment: int, user_profile: Dict, assessment_data: Dict,
                                   journey_config: Dict, previously_used_ids: set,
                                   ongoing_progress_data: Optional[Dict] = None,
                                   layer_feedback_data: Optional[Dict] = None) -> (Dict[str, Any], set[str]):
        journey_type = journey_config.get('type', 'heart_purification')
        theme = self._get_day_theme(day_num, total_days, primary_layer, journey_type)
        learning_objective = self._get_learning_objective(day_num, theme, primary_layer, journey_type)
        
        practices, used_on_this_day_ids = self._generate_day_practices_v2(
            day_num=day_num, theme=theme, primary_layer_focus=primary_layer,
            user_profile=user_profile, assessment_data=assessment_data, journey_config=journey_config,
            previously_used_ids=previously_used_ids,
            ongoing_progress_data=ongoing_progress_data, layer_feedback_data=layer_feedback_data
        )
        
        reflection_prompts = self._generate_reflection_prompts(theme, primary_layer, user_profile, day_num, journey_config.get('duration', 21))
        community_activity = self._generate_community_activity(day_num, theme) if journey_config.get('communityIntegration') else None
        progress_milestone = self._get_progress_milestone(day_num, total_days, theme)
        
        day_data = {
            'dayNumber': day_num, 'theme': theme, 'learningObjective': learning_objective,
            'practices': practices, 'reflectionPrompts': reflection_prompts,
            'communityActivity': community_activity, 'progressMilestone': progress_milestone,
            'adaptiveContent': self._generate_adaptive_content(theme, user_profile)
        }
        return day_data, used_on_this_day_ids

    def _load_journey_templates(self) -> Dict:
        return {
            "heart_purification": {
                "themes": ["Intention & Foundation", "Self-Reflection", "Repentance & Seeking Forgiveness", "Gratitude & Patience", "Love & Compassion", "Spiritual Connection", "Sustaining Purity"],
                "default_objectives": {"Intention & Foundation": "Understand the importance of pure intention...", "Self-Reflection": "Identify personal spiritual blockages...",} # Truncated for example
            },
            "tranquil_mind": {"themes": ["Understanding Anxiety", "Islamic Coping Mechanisms"], "default_objectives": {"Understanding Anxiety": "Recognize sources of anxiety...",}}
        }

    # --- Mock Backend Fetch Methods ---
    def _fetch_mock_name_of_allah_content(self) -> List[BackendNameOfAllahContent]:
        logger.info("Simulating fetch for NameOfAllahContent from backend.")
        # Simulate API call delay if needed: import time; time.sleep(0.1)
        return [
            {"id": "noa_bk_001", "name": "Ar-Rahman", "arabicScript": "الرحمن", "meaning": "The Entirely Merciful", "significance": "Significance of Ar-Rahman...", "reflectionPrompt": "Reflect on Allah's mercy.", "layerFocus": ["qalb"], "benefits": ["hope", "comfort"], "isActive": True, "dhikrCount": 33},
            {"id": "noa_bk_002", "name": "Al-Malik", "arabicScript": "الملك", "meaning": "The Absolute Ruler", "significance": "Significance of Al-Malik...", "reflectionPrompt": "Reflect on Allah's sovereignty.", "layerFocus": ["aql", "ruh"], "benefits": ["strength", "perspective"], "isActive": True, "dhikrCount": 33},
            {"id": "noa_bk_003", "name": "Inactive Name", "meaning":"...", "significance":"...", "layerFocus":["qalb"], "benefits":[], "isActive": False}
        ]

    def _fetch_mock_quranic_verse_content(self) -> List[BackendQuranicVerseContent]:
        logger.info("Simulating fetch for QuranicVerseContent from backend.")
        return [
            {"id": "qvr_bk_001", "surahNumber": 1, "ayahNumber": 1, "arabicText": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ", "translationEn": "In the name of Allah, the Entirely Merciful...", "tafsirEn": "Opening verse.", "contextualExplanation": "The Basmalah.", "reflectionPrompts": ["How does this verse make you feel?"], "layerFocus": ["ruh"], "themes": ["beginning", "mercy"], "isActive": True},
            {"id": "qvr_bk_002", "surahNumber": 2, "ayahNumber": 255, "arabicText": "اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ", "translationEn": "Allah - there is no deity except Him...", "tafsirEn": "Ayat al-Kursi.", "contextualExplanation": "Verse of the Throne.", "reflectionPrompts": ["Reflect on Allah's power."], "layerFocus": ["qalb", "aql"], "themes": ["tawhid", "protection"], "isActive": True},
        ]

    def _fetch_mock_sunnah_practice_content(self) -> List[BackendSunnahPracticeContent]:
        logger.info("Simulating fetch for SunnahPracticeContent from backend.")
        return [
            {"id": "sp_bk_001", "title": "Mindful Wudu", "category": "Physical", "description": "Perform Wudu with presence.", "stepsJson": [{"step":1, "instruction":"..."}], "benefits": ["purification", "mindfulness"], "estimatedDuration": 7, "difficultyLevel": "beginner", "layerFocus": ["jism", "qalb"], "isActive": True},
            {"id": "sp_bk_002", "title": "Using Miswak", "category": "Physical", "description": "The Sunnah of using Miswak.", "benefits": ["oral_hygiene", "sunnah_revival"], "estimatedDuration": 3, "difficultyLevel": "beginner", "layerFocus": ["jism"], "isActive": True},
        ]

    def _transform_name_of_allah_data(self, backend_data: List[BackendNameOfAllahContent]) -> List[Dict[str, Any]]:
        transformed = []
        for item in backend_data:
            if not item.get("isActive", False): continue
            transformed.append({
                "id": item["id"],
                "title": item["name"], # Using name as title
                "description": item.get("significance", ""),
                "duration": item.get("dhikrCount", 33) // 10 + 5, # Approximate duration
                "baseLayerFocus": item["layerFocus"][0] if item.get("layerFocus") else "qalb",
                "themeTags": item.get("benefits", []) + [tag.lower() for tag in item.get("layerFocus", [])],
                "difficultyLevel": "beginner", # Defaulting, as not in backend model
                "componentDetails": {
                    "originalContentId": item["id"],
                    "name": item["name"],
                    "arabicScript": item.get("arabicScript"),
                    "audioUrl": item.get("audioUrl"),
                    "meaning": item.get("meaning"),
                    "significance": item.get("significance"),
                    "reflectionPrompt": item.get("reflectionPrompt"),
                    "practicalApplication": item.get("practicalApplication"),
                    "dhikrCount": item.get("dhikrCount")
                }
            })
        return transformed

    def _transform_quranic_verse_data(self, backend_data: List[BackendQuranicVerseContent]) -> List[Dict[str, Any]]:
        transformed = []
        for item in backend_data:
            if not item.get("isActive", False): continue
            transformed.append({
                "id": item["id"],
                "title": f"Surah {item['surahNumber']}:{item['ayahNumber']}",
                "description": item.get("contextualExplanation", ""),
                "duration": 10, # Default
                "baseLayerFocus": item["layerFocus"][0] if item.get("layerFocus") else "ruh",
                "themeTags": item.get("themes", []),
                "difficultyLevel": "beginner", # Default
                "componentDetails": {
                    "originalContentId": item["id"],
                    "surahNumber": item["surahNumber"],
                    "ayahNumber": item["ayahNumber"],
                    "arabicText": item.get("arabicText"),
                    "audioUrl": item.get("audioUrl"),
                    "translationEn": item.get("translationEn"),
                    "tafsirSource": item.get("tafsirSource"),
                    "tafsirEn": item.get("tafsirEn"),
                    "contextualExplanation": item.get("contextualExplanation"),
                    "reflectionPrompts": item.get("reflectionPrompts", [])
                }
            })
        return transformed

    def _transform_sunnah_practice_data(self, backend_data: List[BackendSunnahPracticeContent]) -> List[Dict[str, Any]]:
        transformed = []
        for item in backend_data:
            if not item.get("isActive", False): continue
            transformed.append({
                "id": item["id"],
                "title": item["title"],
                "description": item.get("description", ""),
                "duration": item.get("estimatedDuration", 5),
                "baseLayerFocus": item["layerFocus"][0] if item.get("layerFocus") else "jism",
                "themeTags": item.get("benefits", []) + [item.get("category","general").lower()],
                "difficultyLevel": item.get("difficultyLevel", "beginner").lower(),
                "componentDetails": {
                    "originalContentId": item["id"],
                    "category": item.get("category"),
                    "stepsJson": item.get("stepsJson"),
                    "duasJson": item.get("duasJson"),
                    "intention": item.get("intention"),
                    "reflection": item.get("reflection"),
                    "benefits": item.get("benefits"),
                    "sourceReference": item.get("sourceReference")
                }
            })
        return transformed

    def _load_practice_library(self) -> Dict[str, List[Dict[str, Any]]]:
        if self._practice_library_cache and \
           self._practice_library_cache_timestamp and \
           (datetime.now() - self._practice_library_cache_timestamp).total_seconds() < self.CACHE_DURATION_SECONDS:
            logger.info("Practice library cache hit. Returning cached version.")
            return self._practice_library_cache

        logger.info("Practice library cache miss or expired. Attempting to fetch from (simulated) backend.")

        fetched_library: Dict[str, List[Dict[str, Any]]] = {
            "NameOfAllahSpotlight": [],
            "QuranicVerseReflection": [],
            "SunnahPractice": []
        }

        try:
            # Simulate fetching each content type
            noa_data = self._fetch_mock_name_of_allah_content()
            fetched_library["NameOfAllahSpotlight"] = self._transform_name_of_allah_data(noa_data)

            qvr_data = self._fetch_mock_quranic_verse_content()
            fetched_library["QuranicVerseReflection"] = self._transform_quranic_verse_data(qvr_data)

            sp_data = self._fetch_mock_sunnah_practice_content()
            fetched_library["SunnahPractice"] = self._transform_sunnah_practice_data(sp_data)

            logger.info("Successfully fetched and transformed practice library from (simulated) backend.")
            self._practice_library_cache = fetched_library
            self._practice_library_cache_timestamp = datetime.now()
            return fetched_library

        except Exception as e:
            logger.error(f"Error fetching/transforming practice library from (simulated) backend: {e}. Falling back to hardcoded library.")
            # Fall through to use hardcoded if exception occurs

        # --- Fallback to Hardcoded Library (if backend call simulation fails) ---
        logger.info("Using hardcoded practice library as fallback or initial data.")
        hardcoded_library = { # This is the original hardcoded structure
            "NameOfAllahSpotlight": [
                {"id": "noa_001", "title": "Ar-Rahman (The Most Merciful)", "description": "Connect with Allah's attribute of immense mercy.", "duration": 7, "baseLayerFocus": "qalb", "difficultyLevel": "beginner", "themeTags": ["mercy", "hope", "compassion"], "keywords": ["Allah's names", "mercy", "Rahman"], "componentDetails": {"originalContentId": "noa_001", "name": "Ar-Rahman", "arabicScript": "الرحمن", "audioUrl": "path/to/ar_rahman.mp3", "meaning": "The Entirely Merciful, The Most Merciful", "significance": "Reflect on how Allah's mercy encompasses all things. It is a source of hope and comfort.", "reflectionPrompt": "How have I experienced Allah's mercy (Rahmah) today, in big or small ways?", "practicalApplication": "Practice showing mercy to yourself and others today. Forgive someone or be gentle in your interactions.", "dhikrCount": 33}},
                {"id": "noa_002", "title": "As-Salam (The Source of Peace)", "description": "Find tranquility through Allah's name As-Salam.", "duration": 7, "baseLayerFocus": "aql", "difficultyLevel": "beginner", "themeTags": ["peace", "tranquility", "security"], "keywords": ["Allah's names", "peace", "Salam"], "componentDetails": {"originalContentId": "noa_002", "name": "As-Salam", "arabicScript": "السلام", "audioUrl": "path/to/as_salam.mp3", "meaning": "The Source of Peace, The Flawless", "significance": "Allah is the source of all peace and security. Turning to Him brings tranquility to the heart and mind.", "reflectionPrompt": "What disturbs my peace? How can I seek peace from As-Salam in those situations?", "practicalApplication": "Be a source of peace for someone today. Avoid arguments and speak kindly.", "dhikrCount": 33}},
                {"id": "noa_003", "title": "Al-Ghaffar (The Forgiving)", "description": "Seek Allah's forgiveness and experience His attribute of Al-Ghaffar.", "duration": 8, "baseLayerFocus": "nafs", "difficultyLevel": "beginner", "themeTags": ["forgiveness", "repentance", "mercy"], "keywords": ["Allah's names", "forgiveness", "Ghaffar", "tawbah"], "componentDetails": {"originalContentId": "noa_003", "name": "Al-Ghaffar", "arabicScript": "الغفار", "audioUrl": "path/to/al_ghaffar.mp3", "meaning": "The All-Forgiving, The Absolver", "significance": "Allah is ever-ready to forgive those who turn to Him in sincere repentance. This name brings hope and encourages seeking forgiveness.", "reflectionPrompt": "Is there anything I am holding onto that I need to seek Allah's forgiveness for? What prevents me from asking?", "practicalApplication": "Sincerely make Istighfar (seek forgiveness) for any known or unknown shortcomings. Forgive someone who may have wronged you.", "dhikrCount": 33}}
            ],
            "QuranicVerseReflection": [
                {"id": "qvr_001", "title": "Reflection on Surah Al-Fatihah (1:1-7)", "description": "Deepen your understanding of the opening chapter of the Quran.", "duration": 10, "baseLayerFocus": "ruh", "difficultyLevel": "beginner", "themeTags": ["guidance", "mercy", "lordship", "essentials_of_faith"], "keywords": ["Quran", "Fatihah", "guidance", "prayer"], "componentDetails": {"originalContentId": "qvr_001", "surahNumber": 1, "ayahNumber": 1, "ayahEndNumber": 7, "arabicText": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ...", "audioUrl": "path/to/al_fatihah.mp3", "translationEn": "In the name of Allah, the Entirely Merciful, the Especially Merciful...", "tafsirSource": "Simplified Tafsir", "tafsirEn": "This Surah is the essence of the Quran, a prayer for guidance, lordship, and mercy.", "contextualExplanation": "Understand the core themes of seeking guidance, acknowledging Allah's dominion, and His mercy.", "reflectionPrompts": ["What does 'guidance' mean to me in my current life situation?", "How can I better live by the Straight Path?"], "practicalApplication": "Recite Al-Fatihah with deeper presence in your prayers today."}},
                {"id": "qvr_002", "title": "Ayat al-Kursi (Quran 2:255)", "description": "Reflect on the Verse of the Throne for protection and understanding Allah's sovereignty.", "duration": 12, "baseLayerFocus": "qalb", "difficultyLevel": "intermediate", "themeTags": ["sovereignty", "protection", "Allahs_attributes", "tawhid"], "keywords": ["Quran", "Ayat al-Kursi", "protection", "Throne Verse"], "componentDetails": {"originalContentId": "qvr_002", "surahNumber": 2, "ayahNumber": 255, "arabicText": "اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ...", "audioUrl": "path/to/ayat_al_kursi.mp3", "translationEn": "Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence...", "tafsirSource": "Tafsir Ibn Kathir (Simplified)", "tafsirEn": "This verse describes Allah's absolute power, knowledge, and sovereignty over the heavens and the earth.", "contextualExplanation": "A powerful verse often recited for protection and to affirm Allah's oneness and unmatched attributes.", "reflectionPrompts": ["How does understanding Allah's ultimate power and knowledge affect my worries?", "In what areas of my life do I need to remind myself of Allah's protection?"], "practicalApplication": "Recite Ayat al-Kursi after prayers or before sleeping with understanding and conviction."}},
                {"id": "qvr_003", "title": "Verse on Patience (Quran 2:153)", "description": "Seek strength and guidance through patience and prayer.", "duration": 8, "baseLayerFocus": "nafs", "difficultyLevel": "beginner", "themeTags": ["patience", "prayer", "seeking_help", "resilience"], "keywords": ["Quran", "sabr", "patience", "salah", "prayer", "hardship"], "componentDetails": {"originalContentId": "qvr_003", "surahNumber": 2, "ayahNumber": 153, "arabicText": "يَا أَيُّهَا الَّذِينَ آمَنُوا اسْتَعِينُوا بِالصَّبْرِ وَالصَّلَاةِ ۚ إِنَّ اللَّهَ مَعَ الصَّابِرِينَ", "audioUrl": "path/to/baqarah_153.mp3", "translationEn": "O you who have believed, seek help through patience and prayer. Indeed, Allah is with the patient.", "tafsirSource": "Simplified Tafsir", "tafsirEn": "This verse guides believers to two powerful tools for navigating life's challenges: patience (Sabr) and prayer (Salah).", "contextualExplanation": "A reminder that true strength in times of difficulty comes from steadfast patience and turning to Allah in prayer.", "reflectionPrompts": ["In what current situation do I need to practice more patience?", "How can I make my prayer a greater source of strength and solace?"], "practicalApplication": "When faced with a minor annoyance today, consciously choose a patient response and make a short dua."}}
            ],
            "SunnahPractice": [
                {"id": "sp_001", "title": "Mindful Wudu (Ablution)", "description": "Perform Wudu with presence and intention.", "duration": 7, "baseLayerFocus": "jism", "difficultyLevel": "beginner", "themeTags": ["purification", "mindfulness", "prayer_preparation"], "keywords": ["Sunnah", "wudu", "ablution", "cleanliness", "salah"], "componentDetails": {"originalContentId": "sp_001", "category": "Physical", "stepsJson": [{"step": 1, "instruction": "Make Niyyah (intention) for Wudu to purify yourself for worship."},{"step": 2, "instruction": "Say 'Bismillah'."},{"step": 3, "instruction": "Wash hands up to the wrists three times, ensuring water reaches between fingers."}], "duasJson": [{"name": "Post-Wudu Dua", "arabic": "أشْهَدُ أنْ لا إله إِلاَّ اللَّهُ...", "translation": "I bear witness..."}], "intention": "To perform Wudu for spiritual purification and readiness for prayer, following the Sunnah.", "reflection": "Reflect on the physical act of cleansing as a metaphor for spiritual purification.", "benefits": ["Spiritual purification", "Physical cleanliness", "Preparation for prayer", "Following Sunnah"]}},
                {"id": "sp_002", "title": "Smiling is Charity", "description": "Practice the Sunnah of smiling at others.", "duration": 3, "baseLayerFocus": "qalb", "difficultyLevel": "beginner", "themeTags": ["kindness", "charity", "social_interaction", "positive_emotions"], "keywords": ["Sunnah", "smile", "sadaqah", "kindness"], "componentDetails": {"originalContentId": "sp_002", "category": "Social", "hadithReference": "Tabassumuka fi wajhi akhika laka sadaqah (Your smiling in the face of your brother is charity for you). - Tirmidhi", "benefits": ["Spreads positivity", "Counts as charity", "Improves social connections", "Lifts mood"], "practicalImplementation": "Make a conscious effort to smile sincerely at family members, colleagues, or even strangers you encounter today.", "reflectionPrompt": "How did it feel to consciously offer a smile? Did you notice any change in your interaction or the other person's response?"}},
                {"id": "sp_003", "title": "Dhikr after Salah", "description": "Engage in remembrance of Allah after completing obligatory prayers.", "duration": 5, "baseLayerFocus": "ruh", "difficultyLevel": "beginner", "themeTags": ["dhikr", "remembrance", "post_prayer", "spiritual_connection"], "keywords": ["Sunnah", "dhikr", "salah", "tasbih", "tahmid", "takbir"], "componentDetails": {"originalContentId": "sp_003", "category": "Spiritual", "instructions": "After concluding your prayer, remain seated and engage in the following remembrances: SubhanAllah (33 times), Alhamdulillah (33 times), Allahu Akbar (33 times), followed by La ilaha illAllah wahdahu la sharika lah, lahu'l-mulk wa lahu'l-hamd wa huwa 'ala kulli shay'in qadir (once).", "benefits": ["Increases reward for prayer", "Strengthens connection with Allah", "Brings peace to the heart", "Forgiveness of minor sins"], "reflectionPrompt": "How does taking these few moments for dhikr after prayer affect your spiritual state and focus?"}}
            ]
        }
        self._practice_library_cache = hardcoded_library
        self._practice_library_cache_timestamp = datetime.now()
        return hardcoded_library

    def _load_personalization_rules(self) -> Dict:
        return {
            "profession": {"healthcare": {"preferred_practices": ["dhikr_001", "reflection_001"], "stress_reduction_focus": True}, "teacher": {"preferred_practices": ["study_001", "reflection_002"], "patience_focus": True}},
            "ruqyaFamiliarity": {"expert": {"additional_ruqya_practices": True}, "unaware": {"introduction_to_ruqya_content": True}},
            "culturalBackground": {"south_asian": {"urdu_terminology_option": True}, "western": {"convert_friendly_explanations": True}}
        }

    def _get_default_parameters(self) -> Dict:
        return {'type': 'heart_purification', 'duration': 21, 'timeCommitment': 20, 'primaryLayer': 'qalb', 'secondaryLayers': [], 'ruqyaLevel': 'none', 'communitySupport': True, 'culturalAdaptations': [], 'recommendations': ['Standard Islamic healing practices']}

    def _get_default_content(self) -> Dict:
        return {'title': 'Islamic Healing Journey', 'description': 'A personalized path to spiritual wellness', 'personalizedWelcome': 'Welcome to your healing journey', 'days': []}

    def _get_day_theme(self, day_num: int, total_days: int, primary_layer: str, journey_type: str) -> str:
        journey_template = self.journey_templates.get(journey_type, self.journey_templates.get("heart_purification"))
        available_themes = journey_template.get("themes", ['General Healing Focus'])
        if not available_themes or total_days == 0: return available_themes[0]
        days_per_theme = total_days / len(available_themes)
        theme_index = min((day_num - 1) // max(1, int(days_per_theme)), len(available_themes) - 1)
        return available_themes[theme_index]

    def _get_learning_objective(self, day_num: int, theme: str, primary_layer: str, journey_type: str) -> str:
        journey_template = self.journey_templates.get(journey_type, self.journey_templates.get("heart_purification"))
        default_objectives = journey_template.get("default_objectives", {})
        objective = default_objectives.get(theme, f"Focus on {primary_layer} layer healing within the theme of {theme}.")
        return f"Day {day_num}: {objective}"

    def _get_placeholder_practice(self, practice_type: str, target_layer: str, day_num: int) -> Dict[str, Any]:
        placeholder_id = f"placeholder_{practice_type.lower()}_{day_num}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        title = f"Contemplation on {practice_type.replace('Spotlight', '').replace('Reflection', '')}"
        description = f"A moment for general reflection on {target_layer} related to {practice_type}."
        details = {"originalContentId": placeholder_id}
        if practice_type == "NameOfAllahSpotlight":
            details.update({"name": "Al-Alim", "arabicScript": "العليم", "meaning": "The All-Knowing.", "reflectionPrompt": f"Reflect on Allah's knowledge regarding your {target_layer}."})
        elif practice_type == "QuranicVerseReflection":
            details.update({"arabicText": "لَا يُكَلِّفُ اللَّهُ نَفْسًا إِلَّا وُسْعَهَا...", "translationEn": "Allah does not burden a soul beyond that it can bear...", "reflectionPrompts": [f"How does this verse apply to your {target_layer}?"]})
        elif practice_type == "SunnahPractice":
            details.update({"category": "Spiritual", "instructions": f"Quiet contemplation or a simple act of kindness for your {target_layer}."})
        else:
            details["message"] = f"Engage in a brief, mindful activity focusing on {target_layer}."
        return {"id": placeholder_id, "type": practice_type, "title": title, "description": description, "duration": 5, "layerFocus": target_layer, "difficultyLevel": "beginner", "isPlaceholder": True, "componentDetails": details}

    def _generate_day_practices_v2(self, day_num: int, theme: str, primary_layer_focus: str,
                                   user_profile: Dict, assessment_data: Dict, journey_config: Dict,
                                   previously_used_ids: set,
                                   ongoing_progress_data: Optional[Dict] = None,
                                   layer_feedback_data: Optional[Dict] = None) -> (List[Dict], set):
        daily_components = []
        used_on_this_day_ids = set()

        morning_check_in_details = {"moodScaleMin": 1, "moodScaleMax": 10, "intentionPrompt": "What is your Niyyah for today?"}
        daily_components.append({"id": f"day{day_num}_morning_checkin", "type": "MorningCheckIn", "title": "Morning Check-In & Intention", "duration": 2, "layerFocus": primary_layer_focus, "componentDetails": morning_check_in_details})

        practice_types_to_select = ["NameOfAllahSpotlight", "QuranicVerseReflection", "SunnahPractice"]
        for pt_key in practice_types_to_select:
            selected_template = self.select_practice(
                practice_type_key=pt_key, target_layer=primary_layer_focus, current_theme=theme,
                user_prof=user_profile, assessment=assessment_data, journey_conf=journey_config,
                excluded_ids=previously_used_ids.union(used_on_this_day_ids),
                available_practices=self.practice_library.get(pt_key, []),
                ongoing_progress=ongoing_progress_data, layer_feedback=layer_feedback_data
            )
            if selected_template:
                daily_components.append(self._format_practice(selected_template, primary_layer_focus, pt_key))
                used_on_this_day_ids.add(selected_template['id'])
            else:
                logger.warning(f"No suitable {pt_key} found for day {day_num}, theme {theme}. Using placeholder.")
                daily_components.append(self._get_placeholder_practice(pt_key, primary_layer_focus, day_num))

        journal_prompts = self._generate_reflection_prompts(theme, primary_layer_focus, user_profile, day_num, journey_config.get('duration', 21))
        daily_components.append({"id": f"day{day_num}_journaling", "type": "PersonalReflectionJournaling", "title": "Personal Reflection & Journaling", "duration": 7, "layerFocus": primary_layer_focus, "componentDetails": {"prompts": journal_prompts, "allowVoiceNote": True}})

        return daily_components, used_on_this_day_ids

    def select_practice(self, practice_type_key: str, target_layer: str, current_theme: str,
                        user_prof: Dict, assessment: Dict, journey_conf: Dict,
                        excluded_ids: set, available_practices: List[Dict],
                        ongoing_progress: Optional[Dict] = None,
                        layer_feedback: Optional[Dict] = None) -> Optional[Dict]:
        if not available_practices:
            logger.warning(f"No practices available in library for type: {practice_type_key}")
            return None

        awareness_level = user_prof.get('awarenessLevel', 'symptom_aware')
        severity_score = assessment.get('severityScore', 5)
        target_difficulty = "beginner"
        if awareness_level == 'spiritual_optimizer' or severity_score < 3: target_difficulty = "intermediate"
        elif severity_score >= 7: target_difficulty = "beginner"
        elif awareness_level == 'clinically_aware' or severity_score >= 5: target_difficulty = "intermediate"

        if ongoing_progress and ongoing_progress.get("struggled_difficulty") == target_difficulty:
            if target_difficulty == "intermediate": target_difficulty = "beginner"
            elif target_difficulty == "advanced": target_difficulty = "intermediate"
            logger.info(f"Adjusting target difficulty to {target_difficulty} due to struggle for {practice_type_key}.")

        current_target_layer = target_layer
        if layer_feedback and layer_feedback.get("prefer_layer"):
            current_target_layer = layer_feedback.get("prefer_layer")
            logger.info(f"Adjusting target layer to {current_target_layer} for {practice_type_key} due to feedback.")

        current_excluded_ids = set(excluded_ids)
        if layer_feedback and layer_feedback.get("ineffective_practice_ids"):
            current_excluded_ids.update(layer_feedback.get("ineffective_practice_ids", []))

        scored_candidates = []
        for p in available_practices:
            if p['id'] in current_excluded_ids: continue
            score = 0
            if p.get('baseLayerFocus') == current_target_layer: score += 5
            if any(current_theme.lower() in tag.lower() for tag in p.get('themeTags', [])): score += 3
            if p.get('difficultyLevel') == target_difficulty: score += 2
            elif p.get('difficultyLevel') == "beginner" and target_difficulty == "intermediate": score += 1

            if ongoing_progress and p['id'] in ongoing_progress.get("practice_ratings", {}):
                rating = ongoing_progress["practice_ratings"][p['id']]
                if rating == "helpful": score += 5
                elif rating == "unhelpful": score -= 5

            if score > 0 :
                 scored_candidates.append((score, p))

        if not scored_candidates:
            fallback_candidates = [p for p in available_practices if p['id'] not in current_excluded_ids]
            if fallback_candidates:
                preferred_difficulty_fallbacks = [fc for fc in fallback_candidates if fc.get('difficultyLevel') == target_difficulty]
                if preferred_difficulty_fallbacks:
                    return preferred_difficulty_fallbacks[journey_conf.get('day_num', 1) % len(preferred_difficulty_fallbacks)]
                return fallback_candidates[journey_conf.get('day_num', 1) % len(fallback_candidates)]
            if available_practices: return available_practices[journey_conf.get('day_num', 1) % len(available_practices)]
            return None

        scored_candidates.sort(key=lambda x: x[0], reverse=True)
        return scored_candidates[0][1]

    def _format_practice(self, practice_template: Dict, default_layer: str, practice_type_override: Optional[str] = None) -> Dict:
        practice_type = practice_type_override if practice_type_override else practice_template.get('type', 'dhikr')
        component_details = practice_template.get('componentDetails', {}).copy()
        if 'originalContentId' not in component_details:
            component_details['originalContentId'] = practice_template['id']
        return {
            'id': practice_template['id'], 'type': practice_type,
            'title': practice_template.get('title', "Untitled Practice"),
            'description': practice_template.get('description', "Engage in this beneficial practice."),
            'duration': practice_template.get('duration', 5),
            'layerFocus': practice_template.get('baseLayerFocus', default_layer),
            'difficultyLevel': practice_template.get('difficultyLevel', 'beginner'),
            'componentDetails': component_details
        }

    def _generate_reflection_prompts(self, theme: str, primary_layer: str, user_profile: Dict, day_num: int, total_days: int) -> List[str]:
        prompts = [f"Reflecting on today's theme of '{theme}', how did the practices impact your {primary_layer} layer?", "What was the most significant insight Allah blessed you with today?"]
        if day_num <= total_days // 3: prompts.append("What initial patterns (positive or negative) are you noticing?")
        elif day_num <= (total_days * 2) // 3: prompts.append("How do you feel Allah is working to change your heart or perspective?")
        else: prompts.append("Looking back, what is the most significant lesson you've learned about yourself and your connection with Allah?")
        if user_profile.get('profession') == 'healthcare': prompts.append("As a healthcare professional, how can today's insights help you?")
        if user_profile.get('awarenessLevel') == 'new_muslim': prompts.append("As someone newer to the faith, what aspect of today's practices felt most connecting or challenging?")
        return prompts
    
    def _generate_community_activity(self, day_num: int, theme: str) -> str:
        return f"Share your experience with {theme} in the community discussion"
    
    def _get_progress_milestone(self, day_num: int, total_days: int, theme: str) -> Optional[str]:
        if day_num == 1: return "Journey begun - Foundation set"
        elif day_num == total_days // 2: return "Halfway point - Deepening understanding"
        elif day_num == total_days: return "Journey completed - Integration achieved"
        return None
    
    def _generate_adaptive_content(self, theme: str, user_profile: Dict) -> Dict:
        return {
            'forNewMuslims': f"New Muslim guidance for {theme}",
            'forProfessionals': f"Professional context for {theme}",
            'forRuqyaExperts': f"Advanced ruqya perspective on {theme}"
        }
