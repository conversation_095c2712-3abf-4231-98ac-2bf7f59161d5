"""
AI Service for Feature 1: Understanding Your Inner Landscape (REVISED)
Spiritual landscape analysis and Islamic layer mapping
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
from dataclasses import dataclass
from .simple_symptom_analyzer import simple_symptom_analyzer

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/spiritual-analysis", tags=["Spiritual Analysis"])

@dataclass
class LayerInsight:
    layer: str
    insights: List[str]
    recommendations: List[str]
    islamic_context: str
    severity_score: int

@dataclass
class SpiritualAnalysisResult:
    primary_layer: str
    layer_insights: Dict[str, LayerInsight]
    personalized_message: str
    islamic_insights: List[str]
    educational_content: str
    crisis_level: str
    crisis_indicators: List[str]
    immediate_actions: List[str]
    next_steps: List[str]
    recommended_journey_type: str
    estimated_healing_duration: int
    confidence: float

class SpiritualAnalysisProcessor:
    """
    AI processor for analyzing spiritual landscape using Islamic framework
    """

    def __init__(self):
        self.layer_weights = {
            'jism': 1.0,
            'nafs': 1.2,
            'aql': 1.1,
            'qalb': 1.5,
            'ruh': 1.3
        }

    def analyze_spiritual_landscape(self, assessment_data: Dict[str, Any]) -> SpiritualAnalysisResult:
        try:
            logger.info("Starting spiritual landscape analysis")
            user_profile = assessment_data.get('user_profile', {}) or {}
            physical_data = assessment_data.get('physical_experiences', {}) or {}
            emotional_data = assessment_data.get('emotional_experiences', {}) or {}
            mental_data = assessment_data.get('mental_experiences', {}) or {}
            spiritual_data = assessment_data.get('spiritual_experiences', {}) or {}

            # Consolidate all reflections from different steps if they exist under 'reflections'
            # The mobile app sends userReflection per step, which backend stores in e.g. physicalExperiences.userReflection
            # The main 'reflections' field in assessment_data might be the final overall reflection.
            overall_reflections_dict = assessment_data.get('reflections', {}) or {}

            # Extract step-specific reflections if they are part of the layer data
            # (as per current mobile app submission for each step category)
            physical_reflection = physical_data.get('user_reflection', '') or ''
            emotional_reflection = emotional_data.get('user_reflection', '') or ''
            mental_reflection = mental_data.get('user_reflection', '') or ''
            spiritual_reflection = spiritual_data.get('user_reflection', '') or ''

            # Combine all available reflection texts for a more holistic crisis check
            # and potentially for other general insights if needed.
            all_reflection_texts_for_crisis = {
                "physical": physical_reflection,
                "emotional": emotional_reflection,
                "mental": mental_reflection,
                "spiritual": spiritual_reflection,
                "final": overall_reflections_dict.get('final_reflection', '') # if final reflection is separate
            }

            session_metadata = assessment_data.get('session_metadata', {}) or {}

            # Use the simple text-based analyzer for all layers
            layer_analyses = {
                'jism': simple_symptom_analyzer.analyze_layer('jism', physical_data.get('symptoms', []), physical_data.get('intensity', 'mild')),
                'nafs': simple_symptom_analyzer.analyze_layer('nafs', emotional_data.get('symptoms', []), emotional_data.get('intensity', 'mild')),
                'aql': simple_symptom_analyzer.analyze_layer('aql', mental_data.get('symptoms', []), mental_data.get('intensity', 'mild')),
                'qalb': simple_symptom_analyzer.analyze_layer('qalb', spiritual_data.get('symptoms', []), spiritual_data.get('intensity', 'mild')),
                'ruh': simple_symptom_analyzer.analyze_layer('ruh', spiritual_data.get('symptoms', []), spiritual_data.get('intensity', 'mild'))
            }

            primary_layer = self.identify_primary_layer(layer_analyses)

            crisis_analysis = self.analyze_crisis_indicators(
                physical_data, emotional_data, mental_data, spiritual_data,
                all_reflection_texts_for_crisis, # Pass combined reflections for crisis check
                user_profile
            )

            # Determine user_type once for use in content generation
            user_type = self.determine_user_type(user_profile)

            personalized_message = self.generate_personalized_message(user_profile, layer_analyses, primary_layer, crisis_analysis['level'], user_type) # Pass user_type
            islamic_insights = self.generate_islamic_insights(layer_analyses, primary_layer, user_profile, crisis_analysis['level'], user_type) # Pass user_type
            educational_content = self.generate_educational_content(layer_analyses, primary_layer, user_profile, user_type) # Pass user_type
            next_steps = self.generate_next_steps(layer_analyses, primary_layer, user_profile, crisis_analysis['level'])
            recommended_journey = self.determine_recommended_journey(layer_analyses, primary_layer, user_profile, crisis_analysis['level'])
            healing_duration = self.estimate_healing_duration(layer_analyses, primary_layer, crisis_analysis['level'])
            # Pass all_reflection_texts_for_crisis to calculate_confidence for accurate reflection counting
            confidence = self.calculate_confidence(assessment_data, session_metadata, all_reflection_texts_for_crisis)


            return SpiritualAnalysisResult(
                primary_layer=primary_layer,
                layer_insights=layer_analyses,
                personalized_message=personalized_message,
                islamic_insights=islamic_insights,
                educational_content=educational_content,
                crisis_level=crisis_analysis['level'],
                crisis_indicators=crisis_analysis['indicators'],
                immediate_actions=crisis_analysis['actions'],
                next_steps=next_steps,
                recommended_journey_type=recommended_journey,
                estimated_healing_duration=healing_duration,
                confidence=confidence
            )
        except Exception as e:
            logger.error(f"Error in spiritual_analysis_processor.analyze_spiritual_landscape: {str(e)}", exc_info=True)
            raise # Re-raise to be caught by endpoint error handler

    # --- Jism Layer Analysis (Previously Enhanced) ---
    def analyze_jism_layer(self, physical_data: Dict, reflections: Dict, user_profile: Optional[Dict[str, Any]] = None) -> LayerInsight:
        selected_symptoms = physical_data.get('symptoms', [])
        intensity_str = physical_data.get('intensity', 'mild')
        reflection_text = reflections.get('physical', '') # Expecting specific reflection for this layer

        insights = []
        recommendations = []
        severity_score = 0
        known_jism_symptoms = {
            "symptom_jism_sleep_difficulties": {"text": "Sleep difficulties", "base_sev": 20},
            "symptom_jism_physical_tension": {"text": "Physical tension/headaches", "base_sev": 15},
            "symptom_jism_heart_breathing": {"text": "Heart and breathing issues", "base_sev": 25},
            "symptom_jism_energy_levels": {"text": "Low or erratic energy levels", "base_sev": 20},
            "symptom_jism_digestive_issues": {"text": "Digestive issues", "base_sev": 15},
            "symptom_jism_physical_restlessness": {"text": "Physical restlessness", "base_sev": 10},
            "symptom_jism_unexplained_aches": {"text": "Unexplained aches and pains", "base_sev": 15},
        }
        symptom_details_for_insights = []
        for symptom_id in selected_symptoms:
            if symptom_id in known_jism_symptoms:
                severity_score += known_jism_symptoms[symptom_id]["base_sev"]
                symptom_details_for_insights.append(known_jism_symptoms[symptom_id]["text"])
        if intensity_str == 'moderate': severity_score *= 1.5
        elif intensity_str == 'severe': severity_score *= 2.0
        severity_score = min(max(0, int(round(severity_score))), 100)

        if not symptom_details_for_insights:
            insights.append("Your physical body (Jism) appears relatively calm." if severity_score <= 10 else "You've indicated physical distress without specifying symptoms; reflecting on precise sensations may help.")
        else:
            insights.append(f"Your Jism shows imbalance related to: {', '.join(symptom_details_for_insights[:3])}.")
            if severity_score >= 70: insights.append("These physical issues are significant and warrant urgent attention for your daily life and spiritual practices.")
            elif severity_score >= 40: insights.append("Notable physical discomfort suggests a need for proactive steps towards well-being, an Islamic duty.")
            else: insights.append("Some physical discomfort is noted; gentle adjustments can support your well-being.")
        if "symptom_jism_sleep_difficulties" in selected_symptoms:
            insights.append("Sleep issues deplete energy. The night is for rest to prepare you for the day.")
            recommendations.append("Follow Prophetic sleep hygiene: Wudu, specific Surahs/Du'as, sleep on right. Avoid pre-bed stimulants/heavy meals.")
            if user_profile and user_profile.get('demographics', {}).get('ageRange') in ['51-65', '65+']:
                recommendations.append("For mature individuals, ensure peaceful sleep environment; consult doctor if issues persist.")
        if "symptom_jism_physical_tension" in selected_symptoms:
            insights.append("Physical tension often results from stress. Islam encourages relieving burdens.")
            recommendations.append("Practice gentle stretching. Recite 'La hawla wa la quwwata illa billah' for overwhelm.")
            if user_profile and user_profile.get('professional_context', {}).get('workStressors'):
                 if any(s in user_profile['professional_context']['workStressors'] for s in ['heavy_workload', 'deadlines']):
                    insights.append("Work stress may contribute to tension. Find halal ways to manage pressure.")
                    recommendations.append("Take short breaks for prayer/stretching. Fulfill duties without over-burdening, per Islamic balance.")
        if "symptom_jism_heart_breathing" in selected_symptoms:
            insights.append("Heart/breathing issues can be distressing; consider medical consultation. Spiritually, the heart's state is key.")
            recommendations.append("Practice mindful breathing with Dhikr (SubhanAllah). If frequent/severe, see a doctor.")
        if "symptom_jism_energy_levels" in selected_symptoms:
            insights.append("Energy issues impact Ibadah and duties. Sustaining energy is part of caring for your body's Amanah.")
            recommendations.append("Focus on balanced Sunnah diet (dates, honey). Hydrate. Gentle exercise (walking) is encouraged.")
        if "symptom_jism_digestive_issues" in selected_symptoms:
            insights.append("Digestive issues link to diet and stress. Prophet ﷺ emphasized moderation: 1/3 food, 1/3 drink, 1/3 air.")
            recommendations.append("Eat mindfully (Bismillah). Avoid overeating. Include Prophetic foods (ginger, vinegar).")
        if not recommendations and severity_score > 20: recommendations.append("Adopt general wellness: Sunnah diet, light exercise, rest.")
        elif not recommendations: recommendations.append("Alhamdulillah, maintain physical health for effective worship. Make Du'as for well-being.")
        islamic_context = """The Jism (Physical Body) is an Amanah (trust) from Allah. The Prophet Muhammad ﷺ said: "Your body has a right over you." (Sahih al-Bukhari). Taking care of your physical health through proper diet, rest, and hygiene is not just a worldly matter but a religious obligation. A healthy body facilitates worship and the fulfillment of your duties. Neglecting it can hinder spiritual progress. Physical ailments can also sometimes be a Kaffarah (expiation for sins) or a test from Allah, requiring patience (Sabr) and seeking permissible means of healing."""
        return LayerInsight(layer='jism', insights=insights[:3], recommendations=recommendations[:3], islamic_context=islamic_context, severity_score=severity_score)

    # --- Nafs Layer Analysis (Previously Enhanced) ---
    def analyze_nafs_layer(self, emotional_data: Dict, reflections: Dict, user_profile: Optional[Dict[str, Any]] = None) -> LayerInsight:
        selected_symptom_ids = emotional_data.get('symptoms', [])
        intensity_str = emotional_data.get('intensity', 'mild')
        reflection_text = reflections.get('emotional', '') # Expecting specific reflection
        insights, recommendations, severity_score = [], [], 0
        known_nafs_symptoms = {
            "symptom_nafs_overwhelming_sadness": {"text": "Overwhelming sadness or emptiness", "base_sev": 20},
            "symptom_nafs_frequent_anger": {"text": "Frequent anger or irritability", "base_sev": 18},
            "symptom_nafs_anxiety_worry": {"text": "Anxiety or constant worry", "base_sev": 20},
            "symptom_nafs_shame_guilt": {"text": "Shame or guilt", "base_sev": 15},
            "symptom_nafs_jealousy_resentment": {"text": "Jealousy or resentment", "base_sev": 15},
            "symptom_nafs_emotional_numbness": {"text": "Emotional numbness", "base_sev": 22},
            "symptom_nafs_mood_swings": {"text": "Mood swings", "base_sev": 15},
            "symptom_nafs_fear_judgment": {"text": "Fear of judgment", "base_sev": 15},
        }
        symptom_details_for_insights = [known_nafs_symptoms[sid]["text"] for sid in selected_symptom_ids if sid in known_nafs_symptoms]
        for sid in selected_symptom_ids: severity_score += known_nafs_symptoms.get(sid, {}).get("base_sev", 0)
        if intensity_str == 'moderate': severity_score *= 1.5
        elif intensity_str == 'severe': severity_score *= 2.0
        severity_score = min(max(0, int(round(severity_score))), 100)
        if not symptom_details_for_insights: insights.append("Alhamdulillah, your Nafs appears relatively balanced." if severity_score <=10 else "You've noted emotional distress; identifying specific emotions helps.")
        else:
            insights.append(f"Your Nafs is impacted by: {', '.join(symptom_details_for_insights[:3])}.")
            if severity_score >= 70: insights.append("These emotions significantly affect well-being and connections. Purifying the Nafs is crucial.")
            elif severity_score >= 40: insights.append("Emotional challenges are notable, requiring effort in Tazkiyat an-Nafs.")
            else: insights.append("Some emotional turbulence noted; self-discipline and remembrance can soothe the Nafs.")
        if "symptom_nafs_overwhelming_sadness" in selected_symptom_ids:
            insights.append("Deep sadness veils the heart. Allah is Al-Jabbar (Restorer).")
            recommendations.append("Recite Du'a Yunus. Reflect on past ease after hardship.")
            if user_profile and "recent_major_life_change" in user_profile.get('life_circumstances', {}).get('situations', []):
                insights.append("Sadness may link to life changes. Islamic teachings guide through grief/transitions.")
                recommendations.append("Process changes, seek trusted Muslim support, make Du'a for strength.")
        if "symptom_nafs_frequent_anger" in selected_symptom_ids:
            insights.append("Anger consumes good deeds. Strong is one who controls anger (Hadith).")
            recommendations.append("When angry: A'udhu billah, change posture, Wudu, silence/Dhikr.")
        if "symptom_nafs_anxiety_worry" in selected_symptom_ids:
            insights.append("Anxiety stems from lacking Tawakkul. Remembrance of Allah brings tranquility.")
            recommendations.append("Recite 'Hasbunallahu wa Ni'mal Wakeel'. Focus on your control, leave rest to Allah.")
            if user_profile and ("financial_pressures" in user_profile.get('professional_context', {}).get('workStressors', []) or \
               "financial_difficulties" in user_profile.get('life_circumstances', {}).get('situations', [])):
                insights.append("Financial pressures cause anxiety. Rizq is from Allah; strive and trust.")
                recommendations.append("Du'a for Rizq, Sadaqah, avoid haram income/expenditure.")
        if "symptom_nafs_shame_guilt" in selected_symptom_ids:
            insights.append("Shame/guilt leading to Tawbah is positive. Excessive guilt can be Shaytan's tool for despair.")
            recommendations.append("Sincere Tawbah, resolve not to repeat, hope in Allah's forgiveness. Don't dwell on past sins after repentance.")
        if not recommendations and severity_score > 20: recommendations.append("Daily Muhasabah, beautify Akhlaq with Prophetic examples.")
        elif not recommendations: recommendations.append("Nurture emotional well-being with Shukr and knowledge that purifies Nafs.")
        islamic_context = """The Nafs (Ego/Lower Self) is the seat of desires and emotions. Islam emphasizes Tazkiyat an-Nafs (purification of the self) as a primary goal. Allah says in the Quran (91:9-10): "He has succeeded who purifies it [the Nafs], And he has failed who instills it [with corruption]." Managing emotions like anger, sadness, fear, and controlling base desires are central to this purification process. The struggle against the negative inclinations of the Nafs is considered the 'greater Jihad'."""
        return LayerInsight(layer='nafs', insights=insights[:3], recommendations=recommendations[:3], islamic_context=islamic_context, severity_score=severity_score)

    # --- Aql Layer Analysis (Previously Enhanced) ---
    def analyze_aql_layer(self, mental_data: Dict, reflections: Dict, user_profile: Optional[Dict[str, Any]] = None) -> LayerInsight:
        selected_symptom_ids = mental_data.get('symptoms', [])
        intensity_str = mental_data.get('intensity', 'mild')
        reflection_text = reflections.get('mental', '')
        insights, recommendations, severity_score = [], [], 0
        known_aql_symptoms = {
            "symptom_aql_racing_thoughts": {"text": "Racing thoughts", "base_sev": 20},
            "symptom_aql_constant_worry_future": {"text": "Constant worry about future", "base_sev": 18},
            "symptom_aql_overthinking_past": {"text": "Overthinking past", "base_sev": 15},
            "symptom_aql_difficulty_concentrating": {"text": "Difficulty concentrating", "base_sev": 18},
            "symptom_aql_negative_thoughts": {"text": "Negative thought patterns", "base_sev": 22},
            "symptom_aql_confusion_decisions": {"text": "Confusion about decisions", "base_sev": 15},
            "symptom_aql_intrusive_thoughts": {"text": "Intrusive thoughts", "base_sev": 20},
            "symptom_aql_mental_fog": {"text": "Mental fog", "base_sev": 15},
        }
        symptom_details_for_insights = [known_aql_symptoms[sid]["text"] for sid in selected_symptom_ids if sid in known_aql_symptoms]
        for sid in selected_symptom_ids: severity_score += known_aql_symptoms.get(sid, {}).get("base_sev", 0)
        if intensity_str == 'moderate': severity_score *= 1.5
        elif intensity_str == 'severe': severity_score *= 2.0
        severity_score = min(max(0, int(round(severity_score))), 100)
        if not symptom_details_for_insights: insights.append("Alhamdulillah, your Aql seems clear." if severity_score <=10 else "You've noted mental distress; identifying specific thought patterns helps.")
        else:
            insights.append(f"Your Aql is experiencing disturbances like: {', '.join(symptom_details_for_insights[:3])}.")
            if severity_score >= 70: insights.append("Mental challenges significantly impact clarity/focus. Addressing them Islamically is essential.")
            elif severity_score >= 40: insights.append("Aql disturbances hinder clear thinking. Islamic practices can restore tranquility.")
            else: insights.append("Some mental distractions noted. Regular focus practices can maintain clarity.")
        if "symptom_aql_racing_thoughts" in selected_symptom_ids:
            insights.append("Racing thoughts hinder peace/Salah. Mind can be trained for focus.")
            recommendations.append("Focused Dhikr, Muraqabah (meditation on Allah's creation/attributes).")
        if "symptom_aql_difficulty_concentrating" in selected_symptom_ids:
            insights.append("Concentration issues affect tasks/worship.")
            recommendations.append("Minimize distractions. Before Salah, clear mind, focus intention. Du'a: 'Rabbi zidni ilma'.")
            if user_profile and (user_profile.get('professional_context',{}).get('field','').lower() in ['education','technology','research'] or \
               "academic_work_pressure" in user_profile.get('life_circumstances',{}).get('situations',[])):
                insights.append("Your work/study demands concentration. Islamic principles aid focus.")
                recommendations.append("Start work/study with Bismillah, Du'a for understanding. Breaks for Salah/Dhikr.")
        if "symptom_aql_negative_thoughts" in selected_symptom_ids:
            insights.append("Negative thoughts erode self-worth/hope. Shaytan exploits these.")
            recommendations.append("Challenge with positive Islamic affirmations. Seek refuge from Waswas.")
        if "symptom_aql_intrusive_thoughts" in selected_symptom_ids:
            insights.append("Intrusive thoughts (faith/sacred matters) are distressing but you're not accountable if not acted on. Often tests/Waswas.")
            recommendations.append("Don't dwell. Say 'Amantu billah', seek refuge, distract with Dhikr/activity. Consult scholar if persistent.")
            if user_profile and user_profile.get('ruqya_knowledge',{}).get('level') in ['expert','practitioner','aware']:
                recommendations.append("Given Ruqya familiarity, consider if these align with Waswas patterns needing self-Ruqya.")
        if not recommendations and severity_score > 20: recommendations.append("Protect mind from negativity. Read Quran with understanding, reflect on creation.")
        elif not recommendations: recommendations.append("Cultivate healthy mind: seek beneficial knowledge, Tafakkur.")
        islamic_context = """The Aql (Rational Mind/Intellect) is a precious gift from Allah, distinguishing humans. Islam strongly encourages its use for Tadabbur (deep reflection on the Quran) and Tafakkur (contemplation on creation). A sound Aql leads to stronger Iman (faith) and better decisions. Protecting the mind from doubts, distracting thoughts (Waswas), and negative influences is crucial for both worldly success and spiritual advancement. The Quran frequently asks, "Will you not use reason?" (Afala Ta'qilun)."""
        return LayerInsight(layer='aql', insights=insights[:3], recommendations=recommendations[:3], islamic_context=islamic_context, severity_score=severity_score)

    # --- Qalb Layer Analysis (Previously Enhanced) ---
    def analyze_qalb_layer(self, spiritual_data: Dict, reflections: Dict, user_profile: Optional[Dict[str, Any]] = None) -> LayerInsight:
        all_selected_spiritual_symptoms = spiritual_data.get('symptoms', [])
        intensity_str = spiritual_data.get('intensity', 'mild')
        reflection_text = reflections.get('spiritual', '')
        insights, recommendations, severity_score = [], [], 0
        qalb_symptom_definitions = {
            "symptom_qalb_distant_from_allah": {"text": "Feeling distant from Allah", "base_sev": 25},
            "symptom_qalb_prayers_mechanical": {"text": "Prayers feeling mechanical", "base_sev": 20},
            "symptom_qalb_difficulty_dua": {"text": "Difficulty making sincere Du'a", "base_sev": 15},
            "symptom_qalb_struggling_trust_qadar": {"text": "Struggling to trust Qadar", "base_sev": 18},
            "symptom_qalb_loss_spiritual_motivation": {"text": "Loss of spiritual motivation", "base_sev": 20},
            "symptom_qalb_feeling_unworthy": {"text": "Feeling unworthy of Allah's mercy", "base_sev": 22},
        }
        actual_qalb_symptoms_selected = [sid for sid in all_selected_spiritual_symptoms if sid in qalb_symptom_definitions]
        symptom_details_for_insights = [qalb_symptom_definitions[sid]["text"] for sid in actual_qalb_symptoms_selected]
        for sid in actual_qalb_symptoms_selected: severity_score += qalb_symptom_definitions.get(sid, {}).get("base_sev", 0)
        if intensity_str == 'moderate': severity_score *= 1.5
        elif intensity_str == 'severe': severity_score *= 2.0
        severity_score = min(max(0, int(round(severity_score))), 100)
        if not symptom_details_for_insights: insights.append("Alhamdulillah, your Qalb seems relatively good." if severity_score <=10 else "Spiritual distress noted, but no specific Qalb symptoms selected. Reflect on heart's connection to Allah.")
        else:
            insights.append(f"Your Qalb is experiencing challenges like: {', '.join(symptom_details_for_insights[:2])}.")
            if severity_score >= 75: insights.append("Qalb state is significant concern, requiring immediate attention. Repentance/dedication can bring healing.")
            elif severity_score >= 45: insights.append("Heart needs spiritual nourishment/purification. Consistent worship/remembrance can heal.")
            else: insights.append("Some Qalb disturbances noted. Regular devotion/forgiveness maintain its health.")
        if "symptom_qalb_distant_from_allah" in actual_qalb_symptoms_selected:
            insights.append("Feeling distant from Allah signals heart needs reconnection via Dhikr/obedience.")
            recommendations.append("Increase daily Dhikr. Sincere Du'a for closeness.")
            if user_profile and user_profile.get('life_circumstances', {}).get('islamicJourneyStage') == 'new_muslim':
                insights.append("As new Muslim, connection fluctuates. Be patient, seek knowledge.")
                recommendations.append("Learn Salah/Surah meanings. Seek mentor/community.")
        if "symptom_qalb_prayers_mechanical" in actual_qalb_symptoms_selected:
            insights.append("Mechanical prayers indicate lack of Khushu'. Khushu' is soul of Salah.")
            recommendations.append("Before prayer, remember you're before Allah. Learn meanings. Minimize distractions.")
        if "symptom_qalb_feeling_unworthy" in actual_qalb_symptoms_selected:
            insights.append("Feeling unworthy can be Shaytan's whisper for despair. Allah's mercy is vast.")
            recommendations.append("Reflect on Allah's names (Al-Ghafur, Ar-Rahim, At-Tawwab). Increase Istighfar with hope.")
            if user_profile and "Depression" in user_profile.get('mental_health_awareness', {}).get('conditions', []):
                insights.append("Unworthiness can intensify with depression. Address depression alongside spiritual remedies.")
        if not recommendations and severity_score > 20: recommendations.append("Purify heart: good deeds, avoid major sins, abundant Dhikr.")
        elif not recommendations: recommendations.append("Guard heart: fill with love/fear/hope in Allah.")
        islamic_context = """The Qalb (Spiritual Heart) is central in Islam. The Prophet Muhammad ﷺ said: "Truly in the body there is a morsel of flesh which, if it be sound, all the body is sound and which, if it be diseased, all of body is diseased. Truly it is the heart." (Bukhari & Muslim). A sound heart (Qalb Saleem) is one submissive to Allah, free from shirk, doubt, and spiritual diseases like envy (hasad), arrogance (kibr), and showing off (riya'). Its health is maintained by Iman (faith), Dhikr (remembrance), Tilawah (Quran recitation), and righteous deeds."""
        return LayerInsight(layer='qalb', insights=insights[:3], recommendations=recommendations[:3], islamic_context=islamic_context, severity_score=severity_score)

    # --- Ruh Layer Analysis (Previously Enhanced) ---
    def analyze_ruh_layer(self, spiritual_data: Dict, reflections: Dict, user_profile: Optional[Dict[str, Any]] = None) -> LayerInsight:
        all_selected_spiritual_symptoms = spiritual_data.get('symptoms', [])
        intensity_str = spiritual_data.get('intensity', 'mild')
        reflection_text = reflections.get('spiritual', '')
        insights, recommendations, severity_score = [], [], 0
        ruh_symptom_definitions = {
            "symptom_ruh_questioning_purpose": {"text": "Questioning life's purpose/meaning", "base_sev": 20},
            "symptom_ruh_stranger_world": {"text": "Feeling like a stranger in this world", "base_sev": 15},
            "symptom_ruh_fear_death_afterlife": {"text": "Fear about death or the afterlife", "base_sev": 18},
            "symptom_ruh_yearning_eternal": {"text": "Yearning for something eternal/transcendent", "base_sev": 15},
        }
        actual_ruh_symptoms_selected = [sid for sid in all_selected_spiritual_symptoms if sid in ruh_symptom_definitions]
        symptom_details_for_insights = [ruh_symptom_definitions[sid]["text"] for sid in actual_ruh_symptoms_selected]
        for sid in actual_ruh_symptoms_selected: severity_score += ruh_symptom_definitions.get(sid, {}).get("base_sev", 0)
        if intensity_str == 'moderate': severity_score *= 1.2
        elif intensity_str == 'severe': severity_score *= 1.5
        severity_score = min(max(0, int(round(severity_score))), 100)
        if not symptom_details_for_insights: insights.append("Alhamdulillah, your Ruh seems content or positively yearning." if severity_score <=5 else "Spiritual unease noted; strong Qalb connection often brings peace to Ruh.")
        else:
            insights.append(f"Your Ruh is signaling experiences like: {', '.join(symptom_details_for_insights[:2])}.")
            if severity_score >= 70: insights.append("Profound searching/disturbance at Ruh level. Deep reflection, authentic knowledge, strengthening core connection with Allah needed.")
            elif severity_score >= 40: insights.append("Ruh expresses notable concerns/yearnings. Contemplation, Du'a, understanding Allah's plan bring peace.")
            else: insights.append("Some soul stirrings noted; opportunities for growth/deeper connection if guided well.")
        if "symptom_ruh_questioning_purpose" in actual_ruh_symptoms_selected:
            insights.append("Questioning purpose is Ruh's call for deeper understanding/connection. Islam provides answers.")
            recommendations.append("Reflect on Quran 51:56. Study Tawhid. Contemplate your role as Allah's servant.")
            if user_profile and user_profile.get('life_circumstances', {}).get('islamicJourneyStage') == 'new_muslim':
                insights.append("As new Muslim, questioning is vital for internalizing faith. Embrace discovery.")
                recommendations.append("Seek knowledge from scholars/mentors on Iman/Islam pillars. Read Quran translations with Tafsir.")
        if "symptom_ruh_fear_death_afterlife" in actual_ruh_symptoms_selected:
            insights.append("Fear of death/afterlife is natural; should motivate righteous action & hope, not despair. Akhirah is true home.")
            recommendations.append("Dhikr al-Mawt to motivate good deeds. Study Jannah/Allah's mercy. Du'a for Husn al-Khatimah.")
            if user_profile and user_profile.get('demographics', {}).get('ageRange') in ['51-65', '65+']:
                insights.append("Reflecting on Akhirah is pertinent with age, a reminder to prepare for meeting Allah.")
                recommendations.append("Focus on Sadaqah Jariyah, frequent forgiveness, strengthening family ties.")
        if "symptom_ruh_yearning_eternal" in actual_ruh_symptoms_selected:
            insights.append("Yearning for eternal/transcendent is Ruh's innate longing for Allah/higher realities. Positive sign if channeled well.")
            recommendations.append("Channel yearning into worship: heartfelt Salah, Sujood, sincere Du'a, contemplation of Allah's majesty.")
        if not recommendations and severity_score > 15: recommendations.append("Deep Tafakkur on Allah's signs. Seek knowledge on Ruh's nature/journey.")
        elif not recommendations: recommendations.append("Nurture Ruh: consistent worship connecting to Divine, beneficial knowledge of Creator.")
        islamic_context = """The Ruh (Soul/Spirit) is of Divine origin, a breath from Allah (Quran 15:29, 38:72). It is the essence of human life and consciousness, possessing an innate recognition of its Creator (Fitra). The Ruh naturally yearns for connection with the Divine and finds ultimate peace only in the remembrance of Allah (Quran 13:28: "Verily, in the remembrance of Allah do hearts find rest."). Understanding its nature, purpose, and journey back to Allah is fundamental to Islamic spirituality."""
        return LayerInsight(layer='ruh', insights=insights[:3], recommendations=recommendations[:3], islamic_context=islamic_context, severity_score=severity_score)

    # --- Helper methods for overall diagnosis results ---

    def identify_primary_layer(self, layer_analyses: Dict[str, LayerInsight]) -> str:
        """Identify the most affected layer"""
        max_score = -1.0
        primary_layer = 'none'
        valid_insights_exist = False
        for layer_key, analysis_insight in layer_analyses.items():
            if hasattr(analysis_insight, 'severity_score') and isinstance(analysis_insight.severity_score, (int, float)):
                valid_insights_exist = True
                weighted_score = analysis_insight.severity_score * self.layer_weights.get(layer_key, 1.0)
                if weighted_score > max_score:
                    max_score = weighted_score
                    primary_layer = layer_key
            else:
                logger.warn(f"LayerInsight for {layer_key} is missing or has invalid severity_score: {analysis_insight}")
        if not valid_insights_exist:
            logger.warn("No valid layer insights found to determine primary layer.")
            return 'none'
        if max_score <= 0: # If all weighted scores are zero or less
             primary_layer = 'none'
             logger.info(f"All weighted layer scores are <= 0. Primary layer set to 'none'. Max score: {max_score}")
        return primary_layer

    def analyze_crisis_indicators(self, physical: Dict, emotional: Dict, mental: Dict, spiritual: Dict, reflections: Dict, user_profile: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Analyze for crisis indicators across all layers"""
        crisis_indicators = []
        crisis_level = 'none'
        actions = []
        high_risk_symptom_values = [
            "symptom_nafs_overwhelming_sadness", "symptom_nafs_emotional_numbness",
            "symptom_aql_negative_thoughts", "symptom_aql_intrusive_thoughts",
            "symptom_qalb_distant_from_allah", "symptom_qalb_feeling_unworthy",
            "symptom_ruh_questioning_purpose", "symptom_ruh_fear_death_afterlife"
        ]
        all_selected_symptom_ids = []
        for data in [physical, emotional, mental, spiritual]:
            all_selected_symptom_ids.extend(data.get('symptoms', []))
        high_risk_symptom_count = sum(1 for symptom_id in all_selected_symptom_ids if symptom_id in high_risk_symptom_values)

        all_reflection_text = ""
        if reflections: # reflections is now all_reflection_texts_for_crisis from analyze_spiritual_landscape
            all_reflection_text = ' '.join(str(v).lower() for v in reflections.values() if isinstance(v, str))

        crisis_keywords = [
            'hopeless', 'worthless', 'useless', 'no point', 'give up',
            'suicide', 'kill myself', 'end my life', 'want to die', 'better off dead',
            'harm myself', 'hurt myself', 'self harm', 'cutting', 'cut myself',
            'overdose', 'pills', 'jump', 'hanging', 'gun', 'knife', 'razor', 'blade', 'poison',
            'can\'t go on', 'can\'t take it anymore', 'end the pain', 'no way out', 'trapped',
            'unbearable', 'breaking point', 'losing control', 'losing my mind'
        ]
        crisis_keyword_hits = [keyword for keyword in crisis_keywords if keyword in all_reflection_text]
        crisis_keyword_count = len(crisis_keyword_hits)

        if crisis_keyword_hits:
            crisis_indicators.extend([f"reflection_keyword: {kw}" for kw in crisis_keyword_hits[:3]])

        onboarding_stated_crisis = False
        if user_profile:
            crisis_indicators_data = user_profile.get('crisisIndicators', {}) or {}
            profile_crisis_level = crisis_indicators_data.get('level', 'none') if crisis_indicators_data else 'none'
            if profile_crisis_level in ['high', 'critical']:
                onboarding_stated_crisis = True
                crisis_indicators.append(f"onboarding_crisis_level: {profile_crisis_level}")
            # Example: primary_concern might be set during onboarding if user selected "I'm having a crisis"
            mental_health_data = user_profile.get('mental_health_awareness', {}) or {}
            primary_concern = mental_health_data.get('primary_concern', '') if mental_health_data else ''
            if 'crisis' in primary_concern.lower():
                 onboarding_stated_crisis = True
                 crisis_indicators.append("onboarding_stated_crisis_concern")

        if onboarding_stated_crisis or any(kw in crisis_keyword_hits for kw in ['suicide', 'kill myself', 'end my life', 'harm myself', 'hurt myself']):
            crisis_level = 'critical'
        elif crisis_keyword_count > 0 or high_risk_symptom_count >= 4: # 4 or more high-risk symptoms can indicate high crisis
            crisis_level = 'high'
        elif high_risk_symptom_count >= 2: # 2-3 high-risk symptoms
            crisis_level = 'moderate'
        elif high_risk_symptom_count >= 1 :
             crisis_level = 'low'

        if crisis_level == 'critical':
            crisis_indicators.append('critical_risk_identified_in_assessment')
            actions = ['IMMEDIATE_EMERGENCY_REFERRAL', 'CONTACT_SUPPORT_PERSON_NOW', 'ACTIVATE_SAFETY_PLAN_IMMEDIATELY', 'EMERGENCY_SAKINA_MODE_GUIDANCE']
        elif crisis_level == 'high':
            crisis_indicators.append('high_risk_identified_in_assessment')
            actions = ['URGENT_PROFESSIONAL_CONSULTATION_ADVISED', 'EMERGENCY_SAKINA_MODE', 'ENHANCED_SUPPORT_CHECK_IN_NEEDED', 'INFORM_TRUSTED_INDIVIDUAL']
        elif crisis_level == 'moderate':
            crisis_indicators.append('moderate_risk_identified_in_assessment')
            actions = ['RECOMMEND_CONSULTING_PROFESSIONAL_SUPPORT', 'EXPLORE_QALB_COMMUNITY_SUPPORT', 'FOCUS_ON_SELF_SOOTHING_ISLAMIC_PRACTICES']
        elif crisis_level == 'low':
            crisis_indicators.append('low_risk_identified_in_assessment')
            actions = ['MONITOR_SYMPTOMS_CLOSELY', 'ENGAGE_IN_SELF_CARE_ROUTINE', 'JOURNAL_FEELINGS_REGULARLY']
        else: # crisis_level == 'none'
            actions = ['CONTINUE_STANDARD_WELLNESS_PRACTICES']

        return {'level': crisis_level, 'indicators': list(set(crisis_indicators))[:5], 'actions': actions[:4]}

    def generate_personalized_message(self, user_profile: Optional[Dict[str, Any]], layer_analyses: Dict[str, LayerInsight],
                                    primary_layer: str, crisis_level: str, user_type: str) -> str: # Added user_type
        # user_type = self.determine_user_type(user_profile) # No longer needed here, pass as arg
        primary_layer_name_formatted = "Overall Well-being"
        overall_severity_score = 0

        if primary_layer != 'none' and primary_layer in layer_analyses and hasattr(layer_analyses[primary_layer], 'severity_score'):
            primary_layer_name_formatted = formatLayerName(primary_layer)
            overall_severity_score = layer_analyses[primary_layer].severity_score
        elif layer_analyses:
            valid_scores = [la.severity_score for la in layer_analyses.values() if hasattr(la, 'severity_score')]
            if valid_scores: overall_severity_score = sum(valid_scores) / len(valid_scores)

        if crisis_level in ['high', 'critical']:
            return f"SubhanAllah, it appears you are going through a very challenging time. Please know that Allah is with those who are patient and turn to Him. We strongly advise you to review the immediate actions recommended and seek appropriate support without delay. You are not alone."

        if primary_layer == 'none':
            return f"Alhamdulillah, As-salamu alaykum. Your spiritual assessment indicates a good state of overall balance across the five layers of your being. This is a great blessing from Allah (SWT) and a strong foundation for continued spiritual growth and drawing nearer to Him. Remember to maintain this state with consistent gratitude (Shukr), remembrance (Dhikr), and righteous deeds. May Allah increase you in wellness."

        base_message = f"As-salamu alaykum. Your spiritual assessment highlights the {primary_layer_name_formatted} as an area for primary focus. "

        type_specific_intro = {
            'clinically_aware': "As someone familiar with clinical concepts, you may appreciate how this Islamic framework offers a holistic perspective, integrating the spiritual alongside the psychological. ",
            'ruqya_expert': "Masha'Allah, your expertise in Ruqya will be a valuable asset in understanding and addressing any spiritual dimensions related to this. ",
            'new_muslim': "As you continue your journey in Islam, understanding these aspects of your inner self can be very empowering and help solidify your connection with Allah. ",
            'clinically_aware_spiritual_optimizer': "Your interest in integrating clinical knowledge with Islamic spirituality is commendable. This assessment aims to provide insights that bridge these two valuable perspectives. ",
            'symptom_aware_spiritual_optimizer': "As a leader or someone seeking deeper traditional understanding, this assessment can help map classical Islamic concepts of inner well-being to contemporary experiences. "
        }.get(user_type, "This understanding is a step towards healing and drawing closer to Allah. ")

        base_message += type_specific_intro

        if overall_severity_score >= 70:
            base_message += "The challenges identified in this area appear significant, suggesting that dedicated attention and sincere effort, with reliance on Allah, can lead to profound positive changes, Insha'Allah. Remember Allah's promise: 'With hardship will be ease.' (Quran 94:6)."
        elif overall_severity_score >= 40:
            base_message += "Focusing on this area can greatly enhance your spiritual and overall well-being. Islamic teachings offer rich guidance for navigating these challenges."
        else:
            base_message += "While some imbalances are noted, they appear manageable with consistent Islamic practices. Even small refinements can lead to greater peace and connection."

        return base_message

    def generate_islamic_insights(self, layer_analyses: Dict[str, LayerInsight], primary_layer: str,
                                user_profile: Optional[Dict[str, Any]], crisis_level: str, user_type: str) -> List[str]: # Added user_type
        insights = []
        # user_type = self.determine_user_type(user_profile) # No longer needed here

        if crisis_level in ['high', 'critical']:
            insights.append("In times of intense distress, turning to Allah with wholehearted Du'a and clinging to Sabr (beautiful patience) are your strongest shields and sources of aid.")

        if primary_layer != 'none' and primary_layer in layer_analyses:
            primary_layer_name_formatted = formatLayerName(primary_layer)
            insights.append(f"The state of your {primary_layer_name_formatted} layer often reflects common human experiences for which Islamic teachings provide profound guidance and remedies.")
            if primary_layer == 'qalb': insights.append("The Prophet ﷺ emphasized the Qalb's (heart) centrality: its soundness leads to the soundness of the entire being.")
            elif primary_layer == 'nafs': insights.append("Tazkiyat an-Nafs (purification of the self) is a vital lifelong journey for a believer, leading to spiritual success.")
            elif primary_layer == 'aql': insights.append("A sound Aql (intellect) guided by revelation leads to deeper Iman and wisdom in navigating life.")
        else:
            insights.append("Maintaining balance across all spiritual layers is a sign of holistic well-being ('Afiyah). Express gratitude (Shukr) for this blessing and strive to preserve it.")

        # Generic insights adaptable by all
        insights.extend([
            "Remember that Allah (SWT) does not burden a soul beyond what it can bear (Quran 2:286). Your struggles are within your capacity to navigate with His help.",
            "Every challenge faced with patience and seeking Allah's pleasure can be a means of purification and drawing closer to Him.",
            "Seeking beneficial knowledge ('Ilm an-Nafi') about your faith and your inner self is a continuous act of worship."
        ])

        # User-type specific insights
        if user_type == 'new_muslim':
            insights.append("As you grow in Islam, remember that Allah (SWT) is Ar-Rahman (The Most Gracious) and appreciates every sincere step you take on this path. Your journey of learning and growth is valued.")
        elif user_type in ['clinically_aware', 'clinically_aware_spiritual_optimizer']:
            insights.append("Reflecting on how these Islamic concepts of inner layers complement or expand upon clinical psychological models can enrich both personal understanding and professional practice.")
        elif user_type in ['ruqya_expert', 'ruqya_practitioner']:
            insights.append("Your knowledge of Ruqya provides a strong foundation for addressing spiritual vulnerabilities. Consistent personal 'ibadah and self-purification are also key components of spiritual fortification.")
        elif user_type == 'symptom_aware_spiritual_optimizer':
             insights.append("Understanding the spiritual states of those you guide through this five-layer lens can help in providing more holistic and traditionally-rooted counsel for contemporary issues.")


        overall_severity_score = 0
        if primary_layer != 'none' and primary_layer in layer_analyses and hasattr(layer_analyses[primary_layer], 'severity_score'):
            overall_severity_score = layer_analyses[primary_layer].severity_score
        elif layer_analyses: # Calculate average if primary layer is 'none' or invalid but analyses exist
            valid_scores = [la.severity_score for la in layer_analyses.values() if hasattr(la, 'severity_score') and isinstance(la.severity_score, (int, float))]
            if valid_scores: overall_severity_score = sum(valid_scores) / len(valid_scores)

        if overall_severity_score >= 60 and crisis_level not in ['high', 'critical']:
            insights.append("When facing significant inner challenges, consistent Du'a, Istighfar (seeking forgiveness), and acts of Sadaqah (charity) can open doors to relief and healing.")

        return list(dict.fromkeys(insights))[:5] # Cap at 5 unique insights to keep it concise

    def generate_educational_content(self, layer_analyses: Dict[str, LayerInsight], primary_layer: str,
                                   user_profile: Optional[Dict[str, Any]], user_type: str) -> str: # Added user_type
        # user_type = self.determine_user_type(user_profile) # No longer needed here

        primary_layer_name_formatted = "your overall spiritual well-being"
        primary_context = "Islam teaches a holistic approach to well-being, encompassing all aspects of your being. Each layer influences the others, and nurturing all of them leads to comprehensive wellness and closeness to Allah."
        if primary_layer != 'none' and primary_layer in layer_analyses and hasattr(layer_analyses[primary_layer], 'islamic_context'):
            primary_layer_name_formatted = formatLayerName(primary_layer)
            primary_context = layer_analyses[primary_layer].islamic_context

        five_layer_intro = ""
        if user_type == 'new_muslim':
            five_layer_intro = """
            Welcome to understanding your inner self through an Islamic lens! Islam teaches us that Allah created humans with five interconnected dimensions:
            🤲 Jism (Physical Body): Your body is an amanah (trust) from Allah, take good care of it.
            😤 Nafs (Ego/Lower Self): This is the part of you with desires and emotions, which we strive to purify.
            🧠 Aql (Rational Mind): Your mind, a tool for thinking, learning, and understanding Allah's signs.
            💖 Qalb (Spiritual Heart): The center of your faith and your connection with Allah.
            ✨ Ruh (Soul): Your spiritual essence, which yearns for its Creator.
            When one layer is struggling, it can affect others. We'll explore this together.
            """
        elif user_type in ['clinically_aware', 'clinically_aware_spiritual_optimizer']:
            five_layer_intro = """
            Islam's holistic model describes five interconnected dimensions of human experience, which can be understood in conjunction with psychological frameworks:
            🤲 Jism (Physical Body): The biological and physiological foundation of our being.
            😤 Nafs (Psyche/Self): Encompasses emotions, egoic drives, and the lower self requiring discipline (Tazkiyah).
            🧠 Aql (Cognitive Mind): Our faculty for reason, intellect, and processing information, which finds peace in remembrance and knowledge.
            💖 Qalb (Spiritual Heart): The core of our Iman (faith), intuitive perception, and locus of our direct relationship with Allah.
            ✨ Ruh (Soul/Spirit): Our transcendent spiritual essence, the Divine breath, which inherently yearns for connection with its Creator.
            These layers interact dynamically, influencing overall psychospiritual well-being.
            """
        elif user_type in ['ruqya_expert', 'ruqya_practitioner', 'symptom_aware_spiritual_optimizer']:
            five_layer_intro = """
            As you may appreciate from a traditional or leadership perspective, Islamic understanding of the self is deeply nuanced, often conceptualized through several interconnected layers:
            🤲 Jism (Physical Body): The vessel for our earthly journey, requiring care as an Amanah.
            😤 Nafs (Ego/Lower Self): The seat of desires and emotions, the primary arena for self-purification (Tazkiyat an-Nafs).
            🧠 Aql (Rational Mind/Intellect): The faculty for discernment and understanding, guided by Divine revelation.
            💖 Qalb (Spiritual Heart): Considered the king of the organs, its state (soundness or disease) determines one's spiritual reality.
            ✨ Ruh (Soul/Spirit): The Divine connection, the core of our being that seeks transcendence and return to Allah.
            Understanding manifestations across these layers can provide comprehensive diagnostic insights.
            """
        else: # Default for symptom_aware and any other types
            five_layer_intro = """
            Islam teaches us that Allah created humans with five interconnected layers:
            🤲 Jism (Physical Body): Your body is an amanah (trust) from Allah. Its health supports your worship and daily life.
            😤 Nafs (Ego/Lower Self): This is the seat of your desires and emotions, requiring continuous purification (Tazkiyah).
            🧠 Aql (Rational Mind): Your intellect, a gift for understanding, reflection, and making sound judgments guided by faith.
            💖 Qalb (Spiritual Heart): The core of your Iman (faith), spiritual perception, and direct connection with Allah.
            ✨ Ruh (Soul): Your divine essence, the breath from Allah, which naturally yearns for its Creator and ultimate purpose.
            """

        concluding_paragraph = "By understanding these layers and addressing imbalances with Islamic principles, you can achieve greater peace and closeness to Allah."
        if user_type in ['clinically_aware', 'clinically_aware_spiritual_optimizer']:
            concluding_paragraph = "Integrating this Islamic framework with an understanding of psychospiritual dynamics can offer a comprehensive path to holistic well-being and a deeper connection with Allah."
        elif user_type == 'new_muslim':
            concluding_paragraph = "Learning about these layers and applying simple Islamic practices can greatly support your journey to inner peace and a stronger connection with Allah."


        return f"""
        Understanding Your Inner Landscape through the Five Layers:
        {five_layer_intro}

        These layers are deeply interconnected. An imbalance in one often influences the others. Your assessment particularly highlighted the {primary_layer_name_formatted}.
        Islamic Context for {primary_layer_name_formatted}:
        {primary_context}

        {concluding_paragraph}
        """

    def generate_next_steps(self, layer_analyses: Dict[str, LayerInsight], primary_layer: str, user_profile: Optional[Dict[str, Any]], crisis_level: str) -> List[str]:
        next_steps = []

        if crisis_level in ['high', 'critical']:
            next_steps.append("Urgently review and act upon the 'Immediate Actions' recommended for your current state of crisis.")
            next_steps.append("Seek immediate support from a trusted individual or professional as advised.")

        if primary_layer != 'none' and primary_layer in layer_analyses:
            primary_recs = layer_analyses[primary_layer].recommendations
            next_steps.append(f"Begin implementing the top 1-2 recommendations for your {formatLayerName(primary_layer)} layer to start your healing.")
            if primary_recs: next_steps.append(primary_recs[0]) # Add first specific recommendation
        else:
            next_steps.append("Focus on general practices that maintain overall spiritual balance and well-being, like consistent Dhikr and Du'a.")

        next_steps.extend([
            "Explore the 'Recommended Healing Journey' tailored to your assessment results.",
            "Utilize the Knowledge Hub within the app to learn more about the concepts related to your assessment.",
            "Use the Healing Journal to track your experiences, reflections, and progress on this journey.",
            "Consider connecting with the Qalb Community for shared support and motivation, if you feel ready."
        ])

        # Add a step about professional help if severity is high, but not in critical crisis (as that's more direct)
        overall_severity_score = layer_analyses[primary_layer].severity_score if primary_layer != 'none' and primary_layer in layer_analyses else 0
        if overall_severity_score >= 70 and crisis_level not in ['high', 'critical']:
            next_steps.append("Given the significance of your challenges, consider consulting with a trusted Islamic scholar or a Muslim mental health professional for personalized guidance.")

        return list(dict.fromkeys(next_steps))[:5]


    def determine_recommended_journey(self, layer_analyses: Dict[str, LayerInsight], primary_layer: str,
                                    user_profile: Optional[Dict[str, Any]], crisis_level: str) -> str:
        user_type = self.determine_user_type(user_profile)

        if crisis_level in ['high', 'critical']:
            return "Crisis Intervention & Stabilization Support"
        if primary_layer == 'none':
            return "Spiritual Growth & Maintenance Journey"

        severity_score = layer_analyses[primary_layer].severity_score if primary_layer in layer_analyses else 30

        # More nuanced journey recommendations
        if primary_layer == 'qalb':
            if severity_score >= 60: return "Intensive Heart Purification (Qalb) Journey"
            return "Qalb Nourishment & Connection Journey"
        elif primary_layer == 'ruh':
            if severity_score >= 60: return "Deepening Purpose & Soul Connection (Ruh) Journey"
            return "Exploring Purpose & Transcendence (Ruh) Journey"
        elif primary_layer == 'nafs':
            if severity_score >= 60: return "Advanced Nafs Purification & Emotional Mastery Journey"
            return "Emotional Regulation & Nafs Discipline Journey"
        elif primary_layer == 'aql':
            if severity_score >= 60: return "Strengthening Intellect & Overcoming Waswas (Aql) Journey"
            return "Mental Clarity & Islamic Focus (Aql) Journey"
        elif primary_layer == 'jism':
            if severity_score >= 60: return "Holistic Physical & Spiritual Wellness (Jism) Journey"
            return "Sunnah-Based Physical Well-being (Jism) Journey"

        # Fallback based on user type if primary layer is unclear or mild
        if user_type == 'new_muslim': return "Foundations of Islamic Wellness for New Muslims"
        if user_type == 'ruqya_expert': return "Advanced Ruqya & Spiritual Fortification Practice"

        return "Comprehensive Islamic Healing Journey"


    def estimate_healing_duration(self, layer_analyses: Dict[str, LayerInsight], primary_layer: str, crisis_level: str) -> int:
        if crisis_level in ['high', 'critical']: return 7 # Initial crisis support duration
        if primary_layer == 'none': return 21 # Maintenance/growth

        if primary_layer not in layer_analyses: return 30 # Default

        severity_score = layer_analyses[primary_layer].severity_score
        secondary_concerns = sum(1 for layer_key, la in layer_analyses.items() if layer_key != primary_layer and la.severity_score >= 45) # Stricter threshold for secondary affecting duration

        duration_map = {
            (80, 101): 90,  # Severe
            (60, 80): 60,   # Moderate-Severe
            (40, 60): 40,   # Moderate
            (0, 40): 21     # Mild
        }
        duration = 21 # Default
        for (min_sev, max_sev), dur_val in duration_map.items():
            if min_sev <= severity_score < max_sev:
                duration = dur_val
                break

        duration += secondary_concerns * 14 # Add two weeks for each significant secondary concern
        return min(max(duration, 14), 120) # Ensure it's between 2 weeks and ~4 months

    def calculate_confidence(self, assessment_data: Dict, session_metadata: Dict, reflections_data: Dict) -> float:
        base_confidence = 0.75 # Start a bit lower for rule-based system

        total_symptoms = 0
        for category_key in ['physicalExperiences', 'emotionalExperiences', 'mentalExperiences', 'spiritualExperiences']:
            category_data = assessment_data.get(category_key, {})
            if isinstance(category_data, dict):
                total_symptoms += len(category_data.get('symptoms', []))

        if total_symptoms >= 12: base_confidence += 0.10
        elif total_symptoms >= 6: base_confidence += 0.05

        # Check if specific reflections for layers were provided
        num_reflections = 0
        # reflections_data is now all_reflection_texts_for_crisis which includes step-specific and final reflections
        for reflection_text in reflections_data.values():
            if isinstance(reflection_text, str) and reflection_text.strip():
                num_reflections +=1

        # Adjust bonus based on number of filled reflection fields. Max 5 (4 layers + 1 final)
        if num_reflections >= 4:  # e.g., at least 4 out of 5 reflection types filled
            base_confidence += 0.07
        elif num_reflections >= 2: # e.g., at least 2 reflection types filled
            base_confidence += 0.03

        total_time_seconds = session_metadata.get('totalTimeSpent', 0)
        if total_time_seconds >= 900:  # 15 minutes
            base_confidence += 0.05

        return min(0.95, round(base_confidence, 2))

    def determine_user_type(self, user_profile: Optional[Dict[str, Any]]) -> str:
        if not user_profile: return 'symptom_aware' # Default if profile is None or empty

        # Check for more specific optimizer types first
        spiritual_optimizer = user_profile.get('spiritualOptimizer', {})
        if isinstance(spiritual_optimizer, dict):
            optimizer_type = spiritual_optimizer.get('type')
            if optimizer_type == 'clinical_integration': return 'clinically_aware_spiritual_optimizer'
            if optimizer_type == 'traditional_bridge': return 'symptom_aware_spiritual_optimizer'

        mental_health_awareness = user_profile.get('mentalHealthAwareness', {})
        if isinstance(mental_health_awareness, dict):
            if mental_health_awareness.get('level') == 'clinically_aware': return 'clinically_aware'

        ruqya_knowledge = user_profile.get('ruqyaKnowledge', {})
        if isinstance(ruqya_knowledge, dict):
            if ruqya_knowledge.get('level') == 'expert': return 'ruqya_expert'
            if ruqya_knowledge.get('level') == 'practitioner': return 'ruqya_practitioner'


        life_circumstances = user_profile.get('lifeCircumstances', {})
        if isinstance(life_circumstances, dict):
            situations = life_circumstances.get('situations', [])
            if isinstance(situations, list) and 'new_muslim_within_2_years' in situations: # Check actual value if it's specific
                 return 'new_muslim'
            islamic_journey_stage = life_circumstances.get('islamicJourneyStage')
            if islamic_journey_stage == 'new_muslim': return 'new_muslim' # Fallback if specific situation key isn't there

        return 'symptom_aware' # Default

# Helper for formatting layer names, if not already available globally
def formatLayerName(layer_key: str) -> str:
    return layer_key.replace('_', ' ').replace('qalb ruh', 'Spiritual & Soul').title()

# Global instance
spiritual_analysis_processor = SpiritualAnalysisProcessor()