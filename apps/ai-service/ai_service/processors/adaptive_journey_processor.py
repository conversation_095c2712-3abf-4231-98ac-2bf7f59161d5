"""
Adaptive Journey Processor
Handles adaptive recommendations and adjustments for personalized healing journeys.
"""
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class AdaptiveJourneyProcessor:
    def __init__(self, practice_library: Optional[Dict[str, List[Dict[str, Any]]]] = None):
        """
        Initializes the AdaptiveJourneyProcessor.

        Args:
            practice_library: A dictionary representing the practice library,
                              similar to the one in JourneyGenerationProcessor.
                              This is needed for suggesting practice variations.
        """
        # Simplified internal representation or direct use if structure is identical
        self.practice_library = practice_library if practice_library else self._load_default_practice_library_stub()
        self.motivational_messages_db = self._load_motivational_messages_stub()

    def _load_default_practice_library_stub(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Loads a stub of the practice library.
        In a real scenario, this might be shared or passed from a central content service.
        """
        # This is a simplified stub. Ideally, it would have access to the same
        # rich library as JourneyGenerationProcessor.
        return {
            "NameOfAllahSpotlight": [
                {"id": "noa_001", "title": "<PERSON><PERSON><PERSON><PERSON>", "themeTags": ["mercy", "hope"]},
                {"id": "noa_002", "title": "As-Salam", "themeTags": ["peace", "tranquility"]},
                {"id": "noa_004", "title": "Al-Wadud (The Most Loving)", "themeTags": ["love", "affection"]},
            ],
            "QuranicVerseReflection": [
                {"id": "qvr_001", "title": "Al-Fatihah", "themeTags": ["guidance", "mercy"]},
                {"id": "qvr_002", "title": "Ayat al-Kursi", "themeTags": ["sovereignty", "protection"]},
                {"id": "qvr_004", "title": "Verse on Hope (e.g., Quran 39:53)", "themeTags": ["hope", "mercy", "forgiveness"]},
            ],
            "SunnahPractice": [
                {"id": "sp_001", "title": "Mindful Wudu", "themeTags": ["purification", "mindfulness"]},
                {"id": "sp_002", "title": "Smiling is Charity", "themeTags": ["kindness", "social"]},
                {"id": "sp_004", "title": "Short Gratitude Exercise (Shukr)", "themeTags": ["gratitude", "positive_emotions"]},
            ]
        }

    def _load_motivational_messages_stub(self) -> Dict[str, List[str]]:
        """
        Loads a stub for motivational messages.
        In a real system, this would come from a database or a more dynamic source.
        """
        return {
            "encouragement_after_difficulty": [
                "It's okay to have challenging days. Your effort is valued. May Allah grant you ease.",
                "Remember, with hardship comes ease. Keep turning to Allah.",
                "Be gentle with yourself. Every step, no matter how small, is progress on your path to healing."
            ],
            "general_encouragement": [
                "Masha'Allah, keep up your sincere efforts. Allah sees your striving.",
                "May your journey be filled with light and guidance. You are doing well.",
                "Continue to nurture your connection with Allah. He is always near."
            ],
            "reinforce_positive_progress": [
                "Alhamdulillah! It's wonderful to see you connecting well with your practices.",
                "Your progress is a testament to your dedication. May Allah increase you in good.",
                "Keep building on this positive momentum. You are on a blessed path."
            ]
            # Add more types and messages as needed
        }

    def get_motivational_message(self, message_type: str, user_name: Optional[str] = None) -> str:
        """
        Selects a motivational message of a given type.
        Placeholder for what could be a more dynamic selection logic.
        """
        messages = self.motivational_messages_db.get(message_type, self.motivational_messages_db.get("general_encouragement", ["Keep striving."]))
        # Simple rotation for now, ensuring not to fail if list is empty (though stub guarantees content)
        if not messages: # Should not happen with current stub
            messages = self.motivational_messages_db.get("general_encouragement", ["Keep striving."])

        selected_message = messages[datetime.now().microsecond % len(messages)] # Use microsecond for better rotation

        greeting = f"Dear {user_name}, " if user_name else ""
        return f"{greeting}{selected_message}"

    def process_adaptive_recommendations(self, user_id: str, journey_id: str, progress_data: Dict[str, Any], context: str) -> Dict[str, Any]:
        """
        Processes progress data and generates adaptive recommendations and adjustments.
        This method contains the core logic moved from main.py's endpoint.
        """
        logger.info(f"Processing adaptive recommendations for user {user_id}, journey {journey_id}, context: {context}")

        recommendations_text: List[str] = []
        adjustments_payload: List[Dict[str, Any]] = []
        reasoning_parts: List[str] = [f"Analysis of progress for {context}:"]
        user_name = progress_data.get("userName") # Assuming userName might be in progress_data

        # --- Historical Progress Data Integration Placeholder ---
        # TODO: This section needs actual implementation to fetch and analyze historical data.
        # For now, we'll mock a potential outcome of such analysis.
        # Example: historical_progress could be a list of past progress entries.
        # historical_progress_data = kwargs.get("historical_progress_data") # Or fetched via a method

        # Mocked trend analysis result (replace with actual analysis)
        mock_mood_trend = "stable" # Possible values: "stable", "improving", "declining"
        # if historical_progress_data and len(historical_progress_data) > 2:
        #     # Dummy logic: if last 3 moods are consistently low, trend is declining
        #     # Real logic would be more sophisticated (e.g., averaging, checking slope)
        #     recent_moods_after = [p.get('moodAfter') for p in historical_progress_data[-3:] if p.get('moodAfter') is not None]
        #     if len(recent_moods_after) == 3 and all(m <= 3 for m in recent_moods_after): # Assuming 1-10 scale
        #         mock_mood_trend = "declining"
        #     elif len(recent_moods_after) == 3 and all(m >= 7 for m in recent_moods_after):
        #         mock_mood_trend = "improving"

        if mock_mood_trend == "declining":
            reasoning_parts.append("Trend analysis suggests a period of declining mood.")
            recommendations_text.append(
                self.get_motivational_message("encouragement_after_difficulty", user_name) +
                " We've noticed your mood trend has been a bit lower recently. Remember, every effort is seen by Allah. Consider if there are any external factors affecting you, and don't hesitate to seek support if needed."
            )
            adjustments_payload.append({
                'type': 'WELLBEING_CHECK_SUGGESTION',
                'message': 'It might be helpful to reflect on what could be contributing to this feeling, or to discuss it with someone you trust. We can also try adjusting the journey focus if you like.',
                'details': {'suggested_action': 'journal_on_feelings', 'offer_support_options': True},
                'reason': 'Declining mood trend observed over recent period.'
            })
            # Potentially make more significant adjustments if trend is strongly negative
            adjustments_payload.append({
                'type': 'CONTENT_THEME_ADJUSTMENT',
                'preferredThemes': ['hope', 'mercy', 'Allahs_nearness'],
                'intensity': 'significant',
                'reason': 'Addressing declining mood trend with supportive themes.'
            })
        elif mock_mood_trend == "improving":
            reasoning_parts.append("Trend analysis shows your mood has been improving. Alhamdulillah!")
            # Could add a specific motivational message for positive trends
            # recommendations_text.append(self.get_motivational_message("reinforce_positive_trend", user_name))


        latest_progress = progress_data # Current logic primarily uses latest_progress

        overall_rating = latest_progress.get('overallRating')
        mood_before = latest_progress.get('moodBefore')
        mood_after = latest_progress.get('moodAfter')
        challenges_faced = latest_progress.get('challengesFaced', "")
        practice_specific_feedback = latest_progress.get('practiceSpecificFeedback', [])

        if overall_rating is not None and overall_rating <= 2:
            recommendations_text.append(self.get_motivational_message("encouragement_after_difficulty", user_name))
            adjustments_payload.append({'type': 'DIFFICULTY_ADJUSTMENT', 'value': 'decrease', 'reason': 'Low overall satisfaction.'})
            # The 'ADD_MOTIVATIONAL_MESSAGE' adjustment signals the *intent* and *type* of message.
            # The actual message text would be resolved by the system (e.g., backend) using this messageType
            # against a dynamic source (database, config, dedicated message service).
            # The self.get_motivational_message() is used here for immediate recommendations_text,
            # but the backend would use its own resolution for the adjustment.
            adjustments_payload.append({'type': 'ADD_MOTIVATIONAL_MESSAGE', 'messageType': 'encouragement_after_difficulty', 'reason': 'Low overall satisfaction.'})
            reasoning_parts.append("Low overall satisfaction triggered difficulty reduction and motivational message.")

        if mood_after is not None:
            mood_change = (mood_after - mood_before) if mood_before is not None else None
            if mood_after <= 3: # Low mood
                recommendations_text.append(f"{user_name or 'Remember'}, it's important to be gentle with yourself. Perhaps focusing on content that brings comfort and hope would be beneficial.")
                adjustments_payload.append({'type': 'CONTENT_THEME_ADJUSTMENT', 'preferredThemes': ['hope', 'mercy', 'sakinah'], 'reason': 'Low mood after practices.'})
                reasoning_parts.append("Low mood after practices prompted a shift towards comforting themes.")
            elif mood_change is not None and mood_change < -2: # Significant drop
                 recommendations_text.append(f"It appears today's practices may have felt heavy. We'll aim for content that feels more uplifting, {user_name or 'inshaAllah'}.")
                 adjustments_payload.append({'type': 'CONTENT_THEME_ADJUSTMENT', 'preferredThemes': ['joy_in_islam', 'gratitude'], 'reason': 'Significant mood decrease.'})
                 reasoning_parts.append("Significant mood drop led to suggesting uplifting themes.")

        if challenges_faced:
            if "time" in challenges_faced.lower() or "busy" in challenges_faced.lower():
                recommendations_text.append("Finding time can be a challenge. Perhaps shorter practices or integrating them into your routine could help.")
                adjustments_payload.append({'type': 'PRACTICE_DURATION_ADJUSTMENT', 'value': 'decrease_by_percentage', 'percentage': 20, 'reason': 'Time management challenges.'})
                reasoning_parts.append("Time challenges reported, suggesting duration reduction.")
            if "understanding" in challenges_faced.lower() or "difficult to grasp" in challenges_faced.lower():
                recommendations_text.append("It's okay if some concepts take time. We can focus on foundational content.")
                adjustments_payload.append({'type': 'CONTENT_COMPLEXITY_ADJUSTMENT', 'value': 'foundational', 'reason': 'Difficulty understanding.'})
                reasoning_parts.append("Difficulty understanding content led to suggesting foundational topics.")

        for pf in practice_specific_feedback:
            pf_rating = pf.get('rating')
            pf_id = pf.get('practiceId')
            pf_type = pf.get('practiceType') # Assuming this is provided or can be inferred

            if pf_rating is not None and pf_rating <= 2:
                reasoning_parts.append(f"Low rating on practice {pf_id} ({pf_type}) triggered a variation request.")
                suggestion_text = f"It seems '{pf.get('practiceTitle', pf_id)}' wasn't as helpful. We'll try to offer alternatives."

                alternative_suggestion = self._suggest_alternative_for_practice(
                    original_practice_id=pf_id,
                    original_practice_type=pf_type,
                    original_theme=pf.get('practiceTheme') # Assuming practiceTheme is part of feedback item
                )

                variation_payload = {
                    'type': 'PRACTICE_VARIATION_REQUEST',
                    'targetPracticeId': pf_id,
                    'targetPracticeType': pf_type, # This is the type of the disliked practice
                    'reason': f'Low rating for {pf_id}.'
                }
                if alternative_suggestion:
                    variation_payload.update(alternative_suggestion) # Adds suggested_alternative_type, suggested_alternative_theme
                    alt_type_display = alternative_suggestion.get('suggested_alternative_type', pf_type)
                    alt_theme_display = alternative_suggestion.get('suggested_alternative_theme', 'a different aspect')
                    suggestion_text += f" Perhaps a practice focusing on '{alt_theme_display}' from the '{alt_type_display}' category would be more suitable next time."
                else:
                     suggestion_text += " We will try to vary the content for this type of practice in the future."

                recommendations_text.append(suggestion_text)
                adjustments_payload.append(variation_payload)

        if overall_rating is not None and overall_rating >= 4.5: # High rating
            recommendations_text.append(self.get_motivational_message("reinforce_positive_progress", user_name))
            adjustments_payload.append({'type': 'CONTENT_DEPTH_ADJUSTMENT', 'value': 'increase', 'reason': 'High satisfaction.'})
            reasoning_parts.append("High overall satisfaction suggests readiness for deeper content.")

        if not recommendations_text and not adjustments_payload: # If no other specific recommendations were added
            # Check if there was only a positive trend identified, and add specific message for it
            if mock_mood_trend == "improving" and "Trend analysis shows your mood has been improving. Alhamdulillah!" in reasoning_parts:
                 recommendations_text.append(self.get_motivational_message("reinforce_positive_progress", user_name) + " Keep up the great work!")
            else: # General encouragement if nothing else was triggered
                recommendations_text.append(self.get_motivational_message("general_encouragement", user_name))

            if not reasoning_parts or reasoning_parts == [f"Analysis of progress for {context}:"]: # Avoid duplicating if only trend was logged
                 reasoning_parts.append("Current progress stable. Standard encouragement.")


        final_recommendations = list(dict.fromkeys(recommendations_text)) # Unique recommendations

        logger.info(f"Generated adaptive response for user {user_id}: Recs: {final_recommendations}, Adjusts: {adjustments_payload}")
        return {
            "recommendations": final_recommendations,
            "adjustments": adjustments_payload,
            "reasoning": " ".join(reasoning_parts)
        }

    def _suggest_alternative_for_practice(self, original_practice_id: str, original_practice_type: Optional[str], original_theme: Optional[str]) -> Optional[Dict[str, str]]:
        """
        Suggests an alternative type or theme for a disliked practice, using the practice_library stub.
        """
        if not original_practice_type:
            return None

        # Try to find an alternative of the same type but different theme
        if original_practice_type in self.practice_library:
            for practice in self.practice_library[original_practice_type]:
                if practice['id'] == original_practice_id: continue # Skip the disliked practice itself

                # Check for different themes
                practice_themes = practice.get("themeTags", [])
                if original_theme and any(theme.lower() != original_theme.lower() for theme in practice_themes):
                    # Found practice of same type with at least one different theme
                    # Prefer a theme that is generally positive if multiple exist
                    positive_themes = ["mercy", "hope", "peace", "tranquility", "love", "gratitude"]
                    chosen_theme = original_theme # fallback
                    for pt in practice_themes:
                        if pt.lower() != original_theme.lower():
                            chosen_theme = pt
                            if pt.lower() in positive_themes: break # Prefer positive theme

                    return {
                        "suggested_alternative_type": original_practice_type,
                        "suggested_alternative_theme": chosen_theme
                    }
                elif not original_theme and practice_themes: # If original had no theme, but this one does
                     return {
                        "suggested_alternative_type": original_practice_type,
                        "suggested_alternative_theme": practice_themes[0]
                    }


        # Fallback: Suggest a different practice type with a generally positive/neutral theme
        # This is a very simplified heuristic
        positive_themes = ["mercy", "hope", "peace", "gratitude", "love"]

        if original_practice_type == "QuranicVerseReflection":
            # Suggest NameOfAllahSpotlight with a positive theme
            for p_theme in positive_themes:
                if any(p_theme in p.get("themeTags", []) for p in self.practice_library.get("NameOfAllahSpotlight",[])):
                    return {"suggested_alternative_type": "NameOfAllahSpotlight", "suggested_alternative_theme": p_theme}
            return {"suggested_alternative_type": "NameOfAllahSpotlight"} # Default if no theme match

        elif original_practice_type == "NameOfAllahSpotlight":
            # Suggest SunnahPractice with a positive theme
            for p_theme in positive_themes:
                if any(p_theme in p.get("themeTags", []) for p in self.practice_library.get("SunnahPractice",[])):
                    return {"suggested_alternative_type": "SunnahPractice", "suggested_alternative_theme": p_theme}
            return {"suggested_alternative_type": "SunnahPractice"}

        elif original_practice_type == "SunnahPractice":
            # Suggest QuranicVerseReflection with a positive theme
            for p_theme in positive_themes:
                if any(p_theme in p.get("themeTags", []) for p in self.practice_library.get("QuranicVerseReflection",[])):
                    return {"suggested_alternative_type": "QuranicVerseReflection", "suggested_alternative_theme": p_theme}
            return {"suggested_alternative_type": "QuranicVerseReflection"}

        return None

# Singleton instance for potential direct use if needed, though FastAPI typically handles instantiation.
# adaptive_journey_processor = AdaptiveJourneyProcessor()
