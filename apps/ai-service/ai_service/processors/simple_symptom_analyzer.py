"""
Simple Symptom Analyzer - Text-based approach for scalability
This approach uses symptom text directly and can be easily replaced with LLM later
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class LayerInsight:
    layer: str
    insights: List[str]
    recommendations: List[str]
    islamic_context: str
    severity_score: int

class SimpleSymptomAnalyzer:
    """
    Simple text-based symptom analyzer that works with symptom descriptions
    Designed to be easily replaceable with LLM-based analysis later
    """
    
    def __init__(self):
        # Keyword-based symptom mapping for each layer
        self.symptom_keywords = {
            'jism': {
                'sleep': {'severity': 20, 'keywords': ['sleep', 'insomnia', 'tired', 'waking', 'rest']},
                'tension': {'severity': 15, 'keywords': ['tension', 'headache', 'muscle', 'jaw', 'clenching', 'tight']},
                'breathing': {'severity': 25, 'keywords': ['heart', 'breathing', 'chest', 'palpitation', 'breath']},
                'energy': {'severity': 20, 'keywords': ['energy', 'fatigue', 'exhausted', 'tired', 'weak']},
                'digestive': {'severity': 15, 'keywords': ['digestive', 'stomach', 'nausea', 'appetite', 'eating']},
                'pain': {'severity': 15, 'keywords': ['aches', 'pains', 'soreness', 'hurt', 'physical pain']},
            },
            'nafs': {
                'sadness': {'severity': 20, 'keywords': ['sad', 'sadness', 'empty', 'emptiness', 'depressed', 'down']},
                'anger': {'severity': 18, 'keywords': ['anger', 'angry', 'irritable', 'frustrated', 'rage']},
                'anxiety': {'severity': 20, 'keywords': ['anxiety', 'anxious', 'worry', 'worried', 'nervous', 'panic']},
                'shame': {'severity': 15, 'keywords': ['shame', 'guilt', 'guilty', 'ashamed', 'embarrassed']},
                'jealousy': {'severity': 15, 'keywords': ['jealous', 'jealousy', 'resentment', 'envious', 'envy']},
                'numbness': {'severity': 22, 'keywords': ['numb', 'numbness', 'disconnected', 'detached']},
                'mood': {'severity': 15, 'keywords': ['mood swings', 'moody', 'emotional ups and downs']},
            },
            'aql': {
                'racing_thoughts': {'severity': 20, 'keywords': ['racing thoughts', 'thoughts racing', 'mind racing', 'fast thoughts']},
                'worry_future': {'severity': 18, 'keywords': ['worry about future', 'future anxiety', 'what if']},
                'overthinking': {'severity': 15, 'keywords': ['overthinking', 'ruminating', 'dwelling on past']},
                'concentration': {'severity': 18, 'keywords': ['concentration', 'focus', 'attention', 'distracted']},
                'negative_thoughts': {'severity': 22, 'keywords': ['negative thoughts', 'self-criticism', 'negative patterns']},
                'confusion': {'severity': 15, 'keywords': ['confused', 'confusion', 'decisions', 'indecisive']},
                'intrusive': {'severity': 20, 'keywords': ['intrusive thoughts', 'unwanted thoughts']},
                'fog': {'severity': 15, 'keywords': ['mental fog', 'brain fog', 'unclear thinking']},
            },
            'qalb': {
                'distant_allah': {'severity': 25, 'keywords': ['distant from allah', 'far from god', 'disconnected spiritually']},
                'mechanical_prayer': {'severity': 20, 'keywords': ['mechanical prayer', 'empty prayer', 'prayers feeling']},
                'difficult_dua': {'severity': 15, 'keywords': ['difficult dua', 'hard to pray', 'sincere dua']},
                'trust_qadar': {'severity': 18, 'keywords': ['trust qadar', 'destiny', 'fate', 'gods plan']},
                'spiritual_motivation': {'severity': 20, 'keywords': ['spiritual motivation', 'religious motivation', 'faith motivation']},
                'unworthy': {'severity': 22, 'keywords': ['unworthy', 'not good enough', 'allah mercy', 'forgiveness']},
            },
            'ruh': {
                'purpose': {'severity': 20, 'keywords': ['purpose', 'meaning', 'why am i here', 'life meaning']},
                'stranger': {'severity': 15, 'keywords': ['stranger in world', 'dont belong', 'alien', 'outsider']},
                'death_fear': {'severity': 18, 'keywords': ['fear death', 'afraid of dying', 'afterlife', 'death anxiety']},
                'yearning': {'severity': 15, 'keywords': ['yearning', 'longing', 'transcendent', 'eternal', 'spiritual longing']},
            }
        }
        
        # Islamic contexts for each layer
        self.islamic_contexts = {
            'jism': "The Jism (Physical Body) is an Amanah (trust) from Allah. The Prophet Muhammad ﷺ said: \"Your body has a right over you.\" (Sahih al-Bukhari). Taking care of your physical health through proper diet, rest, and hygiene is not just a worldly matter but a religious obligation. A healthy body facilitates worship and the fulfillment of your duties.",
            'nafs': "The Nafs (Ego/Lower Self) is the seat of desires and emotions. Islam emphasizes Tazkiyat an-Nafs (purification of the self) as a primary goal. Allah says in the Quran (91:9-10): \"He has succeeded who purifies it [the Nafs], And he has failed who instills it [with corruption].\" Managing emotions like anger, sadness, fear, and controlling base desires are central to this purification process.",
            'aql': "The Aql (Rational Mind/Intellect) is a precious gift from Allah, distinguishing humans. Islam strongly encourages its use for Tadabbur (deep reflection on the Quran) and Tafakkur (contemplation on creation). A sound Aql leads to stronger Iman (faith) and better decisions. Protecting the mind from doubts, distracting thoughts (Waswas), and negative influences is crucial for both worldly success and spiritual advancement.",
            'qalb': "The Qalb (Spiritual Heart) is central in Islam. The Prophet Muhammad ﷺ said: \"Truly in the body there is a morsel of flesh which, if it be sound, all the body is sound and which, if it be diseased, all of body is diseased. Truly it is the heart.\" (Bukhari & Muslim). A sound heart (Qalb Saleem) is one submissive to Allah, free from shirk, doubt, and spiritual diseases like envy (hasad), arrogance (kibr), and showing off (riya').",
            'ruh': "The Ruh (Soul/Spirit) is of Divine origin, a breath from Allah (Quran 15:29, 38:72). It is the essence of human life and consciousness, possessing an innate recognition of its Creator (Fitra). The Ruh naturally yearns for connection with the Divine and finds ultimate peace only in the remembrance of Allah (Quran 13:28: \"Verily, in the remembrance of Allah do hearts find rest.\")."
        }

    def analyze_layer(self, layer: str, symptoms: List[str], intensity: str = 'mild') -> LayerInsight:
        """
        Analyze symptoms for a specific layer using keyword matching
        This approach works with symptom text and is easily replaceable with LLM
        """
        if not symptoms or layer not in self.symptom_keywords:
            return LayerInsight(
                layer=layer,
                insights=[f"Alhamdulillah, your {layer.title()} appears relatively balanced."],
                recommendations=[f"Continue maintaining good {layer} health through Islamic practices."],
                islamic_context=self.islamic_contexts.get(layer, ""),
                severity_score=0
            )
        
        severity_score = 0
        matched_symptoms = []
        
        # Analyze each symptom text
        for symptom_text in symptoms:
            if not isinstance(symptom_text, str):
                continue
                
            symptom_lower = symptom_text.lower()
            
            # Check against all keyword patterns for this layer
            for symptom_type, symptom_data in self.symptom_keywords[layer].items():
                if any(keyword in symptom_lower for keyword in symptom_data['keywords']):
                    severity_score += symptom_data['severity']
                    matched_symptoms.append(symptom_text)
                    break  # Only count each symptom once
        
        # Apply intensity multiplier
        intensity_multipliers = {'mild': 1.0, 'moderate': 1.5, 'severe': 2.0}
        severity_score = int(severity_score * intensity_multipliers.get(intensity, 1.0))
        severity_score = min(severity_score, 100)  # Cap at 100
        
        # Generate insights and recommendations based on severity
        insights = self._generate_insights(layer, matched_symptoms, severity_score)
        recommendations = self._generate_recommendations(layer, matched_symptoms, severity_score)
        
        return LayerInsight(
            layer=layer,
            insights=insights,
            recommendations=recommendations,
            islamic_context=self.islamic_contexts.get(layer, ""),
            severity_score=severity_score
        )
    
    def _generate_insights(self, layer: str, symptoms: List[str], severity: int) -> List[str]:
        """Generate insights based on layer, symptoms, and severity"""
        insights = []
        
        if severity == 0:
            insights.append(f"Alhamdulillah, your {layer.title()} appears relatively balanced.")
        elif severity < 30:
            insights.append(f"Your {layer.title()} shows some minor concerns that can be addressed with gentle Islamic practices.")
        elif severity < 60:
            insights.append(f"Your {layer.title()} shows notable imbalances that would benefit from focused Islamic healing practices.")
        else:
            insights.append(f"Your {layer.title()} shows significant challenges that require dedicated attention and Islamic remedies.")
        
        # Add specific insights based on symptoms
        if symptoms:
            insights.append(f"Specific areas of concern include: {', '.join(symptoms[:3])}.")
        
        return insights[:3]  # Limit to 3 insights
    
    def _generate_recommendations(self, layer: str, symptoms: List[str], severity: int) -> List[str]:
        """Generate recommendations based on layer, symptoms, and severity"""
        recommendations = []
        
        # Layer-specific recommendations
        layer_recommendations = {
            'jism': [
                "Follow Prophetic sleep hygiene and healthy eating habits",
                "Practice gentle exercise and maintain physical cleanliness",
                "Make Du'a for physical well-being and healing"
            ],
            'nafs': [
                "Practice daily Dhikr and Istighfar for emotional purification",
                "Engage in self-reflection (Muhasabah) and gratitude (Shukr)",
                "Seek Islamic counseling or community support when needed"
            ],
            'aql': [
                "Protect your mind through beneficial knowledge and Quran recitation",
                "Practice focused meditation (Muraqabah) and mindful Dhikr",
                "Avoid negative influences and seek refuge from Waswas"
            ],
            'qalb': [
                "Increase daily Dhikr and heartfelt Du'a",
                "Focus on Khushu' (presence of heart) in prayers",
                "Engage in acts of charity and seeking Allah's forgiveness"
            ],
            'ruh': [
                "Contemplate Allah's creation and your purpose in life",
                "Increase worship and connection with the Divine",
                "Study Islamic teachings about the soul and afterlife"
            ]
        }
        
        base_recs = layer_recommendations.get(layer, ["Continue Islamic practices for this layer"])
        
        if severity == 0:
            recommendations.append(f"Continue maintaining your {layer} through regular Islamic practices.")
        elif severity < 30:
            recommendations.extend(base_recs[:1])
        elif severity < 60:
            recommendations.extend(base_recs[:2])
        else:
            recommendations.extend(base_recs)
        
        return recommendations[:3]  # Limit to 3 recommendations

# Global instance
simple_symptom_analyzer = SimpleSymptomAnalyzer()