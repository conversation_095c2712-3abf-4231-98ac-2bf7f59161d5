"""
AI Service Processor for Feature 1: Understanding Your Inner Landscape
Personalized Welcome Message Generation
"""
from typing import Dict, Any, Optional, List
import logging

# Assuming UserProfileData and PersonalizedWelcomeResponse, WelcomeAction models are accessible
# For now, let's imagine they are imported or defined if this were a separate models file.
# from ..main import UserProfileData, PersonalizedWelcomeResponse, WelcomeAction
# For simplicity in this step, we might redefine or rely on dicts matching the structure.

logger = logging.getLogger(__name__)

# Re-defining Pydantic models here for clarity if they were in main.py,
# or imagine they are imported from a central models.py
from pydantic import BaseModel, Field

class UserProfileData(BaseModel):
    user_id: str
    mental_health_awareness: Optional[Dict[str, Any]] = None
    ruqya_knowledge: Optional[Dict[str, Any]] = None
    spiritual_optimizer: Optional[Dict[str, Any]] = None
    # Add other relevant fields from Feature 0 profile as needed by logic

class WelcomeAction(BaseModel):
    id: str
    text: str
    description: Optional[str] = None

class PersonalizedWelcomeResponse(BaseModel):
    user_id: str
    user_type: str
    greeting: str
    introduction: str
    explanation: Optional[str] = None
    motivation: Optional[str] = None
    primary_action: WelcomeAction
    secondary_actions: List[WelcomeAction] = Field(default_factory=list)


class WelcomeGeneratorProcessor:
    """
    Generates personalized welcome messages for the assessment feature
    based on the user's onboarding profile.
    """

    def _determine_user_type(self, user_profile_data: UserProfileData) -> str:
        """
        Determines user type based on profile.
        This logic should align with Feature 0's profile generation outcome
        and how user types are defined in the documentation.
        """
        # Simplified logic based on Feature 0 documentation examples
        # (Ahmed, Layla, Ustadh Saeed, Dr. Fatima, Imam Abdullah)
        # and backend's assessment.service.ts determineUserType

        # Check for Spiritual Optimizer types first
        spiritual_optimizer = user_profile_data.spiritual_optimizer or {}
        if spiritual_optimizer.get('type') == 'clinical_integration':
            return 'clinically_aware_spiritual_optimizer'  # Dr. Fatima
        if spiritual_optimizer.get('type') == 'traditional_bridge':
            return 'symptom_aware_spiritual_optimizer'  # Imam Abdullah

        # Check for Ruqya Expert
        # The original access pattern user_profile_data.ruqya_knowledge['level'].get('ruqya_knowledge') was problematic.
        # Assuming 'level' is the direct string value like 'expert', not another nested dict.
        # If 'level' itself is a dict like {'ruqya_knowledge': 'expert'}, then the original was closer but still unsafe.
        # Adjusting based on a more likely structure for ruqya_knowledge: {"level": "expert"}
        ruqya_knowledge = user_profile_data.ruqya_knowledge or {}
        if ruqya_knowledge.get('level') == 'expert':
            return 'ruqya_expert'  # Ustadh Saeed
        # No explicit 'ruqya_practitioner' type is defined in this method, unlike SpiritualAnalysisProcessor

        # Check for Clinically Aware
        # Similar assumption for mental_health_awareness structure: {"level": "clinical_aware"}
        mental_health_awareness = user_profile_data.mental_health_awareness or {}
        if mental_health_awareness.get('level') == 'clinical_aware':
            return 'clinically_aware'  # Ahmed

        # Check for New Muslim
        life_circumstances = user_profile_data.life_circumstances or {}
        situations = life_circumstances.get('situations', [])
        if isinstance(situations, list) and 'new_muslim_within_2_years' in situations:
            return 'new_muslim'
        # Note: The SpiritualAnalysisProcessor also checks life_circumstances.get('islamicJourneyStage') == 'new_muslim'
        # This welcome_generator's _determine_user_type does not currently check that specific field.

        return 'symptom_aware'  # Layla (default)


    def generate_welcome(self, user_profile: UserProfileData) -> PersonalizedWelcomeResponse:
        """
        Generates the personalized welcome message content.
        """
        user_id = user_profile.user_id
        user_type = self._determine_user_type(user_profile)

        logger.info(f"Generating welcome for user_id: {user_id}, determined user_type: {user_type}")

        # Default content (similar to symptom_aware - Layla)
        greeting = f"As-salamu alaykum, dear brother/sister." # Generic, can be improved if name is in profile
        introduction = "You mentioned feeling overwhelmed but aren't sure exactly what's happening. That's completely normal - sometimes our hearts know something is wrong before our minds can name it."
        explanation = "Let's explore what you're experiencing together. We'll look at your physical, emotional, mental, and spiritual experiences to understand what Allah might be teaching you through this time."
        motivation = "There's no pressure to use any specific terms - just share what feels true."
        primary_action = WelcomeAction(id='begin_assessment', text='Begin Assessment')
        secondary_actions = [WelcomeAction(id='emergency_help', text='I Need Immediate Help')]

        # Templates based on user type (aligning with Feature 1 docs)
        if user_type == 'clinically_aware': # Ahmed
            greeting = f"As-salamu alaykum, {user_profile.demographics.get('fullName', 'brother/sister')}." if user_profile.demographics else greeting # Example using name
            introduction = "You mentioned you're dealing with anxiety/depression and seeking Islamic approaches. As someone familiar with mental health, you understand that healing is complex and multi-dimensional."
            explanation = "Islam teaches us that we exist on five interconnected layers - from our physical body (Jism) to our eternal soul (Ruh). Let's explore how your current experiences map to this Islamic framework."
            motivation = "This assessment will help us understand which layers need attention and provide you with a comprehensive spiritual diagnosis."
            secondary_actions.insert(0, WelcomeAction(id='learn_layers', text='Learn About 5 Layers First'))

        elif user_type == 'ruqya_expert': # Ustadh Saeed
            greeting = f"As-salamu alaykum, respected one." # Placeholder, could use title if available
            introduction = "MashaAllah, your experience with ruqya brings valuable wisdom to your healing journey. This assessment will integrate your spiritual healing knowledge with our comprehensive diagnostic approach."
            explanation = "We'll map your experiences across the five layers and provide advanced insights that complement your ruqya practice, including potential spiritual ailment indicators and treatment recommendations."
            motivation = None
            primary_action = WelcomeAction(id='advanced_assessment', text='Advanced Assessment')
            secondary_actions = [
                WelcomeAction(id='standard_assessment', text='Standard Assessment'),
                WelcomeAction(id='ruqya_focused_diagnosis', text='Ruqya-Focused Diagnosis')
            ]
            secondary_actions.append(WelcomeAction(id='emergency_help', text='I Need Immediate Help'))


        elif user_type == 'clinically_aware_spiritual_optimizer': # Dr. Fatima
            greeting = f"As-salamu alaykum, Dr. {user_profile.demographics.get('lastName', 'esteemed colleague')}." if user_profile.demographics else greeting
            introduction = "As a healthcare professional seeking to integrate clinical knowledge with Islamic spirituality, you're pioneering an important field. This assessment will help you understand how Islamic psychology maps to clinical frameworks."
            explanation = "We'll provide you with advanced insights that bridge your clinical expertise with Islamic spiritual understanding, supporting both your personal growth and professional development in Islamic mental health."
            motivation = None
            primary_action = WelcomeAction(id='clinical_islamic_integration_assessment', text='Clinical-Islamic Integration Assessment')
            secondary_actions = [
                WelcomeAction(id='research_mode_assessment', text='Research Mode'),
                WelcomeAction(id='prof_dev_assessment', text='Professional Development Focus')
            ]
            secondary_actions.append(WelcomeAction(id='emergency_help', text='I Need Immediate Help'))

        elif user_type == 'symptom_aware_spiritual_optimizer': # Imam Abdullah
            greeting = f"As-salamu alaykum, respected Imam/leader." # Placeholder
            introduction = "Your role as a spiritual leader brings unique wisdom to understanding the connection between traditional Islamic knowledge and modern mental health challenges. This assessment will help you see how classical Islamic spiritual diseases relate to contemporary symptoms."
            explanation = "We'll provide insights that honor traditional Islamic healing while helping you understand and support community members facing modern mental health struggles."
            motivation = None
            primary_action = WelcomeAction(id='traditional_modern_bridge_assessment', text='Traditional-Modern Bridge Assessment')
            secondary_actions = [
                WelcomeAction(id='community_leadership_assessment', text='Community Leadership Focus'),
                WelcomeAction(id='spiritual_diseases_mapping_assessment', text='Spiritual Diseases Mapping')
            ]
            secondary_actions.append(WelcomeAction(id='emergency_help', text='I Need Immediate Help'))

        elif user_type == 'new_muslim':
             greeting = f"As-salamu alaykum, welcome to the Ummah!"
             introduction = "We are honored to have you. This journey of healing is for everyone. This assessment will gently guide you through understanding your experiences from an Islamic perspective."
             explanation = "We will explore your physical, emotional, mental, and spiritual well-being in a way that respects your new path and provides foundational Islamic insights."
             motivation = "Allah says, 'Verily, with hardship, there is relief.' (Quran 94:5). Your journey to peace is supported."
             secondary_actions.insert(0, WelcomeAction(id='learn_islamic_basics_assessment', text='Learn Islamic Wellness Basics'))


        return PersonalizedWelcomeResponse(
            user_id=user_id,
            user_type=user_type,
            greeting=greeting,
            introduction=introduction,
            explanation=explanation,
            motivation=motivation,
            primary_action=primary_action,
            secondary_actions=secondary_actions
        )

# Global instance for use in main.py
welcome_generator_processor = WelcomeGeneratorProcessor()
