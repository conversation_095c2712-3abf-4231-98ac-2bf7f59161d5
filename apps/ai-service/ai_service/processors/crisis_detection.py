def calculate_crisis_severity(user_input, behavior_patterns, crisis_keywords):
    # This would involve NLP on user_input, analysis of behavior_patterns,
    # and matching against crisis_keywords.
    score = 0
    user_input_lower = user_input.lower()

    for keyword_info in crisis_keywords:
        if isinstance(keyword_info, tuple):
            keyword, keyword_score = keyword_info
        else:
            keyword = keyword_info
            keyword_score = 5  # Default score if not specified

        if keyword in user_input_lower:
            score += keyword_score
    
    # Simulate behavioral indicators impact
    if "rapid_app_switching" in behavior_patterns:
        score += 2
    if "repeated_emergency_access" in behavior_patterns:
        score += 3

    return score

def detect_crisis_indicators(user_input: str, behavior_patterns: list) -> str:
    crisis_keywords = [
        ("panic attack", 5), ("can't breathe", 5), ("dying", 5), ("help me", 5),
        ("suicidal", 5), ("end it all", 5), ("no hope", 5), ("hopeless", 3), ("worthless", 3), ("<PERSON><PERSON> has abandoned me", 5)
    ]
    
    # behavioral_indicators = [
    #     "rapid_app_switching", "repeated_emergency_access",
    #     "late_night_distress_patterns", "isolation_indicators"
    # ]
    
    crisis_score = calculate_crisis_severity(
        user_input, behavior_patterns, crisis_keywords
    )
    
    if crisis_score >= 8:
        return "immediate_intervention"
    elif crisis_score >= 5:
        return "enhanced_support"
    else:
        return "standard_support"
