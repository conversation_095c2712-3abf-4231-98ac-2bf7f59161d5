"""
Symptom Analysis Processor
Analyzes user symptoms using Islamic healing framework and AI
"""

try:
    import openai
except ImportError:
    openai = None
from typing import Dict, List, Any, Optional
import json
import logging
from dataclasses import dataclass
import os

logger = logging.getLogger(__name__)

@dataclass
class SymptomAnalysisResult:
    """Result of symptom analysis"""
    primary_layers: List[str]
    severity_level: str
    root_causes: List[str]
    recommended_journey: str
    immediate_actions: List[str]
    spotlight: str
    healing_duration: int
    confidence_score: float

class SymptomAnalyzer:
    """
    AI-powered symptom analyzer using Islamic healing framework
    """

    def __init__(self):
        if openai:
            self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        else:
            self.openai_client = None
        self.soul_layers = {
            "jism": "Physical body - material manifestation",
            "nafs": "Ego/emotions - desires, impulses, emotional states",
            "aql": "Mind/intellect - thoughts, reasoning, mental processes",
            "qalb": "Heart - spiritual heart, faith, divine connection",
            "ruh": "Soul/spirit - pure essence, divine spark"
        }

    async def analyze_symptoms(
        self,
        symptoms: Dict[str, List[str]],
        intensity: Dict[str, int],
        duration: str,
        additional_notes: Optional[str] = None
    ) -> SymptomAnalysisResult:
        """
        Analyze symptoms using Islamic healing framework
        """
        try:
            # Prepare analysis prompt
            prompt = self._create_analysis_prompt(symptoms, intensity, duration, additional_notes)

            # Get AI analysis
            response = await self._get_ai_analysis(prompt)

            # Parse and validate response
            result = self._parse_analysis_response(response)

            return result

        except Exception as e:
            logger.error(f"Error in symptom analysis: {str(e)}")
            # Return fallback analysis
            return self._create_fallback_analysis(symptoms, intensity)

    def _create_analysis_prompt(
        self,
        symptoms: Dict[str, List[str]],
        intensity: Dict[str, int],
        duration: str,
        additional_notes: Optional[str]
    ) -> str:
        """Create analysis prompt for AI"""

        prompt = f"""
You are an Islamic healing expert analyzing symptoms using the 5-layer soul model from Islamic tradition.

SOUL LAYERS:
{json.dumps(self.soul_layers, indent=2)}

USER SYMPTOMS:
{json.dumps(symptoms, indent=2)}

INTENSITY RATINGS (1-10):
{json.dumps(intensity, indent=2)}

DURATION: {duration}

ADDITIONAL NOTES: {additional_notes or "None"}

ANALYSIS REQUIREMENTS:
1. Identify primary affected soul layers (ranked by severity)
2. Determine overall severity level (mild/moderate/severe)
3. Identify potential root causes from Islamic perspective
4. Recommend appropriate healing journey type
5. Suggest immediate Islamic healing actions
6. Provide key insight/spotlight for focus
7. Estimate healing duration in days
8. Provide confidence score (0-1)

ISLAMIC HEALING PRINCIPLES:
- Physical (Jism): Proper nutrition, exercise, sleep, hygiene
- Emotional (Nafs): Dhikr, istighfar, emotional regulation, fasting
- Mental (Aql): Quran study, reflection, seeking knowledge
- Heart (Qalb): Prayer, dhikr, purification, tawbah
- Soul (Ruh): Deep worship, contemplation, spiritual practices

RESPONSE FORMAT (JSON):
{{
  "primary_layers": ["layer1", "layer2"],
  "severity_level": "mild|moderate|severe",
  "root_causes": ["cause1", "cause2"],
  "recommended_journey": "journey_type",
  "immediate_actions": ["action1", "action2", "action3"],
  "spotlight": "key insight or focus area",
  "healing_duration": number_of_days,
  "confidence_score": 0.0-1.0
}}

Provide analysis based on authentic Islamic healing traditions and Quranic guidance.
"""
        return prompt

    async def _get_ai_analysis(self, prompt: str) -> str:
        """Get analysis from OpenAI"""
        if not self.openai_client:
            # Return mock response when OpenAI is not available
            return """
            {
                "primary_layers": ["nafs", "qalb"],
                "severity_level": "moderate",
                "root_causes": ["spiritual_disconnection", "emotional_imbalance"],
                "recommended_journey": "14-day-spiritual-healing",
                "immediate_actions": ["Begin with istighfar and dhikr", "Establish regular prayer routine"],
                "spotlight": "Focus on strengthening your connection with Allah",
                "healing_duration": 14,
                "confidence_score": 0.7
            }
            """

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert in Islamic healing and mental wellness, trained in the 5-layer soul model from Islamic tradition. Provide accurate, authentic Islamic guidance."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise

    def _parse_analysis_response(self, response: str) -> SymptomAnalysisResult:
        """Parse AI response into structured result"""
        try:
            # Extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                # No JSON found, return fallback
                logger.warning("No JSON found in AI response, using fallback")
                return self._create_fallback_analysis({}, {})

            json_str = response[start_idx:end_idx]
            data = json.loads(json_str)

            return SymptomAnalysisResult(
                primary_layers=data.get("primary_layers", []),
                severity_level=data.get("severity_level", "moderate"),
                root_causes=data.get("root_causes", []),
                recommended_journey=data.get("recommended_journey", "7-day-general-healing"),
                immediate_actions=data.get("immediate_actions", []),
                spotlight=data.get("spotlight", "Focus on spiritual purification"),
                healing_duration=data.get("healing_duration", 14),
                confidence_score=data.get("confidence_score", 0.7)
            )

        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            # Return fallback instead of raising
            return self._create_fallback_analysis({}, {})

    def _create_fallback_analysis(
        self,
        symptoms: Dict[str, List[str]],
        intensity: Dict[str, int]
    ) -> SymptomAnalysisResult:
        """Create fallback analysis when AI fails"""

        # Determine primary layers based on symptoms
        primary_layers = [layer for layer, symptom_list in symptoms.items() if symptom_list]

        # Calculate average intensity
        avg_intensity = sum(intensity.values()) / len(intensity) if intensity else 5

        if avg_intensity <= 3:
            severity = "mild"
            duration = 7
        elif avg_intensity <= 6:
            severity = "moderate"
            duration = 14
        else:
            severity = "severe"
            duration = 21

        return SymptomAnalysisResult(
            primary_layers=primary_layers[:3],  # Top 3 layers
            severity_level=severity,
            root_causes=["Spiritual disconnection", "Emotional imbalance", "Physical neglect"],
            recommended_journey="comprehensive-healing",
            immediate_actions=[
                "Begin with istighfar and dhikr",
                "Establish regular prayer routine",
                "Practice mindful breathing with Islamic remembrance"
            ],
            spotlight="Focus on strengthening your connection with Allah through consistent worship",
            healing_duration=duration,
            confidence_score=0.6
        )

# Singleton instance
symptom_analyzer = SymptomAnalyzer()
