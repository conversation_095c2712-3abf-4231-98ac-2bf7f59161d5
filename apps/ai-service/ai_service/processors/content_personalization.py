from typing import List, Dict, Any, TypedDict, Optional
import random
import logging
from .. import db_utils # Import the new database utility

logger = logging.getLogger(__name__)

# Define more structured types for content (ensure these match or are compatible with db_utils return dicts)
# These TypedDicts define the structure *after* mapping from DB column names if they differ.
class QuranVerse(TypedDict):
    id: str
    arabic_text: str
    translation_en: str
    surah_name_en: str
    ayah_number: int
    theme: str
    audio_url: Optional[str]

class DhikrPhrase(TypedDict):
    id: str
    arabic_text: str
    transliteration_en: str
    translation_en: str
    recommended_count: Optional[int]
    theme: str
    audio_url: Optional[str]


class ContentPersonalizationProcessor:
    def __init__(self):
        # Data will now be fetched from the database, so no hardcoded lists here.
        # We could potentially load all content on init if the dataset is small and cache it,
        # or fetch on demand in _get_verses_by_theme and _get_dhikr_by_theme.
        # For this refactor, we'll fetch on demand.
        pass

    def _map_db_verse_to_quran_verse(self, db_row: Dict[str, Any]) -> QuranVerse:
        """Maps a DB row (dict) to QuranVerse TypedDict, handling potential case differences."""
        return {
            "id": str(db_row.get("id")), # Ensure ID is a string
            "arabic_text": db_row.get("arabic_text", ""),
            "translation_en": db_row.get("translation_en", ""),
            "surah_name_en": db_row.get("surah_name_en", ""),
            "ayah_number": int(db_row.get("ayah_number", 0)),
            "theme": db_row.get("theme", "general"),
            "audio_url": db_row.get("audio_url")
        }

    def _map_db_dhikr_to_dhikr_phrase(self, db_row: Dict[str, Any]) -> DhikrPhrase:
        """Maps a DB row (dict) to DhikrPhrase TypedDict."""
        return {
            "id": str(db_row.get("id")),
            "arabic_text": db_row.get("arabic_text", ""),
            "transliteration_en": db_row.get("transliteration_en", ""),
            "translation_en": db_row.get("translation_en", ""),
            "recommended_count": db_row.get("recommended_count"),
            "theme": db_row.get("theme", "general"),
            "audio_url": db_row.get("audio_url")
        }

    def _get_verses_by_theme(self, theme: str, count: int = 1) -> List[QuranVerse]:
        try:
            db_verses = db_utils.fetch_quran_verses(theme=theme, count=count)
            return [self._map_db_verse_to_quran_verse(row) for row in db_verses]
        except Exception as e:
            logger.error(f"Error fetching Quran verses from DB for theme '{theme}': {e}")
            return []

    def _get_dhikr_by_theme(self, theme: str, count: int = 1) -> List[DhikrPhrase]:
        try:
            db_dhikr = db_utils.fetch_dhikr_phrases(theme=theme, count=count)
            return [self._map_db_dhikr_to_dhikr_phrase(row) for row in db_dhikr]
        except Exception as e:
            logger.error(f"Error fetching Dhikr from DB for theme '{theme}': {e}")
            return []

    def _get_random_verses(self, count: int = 1) -> List[QuranVerse]:
        try:
            db_verses = db_utils.fetch_quran_verses(count=count)
            return [self._map_db_verse_to_quran_verse(row) for row in db_verses]
        except Exception as e:
            logger.error(f"Error fetching random Quran verses from DB: {e}")
            return []

    def _get_random_dhikr(self, count: int = 1) -> List[DhikrPhrase]:
        try:
            db_dhikr = db_utils.fetch_dhikr_phrases(count=count)
            return [self._map_db_dhikr_to_dhikr_phrase(row) for row in db_dhikr]
        except Exception as e:
            logger.error(f"Error fetching random Dhikr from DB: {e}")
            return []

    def personalize_content(
        self,
        user_preferences: Dict[str, Any], # e.g., {"preferred_themes": ["patience", "hope"], "favorite_surahs": ["Ash-Sharh"]}
        past_interactions: List[Dict[str, Any]], # e.g., [{"content_id": "quran_94_5", "rating": 5, "type": "quran"}]
        crisis_indicators: List[str] # e.g., ["anxiety", "feeling_overwhelmed", "spiritual_dryness"]
    ) -> Dict[str, Any]:
        recommended_verses: List[QuranVerse] = []
        recommended_dhikr: List[DhikrPhrase] = []
        reasoning_phrases: List[str] = ["Personalized based on current needs"]

        # Primary logic based on crisis indicators
        if any(indicator in crisis_indicators for indicator in ["suicidal", "no hope", "despair"]):
            recommended_verses.extend(self._get_verses_by_theme("hope", 1))
            recommended_verses.extend(self._get_verses_by_theme("tawakkul", 1))
            recommended_dhikr.extend(self._get_dhikr_by_theme("tawakkul", 1))
            reasoning_phrases.append("addressing feelings of hopelessness and despair with themes of hope and reliance on Allah")
        elif any(indicator in crisis_indicators for indicator in ["panic attack", "can't breathe", "extreme_anxiety"]):
            recommended_verses.extend(self._get_verses_by_theme("sakinah", 1))
            recommended_dhikr.extend(self._get_dhikr_by_theme("calm", 1))
            recommended_dhikr.extend(self._get_dhikr_by_theme("tawakkul",1))
            reasoning_phrases.append("providing calm and remembrance during panic with themes of tranquility and reliance")
        elif any(indicator in crisis_indicators for indicator in ["sadness", "grief"]):
            recommended_verses.extend(self._get_verses_by_theme("patience", 1))
            recommended_verses.extend(self._get_verses_by_theme("hope", 1))
            reasoning_phrases.append("offering comfort for sadness and grief through patience and hope")
        
        # Secondary logic: user preferences for themes
        preferred_themes = user_preferences.get("preferred_themes", [])
        for theme in preferred_themes:
            if not any(v for v in recommended_verses if v["theme"] == theme): # Avoid duplicates if already added by crisis
                 recommended_verses.extend(self._get_verses_by_theme(theme, 1))
            if not any(d for d in recommended_dhikr if d["theme"] == theme):
                 recommended_dhikr.extend(self._get_dhikr_by_theme(theme, 1))
        if preferred_themes:
            reasoning_phrases.append(f"incorporating preferred themes: {', '.join(preferred_themes)}")

        # TODO: Add logic for past_interactions (e.g., boost content similar to highly-rated past content)
        # TODO: Add logic for favorite_surahs from user_preferences

        # Ensure some content is always recommended if specific rules didn't pick enough
        if not recommended_verses:
            # recommended_verses.extend(random.sample(self.quran_data, min(2, len(self.quran_data)))) # Old hardcoded way
            recommended_verses.extend(self._get_random_verses(count=2))
            reasoning_phrases.append("providing general spiritual comfort with selected verses")
        if not recommended_dhikr:
            # recommended_dhikr.extend(random.sample(self.dhikr_data, min(2, len(self.dhikr_data)))) # Old hardcoded way
            recommended_dhikr.extend(self._get_random_dhikr(count=2))
            reasoning_phrases.append("offering general remembrance with selected dhikr")

        # Limit to a certain number of items to not overwhelm the user
        recommended_verses = recommended_verses[:2] # Max 2 verses
        recommended_dhikr = recommended_dhikr[:2]  # Max 2 dhikr phrases

        # Remove duplicates that might have been added by different rules
        recommended_verses = [dict(t) for t in {tuple(d.items()) for d in recommended_verses}]
        recommended_dhikr = [dict(t) for t in {tuple(d.items()) for d in recommended_dhikr}]


        reasoning = ". ".join(reasoning_phrases) + "."
        if len(reasoning_phrases) == 1: # Only the default phrase
            reasoning = "Selected content to provide spiritual support."


        return {
            "quran_verses": recommended_verses,
            "dhikr_phrases": recommended_dhikr,
            "reasoning": reasoning
        }

content_personalization_processor = ContentPersonalizationProcessor()
