"""
Authentication dependencies for the AI service
"""

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Security
security = HTTPBearer()

# Authentication dependency
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token"""
    # TODO: Implement proper JWT verification
    token = credentials.credentials
    if not token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return {"user_id": "mock_user"}  # Mock for now