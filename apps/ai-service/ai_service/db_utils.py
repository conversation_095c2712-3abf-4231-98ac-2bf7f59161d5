import os
import psycopg2
import logging
from typing import List, Dict, Optional, Any

logger = logging.getLogger(__name__)

DATABASE_URL = os.getenv("DATABASE_URL")

def get_db_connection():
    """Establishes a connection to the PostgreSQL database."""
    if not DATABASE_URL:
        logger.error("DATABASE_URL environment variable is not set.")
        raise ConnectionError("Database URL not configured.")
    try:
        conn = psycopg2.connect(DATABASE_URL)
        return conn
    except psycopg2.OperationalError as e:
        logger.error(f"Failed to connect to database: {e}")
        raise ConnectionError(f"Database connection failed: {e}")

def _execute_query(query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
    """Executes a given SQL query and returns results as a list of dicts."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cursor.execute(query, params or ())
        results = [dict(row) for row in cursor.fetchall()]
        cursor.close()
        return results
    except (Exception, psycopg2.Error) as error:
        logger.error(f"Error executing query: {query} with params {params}. Error: {error}")
        # In a production system, you might want to raise a custom DB error or handle more gracefully.
        raise
    finally:
        if conn:
            conn.close()

# Assuming table names 'quran_verse_ai_content' and 'dhikr_ai_content'
# and columns as defined in the AI Service Production Readiness plan.

def fetch_quran_verses(theme: Optional[str] = None, count: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Fetches Quran verses from the database.
    Can filter by theme and limit by count.
    If theme is None, and count is provided, fetches random verses.
    """
    if theme:
        query = "SELECT id, surah_name_en, ayah_number, arabic_text, translation_en, theme, audio_url FROM quran_verse_ai_content WHERE theme = %s"
        params = [theme]
        if count:
            query += " ORDER BY RANDOM() LIMIT %s" # Simple random for now
            params.append(count)
    elif count:
        query = "SELECT id, surah_name_en, ayah_number, arabic_text, translation_en, theme, audio_url FROM quran_verse_ai_content ORDER BY RANDOM() LIMIT %s"
        params = [count]
    else: # Fetch all if no theme and no count (potentially dangerous for large tables)
        query = "SELECT id, surah_name_en, ayah_number, arabic_text, translation_en, theme, audio_url FROM quran_verse_ai_content"
        params = []

    logger.debug(f"Executing fetch_quran_verses with query: {query} and params: {params}")
    return _execute_query(query, tuple(params))


def fetch_dhikr_phrases(theme: Optional[str] = None, count: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Fetches Dhikr phrases from the database.
    Can filter by theme and limit by count.
    If theme is None, and count is provided, fetches random Dhikr.
    """
    if theme:
        query = "SELECT id, arabic_text, transliteration_en, translation_en, recommended_count, theme, audio_url FROM dhikr_ai_content WHERE theme = %s"
        params = [theme]
        if count:
            query += " ORDER BY RANDOM() LIMIT %s" # Simple random for now
            params.append(count)
    elif count:
        query = "SELECT id, arabic_text, transliteration_en, translation_en, recommended_count, theme, audio_url FROM dhikr_ai_content ORDER BY RANDOM() LIMIT %s"
        params = [count]
    else: # Fetch all
        query = "SELECT id, arabic_text, transliteration_en, translation_en, recommended_count, theme, audio_url FROM dhikr_ai_content"
        params = []

    logger.debug(f"Executing fetch_dhikr_phrases with query: {query} and params: {params}")
    return _execute_query(query, tuple(params))

# Example usage (for testing this module directly, if needed):
if __name__ == '__main__':
    # This requires DATABASE_URL to be set in the environment
    # and the tables to exist with some data.
    try:
        logger.setLevel(logging.DEBUG)
        print("Attempting to fetch Quran verses (theme: hope, count: 1):")
        verses = fetch_quran_verses(theme="hope", count=1)
        for verse in verses:
            print(verse)

        print("\nAttempting to fetch random Dhikr phrases (count: 2):")
        dhikr_phrases = fetch_dhikr_phrases(count=2)
        for dhikr in dhikr_phrases:
            print(dhikr)

        print("\nAttempting to fetch all Dhikr phrases (no theme, no count):")
        all_dhikr = fetch_dhikr_phrases()
        print(f"Found {len(all_dhikr)} dhikr phrases in total.")

    except ConnectionError as e:
        print(f"DB Connection Error: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")
    # Note: psycopg2.extras.DictCursor makes rows accessible like dictionaries.
    # The column names in the DB should match the keys used here (e.g., 'surah_name_en').
    # If your DB uses snake_case (e.g., 'surah_name_en'), DictCursor handles it fine.
    # The TypedDicts in content_personalization.py use camelCase for Python consistency.
    # The mapping from snake_case (DB) to camelCase (Python TypedDict) will happen
    # when the processor consumes the data from these fetch functions.
    # For this db_utils.py, it returns dicts with keys matching DB column names.
    # The processor will then need to map these to its TypedDicts.
    # Alternatively, this db_utils.py could do the mapping here if preferred.
    # For now, keeping it simple: returns dicts with DB column names.
