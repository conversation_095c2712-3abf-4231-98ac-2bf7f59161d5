"""
Qalb Healing AI Service - Main Application
Islamic Mental Wellness AI Processing Service
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import os
import logging
from dotenv import load_dotenv
import uvicorn

# Load environment variables
load_dotenv()

# Configure logging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
numeric_level = getattr(logging, log_level, None)
if not isinstance(numeric_level, int):
    raise ValueError(f"Invalid log level: {log_level}")
logging.basicConfig(level=numeric_level)
logger = logging.getLogger(__name__)

# Import routers
from .endpoints.crisis_analysis import router as crisis_analysis_router
from .endpoints.crisis import router as crisis_router
from .endpoints.content_personalization import router as content_personalization_router
from .endpoints.profile_generation import profile_router

# Initialize FastAPI app
app = FastAPI(
    title="Qalb Healing AI Service",
    description="Islamic Mental Wellness AI Processing Service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Include routers
app.include_router(crisis_analysis_router)
app.include_router(crisis_router)
app.include_router(content_personalization_router)
app.include_router(profile_router)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import authentication
from .auth.dependencies import verify_token

# Pydantic Models
class SoulLayer(BaseModel):
    """Soul layer enumeration"""
    jism: str = "jism"  # Physical body
    nafs: str = "nafs"  # Ego/emotions
    aql: str = "aql"    # Mind/intellect
    qalb: str = "qalb"  # Heart/spiritual heart
    ruh: str = "ruh"    # Soul/spirit

class SymptomAnalysisRequest(BaseModel):
    """Request model for symptom analysis"""
    user_id: str = Field(..., description="User ID")
    symptoms: Dict[str, List[str]] = Field(..., description="Symptoms by soul layer")
    intensity: Dict[str, int] = Field(..., description="Symptom intensity ratings")
    duration: str = Field(..., description="Duration of symptoms")
    additional_notes: Optional[str] = Field(None, description="Additional user notes")

class SymptomAnalysisResponse(BaseModel):
    """Response model for symptom analysis"""
    analysis_id: str = Field(..., description="Unique analysis ID")
    primary_layers_affected: List[str] = Field(..., description="Primary soul layers affected")
    severity_level: str = Field(..., description="Overall severity level")
    recommended_journey: str = Field(..., description="Recommended healing journey")
    immediate_actions: List[str] = Field(..., description="Immediate recommended actions")
    spotlight: str = Field(..., description="Key insight or focus area")
    estimated_healing_duration: int = Field(..., description="Estimated healing duration in days")

class ContentRecommendationRequest(BaseModel):
    """Request model for content recommendations"""
    user_id: str = Field(..., description="User ID")
    healing_focus: List[str] = Field(..., description="Current healing focus areas")
    current_mood: Optional[str] = Field(None, description="Current mood")
    time_available: Optional[int] = Field(None, description="Available time in minutes")
    content_types: Optional[List[str]] = Field(None, description="Preferred content types")

class ContentRecommendationResponse(BaseModel):
    """Response model for content recommendations"""
    content_ids: List[str] = Field(..., description="Recommended content IDs")
    reasoning: str = Field(..., description="Reasoning for recommendations")
    priority_order: List[str] = Field(..., description="Priority order of content")

class JourneyGenerationRequest(BaseModel):
    """Request model for journey generation"""
    user_id: str = Field(..., description="User ID")
    focus_layers: List[str] = Field(..., description="Soul layers to focus on")
    duration_days: int = Field(..., description="Journey duration in days")
    intensity_level: str = Field(..., description="Intensity level: light, moderate, intensive")
    specific_goals: Optional[List[str]] = Field(None, description="Specific healing goals")

class JourneyGenerationResponse(BaseModel):
    """Response model for journey generation"""
    journey_id: str = Field(..., description="Generated journey ID")
    modules: List[Dict[str, Any]] = Field(..., description="Journey modules")
    daily_practices: List[Dict[str, Any]] = Field(..., description="Daily practices")
    milestones: List[Dict[str, Any]] = Field(..., description="Journey milestones")

class CrisisAnalysisRequest(BaseModel):
    """Request model for crisis analysis"""
    response: Dict[str, Any] = Field(..., description="User response data")
    stepId: str = Field(..., description="Current onboarding step")
    context: str = Field(default="onboarding", description="Analysis context")
    userId: Optional[str] = Field(None, description="User ID for logging")

class CrisisAnalysisResponse(BaseModel):
    """Response model for crisis analysis"""
    level: str = Field(..., description="Crisis level: none, low, moderate, high, critical")
    confidence: float = Field(..., description="Confidence score 0-1")
    indicators: List[str] = Field(..., description="List of detected crisis indicators")
# CrisisAnalysisRequest and CrisisAnalysisResponse models are now expected to be handled by the crisis_analysis router.

# Feature 2: Personalized Journey Models
class JourneyParametersRequest(BaseModel):
    """Request model for journey parameters generation"""
    assessment: Dict[str, Any] = Field(..., description="Assessment results")
    userProfile: Dict[str, Any] = Field(..., description="User profile data")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")

class JourneyParametersResponse(BaseModel):
    """Response model for journey parameters"""
    type: str = Field(..., description="Journey type")
    duration: int = Field(..., description="Journey duration in days")
    timeCommitment: int = Field(..., description="Daily time commitment in minutes")
    primaryLayer: str = Field(..., description="Primary layer focus")
    secondaryLayers: List[str] = Field(..., description="Secondary layers")
    ruqyaLevel: str = Field(..., description="Ruqya integration level")
    communitySupport: bool = Field(..., description="Community support recommendation")
    culturalAdaptations: List[str] = Field(..., description="Cultural adaptations")
    recommendations: List[str] = Field(..., description="AI recommendations")

class JourneyContentRequest(BaseModel):
    """Request model for journey content generation"""
    config: Dict[str, Any] = Field(..., description="Journey configuration")
    userProfile: Dict[str, Any] = Field(..., description="User profile data")
    assessment: Dict[str, Any] = Field(..., description="Assessment results")

class JourneyContentResponse(BaseModel):
    """Response model for journey content"""
    title: str = Field(..., description="Journey title")
    description: str = Field(..., description="Journey description")
    personalizedWelcome: str = Field(..., description="Personalized welcome message")
    days: List[Dict[str, Any]] = Field(..., description="Daily journey content")

class CommunityMatchingRequest(BaseModel):
    """Request model for community matching"""
    userProfile: Dict[str, Any] = Field(..., description="User profile data")
    journeyId: str = Field(..., description="Journey ID")

class CommunityMatchingResponse(BaseModel):
    """Response model for community matching"""
    groupId: Optional[str] = Field(None, description="Matched community group ID")
    mentorId: Optional[str] = Field(None, description="Assigned mentor ID")
    peerConnections: List[str] = Field(default_factory=list, description="Peer connection IDs")
    matchingReason: str = Field(..., description="Reason for the match")

class AdaptiveRecommendationsRequest(BaseModel):
    """Request model for adaptive recommendations"""
    userId: str = Field(..., description="User ID")
    journeyId: str = Field(..., description="Journey ID")
    progress: Dict[str, Any] = Field(..., description="Progress data")
    context: str = Field(..., description="Context for recommendations")

class AdaptiveRecommendationsResponse(BaseModel):
    """Response model for adaptive recommendations"""
    recommendations: List[str] = Field(..., description="Adaptive recommendations")
    adjustments: List[Dict[str, Any]] = Field(default_factory=list, description="Suggested adjustments")
    reasoning: str = Field(..., description="Reasoning for recommendations")

class PersonalizedContentRequest(BaseModel):
    """Request model for personalized content recommendations for Qalb Rescue"""
    user_id: str = Field(..., description="User ID")
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences (e.g., preferred qari, language)")
    past_interactions: List[Dict[str, Any]] = Field(default_factory=list, description="History of user interactions with Qalb Rescue")
    crisis_indicators: List[str] = Field(default_factory=list, description="Real-time crisis indicators detected")

# Updated Pydantic models for structured Quran and Dhikr content
class QuranVerseOutput(BaseModel):
    id: str
    arabic_text: str
    translation_en: str
    surah_name_en: str
    ayah_number: int
    theme: str
    audio_url: Optional[str] = None

class DhikrPhraseOutput(BaseModel):
    id: str
    arabic_text: str
    transliteration_en: str
    translation_en: str
    recommended_count: Optional[int] = None
    theme: str
    audio_url: Optional[str] = None

class PersonalizedContentResponse(BaseModel):
    """Response model for personalized content for Qalb Rescue"""
    quran_verses: List[QuranVerseOutput] = Field(default_factory=list, description="Recommended Quranic verses")
    dhikr_phrases: List[DhikrPhraseOutput] = Field(default_factory=list, description="Recommended Dhikr phrases")
    reasoning: str = Field(..., description="Reasoning for the personalized content")

# Pydantic Models for Feature 1: Understanding Your Inner Landscape - Spiritual Landscape Analysis

class UserProfileData(BaseModel):
    """Represents the user profile data from Feature 0 onboarding."""
    user_id: str
    mental_health_awareness: Optional[Dict[str, Any]] = None
    ruqya_knowledge: Optional[Dict[str, Any]] = None
    spiritual_optimizer: Optional[Dict[str, Any]] = None
    professional_context: Optional[Dict[str, Any]] = None
    demographics: Optional[Dict[str, Any]] = None
    life_circumstances: Optional[Dict[str, Any]] = None
    # Add other fields from UserProfile model in backend/models/UserProfile.ts as needed
    # For now, keeping it somewhat generic to allow flexibility from backend's `userProfile: Json`

class SymptomExperienceData(BaseModel):
    """Represents symptom experience data for one category (e.g., physical, emotional)."""
    symptoms: Optional[List[str]] = Field(default_factory=list)
    intensity: Optional[str] = None # E.g., "mild", "moderate", "severe"
    user_reflection: Optional[str] = None

class SpiritualLandscapeRequest(BaseModel):
    """Request model for the comprehensive spiritual landscape analysis."""
    user_profile: UserProfileData = Field(..., description="User profile from Feature 0 onboarding.")
    physical_experiences: Optional[SymptomExperienceData] = None
    emotional_experiences: Optional[SymptomExperienceData] = None
    mental_experiences: Optional[SymptomExperienceData] = None
    spiritual_experiences: Optional[SymptomExperienceData] = None
    reflections: Optional[Dict[str, str]] = Field(default_factory=dict, description="Collected reflection texts, keyed by step or category.")
    session_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadata like time spent per step.")

class LayerAnalysisOutput(BaseModel):
    """Detailed analysis for a single spiritual layer."""
    layer: str = Field(..., description="The name of the layer (e.g., jism, nafs).")
    insights: List[str] = Field(default_factory=list, description="Key insights for this layer.")
    recommendations: List[str] = Field(default_factory=list, description="Specific recommendations for this layer.")
    islamic_context: str = Field(default="", description="Islamic context or teachings related to this layer.")
    severity_score: int = Field(default=0, description="Calculated severity score for this layer (0-100).")

class SpiritualLandscapeResponse(BaseModel):
    """Response model for the spiritual landscape analysis, aligning with SpiritualAnalysisResult."""
    primary_layer: str = Field(..., description="The primary layer identified as needing attention.")
    layer_insights: Dict[str, LayerAnalysisOutput] = Field(..., description="Detailed analysis for each relevant layer.")
    personalized_message: str = Field(..., description="A personalized message for the user.")
    islamic_insights: List[str] = Field(default_factory=list, description="General Islamic insights relevant to the diagnosis.")
    educational_content: str = Field(..., description="Educational content about the Five Layers and the diagnosis.")
    crisis_level: str = Field(default="none", description="Assessed crisis level (none, low, moderate, high, critical).")
    crisis_indicators: List[str] = Field(default_factory=list, description="Specific crisis indicators detected.")
    immediate_actions: List[str] = Field(default_factory=list, description="Immediate actions recommended if in crisis.")
    next_steps: List[str] = Field(default_factory=list, description="Recommended next steps for the user after diagnosis.")
    recommended_journey_type: str = Field(..., description="The type of healing journey recommended.")
    estimated_healing_duration: int = Field(..., description="Estimated duration of the healing journey in days.")
    confidence: float = Field(..., description="The AI's confidence in this diagnosis (0.0 to 1.0).")

# Pydantic Models for Personalized Assessment Welcome (Feature 1)

class WelcomeAction(BaseModel):
    id: str
    text: str
    description: Optional[str] = None

class PersonalizedWelcomeResponse(BaseModel):
    user_id: str # For consistency, though AI might not strictly need to return it if backend passes it.
    user_type: str = Field(..., description="Determined user type (e.g., clinically_aware, symptom_aware).")
    greeting: str = Field(..., description="Personalized greeting message.")
    introduction: str = Field(..., description="Personalized introduction to the assessment.")
    explanation: Optional[str] = Field(None, description="Explanation of the assessment process tailored to the user.")
    motivation: Optional[str] = Field(None, description="Motivational message.")
    primary_action: WelcomeAction = Field(..., description="Primary call to action (e.g., start assessment).")
    secondary_actions: List[WelcomeAction] = Field(default_factory=list, description="Secondary actions (e.g., learn more, emergency help).")
    # This model structure is designed to be compatible with what mobile-app-v3/src/app/assessment/welcome.tsx expects.

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "qalb-healing-ai"}



# AI Processing Endpoints
@app.post("/analyze-symptoms", response_model=SymptomAnalysisResponse)
async def analyze_symptoms(
    request: SymptomAnalysisRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(verify_token)
):
    """
    Analyze user symptoms using Islamic healing framework
    """
    try:
        # Mock analysis for now - replace with actual AI processing
        analysis_id = f"analysis_{request.user_id}_{hash(str(request.symptoms))}"

        # Determine primary affected layers
        primary_layers = []
        for layer, symptoms in request.symptoms.items():
            if symptoms:
                primary_layers.append(layer)

        # Mock severity calculation
        avg_intensity = sum(request.intensity.values()) / len(request.intensity) if request.intensity else 5
        if avg_intensity <= 3:
            severity = "mild"
        elif avg_intensity <= 6:
            severity = "moderate"
        else:
            severity = "severe"

        # Mock recommendations
        recommended_journey = "7-day-qalb-purification" if "qalb" in primary_layers else "14-day-comprehensive-healing"

        immediate_actions = [
            "Begin with morning dhikr and istighfar",
            "Practice deep breathing with Islamic remembrance",
            "Increase daily prayers and Quran recitation"
        ]

        spotlight = f"Focus on {', '.join(primary_layers[:2])} layers for optimal healing"

        response = SymptomAnalysisResponse(
            analysis_id=analysis_id,
            primary_layers_affected=primary_layers,
            severity_level=severity,
            recommended_journey=recommended_journey,
            immediate_actions=immediate_actions,
            spotlight=spotlight,
            estimated_healing_duration=7 if severity == "mild" else 14 if severity == "moderate" else 21
        )

        # Background task to store analysis
        background_tasks.add_task(store_analysis, analysis_id, request, response)

        return response

    except Exception as e:
        logger.error(f"Error analyzing symptoms: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/recommend-content", response_model=ContentRecommendationResponse)
async def recommend_content(
    request: ContentRecommendationRequest,
    user: dict = Depends(verify_token)
):
    """
    Recommend personalized Islamic content based on user's current state
    """
    try:
        # Mock content recommendation - replace with actual AI
        content_ids = ["content_1", "content_2", "content_3"]
        reasoning = f"Based on your focus on {', '.join(request.healing_focus)}, these contents will support your healing journey"
        priority_order = content_ids.copy()

        return ContentRecommendationResponse(
            content_ids=content_ids,
            reasoning=reasoning,
            priority_order=priority_order
        )

    except Exception as e:
        logger.error(f"Error recommending content: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/generate-journey", response_model=JourneyGenerationResponse)
async def generate_journey(
    request: JourneyGenerationRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(verify_token)
):
    """
    Generate personalized healing journey using AI
    """
    try:
        # Mock journey generation - replace with actual AI
        journey_id = f"journey_{request.user_id}_{hash(str(request.focus_layers))}"

        modules = [
            {
                "day": 1,
                "title": "Foundation Setting",
                "description": "Establish spiritual foundation",
                "practices": ["morning_dhikr", "evening_reflection"],
                "duration": 30
            }
        ]

        daily_practices = [
            {
                "name": "Morning Dhikr",
                "description": "Start day with remembrance of Allah",
                "duration": 15,
                "frequency": "daily"
            }
        ]

        milestones = [
            {
                "day": 7,
                "title": "First Week Completion",
                "description": "Assess progress and adjust practices"
            }
        ]

        response = JourneyGenerationResponse(
            journey_id=journey_id,
            modules=modules,
            daily_practices=daily_practices,
            milestones=milestones
        )

        # Background task to store journey
        background_tasks.add_task(store_journey, journey_id, request, response)

        return response

    except Exception as e:
        logger.error(f"Error generating journey: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# The /analyze-crisis endpoint previously defined here is now handled by the router in endpoints/crisis_analysis.py
# and will be included via app.include_router.

# Feature 2: Personalized Journey Endpoints
@app.post("/journey/generate-parameters", response_model=JourneyParametersResponse)
async def generate_journey_parameters(
    request: JourneyParametersRequest,
    user: dict = Depends(verify_token)
):
    """
    Generate journey parameters based on assessment and user profile
    """
    try:
        # Import the processor here to avoid circular imports
        from .processors.journey_generation import JourneyGenerationProcessor

        processor = JourneyGenerationProcessor()
        parameters = processor.generate_journey_parameters({
            'assessment': request.assessment,
            'userProfile': request.userProfile,
            'preferences': request.preferences
        })

        return JourneyParametersResponse(**parameters)

    except Exception as e:
        logger.error(f"Error generating journey parameters: {str(e)}")
        # Return safe defaults
        return JourneyParametersResponse(
            type='heart_purification',
            duration=21,
            timeCommitment=20,
            primaryLayer='qalb',
            secondaryLayers=[],
            ruqyaLevel='none',
            communitySupport=True,
            culturalAdaptations=[],
            recommendations=['Standard Islamic healing practices']
        )

@app.post("/journey/generate-content", response_model=JourneyContentResponse)
async def generate_journey_content(
    request: JourneyContentRequest,
    user: dict = Depends(verify_token)
):
    """
    Generate complete journey content with daily practices
    """
    try:
        from .processors.journey_generation import JourneyGenerationProcessor

        processor = JourneyGenerationProcessor()
        content = processor.generate_journey_content({
            'config': request.config,
            'userProfile': request.userProfile,
            'assessment': request.assessment
        })

        return JourneyContentResponse(**content)

    except Exception as e:
        logger.error(f"Error generating journey content: {str(e)}")
        return JourneyContentResponse(
            title='Islamic Healing Journey',
            description='A personalized path to spiritual wellness',
            personalizedWelcome='Welcome to your healing journey',
            days=[]
        )

@app.post("/journey/match-community", response_model=CommunityMatchingResponse)
async def match_community_support(
    request: CommunityMatchingRequest,
    user: dict = Depends(verify_token)
):
    """
    Match user with appropriate community support
    """
    try:
        # Mock community matching logic
        user_profile = request.userProfile

        # Determine group based on profile
        group_id = None
        mentor_id = None
        peer_connections = []
        matching_reason = "Standard community support"

        # Crisis support gets priority matching
        if user_profile.get('crisisSupport'):
            group_id = "crisis_support_group"
            mentor_id = "crisis_mentor_1"
            matching_reason = "Crisis support community"

        # Professional matching
        elif user_profile.get('profession'):
            profession = user_profile['profession'].lower()
            if 'healthcare' in profession:
                group_id = "healthcare_professionals"
                matching_reason = "Healthcare professionals support group"
            elif 'teacher' in profession:
                group_id = "educators_group"
                matching_reason = "Educators support group"

        # Cultural matching
        elif user_profile.get('culturalBackground'):
            cultural_bg = user_profile['culturalBackground'].lower()
            if 'south_asian' in cultural_bg:
                group_id = "south_asian_community"
                matching_reason = "Cultural community match"
            elif 'arab' in cultural_bg:
                group_id = "arab_community"
                matching_reason = "Cultural community match"

        # Default general support
        if not group_id:
            group_id = "general_support"
            matching_reason = "General community support"

        return CommunityMatchingResponse(
            groupId=group_id,
            mentorId=mentor_id,
            peerConnections=peer_connections,
            matchingReason=matching_reason
        )

    except Exception as e:
        logger.error(f"Error matching community support: {str(e)}")
        return CommunityMatchingResponse(
            groupId="general_support",
            mentorId=None,
            peerConnections=[],
            matchingReason="Default community assignment"
        )

@app.post("/journey/adaptive-recommendations", response_model=AdaptiveRecommendationsResponse)
async def generate_adaptive_recommendations(
    request: AdaptiveRecommendationsRequest,
    user: dict = Depends(verify_token)
):
    try:
        from .processors.adaptive_journey_processor import AdaptiveJourneyProcessor
        # It's good practice to instantiate processors as needed, or use a dependency injection system for them.
        # For now, direct instantiation is fine.
        # We might need to pass the main practice_library to the AdaptiveJourneyProcessor if it needs it.
        # This requires JourneyGenerationProcessor to be initialized first or practice_library to be loaded globally.
        # For simplicity, let's assume AdaptiveJourneyProcessor can load its own stub or has it passed.

        # To pass the main practice library:
        # 1. Ensure JourneyGenerationProcessor is instantiated (e.g. globally or via Depends)
        #    For this refactor, we'll let AdaptiveJourneyProcessor use its internal stub.
        #    A more robust solution would involve a shared content service/loader.
        # journey_gen_processor = JourneyGenerationProcessor() # Potentially problematic if it loads library on init every time
        # adaptive_processor = AdaptiveJourneyProcessor(practice_library=journey_gen_processor.practice_library)

        adaptive_processor = AdaptiveJourneyProcessor() # Uses its own stubbed library for now

        result_data = adaptive_processor.process_adaptive_recommendations(
            user_id=request.userId,
            journey_id=request.journeyId,
            progress_data=request.progress,
            context=request.context
        )
        return AdaptiveRecommendationsResponse(**result_data)

    except Exception as e:
        logger.error(f"Error generating adaptive recommendations for user {request.userId}, journey {request.journeyId}: {str(e)}", exc_info=True)
        # Consider if a more specific error response should be returned or if a generic one is okay.
        # For now, matching the previous fallback.
        return AdaptiveRecommendationsResponse(
            recommendations=["Continue current practices"],
            adjustments=[],
            reasoning="Error in analysis, maintaining current approach"
        )

# ENDPOINTS FOR FEATURE 1: Understanding Your Inner Landscape

# Import the processor for spiritual landscape analysis
from .processors.spiritual_analysis import spiritual_analysis_processor, SpiritualAnalysisResult
# Import the processor for welcome message generation
from .processors.welcome_generator import welcome_generator_processor


@app.post("/generate-assessment-welcome", response_model=PersonalizedWelcomeResponse)
async def generate_assessment_welcome_endpoint(
    request: UserProfileData, # UserProfileData is the request model for this
    user: dict = Depends(verify_token) # Assuming authentication might be relevant
):
    """
    Generates a personalized welcome message for the Feature 1 assessment
    based on the user's Feature 0 onboarding profile.
    """
    try:
        logger.info(f"Received request for personalized assessment welcome for user: {request.user_id}")

        # The request itself is UserProfileData, which is what the processor expects.
        welcome_response_data = welcome_generator_processor.generate_welcome(
            user_profile=request
        )

        # The processor already returns an object that should match PersonalizedWelcomeResponse.
        # If not, manual mapping would be needed here.
        # Let's assume generate_welcome now returns a PersonalizedWelcomeResponse Pydantic model instance.

        logger.info(f"Personalized assessment welcome generated successfully for user: {request.user_id}")
        return welcome_response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in /generate-assessment-welcome endpoint for user {request.user_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="An internal error occurred during welcome message generation.")


@app.post("/analyze-spiritual-landscape", response_model=SpiritualLandscapeResponse)
async def analyze_spiritual_landscape_endpoint(
    request: SpiritualLandscapeRequest,
    user: dict = Depends(verify_token) # Assuming authentication is needed
):
    """
    Provides a comprehensive spiritual landscape analysis based on user's assessment data.
    This is the primary AI endpoint for Feature 1.
    """
    try:
        logger.info(f"Received request for spiritual landscape analysis for user: {request.user_profile.user_id}")

        # Convert Pydantic request model to dict to pass to the processor,
        # as the processor currently expects a Dict[str, Any].
        # In a future refactor, the processor could also accept Pydantic models directly.
        assessment_data_dict = request.dict()

        analysis_result: SpiritualAnalysisResult = spiritual_analysis_processor.analyze_spiritual_landscape(
            assessment_data_dict
        )

        # Convert the dataclass result from the processor to the Pydantic response model.
        # This ensures the response adheres to the defined API contract.
        # If LayerInsight dataclass and LayerAnalysisOutput Pydantic model are identical in fields,
        # direct conversion is simpler.

        # Manual mapping if structures differ subtly or for clarity:
        layer_insights_output: Dict[str, LayerAnalysisOutput] = {}
        for layer_key, insight_dataclass in analysis_result.layer_insights.items():
            layer_insights_output[layer_key] = LayerAnalysisOutput(
                layer=insight_dataclass.layer,
                insights=insight_dataclass.insights,
                recommendations=insight_dataclass.recommendations,
                islamic_context=insight_dataclass.islamic_context,
                severity_score=insight_dataclass.severity_score
            )

        response_data = SpiritualLandscapeResponse(
            primary_layer=analysis_result.primary_layer,
            layer_insights=layer_insights_output,
            personalized_message=analysis_result.personalized_message,
            islamic_insights=analysis_result.islamic_insights,
            educational_content=analysis_result.educational_content,
            crisis_level=analysis_result.crisis_level,
            crisis_indicators=analysis_result.crisis_indicators,
            immediate_actions=analysis_result.immediate_actions,
            next_steps=analysis_result.next_steps,
            recommended_journey_type=analysis_result.recommended_journey_type,
            estimated_healing_duration=analysis_result.estimated_healing_duration,
            confidence=analysis_result.confidence
        )

        logger.info(f"Spiritual landscape analysis successful for user: {request.user_profile.user_id}")
        return response_data

    except HTTPException:
        # Re-raise HTTPException to let FastAPI handle it
        raise
    except Exception as e:
        logger.error(f"Error in /analyze-spiritual-landscape endpoint for user {request.user_profile.user_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="An internal error occurred during spiritual landscape analysis.")


# Background tasks
async def store_analysis(analysis_id: str, request: SymptomAnalysisRequest, response: SymptomAnalysisResponse):
    """Store analysis results in database"""
    logger.info(f"Storing analysis {analysis_id}")
    # TODO: Implement database storage

async def store_journey(journey_id: str, request: JourneyGenerationRequest, response: JourneyGenerationResponse):
    """Store generated journey in database"""
    logger.info(f"Storing journey {journey_id}")
    # TODO: Implement database storage

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=True
    )
