#!/usr/bin/env python3
"""
Comprehensive test runner for Qalb Healing AI Service
Runs all tests with proper reporting and coverage
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - SUCCESS")
        if result.stdout:
            print("Output:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FAILED")
        print("Error:", e.stderr)
        if e.stdout:
            print("Output:", e.stdout)
        return False

def main():
    """Main test runner"""
    print("🕌 QALB HEALING AI SERVICE - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    # Change to AI service directory
    os.chdir(Path(__file__).parent)
    
    # Ensure directories exist
    os.makedirs("coverage/html", exist_ok=True)
    os.makedirs("reports/html", exist_ok=True)
    
    success_count = 0
    total_tests = 0
    
    # Test categories
    test_commands = [
        {
            "cmd": "python -m pytest tests/test_spiritual_analysis.py -v",
            "desc": "Spiritual Analysis Tests (Core Islamic Framework)"
        },
        {
            "cmd": "python -m pytest tests/test_crisis_detection.py -v",
            "desc": "Crisis Detection Tests (Islamic Safety)"
        },
        {
            "cmd": "python -m pytest tests/test_symptom_analyzer.py -v",
            "desc": "Symptom Analyzer Tests (AI Integration)"
        },
        {
            "cmd": "python -m pytest tests/test_content_recommendations.py -v",
            "desc": "Content Recommendation Tests (Personalization)"
        },
        {
            "cmd": "python -m pytest tests/test_journey_generation.py -v",
            "desc": "Journey Generation Tests (Healing Paths)"
        },
        {
            "cmd": "python -m pytest tests/test_islamic_context.py -v",
            "desc": "Islamic Context Tests (Authenticity)"
        },
        {
            "cmd": "python -m pytest tests/test_api_endpoints.py -v",
            "desc": "API Endpoint Tests (Integration)"
        },
        {
            "cmd": "python -m pytest tests/test_performance.py -v",
            "desc": "Performance Tests (Benchmarking)"
        }
    ]
    
    # Run individual test suites
    for test in test_commands:
        total_tests += 1
        if run_command(test["cmd"], test["desc"]):
            success_count += 1
    
    # Run comprehensive coverage test
    print(f"\n{'='*60}")
    print("🔄 Running Comprehensive Coverage Analysis")
    print(f"{'='*60}")
    
    coverage_cmd = "python -m pytest tests/ --cov=ai_service --cov-report=html:coverage/html --cov-report=xml:coverage/coverage.xml --cov-report=term-missing --html=reports/html/index.html --junitxml=reports/junit.xml"
    
    if run_command(coverage_cmd, "Comprehensive Coverage Analysis"):
        success_count += 1
    total_tests += 1
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL TEST SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Successful: {success_count}/{total_tests}")
    print(f"❌ Failed: {total_tests - success_count}/{total_tests}")
    print(f"📈 Success Rate: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 ALL TESTS PASSED! Qalb Healing AI Service is ready for production.")
        print("📋 Reports available in:")
        print("   - Coverage: coverage/html/index.html")
        print("   - Test Results: reports/html/index.html")
        return 0
    else:
        print(f"\n⚠️  {total_tests - success_count} test suite(s) failed. Please review and fix.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
