import pytest
from ai_service.processors.content_personalization import ContentPersonalizationProcessor

@pytest.fixture
def processor():
    return ContentPersonalizationProcessor()

def test_personalize_content_suicidal_indicators(processor):
    user_preferences = {}
    past_interactions = []
    crisis_indicators = ["suicidal"]
    result = processor.personalize_content(user_preferences, past_interactions, crisis_indicators)
    assert len(result["quran_verses"]) >= 1
    assert any("65:3" in v["reference"] for v in result["quran_verses"])
    assert "addressing feelings of hopelessness and despair" in result["reasoning"]

def test_personalize_content_panic_attack_indicators(processor):
    user_preferences = {}
    past_interactions = []
    crisis_indicators = ["panic attack"]
    result = processor.personalize_content(user_preferences, past_interactions, crisis_indicators)
    assert len(result["quran_verses"]) >= 1
    assert any("13:28" in v["reference"] for v in result["quran_verses"])
    assert any("Subhanallah" in d["phrase"] for d in result["dhikr_phrases"])
    assert "providing calm and remembrance during panic" in result["reasoning"]

def test_personalize_content_past_grounding_interactions(processor):
    user_preferences = {}
    past_interactions = [{"type": "grounding_exercise"}]
    crisis_indicators = []
    result = processor.personalize_content(user_preferences, past_interactions, crisis_indicators)
    assert any("La ilaha illa Allah" in d["phrase"] for d in result["dhikr_phrases"])
    assert "reinforcing grounding techniques" in result["reasoning"]

def test_personalize_content_preferred_qari(processor):
    user_preferences = {"preferred_qari": "Mishary Al-Afasy"}
    past_interactions = []
    crisis_indicators = []
    result = processor.personalize_content(user_preferences, past_interactions, crisis_indicators)
    assert "aligning with preferred recitation style" in result["reasoning"]

def test_personalize_content_no_specific_indicators(processor):
    user_preferences = {}
    past_interactions = []
    crisis_indicators = []
    result = processor.personalize_content(user_preferences, past_interactions, crisis_indicators)
    assert len(result["quran_verses"]) >= 1
    assert len(result["dhikr_phrases"]) >= 1
    assert "providing general spiritual comfort" in result["reasoning"] or "offering general remembrance" in result["reasoning"]
