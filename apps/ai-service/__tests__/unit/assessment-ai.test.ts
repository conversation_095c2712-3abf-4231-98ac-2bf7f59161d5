/**
 * AI Service Unit Tests - Assessment AI
 * Tests AI-powered 5-layer Islamic assessment analysis
 */

import { AssessmentAIService } from '../../src/services/assessment-ai.service';
import { IslamicContextService } from '../../src/services/islamic-context.service';
import { NLPService } from '../../src/services/nlp.service';
import { PersonalizationService } from '../../src/services/personalization.service';

describe('Assessment AI Service Unit Tests', () => {
  let assessmentAIService: AssessmentAIService;
  let islamicContextService: IslamicContextService;
  let nlpService: NLPService;
  let personalizationService: PersonalizationService;

  const mockIslamicContextService = {
    getLayerGuidance: jest.fn(),
    validateIslamicContent: jest.fn(),
    getPersonalizedPractices: jest.fn(),
    getCulturalAdaptation: jest.fn(),
  };

  const mockNLPService = {
    analyzeText: jest.fn(),
    extractKeywords: jest.fn(),
    categorizeSymptom: jest.fn(),
    detectLanguage: jest.fn(),
    calculateSentiment: jest.fn(),
  };

  const mockPersonalizationService = {
    adaptLanguage: jest.fn(),
    personalizeRecommendations: jest.fn(),
    adjustComplexity: jest.fn(),
    generatePersonaResponse: jest.fn(),
  };

  beforeEach(() => {
    islamicContextService = mockIslamicContextService as any;
    nlpService = mockNLPService as any;
    personalizationService = mockPersonalizationService as any;
    assessmentAIService = new AssessmentAIService(
      islamicContextService,
      nlpService,
      personalizationService
    );
    jest.clearAllMocks();
  });

  describe('Symptom Categorization by Islamic Layers', () => {
    it('should categorize physical symptoms to Jism layer', async () => {
      const physicalSymptoms = [
        'headache and migraines',
        'chronic fatigue and tiredness',
        'body pain and aches',
        'sleep disturbances',
        'digestive issues',
      ];

      for (const symptom of physicalSymptoms) {
        mockNLPService.categorizeSymptom.mockResolvedValue({
          layer: 'Jism',
          confidence: 0.9,
          keywords: ['physical', 'body', 'pain'],
        });

        const result = await assessmentAIService.categorizeSymptom(symptom);

        expect(result.layer).toBe('Jism');
        expect(result.confidence).toBeGreaterThan(0.8);
      }
    });

    it('should categorize emotional symptoms to Nafs layer', async () => {
      const emotionalSymptoms = [
        'feeling anxious and worried',
        'experiencing anger and irritability',
        'sadness and depression',
        'mood swings and emotional instability',
        'fear and panic attacks',
      ];

      for (const symptom of emotionalSymptoms) {
        mockNLPService.categorizeSymptom.mockResolvedValue({
          layer: 'Nafs',
          confidence: 0.85,
          keywords: ['emotional', 'feeling', 'mood'],
        });

        const result = await assessmentAIService.categorizeSymptom(symptom);

        expect(result.layer).toBe('Nafs');
        expect(result.confidence).toBeGreaterThan(0.8);
      }
    });

    it('should categorize mental symptoms to Aql layer', async () => {
      const mentalSymptoms = [
        'difficulty concentrating and focusing',
        'memory problems and forgetfulness',
        'confusion and mental fog',
        'racing thoughts and overthinking',
        'decision-making difficulties',
      ];

      for (const symptom of mentalSymptoms) {
        mockNLPService.categorizeSymptom.mockResolvedValue({
          layer: 'Aql',
          confidence: 0.88,
          keywords: ['mental', 'thinking', 'cognitive'],
        });

        const result = await assessmentAIService.categorizeSymptom(symptom);

        expect(result.layer).toBe('Aql');
        expect(result.confidence).toBeGreaterThan(0.8);
      }
    });

    it('should categorize spiritual symptoms to Qalb layer', async () => {
      const spiritualSymptoms = [
        'feeling distant from Allah in prayers',
        'spiritual emptiness and disconnection',
        'difficulty in worship and devotion',
        'loss of faith and religious doubt',
        'heart feeling hardened',
      ];

      for (const symptom of spiritualSymptoms) {
        mockNLPService.categorizeSymptom.mockResolvedValue({
          layer: 'Qalb',
          confidence: 0.92,
          keywords: ['spiritual', 'Allah', 'prayer', 'faith'],
        });

        const result = await assessmentAIService.categorizeSymptom(symptom);

        expect(result.layer).toBe('Qalb');
        expect(result.confidence).toBeGreaterThan(0.8);
      }
    });

    it('should categorize transcendent symptoms to Ruh layer', async () => {
      const transcendentSymptoms = [
        'loss of life purpose and meaning',
        'existential crisis and emptiness',
        'disconnection from divine purpose',
        'spiritual void and meaninglessness',
        'lack of transcendent connection',
      ];

      for (const symptom of transcendentSymptoms) {
        mockNLPService.categorizeSymptom.mockResolvedValue({
          layer: 'Ruh',
          confidence: 0.87,
          keywords: ['purpose', 'meaning', 'transcendent', 'divine'],
        });

        const result = await assessmentAIService.categorizeSymptom(symptom);

        expect(result.layer).toBe('Ruh');
        expect(result.confidence).toBeGreaterThan(0.8);
      }
    });

    it('should handle multilingual symptom descriptions', async () => {
      const multilingualSymptoms = [
        { text: 'أشعر بالقلق والخوف', language: 'ar', expectedLayer: 'Nafs' },
        { text: 'میں پریشان ہوں', language: 'ur', expectedLayer: 'Nafs' },
        { text: 'Kaygı ve endişe hissediyorum', language: 'tr', expectedLayer: 'Nafs' },
      ];

      for (const symptom of multilingualSymptoms) {
        mockNLPService.detectLanguage.mockResolvedValue(symptom.language);
        mockNLPService.categorizeSymptom.mockResolvedValue({
          layer: symptom.expectedLayer,
          confidence: 0.85,
          language: symptom.language,
        });

        const result = await assessmentAIService.categorizeSymptom(symptom.text);

        expect(result.layer).toBe(symptom.expectedLayer);
        expect(result.language).toBe(symptom.language);
      }
    });
  });

  describe('Comprehensive 5-Layer Analysis', () => {
    it('should generate complete analysis across all layers', async () => {
      const assessmentData = {
        selectedSymptoms: [
          'chronic fatigue', // Jism
          'anxiety and worry', // Nafs
          'difficulty concentrating', // Aql
          'feeling distant from Allah', // Qalb
          'loss of life purpose', // Ruh
        ],
        reflections: [
          'I feel overwhelmed with daily tasks',
          'My prayers feel mechanical and empty',
          'I struggle to find meaning in my work',
        ],
        userProfile: {
          islamicBackground: 'practicing',
          profession: 'Teacher',
          age: 35,
        },
      };

      const expectedAnalysis = {
        layers: {
          Jism: {
            symptoms: ['chronic fatigue'],
            severity: 'moderate',
            recommendations: ['proper_rest', 'exercise', 'nutrition'],
            islamicPractices: ['wudu_meditation', 'healthy_eating_sunnah'],
            guidance: 'Take care of your body as it is an amanah from Allah',
          },
          Nafs: {
            symptoms: ['anxiety and worry'],
            severity: 'moderate',
            recommendations: ['dhikr', 'breathing_exercises', 'mindfulness'],
            islamicPractices: ['istighfar', 'tasbih', 'seeking_refuge'],
            guidance: 'Remember that Allah is with those who are patient',
          },
          Aql: {
            symptoms: ['difficulty concentrating'],
            severity: 'mild',
            recommendations: ['mental_exercises', 'structured_thinking'],
            islamicPractices: ['quran_study', 'contemplation', 'seeking_knowledge'],
            guidance: 'Seek knowledge and clarity through Islamic learning',
          },
          Qalb: {
            symptoms: ['feeling distant from Allah'],
            severity: 'moderate',
            recommendations: ['prayer_improvement', 'quran_recitation'],
            islamicPractices: ['tahajjud', 'dua', 'dhikr_circles'],
            guidance: 'Draw closer to Allah through sincere worship',
          },
          Ruh: {
            symptoms: ['loss of life purpose'],
            severity: 'moderate',
            recommendations: ['purpose_exploration', 'service_to_others'],
            islamicPractices: ['charity', 'community_service', 'spiritual_mentorship'],
            guidance: 'Remember your purpose as a servant and khalifa of Allah',
          },
        },
        overallAssessment: 'Moderate challenges across multiple layers requiring holistic Islamic approach',
        islamicGuidance: 'Focus on strengthening your relationship with Allah through balanced spiritual practices',
        personalizedRecommendations: [
          'Begin with gentle dhikr practices',
          'Establish consistent prayer routine',
          'Seek community support',
          'Focus on physical wellness',
        ],
      };

      mockNLPService.analyzeText.mockResolvedValue({
        layerDistribution: {
          Jism: 0.2,
          Nafs: 0.3,
          Aql: 0.1,
          Qalb: 0.25,
          Ruh: 0.15,
        },
        overallSentiment: -0.4,
        keyThemes: ['overwhelm', 'spiritual_disconnection', 'purpose_seeking'],
      });

      mockIslamicContextService.getLayerGuidance.mockImplementation((layer) => ({
        practices: expectedAnalysis.layers[layer].islamicPractices,
        guidance: expectedAnalysis.layers[layer].guidance,
      }));

      const result = await assessmentAIService.generateComprehensiveAnalysis(assessmentData);

      expect(result.layers).toBeDefined();
      expect(Object.keys(result.layers)).toHaveLength(5);
      expect(result.overallAssessment).toContain('holistic Islamic approach');
      expect(result.islamicGuidance).toContain('Allah');
    });

    it('should adapt analysis language based on user persona', async () => {
      const professionalUser = {
        selectedSymptoms: ['work stress'],
        userProfile: {
          profession: 'Healthcare Professional',
          islamicBackground: 'practicing',
          educationLevel: 'graduate',
        },
      };

      mockPersonalizationService.adaptLanguage.mockResolvedValue({
        style: 'clinical_islamic',
        terminology: 'professional',
        complexity: 'advanced',
      });

      mockPersonalizationService.generatePersonaResponse.mockResolvedValue({
        overallAssessment: 'Clinical assessment indicates work-related stress with Islamic therapeutic interventions recommended',
        language: 'Evidence-based Islamic practices can effectively address professional burnout',
        recommendations: ['mindfulness-based_islamic_therapy', 'professional_support_groups'],
      });

      const result = await assessmentAIService.generateComprehensiveAnalysis(professionalUser);

      expect(result.overallAssessment).toContain('clinical');
      expect(result.language.style).toBe('clinical_islamic');
      expect(result.recommendations).toContain('mindfulness-based_islamic_therapy');
    });

    it('should provide culturally sensitive analysis', async () => {
      const traditionalUser = {
        selectedSymptoms: ['sadness', 'worry'],
        userProfile: {
          islamicBackground: 'traditional',
          culturalBackground: 'south_asian',
          language: 'ur',
        },
      };

      mockIslamicContextService.getCulturalAdaptation.mockResolvedValue({
        approach: 'gentle_traditional',
        familyInvolvement: true,
        communitySupport: true,
        respectfulLanguage: true,
      });

      mockPersonalizationService.generatePersonaResponse.mockResolvedValue({
        overallAssessment: 'May Allah grant you ease and healing through traditional Islamic practices',
        culturalSensitivity: 'high',
        familyGuidance: 'Include family in healing process with respect for traditional values',
      });

      const result = await assessmentAIService.generateComprehensiveAnalysis(traditionalUser);

      expect(result.overallAssessment).toContain('May Allah grant you ease');
      expect(result.culturalAdaptation.approach).toBe('gentle_traditional');
      expect(result.culturalAdaptation.familyInvolvement).toBe(true);
    });
  });

  describe('Islamic Content Integration', () => {
    it('should provide authentic Islamic practices for each layer', async () => {
      const layers = ['Jism', 'Nafs', 'Aql', 'Qalb', 'Ruh'];

      for (const layer of layers) {
        const layerGuidance = {
          practices: [
            {
              name: 'Layer-specific practice',
              arabic: 'Arabic text',
              transliteration: 'Transliteration',
              translation: 'English translation',
              source: 'Authentic source',
            },
          ],
          verses: [
            {
              arabic: 'Quranic verse',
              reference: 'Quran 2:255',
              translation: 'Verse translation',
            },
          ],
          duas: [
            {
              arabic: 'Du\'a text',
              source: 'Sahih Bukhari',
              translation: 'Du\'a translation',
            },
          ],
        };

        mockIslamicContextService.getLayerGuidance.mockResolvedValue(layerGuidance);

        const result = await islamicContextService.getLayerGuidance(layer);

        expect(result.practices).toBeDefined();
        expect(result.verses).toBeDefined();
        expect(result.duas).toBeDefined();

        // Validate Islamic content authenticity
        result.practices.forEach((practice: any) => {
          expect(practice.source).toBeDefined();
        });

        result.verses.forEach((verse: any) => {
          expect(verse.reference).toMatch(/Quran \d+:\d+/);
        });

        result.duas.forEach((dua: any) => {
          expect(['Sahih Bukhari', 'Sahih Muslim', 'Abu Dawud', 'Tirmidhi']).toContain(dua.source);
        });
      }
    });

    it('should validate all Islamic content for authenticity', async () => {
      const islamicContent = {
        verses: [
          {
            arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
            reference: 'Quran 65:2',
            translation: 'And whoever fears Allah - He will make for him a way out',
          },
        ],
        duas: [
          {
            arabic: 'اللَّهُمَّ أَذْهِبِ الْبَأْسَ رَبَّ النَّاسِ',
            source: 'Sahih Bukhari',
            translation: 'O Allah, remove the hardship, O Lord of mankind',
          },
        ],
        practices: [
          {
            name: 'Morning Dhikr',
            arabic: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
            count: 33,
            source: 'Sunnah',
          },
        ],
      };

      mockIslamicContextService.validateIslamicContent.mockResolvedValue({
        valid: true,
        verseAuthentic: true,
        duaAuthentic: true,
        practiceAuthentic: true,
        sources: ['Quran', 'Sahih Bukhari', 'Sunnah'],
      });

      const validation = await islamicContextService.validateIslamicContent(islamicContent);

      expect(validation.valid).toBe(true);
      expect(validation.verseAuthentic).toBe(true);
      expect(validation.duaAuthentic).toBe(true);
      expect(validation.practiceAuthentic).toBe(true);
    });
  });

  describe('Personalization and Adaptation', () => {
    it('should personalize recommendations based on user profile', async () => {
      const userProfiles = [
        {
          profile: {
            islamicBackground: 'new_muslim',
            practiceLevel: 'beginner',
            timeAvailability: 'limited',
          },
          expectedAdaptations: ['simple_practices', 'basic_explanations', 'gentle_introduction'],
        },
        {
          profile: {
            islamicBackground: 'practicing',
            practiceLevel: 'intermediate',
            timeAvailability: 'moderate',
          },
          expectedAdaptations: ['balanced_practices', 'moderate_complexity', 'structured_approach'],
        },
        {
          profile: {
            islamicBackground: 'traditional',
            practiceLevel: 'advanced',
            timeAvailability: 'extensive',
          },
          expectedAdaptations: ['traditional_methods', 'advanced_practices', 'comprehensive_approach'],
        },
      ];

      for (const test of userProfiles) {
        mockPersonalizationService.personalizeRecommendations.mockResolvedValue({
          adaptations: test.expectedAdaptations,
          practices: test.expectedAdaptations.map(adaptation => ({
            type: adaptation,
            difficulty: test.profile.practiceLevel,
          })),
        });

        const result = await personalizationService.personalizeRecommendations(
          ['generic_recommendation'],
          test.profile
        );

        expect(result.adaptations).toEqual(test.expectedAdaptations);
      }
    });

    it('should adjust complexity based on user education and experience', async () => {
      const complexityTests = [
        {
          user: { educationLevel: 'high_school', mentalHealthExperience: 'none' },
          expectedComplexity: 'simple',
        },
        {
          user: { educationLevel: 'graduate', mentalHealthExperience: 'some' },
          expectedComplexity: 'moderate',
        },
        {
          user: { educationLevel: 'postgraduate', mentalHealthExperience: 'professional' },
          expectedComplexity: 'advanced',
        },
      ];

      for (const test of complexityTests) {
        mockPersonalizationService.adjustComplexity.mockResolvedValue({
          complexity: test.expectedComplexity,
          language: test.expectedComplexity === 'simple' ? 'basic' : 'technical',
          explanationDepth: test.expectedComplexity,
        });

        const result = await personalizationService.adjustComplexity(
          'generic_content',
          test.user
        );

        expect(result.complexity).toBe(test.expectedComplexity);
      }
    });
  });

  describe('Machine Learning Model Performance', () => {
    it('should maintain high accuracy in layer categorization', async () => {
      const testCases = [
        { symptom: 'chest pain and shortness of breath', expectedLayer: 'Jism', confidence: 0.95 },
        { symptom: 'feeling angry and irritable', expectedLayer: 'Nafs', confidence: 0.92 },
        { symptom: 'memory problems and confusion', expectedLayer: 'Aql', confidence: 0.88 },
        { symptom: 'feeling disconnected from Allah', expectedLayer: 'Qalb', confidence: 0.94 },
        { symptom: 'loss of life purpose and meaning', expectedLayer: 'Ruh', confidence: 0.89 },
      ];

      let correctPredictions = 0;
      let totalConfidence = 0;

      for (const testCase of testCases) {
        mockNLPService.categorizeSymptom.mockResolvedValue({
          layer: testCase.expectedLayer,
          confidence: testCase.confidence,
        });

        const result = await assessmentAIService.categorizeSymptom(testCase.symptom);

        if (result.layer === testCase.expectedLayer) {
          correctPredictions++;
        }
        totalConfidence += result.confidence;
      }

      const accuracy = correctPredictions / testCases.length;
      const avgConfidence = totalConfidence / testCases.length;

      expect(accuracy).toBeGreaterThan(0.9); // 90% accuracy
      expect(avgConfidence).toBeGreaterThan(0.85); // 85% average confidence
    });

    it('should handle edge cases and ambiguous symptoms', async () => {
      const edgeCases = [
        'feeling tired but also spiritually empty',
        'anxiety about prayer performance',
        'physical pain affecting spiritual practice',
        'mental confusion during worship',
      ];

      for (const symptom of edgeCases) {
        mockNLPService.categorizeSymptom.mockResolvedValue({
          primaryLayer: 'Nafs',
          secondaryLayer: 'Qalb',
          confidence: 0.75,
          ambiguous: true,
        });

        const result = await assessmentAIService.categorizeSymptom(symptom);

        expect(result.primaryLayer).toBeDefined();
        expect(result.secondaryLayer).toBeDefined();
        expect(result.ambiguous).toBe(true);
      }
    });

    it('should continuously improve through feedback', async () => {
      const feedbackData = [
        { symptom: 'test symptom 1', predicted: 'Nafs', actual: 'Qalb', correct: false },
        { symptom: 'test symptom 2', predicted: 'Jism', actual: 'Jism', correct: true },
        { symptom: 'test symptom 3', predicted: 'Aql', actual: 'Aql', correct: true },
      ];

      for (const feedback of feedbackData) {
        await assessmentAIService.updateModelWithFeedback(
          feedback.symptom,
          feedback.predicted,
          feedback.actual
        );
      }

      const modelStats = await assessmentAIService.getModelPerformanceStats();

      expect(modelStats.totalFeedback).toBe(3);
      expect(modelStats.accuracy).toBeCloseTo(0.67, 2); // 2/3 correct
      expect(modelStats.lastUpdated).toBeDefined();
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle NLP service failures gracefully', async () => {
      mockNLPService.categorizeSymptom.mockRejectedValue(new Error('NLP service unavailable'));

      const result = await assessmentAIService.categorizeSymptom('test symptom');

      expect(result.fallbackUsed).toBe(true);
      expect(result.layer).toBeDefined(); // Should provide fallback categorization
      expect(result.confidence).toBeLessThan(0.7); // Lower confidence for fallback
    });

    it('should handle Islamic context service failures', async () => {
      mockIslamicContextService.getLayerGuidance.mockRejectedValue(
        new Error('Islamic context service unavailable')
      );

      const assessmentData = {
        selectedSymptoms: ['anxiety'],
        userProfile: { islamicBackground: 'practicing' },
      };

      const result = await assessmentAIService.generateComprehensiveAnalysis(assessmentData);

      expect(result.fallbackGuidance).toBeDefined();
      expect(result.islamicGuidance).toContain('basic Islamic principles');
    });

    it('should handle malformed input data', async () => {
      const malformedInputs = [
        null,
        undefined,
        { selectedSymptoms: null },
        { selectedSymptoms: [], userProfile: null },
        { selectedSymptoms: [''], userProfile: {} },
      ];

      for (const input of malformedInputs) {
        const result = await assessmentAIService.generateComprehensiveAnalysis(input as any);

        expect(result.error).toBeDefined();
        expect(result.inputValidation).toBe(false);
      }
    });

    it('should handle timeout scenarios', async () => {
      mockNLPService.analyzeText.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      const result = await assessmentAIService.generateComprehensiveAnalysis({
        selectedSymptoms: ['test'],
        userProfile: {},
      });

      expect(result.timeout).toBe(true);
      expect(result.fallbackUsed).toBe(true);
    });
  });
});
