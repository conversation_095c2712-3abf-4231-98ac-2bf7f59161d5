/**
 * AI Service Unit Tests - Crisis Detection
 * Tests AI-powered crisis detection algorithms and Islamic context integration
 */

import { CrisisDetectionService } from '../../src/services/crisis-detection.service';
import { IslamicContextService } from '../../src/services/islamic-context.service';
import { NLPService } from '../../src/services/nlp.service';
import { EmergencyResponseService } from '../../src/services/emergency-response.service';

describe('Crisis Detection AI Service Unit Tests', () => {
  let crisisDetectionService: CrisisDetectionService;
  let islamicContextService: IslamicContextService;
  let nlpService: NLPService;

  const mockIslamicContextService = {
    getComfortVerse: jest.fn(),
    getHealingDua: jest.fn(),
    getCrisisSupport: jest.fn(),
    validateIslamicContent: jest.fn(),
  };

  const mockNLPService = {
    analyzeText: jest.fn(),
    extractKeywords: jest.fn(),
    calculateSentiment: jest.fn(),
    detectLanguage: jest.fn(),
  };

  beforeEach(() => {
    islamicContextService = mockIslamicContextService as any;
    nlpService = mockNLPService as any;
    crisisDetectionService = new CrisisDetectionService(
      islamicContextService,
      nlpService
    );
    jest.clearAllMocks();
  });

  describe('Crisis Keyword Detection', () => {
    it('should detect high-risk crisis keywords', async () => {
      const crisisTexts = [
        'I want to kill myself',
        'I am going to harm myself',
        'I want to end my life',
        'I am planning to commit suicide',
        'I have thoughts of self-harm',
        'I feel like dying',
        'I want to hurt myself',
        'Life is not worth living',
      ];

      for (const text of crisisTexts) {
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: ['self-harm', 'suicide', 'death'],
          sentiment: { score: -0.9, magnitude: 0.8 },
          confidence: 0.95,
        });

        const result = await crisisDetectionService.analyzeText(text);

        expect(result.crisisDetected).toBe(true);
        expect(result.severity).toBe('critical');
        expect(result.confidence).toBeGreaterThan(0.8);
      }
    });

    it('should detect moderate-risk expressions', async () => {
      const moderateTexts = [
        'I feel hopeless',
        'Nothing matters anymore',
        'I cannot go on like this',
        'I feel trapped',
        'There is no way out',
        'I am worthless',
        'Nobody cares about me',
      ];

      for (const text of moderateTexts) {
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: ['hopeless', 'trapped'],
          sentiment: { score: -0.7, magnitude: 0.6 },
          confidence: 0.75,
        });

        const result = await crisisDetectionService.analyzeText(text);

        expect(result.crisisDetected).toBe(true);
        expect(result.severity).toBe('moderate');
        expect(result.confidence).toBeGreaterThan(0.6);
      }
    });

    it('should not trigger false positives for normal expressions', async () => {
      const normalTexts = [
        'I am feeling a bit sad today',
        'I have some stress at work',
        'I need help with anxiety',
        'I am going through a difficult time',
        'I feel overwhelmed sometimes',
        'I need support',
      ];

      for (const text of normalTexts) {
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: [],
          sentiment: { score: -0.3, magnitude: 0.4 },
          confidence: 0.6,
        });

        const result = await crisisDetectionService.analyzeText(text);

        expect(result.crisisDetected).toBe(false);
        expect(result.severity).toBe('low');
      }
    });

    it('should handle Arabic crisis expressions', async () => {
      const arabicCrisisTexts = [
        'أريد أن أؤذي نفسي', // I want to hurt myself
        'أشعر باليأس التام', // I feel complete despair
        'لا أستطيع الاستمرار', // I cannot continue
      ];

      for (const text of arabicCrisisTexts) {
        mockNLPService.detectLanguage.mockResolvedValue('ar');
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: ['أؤذي', 'يأس'],
          sentiment: { score: -0.8, magnitude: 0.7 },
          confidence: 0.85,
        });

        const result = await crisisDetectionService.analyzeText(text);

        expect(result.crisisDetected).toBe(true);
        expect(result.language).toBe('ar');
        expect(result.severity).toBe('high');
      }
    });
  });

  describe('Contextual Analysis', () => {
    it('should analyze crisis context and triggers', async () => {
      const contextualText =
        'Ever since my father passed away, I have been having thoughts of joining him. I feel like there is no point in living without him.';

      mockNLPService.analyzeText.mockResolvedValue({
        crisisKeywords: ['thoughts of joining', 'no point in living'],
        context: {
          triggers: ['death of loved one', 'grief'],
          emotions: ['sadness', 'despair', 'loneliness'],
          timeframe: 'recent',
        },
        sentiment: { score: -0.85, magnitude: 0.8 },
        confidence: 0.9,
      });

      const result = await crisisDetectionService.analyzeText(contextualText);

      expect(result.crisisDetected).toBe(true);
      expect(result.context.triggers).toContain('death of loved one');
      expect(result.context.emotions).toContain('grief');
      expect(result.recommendations).toContain('grief_counseling');
    });

    it('should identify Islamic coping mechanisms mentioned', async () => {
      const islamicCopingText =
        'I have been praying and making dua, but I still feel hopeless. Even reading Quran does not help anymore.';

      mockNLPService.analyzeText.mockResolvedValue({
        crisisKeywords: ['hopeless', 'does not help'],
        islamicElements: ['praying', 'dua', 'Quran'],
        sentiment: { score: -0.6, magnitude: 0.7 },
        confidence: 0.8,
      });

      const result = await crisisDetectionService.analyzeText(
        islamicCopingText
      );

      expect(result.crisisDetected).toBe(true);
      expect(result.islamicContext.copingMechanisms).toContain('prayer');
      expect(result.islamicContext.copingMechanisms).toContain('dua');
      expect(result.islamicContext.spiritualStruggle).toBe(true);
    });

    it('should detect spiritual crisis indicators', async () => {
      const spiritualCrisisTexts = [
        'I feel like Allah has abandoned me',
        'My prayers feel empty and meaningless',
        'I have lost my faith completely',
        'I feel disconnected from Allah',
        "I cannot feel Allah's presence anymore",
      ];

      for (const text of spiritualCrisisTexts) {
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: ['abandoned', 'empty', 'lost faith'],
          islamicElements: ['Allah', 'prayers', 'faith'],
          spiritualCrisis: true,
          confidence: 0.85,
        });

        const result = await crisisDetectionService.analyzeText(text);

        expect(result.crisisDetected).toBe(true);
        expect(result.islamicContext.spiritualCrisis).toBe(true);
        expect(result.recommendations).toContain('spiritual_counseling');
      }
    });
  });

  describe('Severity Assessment', () => {
    it('should calculate crisis severity based on multiple factors', async () => {
      const severityTests = [
        {
          text: 'I am planning to kill myself tonight',
          expectedSeverity: 'critical',
          factors: ['immediate_plan', 'specific_method', 'timeline'],
        },
        {
          text: 'I have been thinking about suicide lately',
          expectedSeverity: 'high',
          factors: ['suicidal_ideation', 'recurring_thoughts'],
        },
        {
          text: 'Sometimes I wish I was not alive',
          expectedSeverity: 'moderate',
          factors: ['passive_ideation', 'occasional_thoughts'],
        },
        {
          text: 'I feel sad and hopeless',
          expectedSeverity: 'low',
          factors: ['emotional_distress', 'no_specific_plan'],
        },
      ];

      for (const test of severityTests) {
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: test.factors,
          severity: test.expectedSeverity,
          confidence: 0.8,
        });

        const result = await crisisDetectionService.analyzeText(test.text);

        expect(result.severity).toBe(test.expectedSeverity);
        expect(result.riskFactors).toEqual(
          expect.arrayContaining(test.factors)
        );
      }
    });

    it('should consider protective factors in severity assessment', async () => {
      const textWithProtectiveFactors =
        'I have thoughts of suicide but I would never act on them because of my children and my faith in Allah.';

      mockNLPService.analyzeText.mockResolvedValue({
        crisisKeywords: ['thoughts of suicide'],
        protectiveFactors: ['children', 'faith', 'religious_beliefs'],
        severity: 'moderate', // Reduced due to protective factors
        confidence: 0.8,
      });

      const result = await crisisDetectionService.analyzeText(
        textWithProtectiveFactors
      );

      expect(result.crisisDetected).toBe(true);
      expect(result.severity).toBe('moderate');
      expect(result.protectiveFactors).toContain('religious_beliefs');
      expect(result.protectiveFactors).toContain('family_responsibility');
    });
  });

  describe('Islamic Context Integration', () => {
    it('should provide appropriate Islamic comfort for crisis', async () => {
      const crisisText = 'I want to end my life';

      mockIslamicContextService.getComfortVerse.mockResolvedValue({
        arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
        transliteration: "Wa man yattaqi Allaha yaj'al lahu makhrajan",
        translation: 'And whoever fears Allah - He will make for him a way out',
        reference: 'Quran 65:2',
        context: 'hope_and_relief',
      });

      mockIslamicContextService.getHealingDua.mockResolvedValue({
        arabic: 'اللَّهُمَّ أَذْهِبِ الْبَأْسَ رَبَّ النَّاسِ',
        transliteration: "Allahumma adhhib al-ba'sa rabba an-nas",
        translation: 'O Allah, remove the hardship, O Lord of mankind',
        source: 'Sahih Bukhari',
      });

      const result = await crisisDetectionService.analyzeTextWithIslamicContext(
        crisisText
      );

      expect(result.islamicSupport.comfortVerse).toBeDefined();
      expect(result.islamicSupport.healingDua).toBeDefined();
      expect(result.islamicSupport.comfortVerse.reference).toBe('Quran 65:2');
    });

    it('should validate Islamic content authenticity', async () => {
      const islamicContent = {
        verse: {
          arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
          reference: 'Quran 65:2',
        },
        dua: {
          arabic: 'اللَّهُمَّ أَذْهِبِ الْبَأْسَ',
          source: 'Sahih Bukhari',
        },
      };

      mockIslamicContextService.validateIslamicContent.mockResolvedValue({
        valid: true,
        verseAuthentic: true,
        duaAuthentic: true,
        sources: ['Quran', 'Sahih Bukhari'],
      });

      const validation = await islamicContextService.validateIslamicContent(
        islamicContent
      );

      expect(validation.valid).toBe(true);
      expect(validation.verseAuthentic).toBe(true);
      expect(validation.duaAuthentic).toBe(true);
    });

    it('should provide culturally appropriate crisis intervention', async () => {
      const culturalContexts = [
        {
          background: 'traditional',
          expectedApproach: 'gentle_traditional',
          expectedElements: [
            'family_involvement',
            'community_support',
            'traditional_healing',
          ],
        },
        {
          background: 'practicing',
          expectedApproach: 'islamic_integrated',
          expectedElements: [
            'spiritual_counseling',
            'islamic_therapy',
            'religious_guidance',
          ],
        },
        {
          background: 'cultural',
          expectedApproach: 'culturally_sensitive',
          expectedElements: [
            'cultural_awareness',
            'respectful_approach',
            'family_consideration',
          ],
        },
      ];

      for (const context of culturalContexts) {
        mockIslamicContextService.getCrisisSupport.mockResolvedValue({
          approach: context.expectedApproach,
          interventions: context.expectedElements,
          culturalConsiderations: true,
        });

        const support = await islamicContextService.getCrisisSupport(
          context.background
        );

        expect(support.approach).toBe(context.expectedApproach);
        expect(support.interventions).toEqual(
          expect.arrayContaining(context.expectedElements)
        );
      }
    });
  });

  describe('Machine Learning Model Performance', () => {
    it('should maintain high accuracy for crisis detection', async () => {
      const testCases = [
        { text: 'I want to kill myself', expected: true, confidence: 0.95 },
        { text: 'I feel sad today', expected: false, confidence: 0.8 },
        { text: 'I have suicidal thoughts', expected: true, confidence: 0.9 },
        { text: 'I need help with stress', expected: false, confidence: 0.85 },
      ];

      let correctPredictions = 0;
      let totalConfidence = 0;

      for (const testCase of testCases) {
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: testCase.expected ? ['crisis_keyword'] : [],
          confidence: testCase.confidence,
        });

        const result = await crisisDetectionService.analyzeText(testCase.text);

        if (result.crisisDetected === testCase.expected) {
          correctPredictions++;
        }
        totalConfidence += result.confidence;
      }

      const accuracy = correctPredictions / testCases.length;
      const avgConfidence = totalConfidence / testCases.length;

      expect(accuracy).toBeGreaterThan(0.9); // 90% accuracy threshold
      expect(avgConfidence).toBeGreaterThan(0.8); // 80% confidence threshold
    });

    it('should handle edge cases and ambiguous text', async () => {
      const edgeCases = [
        'I could kill for a good meal', // Metaphorical usage
        'This homework is killing me', // Hyperbolic expression
        'I am dying to see the movie', // Idiomatic expression
        'I feel like I am drowning in work', // Metaphorical distress
      ];

      for (const text of edgeCases) {
        mockNLPService.analyzeText.mockResolvedValue({
          crisisKeywords: [],
          metaphoricalUsage: true,
          confidence: 0.7,
        });

        const result = await crisisDetectionService.analyzeText(text);

        expect(result.crisisDetected).toBe(false);
        expect(result.metaphoricalUsage).toBe(true);
      }
    });

    it('should continuously learn and improve accuracy', async () => {
      const feedbackData = [
        {
          text: 'I want to hurt myself',
          prediction: true,
          actual: true,
          correct: true,
        },
        {
          text: 'I feel overwhelmed',
          prediction: true,
          actual: false,
          correct: false,
        },
        {
          text: 'Life is meaningless',
          prediction: false,
          actual: true,
          correct: false,
        },
      ];

      for (const feedback of feedbackData) {
        await crisisDetectionService.updateModelWithFeedback(
          feedback.text,
          feedback.prediction,
          feedback.actual
        );
      }

      // Verify model learning mechanism
      const modelStats =
        await crisisDetectionService.getModelPerformanceStats();
      expect(modelStats.totalFeedback).toBe(3);
      expect(modelStats.accuracy).toBeDefined();
      expect(modelStats.lastUpdated).toBeDefined();
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle NLP service failures gracefully', async () => {
      mockNLPService.analyzeText.mockRejectedValue(
        new Error('NLP service unavailable')
      );

      const result = await crisisDetectionService.analyzeText('test text');

      expect(result.fallbackUsed).toBe(true);
      expect(result.crisisDetected).toBeDefined(); // Should provide fallback detection
      expect(result.confidence).toBeLessThan(0.7); // Lower confidence for fallback
    });

    it('should handle Islamic context service failures', async () => {
      mockIslamicContextService.getComfortVerse.mockRejectedValue(
        new Error('Islamic context service unavailable')
      );

      const result = await crisisDetectionService.analyzeTextWithIslamicContext(
        'crisis text'
      );

      expect(result.islamicSupport.fallbackUsed).toBe(true);
      expect(result.islamicSupport.basicSupport).toBeDefined();
    });

    it('should handle malformed input gracefully', async () => {
      const malformedInputs = [null, undefined, '', '   ', 123, {}, []];

      for (const input of malformedInputs) {
        const result = await crisisDetectionService.analyzeText(input as any);

        expect(result.error).toBeDefined();
        expect(result.crisisDetected).toBe(false);
        expect(result.inputValidation).toBe(false);
      }
    });
  });
});
