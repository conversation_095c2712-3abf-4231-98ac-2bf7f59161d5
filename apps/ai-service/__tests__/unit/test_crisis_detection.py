import pytest
from ai_service.processors.crisis_detection import detect_crisis_indicators

def test_detect_crisis_indicators_immediate_intervention():
    user_input = "I am having a panic attack and can't breathe. I feel like ending it all."
    behavior_patterns = ["rapid_app_switching", "repeated_emergency_access"]
    result = detect_crisis_indicators(user_input, behavior_patterns)
    assert result == "immediate_intervention"

def test_detect_crisis_indicators_enhanced_support():
    user_input = "I feel hopeless and worthless. I can't take it anymore."
    behavior_patterns = []
    result = detect_crisis_indicators(user_input, behavior_patterns)
    assert result == "enhanced_support"

def test_detect_crisis_indicators_standard_support():
    user_input = "I'm feeling a bit down today."
    behavior_patterns = []
    result = detect_crisis_indicators(user_input, behavior_patterns)
    assert result == "standard_support"

def test_detect_crisis_indicators_spiritual_crisis():
    user_input = "Allah has abandoned me. I feel lost and my prayers are meaningless."
    behavior_patterns = []
    result = detect_crisis_indicators(user_input, behavior_patterns)
    assert result == "enhanced_support" # Based on current simplified logic, might be enhanced

def test_detect_crisis_indicators_no_crisis():
    user_input = "I am having a good day."
    behavior_patterns = []
    result = detect_crisis_indicators(user_input, behavior_patterns)
    assert result == "standard_support"
