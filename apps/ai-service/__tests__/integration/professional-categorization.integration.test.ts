import request from 'supertest';
import { Express } from 'express';
import { setup, teardown } from '../setup';
import { OnboardingService } from '../../../../backend/src/services/onboarding.service';

describe('Professional Categorization End-to-End Test', () => {
  let app: Express;
  let onboardingService: OnboardingService;

  beforeAll(async () => {
    const setupResult = await setup();
    app = setupResult.app;
    onboardingService = new OnboardingService();
  });

  afterAll(async () => {
    await teardown();
  });

  it('should handle the healthcare professional flow correctly', async () => {
    // 1. Start onboarding
    const startResponse = await request(app)
      .post('/api/onboarding/start')
      .send({ userId: 'prof-test-user-1' });

    expect(startResponse.status).toBe(200);
    const { sessionId } = startResponse.body.session;

    // 2. Respond to welcome
    await request(app)
      .post('/api/onboarding/respond')
      .send({ sessionId, stepId: 'welcome', response: { selection: 'begin' }, timeSpent: 2 });

    // 3. Respond to mental_health_awareness
    await request(app)
        .post('/api/onboarding/respond')
        .send({ sessionId, stepId: 'mental_health_awareness', response: { mental_health_awareness: 'symptom_aware' }, timeSpent: 5 });

    // 4. Respond to mha_experiences
    await request(app)
        .post('/api/onboarding/respond')
        .send({ sessionId, stepId: 'mha_experiences', response: { experiences: ['overwhelming_sadness_emptiness'] }, timeSpent: 5 });
        
    // 5. Respond to mha_concepts_familiarity
    await request(app)
        .post('/api/onboarding/respond')
        .send({ sessionId, stepId: 'mha_concepts_familiarity', response: { familiarity: 'somewhat_familiar_concepts' }, timeSpent: 5 });

    // 6. Respond to ruqya_knowledge
    await request(app)
        .post('/api/onboarding/respond')
        .send({ sessionId, stepId: 'ruqya_knowledge', response: { ruqya_knowledge: 'aware' }, timeSpent: 5 });

    // 7. Respond to professional_context with healthcare
    const profContextResponse = await request(app)
      .post('/api/onboarding/respond')
      .send({ sessionId, stepId: 'professional_context', response: { professional_context: 'healthcare_pc' }, timeSpent: 5 });
    
    expect(profContextResponse.body.step).toBe('pc_healthcare_details');

    // 8. Respond to pc_healthcare_details
    const healthcareDetailsResponse = await request(app)
      .post('/api/onboarding/respond')
      .send({ sessionId, stepId: 'pc_healthcare_details', response: { role: 'physician' }, timeSpent: 5 });

    expect(healthcareDetailsResponse.body.step).toBe('pc_work_challenges');

    // 9. Respond to pc_work_challenges
    await request(app)
        .post('/api/onboarding/respond')
        .send({ sessionId, stepId: 'pc_work_challenges', response: { challenges: ['heavy_workload_pc'] }, timeSpent: 5 });

    // 10. Respond to demographics
    await request(app)
        .post('/api/onboarding/respond')
        .send({ sessionId, stepId: 'demographics', response: { age_range_section: '26_35_age', gender_section: 'male_gender', family_status_section: 'married_fs' }, timeSpent: 5 });

    // 11. Complete onboarding
    const completeResponse = await request(app)
      .post('/api/onboarding/respond')
      .send({ sessionId, stepId: 'life_circumstances', response: { situations: ['major_life_change_lc'] }, timeSpent: 5 });

    expect(completeResponse.status).toBe(200);
    expect(completeResponse.body.profile.professionalContext.field).toBe('healthcare_pc');
    expect(completeResponse.body.profile.professionalContext.healthcare.role).toBe('physician');
    expect(completeResponse.body.profile.professionalContext.content).toBeDefined();
    expect(completeResponse.body.profile.professionalContext.content.reflection_prompt).toContain('physician');
  });
});
