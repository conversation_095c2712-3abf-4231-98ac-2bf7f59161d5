/**
 * AI Service Integration Tests - Assessment AI
 * Tests complete AI-powered assessment analysis with Islamic context
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { AssessmentAIService } from '../../src/services/assessment-ai.service';
import { IslamicLayerService } from '../../src/services/islamic-layer.service';
import { PersonalizationService } from '../../src/services/personalization.service';

describe('Assessment AI Integration Tests', () => {
  let app: INestApplication;
  let assessmentAIService: AssessmentAIService;
  let islamicLayerService: IslamicLayerService;
  let personalizationService: PersonalizationService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    assessmentAIService = moduleFixture.get<AssessmentAIService>(AssessmentAIService);
    islamicLayerService = moduleFixture.get<IslamicLayerService>(IslamicLayerService);
    personalizationService = moduleFixture.get<PersonalizationService>(PersonalizationService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /api/ai/assessment/categorize', () => {
    it('should categorize symptoms by Islamic soul layers', async () => {
      const symptomsData = {
        symptoms: [
          'feeling anxious and worried',
          'having headaches and fatigue',
          'difficulty concentrating on tasks',
          'feeling distant from Allah in prayers',
          'loss of spiritual purpose and meaning',
        ],
        userProfile: {
          islamicBackground: 'practicing',
          language: 'en',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/categorize')
        .send(symptomsData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.categorization).toBeDefined();

      const categorization = response.body.categorization;
      expect(categorization['feeling anxious and worried']).toBe('Nafs');
      expect(categorization['having headaches and fatigue']).toBe('Jism');
      expect(categorization['difficulty concentrating on tasks']).toBe('Aql');
      expect(categorization['feeling distant from Allah in prayers']).toBe('Qalb');
      expect(categorization['loss of spiritual purpose and meaning']).toBe('Ruh');

      // Verify confidence scores
      expect(response.body.confidence).toBeDefined();
      Object.values(response.body.confidence).forEach((confidence: number) => {
        expect(confidence).toBeGreaterThan(0.7);
      });
    });

    it('should handle Arabic symptom descriptions', async () => {
      const arabicSymptomsData = {
        symptoms: [
          'أشعر بالقلق والخوف', // I feel anxious and afraid
          'لدي صداع مستمر', // I have persistent headaches
          'أجد صعوبة في التركيز', // I find it difficult to concentrate
          'أشعر بالبعد عن الله', // I feel distant from Allah
        ],
        userProfile: {
          islamicBackground: 'traditional',
          language: 'ar',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/categorize')
        .send(arabicSymptomsData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.language).toBe('ar');
      expect(response.body.categorization).toBeDefined();

      // Verify Arabic text handling
      const arabicSymptoms = Object.keys(response.body.categorization);
      arabicSymptoms.forEach(symptom => {
        expect(/[\u0600-\u06FF]/.test(symptom)).toBe(true); // Contains Arabic characters
      });
    });

    it('should provide layer-specific Islamic guidance', async () => {
      const symptomsData = {
        symptoms: ['feeling spiritually empty', 'having panic attacks'],
        userProfile: { islamicBackground: 'practicing' },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/categorize')
        .send(symptomsData)
        .expect(200);

      expect(response.body.islamicGuidance).toBeDefined();
      expect(response.body.islamicGuidance.Qalb).toBeDefined();
      expect(response.body.islamicGuidance.Nafs).toBeDefined();

      // Verify Islamic guidance content
      const qalbGuidance = response.body.islamicGuidance.Qalb;
      expect(qalbGuidance.practices).toContain('dhikr');
      expect(qalbGuidance.practices).toContain('prayer');
      expect(qalbGuidance.verses).toBeDefined();
    });
  });

  describe('POST /api/ai/assessment/analyze', () => {
    it('should generate comprehensive 5-layer Islamic analysis', async () => {
      const analysisData = {
        selectedSymptoms: [
          'anxiety', 'depression', 'fatigue', 'concentration_issues', 
          'spiritual_emptiness', 'prayer_difficulties'
        ],
        reflections: [
          'I feel overwhelmed with daily tasks and responsibilities',
          'My prayers feel mechanical and I struggle to focus',
          'I often feel disconnected from my purpose in life'
        ],
        userProfile: {
          name: 'Ahmed',
          islamicBackground: 'practicing',
          profession: 'Engineer',
          age: 30,
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/analyze')
        .send(analysisData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.analysis).toBeDefined();

      const analysis = response.body.analysis;

      // Verify 5-layer structure
      expect(analysis.layers).toBeDefined();
      expect(Object.keys(analysis.layers)).toHaveLength(5);
      expect(analysis.layers.Jism).toBeDefined();
      expect(analysis.layers.Nafs).toBeDefined();
      expect(analysis.layers.Aql).toBeDefined();
      expect(analysis.layers.Qalb).toBeDefined();
      expect(analysis.layers.Ruh).toBeDefined();

      // Verify each layer has required components
      Object.values(analysis.layers).forEach((layer: any) => {
        expect(layer.symptoms).toBeDefined();
        expect(layer.severity).toBeDefined();
        expect(layer.recommendations).toBeDefined();
        expect(layer.islamicPractices).toBeDefined();
      });

      // Verify overall assessment
      expect(analysis.overallAssessment).toBeDefined();
      expect(analysis.islamicGuidance).toBeDefined();
      expect(analysis.personalizedRecommendations).toBeDefined();
    });

    it('should adapt analysis language based on user persona', async () => {
      const professionalAnalysisData = {
        selectedSymptoms: ['work_stress', 'burnout'],
        userProfile: {
          profession: 'Healthcare Professional',
          islamicBackground: 'practicing',
          pathway: 'clinical_islamic_integration',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/analyze')
        .send(professionalAnalysisData)
        .expect(200);

      expect(response.body.analysis.language).toBe('clinical_islamic');
      expect(response.body.analysis.overallAssessment).toContain('clinical');
      expect(response.body.analysis.recommendations).toContain('evidence-based');

      // Test traditional user
      const traditionalAnalysisData = {
        selectedSymptoms: ['sadness', 'worry'],
        userProfile: {
          islamicBackground: 'traditional',
          pathway: 'gentle_introduction',
        },
      };

      const traditionalResponse = await request(app.getHttpServer())
        .post('/api/ai/assessment/analyze')
        .send(traditionalAnalysisData)
        .expect(200);

      expect(traditionalResponse.body.analysis.language).toBe('gentle_traditional');
      expect(traditionalResponse.body.analysis.overallAssessment).toContain('gentle');
      expect(traditionalResponse.body.analysis.islamicGuidance).toContain('traditional');
    });

    it('should provide personalized Islamic recommendations', async () => {
      const analysisData = {
        selectedSymptoms: ['anxiety', 'spiritual_disconnection'],
        userProfile: {
          islamicBackground: 'practicing',
          practiceLevel: 'intermediate',
          timeAvailability: 'moderate',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/analyze')
        .send(analysisData)
        .expect(200);

      const recommendations = response.body.analysis.personalizedRecommendations;

      expect(recommendations.dailyPractices).toBeDefined();
      expect(recommendations.weeklyGoals).toBeDefined();
      expect(recommendations.islamicResources).toBeDefined();

      // Verify personalization
      expect(recommendations.timeCommitment).toBe('moderate');
      expect(recommendations.practiceLevel).toBe('intermediate');

      // Verify Islamic authenticity
      recommendations.islamicResources.forEach((resource: any) => {
        if (resource.arabic) {
          expect(/[\u0600-\u06FF]/.test(resource.arabic)).toBe(true);
        }
        if (resource.reference) {
          expect(resource.reference).toMatch(/Quran \d+:\d+|Sahih (Bukhari|Muslim)/);
        }
      });
    });

    it('should detect and handle crisis during assessment', async () => {
      const crisisAnalysisData = {
        selectedSymptoms: ['suicidal_thoughts', 'self_harm_urges', 'hopelessness'],
        reflections: [
          'I have been thinking about ending my life',
          'I feel like there is no way out of this pain',
          'I have thoughts of harming myself'
        ],
        userProfile: {
          islamicBackground: 'practicing',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/analyze')
        .send(crisisAnalysisData)
        .expect(200);

      expect(response.body.crisisDetected).toBe(true);
      expect(response.body.crisisSeverity).toBe('critical');
      expect(response.body.immediateActions).toContain('emergency_intervention');

      // Verify Islamic crisis support
      expect(response.body.islamicCrisisSupport).toBeDefined();
      expect(response.body.islamicCrisisSupport.comfortVerse).toBeDefined();
      expect(response.body.islamicCrisisSupport.emergencyDua).toBeDefined();
      expect(response.body.islamicCrisisSupport.hopeMessage).toBeDefined();
    });
  });

  describe('POST /api/ai/assessment/personalize', () => {
    it('should generate persona-specific assessment approach', async () => {
      const personaTests = [
        {
          persona: {
            profession: 'Healthcare Professional',
            islamicBackground: 'practicing',
            mentalHealthExperience: 'professional',
          },
          expectedApproach: 'clinical_islamic_integration',
          expectedFeatures: ['advanced_terminology', 'research_references', 'clinical_insights'],
        },
        {
          persona: {
            profession: 'Student',
            islamicBackground: 'learning',
            age: 20,
          },
          expectedApproach: 'educational_supportive',
          expectedFeatures: ['simple_language', 'educational_resources', 'peer_examples'],
        },
        {
          persona: {
            profession: 'Religious Scholar',
            islamicBackground: 'traditional',
            specialization: 'islamic_studies',
          },
          expectedApproach: 'scholarly_traditional',
          expectedFeatures: ['classical_references', 'traditional_methods', 'scholarly_discourse'],
        },
      ];

      for (const test of personaTests) {
        const response = await request(app.getHttpServer())
          .post('/api/ai/assessment/personalize')
          .send({ userProfile: test.persona })
          .expect(200);

        expect(response.body.approach).toBe(test.expectedApproach);
        test.expectedFeatures.forEach(feature => {
          expect(response.body.features).toContain(feature);
        });
      }
    });

    it('should adapt Islamic content based on religious background', async () => {
      const religiousBackgrounds = [
        {
          background: 'new_muslim',
          expectedContent: 'basic_islamic_concepts',
          expectedLanguage: 'simple_explanatory',
        },
        {
          background: 'traditional',
          expectedContent: 'classical_islamic_wisdom',
          expectedLanguage: 'respectful_traditional',
        },
        {
          background: 'practicing',
          expectedContent: 'contemporary_islamic_guidance',
          expectedLanguage: 'balanced_modern',
        },
        {
          background: 'cultural',
          expectedContent: 'culturally_sensitive_approach',
          expectedLanguage: 'gentle_inclusive',
        },
      ];

      for (const test of religiousBackgrounds) {
        const response = await request(app.getHttpServer())
          .post('/api/ai/assessment/personalize')
          .send({
            userProfile: { islamicBackground: test.background }
          })
          .expect(200);

        expect(response.body.islamicContent.type).toBe(test.expectedContent);
        expect(response.body.language.style).toBe(test.expectedLanguage);
      }
    });
  });

  describe('AI Model Performance and Accuracy', () => {
    it('should maintain high accuracy in symptom categorization', async () => {
      const testCases = [
        { symptom: 'chest pain and shortness of breath', expectedLayer: 'Jism' },
        { symptom: 'feeling angry and irritable', expectedLayer: 'Nafs' },
        { symptom: 'memory problems and confusion', expectedLayer: 'Aql' },
        { symptom: 'feeling disconnected from Allah', expectedLayer: 'Qalb' },
        { symptom: 'loss of life purpose and meaning', expectedLayer: 'Ruh' },
      ];

      let correctPredictions = 0;

      for (const testCase of testCases) {
        const response = await request(app.getHttpServer())
          .post('/api/ai/assessment/categorize')
          .send({
            symptoms: [testCase.symptom],
            userProfile: { islamicBackground: 'practicing' }
          })
          .expect(200);

        const predictedLayer = response.body.categorization[testCase.symptom];
        if (predictedLayer === testCase.expectedLayer) {
          correctPredictions++;
        }
      }

      const accuracy = correctPredictions / testCases.length;
      expect(accuracy).toBeGreaterThan(0.8); // 80% accuracy threshold
    });

    it('should provide consistent results for similar inputs', async () => {
      const similarSymptoms = [
        'feeling anxious and worried',
        'experiencing anxiety and fear',
        'having anxious thoughts and worry',
      ];

      const responses = [];
      for (const symptom of similarSymptoms) {
        const response = await request(app.getHttpServer())
          .post('/api/ai/assessment/categorize')
          .send({
            symptoms: [symptom],
            userProfile: { islamicBackground: 'practicing' }
          })
          .expect(200);

        responses.push(response.body.categorization[symptom]);
      }

      // All similar symptoms should be categorized to the same layer
      const uniqueLayers = [...new Set(responses)];
      expect(uniqueLayers).toHaveLength(1);
      expect(uniqueLayers[0]).toBe('Nafs');
    });

    it('should handle multilingual input consistently', async () => {
      const multilingualSymptoms = [
        { text: 'feeling anxious', language: 'en', expectedLayer: 'Nafs' },
        { text: 'أشعر بالقلق', language: 'ar', expectedLayer: 'Nafs' },
        { text: 'میں پریشان ہوں', language: 'ur', expectedLayer: 'Nafs' },
      ];

      for (const symptom of multilingualSymptoms) {
        const response = await request(app.getHttpServer())
          .post('/api/ai/assessment/categorize')
          .send({
            symptoms: [symptom.text],
            userProfile: { 
              islamicBackground: 'practicing',
              language: symptom.language 
            }
          })
          .expect(200);

        const predictedLayer = response.body.categorization[symptom.text];
        expect(predictedLayer).toBe(symptom.expectedLayer);
      }
    });
  });

  describe('Islamic Authenticity Validation', () => {
    it('should validate all Islamic content for authenticity', async () => {
      const analysisData = {
        selectedSymptoms: ['spiritual_emptiness'],
        userProfile: { islamicBackground: 'practicing' },
      };

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/analyze')
        .send(analysisData)
        .expect(200);

      const islamicContent = response.body.analysis.islamicGuidance;

      // Validate Quranic verses
      if (islamicContent.verses) {
        islamicContent.verses.forEach((verse: any) => {
          expect(verse.arabic).toMatch(/[\u0600-\u06FF]/); // Contains Arabic
          expect(verse.reference).toMatch(/Quran \d+:\d+/); // Valid reference format
          expect(verse.translation).toBeDefined();
        });
      }

      // Validate Du'as
      if (islamicContent.duas) {
        islamicContent.duas.forEach((dua: any) => {
          expect(dua.arabic).toMatch(/[\u0600-\u06FF]/); // Contains Arabic
          expect(dua.source).toMatch(/Sahih (Bukhari|Muslim)|Abu Dawud|Tirmidhi|Ibn Majah/);
          expect(dua.translation).toBeDefined();
        });
      }

      // Validate practices
      if (islamicContent.practices) {
        islamicContent.practices.forEach((practice: any) => {
          expect(['dhikr', 'prayer', 'quran_recitation', 'dua', 'meditation']).toContain(practice.type);
          expect(practice.description).toBeDefined();
        });
      }
    });

    it('should ensure cultural sensitivity across all personas', async () => {
      const culturalTests = [
        { background: 'traditional', expectedSensitivity: 'high' },
        { background: 'practicing', expectedSensitivity: 'moderate' },
        { background: 'cultural', expectedSensitivity: 'high' },
        { background: 'new_muslim', expectedSensitivity: 'very_high' },
      ];

      for (const test of culturalTests) {
        const response = await request(app.getHttpServer())
          .post('/api/ai/assessment/analyze')
          .send({
            selectedSymptoms: ['anxiety'],
            userProfile: { islamicBackground: test.background }
          })
          .expect(200);

        expect(response.body.analysis.culturalSensitivity).toBe(test.expectedSensitivity);
        expect(response.body.analysis.language.respectful).toBe(true);
        expect(response.body.analysis.language.inclusive).toBe(true);
      }
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle AI service failures gracefully', async () => {
      // Simulate AI service failure
      jest.spyOn(assessmentAIService, 'analyzeSymptoms').mockRejectedValue(
        new Error('AI service temporarily unavailable')
      );

      const response = await request(app.getHttpServer())
        .post('/api/ai/assessment/analyze')
        .send({
          selectedSymptoms: ['anxiety'],
          userProfile: { islamicBackground: 'practicing' }
        })
        .expect(200); // Should fallback gracefully

      expect(response.body.fallbackUsed).toBe(true);
      expect(response.body.analysis).toBeDefined(); // Fallback analysis provided
      expect(response.body.analysis.confidence).toBeLessThan(0.7); // Lower confidence
    });

    it('should handle invalid input gracefully', async () => {
      const invalidInputs = [
        { symptoms: [], userProfile: {} }, // Empty data
        { symptoms: null, userProfile: null }, // Null data
        { symptoms: [''], userProfile: { islamicBackground: 'invalid' } }, // Invalid values
      ];

      for (const invalidInput of invalidInputs) {
        const response = await request(app.getHttpServer())
          .post('/api/ai/assessment/analyze')
          .send(invalidInput)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      }
    });

    it('should handle high load and concurrent requests', async () => {
      const concurrentRequests = Array.from({ length: 10 }, (_, i) => 
        request(app.getHttpServer())
          .post('/api/ai/assessment/categorize')
          .send({
            symptoms: [`test symptom ${i}`],
            userProfile: { islamicBackground: 'practicing' }
          })
      );

      const responses = await Promise.all(concurrentRequests);

      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.categorization[`test symptom ${index}`]).toBeDefined();
      });
    });
  });
});
