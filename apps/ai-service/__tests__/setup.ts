/**
 * AI Service Test Setup
 * Global configuration for AI service testing with Islamic content validation
 */

// Islamic content validation utilities
export const validateArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF]/;
  return arabicRegex.test(text);
};

export const validateQuranReference = (reference: string): boolean => {
  const quranRefRegex = /^Quran \d+:\d+$/;
  return quranRefRegex.test(reference);
};

export const validateHadithSource = (source: string): boolean => {
  const validSources = [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>\'i',
    '<PERSON>',
    '<PERSON>',
  ];
  return validSources.some(validSource => source.includes(validSource));
};

export const validateIslamicContent = (content: any): boolean => {
  if (!content) return false;
  
  // Validate Arabic text if present
  if (content.arabic && !validateArabicText(content.arabic)) {
    return false;
  }
  
  // Validate Quran reference if present
  if (content.reference && content.reference.startsWith('Quran') && !validateQuranReference(content.reference)) {
    return false;
  }
  
  // Validate Hadith source if present
  if (content.source && !validateHadithSource(content.source)) {
    return false;
  }
  
  return true;
};

export const validateIslamicLayer = (layer: string): boolean => {
  const validLayers = ['Jism', 'Nafs', 'Aql', 'Qalb', 'Ruh'];
  return validLayers.includes(layer);
};

// Crisis detection validation
export const validateCrisisKeywords = (text: string): boolean => {
  const crisisKeywords = [
    'suicide', 'kill myself', 'end my life', 'harm myself',
    'want to die', 'no point living', 'hopeless', 'worthless',
    'انتحار', 'أريد أن أموت', 'لا فائدة من الحياة', // Arabic crisis keywords
  ];
  
  const lowerText = text.toLowerCase();
  return crisisKeywords.some(keyword => lowerText.includes(keyword.toLowerCase()));
};

export const validateCrisisResponse = (response: any): boolean => {
  if (!response) return false;
  
  // Must have crisis detection flag
  if (typeof response.crisisDetected !== 'boolean') return false;
  
  // If crisis detected, must have appropriate response
  if (response.crisisDetected) {
    if (!response.severity || !response.immediateActions) return false;
    if (!response.islamicComfort || !response.emergencyContacts) return false;
  }
  
  return true;
};

// AI model validation utilities
export const validateModelAccuracy = (predictions: any[], actuals: any[]): number => {
  if (predictions.length !== actuals.length) return 0;
  
  let correct = 0;
  for (let i = 0; i < predictions.length; i++) {
    if (predictions[i] === actuals[i]) correct++;
  }
  
  return correct / predictions.length;
};

export const validateModelConfidence = (confidences: number[]): boolean => {
  return confidences.every(conf => conf >= 0 && conf <= 1);
};

export const validateLayerCategorization = (categorization: any): boolean => {
  const validLayers = ['Jism', 'Nafs', 'Aql', 'Qalb', 'Ruh'];
  
  for (const [symptom, layer] of Object.entries(categorization)) {
    if (!validLayers.includes(layer as string)) return false;
  }
  
  return true;
};

// Mock AI models and services
export const mockNLPModel = {
  analyzeText: jest.fn(),
  extractKeywords: jest.fn(),
  calculateSentiment: jest.fn(),
  detectLanguage: jest.fn(),
  categorizeSymptom: jest.fn(),
};

export const mockCrisisDetectionModel = {
  detectCrisis: jest.fn(),
  calculateSeverity: jest.fn(),
  identifyTriggers: jest.fn(),
  generateResponse: jest.fn(),
};

export const mockAssessmentAIModel = {
  categorizeByLayer: jest.fn(),
  generateAnalysis: jest.fn(),
  personalizeRecommendations: jest.fn(),
  adaptLanguage: jest.fn(),
};

export const mockIslamicContextModel = {
  getLayerGuidance: jest.fn(),
  validateContent: jest.fn(),
  generateComfort: jest.fn(),
  adaptCulturally: jest.fn(),
};

// Test data
export const mockSymptoms = [
  'feeling anxious and worried',
  'having headaches and fatigue',
  'difficulty concentrating',
  'feeling distant from Allah',
  'loss of spiritual purpose',
];

export const mockCrisisTexts = [
  'I want to kill myself',
  'I am going to harm myself',
  'Life is not worth living',
  'I have thoughts of suicide',
  'أريد أن أؤذي نفسي', // Arabic: I want to hurt myself
];

export const mockNormalTexts = [
  'I am feeling a bit sad today',
  'I have some stress at work',
  'I need help with anxiety',
  'I am going through a difficult time',
];

export const mockIslamicContent = {
  verses: [
    {
      arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
      transliteration: 'Wa man yattaqi Allaha yaj\'al lahu makhrajan',
      translation: 'And whoever fears Allah - He will make for him a way out',
      reference: 'Quran 65:2',
    },
    {
      arabic: 'وَبَشِّرِ الصَّابِرِينَ',
      transliteration: 'Wa bashshir as-sabireen',
      translation: 'And give good tidings to the patient',
      reference: 'Quran 2:155',
    },
  ],
  duas: [
    {
      arabic: 'اللَّهُمَّ أَذْهِبِ الْبَأْسَ رَبَّ النَّاسِ',
      transliteration: 'Allahumma adhhib al-ba\'sa rabba an-nas',
      translation: 'O Allah, remove the hardship, O Lord of mankind',
      source: 'Sahih Bukhari',
    },
    {
      arabic: 'حَسْبُنَا اللَّهُ وَنِعْمَ الْوَكِيلُ',
      transliteration: 'Hasbuna Allahu wa ni\'ma al-wakeel',
      translation: 'Allah is sufficient for us and He is the best Disposer of affairs',
      source: 'Quran 3:173',
    },
  ],
  dhikr: [
    {
      arabic: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
      transliteration: 'Subhan Allahi wa bihamdihi',
      translation: 'Glory is to Allah and praise is to Him',
      count: 33,
    },
    {
      arabic: 'لَا إِلَٰهَ إِلَّا اللَّهُ',
      transliteration: 'La ilaha illa Allah',
      translation: 'There is no god but Allah',
      count: 100,
    },
  ],
};

export const mockUserProfiles = [
  {
    islamicBackground: 'practicing',
    profession: 'Healthcare Professional',
    language: 'en',
    practiceLevel: 'intermediate',
  },
  {
    islamicBackground: 'traditional',
    profession: 'Teacher',
    language: 'ar',
    practiceLevel: 'advanced',
  },
  {
    islamicBackground: 'new_muslim',
    profession: 'Student',
    language: 'en',
    practiceLevel: 'beginner',
  },
];

// Performance testing utilities
export const measureAIPerformance = async (aiFunction: () => Promise<any>) => {
  const start = Date.now();
  const memoryBefore = process.memoryUsage();
  
  const result = await aiFunction();
  
  const end = Date.now();
  const memoryAfter = process.memoryUsage();
  
  return {
    result,
    executionTime: end - start,
    memoryUsed: memoryAfter.heapUsed - memoryBefore.heapUsed,
  };
};

export const validateAIPerformance = (metrics: any, thresholds: any) => {
  return {
    executionTimeValid: metrics.executionTime <= thresholds.maxExecutionTime,
    memoryUsageValid: metrics.memoryUsed <= thresholds.maxMemoryUsage,
    accuracyValid: metrics.accuracy >= thresholds.minAccuracy,
  };
};

// Error simulation for AI services
export const simulateAIServiceError = (errorType: string) => {
  const errors = {
    model_unavailable: new Error('AI model service unavailable'),
    timeout: new Error('AI processing timeout'),
    invalid_input: new Error('Invalid input format for AI model'),
    rate_limit: new Error('AI service rate limit exceeded'),
    authentication: new Error('AI service authentication failed'),
  };
  
  throw errors[errorType as keyof typeof errors] || new Error('Unknown AI service error');
};

// Global test utilities
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup and teardown
beforeEach(() => {
  // Reset all mocks
  jest.clearAllMocks();
  
  // Reset AI model mocks
  mockNLPModel.analyzeText.mockClear();
  mockCrisisDetectionModel.detectCrisis.mockClear();
  mockAssessmentAIModel.categorizeByLayer.mockClear();
  mockIslamicContextModel.getLayerGuidance.mockClear();
});

afterEach(() => {
  jest.clearAllMocks();
});

// Export all utilities
export {
  mockNLPModel,
  mockCrisisDetectionModel,
  mockAssessmentAIModel,
  mockIslamicContextModel,
  mockSymptoms,
  mockCrisisTexts,
  mockNormalTexts,
  mockUserProfiles,
  measureAIPerformance,
  validateAIPerformance,
  simulateAIServiceError,
};
