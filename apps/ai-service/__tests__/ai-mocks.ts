/**
 * AI Service Mocks
 * Mock implementations for AI models and external services
 */

// Mock TensorFlow/ML libraries
jest.mock('@tensorflow/tfjs-node', () => ({
  loadLayersModel: jest.fn().mockResolvedValue({
    predict: jest.fn().mockReturnValue({
      dataSync: jest.fn().mockReturnValue([0.8, 0.2, 0.1, 0.9, 0.3]),
    }),
  }),
  tensor: jest.fn().mockReturnValue({
    reshape: jest.fn().mockReturnThis(),
    dispose: jest.fn(),
  }),
  sequential: jest.fn().mockReturnValue({
    add: jest.fn(),
    compile: jest.fn(),
    fit: jest.fn(),
    predict: jest.fn(),
  }),
}));

// Mock OpenAI/GPT services
jest.mock('openai', () => ({
  OpenAI: jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [{
            message: {
              content: JSON.stringify({
                layer: 'Nafs',
                confidence: 0.85,
                reasoning: 'Emotional symptom detected',
              }),
            },
          }],
        }),
      },
    },
  })),
}));

// Mock natural language processing libraries
jest.mock('natural', () => ({
  SentimentAnalyzer: jest.fn().mockImplementation(() => ({
    getSentiment: jest.fn().mockReturnValue(0.2),
  })),
  PorterStemmer: {
    stem: jest.fn().mockImplementation((word) => word.toLowerCase()),
  },
  WordTokenizer: jest.fn().mockImplementation(() => ({
    tokenize: jest.fn().mockImplementation((text) => text.split(' ')),
  })),
  TfIdf: jest.fn().mockImplementation(() => ({
    addDocument: jest.fn(),
    tfidfs: jest.fn().mockImplementation((text, callback) => {
      callback(0, 0.5); // Mock TF-IDF score
    }),
  })),
}));

// Mock language detection
jest.mock('franc', () => ({
  franc: jest.fn().mockImplementation((text) => {
    if (/[\u0600-\u06FF]/.test(text)) return 'ara'; // Arabic
    if (/[\u0627-\u06FF]/.test(text)) return 'urd'; // Urdu
    return 'eng'; // English default
  }),
}));

// Mock Redis for caching
jest.mock('redis', () => ({
  createClient: jest.fn().mockReturnValue({
    connect: jest.fn().mockResolvedValue(undefined),
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue('OK'),
    del: jest.fn().mockResolvedValue(1),
    quit: jest.fn().mockResolvedValue(undefined),
  }),
}));

// Mock HTTP clients for external AI services
jest.mock('axios', () => ({
  default: {
    create: jest.fn().mockReturnValue({
      post: jest.fn().mockResolvedValue({
        data: {
          predictions: [
            { label: 'Nafs', confidence: 0.85 },
            { label: 'Qalb', confidence: 0.12 },
          ],
        },
      }),
      get: jest.fn().mockResolvedValue({
        data: { status: 'healthy' },
      }),
    }),
    post: jest.fn().mockResolvedValue({
      data: {
        analysis: {
          sentiment: 0.2,
          keywords: ['anxiety', 'worry'],
          language: 'en',
        },
      },
    }),
  },
}));

// Mock file system for model loading
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  readFileSync: jest.fn().mockImplementation((path) => {
    if (path.includes('model.json')) {
      return JSON.stringify({
        modelTopology: {},
        weightsManifest: [],
      });
    }
    if (path.includes('islamic-content.json')) {
      return JSON.stringify({
        verses: [
          {
            arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
            reference: 'Quran 65:2',
            translation: 'And whoever fears Allah - He will make for him a way out',
          },
        ],
      });
    }
    return '{}';
  }),
  existsSync: jest.fn().mockReturnValue(true),
}));

// Mock crypto for generating IDs
jest.mock('crypto', () => ({
  ...jest.requireActual('crypto'),
  randomUUID: jest.fn().mockReturnValue('mock-uuid-123'),
  randomBytes: jest.fn().mockReturnValue(Buffer.from('mock-random-bytes')),
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.AI_MODEL_PATH = '/mock/path/to/models';
process.env.OPENAI_API_KEY = 'mock-openai-key';
process.env.REDIS_URL = 'redis://localhost:6379';

// Global AI service mocks
global.mockAIServices = {
  nlp: {
    analyzeText: jest.fn().mockResolvedValue({
      sentiment: 0.2,
      keywords: ['anxiety', 'worry'],
      language: 'en',
      confidence: 0.85,
    }),
    categorizeSymptom: jest.fn().mockResolvedValue({
      layer: 'Nafs',
      confidence: 0.85,
      reasoning: 'Emotional symptom detected',
    }),
  },
  
  crisisDetection: {
    detectCrisis: jest.fn().mockResolvedValue({
      crisisDetected: false,
      severity: 'low',
      confidence: 0.9,
      keywords: [],
    }),
    generateResponse: jest.fn().mockResolvedValue({
      islamicComfort: {
        verse: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
        translation: 'And whoever fears Allah - He will make for him a way out',
        reference: 'Quran 65:2',
      },
      emergencyContacts: [
        { name: 'Crisis Hotline', phone: '988' },
      ],
    }),
  },
  
  assessment: {
    generateAnalysis: jest.fn().mockResolvedValue({
      layers: {
        Jism: { symptoms: [], severity: 'mild' },
        Nafs: { symptoms: ['anxiety'], severity: 'moderate' },
        Aql: { symptoms: [], severity: 'mild' },
        Qalb: { symptoms: [], severity: 'mild' },
        Ruh: { symptoms: [], severity: 'mild' },
      },
      overallAssessment: 'Moderate emotional challenges',
      islamicGuidance: 'Focus on dhikr and prayer',
    }),
  },
  
  islamicContext: {
    getLayerGuidance: jest.fn().mockResolvedValue({
      practices: ['dhikr', 'prayer'],
      verses: [
        {
          arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
          reference: 'Quran 65:2',
          translation: 'And whoever fears Allah - He will make for him a way out',
        },
      ],
      duas: [
        {
          arabic: 'اللَّهُمَّ أَذْهِبِ الْبَأْسَ',
          source: 'Sahih Bukhari',
          translation: 'O Allah, remove the hardship',
        },
      ],
    }),
    validateContent: jest.fn().mockResolvedValue({
      valid: true,
      verseAuthentic: true,
      duaAuthentic: true,
    }),
  },
};

// Performance monitoring mocks
global.mockPerformanceMonitor = {
  startTimer: jest.fn().mockReturnValue('timer-id'),
  endTimer: jest.fn().mockReturnValue(150), // 150ms
  getMemoryUsage: jest.fn().mockReturnValue({
    heapUsed: 50 * 1024 * 1024, // 50MB
    heapTotal: 100 * 1024 * 1024, // 100MB
  }),
};

// Error simulation
global.simulateAIError = (errorType: string) => {
  const errors = {
    timeout: () => Promise.reject(new Error('AI service timeout')),
    rate_limit: () => Promise.reject(new Error('Rate limit exceeded')),
    invalid_input: () => Promise.reject(new Error('Invalid input format')),
    model_error: () => Promise.reject(new Error('Model prediction failed')),
  };
  
  return errors[errorType as keyof typeof errors] || (() => Promise.reject(new Error('Unknown error')));
};

export {};
