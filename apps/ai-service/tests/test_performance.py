"""
Performance and Algorithm Tests for AI Service
Tests response times, load handling, and algorithm accuracy
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from concurrent.futures import ThreadPoolExecutor
import statistics
from ai_service.processors.spiritual_analysis import SpiritualAnalysisProcessor
from ai_service.processors.symptom_analyzer import SymptomAnalyzer
from ai_service.processors.journey_generation import JourneyGenerationProcessor
from ai_service.processors.content_recommender import ContentRecommendationProcessor


class TestPerformance:
    """Test suite for performance and scalability"""

    @pytest.fixture
    def processors(self, mock_openai_client):
        """Create processors for performance testing"""
        with patch('ai_service.processors.symptom_analyzer.openai.OpenAI', return_value=mock_openai_client):
            return {
                'spiritual_analysis': SpiritualAnalysisProcessor(),
                'symptom_analyzer': SymptomAnalyzer(),
                'journey_generation': JourneyGenerationProcessor(),
                'content_recommender': ContentRecommendationProcessor()
            }

    def test_spiritual_analysis_performance(self, processors, sample_assessment_data, performance_thresholds):
        """Test spiritual analysis performance"""
        processor = processors['spiritual_analysis']
        
        start_time = time.time()
        result = processor.analyze_spiritual_landscape(sample_assessment_data)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Should complete within threshold
        assert execution_time < 5.0  # 5 seconds max
        assert result.primary_layer in ['jism', 'nafs', 'aql', 'qalb', 'ruh']
        assert result.confidence > 0

    @pytest.mark.asyncio
    async def test_symptom_analyzer_performance(self, processors, sample_symptoms, sample_intensity, performance_thresholds):
        """Test symptom analyzer performance"""
        analyzer = processors['symptom_analyzer']
        
        start_time = time.time()
        result = await analyzer.analyze_symptoms(
            symptoms=sample_symptoms,
            intensity=sample_intensity,
            duration="2_weeks"
        )
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Should complete within threshold
        assert execution_time < performance_thresholds['symptom_analysis_max_time']
        assert len(result.primary_layers) > 0
        assert result.confidence_score > 0

    def test_journey_generation_performance(self, processors, sample_assessment_data, sample_user_profile, performance_thresholds):
        """Test journey generation performance"""
        processor = processors['journey_generation']
        
        request_data = {
            'assessment': sample_assessment_data,
            'userProfile': sample_user_profile,
            'preferences': {'duration_preference': '2_weeks'}
        }
        
        start_time = time.time()
        result = processor.generate_journey_parameters(request_data)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Should complete within threshold
        assert execution_time < performance_thresholds['journey_generation_max_time']
        assert result['duration'] > 0
        assert result['type']

    @pytest.mark.asyncio
    async def test_content_recommendation_performance(self, processors, performance_thresholds):
        """Test content recommendation performance"""
        processor = processors['content_recommender']
        
        with patch.object(processor, '_get_content_library', return_value=[]):
            with patch.object(processor, '_get_user_profile', return_value={}):
                start_time = time.time()
                result = await processor.recommend_content(
                    user_id="test_user",
                    healing_focus=["anxiety", "spiritual_connection"]
                )
                end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Should complete within threshold
        assert execution_time < performance_thresholds['content_recommendation_max_time']
        assert isinstance(result.content_ids, list)

    @pytest.mark.asyncio
    async def test_concurrent_requests_performance(self, processors, sample_assessment_data):
        """Test performance under concurrent load"""
        processor = processors['spiritual_analysis']
        
        # Create multiple concurrent tasks
        num_concurrent = 10
        tasks = []
        
        start_time = time.time()
        
        for i in range(num_concurrent):
            # Modify data slightly for each request
            modified_data = {**sample_assessment_data}
            modified_data['userProfile']['user_id'] = f"user_{i}"
            
            task = asyncio.create_task(
                asyncio.to_thread(processor.analyze_spiritual_landscape, modified_data)
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time_per_request = total_time / num_concurrent
        
        # All requests should complete successfully
        assert len(results) == num_concurrent
        for result in results:
            assert result.primary_layer in ['jism', 'nafs', 'aql', 'qalb', 'ruh']
        
        # Average time per request should be reasonable
        assert avg_time_per_request < 2.0  # 2 seconds average max

    def test_memory_usage_stability(self, processors, sample_assessment_data):
        """Test memory usage remains stable under repeated operations"""
        processor = processors['spiritual_analysis']
        
        # Run multiple iterations to check for memory leaks
        for i in range(50):
            result = processor.analyze_spiritual_landscape(sample_assessment_data)
            assert result.primary_layer in ['jism', 'nafs', 'aql', 'qalb', 'ruh']
            
            # Clear any potential references
            del result

    @pytest.mark.asyncio
    async def test_large_dataset_performance(self, processors):
        """Test performance with large datasets"""
        analyzer = processors['symptom_analyzer']
        
        # Create large symptom dataset
        large_symptoms = {
            "jism": ["sleep_difficulties", "physical_tension", "fatigue", "headaches", "muscle_pain", "digestive_issues"],
            "nafs": ["anxiety_worry", "overwhelming_sadness", "mood_swings", "irritability", "fear", "anger"],
            "aql": ["racing_thoughts", "negative_thoughts", "concentration_issues", "memory_problems", "confusion"],
            "qalb": ["distant_from_allah", "prayers_mechanical", "reduced_quran_connection", "spiritual_emptiness"],
            "ruh": ["questioning_purpose", "yearning_eternal", "existential_crisis", "spiritual_seeking"]
        }
        
        large_intensity = {layer: 7 for layer in large_symptoms.keys()}
        
        start_time = time.time()
        result = await analyzer.analyze_symptoms(
            symptoms=large_symptoms,
            intensity=large_intensity,
            duration="3_months",
            additional_notes="A very detailed description of complex symptoms and experiences that spans multiple paragraphs and includes various aspects of mental, physical, emotional, and spiritual challenges that the user is experiencing in their daily life and spiritual practice."
        )
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Should handle large datasets efficiently
        assert execution_time < 10.0  # 10 seconds max for large dataset
        assert len(result.primary_layers) > 0

    def test_algorithm_consistency(self, processors, sample_assessment_data):
        """Test algorithm consistency across multiple runs"""
        processor = processors['spiritual_analysis']
        
        results = []
        for i in range(10):
            result = processor.analyze_spiritual_landscape(sample_assessment_data)
            results.append(result)
        
        # Primary layer should be consistent
        primary_layers = [r.primary_layer for r in results]
        most_common_layer = max(set(primary_layers), key=primary_layers.count)
        consistency_ratio = primary_layers.count(most_common_layer) / len(primary_layers)
        
        # Should be at least 80% consistent
        assert consistency_ratio >= 0.8

    def test_layer_weight_algorithm_accuracy(self, processors):
        """Test accuracy of layer weight algorithm"""
        processor = processors['spiritual_analysis']
        
        # Test case where qalb should clearly be primary (highest weight)
        qalb_focused_data = {
            "userProfile": {"islamic_knowledge_level": "intermediate"},
            "physicalExperiences": {"symptoms": [], "intensity": "mild"},
            "emotionalExperiences": {"symptoms": [], "intensity": "mild"},
            "mentalExperiences": {"symptoms": [], "intensity": "mild"},
            "spiritualExperiences": {
                "symptoms": ["distant_from_allah", "prayers_mechanical", "feeling_unworthy"],
                "intensity": "severe"
            },
            "reflections": {"main_concern": "spiritual connection"}
        }
        
        result = processor.analyze_spiritual_landscape(qalb_focused_data)
        
        # Should identify qalb as primary layer
        assert result.primary_layer == 'qalb'

    @pytest.mark.asyncio
    async def test_api_response_time_distribution(self, processors, sample_assessment_data):
        """Test distribution of API response times"""
        processor = processors['spiritual_analysis']
        
        response_times = []
        num_requests = 20
        
        for i in range(num_requests):
            start_time = time.time()
            result = await asyncio.to_thread(
                processor.analyze_spiritual_landscape, 
                sample_assessment_data
            )
            end_time = time.time()
            
            response_times.append(end_time - start_time)
            assert result.primary_layer in ['jism', 'nafs', 'aql', 'qalb', 'ruh']
        
        # Calculate statistics
        mean_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        max_time = max(response_times)
        min_time = min(response_times)
        
        # Response time distribution should be reasonable
        assert mean_time < 3.0  # Average under 3 seconds
        assert median_time < 2.0  # Median under 2 seconds
        assert max_time < 8.0  # No request over 8 seconds
        assert min_time > 0.01  # Sanity check - should take some time

    def test_crisis_detection_algorithm_accuracy(self, processors):
        """Test accuracy of crisis detection algorithm"""
        processor = processors['spiritual_analysis']
        
        # Test clear crisis case
        crisis_data = {
            "userProfile": {"user_id": "crisis_test"},
            "physicalExperiences": {"symptoms": ["severe_fatigue"], "intensity": "severe"},
            "emotionalExperiences": {"symptoms": ["overwhelming_sadness", "emotional_numbness"], "intensity": "severe"},
            "mentalExperiences": {"symptoms": ["intrusive_thoughts", "racing_thoughts"], "intensity": "severe"},
            "spiritualExperiences": {"symptoms": ["distant_from_allah", "feeling_unworthy"], "intensity": "severe"},
            "reflections": {"main_concern": "I feel hopeless and worthless"}
        }
        
        result = processor.analyze_spiritual_landscape(crisis_data)
        
        # Should detect crisis
        assert result.crisis_level in ['moderate', 'high', 'critical']
        assert len(result.crisis_indicators) > 0
        
        # Test non-crisis case
        normal_data = {
            "userProfile": {"user_id": "normal_test"},
            "physicalExperiences": {"symptoms": ["mild_fatigue"], "intensity": "mild"},
            "emotionalExperiences": {"symptoms": ["occasional_worry"], "intensity": "mild"},
            "mentalExperiences": {"symptoms": [], "intensity": "mild"},
            "spiritualExperiences": {"symptoms": [], "intensity": "mild"},
            "reflections": {"main_concern": "Just want to improve my spiritual practice"}
        }
        
        normal_result = processor.analyze_spiritual_landscape(normal_data)
        
        # Should not detect crisis
        assert normal_result.crisis_level in ['none', 'low']

    @pytest.mark.asyncio
    async def test_recommendation_algorithm_relevance(self, processors):
        """Test relevance of recommendation algorithms"""
        processor = processors['content_recommender']
        
        # Mock content library with known items
        mock_content = [
            {
                "id": "anxiety_dhikr",
                "type": "dhikr",
                "healing_focus": ["anxiety", "calm"],
                "soul_layers": ["nafs"],
                "difficulty": "beginner",
                "duration": 10
            },
            {
                "id": "spiritual_quran",
                "type": "quran",
                "healing_focus": ["spiritual_connection"],
                "soul_layers": ["qalb"],
                "difficulty": "intermediate",
                "duration": 20
            },
            {
                "id": "depression_dua",
                "type": "dua",
                "healing_focus": ["depression", "hope"],
                "soul_layers": ["nafs", "qalb"],
                "difficulty": "beginner",
                "duration": 15
            }
        ]
        
        user_profile = {
            "skill_level": "beginner",
            "preferred_content_types": ["dhikr", "dua"],
            "completed_content": []
        }
        
        with patch.object(processor, '_get_content_library', return_value=mock_content):
            with patch.object(processor, '_get_user_profile', return_value=user_profile):
                # Test anxiety-focused recommendation
                anxiety_result = await processor.recommend_content(
                    user_id="test_user",
                    healing_focus=["anxiety"],
                    current_mood="anxious"
                )
                
                # Should prioritize anxiety-related content
                assert "anxiety_dhikr" in anxiety_result.content_ids
                
                # Test spiritual-focused recommendation
                spiritual_result = await processor.recommend_content(
                    user_id="test_user",
                    healing_focus=["spiritual_connection"],
                    soul_layers=["qalb"]
                )
                
                # Should include spiritual content
                assert "spiritual_quran" in spiritual_result.content_ids

    def test_scalability_with_user_profiles(self, processors):
        """Test scalability with different user profile complexities"""
        processor = processors['journey_generation']
        
        # Simple profile
        simple_profile = {
            "islamic_knowledge_level": "beginner",
            "ruqya_experience": "none"
        }
        
        # Complex profile
        complex_profile = {
            "islamic_knowledge_level": "advanced",
            "ruqya_experience": "expert",
            "cultural_background": "multi_cultural",
            "profession": "islamic_scholar",
            "family_situation": "complex",
            "community_involvement": "leadership",
            "previous_therapy": True,
            "mental_health_awareness": "high",
            "spiritual_practices": ["advanced_dhikr", "quran_memorization", "night_prayers"],
            "support_system": "extensive_network"
        }
        
        assessment = {"primaryLayer": "qalb", "severityLevel": "moderate"}
        
        # Both should process efficiently
        start_time = time.time()
        simple_result = processor.generate_journey_parameters({
            'assessment': assessment,
            'userProfile': simple_profile,
            'preferences': {}
        })
        simple_time = time.time() - start_time
        
        start_time = time.time()
        complex_result = processor.generate_journey_parameters({
            'assessment': assessment,
            'userProfile': complex_profile,
            'preferences': {}
        })
        complex_time = time.time() - start_time
        
        # Both should complete quickly
        assert simple_time < 2.0
        assert complex_time < 3.0  # Allow slightly more time for complex profile
        
        # Both should produce valid results
        assert simple_result['duration'] > 0
        assert complex_result['duration'] > 0

    @pytest.mark.asyncio
    async def test_error_recovery_performance(self, processors):
        """Test performance of error recovery mechanisms"""
        analyzer = processors['symptom_analyzer']
        
        # Test with data that will trigger fallback mechanisms
        problematic_data = {
            "symptoms": {"invalid_layer": ["invalid_symptom"]},
            "intensity": {"invalid_layer": "invalid_intensity"},
            "duration": None
        }
        
        start_time = time.time()
        result = await analyzer.analyze_symptoms(
            symptoms=problematic_data["symptoms"],
            intensity=problematic_data["intensity"],
            duration=problematic_data["duration"]
        )
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Error recovery should be fast
        assert execution_time < 1.0  # Should fallback quickly
        assert result.severity_level in ['mild', 'moderate', 'severe']
        assert result.confidence_score <= 0.7  # Fallback should have lower confidence
