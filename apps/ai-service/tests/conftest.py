"""
Comprehensive test configuration for Qalb Healing AI Service
Provides fixtures and mocks for Islamic mental health testing
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, List, Any, Optional
import json
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Import the main app
from ai_service.main import app

# Test data fixtures for Islamic mental health scenarios
@pytest.fixture
def sample_user_profile():
    """Sample user profile for testing"""
    return {
        "user_id": "test_user_123",
        "age": 28,
        "gender": "male",
        "location": "USA",
        "islamic_knowledge_level": "intermediate",
        "profession": "software_engineer",
        "preferred_language": "english",
        "cultural_background": "south_asian",
        "ruqya_experience": "beginner",
        "mental_health_awareness": "moderate",
        "previous_therapy": False,
        "spiritual_practices": ["daily_prayers", "weekly_dhikr", "quran_reading"],
        "support_system": "family_friends",
        "crisis_contacts": ["family_member", "local_imam"]
    }

@pytest.fixture
def sample_assessment_data():
    """Sample assessment data covering all 5 layers"""
    return {
        "userProfile": {
            "user_id": "test_user_123",
            "islamic_knowledge_level": "intermediate",
            "ruqya_experience": "beginner"
        },
        "physicalExperiences": {
            "symptoms": ["sleep_difficulties", "physical_tension", "fatigue"],
            "intensity": "moderate",
            "duration": "2_weeks",
            "impact_on_worship": "moderate"
        },
        "emotionalExperiences": {
            "symptoms": ["anxiety_worry", "overwhelming_sadness", "mood_swings"],
            "intensity": "moderate",
            "triggers": ["work_stress", "family_issues"],
            "coping_mechanisms": ["dhikr", "prayer"]
        },
        "mentalExperiences": {
            "symptoms": ["racing_thoughts", "negative_thoughts", "concentration_issues"],
            "intensity": "mild",
            "thought_patterns": ["catastrophizing", "self_blame"],
            "clarity_during_prayer": "poor"
        },
        "spiritualExperiences": {
            "symptoms": ["distant_from_allah", "prayers_mechanical", "reduced_quran_connection"],
            "intensity": "moderate",
            "worship_quality": "declining",
            "spiritual_practices": ["irregular_prayers", "minimal_dhikr"]
        },
        "reflections": {
            "main_concern": "Feeling disconnected from Allah and struggling with anxiety",
            "goals": "Restore spiritual connection and find peace",
            "support_needed": "Islamic guidance and practical tools"
        },
        "sessionMetadata": {
            "completion_time": 25,
            "session_quality": "good",
            "user_engagement": "high"
        }
    }

@pytest.fixture
def crisis_assessment_data():
    """Assessment data indicating crisis situation"""
    return {
        "userProfile": {"user_id": "crisis_user_456"},
        "physicalExperiences": {
            "symptoms": ["severe_fatigue", "heart_breathing", "sleep_difficulties"],
            "intensity": "severe"
        },
        "emotionalExperiences": {
            "symptoms": ["overwhelming_sadness", "emotional_numbness", "hopelessness"],
            "intensity": "severe"
        },
        "mentalExperiences": {
            "symptoms": ["intrusive_thoughts", "racing_thoughts", "negative_thoughts"],
            "intensity": "severe"
        },
        "spiritualExperiences": {
            "symptoms": ["distant_from_allah", "feeling_unworthy", "questioning_purpose"],
            "intensity": "severe"
        },
        "reflections": {
            "main_concern": "I feel hopeless and worthless, like there's no point in continuing",
            "goals": "I don't know if I can get better",
            "support_needed": "I need help immediately"
        }
    }

@pytest.fixture
def islamic_content_samples():
    """Sample Islamic content for recommendation testing"""
    return [
        {
            "id": "dhikr_anxiety_001",
            "type": "dhikr",
            "title": "Dhikr for Anxiety Relief",
            "content": "La hawla wa la quwwata illa billah",
            "translation": "There is no power except with Allah",
            "healing_focus": ["anxiety", "trust_in_allah"],
            "soul_layers": ["nafs", "qalb"],
            "difficulty": "beginner",
            "duration": 10,
            "arabic_text": "لا حول ولا قوة إلا بالله"
        },
        {
            "id": "quran_healing_002",
            "type": "quran",
            "title": "Surah Al-Fatiha for Healing",
            "content": "Recitation and reflection on Al-Fatiha",
            "healing_focus": ["spiritual_connection", "general_healing"],
            "soul_layers": ["qalb", "ruh"],
            "difficulty": "beginner",
            "duration": 15,
            "ruqya_component": True
        },
        {
            "id": "dua_depression_003",
            "type": "dua",
            "title": "Du'a for Depression and Sadness",
            "content": "Allahumma rahmataka arju",
            "translation": "O Allah, I hope for Your mercy",
            "healing_focus": ["depression", "hope"],
            "soul_layers": ["nafs", "qalb"],
            "difficulty": "intermediate",
            "duration": 20
        }
    ]

@pytest.fixture
def journey_templates():
    """Sample journey templates for testing"""
    return {
        "7_day_anxiety_relief": {
            "title": "7-Day Anxiety Relief Journey",
            "description": "Islamic approach to managing anxiety",
            "duration": 7,
            "primary_layer": "nafs",
            "practices": ["morning_dhikr", "breathing_exercises", "evening_reflection"],
            "ruqya_level": "basic"
        },
        "14_day_spiritual_connection": {
            "title": "14-Day Spiritual Reconnection",
            "description": "Restore your connection with Allah",
            "duration": 14,
            "primary_layer": "qalb",
            "practices": ["enhanced_prayers", "quran_reflection", "dhikr_sessions"],
            "ruqya_level": "intermediate"
        }
    }

@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing AI responses"""
    mock_client = Mock()
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message.content = json.dumps({
        "primary_layers": ["nafs", "qalb"],
        "severity_level": "moderate",
        "root_causes": ["spiritual_disconnection", "emotional_imbalance"],
        "recommended_journey": "14-day-spiritual-healing",
        "immediate_actions": [
            "Begin with istighfar and dhikr",
            "Establish regular prayer routine",
            "Practice mindful breathing with Islamic remembrance"
        ],
        "spotlight": "Focus on strengthening your connection with Allah",
        "healing_duration": 14,
        "confidence_score": 0.85
    })

    mock_client.chat.completions.create = AsyncMock(return_value=mock_response)
    return mock_client

@pytest.fixture
def test_client():
    """FastAPI test client"""
    return TestClient(app)

@pytest.fixture
async def async_client():
    """Async test client for FastAPI"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture
def mock_auth_token():
    """Mock authentication token"""
    return "Bearer mock_jwt_token_for_testing"

@pytest.fixture
def islamic_crisis_keywords():
    """Keywords that indicate Islamic-specific crisis situations"""
    return {
        "spiritual_crisis": [
            "lost_faith", "allah_abandoned_me", "prayers_meaningless",
            "quran_no_comfort", "spiritual_emptiness", "religious_doubt"
        ],
        "cultural_crisis": [
            "family_shame", "community_rejection", "cultural_conflict",
            "identity_crisis", "torn_between_worlds"
        ],
        "ruqya_indicators": [
            "evil_eye", "black_magic", "jinn_possession", "spiritual_attack",
            "unexplained_symptoms", "nightmares_islamic"
        ]
    }

@pytest.fixture
def arabic_text_samples():
    """Sample Arabic texts for testing Arabic handling"""
    return {
        "dhikr": {
            "text": "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ",
            "transliteration": "Subhan Allah wa bihamdihi",
            "translation": "Glory be to Allah and praise be to Him"
        },
        "dua": {
            "text": "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ",
            "transliteration": "Rabbana atina fi'd-dunya hasanatan wa fi'l-akhirati hasanatan wa qina 'adhab an-nar",
            "translation": "Our Lord, give us good in this world and good in the hereafter, and save us from the punishment of the Fire"
        },
        "quran": {
            "text": "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
            "transliteration": "Wa man yattaqi Allah yaj'al lahu makhrajan",
            "translation": "And whoever fears Allah - He will make for him a way out",
            "reference": "Quran 65:2"
        }
    }

# Event loop fixture for async tests
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# Mock database responses
@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for database operations"""
    mock_client = Mock()

    # Mock successful responses
    mock_client.table.return_value.insert.return_value.execute.return_value = Mock(
        data=[{"id": "test_id", "created_at": datetime.now().isoformat()}]
    )

    mock_client.table.return_value.select.return_value.execute.return_value = Mock(
        data=[{"id": "test_id", "content": "test_content"}]
    )

    return mock_client

# Performance testing fixtures
@pytest.fixture
def performance_thresholds():
    """Performance thresholds for testing"""
    return {
        "symptom_analysis_max_time": 5.0,  # seconds
        "journey_generation_max_time": 10.0,
        "content_recommendation_max_time": 3.0,
        "crisis_detection_max_time": 2.0,
        "api_response_max_time": 15.0
    }
