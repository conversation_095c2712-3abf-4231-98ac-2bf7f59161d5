import pytest
from unittest.mock import patch, MagicMock
from ai_service.processors.content_personalization import ContentPersonalizationProcessor, QuranV<PERSON>, DhikrPhrase
# Import db_utils to mock its functions
from ai_service import db_utils

# Sample DB data to be returned by mocks
MOCK_DB_VERSES = [
    {"id": "db_q_65_3", "arabic_text": "وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ", "translation_en": "And whoever relies upon Allah - then He is sufficient for him.", "surah_name_en": "At-Talaq", "ayah_number": 3, "theme": "tawakkul", "audio_url": "audio.mp3"},
    {"id": "db_q_13_28", "arabic_text": "أَلَا بِذِكْرِ اللَّهِ تَطْمَئِنُّ الْقُلُوبُ", "translation_en": "Verily, in the remembrance of <PERSON> do hearts find rest.", "surah_name_en": "Ar-Ra'd", "ayah_number": 28, "theme": "sakinah", "audio_url": "audio.mp3"},
    {"id": "db_q_94_5", "arabic_text": "فَإِنَّ مَعَ الْعُسْرِ يُسْرًا", "translation_en": "For indeed, with hardship [will be] ease.", "surah_name_en": "Ash-Sharh", "ayah_number": 5, "theme": "hope", "audio_url": "audio.mp3"},
    {"id": "db_q_2_153", "arabic_text": "يَا أَيُّهَا الَّذِينَ آمَنُوا اسْتَعِينُوا بِالصَّبْرِ وَالصَّلَاةِ", "translation_en": "O you who have believed, seek help through patience and prayer.", "surah_name_en": "Al-Baqarah", "ayah_number": 153, "theme": "patience", "audio_url": "audio.mp3"},
]

MOCK_DB_DHIKR = [
    {"id": "db_d_subhanallah", "arabic_text": "سُبْحَانَ اللَّهِ", "transliteration_en": "SubhanAllah", "translation_en": "Glory be to Allah", "recommended_count": 33, "theme": "calm", "audio_url": "audio.mp3"},
    {"id": "db_d_lahawla", "arabic_text": "لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ", "transliteration_en": "La hawla wa la quwwata illa billah", "translation_en": "There is no power and no strength except with Allah", "recommended_count": 10, "theme": "tawakkul", "audio_url": "audio.mp3"},
    {"id": "db_d_alhamdulillah", "arabic_text": "الْحَمْدُ لِلَّهِ", "transliteration_en": "Alhamdulillah", "translation_en": "All praise is due to Allah", "recommended_count": 33, "theme": "gratitude", "audio_url": "audio.mp3"},
]

@patch('ai_service.processors.content_personalization.db_utils', autospec=True)
class TestContentPersonalizationProcessorWithDB:

    @pytest.fixture
    def processor(self) -> ContentPersonalizationProcessor:
        return ContentPersonalizationProcessor()

    def test_processor_initialization_no_hardcoded_data(self, mock_db_utils: MagicMock, processor: ContentPersonalizationProcessor):
        assert processor is not None
        # Check that hardcoded data attributes are not present or are empty if we decide to clear them in init
        assert not hasattr(processor, 'quran_data') # Or assert that it's empty if structure is kept
        assert not hasattr(processor, 'dhikr_data') # Or assert that it's empty

    @pytest.mark.parametrize("crisis_indicators, expected_themes_to_fetch", [
        (["suicidal", "no hope"], ["hope", "tawakkul"]),
        (["panic attack", "extreme_anxiety"], ["sakinah", "calm", "tawakkul"]),
        (["sadness", "grief"], ["patience", "hope"]),
    ])
    def test_personalize_content_by_crisis_indicator_calls_db(
        self, mock_db_utils: MagicMock, processor: ContentPersonalizationProcessor, crisis_indicators: list[str], expected_themes_to_fetch: list[str]
    ):
        # Mock DB responses for themes
        mock_db_utils.fetch_quran_verses.side_effect = lambda theme, count: [v for v in MOCK_DB_VERSES if v["theme"] == theme][:count]
        mock_db_utils.fetch_dhikr_phrases.side_effect = lambda theme, count: [d for d in MOCK_DB_DHIKR if d["theme"] == theme][:count]

        result = processor.personalize_content(
            user_preferences={}, past_interactions=[], crisis_indicators=crisis_indicators
        )

        # Verify db_utils were called with expected themes
        verse_themes_called = {call_args[1].get('theme') for call_args in mock_db_utils.fetch_quran_verses.call_args_list}
        dhikr_themes_called = {call_args[1].get('theme') for call_args in mock_db_utils.fetch_dhikr_phrases.call_args_list}

        for theme in expected_themes_to_fetch:
            assert theme in verse_themes_called or theme in dhikr_themes_called

        assert len(result['quran_verses']) <= 2
        assert len(result['dhikr_phrases']) <= 2
        assert any(reason_part in result["reasoning"].lower() for reason_part in crisis_indicators)


    def test_personalize_content_with_user_preferred_themes_calls_db(self, mock_db_utils: MagicMock, processor: ContentPersonalizationProcessor):
        user_preferences = {"preferred_themes": ["patience", "gratitude"]}

        mock_db_utils.fetch_quran_verses.side_effect = lambda theme, count: [v for v in MOCK_DB_VERSES if v["theme"] == theme][:count]
        mock_db_utils.fetch_dhikr_phrases.side_effect = lambda theme, count: [d for d in MOCK_DB_DHIKR if d["theme"] == theme][:count]

        result = processor.personalize_content(
            user_preferences=user_preferences, past_interactions=[], crisis_indicators=["mild_stress"]
        )

        # Check if fetch was called for 'patience' or 'gratitude'
        themes_fetched_for_verses = {call[1].get('theme') for call in mock_db_utils.fetch_quran_verses.call_args_list if call[1].get('theme')}
        themes_fetched_for_dhikr = {call[1].get('theme') for call in mock_db_utils.fetch_dhikr_phrases.call_args_list if call[1].get('theme')}

        assert "patience" in themes_fetched_for_verses or "patience" in themes_fetched_for_dhikr or \
               "gratitude" in themes_fetched_for_verses or "gratitude" in themes_fetched_for_dhikr
        assert "preferred themes: patience, gratitude" in result["reasoning"]

    def test_personalize_content_fallback_calls_db_for_random(self, mock_db_utils: MagicMock, processor: ContentPersonalizationProcessor):
        # Ensure theme-specific fetches return empty to trigger fallback
        mock_db_utils.fetch_quran_verses.side_effect = lambda theme, count: [] if theme else [MOCK_DB_VERSES[0]]*count # Random fetch
        mock_db_utils.fetch_dhikr_phrases.side_effect = lambda theme, count: [] if theme else [MOCK_DB_DHIKR[0]]*count # Random fetch

        result = processor.personalize_content(
            user_preferences={}, past_interactions=[], crisis_indicators=["feeling_okay"]
        )

        # Check if fetch_quran_verses/fetch_dhikr_phrases were called with count for random items
        assert any(call[1].get('count') == 2 and not call[1].get('theme') for call in mock_db_utils.fetch_quran_verses.call_args_list)
        assert any(call[1].get('count') == 2 and not call[1].get('theme') for call in mock_db_utils.fetch_dhikr_phrases.call_args_list)
        assert len(result['quran_verses']) > 0
        assert len(result['dhikr_phrases']) > 0

    def test_db_error_handling_returns_empty_lists(self, mock_db_utils: MagicMock, processor: ContentPersonalizationProcessor):
        """Test that if db_utils raises errors, the processor returns empty lists for that content type."""
        mock_db_utils.fetch_quran_verses.side_effect = Exception("DB connection error for Quran")
        mock_db_utils.fetch_dhikr_phrases.return_value = [self.map_to_dhikr(MOCK_DB_DHIKR[0])] # Dhikr fetch is fine

        result = processor.personalize_content({}, [], ["some_crisis"])
        assert result['quran_verses'] == [] # Expect empty due to DB error
        assert len(result['dhikr_phrases']) > 0 # Dhikr should still be there

        mock_db_utils.fetch_quran_verses.side_effect = None # Reset side effect
        mock_db_utils.fetch_quran_verses.return_value = [self.map_to_verse(MOCK_DB_VERSES[0])]
        mock_db_utils.fetch_dhikr_phrases.side_effect = Exception("DB connection error for Dhikr")

        result = processor.personalize_content({}, [], ["some_crisis"])
        assert len(result['quran_verses']) > 0
        assert result['dhikr_phrases'] == []

    # Helper methods to create TypedDicts from mock DB data for assertions if needed elsewhere
    # These are similar to the mapping functions in the processor but for test data setup/assertion.
    def map_to_verse(self, db_row: Dict[str, Any]) -> QuranVerse:
        return {
            "id": str(db_row.get("id")), "arabic_text": db_row.get("arabic_text", ""),
            "translation_en": db_row.get("translation_en", ""), "surah_name_en": db_row.get("surah_name_en", ""),
            "ayah_number": int(db_row.get("ayah_number", 0)), "theme": db_row.get("theme", "general"),
            "audio_url": db_row.get("audio_url")
        }

    def map_to_dhikr(self, db_row: Dict[str, Any]) -> DhikrPhrase:
        return {
            "id": str(db_row.get("id")), "arabic_text": db_row.get("arabic_text", ""),
            "transliteration_en": db_row.get("transliteration_en", ""), "translation_en": db_row.get("translation_en", ""),
            "recommended_count": db_row.get("recommended_count"), "theme": db_row.get("theme", "general"),
            "audio_url": db_row.get("audio_url")
        }

    # Test for limits and deduplication can be adapted.
    # The critical part is that the input to these logics now comes from mocked DB calls.
    def test_personalize_content_limits_recommendations_from_db(self, mock_db_utils: MagicMock, processor: ContentPersonalizationProcessor):
        # Mock DB to return more than 2 items for each theme
        mock_db_utils.fetch_quran_verses.return_value = [self.map_to_verse(v) for v in MOCK_DB_VERSES]
        mock_db_utils.fetch_dhikr_phrases.return_value = [self.map_to_dhikr(d) for d in MOCK_DB_DHIKR]

        result = processor.personalize_content(
            user_preferences={"preferred_themes": ["patience", "hope", "sakinah", "tawakkul"]},
            past_interactions=[],
            crisis_indicators=["suicidal", "panic attack"]
        )
        assert len(result['quran_verses']) <= 2
        assert len(result['dhikr_phrases']) <= 2

    # The output structure test remains largely the same, verifying keys and basic types
    def test_output_structure_and_types_with_db_mock(self, mock_db_utils: MagicMock, processor: ContentPersonalizationProcessor):
        mock_db_utils.fetch_quran_verses.return_value = [self.map_to_verse(MOCK_DB_VERSES[0])]
        mock_db_utils.fetch_dhikr_phrases.return_value = [self.map_to_dhikr(MOCK_DB_DHIKR[0])]

        result = processor.personalize_content({}, [], ["panic attack"])
        assert "quran_verses" in result
        assert "dhikr_phrases" in result
        assert "reasoning" in result
        if result["quran_verses"]:
            assert "id" in result["quran_verses"][0]
        if result["dhikr_phrases"]:
            assert "id" in result["dhikr_phrases"][0]

    # TODO: Add tests for past_interactions if that logic gets more sophisticated
    # TODO: Add tests for user_preferences like favorite_surahs if implemented
```
