"""
Comprehensive tests for FastAPI endpoints
Tests all API endpoints with various scenarios and error handling
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient
from ai_service.main import app


class TestAPIEndpoints:
    """Test suite for FastAPI endpoints"""

    @pytest.fixture
    def client(self):
        """FastAPI test client"""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self, mock_auth_token):
        """Authentication headers for requests"""
        return {"Authorization": mock_auth_token}

    def test_health_check_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "qalb-healing-ai"

    def test_analyze_symptoms_endpoint_success(self, client, auth_headers, sample_assessment_data):
        """Test successful symptom analysis endpoint"""
        request_data = {
            "user_id": "test_user_123",
            "symptoms": {
                "jism": ["sleep_difficulties", "physical_tension"],
                "nafs": ["anxiety_worry", "mood_swings"],
                "qalb": ["distant_from_allah"]
            },
            "intensity": {
                "jism": 6,
                "nafs": 7,
                "qalb": 8
            },
            "duration": "2_weeks",
            "additional_notes": "Feeling spiritually disconnected"
        }

        with patch('ai_service.main.store_analysis'):
            response = client.post(
                "/analyze-symptoms",
                json=request_data,
                headers=auth_headers
            )

        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "analysis_id" in data
        assert "primary_layers_affected" in data
        assert "severity_level" in data
        assert "recommended_journey" in data
        assert "immediate_actions" in data
        assert "spotlight" in data
        assert "estimated_healing_duration" in data
        
        # Verify data types and values
        assert isinstance(data["primary_layers_affected"], list)
        assert data["severity_level"] in ["mild", "moderate", "severe"]
        assert isinstance(data["immediate_actions"], list)
        assert data["estimated_healing_duration"] > 0

    def test_analyze_symptoms_endpoint_missing_auth(self, client):
        """Test symptom analysis without authentication"""
        request_data = {
            "user_id": "test_user_123",
            "symptoms": {"nafs": ["anxiety_worry"]},
            "intensity": {"nafs": 5},
            "duration": "1_week"
        }

        response = client.post("/analyze-symptoms", json=request_data)
        
        assert response.status_code == 403  # Forbidden without auth

    def test_analyze_symptoms_endpoint_invalid_data(self, client, auth_headers):
        """Test symptom analysis with invalid data"""
        invalid_request = {
            "user_id": "",  # Empty user ID
            "symptoms": {},  # Empty symptoms
            "intensity": {},
            "duration": "invalid_duration"
        }

        response = client.post(
            "/analyze-symptoms",
            json=invalid_request,
            headers=auth_headers
        )

        # Should handle gracefully and return fallback analysis
        assert response.status_code == 200
        data = response.json()
        assert "analysis_id" in data

    def test_recommend_content_endpoint_success(self, client, auth_headers):
        """Test successful content recommendation endpoint"""
        request_data = {
            "user_id": "test_user_123",
            "healing_focus": ["anxiety", "spiritual_connection"],
            "current_mood": "anxious",
            "time_available": 15,
            "content_types": ["dhikr", "quran"],
            "user_preferences": {
                "language": "english",
                "difficulty_level": "beginner"
            }
        }

        response = client.post(
            "/recommend-content",
            json=request_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "content_ids" in data
        assert "reasoning" in data
        assert "priority_order" in data
        
        # Verify data types
        assert isinstance(data["content_ids"], list)
        assert isinstance(data["reasoning"], str)
        assert isinstance(data["priority_order"], list)

    def test_generate_journey_endpoint_success(self, client, auth_headers):
        """Test successful journey generation endpoint"""
        request_data = {
            "user_id": "test_user_123",
            "focus_layers": ["qalb", "nafs"],
            "duration_preference": "2_weeks",
            "time_commitment": 30,
            "user_profile": {
                "islamic_knowledge_level": "intermediate",
                "ruqya_experience": "beginner"
            },
            "assessment_results": {
                "primary_layer": "qalb",
                "severity_level": "moderate"
            }
        }

        with patch('ai_service.main.store_journey'):
            response = client.post(
                "/generate-journey",
                json=request_data,
                headers=auth_headers
            )

        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "journey_id" in data
        assert "modules" in data
        assert "daily_practices" in data
        assert "estimated_completion" in data
        
        # Verify data types
        assert isinstance(data["modules"], list)
        assert isinstance(data["daily_practices"], list)

    def test_spiritual_analysis_endpoint_success(self, client, auth_headers, sample_assessment_data):
        """Test spiritual analysis endpoint"""
        response = client.post(
            "/spiritual-analysis/analyze",
            json=sample_assessment_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        
        # Verify comprehensive analysis response
        assert "primaryLayer" in data
        assert "layerInsights" in data
        assert "personalizedMessage" in data
        assert "islamicInsights" in data
        assert "educationalContent" in data
        assert "crisisLevel" in data
        assert "immediateActions" in data
        assert "nextSteps" in data
        assert "recommendedJourneyType" in data
        assert "estimatedHealingDuration" in data
        assert "confidence" in data

    def test_crisis_analysis_endpoint_no_crisis(self, client, auth_headers):
        """Test crisis analysis with no crisis indicators"""
        request_data = {
            "stepId": "emotional_assessment",
            "response": {
                "mood": "slightly_sad",
                "energy_level": "moderate",
                "main_concern": "Just feeling a bit down lately"
            }
        }

        response = client.post(
            "/crisis/analyze",
            json=request_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        
        assert data["crisisLevel"] == "none"
        assert data["urgency"] == "routine"
        assert len(data["indicators"]) == 0

    def test_crisis_analysis_endpoint_high_crisis(self, client, auth_headers):
        """Test crisis analysis with high crisis indicators"""
        request_data = {
            "stepId": "emotional_assessment",
            "response": {
                "mood": "hopeless",
                "thoughts": "I want to hurt myself and end everything",
                "immediate_help": True
            }
        }

        response = client.post(
            "/crisis/analyze",
            json=request_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        
        assert data["crisisLevel"] in ["high", "critical"]
        assert data["urgency"] in ["urgent", "immediate"]
        assert len(data["indicators"]) > 0
        assert len(data["recommendedActions"]) > 0

    def test_journey_parameters_endpoint(self, client, auth_headers, sample_assessment_data, sample_user_profile):
        """Test journey parameters generation endpoint"""
        request_data = {
            "assessment": sample_assessment_data,
            "userProfile": sample_user_profile,
            "preferences": {
                "duration_preference": "2_weeks",
                "time_commitment": "moderate",
                "community_involvement": True
            }
        }

        response = client.post(
            "/journey/generate-parameters",
            json=request_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        
        # Verify journey parameters structure
        assert "type" in data
        assert "duration" in data
        assert "timeCommitment" in data
        assert "primaryLayer" in data
        assert "secondaryLayers" in data
        assert "ruqyaLevel" in data
        assert "communitySupport" in data
        assert "culturalAdaptations" in data
        assert "recommendations" in data

    def test_journey_content_endpoint(self, client, auth_headers, sample_assessment_data, sample_user_profile):
        """Test journey content generation endpoint"""
        request_data = {
            "config": {
                "type": "spiritual_healing",
                "duration": 14,
                "timeCommitment": 30,
                "primaryLayer": "qalb",
                "ruqyaLevel": "basic"
            },
            "userProfile": sample_user_profile,
            "assessment": sample_assessment_data
        }

        response = client.post(
            "/journey/generate-content",
            json=request_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        
        # Verify journey content structure
        assert "title" in data
        assert "description" in data
        assert "personalizedWelcome" in data
        assert "days" in data
        assert len(data["days"]) == 14  # Should match duration

    def test_error_handling_internal_server_error(self, client, auth_headers):
        """Test error handling for internal server errors"""
        # Mock an internal error
        with patch('ai_service.main.analyze_symptoms', side_effect=Exception("Internal error")):
            request_data = {
                "user_id": "test_user",
                "symptoms": {"nafs": ["anxiety"]},
                "intensity": {"nafs": 5},
                "duration": "1_week"
            }

            response = client.post(
                "/analyze-symptoms",
                json=request_data,
                headers=auth_headers
            )

            assert response.status_code == 500
            data = response.json()
            assert "detail" in data
            assert "Internal server error" in data["detail"]

    def test_request_validation_missing_required_fields(self, client, auth_headers):
        """Test request validation with missing required fields"""
        incomplete_request = {
            "user_id": "test_user"
            # Missing symptoms, intensity, duration
        }

        response = client.post(
            "/analyze-symptoms",
            json=incomplete_request,
            headers=auth_headers
        )

        assert response.status_code == 422  # Validation error

    def test_request_validation_invalid_field_types(self, client, auth_headers):
        """Test request validation with invalid field types"""
        invalid_request = {
            "user_id": 123,  # Should be string
            "symptoms": "not_a_dict",  # Should be dict
            "intensity": ["not_a_dict"],  # Should be dict
            "duration": 123  # Should be string
        }

        response = client.post(
            "/analyze-symptoms",
            json=invalid_request,
            headers=auth_headers
        )

        assert response.status_code == 422  # Validation error

    def test_cors_headers(self, client):
        """Test CORS headers are properly set"""
        response = client.options("/health")
        
        # Should have CORS headers (configured in main.py)
        assert response.status_code in [200, 405]  # OPTIONS might not be explicitly handled

    def test_content_type_json_required(self, client, auth_headers):
        """Test that endpoints require JSON content type"""
        # Send form data instead of JSON
        response = client.post(
            "/analyze-symptoms",
            data="user_id=test&symptoms=anxiety",
            headers={**auth_headers, "Content-Type": "application/x-www-form-urlencoded"}
        )

        assert response.status_code == 422  # Should reject non-JSON

    @pytest.mark.asyncio
    async def test_async_endpoint_performance(self, async_client, auth_headers):
        """Test async endpoint performance"""
        request_data = {
            "user_id": "test_user_123",
            "healing_focus": ["anxiety"],
            "current_mood": "anxious"
        }

        import time
        start_time = time.time()
        
        response = await async_client.post(
            "/recommend-content",
            json=request_data,
            headers=auth_headers
        )
        
        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        assert response_time < 10.0  # Should respond within 10 seconds

    def test_rate_limiting_simulation(self, client, auth_headers):
        """Test multiple rapid requests (simulating rate limiting scenarios)"""
        request_data = {
            "user_id": "test_user_123",
            "symptoms": {"nafs": ["anxiety_worry"]},
            "intensity": {"nafs": 5},
            "duration": "1_week"
        }

        # Make multiple rapid requests
        responses = []
        for i in range(5):
            response = client.post(
                "/analyze-symptoms",
                json=request_data,
                headers=auth_headers
            )
            responses.append(response)

        # All should succeed (no rate limiting implemented yet)
        for response in responses:
            assert response.status_code == 200

    def test_large_payload_handling(self, client, auth_headers):
        """Test handling of large payloads"""
        # Create a large request with many symptoms
        large_request = {
            "user_id": "test_user_123",
            "symptoms": {
                "jism": ["sleep_difficulties", "physical_tension", "fatigue", "headaches", "muscle_pain"],
                "nafs": ["anxiety_worry", "overwhelming_sadness", "mood_swings", "irritability", "fear"],
                "aql": ["racing_thoughts", "negative_thoughts", "concentration_issues", "memory_problems"],
                "qalb": ["distant_from_allah", "prayers_mechanical", "reduced_quran_connection"],
                "ruh": ["questioning_purpose", "yearning_eternal"]
            },
            "intensity": {
                "jism": 7, "nafs": 8, "aql": 6, "qalb": 9, "ruh": 5
            },
            "duration": "3_months",
            "additional_notes": "A very long description of symptoms and experiences that goes on for quite a while to test how the system handles larger text inputs and whether it can process them effectively without timing out or causing errors in the analysis pipeline."
        }

        response = client.post(
            "/analyze-symptoms",
            json=large_request,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert "analysis_id" in data

    def test_special_characters_handling(self, client, auth_headers):
        """Test handling of special characters and Unicode"""
        request_data = {
            "user_id": "test_user_123",
            "symptoms": {"nafs": ["anxiety_worry"]},
            "intensity": {"nafs": 5},
            "duration": "1_week",
            "additional_notes": "Feeling disconnected from Allah سبحانه وتعالى and struggling with my prayers 🤲"
        }

        response = client.post(
            "/analyze-symptoms",
            json=request_data,
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert "analysis_id" in data

    def test_api_documentation_endpoints(self, client):
        """Test that API documentation endpoints are accessible"""
        # Test Swagger UI
        docs_response = client.get("/docs")
        assert docs_response.status_code == 200

        # Test ReDoc
        redoc_response = client.get("/redoc")
        assert redoc_response.status_code == 200

        # Test OpenAPI schema
        openapi_response = client.get("/openapi.json")
        assert openapi_response.status_code == 200
        
        # Verify it's valid JSON
        openapi_data = openapi_response.json()
        assert "openapi" in openapi_data
        assert "info" in openapi_data
        assert "paths" in openapi_data
