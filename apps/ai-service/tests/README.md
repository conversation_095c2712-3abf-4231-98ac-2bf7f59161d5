# Qalb Healing AI Service - Comprehensive Test Suite

This directory contains comprehensive tests for the Qalb Healing AI Service, covering all aspects of Islamic mental health analysis and AI processing.

## Test Structure

### Core Test Files

#### 1. **test_spiritual_analysis.py**
Tests the 5-layer Islamic framework analysis:
- **<PERSON><PERSON> (Physical Body)** layer analysis
- **Nafs (Ego/Lower Self)** layer analysis  
- **Aql (Rational Mind)** layer analysis
- **<PERSON><PERSON><PERSON> (Spiritual Heart)** layer analysis
- **<PERSON>uh (Soul)** layer analysis
- Primary layer identification with weighted scoring
- Crisis indicator detection across all layers
- Personalized message generation for different user types
- Islamic insights and educational content generation

#### 2. **test_symptom_analyzer.py**
Tests AI-powered symptom analysis:
- OpenAI integration for Islamic healing analysis
- Symptom categorization by spiritual layers
- Severity assessment (mild/moderate/severe)
- Fallback mechanisms when AI fails
- Islamic framework prompt generation
- Response parsing and validation
- Concurrent analysis handling

#### 3. **test_crisis_detection.py**
Tests Islamic-specific crisis detection:
- Danger keyword detection (suicide, self-harm)
- Spiritual crisis indicators (faith loss, Allah abandonment)
- Cultural crisis situations (family shame, community rejection)
- Ruqya-related crisis indicators (spiritual attacks, jinn)
- Crisis level calculation (none/low/moderate/high/critical)
- Appropriate Islamic crisis response protocols
- Multilingual crisis detection (Arabic terms)

#### 4. **test_journey_generation.py**
Tests personalized healing journey creation:
- Journey type determination based on primary layer
- Duration calculation based on severity and user availability
- Ruqya integration levels (none/basic/intermediate/advanced)
- Cultural adaptations for different Muslim backgrounds
- Community support recommendations
- Daily content generation with Islamic practices
- Professional context adaptations (healthcare, scholars)

#### 5. **test_content_recommendations.py**
Tests Islamic content matching algorithms:
- Content filtering by time, type, and soul layers
- Healing focus alignment scoring
- User preference matching
- Difficulty level appropriateness
- Novelty bonus for new content
- Arabic proficiency considerations
- Ruqya experience-based recommendations
- Mood-based content selection

#### 6. **test_islamic_context.py**
Tests Islamic authenticity and cultural sensitivity:
- Quran and Hadith reference accuracy
- Arabic terminology correctness
- Islamic practice authenticity
- Cultural adaptations for different backgrounds
- Sectarian neutrality maintenance
- Gender-appropriate content
- Convert-specific considerations
- Islamic calendar awareness

#### 7. **test_api_endpoints.py**
Tests all FastAPI endpoints:
- `/analyze-symptoms` - Symptom analysis endpoint
- `/recommend-content` - Content recommendation endpoint
- `/generate-journey` - Journey generation endpoint
- `/spiritual-analysis/analyze` - Comprehensive spiritual analysis
- `/crisis/analyze` - Crisis detection endpoint
- `/journey/generate-parameters` - Journey parameter generation
- `/journey/generate-content` - Journey content creation
- Authentication and authorization
- Error handling and validation
- Performance and response times

#### 8. **test_integration.py**
Tests data flow between components:
- Full assessment to journey workflow
- Symptom analysis to content recommendation flow
- Crisis detection integration across components
- Data consistency across processors
- Error propagation and fallback mechanisms
- Performance under concurrent load
- Islamic authenticity maintenance
- End-to-end user journey testing

#### 9. **test_performance.py**
Tests performance and algorithm accuracy:
- Response time benchmarks
- Concurrent request handling
- Memory usage stability
- Large dataset processing
- Algorithm consistency across runs
- Crisis detection accuracy
- Recommendation relevance
- Scalability with complex user profiles

### Configuration Files

#### **conftest.py**
Comprehensive test configuration providing:
- Sample user profiles (different Islamic knowledge levels)
- Assessment data covering all 5 spiritual layers
- Crisis scenario data
- Islamic content samples
- Journey templates
- Mock OpenAI client responses
- Arabic text samples
- Performance thresholds
- Authentication tokens

## Running Tests

### Prerequisites
```bash
cd apps/ai-service
poetry install --with dev
```

### Run All Tests
```bash
poetry run pytest
```

### Run Specific Test Categories
```bash
# Core functionality tests
poetry run pytest tests/test_spiritual_analysis.py -v

# API endpoint tests
poetry run pytest tests/test_api_endpoints.py -v

# Performance tests
poetry run pytest tests/test_performance.py -v

# Integration tests
poetry run pytest tests/test_integration.py -v

# Islamic context tests
poetry run pytest tests/test_islamic_context.py -v
```

### Run Tests with Coverage
```bash
poetry run pytest --cov=ai_service --cov-report=html --cov-report=term
```

### Run Tests in Parallel
```bash
poetry run pytest -n auto  # Uses pytest-xdist
```

### Run Performance Benchmarks
```bash
poetry run pytest tests/test_performance.py --benchmark-only
```

## Test Data and Fixtures

### User Profiles
- **Beginner**: New Muslim, basic Islamic knowledge
- **Intermediate**: Regular practitioner, moderate knowledge
- **Advanced**: Scholar, expert-level knowledge
- **Healthcare Professional**: Clinical awareness
- **Ruqya Expert**: Spiritual healing specialist

### Cultural Backgrounds
- **Arab**: Traditional Islamic culture, Arabic language
- **South Asian**: Subcontinental traditions, Urdu/Hindi
- **Western Convert**: New Muslim, English-speaking
- **African**: African Islamic traditions
- **Southeast Asian**: Local customs integration

### Assessment Scenarios
- **Normal**: Mild symptoms, no crisis
- **Moderate**: Multiple symptoms, some concern
- **Crisis**: Severe symptoms, immediate help needed
- **Spiritual Focus**: Qalb/Ruh layer issues
- **Emotional Focus**: Nafs layer issues
- **Physical Focus**: Jism layer issues

## Islamic Authenticity Testing

### Quran References
- Verse accuracy and proper citation
- Contextual appropriateness
- Translation quality

### Hadith References
- Authentic narrations only
- Proper attribution
- Relevant application

### Islamic Practices
- Authentic dhikr and duas
- Proper prayer guidance
- Ruqya authenticity
- Cultural sensitivity

### Arabic Text Handling
- Unicode support
- Transliteration accuracy
- Translation quality
- Proper formatting

## Performance Benchmarks

### Response Time Targets
- **Spiritual Analysis**: < 5 seconds
- **Symptom Analysis**: < 5 seconds
- **Journey Generation**: < 10 seconds
- **Content Recommendation**: < 3 seconds
- **Crisis Detection**: < 2 seconds
- **API Endpoints**: < 15 seconds

### Concurrency Targets
- **10 concurrent users**: < 2 seconds average
- **50 concurrent users**: < 5 seconds average
- **100 concurrent users**: < 10 seconds average

### Memory Usage
- **Stable under load**: No memory leaks
- **Efficient processing**: Minimal memory footprint
- **Garbage collection**: Proper cleanup

## Error Handling Testing

### Graceful Degradation
- AI service failures → Fallback algorithms
- Network timeouts → Cached responses
- Invalid input → Validation errors
- Authentication failures → Proper error messages

### Islamic Safety
- Crisis detection → Immediate protocols
- Inappropriate content → Filtering
- Cultural insensitivity → Rejection
- Non-Islamic practices → Exclusion

## Continuous Integration

### Test Automation
- All tests run on every commit
- Performance regression detection
- Islamic authenticity validation
- Security vulnerability scanning

### Quality Gates
- **Test Coverage**: > 90%
- **Performance**: Within benchmarks
- **Islamic Authenticity**: 100% compliance
- **Security**: No vulnerabilities

## Contributing to Tests

### Adding New Tests
1. Follow existing naming conventions
2. Include Islamic context validation
3. Add performance benchmarks
4. Document cultural considerations
5. Ensure comprehensive coverage

### Test Guidelines
- **Islamic Authenticity**: Always validate
- **Cultural Sensitivity**: Test all backgrounds
- **Performance**: Include benchmarks
- **Error Handling**: Test failure scenarios
- **Documentation**: Clear descriptions

### Review Checklist
- [ ] Islamic references are authentic
- [ ] Cultural adaptations are appropriate
- [ ] Performance meets benchmarks
- [ ] Error handling is comprehensive
- [ ] Documentation is complete
