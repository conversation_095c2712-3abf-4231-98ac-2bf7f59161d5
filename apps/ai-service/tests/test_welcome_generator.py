import pytest
from typing import Dict, Any, Optional, List

# Assuming UserProfileData, PersonalizedWelcomeResponse, WelcomeAction are defined in the processor module
# If they are in main.py or a models.py, the import path would change.
# For this test, we'll use the Pydantic models defined within welcome_generator.py itself.
from ai_service.processors.welcome_generator import (
    WelcomeGeneratorProcessor,
    UserProfileData,
    PersonalizedWelcomeResponse,
    WelcomeAction
)

# --- Fixtures for User Profiles ---

@pytest.fixture
def default_user_profile_data() -> UserProfileData:
    return UserProfileData(user_id="default_user_123")

@pytest.fixture
def clinically_aware_profile_data() -> UserProfileData: # Ahmed
    return UserProfileData(
        user_id="ahmed_clinical_1",
        mental_health_awareness={"level": {"mental_health_awareness": "clinical_aware"}}, # Matches current problematic access
        demographics={"fullName": "Ahmed"},
        # User mentioned dealing with anxiety - this info is usually in a field like 'primaryConcern' or 'assessmentReason'
        # For this test, we'll assume the type determination relies on mental_health_awareness.level
    )

@pytest.fixture
def symptom_aware_profile_data() -> UserProfileData: # Layla
    return UserProfileData(user_id="layla_symptom_1") # Default type

@pytest.fixture
def ruqya_expert_profile_data() -> UserProfileData: # Ustadh Saeed
    return UserProfileData(
        user_id="ustadh_saeed_1",
        ruqya_knowledge={"level": {"ruqya_knowledge": "expert"}} # Matches current problematic access
    )

@pytest.fixture
def new_muslim_profile_data() -> UserProfileData:
    return UserProfileData(
        user_id="new_muslim_convert_1",
        life_circumstances={"situations": ["new_muslim_within_2_years"]}
    )

@pytest.fixture
def optimizer_clinical_profile_data() -> UserProfileData: # Dr. Fatima
    return UserProfileData(
        user_id="dr_fatima_opt_clinical",
        spiritual_optimizer={"type": "clinical_integration"},
        demographics={"lastName": "Fatima"}
    )

@pytest.fixture
def optimizer_traditional_profile_data() -> UserProfileData: # Imam Abdullah
    return UserProfileData(
        user_id="imam_abdullah_opt_trad",
        spiritual_optimizer={"type": "traditional_bridge"}
    )

# Profile for testing missing nested keys
@pytest.fixture
def sparse_profile_data() -> UserProfileData:
    return UserProfileData(
        user_id="sparse_user_1",
        mental_health_awareness={}, # Missing 'level'
        ruqya_knowledge=None # Ruqya knowledge is None
    )


# --- Test Class ---

class TestWelcomeGeneratorProcessor:

    @pytest.fixture
    def processor(self) -> WelcomeGeneratorProcessor:
        return WelcomeGeneratorProcessor()

    # --- Tests for _determine_user_type ---
    @pytest.mark.parametrize("profile_data_fixture, expected_type", [
        ("optimizer_clinical_profile_data", "clinically_aware_spiritual_optimizer"),
        ("optimizer_traditional_profile_data", "symptom_aware_spiritual_optimizer"),
        ("ruqya_expert_profile_data", "ruqya_expert"),
        ("clinically_aware_profile_data", "clinically_aware"),
        ("new_muslim_profile_data", "new_muslim"),
        ("symptom_aware_profile_data", "symptom_aware"), # Default
        ("default_user_profile_data", "symptom_aware"), # Also default
    ])
    def test_determine_user_type_various_profiles(self, processor, request, profile_data_fixture, expected_type):
        profile_data = request.getfixturevalue(profile_data_fixture)
        assert processor._determine_user_type(profile_data) == expected_type

    def test_determine_user_type_precedence(self, processor, clinically_aware_profile_data):
        # Optimizer should take precedence over clinically_aware
        optimizer_profile = UserProfileData(
            **clinically_aware_profile_data.model_dump(),
            spiritual_optimizer={"type": "clinical_integration"}
        )
        assert processor._determine_user_type(optimizer_profile) == "clinically_aware_spiritual_optimizer"

    def test_determine_user_type_handles_sparse_data(self, processor, sparse_profile_data):
        # This test will likely fail if the direct access ['level']['mental_health_awareness'] is used
        # without .get() or if the Pydantic models aren't specific enough about nested structures.
        # If it fails, it indicates the need to refactor _determine_user_type for robustness.
        # Assuming current (potentially unsafe) implementation, this test might reveal issues.
        # If the implementation uses .get() properly, it should default.
        # Current implementation will raise KeyError/TypeError. Let's test for that if not refactored.

        # After refactoring _determine_user_type to use safe .get() access,
        # sparse or malformed profiles should now default to 'symptom_aware'
        # instead of raising an error.
        assert processor._determine_user_type(sparse_profile_data) == "symptom_aware"

        profile_missing_inner_key = UserProfileData(
            user_id="test",
            mental_health_awareness={"some_other_key": "value"} # 'level' is missing
        )
        assert processor._determine_user_type(profile_missing_inner_key) == "symptom_aware"

        profile_none_values = UserProfileData(
            user_id="test_none",
            spiritual_optimizer=None,
            ruqya_knowledge=None,
            mental_health_awareness=None,
            life_circumstances=None
        )
        assert processor._determine_user_type(profile_none_values) == "symptom_aware"

        profile_empty_dicts = UserProfileData(
            user_id="test_empty_dicts",
            spiritual_optimizer={},
            ruqya_knowledge={},
            mental_health_awareness={},
            life_circumstances={}
        )
        assert processor._determine_user_type(profile_empty_dicts) == "symptom_aware"

    # --- Tests for generate_welcome ---
    def test_generate_welcome_symptom_aware_layla(self, processor, symptom_aware_profile_data):
        response = processor.generate_welcome(symptom_aware_profile_data)
        assert response.user_type == "symptom_aware"
        assert "As-salamu alaykum, dear brother/sister." in response.greeting
        assert "You mentioned feeling overwhelmed" in response.introduction
        assert "Let's explore what you're experiencing together." in response.explanation
        assert "just share what feels true" in response.motivation
        assert response.primary_action == WelcomeAction(id='begin_assessment', text='Begin Assessment')
        assert WelcomeAction(id='emergency_help', text='I Need Immediate Help') in response.secondary_actions

    def test_generate_welcome_clinically_aware_ahmed(self, processor, clinically_aware_profile_data):
        response = processor.generate_welcome(clinically_aware_profile_data)
        assert response.user_type == "clinically_aware"
        assert f"As-salamu alaykum, {clinically_aware_profile_data.demographics['fullName']}." in response.greeting
        assert "dealing with anxiety/depression" in response.introduction
        assert "five interconnected layers" in response.explanation
        assert "comprehensive spiritual diagnosis" in response.motivation
        assert response.primary_action == WelcomeAction(id='begin_assessment', text='Begin Assessment')
        assert WelcomeAction(id='learn_layers', text='Learn About 5 Layers First') in response.secondary_actions
        assert WelcomeAction(id='emergency_help', text='I Need Immediate Help') in response.secondary_actions

    def test_generate_welcome_ruqya_expert_ustadh_saeed(self, processor, ruqya_expert_profile_data):
        response = processor.generate_welcome(ruqya_expert_profile_data)
        assert response.user_type == "ruqya_expert"
        assert "As-salamu alaykum, respected one." in response.greeting
        assert "MashaAllah, your experience with ruqya" in response.introduction
        assert "complement your ruqya practice" in response.explanation
        assert response.motivation is None
        assert response.primary_action == WelcomeAction(id='advanced_assessment', text='Advanced Assessment')
        assert WelcomeAction(id='standard_assessment', text='Standard Assessment') in response.secondary_actions
        assert WelcomeAction(id='ruqya_focused_diagnosis', text='Ruqya-Focused Diagnosis') in response.secondary_actions
        assert WelcomeAction(id='emergency_help', text='I Need Immediate Help') in response.secondary_actions

    def test_generate_welcome_new_muslim(self, processor, new_muslim_profile_data):
        response = processor.generate_welcome(new_muslim_profile_data)
        assert response.user_type == "new_muslim"
        assert "welcome to the Ummah!" in response.greeting
        assert "journey of healing is for everyone" in response.introduction
        assert "respects your new path" in response.explanation
        assert "Allah says, 'Verily, with hardship, there is relief.'" in response.motivation
        assert response.primary_action == WelcomeAction(id='begin_assessment', text='Begin Assessment')
        assert WelcomeAction(id='learn_islamic_basics_assessment', text='Learn Islamic Wellness Basics') in response.secondary_actions
        assert WelcomeAction(id='emergency_help', text='I Need Immediate Help') in response.secondary_actions

    def test_generate_welcome_optimizer_clinical_dr_fatima(self, processor, optimizer_clinical_profile_data):
        response = processor.generate_welcome(optimizer_clinical_profile_data)
        assert response.user_type == "clinically_aware_spiritual_optimizer"
        assert f"As-salamu alaykum, Dr. {optimizer_clinical_profile_data.demographics['lastName']}." in response.greeting
        assert "pioneering an important field" in response.introduction
        assert "bridge your clinical expertise" in response.explanation
        assert response.motivation is None
        assert response.primary_action == WelcomeAction(id='clinical_islamic_integration_assessment', text='Clinical-Islamic Integration Assessment')
        assert WelcomeAction(id='research_mode_assessment', text='Research Mode') in response.secondary_actions
        assert WelcomeAction(id='prof_dev_assessment', text='Professional Development Focus') in response.secondary_actions
        assert WelcomeAction(id='emergency_help', text='I Need Immediate Help') in response.secondary_actions

    def test_generate_welcome_optimizer_traditional_imam_abdullah(self, processor, optimizer_traditional_profile_data):
        response = processor.generate_welcome(optimizer_traditional_profile_data)
        assert response.user_type == "symptom_aware_spiritual_optimizer"
        assert "As-salamu alaykum, respected Imam/leader." in response.greeting
        assert "classical Islamic spiritual diseases" in response.introduction
        assert "support community members" in response.explanation
        assert response.motivation is None
        assert response.primary_action == WelcomeAction(id='traditional_modern_bridge_assessment', text='Traditional-Modern Bridge Assessment')
        assert WelcomeAction(id='community_leadership_assessment', text='Community Leadership Focus') in response.secondary_actions
        assert WelcomeAction(id='spiritual_diseases_mapping_assessment', text='Spiritual Diseases Mapping') in response.secondary_actions
        assert WelcomeAction(id='emergency_help', text='I Need Immediate Help') in response.secondary_actions

    def test_generate_welcome_minimal_profile_defaults_to_symptom_aware(self, processor):
        minimal_profile = UserProfileData(user_id="minimal_user")
        response = processor.generate_welcome(minimal_profile)
        assert response.user_type == "symptom_aware" # Default kicks in
        assert "As-salamu alaykum, dear brother/sister." in response.greeting # Default greeting
        assert response.primary_action == WelcomeAction(id='begin_assessment', text='Begin Assessment')
        assert WelcomeAction(id='emergency_help', text='I Need Immediate Help') in response.secondary_actions

```
