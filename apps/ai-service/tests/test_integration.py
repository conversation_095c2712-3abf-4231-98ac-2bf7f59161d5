"""
Comprehensive integration tests for AI Service
Tests data flow between components and external integrations
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import json
from ai_service.processors.spiritual_analysis import SpiritualAnalysisProcessor
from ai_service.processors.symptom_analyzer import SymptomAnalyzer
from ai_service.processors.journey_generation import JourneyGenerationProcessor
from ai_service.processors.content_recommender import ContentRecommendationProcessor


class TestAIServiceIntegration:
    """Test suite for AI Service integration scenarios"""

    @pytest.fixture
    def all_processors(self, mock_openai_client):
        """Create all processors for integration testing"""
        with patch('ai_service.processors.symptom_analyzer.openai.OpenAI', return_value=mock_openai_client):
            return {
                'spiritual_analysis': SpiritualAnalysisProcessor(),
                'symptom_analyzer': SymptomAnalyzer(),
                'journey_generation': JourneyGenerationProcessor(),
                'content_recommender': ContentRecommendationProcessor()
            }

    @pytest.mark.asyncio
    async def test_full_assessment_to_journey_workflow(self, all_processors, sample_assessment_data):
        """Test complete workflow from assessment to journey generation"""
        spiritual_processor = all_processors['spiritual_analysis']
        journey_processor = all_processors['journey_generation']
        
        # Step 1: Analyze spiritual landscape
        spiritual_result = spiritual_processor.analyze_spiritual_landscape(sample_assessment_data)
        
        # Step 2: Generate journey parameters based on analysis
        journey_request = {
            'assessment': {
                'primaryLayer': spiritual_result.primary_layer,
                'severityLevel': 'moderate',  # Based on analysis
                'crisisLevel': spiritual_result.crisis_level
            },
            'userProfile': sample_assessment_data['userProfile'],
            'preferences': {
                'duration_preference': 'flexible',
                'time_commitment': 'moderate',
                'community_involvement': True
            }
        }
        
        journey_params = journey_processor.generate_journey_parameters(journey_request)
        
        # Step 3: Generate journey content
        content_request = {
            'config': {
                'type': journey_params['type'],
                'duration': journey_params['duration'],
                'timeCommitment': journey_params['timeCommitment'],
                'primaryLayer': journey_params['primaryLayer'],
                'ruqyaLevel': journey_params['ruqyaLevel']
            },
            'userProfile': sample_assessment_data['userProfile'],
            'assessment': journey_request['assessment']
        }
        
        journey_content = journey_processor.generate_journey_content(content_request)
        
        # Verify integration
        assert spiritual_result.primary_layer == journey_params['primaryLayer']
        assert journey_params['duration'] > 0
        assert len(journey_content['days']) == journey_params['duration']
        assert journey_content['title']
        assert journey_content['personalizedWelcome']

    @pytest.mark.asyncio
    async def test_symptom_analysis_to_content_recommendation_flow(self, all_processors, sample_symptoms, sample_intensity):
        """Test flow from symptom analysis to content recommendations"""
        symptom_analyzer = all_processors['symptom_analyzer']
        content_recommender = all_processors['content_recommender']
        
        # Step 1: Analyze symptoms
        symptom_result = await symptom_analyzer.analyze_symptoms(
            symptoms=sample_symptoms,
            intensity=sample_intensity,
            duration="2_weeks"
        )
        
        # Step 2: Get content recommendations based on analysis
        with patch.object(content_recommender, '_get_content_library', return_value=[]):
            with patch.object(content_recommender, '_get_user_profile', return_value={}):
                content_result = await content_recommender.recommend_content(
                    user_id="test_user_123",
                    healing_focus=symptom_result.primary_layers,
                    current_mood="anxious",
                    soul_layers=symptom_result.primary_layers
                )
        
        # Verify integration
        assert len(symptom_result.primary_layers) > 0
        assert isinstance(content_result.content_ids, list)
        assert content_result.reasoning

    @pytest.mark.asyncio
    async def test_crisis_detection_integration(self, all_processors, crisis_assessment_data):
        """Test crisis detection across all components"""
        spiritual_processor = all_processors['spiritual_analysis']
        journey_processor = all_processors['journey_generation']
        
        # Step 1: Detect crisis in spiritual analysis
        spiritual_result = spiritual_processor.analyze_spiritual_landscape(crisis_assessment_data)
        
        # Should detect crisis
        assert spiritual_result.crisis_level in ['moderate', 'high', 'critical']
        assert len(spiritual_result.crisis_indicators) > 0
        
        # Step 2: Journey generation should adapt for crisis
        journey_request = {
            'assessment': {
                'primaryLayer': spiritual_result.primary_layer,
                'severityLevel': 'severe',
                'crisisLevel': spiritual_result.crisis_level
            },
            'userProfile': crisis_assessment_data['userProfile'],
            'preferences': {'community_involvement': True}
        }
        
        journey_params = journey_processor.generate_journey_parameters(journey_request)
        
        # Crisis users should get enhanced support
        assert journey_params['communitySupport'] in [True, 'essential', 'required']
        recommendations_text = ' '.join(journey_params['recommendations']).lower()
        assert 'crisis' in recommendations_text or 'support' in recommendations_text

    def test_data_consistency_across_processors(self, all_processors, sample_assessment_data):
        """Test that data remains consistent across different processors"""
        spiritual_processor = all_processors['spiritual_analysis']
        journey_processor = all_processors['journey_generation']
        
        # Analyze with spiritual processor
        spiritual_result = spiritual_processor.analyze_spiritual_landscape(sample_assessment_data)
        
        # Generate journey parameters
        journey_request = {
            'assessment': {
                'primaryLayer': spiritual_result.primary_layer,
                'severityLevel': 'moderate',
                'crisisLevel': spiritual_result.crisis_level
            },
            'userProfile': sample_assessment_data['userProfile'],
            'preferences': {}
        }
        
        journey_params = journey_processor.generate_journey_parameters(journey_request)
        
        # Data should be consistent
        assert spiritual_result.primary_layer == journey_params['primaryLayer']
        assert spiritual_result.crisis_level == journey_request['assessment']['crisisLevel']

    @pytest.mark.asyncio
    async def test_error_propagation_and_fallbacks(self, all_processors):
        """Test error handling and fallback mechanisms across components"""
        symptom_analyzer = all_processors['symptom_analyzer']
        
        # Test with invalid data that should trigger fallbacks
        invalid_symptoms = {}
        invalid_intensity = {}
        
        # Should not raise exception, should use fallback
        result = await symptom_analyzer.analyze_symptoms(
            symptoms=invalid_symptoms,
            intensity=invalid_intensity,
            duration="invalid_duration"
        )
        
        # Should return valid fallback result
        assert result.severity_level in ['mild', 'moderate', 'severe']
        assert result.confidence_score <= 0.7  # Fallback should have lower confidence

    @pytest.mark.asyncio
    async def test_performance_under_load(self, all_processors, sample_assessment_data):
        """Test system performance with multiple concurrent requests"""
        spiritual_processor = all_processors['spiritual_analysis']
        
        # Create multiple concurrent analysis tasks
        tasks = []
        for i in range(5):
            # Modify assessment data slightly for each task
            modified_data = {**sample_assessment_data}
            modified_data['userProfile']['user_id'] = f"user_{i}"
            
            task = asyncio.create_task(
                asyncio.to_thread(
                    spiritual_processor.analyze_spiritual_landscape,
                    modified_data
                )
            )
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should complete successfully
        assert len(results) == 5
        for result in results:
            assert not isinstance(result, Exception)
            assert result.primary_layer in ['jism', 'nafs', 'aql', 'qalb', 'ruh']

    def test_islamic_authenticity_across_components(self, all_processors, sample_assessment_data):
        """Test Islamic authenticity is maintained across all components"""
        spiritual_processor = all_processors['spiritual_analysis']
        journey_processor = all_processors['journey_generation']
        
        # Get spiritual analysis
        spiritual_result = spiritual_processor.analyze_spiritual_landscape(sample_assessment_data)
        
        # Generate journey
        journey_request = {
            'assessment': {'primaryLayer': spiritual_result.primary_layer, 'severityLevel': 'moderate'},
            'userProfile': sample_assessment_data['userProfile'],
            'preferences': {}
        }
        
        journey_params = journey_processor.generate_journey_parameters(journey_request)
        journey_content = journey_processor.generate_journey_content({
            'config': {
                'type': journey_params['type'],
                'duration': journey_params['duration'],
                'primaryLayer': journey_params['primaryLayer'],
                'ruqyaLevel': journey_params['ruqyaLevel']
            },
            'userProfile': sample_assessment_data['userProfile'],
            'assessment': journey_request['assessment']
        })
        
        # Check Islamic authenticity across all outputs
        all_text = (
            ' '.join(spiritual_result.islamic_insights) +
            spiritual_result.educational_content +
            journey_content['title'] +
            journey_content['description'] +
            journey_content['personalizedWelcome']
        ).lower()
        
        islamic_terms = ['allah', 'quran', 'prophet', 'islamic', 'dhikr', 'prayer']
        assert any(term in all_text for term in islamic_terms)

    @pytest.mark.asyncio
    async def test_user_profile_consistency(self, all_processors, sample_user_profile):
        """Test that user profile data is consistently used across components"""
        content_recommender = all_processors['content_recommender']
        journey_processor = all_processors['journey_generation']
        
        # Test content recommendations
        with patch.object(content_recommender, '_get_content_library', return_value=[]):
            with patch.object(content_recommender, '_get_user_profile', return_value=sample_user_profile):
                content_result = await content_recommender.recommend_content(
                    user_id=sample_user_profile['user_id'],
                    healing_focus=['anxiety']
                )
        
        # Test journey generation
        journey_request = {
            'assessment': {'primaryLayer': 'nafs', 'severityLevel': 'moderate'},
            'userProfile': sample_user_profile,
            'preferences': {}
        }
        
        journey_params = journey_processor.generate_journey_parameters(journey_request)
        
        # User profile should influence both recommendations and journey
        assert content_result.personalization_factors
        assert journey_params['ruqyaLevel']  # Should be set based on user profile

    def test_layer_focus_consistency(self, all_processors, sample_assessment_data):
        """Test that layer focus is consistent across all components"""
        spiritual_processor = all_processors['spiritual_analysis']
        journey_processor = all_processors['journey_generation']
        
        # Force a specific primary layer
        modified_assessment = {**sample_assessment_data}
        modified_assessment['spiritualExperiences']['symptoms'] = ['distant_from_allah', 'prayers_mechanical']
        modified_assessment['spiritualExperiences']['intensity'] = 'severe'
        
        spiritual_result = spiritual_processor.analyze_spiritual_landscape(modified_assessment)
        
        # Should identify qalb as primary layer due to severe spiritual symptoms
        assert spiritual_result.primary_layer == 'qalb'
        
        # Journey should focus on the same layer
        journey_request = {
            'assessment': {
                'primaryLayer': spiritual_result.primary_layer,
                'severityLevel': 'moderate'
            },
            'userProfile': modified_assessment['userProfile'],
            'preferences': {}
        }
        
        journey_params = journey_processor.generate_journey_parameters(journey_request)
        
        assert journey_params['primaryLayer'] == 'qalb'

    @pytest.mark.asyncio
    async def test_recommendation_relevance_integration(self, all_processors, sample_assessment_data):
        """Test that content recommendations are relevant to assessment results"""
        spiritual_processor = all_processors['spiritual_analysis']
        content_recommender = all_processors['content_recommender']
        
        # Get spiritual analysis
        spiritual_result = spiritual_processor.analyze_spiritual_landscape(sample_assessment_data)
        
        # Mock content library with relevant content
        mock_content = [
            {
                "id": "qalb_healing_001",
                "type": "dhikr",
                "healing_focus": ["spiritual_connection"],
                "soul_layers": ["qalb"],
                "difficulty": "beginner",
                "duration": 15
            },
            {
                "id": "nafs_purification_002", 
                "type": "dua",
                "healing_focus": ["emotional_balance"],
                "soul_layers": ["nafs"],
                "difficulty": "intermediate",
                "duration": 20
            }
        ]
        
        with patch.object(content_recommender, '_get_content_library', return_value=mock_content):
            with patch.object(content_recommender, '_get_user_profile', return_value={}):
                content_result = await content_recommender.recommend_content(
                    user_id="test_user",
                    healing_focus=["spiritual_connection"],
                    soul_layers=[spiritual_result.primary_layer]
                )
        
        # Recommendations should be relevant to primary layer
        if spiritual_result.primary_layer == 'qalb':
            assert any('qalb' in content_id for content_id in content_result.content_ids)

    def test_cultural_adaptation_integration(self, all_processors):
        """Test cultural adaptations work across all components"""
        journey_processor = all_processors['journey_generation']
        
        # Test with different cultural backgrounds
        cultural_profiles = [
            {"cultural_background": "arab", "islamic_knowledge_level": "advanced"},
            {"cultural_background": "western_convert", "islamic_knowledge_level": "beginner"},
            {"cultural_background": "south_asian", "islamic_knowledge_level": "intermediate"}
        ]
        
        for profile in cultural_profiles:
            adaptations = journey_processor._determine_cultural_adaptations(profile)
            
            # Should have appropriate adaptations for each culture
            assert len(adaptations) > 0
            adaptations_text = ' '.join(adaptations).lower()
            
            if profile["cultural_background"] == "western_convert":
                assert "basic" in adaptations_text or "explanation" in adaptations_text
            elif profile["cultural_background"] == "arab":
                assert "arabic" in adaptations_text or "traditional" in adaptations_text

    @pytest.mark.asyncio
    async def test_end_to_end_user_journey(self, all_processors, sample_assessment_data):
        """Test complete end-to-end user journey through the system"""
        spiritual_processor = all_processors['spiritual_analysis']
        symptom_analyzer = all_processors['symptom_analyzer']
        journey_processor = all_processors['journey_generation']
        content_recommender = all_processors['content_recommender']
        
        # Step 1: Initial assessment
        spiritual_result = spiritual_processor.analyze_spiritual_landscape(sample_assessment_data)
        
        # Step 2: Symptom analysis
        symptoms = {
            spiritual_result.primary_layer: ["anxiety_worry", "spiritual_disconnection"]
        }
        intensity = {spiritual_result.primary_layer: 7}
        
        symptom_result = await symptom_analyzer.analyze_symptoms(
            symptoms=symptoms,
            intensity=intensity,
            duration="2_weeks"
        )
        
        # Step 3: Journey generation
        journey_request = {
            'assessment': {
                'primaryLayer': spiritual_result.primary_layer,
                'severityLevel': symptom_result.severity_level,
                'crisisLevel': spiritual_result.crisis_level
            },
            'userProfile': sample_assessment_data['userProfile'],
            'preferences': {'duration_preference': '2_weeks'}
        }
        
        journey_params = journey_processor.generate_journey_parameters(journey_request)
        
        # Step 4: Content recommendations
        with patch.object(content_recommender, '_get_content_library', return_value=[]):
            with patch.object(content_recommender, '_get_user_profile', return_value={}):
                content_result = await content_recommender.recommend_content(
                    user_id="test_user",
                    healing_focus=symptom_result.primary_layers,
                    soul_layers=[spiritual_result.primary_layer]
                )
        
        # Verify end-to-end consistency
        assert spiritual_result.primary_layer == journey_params['primaryLayer']
        assert spiritual_result.crisis_level == journey_request['assessment']['crisisLevel']
        assert symptom_result.severity_level == journey_request['assessment']['severityLevel']
        
        # All components should produce valid outputs
        assert spiritual_result.confidence > 0
        assert symptom_result.confidence_score > 0
        assert journey_params['duration'] > 0
        assert isinstance(content_result.content_ids, list)
