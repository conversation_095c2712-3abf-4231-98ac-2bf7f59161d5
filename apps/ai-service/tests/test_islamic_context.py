"""
Comprehensive tests for Islamic Context and Cultural Sensitivity
Tests authentic Islamic content, cultural adaptations, and religious accuracy
"""

import pytest
from unittest.mock import Mock, patch
import re
from ai_service.processors.spiritual_analysis import SpiritualAnalysisProcessor
from ai_service.processors.content_recommender import ContentRecommendationProcessor
from ai_service.processors.journey_generation import JourneyGenerationProcessor


class TestIslamicContextAuthenticity:
    """Test suite for Islamic authenticity and cultural sensitivity"""

    @pytest.fixture
    def spiritual_processor(self):
        return SpiritualAnalysisProcessor()

    @pytest.fixture
    def content_processor(self):
        return ContentRecommendationProcessor()

    @pytest.fixture
    def journey_processor(self):
        return JourneyGenerationProcessor()

    def test_quran_references_authenticity(self, spiritual_processor):
        """Test that Quran references are authentic and properly formatted"""
        # Test Nafs layer analysis which should contain Quran 91:7-10
        emotional_data = {"symptoms": ["anxiety_worry"], "intensity": "mild"}
        reflections = {}
        
        result = spiritual_processor.analyze_nafs_layer(emotional_data, reflections)
        
        # Should contain authentic Quran reference
        assert "91:7-10" in result.islamic_context
        # Should mention the actual verse content
        assert "proportioned" in result.islamic_context or "purifies" in result.islamic_context

    def test_hadith_references_authenticity(self, spiritual_processor):
        """Test that Hadith references are authentic"""
        spiritual_data = {"symptoms": ["distant_from_allah"], "intensity": "moderate"}
        reflections = {}
        
        result = spiritual_processor.analyze_qalb_layer(spiritual_data, reflections)
        
        # Should contain authentic Hadith reference about the heart
        assert "prophet" in result.islamic_context.lower()
        assert "heart" in result.islamic_context.lower()
        # Should reference the famous hadith about the heart being sound/corrupt
        assert ("sound" in result.islamic_context.lower() or 
                "corrupt" in result.islamic_context.lower())

    def test_arabic_terminology_accuracy(self, arabic_text_samples):
        """Test that Arabic terms are used correctly"""
        # Test common Islamic terms
        islamic_terms = {
            "amanah": "trust",
            "tawakkul": "trust in Allah",
            "sabr": "patience",
            "dhikr": "remembrance",
            "khushu": "presence of heart",
            "istighfar": "seeking forgiveness"
        }
        
        # These terms should appear in appropriate contexts
        for arabic_term, meaning in islamic_terms.items():
            # Test that the term is used in meaningful contexts
            assert isinstance(arabic_term, str)
            assert len(arabic_term) > 0

    def test_islamic_practices_authenticity(self, spiritual_processor):
        """Test that recommended Islamic practices are authentic"""
        physical_data = {"symptoms": ["sleep_difficulties"], "intensity": "mild"}
        emotional_data = {"symptoms": ["anxiety_worry"], "intensity": "mild"}
        mental_data = {"symptoms": ["racing_thoughts"], "intensity": "mild"}
        spiritual_data = {"symptoms": ["distant_from_allah"], "intensity": "mild"}
        reflections = {}

        # Get recommendations from all layers
        jism_result = spiritual_processor.analyze_jism_layer(physical_data, reflections)
        nafs_result = spiritual_processor.analyze_nafs_layer(emotional_data, reflections)
        aql_result = spiritual_processor.analyze_aql_layer(mental_data, reflections)
        qalb_result = spiritual_processor.analyze_qalb_layer(spiritual_data, reflections)

        all_recommendations = (jism_result.recommendations + nafs_result.recommendations + 
                             aql_result.recommendations + qalb_result.recommendations)
        
        recommendations_text = ' '.join(all_recommendations).lower()
        
        # Should contain authentic Islamic practices
        authentic_practices = ["dhikr", "prayer", "quran", "dua", "istighfar", "salah"]
        assert any(practice in recommendations_text for practice in authentic_practices)
        
        # Should not contain non-Islamic practices
        non_islamic_practices = ["meditation" if "islamic" not in recommendations_text else None,
                               "yoga", "chakra", "crystal", "astrology"]
        non_islamic_practices = [p for p in non_islamic_practices if p is not None]
        assert not any(practice in recommendations_text for practice in non_islamic_practices)

    def test_cultural_sensitivity_different_backgrounds(self, journey_processor):
        """Test cultural adaptations for different Muslim backgrounds"""
        # Test Arab background
        arab_profile = {
            "cultural_background": "arab",
            "preferred_language": "arabic",
            "islamic_knowledge_level": "advanced"
        }
        arab_adaptations = journey_processor._determine_cultural_adaptations(arab_profile)
        
        # Test South Asian background
        south_asian_profile = {
            "cultural_background": "south_asian",
            "preferred_language": "urdu",
            "islamic_knowledge_level": "intermediate"
        }
        south_asian_adaptations = journey_processor._determine_cultural_adaptations(south_asian_profile)
        
        # Test Western convert background
        convert_profile = {
            "cultural_background": "western_convert",
            "preferred_language": "english",
            "islamic_knowledge_level": "beginner"
        }
        convert_adaptations = journey_processor._determine_cultural_adaptations(convert_profile)
        
        # Each should have appropriate adaptations
        assert len(arab_adaptations) > 0
        assert len(south_asian_adaptations) > 0
        assert len(convert_adaptations) > 0
        
        # Convert adaptations should be more explanatory
        convert_text = ' '.join(convert_adaptations).lower()
        assert ("explanation" in convert_text or "basic" in convert_text or 
                "introduction" in convert_text)

    def test_islamic_knowledge_level_appropriateness(self, spiritual_processor):
        """Test that content is appropriate for different Islamic knowledge levels"""
        # Test beginner level
        beginner_profile = {"islamic_knowledge_level": "beginner"}
        layer_analyses = {"qalb": Mock(severity_score=50)}
        
        beginner_message = spiritual_processor.generate_personalized_message(
            beginner_profile, layer_analyses, "qalb"
        )
        
        # Should be encouraging and not assume advanced knowledge
        assert "allah" in beginner_message.lower()
        assert ("broken" not in beginner_message.lower() or 
                "not" in beginner_message.lower())  # Should be encouraging
        
        # Test advanced level
        advanced_profile = {"islamic_knowledge_level": "advanced"}
        advanced_message = spiritual_processor.generate_personalized_message(
            advanced_profile, layer_analyses, "qalb"
        )
        
        # Can use more sophisticated terminology
        assert len(advanced_message) > 0

    def test_gender_appropriate_content(self):
        """Test that content recommendations are gender-appropriate"""
        # This is a placeholder for gender-specific content testing
        # In Islamic context, some practices may have gender-specific guidelines
        
        male_profile = {"gender": "male", "islamic_knowledge_level": "intermediate"}
        female_profile = {"gender": "female", "islamic_knowledge_level": "intermediate"}
        
        # Both should receive appropriate content
        assert male_profile["gender"] == "male"
        assert female_profile["gender"] == "female"
        
        # Content should be respectful and appropriate for both

    def test_sectarian_neutrality(self, spiritual_processor):
        """Test that content remains sectarian-neutral and focuses on common Islamic principles"""
        assessment_data = {
            "userProfile": {"islamic_knowledge_level": "intermediate"},
            "spiritualExperiences": {"symptoms": ["distant_from_allah"], "intensity": "moderate"},
            "emotionalExperiences": {"symptoms": ["anxiety_worry"], "intensity": "mild"},
            "mentalExperiences": {"symptoms": ["racing_thoughts"], "intensity": "mild"},
            "physicalExperiences": {"symptoms": ["fatigue"], "intensity": "mild"},
            "reflections": {"main_concern": "spiritual connection"}
        }
        
        result = spiritual_processor.analyze_spiritual_landscape(assessment_data)
        
        # Should focus on universally accepted Islamic principles
        all_content = (result.personalized_message + ' '.join(result.islamic_insights) + 
                      result.educational_content).lower()
        
        # Should contain universal Islamic concepts
        universal_concepts = ["allah", "quran", "prophet", "prayer", "dhikr"]
        assert any(concept in all_content for concept in universal_concepts)
        
        # Should avoid sectarian-specific terminology
        sectarian_terms = ["sunni", "shia", "madhab", "sect"]
        assert not any(term in all_content for term in sectarian_terms)

    def test_arabic_text_handling(self, arabic_text_samples):
        """Test proper handling of Arabic text"""
        for category, sample in arabic_text_samples.items():
            arabic_text = sample["text"]
            transliteration = sample["transliteration"]
            translation = sample["translation"]
            
            # Arabic text should be valid Unicode
            assert isinstance(arabic_text, str)
            assert len(arabic_text) > 0
            
            # Should have proper transliteration
            assert isinstance(transliteration, str)
            assert len(transliteration) > 0
            
            # Should have meaningful translation
            assert isinstance(translation, str)
            assert len(translation) > 0
            
            # Arabic text should contain Arabic characters
            arabic_pattern = re.compile(r'[\u0600-\u06FF]')
            assert arabic_pattern.search(arabic_text) is not None

    def test_ruqya_content_authenticity(self, islamic_content_samples):
        """Test that ruqya content is authentic and safe"""
        ruqya_content = [content for content in islamic_content_samples 
                        if content.get("ruqya_component")]
        
        for content in ruqya_content:
            # Should contain authentic Quranic verses or authentic duas
            assert content["type"] in ["quran", "dua", "dhikr"]
            
            # Should have proper attribution
            if content["type"] == "quran":
                assert "surah" in content["title"].lower() or "quran" in content["title"].lower()

    def test_crisis_response_islamic_appropriateness(self):
        """Test that crisis responses are Islamically appropriate"""
        from ai_service.endpoints.crisis_analysis import determine_urgency_and_actions
        
        # Test high crisis response
        urgency, actions = determine_urgency_and_actions('high', ['severe_distress: hopeless'])
        
        actions_text = ' '.join(actions).lower()
        
        # Should include Islamic support options
        islamic_support_terms = ["islamic", "imam", "community", "spiritual", "counselor"]
        assert any(term in actions_text for term in islamic_support_terms)
        
        # Should also include professional help
        professional_terms = ["professional", "counselor", "support"]
        assert any(term in actions_text for term in professional_terms)

    def test_prayer_time_awareness(self):
        """Test awareness of Islamic prayer times in recommendations"""
        # This tests that the system is aware of Islamic daily rhythm
        
        # Morning recommendations should align with Fajr/morning practices
        morning_practices = ["morning_dhikr", "fajr_reflection", "dawn_prayer"]
        
        # Evening recommendations should align with Maghrib/Isha practices  
        evening_practices = ["evening_dhikr", "maghrib_reflection", "night_prayer"]
        
        # Both should be valid Islamic practices
        for practice in morning_practices + evening_practices:
            assert isinstance(practice, str)
            assert len(practice) > 0

    def test_ramadan_and_hajj_awareness(self):
        """Test system awareness of major Islamic observances"""
        # System should be able to adapt for Ramadan, Hajj, etc.
        
        ramadan_considerations = [
            "fasting_schedule", "iftar_timing", "suhur_practices", 
            "night_prayers", "increased_worship"
        ]
        
        hajj_considerations = [
            "pilgrimage_preparation", "spiritual_purification",
            "increased_dhikr", "dua_acceptance"
        ]
        
        # These should be valid considerations
        for consideration in ramadan_considerations + hajj_considerations:
            assert isinstance(consideration, str)
            assert len(consideration) > 0

    def test_family_and_community_integration(self, journey_processor):
        """Test that recommendations consider Islamic family/community context"""
        # Test family-oriented profile
        family_profile = {
            "family_situation": "married_with_children",
            "community_involvement": "active",
            "support_system": "family_friends_community"
        }
        
        assessment = {"crisisLevel": "none", "severityLevel": "mild"}
        
        community_support = journey_processor._recommend_community_support(family_profile, assessment)
        
        # Should recommend community involvement for stable family situations
        assert community_support in [True, "recommended", "beneficial"]

    def test_convert_specific_considerations(self, spiritual_processor):
        """Test special considerations for new Muslims/converts"""
        convert_profile = {
            "islamic_knowledge_level": "beginner",
            "cultural_background": "western_convert",
            "conversion_timeframe": "recent"
        }
        
        layer_analyses = {"qalb": Mock(severity_score=40)}
        
        message = spiritual_processor.generate_personalized_message(
            convert_profile, layer_analyses, "qalb"
        )
        
        # Should be especially encouraging and educational
        assert len(message) > 0
        # Should not assume prior Islamic knowledge
        message_lower = message.lower()
        assert "allah" in message_lower  # Should introduce Islamic concepts gently

    def test_professional_islamic_counselor_integration(self):
        """Test integration with Islamic counseling approaches"""
        # System should be compatible with Islamic counseling methods
        
        islamic_counseling_approaches = [
            "quran_based_therapy", "prophetic_guidance", "islamic_cbt",
            "spiritual_counseling", "islamic_psychology"
        ]
        
        # These should be recognized as valid approaches
        for approach in islamic_counseling_approaches:
            assert isinstance(approach, str)
            assert "islamic" in approach or "quran" in approach or "prophetic" in approach

    @pytest.mark.parametrize("cultural_background,expected_adaptations", [
        ("arab", ["arabic_language_support", "traditional_practices"]),
        ("south_asian", ["urdu_hindi_support", "subcontinental_traditions"]),
        ("african", ["african_islamic_traditions", "community_emphasis"]),
        ("western_convert", ["basic_explanations", "gradual_introduction"]),
        ("southeast_asian", ["local_customs_integration", "community_practices"])
    ])
    def test_cultural_background_specific_adaptations(self, journey_processor, cultural_background, expected_adaptations):
        """Test specific adaptations for different cultural backgrounds"""
        profile = {
            "cultural_background": cultural_background,
            "islamic_knowledge_level": "intermediate"
        }
        
        adaptations = journey_processor._determine_cultural_adaptations(profile)
        adaptations_text = ' '.join(adaptations).lower()
        
        # Should contain culturally relevant adaptations
        assert len(adaptations) > 0
        # The specific adaptations will vary, but should be culturally sensitive
        assert cultural_background.replace("_", " ") in adaptations_text or any(
            expected.replace("_", " ") in adaptations_text for expected in expected_adaptations
        )

    def test_islamic_calendar_awareness(self):
        """Test awareness of Islamic calendar and significant dates"""
        # System should be aware of Islamic months and their significance
        
        islamic_months = [
            "muharram", "safar", "rabi_al_awwal", "rabi_al_thani",
            "jumada_al_awwal", "jumada_al_thani", "rajab", "shaban",
            "ramadan", "shawwal", "dhul_qadah", "dhul_hijjah"
        ]
        
        significant_months = ["ramadan", "dhul_hijjah", "muharram", "rajab"]
        
        # These should be recognized as significant
        for month in significant_months:
            assert month in islamic_months
