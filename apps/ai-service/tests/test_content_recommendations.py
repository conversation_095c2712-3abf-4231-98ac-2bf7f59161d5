"""
Comprehensive tests for Content Recommendation System
Tests Islamic content matching algorithms and personalization
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from ai_service.processors.content_recommender import ContentRecommendationProcessor, ContentRecommendation


class TestContentRecommendationProcessor:
    """Test suite for Content Recommendation Processor"""

    @pytest.fixture
    def processor(self):
        """Create a ContentRecommendationProcessor instance"""
        return ContentRecommendationProcessor()

    @pytest.fixture
    def sample_content_library(self):
        """Sample Islamic content library for testing"""
        return [
            {
                "id": "dhikr_anxiety_001",
                "type": "dhikr",
                "title": "Dhikr for Anxiety Relief",
                "healing_focus": ["anxiety", "trust_in_allah"],
                "soul_layers": ["nafs", "qalb"],
                "difficulty": "beginner",
                "duration": 10,
                "arabic_text": "لا حول ولا قوة إلا بالله",
                "translation": "There is no power except with <PERSON>"
            },
            {
                "id": "quran_healing_002",
                "type": "quran",
                "title": "Surah Al-Fatiha for Healing",
                "healing_focus": ["spiritual_connection", "general_healing"],
                "soul_layers": ["qalb", "ruh"],
                "difficulty": "beginner",
                "duration": 15,
                "ruqya_component": True
            },
            {
                "id": "dua_depression_003",
                "type": "dua",
                "title": "Du'a for Depression",
                "healing_focus": ["depression", "hope", "mercy"],
                "soul_layers": ["nafs", "qalb"],
                "difficulty": "intermediate",
                "duration": 20
            },
            {
                "id": "meditation_peace_004",
                "type": "meditation",
                "title": "Islamic Mindfulness Practice",
                "healing_focus": ["peace", "mindfulness", "presence"],
                "soul_layers": ["aql", "qalb"],
                "difficulty": "advanced",
                "duration": 25
            },
            {
                "id": "ruqya_protection_005",
                "type": "ruqya",
                "title": "Protective Ruqya Session",
                "healing_focus": ["protection", "spiritual_cleansing"],
                "soul_layers": ["qalb", "ruh"],
                "difficulty": "intermediate",
                "duration": 30,
                "ruqya_component": True
            }
        ]

    @pytest.fixture
    def sample_user_profile(self):
        """Sample user profile for recommendations"""
        return {
            "user_id": "test_user_123",
            "skill_level": "intermediate",
            "preferred_content_types": ["dhikr", "quran", "dua"],
            "completed_content": ["dhikr_anxiety_001"],
            "favorite_practices": ["morning_dhikr", "evening_reflection"],
            "arabic_proficiency": "basic",
            "time_preferences": {"morning": 15, "evening": 20},
            "ruqya_experience": "beginner"
        }

    def test_processor_initialization(self, processor):
        """Test processor initializes correctly"""
        assert processor is not None
        assert hasattr(processor, 'content_library')
        assert hasattr(processor, 'user_profiles')
        assert hasattr(processor, 'recommendation_cache')

    @pytest.mark.asyncio
    async def test_recommend_content_basic(self, processor, sample_content_library, sample_user_profile):
        """Test basic content recommendation"""
        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=sample_user_profile):
                
                result = await processor.recommend_content(
                    user_id="test_user_123",
                    healing_focus=["anxiety", "spiritual_connection"],
                    current_mood="anxious",
                    time_available=15,
                    content_types=["dhikr", "quran"]
                )

        assert isinstance(result, ContentRecommendation)
        assert len(result.content_ids) > 0
        assert result.reasoning
        assert len(result.priority_order) == len(result.content_ids)
        assert len(result.confidence_scores) > 0
        assert isinstance(result.personalization_factors, list)

    @pytest.mark.asyncio
    async def test_recommend_content_anxiety_focus(self, processor, sample_content_library, sample_user_profile):
        """Test content recommendation for anxiety-focused healing"""
        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=sample_user_profile):
                
                result = await processor.recommend_content(
                    user_id="test_user_123",
                    healing_focus=["anxiety", "trust_in_allah"],
                    current_mood="anxious"
                )

        # Should prioritize anxiety-related content
        assert "dhikr_anxiety_001" in result.content_ids or any(
            "anxiety" in content_id for content_id in result.content_ids
        )

    @pytest.mark.asyncio
    async def test_recommend_content_spiritual_focus(self, processor, sample_content_library, sample_user_profile):
        """Test content recommendation for spiritual healing"""
        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=sample_user_profile):
                
                result = await processor.recommend_content(
                    user_id="test_user_123",
                    healing_focus=["spiritual_connection", "general_healing"],
                    soul_layers=["qalb", "ruh"]
                )

        # Should include Quran and spiritual content
        recommended_content = [c for c in sample_content_library if c["id"] in result.content_ids]
        spiritual_content = [c for c in recommended_content if "spiritual" in ' '.join(c["healing_focus"])]
        assert len(spiritual_content) > 0

    def test_filter_content_by_time(self, processor, sample_content_library):
        """Test content filtering by available time"""
        # Filter for short sessions (15 minutes)
        filtered = processor._filter_content(
            content_list=sample_content_library,
            time_available=15
        )

        # Should only include content that fits in 15 minutes
        for content in filtered:
            assert content["duration"] <= 15

    def test_filter_content_by_type(self, processor, sample_content_library):
        """Test content filtering by content type"""
        filtered = processor._filter_content(
            content_list=sample_content_library,
            content_types=["dhikr", "dua"]
        )

        # Should only include dhikr and dua content
        for content in filtered:
            assert content["type"] in ["dhikr", "dua"]

    def test_filter_content_by_soul_layers(self, processor, sample_content_library):
        """Test content filtering by soul layers"""
        filtered = processor._filter_content(
            content_list=sample_content_library,
            soul_layers=["qalb"]
        )

        # Should only include content targeting qalb layer
        for content in filtered:
            assert "qalb" in content["soul_layers"]

    @pytest.mark.asyncio
    async def test_score_content_healing_focus_alignment(self, processor, sample_content_library, sample_user_profile):
        """Test content scoring based on healing focus alignment"""
        healing_focus = ["anxiety", "trust_in_allah"]
        
        scored_content = await processor._score_content(
            content_list=sample_content_library,
            healing_focus=healing_focus,
            current_mood="anxious",
            user_profile=sample_user_profile
        )

        # Content with matching healing focus should score higher
        anxiety_content = next(c for c in scored_content if c["id"] == "dhikr_anxiety_001")
        other_content = next(c for c in scored_content if c["id"] == "meditation_peace_004")
        
        assert anxiety_content["score"] > other_content["score"]

    @pytest.mark.asyncio
    async def test_score_content_user_preferences(self, processor, sample_content_library, sample_user_profile):
        """Test content scoring based on user preferences"""
        scored_content = await processor._score_content(
            content_list=sample_content_library,
            healing_focus=["general_healing"],
            current_mood="neutral",
            user_profile=sample_user_profile
        )

        # Content matching user's preferred types should score higher
        preferred_content = [c for c in scored_content if c["type"] in sample_user_profile["preferred_content_types"]]
        non_preferred_content = [c for c in scored_content if c["type"] not in sample_user_profile["preferred_content_types"]]
        
        if preferred_content and non_preferred_content:
            avg_preferred_score = sum(c["score"] for c in preferred_content) / len(preferred_content)
            avg_non_preferred_score = sum(c["score"] for c in non_preferred_content) / len(non_preferred_content)
            assert avg_preferred_score >= avg_non_preferred_score

    @pytest.mark.asyncio
    async def test_score_content_difficulty_match(self, processor, sample_content_library, sample_user_profile):
        """Test content scoring based on difficulty level match"""
        scored_content = await processor._score_content(
            content_list=sample_content_library,
            healing_focus=["general_healing"],
            current_mood="neutral",
            user_profile=sample_user_profile
        )

        # Content matching user's skill level should get bonus points
        intermediate_content = [c for c in scored_content if c["difficulty"] == "intermediate"]
        beginner_content = [c for c in scored_content if c["difficulty"] == "beginner"]
        
        # Intermediate content should score higher for intermediate user
        if intermediate_content and beginner_content:
            max_intermediate_score = max(c["score"] for c in intermediate_content)
            max_beginner_score = max(c["score"] for c in beginner_content)
            # Allow for some variation but intermediate should generally score higher
            assert max_intermediate_score >= max_beginner_score - 0.1

    def test_calculate_mood_relevance(self, processor):
        """Test mood-based content relevance calculation"""
        # Anxious mood should prefer calming content
        calming_content = {
            "healing_focus": ["peace", "calm", "trust_in_allah"],
            "type": "dhikr"
        }
        
        anxiety_relevance = processor._calculate_mood_relevance(calming_content, "anxious")
        assert anxiety_relevance > 0

        # Sad mood should prefer uplifting content
        uplifting_content = {
            "healing_focus": ["hope", "mercy", "comfort"],
            "type": "dua"
        }
        
        sadness_relevance = processor._calculate_mood_relevance(uplifting_content, "sad")
        assert sadness_relevance > 0

    @pytest.mark.asyncio
    async def test_generate_reasoning(self, processor):
        """Test AI-powered reasoning generation"""
        healing_focus = ["anxiety", "spiritual_connection"]
        current_mood = "anxious"
        top_recommendations = [
            {"id": "dhikr_anxiety_001", "title": "Dhikr for Anxiety", "type": "dhikr"},
            {"id": "quran_healing_002", "title": "Quran Healing", "type": "quran"}
        ]

        reasoning = await processor._generate_reasoning(healing_focus, current_mood, top_recommendations)

        assert isinstance(reasoning, str)
        assert len(reasoning) > 0
        assert "anxiety" in reasoning.lower()
        assert any(rec["title"].lower() in reasoning.lower() for rec in top_recommendations)

    def test_identify_personalization_factors(self, processor, sample_user_profile):
        """Test identification of personalization factors"""
        healing_focus = ["anxiety", "spiritual_connection"]
        current_mood = "anxious"

        factors = processor._identify_personalization_factors(
            healing_focus, current_mood, sample_user_profile
        )

        assert isinstance(factors, list)
        assert len(factors) > 0
        
        factors_text = ' '.join(factors).lower()
        assert "anxiety" in factors_text or "spiritual" in factors_text

    @pytest.mark.asyncio
    async def test_recommend_content_novelty_bonus(self, processor, sample_content_library):
        """Test that new content gets novelty bonus"""
        # User who has completed some content
        experienced_user = {
            "user_id": "experienced_user",
            "skill_level": "intermediate",
            "preferred_content_types": ["dhikr", "quran"],
            "completed_content": ["dhikr_anxiety_001", "quran_healing_002"],  # Has completed some content
            "arabic_proficiency": "basic"
        }

        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=experienced_user):
                
                result = await processor.recommend_content(
                    user_id="experienced_user",
                    healing_focus=["general_healing"]
                )

        # Should prioritize content not yet completed
        uncompleted_content = [cid for cid in result.content_ids 
                             if cid not in experienced_user["completed_content"]]
        assert len(uncompleted_content) > 0

    @pytest.mark.asyncio
    async def test_recommend_content_ruqya_experience(self, processor, sample_content_library):
        """Test recommendations based on ruqya experience"""
        # Beginner ruqya user
        beginner_user = {
            "user_id": "beginner_ruqya",
            "skill_level": "beginner",
            "ruqya_experience": "beginner",
            "preferred_content_types": ["dhikr", "quran"],
            "completed_content": []
        }

        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=beginner_user):
                
                result = await processor.recommend_content(
                    user_id="beginner_ruqya",
                    healing_focus=["spiritual_cleansing", "protection"]
                )

        # Should not recommend advanced ruqya content to beginners
        recommended_content = [c for c in sample_content_library if c["id"] in result.content_ids]
        ruqya_content = [c for c in recommended_content if c.get("ruqya_component")]
        
        for content in ruqya_content:
            assert content["difficulty"] in ["beginner", "intermediate"]

    @pytest.mark.asyncio
    async def test_recommend_content_arabic_proficiency(self, processor, sample_content_library):
        """Test recommendations based on Arabic proficiency"""
        # User with limited Arabic
        limited_arabic_user = {
            "user_id": "limited_arabic",
            "skill_level": "intermediate",
            "arabic_proficiency": "none",
            "preferred_content_types": ["dhikr", "dua"],
            "completed_content": []
        }

        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=limited_arabic_user):
                
                result = await processor.recommend_content(
                    user_id="limited_arabic",
                    healing_focus=["general_healing"]
                )

        # Should prioritize content with translations
        recommended_content = [c for c in sample_content_library if c["id"] in result.content_ids]
        translated_content = [c for c in recommended_content if "translation" in c]
        
        assert len(translated_content) > 0

    @pytest.mark.asyncio
    async def test_recommend_content_time_constraints(self, processor, sample_content_library, sample_user_profile):
        """Test recommendations with strict time constraints"""
        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=sample_user_profile):
                
                result = await processor.recommend_content(
                    user_id="test_user_123",
                    healing_focus=["general_healing"],
                    time_available=12  # Very limited time
                )

        # All recommended content should fit within time limit
        recommended_content = [c for c in sample_content_library if c["id"] in result.content_ids]
        for content in recommended_content:
            assert content["duration"] <= 12

    @pytest.mark.asyncio
    async def test_recommend_content_empty_library(self, processor, sample_user_profile):
        """Test recommendation with empty content library"""
        with patch.object(processor, '_get_content_library', return_value=[]):
            with patch.object(processor, '_get_user_profile', return_value=sample_user_profile):
                
                result = await processor.recommend_content(
                    user_id="test_user_123",
                    healing_focus=["general_healing"]
                )

        # Should handle empty library gracefully
        assert isinstance(result, ContentRecommendation)
        assert len(result.content_ids) == 0
        assert result.reasoning  # Should still provide reasoning

    @pytest.mark.asyncio
    async def test_concurrent_recommendations(self, processor, sample_content_library, sample_user_profile):
        """Test handling multiple concurrent recommendation requests"""
        with patch.object(processor, '_get_content_library', return_value=sample_content_library):
            with patch.object(processor, '_get_user_profile', return_value=sample_user_profile):
                
                tasks = []
                for i in range(3):
                    task = processor.recommend_content(
                        user_id=f"user_{i}",
                        healing_focus=["anxiety", "peace"],
                        current_mood="anxious"
                    )
                    tasks.append(task)

                results = await asyncio.gather(*tasks)

        assert len(results) == 3
        for result in results:
            assert isinstance(result, ContentRecommendation)
            assert len(result.content_ids) > 0

    def test_content_scoring_bounds(self, processor, sample_content_library, sample_user_profile):
        """Test that content scores are within expected bounds"""
        async def test_scoring():
            scored_content = await processor._score_content(
                content_list=sample_content_library,
                healing_focus=["general_healing"],
                current_mood="neutral",
                user_profile=sample_user_profile
            )

            for content in scored_content:
                assert 0 <= content["score"] <= 1.0

        asyncio.run(test_scoring())

    @pytest.mark.parametrize("mood,expected_content_type", [
        ("anxious", "dhikr"),
        ("sad", "dua"),
        ("peaceful", "meditation"),
        ("confused", "quran")
    ])
    def test_mood_based_content_preference(self, processor, mood, expected_content_type):
        """Test that different moods prefer appropriate content types"""
        sample_content = {
            "dhikr": {"healing_focus": ["calm", "peace"], "type": "dhikr"},
            "dua": {"healing_focus": ["hope", "comfort"], "type": "dua"},
            "meditation": {"healing_focus": ["mindfulness", "presence"], "type": "meditation"},
            "quran": {"healing_focus": ["guidance", "clarity"], "type": "quran"}
        }

        content = sample_content[expected_content_type]
        relevance = processor._calculate_mood_relevance(content, mood)
        
        # Should have positive relevance for appropriate content
        assert relevance >= 0
