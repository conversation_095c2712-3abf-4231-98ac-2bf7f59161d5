/**
 * Jest Configuration for Qalb Healing Backend
 * Comprehensive testing setup for NestJS backend with Islamic content validation
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup.ts'],

  testMatch: ['<rootDir>/__tests__/**/*.{test,spec}.{js,ts}'],

  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/**/*.d.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.module.ts',
    '!src/main.ts',
  ],

  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],

  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    './src/auth/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    './src/assessment/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    './src/journey/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },

  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@auth/(.*)$': '<rootDir>/src/auth/$1',
    '^@assessment/(.*)$': '<rootDir>/src/assessment/$1',
    '^@journey/(.*)$': '<rootDir>/src/journey/$1',
    '^@crisis/(.*)$': '<rootDir>/src/crisis/$1',
    '^@community/(.*)$': '<rootDir>/src/community/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^../src/utils/logger$': '<rootDir>/__tests__/__mocks__/logger.ts',
  },

  transform: {
    '^.+\\.(ts|js)$': 'ts-jest',
  },

  moduleFileExtensions: ['ts', 'js', 'json'],

  testTimeout: 10000,
  verbose: true,
  bail: false,
  maxWorkers: '50%',

  // Islamic content validation
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
    },
    __ISLAMIC_CONTENT_VALIDATION__: true,
    __ARABIC_TEXT_VALIDATION__: true,
    __QURAN_REFERENCE_VALIDATION__: true,
  },

  // Test reporting
  reporters: ['default'],

  // Performance monitoring
  detectOpenHandles: false,
  detectLeaks: false,
  logHeapUsage: false,

  // Error handling
  errorOnDeprecated: true,

  // Cache configuration
  cacheDirectory: '<rootDir>/.jest-cache',
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
};
