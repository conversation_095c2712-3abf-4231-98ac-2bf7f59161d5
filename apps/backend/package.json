{"name": "@qalb-healing-workspace/backend", "version": "0.0.1", "private": true, "dependencies": {"@prisma/client": "^6.10.1", "@supabase/supabase-js": "^2.38.4", "@types/uuid": "^10.0.0", "axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "openai": "^4.20.1", "rate-limit-redis": "^4.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^11.1.0", "validator": "^13.11.0", "winston": "^3.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.23", "@types/jest": "^29.5.12", "@types/node": "^20.19.1", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/validator": "^13.11.7", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "prisma": "^6.10.1", "supabase": "^2.26.9", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "nx": {"targets": {"serve-ts": {"executor": "nx:run-commands", "options": {"cwd": "apps/backend", "command": "ts-node-dev --respawn --transpile-only src/main.ts", "watch": ["src"], "envFile": ".env"}}, "serve-exp": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "backend:build", "watch": true}, "configurations": {"development": {"buildTarget": "backend:build:development"}, "production": {"buildTarget": "backend:build:production"}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "@qalb-healing-workspace/backend:build", "watch": true, "debounce": 0}, "configurations": {"development": {"buildTarget": "@qalb-healing-workspace/backend:build:development"}, "production": {"buildTarget": "@qalb-healing-workspace/backend:build:production"}}}}}, "scripts": {"dev": "ts-node-dev --respawn --transpile-only --env-file .env src/main.ts", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "db:backup": "bash scripts/backup.sh qalb_healing ./backups"}}