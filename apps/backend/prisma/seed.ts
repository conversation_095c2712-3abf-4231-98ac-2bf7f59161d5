import { PrismaClient, Prisma } from '@prisma/client';
const prisma = new PrismaClient();

async function main() {
  console.log('Starting Prisma seed script...');
  // Example: Seed a user and an assessment session
  // const userId = '00000000-0000-0000-0000-000000000001';
  // try {
  //   await prisma.profile.upsert({
  //     where: { id: userId },
  //     update: {},
  //     create: {
  //       id: userId,
  //       email: '<EMAIL>',
  //       fullName: 'Seed User',
  //     },
  //   });
  //   await prisma.assessmentSession.upsert({
  //     where: { id: '11111111-1111-1111-1111-111111111111' },
  //     update: {},
  //     create: {
  //       id: '11111111-1111-1111-1111-111111111111',
  //       userId,
  //       userProfile: { name: 'Seed User', email: '<EMAIL>' },
  //       startedAt: new Date(),
  //       currentStep: 'welcome',
  //       totalSteps: 10,
  //     },
  //   });
  //   console.log('Example user and session seeded.');
  // } catch (e) {
  //   console.warn(
  //     'Could not seed example user/session (might already exist or foreign key issue):',
  //     e
  //   );
  // }

  console.log('Calling seedAssessmentQuestions...');
  await seedAssessmentQuestions();
  console.log('seedAssessmentQuestions finished.');

  console.log('Calling seedNameOfAllahContent...');
  await seedNameOfAllahContent();
  console.log('seedNameOfAllahContent finished.');

  console.log('Calling seedQuranicVerseContent...');
  await seedQuranicVerseContent();
  console.log('seedQuranicVerseContent finished.');

  console.log('Calling seedSunnahPracticeContent...');
  await seedSunnahPracticeContent();
  console.log('seedSunnahPracticeContent finished.');

  console.log('Calling seedSampleJourney...');
  await seedSampleJourney();
  console.log('seedSampleJourney finished.');
}

async function seedSampleJourney() {
  console.log('Seeding sample Journey...');

  // 1. Ensure a sample user exists
  const userEmail = '<EMAIL>';
  let user = await prisma.profile.findUnique({ where: { email: userEmail } });
  if (!user) {
    user = await prisma.profile.create({
      data: {
        id: '00000000-0000-0000-0000-000000000010', // Fixed UUID for sample user
        email: userEmail,
        fullName: 'Journey User',
        firstName: 'Journey', // Added to satisfy required field
        // Optionally add lastName if required by schema
      },
    });
    console.log(`Created sample user: ${user.email}`);
  } else {
    console.log(`Found existing sample user: ${user.email}`);
  }
  const userId = user.id;

  // 2. Ensure a sample AssessmentSession and SpiritualDiagnosis exist
  const assessmentSessionId = '7b9d6b3a-5b0f-4b0f-8b0f-0b3a7b9d6b3a'; // Fixed UUID
  const spiritualDiagnosisId = '8c0e7c4b-6c1c-5c1c-9c1c-1c4b8c0e7c4b'; // Fixed UUID
  const journeyTitle = 'My First Healing Journey (Seed)';

  let assessmentSession = await prisma.assessmentSession.findUnique({ where: { id: assessmentSessionId }});
  if (!assessmentSession) {
    assessmentSession = await prisma.assessmentSession.create({
      data: {
        id: assessmentSessionId,
        userId: userId,
        userProfile: { name: user.fullName, email: user.email }, // Simplified userProfile JSON
        startedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        currentStep: 'completed',
        totalSteps: 5, // Example
      }
    });
    console.log(`Created sample assessment session: ${assessmentSession.id}`);
  }

  let diagnosis = await prisma.spiritualDiagnosis.findUnique({ where: { id: spiritualDiagnosisId }});
  if (!diagnosis) {
    diagnosis = await prisma.spiritualDiagnosis.create({
      data: {
        id: spiritualDiagnosisId,
        userId: userId,
        assessmentId: assessmentSessionId,
        diagnosisData: { severityScore: 6, primaryConcern: "Anxiety" }, // Example diagnosis data
        primaryLayer: "qalb",
        overallSeverity: "moderate",
        recommendedJourneyType: "heart_purification",
        estimatedHealingDuration: 21,
        generatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      }
    });
    console.log(`Created sample spiritual diagnosis: ${diagnosis.id}`);
  }

  // 3. Check if the sample journey already exists
  const existingJourney = await prisma.journey.findFirst({
    where: { title: journeyTitle, userId: userId },
  });

  if (existingJourney) {
    console.log('Sample Journey already seeded. Skipping.');
    return;
  }

  // 4. Fetch seeded content to link
  const arRahman = await prisma.nameOfAllahContent.findUnique({ where: { name: 'Ar-Rahman' } });
  const alFatiha1 = await prisma.quranicVerseContent.findUnique({ where: { surahNumber_ayahNumber: { surahNumber: 1, ayahNumber: 1 } } });
  const mindfulWudu = await prisma.sunnahPracticeContent.findUnique({ where: { title: 'Mindful Wudu (Ablution)' } });

  if (!arRahman || !alFatiha1 || !mindfulWudu) {
    console.error('Required seeded content (Ar-Rahman, Al-Fatiha Ayah 1, Mindful Wudu) not found. Cannot seed sample journey.');
    return;
  }

  // 5. Create the Journey with nested JourneyDays and DailyPracticeInJourney
  try {
    const sampleJourney = await prisma.journey.create({
      data: {
        userId: userId,
        assessmentId: assessmentSessionId, // FIX: use assessmentSessionId, not spiritualDiagnosisId
        type: 'heart_purification', // From enum JourneyType
        status: 'active', // From enum JourneyStatus
        title: journeyTitle,
        description: 'A sample 2-day journey focusing on heart purification, seeded for demonstration.',
        personalizedWelcome: `Assalamu Alaikum ${user.fullName}, welcome to your seeded healing journey!`,
        duration: 2, // Short journey for seeding example
        dailyTimeCommitment: 20,
        primaryLayer: 'qalb', // From enum LayerFocus
        communityIntegration: false,
        userProfile: { awarenessLevel: 'symptom_aware', profession: 'student', ruqyaFamiliarity: 'none' },
        currentDay: 1,
        startedAt: new Date(),
        journeyDays: {
          create: [
            // Day 1
            {
              dayNumber: 1,
              theme: 'Opening the Heart with Mercy',
              learningObjective: 'To understand and connect with Allah\'s mercy and begin self-reflection.',
              dailyPracticesInJourney: {
                create: [
                  { // Comp 1: Morning CheckIn
                    type: 'MorningCheckIn', title: 'Day 1: Morning Check-In', description: 'Start your day with intention.', duration: 2, orderIndex: 0,
                    instructions: 'Assess your mood, energy, spiritual state, and set your Niyyah.',
                    componentData: { moodScaleMin: 1, moodScaleMax: 10, moodDescriptorLow: "Heavy", moodDescriptorHigh: "Light", energyPrompt: "Energy (1-10)?", spiritualStatePrompt: "Spiritual state?", intentionPrompt: "Today's Niyyah?" },
                    layerFocus: 'qalb',
                  },
                  { // Comp 2: Name of Allah
                    type: 'NameOfAllahSpotlight', title: arRahman.name, description: arRahman.meaning, duration: 7, orderIndex: 1,
                    nameOfAllahContentId: arRahman.id, // Link to Ar-Rahman
                    componentData: { originalContentId: arRahman.id, ...arRahman }, // Denormalize for ease or specific fields
                    layerFocus: 'qalb',
                    instructions: 'Reflect on the meaning and significance of this Name of Allah.', // Added
                  },
                  { // Comp 3: Quranic Verse
                    type: 'QuranicVerseReflection', title: `Surah ${alFatiha1.surahNumber}:${alFatiha1.ayahNumber}`, description: 'Reflect on the opening of the Quran.', duration: 5, orderIndex: 2,
                    quranicVerseContentId: alFatiha1.id, // Link to Al-Fatiha Ayah 1
                    componentData: { originalContentId: alFatiha1.id, ...alFatiha1 },
                    layerFocus: 'ruh',
                    instructions: 'Read the verse and reflect on its meaning in your life.', // Added
                  },
                  { // Comp 4: Journaling
                    type: 'PersonalReflectionJournaling', title: 'Day 1: Reflection', description: 'Journal your thoughts on today\'s learnings.', duration: 5, orderIndex: 3,
                    instructions: 'Use the prompts to guide your reflection.',
                    componentData: { prompts: ["How did focusing on Ar-Rahman make you feel?", "What does 'guidance' in Al-Fatiha mean to you right now?"] },
                    layerFocus: 'aql',
                  },
                  { // Comp 5: Sunnah Practice
                    type: 'SunnahPractice', title: mindfulWudu.title, description: mindfulWudu.description.substring(0,100)+'...', duration: 3, orderIndex: 4,
                    sunnahPracticeContentId: mindfulWudu.id, // Link to Mindful Wudu
                    componentData: { originalContentId: mindfulWudu.id, category: mindfulWudu.category },
                    layerFocus: 'jism',
                    instructions: 'Perform the Sunnah practice as described.', // Added
                  },
                ],
              },
            },
            // Day 2 (can add more practices or different ones)
            {
              dayNumber: 2,
              theme: 'Deepening Trust and Gratitude',
              learningObjective: 'To build on trust in Allah and cultivate gratitude.',
              dailyPracticesInJourney: {
                create: [
                   { type: 'MorningCheckIn', title: 'Day 2: Morning Check-In', description: 'Begin with awareness.', duration: 2, orderIndex: 0, instructions:'...', componentData: {moodScaleMin:1, moodScaleMax:10}, layerFocus: 'qalb' },
                   // ... Add other 4 components for Day 2, potentially linking to other seeded content or using generic ones
                ],
              },
            },
          ],
        },
      },
    });
    console.log(`Seeded sample Journey: ${sampleJourney.title} (ID: ${sampleJourney.id})`);
  } catch (error) {
    console.error('Error seeding sample journey:', error);
  }
}


async function seedSunnahPracticeContent() {
  console.log('Seeding Sunnah Practice content...');

  const existingMindfulWudu = await prisma.sunnahPracticeContent.findUnique({
    where: { title: 'Mindful Wudu (Ablution)' },
  });

  if (existingMindfulWudu) {
    console.log('Sunnah Practice content already seeded (found Mindful Wudu). Skipping.');
    return;
  }

  const practices = [
    {
      title: 'Mindful Wudu (Ablution)',
      category: 'Physical', // Matches SunnahPracticeCategory enum (string form)
      description: 'Performing Wudu (ablution) with full presence, mindfulness, and understanding of its spiritual dimensions.',
      stepsJson: [
        { stepNumber: 1, instruction: "Begin with the Niyyah (intention) in your heart to purify yourself for worship or closeness to Allah." },
        { stepNumber: 2, instruction: "Say 'Bismillah' (In the name of Allah)." },
        { stepNumber: 3, instruction: "Wash your hands up to the wrists three times, ensuring water reaches between the fingers. Reflect on washing away minor sins committed by the hands." },
        { stepNumber: 4, instruction: "Rinse your mouth three times. Consider this as cleansing your speech from falsehood and harmful words." },
        { stepNumber: 5, instruction: "Gently sniff water into your nostrils and expel it (Istinshaaq and Istinthaar) three times. Think of it as purifying your senses." },
        { stepNumber: 6, instruction: "Wash your face three times, from the hairline to the chin and from ear to ear. Imagine light (Nur) enveloping your face." },
        { stepNumber: 7, instruction: "Wash your right arm up to and including the elbow three times, then your left arm similarly. Reflect on the strength these limbs will have for good deeds." },
        { stepNumber: 8, instruction: "Wipe your head (Masah) once, from front to back and then back to front (or a portion of it). Seek protection for your thoughts." },
        { stepNumber: 9, instruction: "Wash your right foot up to and including the ankle three times, then your left foot similarly. Intend to walk only towards good." },
        { stepNumber: 10, instruction: "Recite the Shahada and the Du'a after Wudu: 'Ash-hadu an la ilaha illAllah wahdahu la sharika lah, wa ash-hadu anna Muhammadan 'abduhu wa Rasuluh. Allahumma-j'alni minat-tawwabina waj'alni minal-mutatahhirin.' (I bear witness that there is no god but Allah alone, with no partner, and I bear witness that Muhammad is His servant and Messenger. O Allah, make me among those who repent often and make me among those who purify themselves.)" }
      ],
      intention: "To purify my body and soul in accordance with the Sunnah, preparing myself for connection with Allah, and seeking His acceptance.",
      reflection: "As the water cleanses your limbs, visualize your sins being washed away. Feel the spiritual refreshment and readiness for worship. How does this physical act of purification translate to your inner state?",
      benefits: ["Physical cleanliness", "Spiritual purification", "Removal of minor sins", "Increased mindfulness", "Preparation for prayer and Quran recitation"],
      estimatedDuration: 7, // Fixed property name
      difficultyLevel: 'beginner',
      sourceReference: "Various Ahadith on the Wudu of Prophet Muhammad (PBUH), e.g., Sahih al-Bukhari, Sahih Muslim.",
      layerFocus: ['jism', 'qalb', 'ruh'],
      isActive: true,
    },
    {
      title: 'Smiling is a Charity (Sadaqah)',
      category: 'Social',
      description: "The Prophet Muhammad (PBUH) emphasized the importance of smiling at others, considering it an act of charity.",
      stepsJson: [
        { stepNumber: 1, instruction: "Make a conscious intention to smile at fellow Muslims (and others) for the sake of Allah, to spread positivity and follow the Sunnah." },
        { stepNumber: 2, instruction: "When you meet someone, offer a genuine, warm smile." },
        { stepNumber: 3, instruction: "Maintain positive and approachable body language along with your smile." }
      ],
      intention: "To follow the Sunnah of the Prophet (PBUH), to spread kindness and brotherhood/sisterhood, and to earn reward from Allah for a simple act of charity.",
      reflection: "How does a simple smile affect your own mood and the mood of those around you? How can this small act build bridges and soften hearts in your daily interactions?",
      benefits: ["Earns reward (Sadaqah)", "Spreads positivity and happiness", "Strengthens social bonds", "Easy to implement", "Follows Prophetic example"],
      estimatedDuration: 1, // Per interaction
      difficultyLevel: 'beginner',
      sourceReference: "Jami` at-Tirmidhi, Hadith 1956 (Hasan)",
      layerFocus: ['nafs', 'qalb'],
      isActive: true,
    }
  ];

  for (const practiceData of practices) {
    await prisma.sunnahPracticeContent.upsert({
      where: { title: practiceData.title },
      update: {
        category: practiceData.category as any, // Prisma expects enum
        description: practiceData.description,
        stepsJson: practiceData.stepsJson as Prisma.JsonArray,
        intention: practiceData.intention,
        reflection: practiceData.reflection,
        benefits: practiceData.benefits,
        estimatedDuration: practiceData.estimatedDuration, // Fixed property name
        difficultyLevel: practiceData.difficultyLevel,
        sourceReference: practiceData.sourceReference,
        layerFocus: practiceData.layerFocus as any, // Prisma expects LayerFocus[]
        isActive: practiceData.isActive,
      },
      create: {
        title: practiceData.title,
        category: practiceData.category as any, // Prisma expects enum
        description: practiceData.description,
        stepsJson: practiceData.stepsJson as Prisma.JsonArray,
        intention: practiceData.intention,
        reflection: practiceData.reflection,
        benefits: practiceData.benefits,
        estimatedDuration: practiceData.estimatedDuration, // Fixed property name
        difficultyLevel: practiceData.difficultyLevel,
        sourceReference: practiceData.sourceReference,
        layerFocus: practiceData.layerFocus as any, // Prisma expects LayerFocus[]
        isActive: practiceData.isActive,
      }
    });
    console.log(`Seeded Sunnah Practice: ${practiceData.title}`);
  }
  console.log('Sunnah Practice content seeding finished.');
}


async function seedQuranicVerseContent() {
  console.log('Seeding Quranic Verse content...');

  const existingFatiha = await prisma.quranicVerseContent.findUnique({
    where: { surahNumber_ayahNumber: { surahNumber: 1, ayahNumber: 1 } },
  });

  if (existingFatiha) {
    console.log('Quranic Verse content already seeded (found Al-Fatiha). Skipping.');
    return;
  }

  const verses = [
    {
      surahNumber: 1,
      ayahNumber: 1,
      arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      translationEn: 'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
      tafsirEn: 'This verse, known as the Basmala, is recited before starting most actions, signifying that one begins in the name of Allah, seeking His help and blessings. It encompasses His two primary names of mercy, Ar-Rahman (mercy to all creation) and Ar-Rahim (specific mercy to believers).',
      reflectionPrompts: ["How does starting actions with 'Bismillah' change your awareness and intention?", "Reflect on the vastness of Allah's mercy (Ar-Rahman and Ar-Rahim)."],
      practicalApplication: "Make a conscious effort to say 'Bismillah' before starting tasks today, like eating, working, or even simple chores.",
      layerFocus: ['ruh', 'qalb'],
      themes: ['Beginning with Allah', 'Mercy', 'Intention'],
      isActive: true,
    },
    {
      surahNumber: 1,
      ayahNumber: 2,
      arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
      translationEn: 'All praise is due to Allah, Lord of the worlds -',
      tafsirEn: "This verse expresses gratitude and praise to Allah, acknowledging Him as the Sustainer and Cherisher of all that exists. 'Alamin' (worlds) includes all realms, beings, and times, highlighting His supreme authority and provision.",
      reflectionPrompts: ["What are three things you are truly grateful for today that you can attribute to Allah as 'Rabbil Alamin'?", "How does recognizing Allah as Lord of all worlds impact your perspective on your own problems?"],
      practicalApplication: "Spend a few minutes consciously saying 'Alhamdulillah' for specific blessings. Observe nature and reflect on Allah's creation, attributing it to Him as the Lord of the Worlds.",
      layerFocus: ['qalb', 'ruh', 'aql'],
      themes: ['Gratitude (Shukr)', 'Tawhid (Oneness)', 'Lordship'],
      isActive: true,
    },
    {
      surahNumber: 2, // Al-Baqarah
      ayahNumber: 286,
      arabicText: 'لَا يُكَلِّفُ اللَّهُ نَفْسًا إِلَّا وُسْعَهَا ۚ لَهَا مَا كَسَبَتْ وَعَلَيْهَا مَا اكْتَسَبَتْ ۗ...',
      translationEn: 'Allah does not charge a soul except [with that within] its capacity. It will have [the consequence of] what [good] it has gained, and it will bear [the consequence of] what [evil] it has earned...',
      tafsirEn: 'This verse provides immense comfort, stating that Allah never burdens a soul with more than it can bear. It emphasizes individual responsibility for one\'s actions, both good and bad. It concludes with a powerful dua for forgiveness, help, and victory.',
      reflectionPrompts: ["Recall a time you felt overwhelmed but managed to get through it. How might this verse apply?", "How does understanding personal accountability affect your daily choices?"],
      practicalApplication: "When facing a difficulty, remind yourself of this verse. Make the dua at the end of this verse, especially if you feel burdened.",
      layerFocus: ['nafs', 'qalb', 'aql'],
      themes: ['Ease', 'Responsibility', 'Dua', 'Trust in Allah'],
      isActive: true,
    },
  ];

  for (const verseData of verses) {
    await prisma.quranicVerseContent.upsert({
      where: { surahNumber_ayahNumber: { surahNumber: verseData.surahNumber, ayahNumber: verseData.ayahNumber } },
      update: {
        arabicText: verseData.arabicText,
        translationEn: verseData.translationEn,
        tafsirEn: verseData.tafsirEn,
        reflectionPrompts: verseData.reflectionPrompts,
        practicalApplication: verseData.practicalApplication,
        layerFocus: verseData.layerFocus as any, // Cast to correct type
        themes: verseData.themes,
        isActive: verseData.isActive,
      },
      create: {
        ...verseData,
        layerFocus: verseData.layerFocus as any, // Cast to correct type
      },
    });
    console.log(`Seeded Quranic Verse: Surah ${verseData.surahNumber}, Ayah ${verseData.ayahNumber}`);
  }
  console.log('Quranic Verse content seeding finished.');
}

async function seedNameOfAllahContent() {
  console.log('Seeding Names of Allah content...');

  const existingArRahman = await prisma.nameOfAllahContent.findUnique({
    where: { name: 'Ar-Rahman' },
  });

  if (existingArRahman) {
    console.log('Names of Allah content already seeded. Skipping.');
    return;
  }

  const namesOfAllah = [
    {
      name: 'Ar-Rahman',
      arabicScript: 'الرَّحْمَٰنُ',
      audioUrl: '/assets/audio/names_of_allah/ar_rahman.mp3', // Placeholder path
      meaning: 'The Entirely Merciful, The Most Beneficent',
      significance:
        "Allah's mercy that encompasses all of creation, believer and non-believer alike in this worldly life. Reflecting on Ar-Rahman instills hope and gratitude for the universal blessings bestowed by Allah.",
      reflectionPrompt:
        "Contemplate a moment today, no matter how small, where you felt or witnessed an act of mercy. How did it make you feel? How can you extend such mercy to others, reflecting this Divine attribute?",
      practicalApplication:
        "Practice active listening with someone today without judgment. Offer a smile or a kind word to a stranger. Forgive someone who may have wronged you, seeking to embody a fraction of Allah's vast mercy.",
      dhikrCount: 100,
      layerFocus: ['qalb', 'ruh'],
      benefits: ['Increases hope in Allah mercy', 'Cultivates gratitude', 'Softens the heart'],
      sourceReference: 'Quran 1:3, Quran 55:1',
      isActive: true,
    },
    {
      name: 'Ar-Rahim',
      arabicScript: 'الرَّحِيمُ',
      audioUrl: '/assets/audio/names_of_allah/ar_rahim.mp3',
      meaning: 'The Especially Merciful, The Bestower of Mercy',
      significance:
        "Allah's specific mercy reserved for the believers in this life and the Hereafter. Reflecting on Ar-Rahim strengthens faith and encourages righteous deeds to be deserving of this special mercy.",
      reflectionPrompt:
        "Think about a specific blessing in your life you feel is a special mercy from Allah. How does recognizing this as 'Ar-Rahim's' gift deepen your connection to Him?",
      practicalApplication:
        "Make a sincere du'a for a fellow believer today. Perform an act of kindness specifically hoping for Allah's special mercy (Rahmah) in the hereafter.",
      dhikrCount: 100,
      layerFocus: ['qalb', 'ruh'],
      benefits: ['Strengthens faith', 'Encourages good deeds', 'Provides comfort'],
      sourceReference: 'Quran 1:3, Quran 33:43',
      isActive: true,
    },
    {
      name: 'Al-Malik',
      arabicScript: 'الْمَلِكُ',
      audioUrl: '/assets/audio/names_of_allah/al_malik.mp3',
      meaning: 'The Absolute King, The Sovereign Lord',
      significance:
        "Allah's absolute dominion and ownership over all creation. Understanding Al-Malik helps in realizing our own powerlessness and complete dependence on Him, leading to humility.",
      reflectionPrompt:
        "In what areas of your life are you trying to exert control where true control only belongs to Al-Malik? How can surrendering this to Him bring you peace?",
      practicalApplication:
        "Delegate a task you usually control tightly, trusting in Allah's outcome. When faced with a situation beyond your control, remind yourself that Allah is Al-Malik and make du'a.",
      dhikrCount: 100,
      layerFocus: ['aql', 'nafs'],
      benefits: ['Instills humility', 'Reduces anxiety about control', 'Strengthens trust in Allah'],
      sourceReference: 'Quran 20:114, Quran 59:23',
      isActive: true,
    },
  ];

  for (const noaData of namesOfAllah) {
    await prisma.nameOfAllahContent.upsert({
      where: { name: noaData.name },
      update: {}, // No updates if it exists, just ensure it's there
      create: {
        name: noaData.name,
        arabicScript: noaData.arabicScript,
        audioUrl: noaData.audioUrl,
        meaning: noaData.meaning,
        significance: noaData.significance,
        reflectionPrompt: noaData.reflectionPrompt,
        practicalApplication: noaData.practicalApplication,
        dhikrCount: noaData.dhikrCount,
        layerFocus: noaData.layerFocus as any, // Prisma expects LayerFocus[]
        benefits: noaData.benefits,
        sourceReference: noaData.sourceReference,
        isActive: noaData.isActive,
      },
    });
    console.log(`Seeded Name of Allah: ${noaData.name}`);
  }
  console.log('Names of Allah content seeding finished.');
}


async function seedAssessmentQuestions() {
  console.log('Seeding assessment questions...');

  // Ensure questions are only seeded once to avoid duplicates if seed is run multiple times
  const existingQuestion = await prisma.assessmentQuestion.findFirst({
    where: { step: 'physical_experiences' },
  });

  if (existingQuestion) {
    console.log('Assessment questions already seeded. Skipping.');
    return;
  }

  const questionData = [
    // Physical Experiences
    {
      step: 'physical_experiences',
      category: 'jism',
      layer: 'jism', // Added layer field
      displayOrder: 1,
      title:
        "Let's start with what your body is telling you. Select any experiences you've had:",
      description:
        'This section helps us understand the physical sensations or challenges you might be facing. Your body often communicates when other layers of your being are affected.',
      questionType: 'symptom_multi_choice',
      required: false,
      isActive: true,
      allowMultipleSelection: true,
      intensityScale: true,
      customInputAllowed: false,
      reflectionPrompt:
        'How much do these physical experiences affect your daily life? (Optional)',
      reflectionRequired: false,
      choices: [
        {
          text: 'Sleep difficulties (trouble falling asleep, staying asleep, or waking up tired)',
          value: 'symptom_jism_sleep_difficulties',
          order: 1,
        },
        {
          text: 'Physical tension (muscle tightness, headaches, jaw clenching)',
          value: 'symptom_jism_physical_tension',
          order: 2,
        },
        {
          text: 'Heart and breathing (racing heart, shortness of breath, chest tightness)',
          value: 'symptom_jism_heart_breathing',
          order: 3,
        },
        {
          text: 'Energy levels (chronic fatigue, feeling drained, or restless energy)',
          value: 'symptom_jism_energy_levels',
          order: 4,
        },
        {
          text: 'Digestive issues (stomach problems, loss of appetite, or stress eating)',
          value: 'symptom_jism_digestive_issues',
          order: 5,
        },
        {
          text: "Physical restlessness (can't sit still, need to move constantly)",
          value: 'symptom_jism_physical_restlessness',
          order: 6,
        },
        {
          text: 'Unexplained aches and pains',
          value: 'symptom_jism_unexplained_aches',
          order: 7,
        },
      ],
    },
    // Emotional Experiences
    {
      step: 'emotional_experiences',
      category: 'nafs',
      layer: 'nafs', // Added layer field
      displayOrder: 1,
      title:
        "Now, let's explore your emotional landscape. Select any experiences you've had:",
      description:
        'Our emotions are a key part of our Nafs (ego/lower self). Understanding them is vital for spiritual purification.',
      questionType: 'symptom_multi_choice',
      required: false,
      isActive: true,
      allowMultipleSelection: true,
      intensityScale: false,
      customInputAllowed: false,
      reflectionPrompt:
        'Which emotions feel most overwhelming right now, and how do they impact you? (Optional)',
      reflectionRequired: false,
      choices: [
        {
          text: 'Overwhelming sadness or emptiness',
          value: 'symptom_nafs_overwhelming_sadness',
          order: 1,
        },
        {
          text: 'Frequent anger or irritability (especially over small things)',
          value: 'symptom_nafs_frequent_anger',
          order: 2,
        },
        {
          text: 'Anxiety or constant worry',
          value: 'symptom_nafs_anxiety_worry',
          order: 3,
        },
        {
          text: 'Shame or guilt about past actions',
          value: 'symptom_nafs_shame_guilt',
          order: 4,
        },
        {
          text: 'Jealousy or resentment toward others',
          value: 'symptom_nafs_jealousy_resentment',
          order: 5,
        },
        {
          text: 'Feeling emotionally numb or disconnected',
          value: 'symptom_nafs_emotional_numbness',
          order: 6,
        },
        {
          text: 'Mood swings or emotional unpredictability',
          value: 'symptom_nafs_mood_swings',
          order: 7,
        },
        {
          text: 'Fear of judgment from others',
          value: 'symptom_nafs_fear_judgment',
          order: 8,
        },
      ],
    },
    // Mental Experiences
    {
      step: 'mental_experiences',
      category: 'aql',
      layer: 'aql', // Added layer field
      displayOrder: 1,
      title:
        "Let's look at what's happening in your mind. Select any experiences you've had:",
      description:
        "The Aql (rational mind) is a gift for understanding. When it's troubled, our clarity and peace are affected.",
      questionType: 'symptom_multi_choice',
      required: false,
      isActive: true,
      allowMultipleSelection: true,
      intensityScale: false,
      customInputAllowed: false,
      reflectionPrompt:
        'What thoughts occupy your mind most during quiet moments, and how do they affect your peace? (Optional)',
      reflectionRequired: false,
      choices: [
        {
          text: "Racing thoughts that won't slow down",
          value: 'symptom_aql_racing_thoughts',
          order: 1,
        },
        {
          text: 'Constant worry about the future',
          value: 'symptom_aql_constant_worry_future',
          order: 2,
        },
        {
          text: 'Overthinking past decisions or conversations',
          value: 'symptom_aql_overthinking_past',
          order: 3,
        },
        {
          text: 'Difficulty concentrating (on work, studies, or prayers)',
          value: 'symptom_aql_difficulty_concentrating',
          order: 4,
        },
        {
          text: 'Negative thought patterns or self-criticism',
          value: 'symptom_aql_negative_thoughts',
          order: 5,
        },
        {
          text: 'Confusion about decisions or life direction',
          value: 'symptom_aql_confusion_decisions',
          order: 6,
        },
        {
          text: 'Intrusive or unwanted thoughts',
          value: 'symptom_aql_intrusive_thoughts',
          order: 7,
        },
        {
          text: 'Mental fog or feeling "cloudy"',
          value: 'symptom_aql_mental_fog',
          order: 8,
        },
      ],
    },
    // Spiritual Experiences
    {
      step: 'spiritual_experiences',
      category: 'qalb_ruh',
      layer: 'qalb_ruh', // Added layer field
      displayOrder: 1,
      title:
        "Finally, let's explore your spiritual and soul experiences. Select any you've had:",
      description:
        'The Qalb (spiritual heart) and Ruh (soul) are central to our connection with Allah. Challenges here often signify a deep yearning or a need for spiritual reconnection.',
      questionType: 'symptom_multi_choice',
      required: false,
      isActive: true,
      allowMultipleSelection: true,
      intensityScale: false,
      customInputAllowed: false,
      reflectionPrompt:
        'When did you last feel truly connected to Allah, and what is your current spiritual state like? (Optional)',
      reflectionRequired: false,
      choices: [
        {
          text: 'Feeling distant from Allah or spiritually disconnected',
          value: 'symptom_qalb_distant_from_allah',
          order: 1,
        },
        {
          text: 'Prayers feeling mechanical or empty',
          value: 'symptom_qalb_prayers_mechanical',
          order: 2,
        },
        {
          text: "Difficulty making sincere du'a or feeling heard",
          value: 'symptom_qalb_difficulty_dua',
          order: 3,
        },
        {
          text: "Questioning life's purpose or meaning",
          value: 'symptom_ruh_questioning_purpose',
          order: 4,
        },
        {
          text: 'Feeling like a stranger in this world',
          value: 'symptom_ruh_stranger_world',
          order: 5,
        },
        {
          text: "Struggling to trust Allah's qadar (decree)",
          value: 'symptom_qalb_struggling_trust_qadar',
          order: 6,
        },
        {
          text: 'Loss of spiritual motivation or joy',
          value: 'symptom_qalb_loss_spiritual_motivation',
          order: 7,
        },
        {
          text: 'Fear about death or the afterlife',
          value: 'symptom_ruh_fear_death_afterlife',
          order: 8,
        },
        {
          text: "Feeling unworthy of Allah's mercy",
          value: 'symptom_qalb_feeling_unworthy',
          order: 9,
        },
        {
          text: 'Yearning for something eternal or transcendent',
          value: 'symptom_ruh_yearning_eternal',
          order: 10,
        },
      ],
    },
    // Reflection Step (as a standalone question, as per backend service logic)
    {
      step: 'reflections',
      category: 'reflection', // General reflection category
      layer: 'reflection', // Added layer field
      displayOrder: 1,
      title:
        'Please take a moment to reflect on your overall experiences. Is there anything else you would like to share about what you are going through, or any specific concerns you have?',
      description:
        "This is a space for you to share any further thoughts or feelings that weren't covered by the specific questions. Your reflections are valuable.",
      questionType: 'reflection_text_long',
      required: false,
      isActive: true,
      allowMultipleSelection: false,
      intensityScale: false,
      customInputAllowed: true,
      reflectionPrompt: null, // The main text is the prompt
      reflectionRequired: false,
      choices: [], // No choices for a text input
    },
  ];

  for (const q of questionData) {
    console.log(
      `Attempting to create question: ${q.title.substring(0, 50)}...`
    );
    const { choices, required, ...questionDetails } = q; // Remove 'required' from destructured object

    const createdQuestion = await prisma.assessmentQuestion.create({
      data: {
        step: questionDetails.step,
        category: questionDetails.category,
        layer: questionDetails.layer, // Explicitly set layer
        displayOrder: questionDetails.displayOrder,
        title: questionDetails.title,
        description: questionDetails.description,
        questionType: questionDetails.questionType, // <-- add this line
        reflectionPrompt: questionDetails.reflectionPrompt, // Use from questionDetails
        reflectionRequired: questionDetails.reflectionRequired,
        isActive: questionDetails.isActive,
        allowMultipleSelection: questionDetails.allowMultipleSelection,
        intensityScale: questionDetails.intensityScale,
        customInputAllowed: questionDetails.customInputAllowed,
        assessment_symptoms: {
          create: choices.map((choice) => ({
            text: choice.text,
            severity: 'mild', // Default value, adjust if needed
            primaryLayer: questionDetails.category, // Assuming category maps to primary_layer
            displayOrder: choice.order,
          })),
        },
      },
    });
    console.log(
      `Created question: ${createdQuestion.title.substring(0, 50)}...`
    );

    // If there's a reflection prompt for this step, create it as a separate question
    // This assumes the mobile app will query for questions by step and handle multiple questions per step.
    if (questionDetails.reflectionPrompt && q.step !== 'reflections') {
      // Use questionDetails.reflectionPrompt
      console.log(
        `Attempting to create reflection question for step: ${
          q.step
        } - ${questionDetails.reflectionPrompt.substring(0, 30)}...`
      );
      await prisma.assessmentQuestion.create({
        data: {
          step: q.step, // Same step, so it appears on the same "page" or section
          category: q.category, // Same category
          layer: q.category, // Explicitly set layer for reflection question
          displayOrder: questionDetails.displayOrder + 1, // Ensure it appears after the main symptom question for that step
          title: questionDetails.reflectionPrompt, // Use questionDetails.reflectionPrompt
          questionType: 'reflection_text_short', // <-- add missing property
          isActive: true,
          allowMultipleSelection: false,
          intensityScale: false,
          customInputAllowed: true,
          reflectionPrompt: null,
          reflectionRequired: false,
        },
      });
      console.log(
        `Created reflection question for step: ${
          q.step
        } - ${questionDetails.reflectionPrompt.substring(0, 30)}...`
      );
    }
  }
  console.log('Assessment questions seeded.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
