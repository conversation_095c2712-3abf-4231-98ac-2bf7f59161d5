CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- CreateEnum
CREATE TYPE "JourneyType" AS ENUM ('tranquil_mind', 'heart_purification', 'ego_purification', 'spiritual_optimization', 'crisis_recovery', 'maintenance_program');

-- CreateEnum
CREATE TYPE "JourneyStatus" AS ENUM ('created', 'active', 'paused', 'completed', 'abandoned', 'evolved');

-- CreateEnum
CREATE TYPE "PracticeType" AS ENUM ('dhikr', 'prayer', 'reflection', 'study', 'community', 'ruqya', 'mindfulness', 'gratitude', 'MorningCheckIn', 'NameOfAllahSpotlight', 'QuranicVerseReflection', 'PersonalReflectionJournaling', 'SunnahPractice');

-- CreateEnum
CREATE TYPE "LayerFocus" AS ENUM ('jism', 'nafs', 'aql', 'qalb', 'ruh');

-- CreateEnum
CREATE TYPE "CrisisLevelType" AS ENUM ('none', 'low', 'moderate', 'high', 'critical');

-- CreateEnum
CREATE TYPE "SunnahPracticeCategory" AS ENUM ('Physical', 'Spiritual', 'Social', 'Mental', 'Emotional');

-- CreateTable
CREATE TABLE "profiles" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT,
    "full_name" TEXT,
    "avatar_url" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_profiles" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "profile_data" JSONB NOT NULL,
    "completion_status" TEXT NOT NULL DEFAULT 'incomplete',
    "profile_version" TEXT NOT NULL DEFAULT '1.0.0',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "user_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "onboarding_sessions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "session_id" TEXT NOT NULL,
    "user_id" UUID NOT NULL,
    "started_at" TIMESTAMP(6) NOT NULL,
    "completed_at" TIMESTAMP(6),
    "current_step" TEXT NOT NULL,
    "steps" JSONB NOT NULL DEFAULT '[]',
    "total_time_spent" INTEGER NOT NULL DEFAULT 0,
    "device_info" JSONB,
    "abandoned_at" TIMESTAMP(6),
    "abandonment_reason" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "onboarding_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "crisis_events" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "session_id" TEXT,
    "crisis_level" TEXT NOT NULL,
    "indicators" TEXT[],
    "confidence" DECIMAL(3,2) NOT NULL,
    "urgency" TEXT NOT NULL,
    "context" JSONB,
    "response_actions" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "intervention_triggered" BOOLEAN NOT NULL DEFAULT false,
    "resolved_at" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "crisis_events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "profile_updates" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "profile_id" UUID NOT NULL,
    "updates" JSONB NOT NULL,
    "reason" TEXT,
    "confidence" DECIMAL(3,2),
    "applied_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "applied_by" UUID,

    CONSTRAINT "profile_updates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "onboarding_analytics" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "date" DATE NOT NULL,
    "total_sessions" INTEGER NOT NULL DEFAULT 0,
    "completed_sessions" INTEGER NOT NULL DEFAULT 0,
    "abandoned_sessions" INTEGER NOT NULL DEFAULT 0,
    "crisis_detections" INTEGER NOT NULL DEFAULT 0,
    "average_completion_time" INTEGER,
    "step_dropoff_data" JSONB,
    "pathway_distribution" JSONB,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "onboarding_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assessment_sessions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "user_profile" JSONB NOT NULL,
    "started_at" TIMESTAMP(6) NOT NULL,
    "completed_at" TIMESTAMP(6),
    "abandoned_at" TIMESTAMP(6),
    "abandonment_reason" TEXT,
    "current_step" TEXT NOT NULL DEFAULT 'welcome',
    "total_steps" INTEGER NOT NULL DEFAULT 10,
    "physical_experiences" JSONB NOT NULL DEFAULT '{}',
    "emotional_experiences" JSONB NOT NULL DEFAULT '{}',
    "mental_experiences" JSONB NOT NULL DEFAULT '{}',
    "spiritual_experiences" JSONB NOT NULL DEFAULT '{}',
    "reflections" JSONB NOT NULL DEFAULT '{}',
    "time_spent_per_step" JSONB NOT NULL DEFAULT '{}',
    "total_time_spent" INTEGER NOT NULL DEFAULT 0,
    "session_data" JSONB,
    "device_info" JSONB,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "assessment_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "spiritual_diagnoses" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "assessment_id" UUID NOT NULL,
    "diagnosis_data" JSONB NOT NULL,
    "primary_layer" TEXT NOT NULL,
    "secondary_layers" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "overall_severity" TEXT NOT NULL,
    "crisis_level" TEXT NOT NULL DEFAULT 'none',
    "confidence" DECIMAL(3,2) NOT NULL DEFAULT 0.8,
    "recommended_journey_type" TEXT,
    "estimated_healing_duration" INTEGER,
    "next_steps" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "user_feedback" JSONB,
    "generated_at" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "spiritual_diagnoses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "layer_analyses" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "diagnosis_id" UUID NOT NULL,
    "layer" TEXT NOT NULL,
    "layer_name" TEXT NOT NULL,
    "impact_score" INTEGER NOT NULL DEFAULT 0,
    "priority" TEXT NOT NULL,
    "affected_symptoms" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "insights" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "recommendations" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "islamic_context" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "layer_analyses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assessment_analytics" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "date" DATE NOT NULL,
    "total_sessions" INTEGER NOT NULL DEFAULT 0,
    "completed_sessions" INTEGER NOT NULL DEFAULT 0,
    "abandoned_sessions" INTEGER NOT NULL DEFAULT 0,
    "total_diagnoses" INTEGER NOT NULL DEFAULT 0,
    "crisis_detections" INTEGER NOT NULL DEFAULT 0,
    "primary_layer_distribution" JSONB,
    "severity_distribution" JSONB,
    "average_completion_time" INTEGER,
    "average_accuracy_rating" DECIMAL(3,2),
    "average_helpfulness_rating" DECIMAL(3,2),
    "step_completion_rates" JSONB,
    "step_average_times" JSONB,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "assessment_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_items" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "content_type" TEXT NOT NULL,
    "healing_layer" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "storage_path" TEXT NOT NULL,
    "duration" INTEGER,
    "access_level" TEXT NOT NULL DEFAULT 'basic',
    "requires_subscription" BOOLEAN NOT NULL DEFAULT false,
    "series_id" UUID,
    "part_number" INTEGER,
    "focus_areas" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "view_count" INTEGER NOT NULL DEFAULT 0,
    "completion_count" INTEGER NOT NULL DEFAULT 0,
    "average_rating" DECIMAL(3,2),
    "status" TEXT NOT NULL DEFAULT 'draft',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "content_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_metadata" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "content_id" UUID NOT NULL,
    "language" TEXT NOT NULL DEFAULT 'en',
    "author" TEXT,
    "source" TEXT,
    "references" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "keywords" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "difficulty_level" TEXT,
    "estimated_duration" INTEGER,
    "prerequisites" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_metadata_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_tags" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "content_id" UUID NOT NULL,
    "tag_name" TEXT NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_series" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "total_parts" INTEGER NOT NULL,
    "category" TEXT NOT NULL,
    "healing_layer" TEXT NOT NULL,
    "difficulty_level" TEXT NOT NULL,
    "prerequisites" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "status" TEXT NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_series_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_interactions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "content_id" UUID NOT NULL,
    "interaction_type" TEXT NOT NULL,
    "duration" INTEGER,
    "progress" INTEGER,
    "rating" INTEGER,
    "interaction_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_interactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_progress" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "content_id" UUID NOT NULL,
    "progress_percentage" INTEGER NOT NULL,
    "last_accessed" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_progress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "series_progress" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "series_id" UUID NOT NULL,
    "completed_parts" INTEGER NOT NULL DEFAULT 0,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "last_updated" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "series_progress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_completions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "content_id" UUID NOT NULL,
    "completion_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_completions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "series_completions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "series_id" UUID NOT NULL,
    "completion_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "series_completions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_issues" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "content_id" UUID NOT NULL,
    "issue_type" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "resolution" TEXT,
    "report_date" TIMESTAMP(6) NOT NULL,
    "resolved_date" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_issues_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "health_assessments" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "metrics" JSONB NOT NULL,
    "assessment_date" TIMESTAMP(6) NOT NULL,
    "notes" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "health_assessments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_interactions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "interaction_type" TEXT NOT NULL,
    "layer" TEXT,
    "engagement_score" INTEGER,
    "duration_seconds" INTEGER,
    "interaction_date" TIMESTAMP(6) NOT NULL,
    "metadata" JSONB,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_interactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "daily_practices" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "practice_type" TEXT NOT NULL,
    "layer" TEXT NOT NULL,
    "practice_date" DATE NOT NULL,
    "completion_status" TEXT NOT NULL,
    "duration_minutes" INTEGER,
    "effectiveness_rating" INTEGER,
    "notes" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "daily_practices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "journey_milestones" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "journey_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "milestone_type" TEXT NOT NULL,
    "sequence_order" INTEGER NOT NULL,
    "requirements" JSONB NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "journey_milestones_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "milestone_completions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "milestone_id" UUID NOT NULL,
    "completion_date" TIMESTAMP(6) NOT NULL,
    "reflection" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "milestone_completions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "analytics_metrics" (
    "time" TIMESTAMP(6) NOT NULL,
    "user_id" UUID NOT NULL,
    "metric_name" TEXT NOT NULL,
    "metric_value" DECIMAL NOT NULL,
    "metadata" JSONB,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "analytics_metrics_pkey" PRIMARY KEY ("time","user_id","metric_name")
);

-- CreateTable
CREATE TABLE "analytics_webhooks" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "url" TEXT NOT NULL,
    "events" TEXT[],
    "status" TEXT NOT NULL DEFAULT 'active',
    "last_triggered" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "analytics_webhooks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "analytics_exports" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "export_type" TEXT NOT NULL,
    "timeframe" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "file_url" TEXT,
    "export_date" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "analytics_exports_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "emergency_sessions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "trigger_type" TEXT NOT NULL DEFAULT 'manual',
    "current_step" TEXT,
    "current_symptoms" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "start_time" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_time" TIMESTAMP(6),
    "status" TEXT NOT NULL DEFAULT 'active',
    "escalationReason" TEXT,
    "recommended_actions" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "estimated_duration" INTEGER DEFAULT 15,
    "effectiveness_rating" INTEGER,
    "feedback" TEXT,
    "log" JSONB,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "emergency_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peer_support_requests" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "session_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "supporter_id" UUID,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "peer_support_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dua_request_logs" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "session_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "requested_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "dua_request_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "crisis_escalations" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "escalation_id" TEXT NOT NULL,
    "user_id" UUID NOT NULL,
    "severity" TEXT NOT NULL,
    "indicators" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "context" JSONB NOT NULL DEFAULT '{}',
    "immediate_risk" BOOLEAN NOT NULL DEFAULT false,
    "status" TEXT NOT NULL DEFAULT 'active',
    "actions_taken" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "crisis_escalations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "crisis_followups" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "escalation_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "status" TEXT NOT NULL,
    "notes" TEXT,
    "additional_support" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "crisis_followups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "breathing_exercises" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT NOT NULL,
    "instructions" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "duration" INTEGER NOT NULL,
    "intensity" TEXT NOT NULL DEFAULT 'medium',
    "audio_url" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "breathing_exercises_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dhikr_content" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "arabic" TEXT NOT NULL,
    "transliteration" TEXT NOT NULL,
    "translation" TEXT NOT NULL,
    "count" INTEGER DEFAULT 1,
    "category" TEXT NOT NULL DEFAULT 'general',
    "priority" INTEGER DEFAULT 0,
    "audio_url" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "dhikr_content_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqyah_verses" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "surah" TEXT NOT NULL,
    "ayah" INTEGER NOT NULL,
    "arabic" TEXT NOT NULL,
    "translation" TEXT NOT NULL,
    "category" TEXT NOT NULL DEFAULT 'general',
    "sequence_order" INTEGER DEFAULT 0,
    "audio_url" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqyah_verses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "emergency_helplines" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "description" TEXT,
    "availability" TEXT DEFAULT '24/7',
    "country" TEXT NOT NULL DEFAULT 'US',
    "is_islamic" BOOLEAN DEFAULT false,
    "priority" INTEGER DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "emergency_helplines_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "journal_entries" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "entry_type" TEXT NOT NULL DEFAULT 'general',
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "related_data" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "journal_entries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "journeys" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "assessment_id" UUID NOT NULL,
    "type" "JourneyType" NOT NULL,
    "status" "JourneyStatus" NOT NULL DEFAULT 'created',
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "personalized_welcome" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "daily_time_commitment" INTEGER NOT NULL,
    "primary_layer" "LayerFocus" NOT NULL,
    "secondary_layers" "LayerFocus"[] DEFAULT ARRAY[]::"LayerFocus"[],
    "ruqya_integration_level" TEXT NOT NULL DEFAULT 'none',
    "community_integration" BOOLEAN NOT NULL DEFAULT false,
    "professional_context" TEXT,
    "cultural_adaptations" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "crisis_support" BOOLEAN NOT NULL DEFAULT false,
    "current_day" INTEGER DEFAULT 1,
    "completed_days" INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    "total_progress" DECIMAL(5,2) DEFAULT 0.00,
    "user_profile" JSONB NOT NULL,
    "community_group" UUID,
    "mentor_id" UUID,
    "peer_connections" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "ai_recommendations" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "adaptive_adjustments" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "crisis_flags" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "started_at" TIMESTAMP(6),
    "completed_at" TIMESTAMP(6),
    "last_active_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "journeys_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "journey_days" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "journey_id" UUID NOT NULL,
    "day_number" INTEGER NOT NULL,
    "theme" TEXT NOT NULL,
    "learning_objective" TEXT NOT NULL,
    "reflection_prompts" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "community_activity" TEXT,
    "progress_milestone" TEXT,
    "adaptive_content" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "journey_days_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "daily_practices_journey" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "journey_day_id" UUID NOT NULL,
    "type" "PracticeType" NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "instructions" TEXT NOT NULL,
    "arabic_text" TEXT,
    "transliteration" TEXT,
    "translation" TEXT,
    "benefits" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "layer_focus" "LayerFocus" NOT NULL,
    "difficulty_level" TEXT NOT NULL DEFAULT 'beginner',
    "ruqya_component" BOOLEAN NOT NULL DEFAULT false,
    "professional_context" TEXT,
    "cultural_notes" TEXT,
    "order_index" INTEGER NOT NULL DEFAULT 0,
    "component_data" JSONB,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "nameOfAllahContentId" UUID,
    "quranicVerseContentId" UUID,
    "sunnahPracticeContentId" UUID,

    CONSTRAINT "daily_practices_journey_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "journey_progress" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "journey_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "day_number" INTEGER NOT NULL,
    "date" DATE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "practices_completed" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "overall_rating" INTEGER,
    "mood_before" INTEGER,
    "mood_after" INTEGER,
    "energy_level_before" INTEGER,
    "spiritual_state_before" TEXT,
    "daily_intention" TEXT,
    "spiritual_connection" INTEGER,
    "stress_level" INTEGER,
    "daily_reflection" TEXT,
    "gratitude" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "challenges" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "insights" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "community_participation" BOOLEAN NOT NULL DEFAULT false,
    "community_contribution" TEXT,
    "peer_support" BOOLEAN NOT NULL DEFAULT false,
    "content_relevance" INTEGER,
    "practice_effectiveness" INTEGER,
    "time_appropriate" BOOLEAN,
    "suggested_adjustments" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "journey_progress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "journey_analytics" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "journey_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "completion_rate" DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    "average_daily_rating" DECIMAL(3,2),
    "practice_adherence" DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    "community_engagement" DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    "symptom_improvement" JSONB NOT NULL DEFAULT '{}',
    "spiritual_development" JSONB NOT NULL DEFAULT '{}',
    "personalization_success" JSONB NOT NULL DEFAULT '{}',
    "next_step_recommendations" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "graduation_readiness" BOOLEAN NOT NULL DEFAULT false,
    "generated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "journey_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "community_groups" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "group_type" TEXT NOT NULL,
    "matching_criteria" JSONB NOT NULL DEFAULT '{}',
    "max_members" INTEGER DEFAULT 20,
    "current_members" INTEGER DEFAULT 0,
    "mentor_id" UUID,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "community_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "community_memberships" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "group_id" UUID NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'member',
    "joined_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "community_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_journeys" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "journey_type" TEXT NOT NULL,
    "focus_layers" TEXT[],
    "duration_days" INTEGER NOT NULL,
    "start_date" TIMESTAMP(6) NOT NULL,
    "end_date" TIMESTAMP(6),
    "status" TEXT NOT NULL DEFAULT 'active',
    "current_day" INTEGER NOT NULL DEFAULT 1,
    "modules_plan" JSONB NOT NULL DEFAULT '[]',
    "last_activity_date" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_journeys_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "journey_modules" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "journey_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "day_number" INTEGER NOT NULL,
    "content" JSONB NOT NULL,
    "focus_layer" TEXT NOT NULL,
    "estimated_duration" INTEGER,
    "sequence_order" INTEGER NOT NULL,
    "prerequisites" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "is_advanced" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "journey_modules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "module_completions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "module_id" UUID NOT NULL,
    "status" TEXT NOT NULL,
    "reflections" TEXT,
    "challenges" JSONB,
    "completion_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "module_completions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "daily_check_ins" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "mood" TEXT NOT NULL,
    "dhikr_count" INTEGER DEFAULT 0,
    "prayer_consistency" INTEGER,
    "notes" TEXT,
    "check_in_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "daily_check_ins_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_streaks" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "current_streak" INTEGER NOT NULL DEFAULT 0,
    "longest_streak" INTEGER NOT NULL DEFAULT 0,
    "last_check_in" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_streaks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "achievements" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "requirement_type" TEXT NOT NULL,
    "requirement_value" INTEGER NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "achievements_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_achievements" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "journey_id" UUID NOT NULL,
    "achievement_type" TEXT NOT NULL,
    "earned_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_achievements_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_assessment_questions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "question_text" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "sequence_order" INTEGER NOT NULL,
    "response_type" TEXT NOT NULL,
    "options" JSONB,
    "status" TEXT DEFAULT 'active',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_assessment_questions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_assessments" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "responses" JSONB NOT NULL,
    "additional_notes" TEXT,
    "emergency_contact" JSONB,
    "assessment_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_assessments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_analysis" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "assessment_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "risk_level" TEXT NOT NULL,
    "recommended_practices" TEXT[],
    "healing_focus" TEXT[],
    "spiritual_insights" JSONB NOT NULL,
    "analysis_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_analysis_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_treatment_plans" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "risk_level" TEXT NOT NULL,
    "healing_focus" TEXT[],
    "duration_weeks" INTEGER NOT NULL,
    "start_date" TIMESTAMP(6) NOT NULL,
    "end_date" TIMESTAMP(6),
    "status" TEXT NOT NULL DEFAULT 'active',
    "last_modified" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_treatment_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_practices" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "plan_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "instructions" TEXT NOT NULL,
    "duration_minutes" INTEGER,
    "frequency" TEXT NOT NULL,
    "prerequisites" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "contraindications" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "is_milestone" BOOLEAN NOT NULL DEFAULT false,
    "sequence_order" INTEGER NOT NULL,
    "arabic_content" JSONB,
    "audio_url" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_practices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_practice_progress" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "practice_id" UUID NOT NULL,
    "completed" BOOLEAN NOT NULL DEFAULT false,
    "effectiveness" INTEGER,
    "notes" TEXT,
    "completion_date" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_practice_progress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_duas" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "title" TEXT NOT NULL,
    "arabic_text" TEXT NOT NULL,
    "translation" TEXT NOT NULL,
    "transliteration" TEXT,
    "category" TEXT NOT NULL,
    "benefits" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "source" TEXT,
    "audio_url" TEXT,
    "status" TEXT DEFAULT 'active',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_duas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_favorite_duas" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "dua_id" UUID NOT NULL,
    "category" TEXT NOT NULL,
    "notes" TEXT,
    "saved_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_favorite_duas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_resources" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "content_type" TEXT NOT NULL,
    "content_url" TEXT NOT NULL,
    "prerequisites" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "status" TEXT DEFAULT 'active',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_resources_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_concerns" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "concern_type" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "severity" INTEGER NOT NULL,
    "is_emergency" BOOLEAN NOT NULL DEFAULT false,
    "emergency_assessment" JSONB,
    "needs_immediate_attention" BOOLEAN NOT NULL DEFAULT false,
    "report_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_concerns_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_consultations" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "preferred_date" TIMESTAMP(6) NOT NULL,
    "consultation_type" TEXT NOT NULL,
    "primary_concern" TEXT NOT NULL,
    "previous_treatment" BOOLEAN,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "practitioner_id" UUID,
    "scheduled_date" TIMESTAMP(6),
    "notes" TEXT,
    "request_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_consultations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_safety_guidelines" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "instructions" TEXT[],
    "priority" INTEGER NOT NULL,
    "status" TEXT DEFAULT 'active',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_safety_guidelines_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_user_precautions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "plan_id" UUID NOT NULL,
    "precautions" JSONB NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_user_precautions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_daily_protection" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "practice_type" TEXT NOT NULL,
    "completed" BOOLEAN NOT NULL,
    "completion_time" TIMESTAMP(6) NOT NULL,
    "submission_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_daily_protection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_protection_streaks" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "current_streak" INTEGER NOT NULL DEFAULT 0,
    "longest_streak" INTEGER NOT NULL DEFAULT 0,
    "total_completions" INTEGER NOT NULL DEFAULT 0,
    "last_completion" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_protection_streaks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ruqya_effectiveness_assessments" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "plan_id" UUID NOT NULL,
    "metrics" JSONB NOT NULL,
    "assessment_date" TIMESTAMP(6) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ruqya_effectiveness_assessments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "symptom_submissions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "jism_symptoms" JSONB NOT NULL DEFAULT '[]',
    "nafs_symptoms" JSONB NOT NULL DEFAULT '[]',
    "aql_symptoms" JSONB NOT NULL DEFAULT '[]',
    "qalb_symptoms" JSONB NOT NULL DEFAULT '[]',
    "ruh_symptoms" JSONB NOT NULL DEFAULT '[]',
    "intensity_ratings" JSONB NOT NULL,
    "duration" TEXT NOT NULL,
    "submission_date" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "symptom_submissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_diagnoses" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "submission_id" UUID NOT NULL,
    "layers_affected" TEXT[],
    "spotlight" TEXT NOT NULL,
    "recommended_journey" TEXT NOT NULL,
    "severity_level" TEXT NOT NULL,
    "diagnosis_date" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_diagnoses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "symptom_tracking" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "user_id" UUID NOT NULL,
    "symptom_id" UUID NOT NULL,
    "intensity" INTEGER NOT NULL,
    "notes" TEXT,
    "tracking_date" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "symptom_tracking_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assessment_questions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "category" TEXT NOT NULL,
    "layer" TEXT NOT NULL,
    "step" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "questionType" TEXT NOT NULL,
    "reflection_prompt" TEXT,
    "reflection_required" BOOLEAN NOT NULL DEFAULT false,
    "allow_multiple_selection" BOOLEAN NOT NULL DEFAULT true,
    "intensity_scale" BOOLEAN NOT NULL DEFAULT true,
    "custom_input_allowed" BOOLEAN NOT NULL DEFAULT true,
    "show_conditions" JSONB,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "assessment_questions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assessment_symptoms" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "question_id" UUID,
    "text" TEXT NOT NULL,
    "description" TEXT,
    "severity" TEXT NOT NULL,
    "primary_layer" TEXT NOT NULL,
    "secondary_layers" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "clinical_terms" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "dsm_mapping" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "spiritual_ailment_indicators" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "ruqya_relevance" TEXT NOT NULL DEFAULT 'none',
    "islamic_context" TEXT,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "assessment_symptoms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "name_of_allah_content" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT NOT NULL,
    "arabic_script" TEXT,
    "audio_url" TEXT,
    "meaning" TEXT NOT NULL,
    "significance" TEXT NOT NULL,
    "reflection_prompt" TEXT,
    "practical_application" TEXT,
    "dhikr_count" INTEGER,
    "layer_focus" "LayerFocus"[] DEFAULT ARRAY[]::"LayerFocus"[],
    "benefits" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "source_reference" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "name_of_allah_content_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "quranic_verse_content" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "surah_number" INTEGER NOT NULL,
    "ayah_number" INTEGER NOT NULL,
    "arabic_text" TEXT NOT NULL,
    "audio_url" TEXT,
    "reciter_options" JSONB,
    "translation_en" TEXT,
    "translation_ur" TEXT,
    "tafsir_source" TEXT,
    "tafsir_en" TEXT,
    "contextual_explanation" TEXT,
    "reflection_prompts" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "practical_application" TEXT,
    "layer_focus" "LayerFocus"[] DEFAULT ARRAY[]::"LayerFocus"[],
    "themes" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "related_hadith" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "quranic_verse_content_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sunnah_practice_content" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "title" TEXT NOT NULL,
    "category" "SunnahPracticeCategory" NOT NULL,
    "description" TEXT NOT NULL,
    "steps_json" JSONB,
    "duas_json" JSONB,
    "intention" TEXT,
    "reflection" TEXT,
    "benefits" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "estimated_duration_minutes" INTEGER,
    "difficulty_level" TEXT,
    "source_reference" TEXT,
    "layer_focus" "LayerFocus"[] DEFAULT ARRAY[]::"LayerFocus"[],
    "related_content_ids" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "sunnah_practice_content_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_ContentJourneyModules" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_ContentJourneyModules_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "profiles_email_key" ON "profiles"("email");

-- CreateIndex
CREATE UNIQUE INDEX "user_profiles_user_id_key" ON "user_profiles"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "onboarding_sessions_session_id_key" ON "onboarding_sessions"("session_id");

-- CreateIndex
CREATE UNIQUE INDEX "onboarding_analytics_date_key" ON "onboarding_analytics"("date");

-- CreateIndex
CREATE UNIQUE INDEX "assessment_analytics_date_key" ON "assessment_analytics"("date");

-- CreateIndex
CREATE UNIQUE INDEX "content_tags_content_id_tag_name_key" ON "content_tags"("content_id", "tag_name");

-- CreateIndex
CREATE UNIQUE INDEX "content_progress_user_id_content_id_key" ON "content_progress"("user_id", "content_id");

-- CreateIndex
CREATE UNIQUE INDEX "series_progress_user_id_series_id_key" ON "series_progress"("user_id", "series_id");

-- CreateIndex
CREATE UNIQUE INDEX "content_completions_user_id_content_id_key" ON "content_completions"("user_id", "content_id");

-- CreateIndex
CREATE UNIQUE INDEX "series_completions_user_id_series_id_key" ON "series_completions"("user_id", "series_id");

-- CreateIndex
CREATE UNIQUE INDEX "milestone_completions_user_id_milestone_id_key" ON "milestone_completions"("user_id", "milestone_id");

-- CreateIndex
CREATE UNIQUE INDEX "crisis_escalations_escalation_id_key" ON "crisis_escalations"("escalation_id");

-- CreateIndex
CREATE UNIQUE INDEX "journey_days_journey_id_day_number_key" ON "journey_days"("journey_id", "day_number");

-- CreateIndex
CREATE UNIQUE INDEX "journey_progress_journey_id_user_id_day_number_key" ON "journey_progress"("journey_id", "user_id", "day_number");

-- CreateIndex
CREATE UNIQUE INDEX "journey_analytics_journey_id_user_id_key" ON "journey_analytics"("journey_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "community_memberships_user_id_group_id_key" ON "community_memberships"("user_id", "group_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_streaks_user_id_key" ON "user_streaks"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "achievements_name_key" ON "achievements"("name");

-- CreateIndex
CREATE UNIQUE INDEX "user_achievements_user_id_achievement_type_key" ON "user_achievements"("user_id", "achievement_type");

-- CreateIndex
CREATE UNIQUE INDEX "user_favorite_duas_user_id_dua_id_key" ON "user_favorite_duas"("user_id", "dua_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_protection_streaks_user_id_key" ON "user_protection_streaks"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "name_of_allah_content_name_key" ON "name_of_allah_content"("name");

-- CreateIndex
CREATE UNIQUE INDEX "quranic_verse_content_surah_number_ayah_number_key" ON "quranic_verse_content"("surah_number", "ayah_number");

-- CreateIndex
CREATE UNIQUE INDEX "sunnah_practice_content_title_key" ON "sunnah_practice_content"("title");

-- CreateIndex
CREATE INDEX "_ContentJourneyModules_B_index" ON "_ContentJourneyModules"("B");

-- AddForeignKey
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "onboarding_sessions" ADD CONSTRAINT "onboarding_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crisis_events" ADD CONSTRAINT "crisis_events_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "onboarding_sessions"("session_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crisis_events" ADD CONSTRAINT "crisis_events_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profile_updates" ADD CONSTRAINT "profile_updates_applied_by_fkey" FOREIGN KEY ("applied_by") REFERENCES "profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profile_updates" ADD CONSTRAINT "profile_updates_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessment_sessions" ADD CONSTRAINT "assessment_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spiritual_diagnoses" ADD CONSTRAINT "spiritual_diagnoses_assessment_id_fkey" FOREIGN KEY ("assessment_id") REFERENCES "assessment_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spiritual_diagnoses" ADD CONSTRAINT "spiritual_diagnoses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "layer_analyses" ADD CONSTRAINT "layer_analyses_diagnosis_id_fkey" FOREIGN KEY ("diagnosis_id") REFERENCES "spiritual_diagnoses"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_items" ADD CONSTRAINT "content_items_series_id_fkey" FOREIGN KEY ("series_id") REFERENCES "content_series"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_metadata" ADD CONSTRAINT "content_metadata_content_id_fkey" FOREIGN KEY ("content_id") REFERENCES "content_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_tags" ADD CONSTRAINT "content_tags_content_id_fkey" FOREIGN KEY ("content_id") REFERENCES "content_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_interactions" ADD CONSTRAINT "content_interactions_content_id_fkey" FOREIGN KEY ("content_id") REFERENCES "content_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_interactions" ADD CONSTRAINT "content_interactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_progress" ADD CONSTRAINT "content_progress_content_id_fkey" FOREIGN KEY ("content_id") REFERENCES "content_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_progress" ADD CONSTRAINT "content_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "series_progress" ADD CONSTRAINT "series_progress_series_id_fkey" FOREIGN KEY ("series_id") REFERENCES "content_series"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "series_progress" ADD CONSTRAINT "series_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_completions" ADD CONSTRAINT "content_completions_content_id_fkey" FOREIGN KEY ("content_id") REFERENCES "content_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_completions" ADD CONSTRAINT "content_completions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "series_completions" ADD CONSTRAINT "series_completions_series_id_fkey" FOREIGN KEY ("series_id") REFERENCES "content_series"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "series_completions" ADD CONSTRAINT "series_completions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_issues" ADD CONSTRAINT "content_issues_content_id_fkey" FOREIGN KEY ("content_id") REFERENCES "content_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_issues" ADD CONSTRAINT "content_issues_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health_assessments" ADD CONSTRAINT "health_assessments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_interactions" ADD CONSTRAINT "user_interactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_practices" ADD CONSTRAINT "daily_practices_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_milestones" ADD CONSTRAINT "journey_milestones_journey_id_fkey" FOREIGN KEY ("journey_id") REFERENCES "user_journeys"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_milestones" ADD CONSTRAINT "journey_milestones_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "milestone_completions" ADD CONSTRAINT "milestone_completions_milestone_id_fkey" FOREIGN KEY ("milestone_id") REFERENCES "journey_milestones"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "milestone_completions" ADD CONSTRAINT "milestone_completions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "analytics_metrics" ADD CONSTRAINT "analytics_metrics_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "analytics_webhooks" ADD CONSTRAINT "analytics_webhooks_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "analytics_exports" ADD CONSTRAINT "analytics_exports_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "emergency_sessions" ADD CONSTRAINT "emergency_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peer_support_requests" ADD CONSTRAINT "peer_support_requests_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "emergency_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peer_support_requests" ADD CONSTRAINT "peer_support_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peer_support_requests" ADD CONSTRAINT "peer_support_requests_supporter_id_fkey" FOREIGN KEY ("supporter_id") REFERENCES "profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dua_request_logs" ADD CONSTRAINT "dua_request_logs_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "emergency_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dua_request_logs" ADD CONSTRAINT "dua_request_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crisis_escalations" ADD CONSTRAINT "crisis_escalations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crisis_followups" ADD CONSTRAINT "crisis_followups_escalation_id_fkey" FOREIGN KEY ("escalation_id") REFERENCES "crisis_escalations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crisis_followups" ADD CONSTRAINT "crisis_followups_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journal_entries" ADD CONSTRAINT "journal_entries_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journeys" ADD CONSTRAINT "journeys_assessment_id_fkey" FOREIGN KEY ("assessment_id") REFERENCES "assessment_sessions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journeys" ADD CONSTRAINT "journeys_community_group_fkey" FOREIGN KEY ("community_group") REFERENCES "community_groups"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journeys" ADD CONSTRAINT "journeys_mentor_id_fkey" FOREIGN KEY ("mentor_id") REFERENCES "profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journeys" ADD CONSTRAINT "journeys_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_days" ADD CONSTRAINT "journey_days_journey_id_fkey" FOREIGN KEY ("journey_id") REFERENCES "journeys"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_practices_journey" ADD CONSTRAINT "daily_practices_journey_journey_day_id_fkey" FOREIGN KEY ("journey_day_id") REFERENCES "journey_days"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_practices_journey" ADD CONSTRAINT "daily_practices_journey_nameOfAllahContentId_fkey" FOREIGN KEY ("nameOfAllahContentId") REFERENCES "name_of_allah_content"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_practices_journey" ADD CONSTRAINT "daily_practices_journey_quranicVerseContentId_fkey" FOREIGN KEY ("quranicVerseContentId") REFERENCES "quranic_verse_content"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_practices_journey" ADD CONSTRAINT "daily_practices_journey_sunnahPracticeContentId_fkey" FOREIGN KEY ("sunnahPracticeContentId") REFERENCES "sunnah_practice_content"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_progress" ADD CONSTRAINT "journey_progress_journey_id_fkey" FOREIGN KEY ("journey_id") REFERENCES "journeys"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_progress" ADD CONSTRAINT "journey_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_analytics" ADD CONSTRAINT "journey_analytics_journey_id_fkey" FOREIGN KEY ("journey_id") REFERENCES "journeys"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_analytics" ADD CONSTRAINT "journey_analytics_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "community_groups" ADD CONSTRAINT "community_groups_mentor_id_fkey" FOREIGN KEY ("mentor_id") REFERENCES "profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "community_memberships" ADD CONSTRAINT "community_memberships_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "community_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "community_memberships" ADD CONSTRAINT "community_memberships_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_journeys" ADD CONSTRAINT "user_journeys_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_modules" ADD CONSTRAINT "journey_modules_journey_id_fkey" FOREIGN KEY ("journey_id") REFERENCES "user_journeys"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "journey_modules" ADD CONSTRAINT "journey_modules_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "module_completions" ADD CONSTRAINT "module_completions_module_id_fkey" FOREIGN KEY ("module_id") REFERENCES "journey_modules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "module_completions" ADD CONSTRAINT "module_completions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_check_ins" ADD CONSTRAINT "daily_check_ins_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_streaks" ADD CONSTRAINT "user_streaks_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_achievements" ADD CONSTRAINT "user_achievements_achievement_type_fkey" FOREIGN KEY ("achievement_type") REFERENCES "achievements"("name") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_achievements" ADD CONSTRAINT "user_achievements_journey_id_fkey" FOREIGN KEY ("journey_id") REFERENCES "user_journeys"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_achievements" ADD CONSTRAINT "user_achievements_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_assessments" ADD CONSTRAINT "ruqya_assessments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_analysis" ADD CONSTRAINT "ruqya_analysis_assessment_id_fkey" FOREIGN KEY ("assessment_id") REFERENCES "ruqya_assessments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_analysis" ADD CONSTRAINT "ruqya_analysis_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_treatment_plans" ADD CONSTRAINT "ruqya_treatment_plans_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_practices" ADD CONSTRAINT "ruqya_practices_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "ruqya_treatment_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_practices" ADD CONSTRAINT "ruqya_practices_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_practice_progress" ADD CONSTRAINT "ruqya_practice_progress_practice_id_fkey" FOREIGN KEY ("practice_id") REFERENCES "ruqya_practices"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_practice_progress" ADD CONSTRAINT "ruqya_practice_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_favorite_duas" ADD CONSTRAINT "user_favorite_duas_dua_id_fkey" FOREIGN KEY ("dua_id") REFERENCES "ruqya_duas"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_favorite_duas" ADD CONSTRAINT "user_favorite_duas_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_concerns" ADD CONSTRAINT "ruqya_concerns_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_consultations" ADD CONSTRAINT "ruqya_consultations_practitioner_id_fkey" FOREIGN KEY ("practitioner_id") REFERENCES "profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_consultations" ADD CONSTRAINT "ruqya_consultations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_user_precautions" ADD CONSTRAINT "ruqya_user_precautions_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "ruqya_treatment_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_user_precautions" ADD CONSTRAINT "ruqya_user_precautions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_daily_protection" ADD CONSTRAINT "ruqya_daily_protection_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_protection_streaks" ADD CONSTRAINT "user_protection_streaks_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_effectiveness_assessments" ADD CONSTRAINT "ruqya_effectiveness_assessments_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "ruqya_treatment_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ruqya_effectiveness_assessments" ADD CONSTRAINT "ruqya_effectiveness_assessments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "symptom_submissions" ADD CONSTRAINT "symptom_submissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_diagnoses" ADD CONSTRAINT "user_diagnoses_submission_id_fkey" FOREIGN KEY ("submission_id") REFERENCES "symptom_submissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_diagnoses" ADD CONSTRAINT "user_diagnoses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "symptom_tracking" ADD CONSTRAINT "symptom_tracking_symptom_id_fkey" FOREIGN KEY ("symptom_id") REFERENCES "symptom_submissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "symptom_tracking" ADD CONSTRAINT "symptom_tracking_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessment_symptoms" ADD CONSTRAINT "assessment_symptoms_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "assessment_questions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ContentJourneyModules" ADD CONSTRAINT "_ContentJourneyModules_A_fkey" FOREIGN KEY ("A") REFERENCES "content_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ContentJourneyModules" ADD CONSTRAINT "_ContentJourneyModules_B_fkey" FOREIGN KEY ("B") REFERENCES "journey_modules"("id") ON DELETE CASCADE ON UPDATE CASCADE;
