-- CreateTable
CREATE TABLE "diagnosis_feedback" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "diagnosis_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "accuracy" INTEGER NOT NULL,
    "helpfulness" INTEGER NOT NULL,
    "comments" TEXT,
    "submitted_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "diagnosis_feedback_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "diagnosis_feedback_diagnosis_id_user_id_key" ON "diagnosis_feedback"("diagnosis_id", "user_id");

-- AddForeignKey
ALTER TABLE "diagnosis_feedback" ADD CONSTRAINT "diagnosis_feedback_diagnosis_id_fkey" FOREIGN KEY ("diagnosis_id") REFERENCES "spiritual_diagnoses"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddF<PERSON>ign<PERSON><PERSON>
ALTER TABLE "diagnosis_feedback" ADD CONSTRAINT "diagnosis_feedback_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
