generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Profile {
  id                               String                         @id @db.Uuid
  email                            String                         @unique
  firstName                        String                         @map("first_name")
  lastName                         String?                        @map("last_name")
  fullName                         String?                        @map("full_name")
  avatarUrl                        String?                        @map("avatar_url")
  createdAt                        DateTime                       @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt                        DateTime                       @updatedAt @map("updated_at") @db.Timestamp(6)
  analyticsExports                 AnalyticsExport[]
  analyticsMetrics                 AnalyticsMetric[]
  analyticsWebhooks                AnalyticsWebhook[]
  assessmentSessions               AssessmentSession[]
  communityGroupsMentored          CommunityGroup[]               @relation("CommunityGroupMentor")
  communityMemberships             CommunityMembership[]
  contentCompletions               ContentCompletion[]
  contentInteractions              ContentInteraction[]
  contentIssues                    ContentIssue[]
  contentProgress                  ContentProgress[]
  crisisEscalations                CrisisEscalation[]
  crisisEvents                     CrisisEvent[]
  crisisFollowups                  CrisisFollowup[]
  dailyCheckIns                    DailyCheckIn[]
  dailyPracticesGeneral            DailyPracticeGeneral[]
  emergencySessions                EmergencySession[]
  healthAssessments                HealthAssessment[]
  journalEntries                   JournalEntry[]
  journeyAnalyticsEntries          JourneyAnalytics[]
  journeyMilestones                JourneyMilestone[]
  journeyModulesAsUser             JourneyModule[]                @relation("JourneyModuleUser")
  journeyProgressEntries           JourneyProgress[]
  mentoredJourneys                 Journey[]                      @relation("JourneyMentor")
  journeys                         Journey[]                      @relation("UserJourneys")
  milestoneCompletions             MilestoneCompletion[]
  moduleCompletions                ModuleCompletion[]
  onboardingSessions               OnboardingSession[]
  profileUpdates                   ProfileUpdate[]
  ruqyaAnalyses                    RuqyaAnalysis[]
  ruqyaAssessments                 RuqyaAssessment[]
  ruqyaConcerns                    RuqyaConcern[]
  ruqyaConsultationsAsPractitioner RuqyaConsultation[]            @relation("PractitionerConsultations")
  ruqyaConsultationsAsUser         RuqyaConsultation[]            @relation("UserConsultations")
  ruqyaDailyProtections            RuqyaDailyProtection[]
  ruqyaEffectivenessAssessments    RuqyaEffectivenessAssessment[]
  ruqyaPracticeProgress            RuqyaPracticeProgress[]
  ruqyaPracticesAsUser             RuqyaPractice[]                @relation("RuqyaPracticeUser")
  ruqyaTreatmentPlans              RuqyaTreatmentPlan[]
  ruqyaUserPrecautions             RuqyaUserPrecaution[]
  seriesCompletions                SeriesCompletion[]
  seriesProgress                   SeriesProgress[]
  spiritualDiagnoses               SpiritualDiagnosis[]
  diagnosisFeedback                DiagnosisFeedback[]
  symptomSubmissions               SymptomSubmission[]
  symptomTrackings                 SymptomTracking[]
  userAchievements                 UserAchievement[]              @relation("UserAchievementsLink")
  userDiagnosesFromSymptoms        UserDiagnosisFromSymptoms[]
  userFavoriteDuas                 UserFavoriteDua[]
  userInteractions                 UserInteraction[]
  userJourneys                     UserJourney[]                  @relation("UserSimpleJourneys")
  detailedUserProfile              UserProfileDetailed?           @relation("UserProfileToProfileRelation")
  userProtectionStreak             UserProtectionStreak?
  userStreak                       UserStreak?
  peerSupportRequestsAsUser        PeerSupportRequest[]           @relation("RequestingUser")
  peerSupportRequestsAsSupporter   PeerSupportRequest[]           @relation("SupportingUser")
  duaRequestLogs                   DuaRequestLog[]
  duaRequests                      DuaRequest[]

  @@map("profiles")
}

model UserProfileDetailed {
  id               String          @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId           String          @unique @map("user_id") @db.Uuid
  profileData      Json            @map("profile_data")
  completionStatus String          @default("incomplete") @map("completion_status")
  profileVersion   String          @default("1.0.0") @map("profile_version")
  createdAt        DateTime        @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt        DateTime        @updatedAt @map("updated_at") @db.Timestamp(6)
  profileUpdates   ProfileUpdate[]
  user             Profile         @relation("UserProfileToProfileRelation", fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

model OnboardingSession {
  id                String        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  sessionId         String        @unique @map("session_id")
  userId            String        @map("user_id") @db.Uuid
  startedAt         DateTime      @map("started_at") @db.Timestamp(6)
  completedAt       DateTime?     @map("completed_at") @db.Timestamp(6)
  currentStep       String        @map("current_step")
  steps             Json          @default("[]")
  totalTimeSpent    Int           @default(0) @map("total_time_spent")
  deviceInfo        Json?         @map("device_info")
  abandonedAt       DateTime?     @map("abandoned_at") @db.Timestamp(6)
  abandonmentReason String?       @map("abandonment_reason")
  createdAt         DateTime      @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt         DateTime      @updatedAt @map("updated_at") @db.Timestamp(6)
  crisisEvents      CrisisEvent[]
  user              Profile       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("onboarding_sessions")
}

model CrisisEvent {
  id                    String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                String             @map("user_id") @db.Uuid
  sessionId             String?            @map("session_id")
  crisisLevel           String             @map("crisis_level")
  indicators            String[]
  confidence            Decimal            @db.Decimal(3, 2)
  urgency               String
  context               Json?
  responseActions       String[]           @default([]) @map("response_actions")
  interventionTriggered Boolean            @default(false) @map("intervention_triggered")
  resolvedAt            DateTime?          @map("resolved_at") @db.Timestamp(6)
  createdAt             DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  onboardingSession     OnboardingSession? @relation(fields: [sessionId], references: [sessionId])
  user                  Profile            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("crisis_events")
}

model ProfileUpdate {
  id               String              @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  profileId        String              @map("profile_id") @db.Uuid
  updates          Json
  reason           String?
  confidence       Decimal?            @db.Decimal(3, 2)
  appliedAt        DateTime            @default(now()) @map("applied_at") @db.Timestamp(6)
  appliedBy        String?             @map("applied_by") @db.Uuid
  applicantProfile Profile?            @relation(fields: [appliedBy], references: [id])
  profile          UserProfileDetailed @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("profile_updates")
}

model OnboardingAnalytics {
  id                    String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  date                  DateTime @unique @db.Date
  totalSessions         Int      @default(0) @map("total_sessions")
  completedSessions     Int      @default(0) @map("completed_sessions")
  abandonedSessions     Int      @default(0) @map("abandoned_sessions")
  crisisDetections      Int      @default(0) @map("crisis_detections")
  averageCompletionTime Int?     @map("average_completion_time")
  stepDropoffData       Json?    @map("step_dropoff_data")
  pathwayDistribution   Json?    @map("pathway_distribution")
  createdAt             DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("onboarding_analytics")
}

model AssessmentSession {
  id                   String               @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId               String               @map("user_id") @db.Uuid
  userProfile          Json                 @map("user_profile")
  startedAt            DateTime             @map("started_at") @db.Timestamp(6)
  completedAt          DateTime?            @map("completed_at") @db.Timestamp(6)
  abandonedAt          DateTime?            @map("abandoned_at") @db.Timestamp(6)
  abandonmentReason    String?              @map("abandonment_reason")
  currentStep          String               @default("welcome") @map("current_step")
  totalSteps           Int                  @default(10) @map("total_steps")
  physicalExperiences  Json                 @default("{}") @map("physical_experiences")
  emotionalExperiences Json                 @default("{}") @map("emotional_experiences")
  mentalExperiences    Json                 @default("{}") @map("mental_experiences")
  spiritualExperiences Json                 @default("{}") @map("spiritual_experiences")
  reflections          Json                 @default("{}")
  timeSpentPerStep     Json                 @default("{}") @map("time_spent_per_step")
  totalTimeSpent       Int                  @default(0) @map("total_time_spent")
  sessionData          Json?                @map("session_data")
  deviceInfo           Json?                @map("device_info")
  createdAt            DateTime             @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt            DateTime             @updatedAt @map("updated_at") @db.Timestamp(6)
  user                 Profile              @relation(fields: [userId], references: [id], onDelete: Cascade)
  journeys             Journey[]
  spiritualDiagnoses   SpiritualDiagnosis[]

  @@map("assessment_sessions")
}

model SpiritualDiagnosis {
  id                       String               @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                   String               @map("user_id") @db.Uuid
  assessmentId             String               @map("assessment_id") @db.Uuid
  diagnosisData            Json                 @map("diagnosis_data")
  primaryLayer             String               @map("primary_layer")
  secondaryLayers          String[]             @default([]) @map("secondary_layers")
  overallSeverity          String               @map("overall_severity")
  crisisLevel              String               @default("none") @map("crisis_level")
  confidence               Decimal              @default(0.8) @db.Decimal(3, 2)
  recommendedJourneyType   String?              @map("recommended_journey_type")
  estimatedHealingDuration Int?                 @map("estimated_healing_duration")
  nextSteps                String[]             @default([]) @map("next_steps")
  userFeedback             Json?                @map("user_feedback")
  generatedAt              DateTime             @map("generated_at") @db.Timestamp(6)
  createdAt                DateTime             @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt                DateTime             @updatedAt @map("updated_at") @db.Timestamp(6)
  layerAnalyses            LayerAnalysis[]
  diagnosisFeedback        DiagnosisFeedback[]
  assessmentSession        AssessmentSession    @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  user                     Profile              @relation(fields: [userId], references: [id])

  @@map("spiritual_diagnoses")
}

model LayerAnalysis {
  id                 String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  diagnosisId        String             @map("diagnosis_id") @db.Uuid
  layer              String
  layerName          String             @map("layer_name")
  impactScore        Int                @default(0) @map("impact_score")
  priority           String
  affectedSymptoms   String[]           @default([]) @map("affected_symptoms")
  insights           String[]           @default([])
  recommendations    String[]           @default([])
  islamicContext     String?            @map("islamic_context")
  createdAt          DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  spiritualDiagnosis SpiritualDiagnosis @relation(fields: [diagnosisId], references: [id], onDelete: Cascade)

  @@map("layer_analyses")
}

model DiagnosisFeedback {
  id           String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  diagnosisId  String             @map("diagnosis_id") @db.Uuid
  userId       String             @map("user_id") @db.Uuid
  accuracy     Int                // Rating from 1-10
  helpfulness  Int                // Rating from 1-10
  comments     String?
  submittedAt  DateTime           @default(now()) @map("submitted_at") @db.Timestamp(6)
  createdAt    DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  diagnosis    SpiritualDiagnosis @relation(fields: [diagnosisId], references: [id], onDelete: Cascade)
  user         Profile            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([diagnosisId, userId])
  @@map("diagnosis_feedback")
}

model AssessmentAnalytics {
  id                       String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  date                     DateTime @unique @db.Date
  totalSessions            Int      @default(0) @map("total_sessions")
  completedSessions        Int      @default(0) @map("completed_sessions")
  abandonedSessions        Int      @default(0) @map("abandoned_sessions")
  totalDiagnoses           Int      @default(0) @map("total_diagnoses")
  crisisDetections         Int      @default(0) @map("crisis_detections")
  primaryLayerDistribution Json?    @map("primary_layer_distribution")
  severityDistribution     Json?    @map("severity_distribution")
  averageCompletionTime    Int?     @map("average_completion_time")
  averageAccuracyRating    Decimal? @map("average_accuracy_rating") @db.Decimal(3, 2)
  averageHelpfulnessRating Decimal? @map("average_helpfulness_rating") @db.Decimal(3, 2)
  stepCompletionRates      Json?    @map("step_completion_rates")
  stepAverageTimes         Json?    @map("step_average_times")
  createdAt                DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("assessment_analytics")
}

model ContentItem {
  id                   String               @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title                String
  description          String
  contentType          String               @map("content_type")
  healingLayer         String               @map("healing_layer")
  category             String
  storagePath          String               @map("storage_path")
  duration             Int?
  accessLevel          String               @default("basic") @map("access_level")
  requiresSubscription Boolean              @default(false) @map("requires_subscription")
  seriesId             String?              @map("series_id") @db.Uuid
  partNumber           Int?                 @map("part_number")
  focusAreas           String[]             @default([]) @map("focus_areas")
  viewCount            Int                  @default(0) @map("view_count")
  completionCount      Int                  @default(0) @map("completion_count")
  averageRating        Decimal?             @map("average_rating") @db.Decimal(3, 2)
  status               String               @default("draft")
  createdAt            DateTime             @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt            DateTime             @updatedAt @map("updated_at") @db.Timestamp(6)
  completions          ContentCompletion[]
  interactions         ContentInteraction[]
  issues               ContentIssue[]
  series               ContentSeries?       @relation(fields: [seriesId], references: [id])
  metadata             ContentMetadata[]
  progressEntries      ContentProgress[]
  tags                 ContentTag[]
  journeyModules       JourneyModule[]      @relation("ContentJourneyModules")

  @@map("content_items")
}

model ContentMetadata {
  id                String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  contentId         String      @map("content_id") @db.Uuid
  language          String      @default("en")
  author            String?
  source            String?
  references        String[]    @default([])
  keywords          String[]    @default([])
  difficultyLevel   String?     @map("difficulty_level")
  estimatedDuration Int?        @map("estimated_duration")
  prerequisites     String[]    @default([]) @map("prerequisites")
  createdAt         DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  contentItem       ContentItem @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@map("content_metadata")
}

model ContentTag {
  id          String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  contentId   String      @map("content_id") @db.Uuid
  tagName     String      @map("tag_name")
  createdAt   DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  contentItem ContentItem @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@unique([contentId, tagName], name: "unique_content_tag")
  @@map("content_tags")
}

model ContentSeries {
  id              String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title           String
  description     String
  totalParts      Int                @map("total_parts")
  category        String
  healingLayer    String             @map("healing_layer")
  difficultyLevel String             @map("difficulty_level")
  prerequisites   String[]           @default([])
  status          String             @default("active")
  createdAt       DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  items           ContentItem[]
  completions     SeriesCompletion[]
  progressEntries SeriesProgress[]

  @@map("content_series")
}

model ContentInteraction {
  id              String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId          String      @map("user_id") @db.Uuid
  contentId       String      @map("content_id") @db.Uuid
  interactionType String      @map("interaction_type")
  duration        Int?
  progress        Int?
  rating          Int?
  interactionDate DateTime    @map("interaction_date") @db.Timestamp(6)
  createdAt       DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  contentItem     ContentItem @relation(fields: [contentId], references: [id], onDelete: Cascade)
  user            Profile     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("content_interactions")
}

model ContentProgress {
  id                 String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId             String      @map("user_id") @db.Uuid
  contentId          String      @map("content_id") @db.Uuid
  progressPercentage Int         @map("progress_percentage")
  lastAccessed       DateTime    @map("last_accessed") @db.Timestamp(6)
  createdAt          DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  contentItem        ContentItem @relation(fields: [contentId], references: [id], onDelete: Cascade)
  user               Profile     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, contentId], name: "unique_user_content_progress")
  @@map("content_progress")
}

model SeriesProgress {
  id             String        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String        @map("user_id") @db.Uuid
  seriesId       String        @map("series_id") @db.Uuid
  completedParts Int           @default(0) @map("completed_parts")
  isCompleted    Boolean       @default(false) @map("is_completed")
  lastUpdated    DateTime      @map("last_updated") @db.Timestamp(6)
  createdAt      DateTime      @default(now()) @map("created_at") @db.Timestamp(6)
  contentSeries  ContentSeries @relation(fields: [seriesId], references: [id], onDelete: Cascade)
  user           Profile       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, seriesId], name: "unique_user_series_progress")
  @@map("series_progress")
}

model ContentCompletion {
  id             String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String      @map("user_id") @db.Uuid
  contentId      String      @map("content_id") @db.Uuid
  completionDate DateTime    @map("completion_date") @db.Timestamp(6)
  createdAt      DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  contentItem    ContentItem @relation(fields: [contentId], references: [id], onDelete: Cascade)
  user           Profile     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, contentId], name: "unique_user_content_completion")
  @@map("content_completions")
}

model SeriesCompletion {
  id             String        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String        @map("user_id") @db.Uuid
  seriesId       String        @map("series_id") @db.Uuid
  completionDate DateTime      @map("completion_date") @db.Timestamp(6)
  createdAt      DateTime      @default(now()) @map("created_at") @db.Timestamp(6)
  contentSeries  ContentSeries @relation(fields: [seriesId], references: [id], onDelete: Cascade)
  user           Profile       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, seriesId], name: "unique_user_series_completion")
  @@map("series_completions")
}

model ContentIssue {
  id           String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId       String      @map("user_id") @db.Uuid
  contentId    String      @map("content_id") @db.Uuid
  issueType    String      @map("issue_type")
  description  String
  status       String      @default("pending")
  resolution   String?
  reportDate   DateTime    @map("report_date") @db.Timestamp(6)
  resolvedDate DateTime?   @map("resolved_date") @db.Timestamp(6)
  createdAt    DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  contentItem  ContentItem @relation(fields: [contentId], references: [id], onDelete: Cascade)
  user         Profile     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("content_issues")
}

model HealthAssessment {
  id             String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String   @map("user_id") @db.Uuid
  metrics        Json
  assessmentDate DateTime @map("assessment_date") @db.Timestamp(6)
  notes          String?
  createdAt      DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user           Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("health_assessments")
}

model UserInteraction {
  id              String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId          String   @map("user_id") @db.Uuid
  interactionType String   @map("interaction_type")
  layer           String?
  engagementScore Int?     @map("engagement_score")
  durationSeconds Int?     @map("duration_seconds")
  interactionDate DateTime @map("interaction_date") @db.Timestamp(6)
  metadata        Json?
  createdAt       DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user            Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_interactions")
}

model DailyPracticeGeneral {
  id                  String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId              String   @map("user_id") @db.Uuid
  practiceType        String   @map("practice_type")
  layer               String
  practiceDate        DateTime @map("practice_date") @db.Date
  completionStatus    String   @map("completion_status")
  durationMinutes     Int?     @map("duration_minutes")
  effectivenessRating Int?     @map("effectiveness_rating")
  notes               String?
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user                Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("daily_practices")
}

model JourneyMilestone {
  id                   String                @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId               String                @map("user_id") @db.Uuid
  journeyId            String                @map("journey_id") @db.Uuid
  name                 String
  description          String
  milestoneType        String                @map("milestone_type")
  sequenceOrder        Int                   @map("sequence_order")
  requirements         Json
  createdAt            DateTime              @default(now()) @map("created_at") @db.Timestamp(6)
  userJourney          UserJourney           @relation(fields: [journeyId], references: [id])
  user                 Profile               @relation(fields: [userId], references: [id])
  milestoneCompletions MilestoneCompletion[]

  @@map("journey_milestones")
}

model MilestoneCompletion {
  id               String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId           String           @map("user_id") @db.Uuid
  milestoneId      String           @map("milestone_id") @db.Uuid
  completionDate   DateTime         @map("completion_date") @db.Timestamp(6)
  reflection       String?
  createdAt        DateTime         @default(now()) @map("created_at") @db.Timestamp(6)
  journeyMilestone JourneyMilestone @relation(fields: [milestoneId], references: [id])
  user             Profile          @relation(fields: [userId], references: [id])

  @@unique([userId, milestoneId], name: "unique_user_milestone")
  @@map("milestone_completions")
}

model AnalyticsMetric {
  time        DateTime @db.Timestamp(6)
  userId      String   @map("user_id") @db.Uuid
  metricName  String   @map("metric_name")
  metricValue Decimal  @map("metric_value") @db.Decimal
  metadata    Json?
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user        Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([time, userId, metricName])
  @@map("analytics_metrics")
}

model AnalyticsWebhook {
  id            String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId        String    @map("user_id") @db.Uuid
  url           String
  events        String[]
  status        String    @default("active")
  lastTriggered DateTime? @map("last_triggered") @db.Timestamp(6)
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  user          Profile   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("analytics_webhooks")
}

model AnalyticsExport {
  id         String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId     String    @map("user_id") @db.Uuid
  exportType String    @map("export_type")
  timeframe  String
  status     String    @default("pending")
  fileUrl    String?   @map("file_url")
  exportDate DateTime? @map("export_date") @db.Timestamp(6)
  createdAt  DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  user       Profile   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("analytics_exports")
}

model EmergencySession {
  id                  String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId              String    @map("user_id") @db.Uuid
  triggerType         String    @default("manual") @map("trigger_type")
  currentStep         String?   @map("current_step") // Added for Qalb Rescue
  currentSymptoms     String[]  @default([]) @map("current_symptoms")
  startTime           DateTime  @default(now()) @map("start_time") @db.Timestamp(6)
  endTime             DateTime? @map("end_time") @db.Timestamp(6)
  status              String    @default("active")
  escalationReason    String? // Added for Qalb Rescue feature
  recommendedActions  String[]  @default([]) @map("recommended_actions")
  estimatedDuration   Int?      @default(15) @map("estimated_duration")
  effectivenessRating Int?      @map("effectiveness_rating")
  feedback            String?
  log                 Json? // Added for Qalb Rescue step timings and events
  createdAt           DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt           DateTime  @updatedAt @map("updated_at") @db.Timestamp(6)
  user                Profile   @relation(fields: [userId], references: [id], onDelete: Cascade)
  peerSupportRequests PeerSupportRequest[] // Relation to PeerSupportRequest
  duaRequestLogs      DuaRequestLog[]      // Relation to DuaRequestLog

  @@map("emergency_sessions")
}

model PeerSupportRequest {
  id          String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  sessionId   String   @map("session_id") @db.Uuid
  userId      String   @map("user_id") @db.Uuid // User requesting support
  supporterId String?  @map("supporter_id") @db.Uuid // User providing support
  status      String   @default("pending") // e.g., pending, active, closed, unavailable
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamp(6)

  session   EmergencySession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user      Profile          @relation("RequestingUser", fields: [userId], references: [id], onDelete: Cascade)
  supporter Profile?         @relation("SupportingUser", fields: [supporterId], references: [id])

  @@map("peer_support_requests")
}

model DuaRequestLog {
  id          String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  sessionId   String   @map("session_id") @db.Uuid
  userId      String   @map("user_id") @db.Uuid // User whose session triggered the dua request
  requestedAt DateTime @default(now()) @map("requested_at") @db.Timestamp(6)

  session EmergencySession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    Profile          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("dua_request_logs")
}

model CrisisEscalation {
  id              String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  escalationId    String           @unique @map("escalation_id")
  userId          String           @map("user_id") @db.Uuid
  severity        String
  indicators      String[]         @default([])
  context         Json             @default("{}")
  immediateRisk   Boolean          @default(false) @map("immediate_risk")
  status          String           @default("active") @map("status")
  actionsTaken    String[]         @default([]) @map("actions_taken")
  createdAt       DateTime         @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt       DateTime         @updatedAt @map("updated_at") @db.Timestamp(6)
  user            Profile          @relation(fields: [userId], references: [id], onDelete: Cascade)
  crisisFollowups CrisisFollowup[]

  @@map("crisis_escalations")
}

model CrisisFollowup {
  id                String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  escalationId      String           @map("escalation_id") @db.Uuid
  userId            String           @map("user_id") @db.Uuid
  status            String
  notes             String?
  additionalSupport Boolean          @default(false) @map("additional_support")
  createdAt         DateTime         @default(now()) @map("created_at") @db.Timestamp(6)
  crisisEscalation  CrisisEscalation @relation(fields: [escalationId], references: [id], onDelete: Cascade)
  user              Profile          @relation(fields: [userId], references: [id])

  @@map("crisis_followups")
}

model BreathingExercise {
  id           String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name         String
  instructions String[] @default([])
  duration     Int
  intensity    String   @default("medium")
  audioUrl     String?  @map("audio_url")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("breathing_exercises")
}

model DhikrContent {
  id              String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  arabic          String
  transliteration String
  translation     String
  count           Int?     @default(1)
  category        String   @default("general")
  priority        Int?     @default(0)
  audioUrl        String?  @map("audio_url")
  isActive        Boolean  @default(true) @map("is_active")
  createdAt       DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("dhikr_content")
}

model RuqyahVerse {
  id            String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  surah         String
  ayah          Int
  arabic        String
  translation   String
  category      String   @default("general") @map("category")
  sequenceOrder Int?     @default(0) @map("sequence_order")
  audioUrl      String?  @map("audio_url")
  isActive      Boolean  @default(true) @map("is_active")
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("ruqyah_verses")
}

model EmergencyHelpline {
  id           String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name         String
  phone        String
  description  String?
  availability String?  @default("24/7")
  country      String   @default("US")
  isIslamic    Boolean? @default(false) @map("is_islamic")
  priority     Int?     @default(0)
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("emergency_helplines")
}

model JournalEntry {
  id          String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  title       String
  content     String
  entryType   String   @default("general") @map("entry_type")
  tags        String[] @default([])
  relatedData Json     @default("{}") @map("related_data")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamp(6)
  user        Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("journal_entries")
}

model Journey {
  id                    String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                String             @map("user_id") @db.Uuid
  assessmentId          String             @map("assessment_id") @db.Uuid
  type                  JourneyType
  status                JourneyStatus      @default(created)
  title                 String
  description           String
  personalizedWelcome   String             @map("personalized_welcome")
  duration              Int
  dailyTimeCommitment   Int                @map("daily_time_commitment")
  primaryLayer          LayerFocus         @map("primary_layer")
  secondaryLayers       LayerFocus[]       @default([]) @map("secondary_layers")
  ruqyaIntegrationLevel String             @default("none") @map("ruqya_integration_level")
  communityIntegration  Boolean            @default(false) @map("community_integration")
  professionalContext   String?            @map("professional_context")
  culturalAdaptations   String[]           @default([]) @map("cultural_adaptations")
  crisisSupport         Boolean            @default(false) @map("crisis_support")
  currentDay            Int?               @default(1) @map("current_day")
  completedDays         Int[]              @default([]) @map("completed_days")
  totalProgress         Decimal?           @default(0.00) @map("total_progress") @db.Decimal(5, 2)
  userProfile           Json               @map("user_profile")
  communityGroupId      String?            @map("community_group") @db.Uuid
  mentorId              String?            @map("mentor_id") @db.Uuid
  peerConnections       String[]           @default([]) @map("peer_connections")
  aiRecommendations     String[]           @default([]) @map("ai_recommendations")
  adaptiveAdjustments   Json[]             @default([]) @map("adaptive_adjustments")
  crisisFlags           Json[]             @default([]) @map("crisis_flags")
  createdAt             DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  startedAt             DateTime?          @map("started_at") @db.Timestamp(6)
  completedAt           DateTime?          @map("completed_at") @db.Timestamp(6)
  lastActiveAt          DateTime?          @map("last_active_at") @db.Timestamp(6)
  updatedAt             DateTime           @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  journeyAnalytics      JourneyAnalytics[]
  journeyDays           JourneyDay[]
  journeyProgress       JourneyProgress[]
  assessmentSession     AssessmentSession  @relation(fields: [assessmentId], references: [id])
  communityGroup        CommunityGroup?    @relation(fields: [communityGroupId], references: [id])
  mentor                Profile?           @relation("JourneyMentor", fields: [mentorId], references: [id])
  user                  Profile            @relation("UserJourneys", fields: [userId], references: [id], onDelete: Cascade)

  @@map("journeys")
}

model JourneyDay {
  id                      String                   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  journeyId               String                   @map("journey_id") @db.Uuid
  dayNumber               Int                      @map("day_number")
  theme                   String
  learningObjective       String                   @map("learning_objective")
  reflectionPrompts       String[]                 @default([]) @map("reflection_prompts")
  communityActivity       String?                  @map("community_activity")
  progressMilestone       String?                  @map("progress_milestone")
  adaptiveContent         Json                     @default("{}") @map("adaptive_content")
  createdAt               DateTime                 @default(now()) @map("created_at") @db.Timestamp(6)
  dailyPracticesInJourney DailyPracticeInJourney[]
  journey                 Journey                  @relation(fields: [journeyId], references: [id], onDelete: Cascade)

  @@unique([journeyId, dayNumber])
  @@map("journey_days")
}

model DailyPracticeInJourney {
  id                  String       @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  journeyDayId        String       @map("journey_day_id") @db.Uuid
  type                PracticeType
  title               String
  description         String
  duration            Int
  instructions        String
  arabicText          String?      @map("arabic_text")
  transliteration     String?
  translation         String?
  benefits            String[]     @default([])
  layerFocus          LayerFocus   @map("layer_focus")
  difficultyLevel     String       @default("beginner") @map("difficulty_level")
  ruqyaComponent      Boolean      @default(false) @map("ruqya_component")
  professionalContext String?      @map("professional_context")
  culturalNotes       String?      @map("cultural_notes")
  orderIndex          Int          @default(0) @map("order_index")
  componentData       Json?        @map("component_data") // To store component-specific details
  createdAt           DateTime     @default(now()) @map("created_at") @db.Timestamp(6)
  journeyDay          JourneyDay   @relation(fields: [journeyDayId], references: [id], onDelete: Cascade)
  nameOfAllahContent  NameOfAllahContent? @relation(fields: [nameOfAllahContentId], references: [id])
  nameOfAllahContentId String? @db.Uuid
  quranicVerseContent QuranicVerseContent? @relation(fields: [quranicVerseContentId], references: [id])
  quranicVerseContentId String? @db.Uuid
  sunnahPracticeContent SunnahPracticeContent? @relation(fields: [sunnahPracticeContentId], references: [id])
  sunnahPracticeContentId String? @db.Uuid


  @@map("daily_practices_journey")
}

model JourneyProgress {
  id                     String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  journeyId              String   @map("journey_id") @db.Uuid
  userId                 String   @map("user_id") @db.Uuid
  dayNumber              Int      @map("day_number")
  date                   DateTime @default(now()) @db.Date
  practicesCompleted     Json[]   @default([]) @map("practices_completed")
  overallRating          Int?     @map("overall_rating")
  moodBefore             Int?     @map("mood_before") // From Morning Check-in
  moodAfter              Int?     @map("mood_after") // From Morning Check-in or end-of-day reflection
  energyLevelBefore      Int?     @map("energy_level_before") // From Morning Check-in
  spiritualStateBefore   String?  @map("spiritual_state_before") // From Morning Check-in
  dailyIntention         String?  @map("daily_intention") // From Morning Check-in (Niyyah)
  spiritualConnection    Int?     @map("spiritual_connection") // General or tied to a specific practice
  stressLevel            Int?     @map("stress_level") // General or tied to practices
  dailyReflection        String?  @map("daily_reflection") // From PersonalReflectionJournaling
  gratitude              String[] @default([]) // From PersonalReflectionJournaling or specific practice
  challenges             String[] @default([])
  insights               String[] @default([])
  communityParticipation Boolean  @default(false) @map("community_participation")
  communityContribution  String?  @map("community_contribution")
  peerSupport            Boolean  @default(false) @map("peer_support")
  contentRelevance       Int?     @map("content_relevance")
  practiceEffectiveness  Int?     @map("practice_effectiveness")
  timeAppropriate        Boolean? @map("time_appropriate")
  suggestedAdjustments   String?  @map("suggested_adjustments")
  createdAt              DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt              DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  journey                Journey  @relation(fields: [journeyId], references: [id], onDelete: Cascade)
  user                   Profile  @relation(fields: [userId], references: [id])

  @@unique([journeyId, userId, dayNumber])
  @@map("journey_progress")
}

model JourneyAnalytics {
  id                      String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  journeyId               String   @map("journey_id") @db.Uuid
  userId                  String   @map("user_id") @db.Uuid
  completionRate          Decimal  @default(0.00) @map("completion_rate") @db.Decimal(5, 2)
  averageDailyRating      Decimal? @map("average_daily_rating") @db.Decimal(3, 2)
  practiceAdherence       Decimal  @default(0.00) @map("practice_adherence") @db.Decimal(5, 2)
  communityEngagement     Decimal  @default(0.00) @map("community_engagement") @db.Decimal(5, 2)
  symptomImprovement      Json     @default("{}") @map("symptom_improvement")
  spiritualDevelopment    Json     @default("{}") @map("spiritual_development")
  personalizationSuccess  Json     @default("{}") @map("personalization_success")
  nextStepRecommendations String[] @default([]) @map("next_step_recommendations")
  graduationReadiness     Boolean  @default(false) @map("graduation_readiness")
  generatedAt             DateTime @default(now()) @map("generated_at") @db.Timestamp(6)
  journey                 Journey  @relation(fields: [journeyId], references: [id], onDelete: Cascade)
  user                    Profile  @relation(fields: [userId], references: [id])

  @@unique([journeyId, userId])
  @@map("journey_analytics")
}

model CommunityGroup {
  id               String                @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name             String
  description      String
  groupType        String                @map("group_type")
  matchingCriteria Json                  @default("{}") @map("matching_criteria")
  maxMembers       Int?                  @default(20) @map("max_members")
  currentMembers   Int?                  @default(0) @map("current_members")
  mentorId         String?               @map("mentor_id") @db.Uuid
  isActive         Boolean               @default(true) @map("is_active")
  createdAt        DateTime              @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt        DateTime              @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  mentor           Profile?              @relation("CommunityGroupMentor", fields: [mentorId], references: [id])
  memberships      CommunityMembership[]
  journeys         Journey[]

  @@map("community_groups")
}

model CommunityMembership {
  id       String         @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId   String         @map("user_id") @db.Uuid
  groupId  String         @map("group_id") @db.Uuid
  role     String         @default("member")
  joinedAt DateTime       @default(now()) @map("joined_at") @db.Timestamp(6)
  isActive Boolean        @default(true) @map("is_active")
  group    CommunityGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user     Profile        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, groupId])
  @@map("community_memberships")
}

model UserJourney {
  id                String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId            String             @map("user_id") @db.Uuid
  journeyType       String             @map("journey_type")
  focusLayers       String[]           @map("focus_layers")
  durationDays      Int                @map("duration_days")
  startDate         DateTime           @map("start_date") @db.Timestamp(6)
  endDate           DateTime?          @map("end_date") @db.Timestamp(6)
  status            String             @default("active")
  currentDay        Int                @default(1) @map("current_day")
  modulesPlan       Json               @default("[]") @map("modules_plan")
  lastActivityDate  DateTime?          @map("last_activity_date") @db.Timestamp(6)
  createdAt         DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  journeyMilestones JourneyMilestone[]
  journeyModules    JourneyModule[]
  userAchievements  UserAchievement[]
  user              Profile            @relation("UserSimpleJourneys", fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_journeys")
}

model JourneyModule {
  id                String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  journeyId         String             @map("journey_id") @db.Uuid
  userId            String             @map("user_id") @db.Uuid
  title             String
  type              String
  dayNumber         Int                @map("day_number")
  content           Json
  focusLayer        String             @map("focus_layer")
  estimatedDuration Int?               @map("estimated_duration")
  sequenceOrder     Int                @map("sequence_order")
  prerequisites     String[]           @default([]) @map("prerequisites")
  isAdvanced        Boolean            @default(false) @map("is_advanced")
  createdAt         DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  userJourney       UserJourney        @relation(fields: [journeyId], references: [id], onDelete: Cascade)
  user              Profile            @relation("JourneyModuleUser", fields: [userId], references: [id])
  moduleCompletions ModuleCompletion[]
  contentItems      ContentItem[]      @relation("ContentJourneyModules")

  @@map("journey_modules")
}

model ModuleCompletion {
  id             String        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String        @map("user_id") @db.Uuid
  moduleId       String        @map("module_id") @db.Uuid
  status         String
  reflections    String?
  challenges     Json?
  completionDate DateTime      @map("completion_date") @db.Timestamp(6)
  createdAt      DateTime      @default(now()) @map("created_at") @db.Timestamp(6)
  journeyModule  JourneyModule @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  user           Profile       @relation(fields: [userId], references: [id])

  @@map("module_completions")
}

model DailyCheckIn {
  id                String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId            String   @map("user_id") @db.Uuid
  mood              String
  dhikrCount        Int?     @default(0) @map("dhikr_count")
  prayerConsistency Int?     @map("prayer_consistency")
  notes             String?
  checkInDate       DateTime @map("check_in_date") @db.Timestamp(6)
  createdAt         DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user              Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("daily_check_ins")
}

model UserStreak {
  id            String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId        String   @unique @map("user_id") @db.Uuid
  currentStreak Int      @default(0) @map("current_streak")
  longestStreak Int      @default(0) @map("longest_streak")
  lastCheckIn   DateTime @map("last_check_in") @db.Timestamp(6)
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user          Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_streaks")
}

model Achievement {
  id               String            @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name             String            @unique
  description      String
  category         String
  requirementType  String            @map("requirement_type")
  requirementValue Int               @map("requirement_value")
  createdAt        DateTime          @default(now()) @map("created_at") @db.Timestamp(6)
  userAchievements UserAchievement[] @relation("UserAchievementsForAchievement")

  @@map("achievements")
}

model UserAchievement {
  id              String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId          String      @map("user_id") @db.Uuid
  journeyId       String      @map("journey_id") @db.Uuid
  achievementType String      @map("achievement_type")
  earnedDate      DateTime    @map("earned_date") @db.Timestamp(6)
  createdAt       DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  achievement     Achievement @relation("UserAchievementsForAchievement", fields: [achievementType], references: [name])
  userJourney     UserJourney @relation(fields: [journeyId], references: [id])
  user            Profile     @relation("UserAchievementsLink", fields: [userId], references: [id])

  @@unique([userId, achievementType])
  @@map("user_achievements")
}

model RuqyaAssessmentQuestion {
  id            String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  questionText  String   @map("question_text")
  category      String
  sequenceOrder Int      @map("sequence_order")
  responseType  String   @map("response_type")
  options       Json?
  status        String?  @default("active")
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("ruqya_assessment_questions")
}

model RuqyaAssessment {
  id               String          @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId           String          @map("user_id") @db.Uuid
  responses        Json
  additionalNotes  String?         @map("additional_notes")
  emergencyContact Json?           @map("emergency_contact")
  assessmentDate   DateTime        @map("assessment_date") @db.Timestamp(6)
  createdAt        DateTime        @default(now()) @map("created_at") @db.Timestamp(6)
  ruqyaAnalyses    RuqyaAnalysis[]
  user             Profile         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ruqya_assessments")
}

model RuqyaAnalysis {
  id                   String          @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  assessmentId         String          @map("assessment_id") @db.Uuid
  userId               String          @map("user_id") @db.Uuid
  riskLevel            String          @map("risk_level")
  recommendedPractices String[]        @map("recommended_practices")
  healingFocus         String[]        @map("healing_focus")
  spiritualInsights    Json            @map("spiritual_insights")
  analysisDate         DateTime        @map("analysis_date") @db.Timestamp(6)
  createdAt            DateTime        @default(now()) @map("created_at") @db.Timestamp(6)
  assessment           RuqyaAssessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  user                 Profile         @relation(fields: [userId], references: [id])

  @@map("ruqya_analysis")
}

model RuqyaTreatmentPlan {
  id                            String                         @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                        String                         @map("user_id") @db.Uuid
  riskLevel                     String                         @map("risk_level")
  healingFocus                  String[]                       @map("healing_focus")
  durationWeeks                 Int                            @map("duration_weeks")
  startDate                     DateTime                       @map("start_date") @db.Timestamp(6)
  endDate                       DateTime?                      @map("end_date") @db.Timestamp(6)
  status                        String                         @default("active")
  lastModified                  DateTime                       @map("last_modified") @db.Timestamp(6)
  createdAt                     DateTime                       @default(now()) @map("created_at") @db.Timestamp(6)
  ruqyaEffectivenessAssessments RuqyaEffectivenessAssessment[]
  ruqyaPractices                RuqyaPractice[]
  user                          Profile                        @relation(fields: [userId], references: [id], onDelete: Cascade)
  ruqyaUserPrecautions          RuqyaUserPrecaution[]

  @@map("ruqya_treatment_plans")
}

model RuqyaPractice {
  id                    String                  @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  planId                String                  @map("plan_id") @db.Uuid
  userId                String                  @map("user_id") @db.Uuid
  type                  String
  title                 String
  description           String
  instructions          String
  durationMinutes       Int?                    @map("duration_minutes")
  frequency             String
  prerequisites         String[]                @default([]) @map("prerequisites")
  contraindications     String[]                @default([])
  isMilestone           Boolean                 @default(false) @map("is_milestone")
  sequenceOrder         Int                     @map("sequence_order")
  arabicContent         Json?                   @map("arabic_content")
  audioUrl              String?                 @map("audio_url")
  createdAt             DateTime                @default(now()) @map("created_at") @db.Timestamp(6)
  ruqyaPracticeProgress RuqyaPracticeProgress[]
  plan                  RuqyaTreatmentPlan      @relation(fields: [planId], references: [id], onDelete: Cascade)
  user                  Profile                 @relation("RuqyaPracticeUser", fields: [userId], references: [id])

  @@map("ruqya_practices")
}

model RuqyaPracticeProgress {
  id             String        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String        @map("user_id") @db.Uuid
  practiceId     String        @map("practice_id") @db.Uuid
  completed      Boolean       @default(false)
  effectiveness  Int?
  notes          String?
  completionDate DateTime?     @map("completion_date") @db.Timestamp(6)
  createdAt      DateTime      @default(now()) @map("created_at") @db.Timestamp(6)
  practice       RuqyaPractice @relation(fields: [practiceId], references: [id], onDelete: Cascade)
  user           Profile       @relation(fields: [userId], references: [id])

  @@map("ruqya_practice_progress")
}

model RuqyaDua {
  id               String            @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title            String
  arabicText       String            @map("arabic_text")
  translation      String
  transliteration  String?
  category         String
  benefits         String[]          @default([])
  source           String?
  audioUrl         String?           @map("audio_url")
  status           String?           @default("active")
  createdAt        DateTime          @default(now()) @map("created_at") @db.Timestamp(6)
  userFavoriteDuas UserFavoriteDua[]

  @@map("ruqya_duas")
}

model UserFavoriteDua {
  id        String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  duaId     String   @map("dua_id") @db.Uuid
  category  String
  notes     String?
  savedDate DateTime @map("saved_date") @db.Timestamp(6)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  dua       RuqyaDua @relation(fields: [duaId], references: [id], onDelete: Cascade)
  user      Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, duaId], name: "unique_user_dua")
  @@map("user_favorite_duas")
}

model RuqyaResource {
  id            String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title         String
  description   String
  category      String
  contentType   String   @map("content_type")
  contentUrl    String   @map("content_url")
  prerequisites String[] @default([])
  status        String?  @default("active")
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("ruqya_resources")
}

model RuqyaConcern {
  id                      String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                  String   @map("user_id") @db.Uuid
  concernType             String   @map("concern_type")
  description             String
  severity                Int
  isEmergency             Boolean  @default(false) @map("is_emergency")
  emergencyAssessment     Json?    @map("emergency_assessment")
  needsImmediateAttention Boolean  @default(false) @map("needs_immediate_attention")
  reportDate              DateTime @map("report_date") @db.Timestamp(6)
  createdAt               DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user                    Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ruqya_concerns")
}

model RuqyaConsultation {
  id                String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId            String    @map("user_id") @db.Uuid
  preferredDate     DateTime  @map("preferred_date") @db.Timestamp(6)
  consultationType  String    @map("consultation_type")
  primaryConcern    String    @map("primary_concern")
  previousTreatment Boolean?  @map("previous_treatment")
  status            String    @default("pending")
  practitionerId    String?   @map("practitioner_id") @db.Uuid
  scheduledDate     DateTime? @map("scheduled_date") @db.Timestamp(6)
  notes             String?
  requestDate       DateTime  @map("request_date") @db.Timestamp(6)
  createdAt         DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  practitioner      Profile?  @relation("PractitionerConsultations", fields: [practitionerId], references: [id])
  user              Profile   @relation("UserConsultations", fields: [userId], references: [id], onDelete: Cascade)

  @@map("ruqya_consultations")
}

model RuqyaSafetyGuideline {
  id           String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title        String
  description  String
  instructions String[]
  priority     Int
  status       String?  @default("active")
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("ruqya_safety_guidelines")
}

model RuqyaUserPrecaution {
  id          String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId      String             @map("user_id") @db.Uuid
  planId      String             @map("plan_id") @db.Uuid
  precautions Json
  createdAt   DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  plan        RuqyaTreatmentPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  user        Profile            @relation(fields: [userId], references: [id])

  @@map("ruqya_user_precautions")
}

model RuqyaDailyProtection {
  id             String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String   @map("user_id") @db.Uuid
  practiceType   String   @map("practice_type")
  completed      Boolean
  completionTime DateTime @map("completion_time") @db.Timestamp(6)
  submissionDate DateTime @map("submission_date") @db.Timestamp(6)
  createdAt      DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user           Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ruqya_daily_protection")
}

model UserProtectionStreak {
  id               String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId           String   @unique @map("user_id") @db.Uuid
  currentStreak    Int      @default(0) @map("current_streak")
  longestStreak    Int      @default(0) @map("longest_streak")
  totalCompletions Int      @default(0) @map("total_completions")
  lastCompletion   DateTime @map("last_completion") @db.Timestamp(6)
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  user             Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_protection_streaks")
}

model RuqyaEffectivenessAssessment {
  id             String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId         String             @map("user_id") @db.Uuid
  planId         String             @map("plan_id") @db.Uuid
  metrics        Json
  assessmentDate DateTime           @map("assessment_date") @db.Timestamp(6)
  createdAt      DateTime           @default(now()) @map("created_at") @db.Timestamp(6)
  plan           RuqyaTreatmentPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  user           Profile            @relation(fields: [userId], references: [id])

  @@map("ruqya_effectiveness_assessments")
}

model SymptomSubmission {
  id                        String                      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId                    String                      @map("user_id") @db.Uuid
  jismSymptoms              Json                        @default("[]") @map("jism_symptoms")
  nafsSymptoms              Json                        @default("[]") @map("nafs_symptoms")
  aqlSymptoms               Json                        @default("[]") @map("aql_symptoms")
  qalbSymptoms              Json                        @default("[]") @map("qalb_symptoms")
  ruhSymptoms               Json                        @default("[]") @map("ruh_symptoms")
  intensityRatings          Json                        @map("intensity_ratings")
  duration                  String
  submissionDate            DateTime                    @default(now()) @map("submission_date") @db.Timestamp(6)
  createdAt                 DateTime                    @default(now()) @map("created_at") @db.Timestamp(6)
  user                      Profile                     @relation(fields: [userId], references: [id], onDelete: Cascade)
  symptomTrackings          SymptomTracking[]
  userDiagnosesFromSymptoms UserDiagnosisFromSymptoms[]

  @@map("symptom_submissions")
}

model UserDiagnosisFromSymptoms {
  id                 String            @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId             String            @map("user_id") @db.Uuid
  submissionId       String            @map("submission_id") @db.Uuid
  layersAffected     String[]          @map("layers_affected")
  spotlight          String
  recommendedJourney String            @map("recommended_journey")
  severityLevel      String            @map("severity_level")
  diagnosisDate      DateTime          @default(now()) @map("diagnosis_date") @db.Timestamp(6)
  createdAt          DateTime          @default(now()) @map("created_at") @db.Timestamp(6)
  symptomSubmission  SymptomSubmission @relation(fields: [submissionId], references: [id], onDelete: Cascade)
  user               Profile           @relation(fields: [userId], references: [id])

  @@map("user_diagnoses")
}

model SymptomTracking {
  id                String            @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId            String            @map("user_id") @db.Uuid
  symptomId         String            @map("symptom_id") @db.Uuid
  intensity         Int
  notes             String?
  trackingDate      DateTime          @default(now()) @map("tracking_date") @db.Timestamp(6)
  createdAt         DateTime          @default(now()) @map("created_at") @db.Timestamp(6)
  symptomSubmission SymptomSubmission @relation(fields: [symptomId], references: [id], onDelete: Cascade)
  user              Profile           @relation(fields: [userId], references: [id])

  @@map("symptom_tracking")
}

model AssessmentQuestion {
  id                       String                @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  category                 String
  layer                    String
  step                     String
  title                    String
  description              String?
  questionType             String                // Added missing field
  reflectionPrompt         String?               @map("reflection_prompt")
  reflectionRequired       Boolean               @default(false) @map("reflection_required")
  allowMultipleSelection   Boolean               @default(true) @map("allow_multiple_selection")
  intensityScale           Boolean               @default(true) @map("intensity_scale")
  customInputAllowed       Boolean               @default(true) @map("custom_input_allowed")
  showConditions           Json?                 @map("show_conditions")
  displayOrder             Int                   @default(0) @map("display_order")
  isActive                 Boolean               @default(true) @map("is_active")
  createdAt                DateTime              @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt                DateTime              @updatedAt @map("updated_at") @db.Timestamp(6)
  assessment_symptoms      AssessmentSymptom[]

  @@map("assessment_questions")
}

model AssessmentSymptom {
  id                           String                @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  questionId                   String?               @map("question_id") @db.Uuid
  text                         String
  description                  String?
  severity                     String
  primaryLayer                 String                @map("primary_layer")
  secondaryLayers              String[]              @default([]) @map("secondary_layers")
  clinicalTerms                String[]              @default([]) @map("clinical_terms")
  dsmMapping                   String[]              @default([]) @map("dsm_mapping")
  spiritualAilmentIndicators   String[]              @default([]) @map("spiritual_ailment_indicators")
  ruqyaRelevance               String                @default("none") @map("ruqya_relevance")
  islamicContext               String?               @map("islamic_context")
  displayOrder                 Int                   @default(0) @map("display_order")
  isActive                     Boolean               @default(true) @map("is_active")
  createdAt                    DateTime              @default(now()) @map("created_at") @db.Timestamp(6)
  assessmentQuestion           AssessmentQuestion?   @relation(fields: [questionId], references: [id])

  @@map("assessment_symptoms")
}

enum JourneyType {
  tranquil_mind
  heart_purification
  ego_purification
  spiritual_optimization
  crisis_recovery
  maintenance_program
}

enum JourneyStatus {
  created
  active
  paused
  completed
  abandoned
  evolved
}

enum PracticeType {
  dhikr
  prayer
  reflection
  study
  community
  ruqya
  mindfulness
  gratitude
  // Feature 2: 5 Daily Components
  MorningCheckIn
  NameOfAllahSpotlight
  QuranicVerseReflection
  PersonalReflectionJournaling
  SunnahPractice
}

enum LayerFocus {
  jism
  nafs
  aql
  qalb
  ruh
}

enum CrisisLevelType {
  none
  low
  moderate
  high
  critical
}

model NameOfAllahContent {
  id                  String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name                String    @unique // e.g., Ar-Rahman
  arabicScript        String?   @map("arabic_script") // Calligraphy path or direct script
  audioUrl            String?   @map("audio_url")
  meaning             String
  significance        String    @db.Text
  reflectionPrompt    String?   @map("reflection_prompt") @db.Text
  practicalApplication String?  @map("practical_application") @db.Text
  dhikrCount          Int?      @map("dhikr_count")
  layerFocus          LayerFocus[] @default([]) @map("layer_focus")
  benefits            String[]  @default([])
  sourceReference     String?   @map("source_reference")
  isActive            Boolean   @default(true) @map("is_active")
  createdAt           DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt           DateTime  @updatedAt @map("updated_at") @db.Timestamp(6)
  dailyPractices      DailyPracticeInJourney[]

  @@map("name_of_allah_content")
}

model QuranicVerseContent {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  surahNumber             Int       @map("surah_number")
  ayahNumber              Int       @map("ayah_number")
  arabicText              String    @map("arabic_text") @db.Text
  audioUrl                String?   @map("audio_url") // URL to recitation of this specific verse
  reciterOptions          Json?     @map("reciter_options") // e.g., { "Mishary": "url", "Sudais": "url" }
  translationEn           String?   @map("translation_en") @db.Text
  translationUr           String?   @map("translation_ur") @db.Text
  // Add other languages as needed
  tafsirSource            String?   @map("tafsir_source") // e.g., "Ibn Kathir", "Maariful Quran"
  tafsirEn                String?   @map("tafsir_en") @db.Text
  contextualExplanation   String?   @map("contextual_explanation") @db.Text
  reflectionPrompts       String[]  @default([]) @map("reflection_prompts")
  practicalApplication    String?   @map("practical_application") @db.Text
  layerFocus              LayerFocus[] @default([]) @map("layer_focus")
  themes                  String[]  @default([]) // e.g., ["Patience", "Gratitude", "Tawakkul"]
  relatedHadith           String[]  @default([]) @map("related_hadith")
  isActive                Boolean   @default(true) @map("is_active")
  createdAt               DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt               DateTime  @updatedAt @map("updated_at") @db.Timestamp(6)
  dailyPractices          DailyPracticeInJourney[]


  @@unique([surahNumber, ayahNumber])
  @@map("quranic_verse_content")
}

enum SunnahPracticeCategory {
  Physical
  Spiritual
  Social
  Mental
  Emotional
}

model SunnahPracticeContent {
  id                  String                 @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title               String                 @unique
  category            SunnahPracticeCategory
  description         String                 @db.Text
  stepsJson           Json?                  @map("steps_json") // Array of steps with details
  duasJson            Json?                  @map("duas_json") // Array of relevant duas with text/translation
  intention           String?                @db.Text
  reflection          String?                @db.Text
  benefits            String[]               @default([])
  estimatedDuration   Int?                   @map("estimated_duration_minutes")
  difficultyLevel     String?                @map("difficulty_level") // beginner, intermediate, advanced
  sourceReference     String?                @map("source_reference") // e.g., Hadith reference
  layerFocus          LayerFocus[]           @default([]) @map("layer_focus")
  relatedContentIds   String[]               @default([]) @map("related_content_ids") // For linking to other practices/verses
  isActive            Boolean                @default(true) @map("is_active")
  createdAt           DateTime               @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt           DateTime               @updatedAt @map("updated_at") @db.Timestamp(6)
  dailyPractices      DailyPracticeInJourney[]

  @@map("sunnah_practice_content")
}

model DuaRequest {
  id          String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  requestText String   @map("request_text") @db.Text
  status      String   @default("pending") // 'pending' | 'completed'
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  completedAt DateTime? @map("completed_at") @db.Timestamp(6)
  user        Profile  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("dua_requests")
}
