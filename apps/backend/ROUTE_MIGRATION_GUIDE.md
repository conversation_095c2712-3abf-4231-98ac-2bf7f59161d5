# Route Migration Guide - Enhanced Backend Architecture

## Overview

This guide provides step-by-step instructions for migrating existing routes to use the enhanced backend architecture with Islamic context, improved validation, and standardized responses.

## 🔄 Migration Status

### ✅ Completed Migrations
- **Authentication Middleware** - Updated to use enhanced auth system
- **Symptoms Routes** - Fully migrated with enhanced validation and responses
- **Main Server** - Updated with comprehensive middleware and versioning

### 🔄 In Progress
- **Emergency Routes** - Partially enhanced (existing implementation is good)
- **Auth Routes** - Need response format updates

### ⏳ Pending Migrations
- Assessment Routes
- Journey Routes
- Content Routes
- Community Routes
- Analytics Routes
- Dashboard Routes
- Journal Routes
- Ruqya Routes
- Sadaqah Routes

## 📋 Migration Checklist

For each route file, follow this checklist:

### 1. **Update Imports**
```typescript
// OLD
import { logger } from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

// NEW
import { islamicLogger } from '../utils/enhancedLogger';
import { sendSuccess, sendError, sendNotFound, sendValidationError } from '../utils/response';
import { asyncHandler, createValidationError } from '../middleware/enhancedErrorHandler';
import { validationSchemas } from '../middleware/validation';
```

### 2. **Update Controller Functions**
```typescript
// OLD
export const controllerFunction = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // logic here
    res.status(200).json({
      status: 'success',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

// NEW
export const controllerFunction = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  // Validation
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    sendValidationError(res, formattedErrors, 'Islamic guidance message here');
    return;
  }

  // Logic here
  islamicLogger.info('Operation started', { userId: req.user?.id });

  // Success response
  sendSuccess(
    res,
    result,
    'Success message with Islamic context',
    200,
    'success' // Islamic context type
  );
});
```

### 3. **Update Route Definitions**
```typescript
// OLD
router.post('/endpoint', [
  body('field').isString().withMessage('Field is required'),
], controller.function);

// NEW
router.post('/endpoint', 
  validationSchemas.category.endpoint,
  controller.function
);
```

### 4. **Add Islamic Context to Responses**
```typescript
// Success responses
sendSuccess(res, data, message, statusCode, islamicContext);

// Islamic context options:
// - 'success' - General success with Alhamdulillah
// - 'healing' - For healing-related operations
// - 'guidance' - For guidance and recommendations
// - 'protection' - For emergency and protection features
```

### 5. **Update Error Handling**
```typescript
// OLD
if (error) {
  throw new AppError(error.message, 400);
}

// NEW
if (error) {
  islamicLogger.error('Operation failed', { 
    userId: req.user?.id, 
    error: error.message 
  });
  
  sendError(
    res,
    'ERROR_CODE',
    'User-friendly message',
    400,
    { details: error.message },
    'Islamic guidance message'
  );
  return;
}
```

## 🎯 Specific Migration Examples

### Example 1: Auth Controller Migration

```typescript
// Before
export const login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email, password } = req.body;
    // ... auth logic
    res.status(200).json({
      status: 'success',
      data: { user, token }
    });
  } catch (error) {
    next(error);
  }
};

// After
export const login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please provide valid credentials to continue your spiritual journey.'
    );
    return;
  }

  const { email, password } = req.body;
  
  islamicLogger.auth('Login attempt', { email });
  
  // ... auth logic
  
  islamicLogger.auth('Login successful', { userId: user.id, email });
  
  sendSuccess(
    res,
    { user, token },
    'Welcome back! May Allah bless your continued journey.',
    200,
    'auth'
  );
});
```

### Example 2: Journey Controller Migration

```typescript
// Before
export const createJourney = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const journey = await createUserJourney(req.body);
    res.status(201).json({
      status: 'success',
      data: { journey }
    });
  } catch (error) {
    next(error);
  }
};

// After
export const createJourney = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please provide complete journey information to begin your healing path.'
    );
    return;
  }

  const userId = req.user?.id;
  
  islamicLogger.journey('Journey creation started', { 
    userId, 
    journeyType: req.body.type 
  });
  
  try {
    const journey = await createUserJourney(req.body);
    
    islamicLogger.journey('Journey created successfully', { 
      userId, 
      journeyId: journey.id 
    });
    
    sendSuccess(
      res,
      { journey },
      'Your healing journey has been created successfully. May Allah guide you on this blessed path.',
      201,
      'guidance'
    );
  } catch (error) {
    islamicLogger.error('Journey creation failed', {
      userId,
      error: (error as Error).message
    });
    
    sendError(
      res,
      'JOURNEY_CREATION_FAILED',
      'Failed to create your healing journey',
      500,
      undefined,
      'We encountered an issue creating your journey. Please try again, and trust in Allah\'s timing.'
    );
  }
});
```

## 🔧 Validation Schema Creation

For each route category, create validation schemas in `middleware/validation.ts`:

```typescript
export const validationSchemas = {
  // ... existing schemas

  journey: {
    create: [
      body('type').isIn(['comprehensive', 'focused', 'crisis', 'maintenance'])
        .withMessage('Journey type must be valid'),
      body('primaryLayer').custom(islamicValidators.islamicLayer)
        .withMessage('Primary layer must be valid Islamic layer'),
      body('duration').custom(islamicValidators.duration)
        .withMessage('Duration must be valid'),
      handleValidationErrors
    ],
    
    update: [
      commonValidations.uuidParam('journeyId'),
      body('progress').optional().isObject(),
      body('notes').optional().isString().trim(),
      handleValidationErrors
    ]
  },

  // Add more schemas as needed
};
```

## 📊 Migration Priority

### High Priority (Complete First)
1. **Auth Routes** - Critical for all other functionality
2. **Emergency Routes** - Safety-critical features
3. **Journey Routes** - Core healing functionality
4. **Assessment Routes** - Entry point for users

### Medium Priority
1. **Content Routes** - Islamic content delivery
2. **Community Routes** - User interaction features
3. **Analytics Routes** - Monitoring and insights

### Low Priority
1. **Dashboard Routes** - Administrative features
2. **Journal Routes** - Personal reflection features
3. **Sadaqah Routes** - Additional features

## 🧪 Testing Migration

After migrating each route:

1. **Test existing functionality** - Ensure backward compatibility
2. **Test new validation** - Verify enhanced validation works
3. **Test error responses** - Check Islamic context in errors
4. **Test success responses** - Verify new response format
5. **Test logging** - Ensure proper Islamic context logging

## 📝 Migration Commands

```bash
# 1. Backup original file
cp src/routes/example.routes.ts src/routes/example.routes.backup.ts

# 2. Create enhanced version
cp src/routes/example.routes.ts src/routes/example-enhanced.routes.ts

# 3. Update the enhanced version with new patterns

# 4. Test the enhanced version

# 5. Replace original when ready
mv src/routes/example-enhanced.routes.ts src/routes/example.routes.ts
```

## 🔄 Rollback Plan

If issues arise during migration:

1. **Immediate rollback**: Restore from backup files
2. **Partial rollback**: Use feature flags to disable enhanced features
3. **Gradual migration**: Migrate one endpoint at a time

## 📞 Support

For migration assistance:
- Check existing enhanced examples (symptoms routes)
- Review the enhanced response utilities
- Test with the health check endpoints
- Use the Islamic logger for debugging

---

**May Allah bless this migration and make it beneficial for the Muslim Ummah. Ameen.**

*Last updated: January 2025*