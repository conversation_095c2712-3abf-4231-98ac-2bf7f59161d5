# Database Setup & Environment Management

> **Best Practice:** Use separate `.env`, `.env.example`, and `.env.production` files for different environments. Never commit secrets or production credentials to version control.

## Local Development
1. Ensure PostgreSQL is installed and running locally.
2. Set up a `.env` file with your local database credentials:
   ```
   DATABASE_URL="postgresql://user:password@localhost:5432/qalb_healing"
   ```
3. Run `npx prisma migrate dev` to apply migrations.
4. Run `npx ts-node prisma/seed.ts` to seed initial data.

## Development/Production
- Use managed PostgreSQL (e.g., Supabase, AWS RDS, Azure, etc.).
- Set `DATABASE_URL` in environment variables for each environment.
- Use `prisma migrate deploy` for production migrations.
- Always backup the database before applying migrations in production.

## Environment Variables
- `.env.example` is provided. Copy to `.env` and fill in credentials.

## Backup & Migration Safety
- Use `pg_dump` or managed service backups before migrations.
- Review migration SQL with `prisma migrate diff` before applying.

---
*Update this file as your setup evolves.*
