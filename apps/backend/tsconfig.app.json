{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node", "express"], "rootDir": "src", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "sourceMap": true, "inlineSources": true, "sourceRoot": "/"}, "include": ["src/**/*.ts"], "exclude": ["out-tsc", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"]}