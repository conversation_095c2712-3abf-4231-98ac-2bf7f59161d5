{"item": [{"name": "auth", "description": "", "item": [{"name": "register", "description": "", "item": [{"id": "a1529e63-5cad-4580-bcaf-4279e7908bbe", "name": "Register a new user", "request": {"name": "Register a new user", "description": {}, "url": {"path": ["auth", "register"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "auth": null}, "response": [{"id": "802eae14-c2eb-4cb3-8cf4-6578a414c53a", "name": "The user has been successfully created.", "originalRequest": {"url": {"path": ["auth", "register"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}}, "status": "Created", "code": 201, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"<number>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"createdAt\": \"<dateTime>\",\n  \"updatedAt\": \"<dateTime>\"\n}", "cookie": [], "_postman_previewlanguage": "json"}], "event": [], "protocolProfileBehavior": {"disableBodyPruning": true}}]}, {"name": "login", "description": "", "item": [{"id": "695c54ff-14b9-4005-8004-c7be8b7f8a07", "name": "Login a user", "request": {"name": "Login a user", "description": {}, "url": {"path": ["auth", "login"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "auth": null}, "response": [{"id": "f958de80-70c3-403a-afe4-b353a37fd617", "name": "The user has been successfully logged in.", "originalRequest": {"url": {"path": ["auth", "login"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"access_token\": \"<string>\"\n}", "cookie": [], "_postman_previewlanguage": "json"}], "event": [], "protocolProfileBehavior": {"disableBodyPruning": true}}]}]}, {"name": "users", "description": "", "item": [{"id": "38a15eff-e28d-430c-8b8c-c9911e5d1147", "name": "Get all users", "request": {"name": "Get all users", "description": {}, "url": {"path": ["users"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Accept", "value": "application/json"}], "method": "GET", "body": {}, "auth": null}, "response": [{"id": "4be9dda8-8260-42b4-a200-b94ff1f87cf6", "originalRequest": {"url": {"path": ["users"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Accept", "value": "application/json"}], "method": "GET", "body": {}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "[\n  {\n    \"id\": \"<number>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"createdAt\": \"<dateTime>\",\n    \"updatedAt\": \"<dateTime>\"\n  },\n  {\n    \"id\": \"<number>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"createdAt\": \"<dateTime>\",\n    \"updatedAt\": \"<dateTime>\"\n  }\n]", "cookie": [], "_postman_previewlanguage": "json"}], "event": [], "protocolProfileBehavior": {"disableBodyPruning": true}}, {"name": "{id}", "description": "", "item": [{"id": "3921172c-ee8c-455f-9848-a0f488a2ac26", "name": "Get a user by ID", "request": {"name": "Get a user by ID", "description": {}, "url": {"path": ["users", ":id"], "host": ["{{baseUrl}}"], "query": [], "variable": [{"type": "any", "value": "<string>", "key": "id", "disabled": false, "description": {"content": "(Required) ", "type": "text/plain"}}]}, "header": [{"key": "Accept", "value": "application/json"}], "method": "GET", "body": {}, "auth": null}, "response": [{"id": "0d55e52e-c565-4032-a0ee-b6bbbdc3ac77", "originalRequest": {"url": {"path": ["users", ":id"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Accept", "value": "application/json"}], "method": "GET", "body": {}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"id\": \"<number>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"createdAt\": \"<dateTime>\",\n  \"updatedAt\": \"<dateTime>\"\n}", "cookie": [], "_postman_previewlanguage": "json"}], "event": [], "protocolProfileBehavior": {"disableBodyPruning": true}}]}]}], "event": [], "variable": [{"key": "baseUrl", "value": "/"}], "info": {"_postman_id": "8653d4d8-f3f7-4aea-852e-4371513da276", "name": "NestJS API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": {"content": "The NestJS API description", "type": "text/plain"}}}