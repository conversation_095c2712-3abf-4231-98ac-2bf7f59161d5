{"openapi": "3.0.0", "info": {"title": "NestJS API", "description": "The NestJS API description", "version": "1.0"}, "tags": [{"name": "auth", "description": ""}, {"name": "users", "description": ""}], "servers": [], "components": {"schemas": {"CreateUserDto": {"type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}, "required": ["email", "password"]}, "User": {"type": "object", "properties": {"id": {"type": "number"}, "email": {"type": "string"}, "password": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "email", "password", "createdAt", "updatedAt"]}, "LoginUserDto": {"type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}, "required": ["email", "password"]}}}, "paths": {"/auth/register": {"post": {"operationId": "AuthController_register", "summary": "Register a new user", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "The user has been successfully created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "tags": ["auth"]}}, "/auth/login": {"post": {"operationId": "AuthController_login", "summary": "Login a user", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginUserDto"}}}}, "responses": {"200": {"description": "The user has been successfully logged in.", "content": {"application/json": {"schema": {"type": "object", "properties": {"access_token": {"type": "string"}}}}}}}, "tags": ["auth"]}}, "/users": {"get": {"operationId": "UsersController_findAll", "summary": "Get all users", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}, "/users/{id}": {"get": {"operationId": "UsersController_findOne", "summary": "Get a user by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "tags": ["users"], "security": [{"bearer": []}]}}}}