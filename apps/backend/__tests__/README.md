# Qalb Healing Backend Test Suite

## 📋 Overview

This comprehensive test suite covers the Qalb Healing backend with **high-quality, maintainable tests** following industry best practices. The tests are organized by component type and use Jest + Supertest for thorough coverage.

## 🏗️ Test Architecture

### Test Organization
```
tests/
├── setup.ts                    # Global test configuration & mocks
├── app.spec.ts                 # E2E smoke tests for main app
├── controllers/                # Integration tests via HTTP
│   ├── auth.controller.spec.ts
│   └── assessment.controller.spec.ts
├── middleware/                 # Unit tests with req/res/next mocks
│   ├── auth.middleware.spec.ts
│   ├── errorHandler.middleware.spec.ts
│   └── security.middleware.spec.ts
└── services/                   # Unit tests with mocked dependencies
    └── assessment.service.spec.ts
```

### Testing Strategy by Component

| Component Type | Test Method | Approach | File Size Limit |
|---------------|-------------|----------|-----------------|
| **Controllers** | Integration | HTTP via `supertest` | ≤ 300 LOC |
| **Services** | Unit | Function calls + mocks | ≤ 300 LOC |
| **Middleware** | Unit | req/res/next mocks | ≤ 300 LOC |
| **Main App** | E2E Smoke | HTTP + app bootstrap | ≤ 300 LOC |

## 🧪 Test Coverage

### Controllers (Integration Tests)
- ✅ **Auth Controller** - Authentication endpoints
  - User signup/login flows
  - Profile management
  - Token validation
  - Error handling

- ✅ **Assessment Controller** - Assessment endpoints
  - Session creation/resumption
  - Response submission
  - Crisis detection
  - Diagnosis retrieval

### Middleware (Unit Tests)
- ✅ **Auth Middleware** - Authentication logic
  - Bearer token validation
  - Supabase integration
  - Error scenarios
  - Edge cases

- ✅ **Error Handler** - Error processing
  - AppError class functionality
  - Environment-specific responses
  - Logging behavior
  - Edge cases

- ✅ **Security Middleware** - Security features
  - XSS protection
  - Input sanitization
  - Rate limiting
  - SQL injection prevention

### Services (Unit Tests)
- ✅ **Assessment Service** - Core business logic
  - Session management
  - Response processing
  - AI integration
  - Crisis detection
  - Diagnosis generation

### Application (E2E Smoke Tests)
- ✅ **Main App** - Application-level tests
  - Health checks
  - Route mounting
  - CORS configuration
  - Security headers
  - Error handling
  - Performance

## 🔧 Test Configuration

### Jest Setup
- **Environment**: Node.js
- **Preset**: ts-jest
- **Timeout**: 10 seconds
- **Coverage**: 80% threshold
- **Mocking**: Comprehensive service mocks

### Mock Strategy
- **Supabase**: Complete client mock
- **Redis**: Full client simulation
- **AI Service**: Response mocking
- **Logger**: Silent operation
- **Crisis Detection**: Configurable responses

## 🚀 Running Tests

### All Tests
```bash
npm test
```

### Specific Test Categories
```bash
# Controllers only
npm test -- tests/controllers

# Middleware only
npm test -- tests/middleware

# Services only
npm test -- tests/services

# App smoke tests
npm test -- tests/app.spec.ts
```

### With Coverage
```bash
npm test -- --coverage
```

### Watch Mode
```bash
npm test -- --watch
```

### Specific Test File
```bash
npm test -- tests/controllers/auth.controller.spec.ts
```

## 📊 Test Quality Standards

### ✅ What We Test
- **Happy paths** - Normal operation flows
- **Error scenarios** - Database errors, network issues
- **Edge cases** - Invalid inputs, boundary conditions
- **Security** - XSS, SQL injection, authentication
- **Performance** - Response times, concurrent requests
- **Integration** - Service interactions, HTTP flows

### ❌ What We Don't Test
- External service implementations (Supabase internals)
- Framework core functionality (Express.js)
- Third-party library internals
- Environment-specific configurations

## 🎯 Test Patterns

### Controller Tests (Integration)
```typescript
describe('POST /api/auth/login', () => {
  it('should login user successfully', async () => {
    const response = await request(app)
      .post('/api/auth/login')
      .send(validLoginData);
    
    expect(response.status).toBe(200);
    expect(response.body.data.user).toBeDefined();
  });
});
```

### Service Tests (Unit)
```typescript
describe('AssessmentService.startAssessment', () => {
  it('should create new session', async () => {
    const result = await assessmentService.startAssessment(userId, profile);
    
    expect(result.session.id).toMatch(/^assess_\d+_[a-z0-9]+$/);
    expect(mockSupabase.from).toHaveBeenCalledWith('assessment_sessions');
  });
});
```

### Middleware Tests (Unit)
```typescript
describe('authMiddleware', () => {
  it('should authenticate valid token', async () => {
    mockRequest.headers = { authorization: 'Bearer valid-token' };
    
    await authMiddleware(mockRequest, mockResponse, mockNext);
    
    expect(mockRequest.user).toBeDefined();
    expect(mockNext).toHaveBeenCalledWith();
  });
});
```

## 🔍 Debugging Tests

### Common Issues
1. **Mock not working**: Check mock setup in `setup.ts`
2. **Async issues**: Ensure proper `await` usage
3. **Database errors**: Verify Supabase mock responses
4. **Timeout errors**: Increase Jest timeout if needed

### Debug Commands
```bash
# Run with verbose output
npm test -- --verbose

# Run single test with debugging
npm test -- --testNamePattern="should login user" --verbose

# Check test coverage
npm test -- --coverage --coverageReporters=text-lcov
```

## 📈 Continuous Improvement

### Adding New Tests
1. Follow existing patterns
2. Keep files under 300 lines
3. Use descriptive test names
4. Cover happy path + errors + edge cases
5. Mock external dependencies

### Test Maintenance
- Update mocks when APIs change
- Refactor tests when code changes
- Monitor coverage reports
- Remove obsolete tests

## 🏆 Quality Metrics

- **Test Coverage**: >80% lines/functions/branches
- **Test Speed**: <10 seconds total runtime
- **Test Reliability**: 0% flaky tests
- **Test Maintainability**: Clear, readable test code

---

**Note**: This test suite is designed to be comprehensive yet maintainable, following the principle that good tests are documentation of how the system should behave.
