/**
 * Jest Test Setup for Qalb Healing Backend
 * Global test configuration and mocks
 */

import { jest } from '@jest/globals';
import { randomUUID } from 'crypto';

// Logger mock handled by moduleNameMapper in jest.config.js

// Polyfill crypto for tests
global.crypto = {
  randomUUID: randomUUID,
} as any;

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.SUPABASE_URL = 'https://test.supabase.co';
process.env.SUPABASE_ANON_KEY = 'test-anon-key';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.FRONTEND_URL = 'http://localhost:3000';

// Create a proper mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
    // Add missing methods that are used in tests
    mockResolvedValue: jest.fn().mockReturnThis(),
    mockResolvedValueOnce: jest.fn().mockReturnThis(),
    mockRejectedValue: jest.fn().mockReturnThis(),
    mockRejectedValueOnce: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
        'mockResolvedValue',
        'mockResolvedValueOnce',
        'mockRejectedValue',
        'mockRejectedValueOnce',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  // Add mock methods to the terminal methods for chaining
  mockBuilder.single.mockResolvedValue = jest
    .fn()
    .mockReturnValue(mockBuilder.single);
  mockBuilder.single.mockResolvedValueOnce = jest
    .fn()
    .mockReturnValue(mockBuilder.single);
  mockBuilder.single.mockRejectedValue = jest
    .fn()
    .mockReturnValue(mockBuilder.single);
  mockBuilder.single.mockRejectedValueOnce = jest
    .fn()
    .mockReturnValue(mockBuilder.single);

  return mockBuilder;
};

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    signUp: jest.fn(),
    signInWithPassword: jest.fn(),
    getUser: jest.fn(),
    signOut: jest.fn(),
  },
  from: jest.fn(() => createMockQueryBuilder()),
  rpc: jest.fn(),
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn(),
      download: jest.fn(),
      remove: jest.fn(),
      list: jest.fn(),
      getPublicUrl: jest.fn(),
      createSignedUrl: jest.fn(),
    })),
  },
};

// Mock Supabase config
jest.mock('../src/config/supabase', () => ({
  getSupabase: jest.fn(() => mockSupabaseClient),
  setupSupabase: jest.fn(() => mockSupabaseClient),
}));

// Mock Redis client
const mockRedisClient = {
  get: jest.fn(),
  set: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  expire: jest.fn(),
  ttl: jest.fn(),
  keys: jest.fn(),
  flushall: jest.fn(),
  quit: jest.fn(),
  on: jest.fn(),
  call: jest.fn(),
  eval: jest.fn(),
  evalsha: jest.fn(),
  script: jest.fn(() => ({
    load: jest.fn().mockResolvedValue('mock-sha'),
  })),
  multi: jest.fn(() => ({
    exec: jest.fn().mockResolvedValue([]),
  })),
};

jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => mockRedisClient);
});

// Mock rate-limit-redis to prevent Redis errors
jest.mock('rate-limit-redis', () => {
  class MockRedisStore {
    constructor() {}
    increment = jest
      .fn()
      .mockResolvedValue({ totalHits: 1, timeToExpire: 60000 });
    decrement = jest.fn().mockResolvedValue({ totalHits: 0 });
    resetKey = jest.fn().mockResolvedValue();
  }

  return {
    __esModule: true,
    default: MockRedisStore,
    RedisStore: MockRedisStore,
  };
});

// Mock express-rate-limit to prevent rate limiting in tests
jest.mock('express-rate-limit', () => {
  const mockRateLimit = jest.fn(
    () => (req: any, res: any, next: any) => next()
  );
  return {
    __esModule: true,
    default: mockRateLimit,
    rateLimit: mockRateLimit,
  };
});

// Mock logger
// Logger mock moved to top of file

// Mock AI service
const mockAiService = {
  analyzeAssessmentResponses: jest.fn(),
  generateJourneyParameters: jest.fn(),
  generateJourneyContent: jest.fn(),
  generatePersonalizedContent: jest.fn(),
  detectCrisisIndicators: jest.fn(),
  analyzeSpiritualLandscape: jest.fn(),
  generateAdaptiveRecommendations: jest.fn(),
  analyzeCrisisIndicators: jest.fn(),
  matchCommunitySupport: jest.fn(),
};

// Mock AI Service class
class MockAIService {
  analyzeAssessmentResponses = mockAiService.analyzeAssessmentResponses;
  generateJourneyParameters = mockAiService.generateJourneyParameters;
  generateJourneyContent = mockAiService.generateJourneyContent;
  generatePersonalizedContent = mockAiService.generatePersonalizedContent;
  detectCrisisIndicators = mockAiService.detectCrisisIndicators;
  analyzeSpiritualLandscape = mockAiService.analyzeSpiritualLandscape;
  generateAdaptiveRecommendations =
    mockAiService.generateAdaptiveRecommendations;
  analyzeCrisisIndicators = mockAiService.analyzeCrisisIndicators;
  matchCommunitySupport = mockAiService.matchCommunitySupport;
}

jest.mock('../src/services/ai.service', () => ({
  AIService: MockAIService,
  aiService: mockAiService,
}));

// Mock N8N service
const mockN8nService = {
  triggerWorkflow: jest.fn(),
};

global.mockN8nService = mockN8nService;

jest.mock('../src/services/n8n.service', () => ({
  N8nService: jest.fn().mockImplementation(() => mockN8nService),
  n8nService: mockN8nService,
}));

// Mock crisis detection service
const mockCrisisDetectionService = {
  analyzeResponse: jest.fn(),
  checkForCrisisIndicators: jest.fn(),
  escalateCrisis: jest.fn(),
  getCrisisResources: jest.fn(),
};

// Mock Crisis Detection Service class
class MockCrisisDetectionService {
  analyzeResponse = mockCrisisDetectionService.analyzeResponse;
  checkForCrisisIndicators =
    mockCrisisDetectionService.checkForCrisisIndicators;
  escalateCrisis = mockCrisisDetectionService.escalateCrisis;
  getCrisisResources = mockCrisisDetectionService.getCrisisResources;
}

jest.mock('../src/services/crisis-detection.service', () => ({
  CrisisDetectionService: MockCrisisDetectionService,
  crisisDetectionService: mockCrisisDetectionService,
}));

// Import the mock logger
const mockLogger = require('./__mocks__/logger.ts').default;

// Global test utilities
global.mockSupabaseClient = mockSupabaseClient;
global.mockRedisClient = mockRedisClient;
global.mockLogger = mockLogger;
global.mockAiService = mockAiService;
global.mockN8nService = mockN8nService;
global.mockCrisisDetectionService = mockCrisisDetectionService;

// Test user data
global.testUser = {
  id: 'test-user-id-123',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00.000Z',
  updated_at: '2024-01-01T00:00:00.000Z',
  email_confirmed_at: '2024-01-01T00:00:00.000Z',
  phone_confirmed_at: null,
  confirmation_sent_at: null,
  recovery_sent_at: null,
  email_change_sent_at: null,
  new_email: null,
  invited_at: null,
  action_link: null,
  email_change: null,
  email_change_confirm_status: 0,
  banned_until: null,
  aud: 'authenticated',
  role: 'authenticated',
};

global.testSession = {
  access_token: 'test-access-token',
  refresh_token: 'test-refresh-token',
  expires_in: 3600,
  expires_at: Date.now() + 3600000,
  token_type: 'bearer',
  user: global.testUser,
};

global.testProfile = {
  user_id: 'test-user-id-123',
  email: '<EMAIL>',
  selected_layers: ['jism', 'nafs', 'qalb'],
  journey_type: 'comprehensive',
  created_at: '2024-01-01T00:00:00.000Z',
  updated_at: '2024-01-01T00:00:00.000Z',
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();

  // Ensure Supabase mock is properly set up
  const { getSupabase } = require('../src/config/supabase');
  getSupabase.mockReturnValue(mockSupabaseClient);

  // Reset Supabase mock responses
  mockSupabaseClient.auth.signUp.mockResolvedValue({
    data: { user: global.testUser, session: global.testSession },
    error: null,
  });

  mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
    data: { user: global.testUser, session: global.testSession },
    error: null,
  });

  mockSupabaseClient.auth.getUser.mockResolvedValue({
    data: { user: global.testUser },
    error: null,
  });

  // Reset query builder mocks
  mockSupabaseClient.from.mockImplementation(() => createMockQueryBuilder());

  // Set default responses for common queries
  const defaultBuilder = createMockQueryBuilder();
  defaultBuilder.single.mockResolvedValue({
    data: global.testProfile,
    error: null,
  });

  mockSupabaseClient.from.mockReturnValue(defaultBuilder);

  // Reset Redis mock responses
  mockRedisClient.get.mockResolvedValue(null);
  mockRedisClient.set.mockResolvedValue('OK');
  mockRedisClient.setex.mockResolvedValue('OK');
  mockRedisClient.del.mockResolvedValue(1);

  // Reset AI service mock responses
  mockAiService.analyzeAssessmentResponses.mockResolvedValue({
    layerAnalysis: {},
    overallSeverity: 'moderate',
    recommendedJourneyType: 'comprehensive',
    confidence: 0.85,
  });

  mockAiService.generateJourneyParameters.mockResolvedValue({
    duration: 21,
    timeCommitment: 30,
    primaryLayer: 'nafs',
    secondaryLayers: ['qalb'],
    ruqyaLevel: 'intermediate',
    communitySupport: true,
    type: 'comprehensive',
  });

  mockAiService.generateJourneyContent.mockResolvedValue({
    title: 'Healing the Nafs: A 21-Day Journey',
    description: 'A comprehensive journey to heal your nafs',
    personalizedWelcome: 'Welcome to your personalized healing journey...',
    days: Array.from({ length: 21 }, (_, i) => ({
      day: i + 1,
      title: `Day ${i + 1}: Foundation`,
      practices: [],
      content: [],
    })),
  });

  mockAiService.analyzeSpiritualLandscape.mockResolvedValue({
    personalizedMessage: 'Your spiritual analysis is complete',
    islamicInsights: ['Focus on dhikr for peace'],
    educationalContent: ['Learn about the 5 layers'],
    crisisLevel: 'none',
    crisisIndicators: [],
    immediateActions: [],
    nextSteps: ['Begin your healing journey'],
    recommendedJourneyType: 'comprehensive',
    estimatedHealingDuration: 21,
    confidence: 0.85,
  });

  mockAiService.generateAdaptiveRecommendations.mockResolvedValue([]);
  mockAiService.analyzeCrisisIndicators.mockResolvedValue({ level: 'none' });
  mockAiService.matchCommunitySupport.mockResolvedValue({ groupId: null });

  // Reset crisis detection mock responses
  mockCrisisDetectionService.analyzeResponse.mockResolvedValue({
    isCrisis: false,
    severity: 'low',
    indicators: [],
    confidence: 0.8,
    riskLevel: 'normal',
    recommendations: ['self_care'],
    emergencyProtocol: false,
  });

  mockCrisisDetectionService.escalateCrisis.mockResolvedValue({
    escalationId: 'crisis_123_abc',
    severity: 'high',
    actions: ['emergency_contacts_notified'],
    resources: ['suicide_hotline'],
    followUpRequired: true,
    emergencyProtocol: true,
  });

  mockCrisisDetectionService.checkForCrisisIndicators.mockReturnValue({
    hasCrisisKeywords: false,
    indicators: [],
  });

  mockCrisisDetectionService.getCrisisResources.mockReturnValue({
    hotlines: [
      {
        name: 'National Suicide Prevention Lifeline',
        phone: '988',
        available: '24/7',
      },
    ],
    islamicCounselors: [
      {
        name: 'Islamic Counseling Center',
        specialization: 'Islamic Mental Health',
        contact: '<EMAIL>',
      },
    ],
    emergencyPrayers: [
      {
        title: 'Dua for Relief',
        arabic: 'اللهم اكشف عني البلاء',
        transliteration: 'Allahumma ikshif anni al-balaa',
        translation: 'O Allah, remove this trial from me',
      },
    ],
    selfCareSteps: ['Take deep breaths', 'Seek support'],
  });

  // Reset N8N service mock responses
  mockN8nService.triggerWorkflow.mockResolvedValue({
    success: true,
    data: {},
  });
});

// Global test timeout - increased for complex operations
jest.setTimeout(30000);

// Suppress console logs during tests unless explicitly needed
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

export {};
