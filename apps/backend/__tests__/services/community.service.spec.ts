import { communityService } from '../../src/services/community.service';
import { AppError } from '../../src/middleware/errorHandler';

// Mock the prisma client
jest.mock('../../src/config/database', () => ({
  prisma: {
    heartCircle: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    circleMember: {
      findUnique: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
    circleMessage: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
    messageReaction: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    communityActivity: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
    duaRequest: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

// Mock logger to prevent console output during tests
jest.mock('../../src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Import the mocked prisma
import { prisma } from '../../src/config/database';
const mockedPrisma = prisma as jest.Mocked<typeof prisma>;

describe('CommunityService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('requestDua', () => {
    it('should create a new Du\'a request', async () => {
      const mockDuaRequest = {
        id: 'dua1',
        userId: 'user123',
        requestText: 'Please pray for my health',
        status: 'pending',
        createdAt: new Date(),
        completedAt: null,
      };
      mockedPrisma.duaRequest.create.mockResolvedValue(mockDuaRequest);

      const result = await communityService.requestDua(
        'user123',
        'Please pray for my health'
      );

      expect(mockedPrisma.duaRequest.create).toHaveBeenCalledWith({
        data: {
          userId: 'user123',
          requestText: 'Please pray for my health',
          status: 'pending',
        },
      });
      expect(result).toEqual(mockDuaRequest);
    });

    it('should throw AppError if Du\'a request creation fails', async () => {
      mockedPrisma.duaRequest.create.mockRejectedValue(new Error('DB error'));

      await expect(
        communityService.requestDua('user123', 'Please pray for my health')
      ).rejects.toThrow(AppError);
    });
  });

  describe('getPendingDuaRequests', () => {
    it('should return a list of pending Du\'a requests', async () => {
      const mockPendingRequests = [
        { id: 'dua1', requestText: 'Request 1', userId: 'user1', status: 'pending', createdAt: new Date(), completedAt: null },
        { id: 'dua2', requestText: 'Request 2', userId: 'user2', status: 'pending', createdAt: new Date(), completedAt: null },
      ];
      mockedPrisma.duaRequest.findMany.mockResolvedValue(mockPendingRequests);

      const result = await communityService.getPendingDuaRequests();

      expect(mockedPrisma.duaRequest.findMany).toHaveBeenCalledWith({
        where: { status: 'pending' },
        orderBy: { createdAt: 'desc' },
      });
      expect(result).toEqual(mockPendingRequests);
    });

    it('should throw AppError if retrieving pending Du\'a requests fails', async () => {
      mockedPrisma.duaRequest.findMany.mockRejectedValue(new Error('DB error'));

      await expect(communityService.getPendingDuaRequests()).rejects.toThrow(
        AppError
      );
    });
  });

  describe('completeDuaRequest', () => {
    it('should mark a Du\'a request as completed', async () => {
      const mockDuaRequest = {
        id: 'dua1',
        userId: 'user123',
        requestText: 'Please pray for my health',
        status: 'pending',
        createdAt: new Date(),
        completedAt: null,
      };
      const mockUpdatedDuaRequest = {
        ...mockDuaRequest,
        status: 'completed',
        completedAt: new Date(),
      };
      mockedPrisma.duaRequest.findUnique.mockResolvedValue(mockDuaRequest);
      mockedPrisma.duaRequest.update.mockResolvedValue(mockUpdatedDuaRequest);
      mockedPrisma.communityActivity.create.mockResolvedValue({} as any);

      const result = await communityService.completeDuaRequest(
        'dua1',
        'completer123'
      );

      expect(mockedPrisma.duaRequest.findUnique).toHaveBeenCalledWith({
        where: { id: 'dua1' },
      });
      expect(mockedPrisma.duaRequest.update).toHaveBeenCalledWith({
        where: { id: 'dua1' },
        data: {
          status: 'completed',
          completedAt: expect.any(Date),
        },
      });
      expect(result).toEqual(mockUpdatedDuaRequest);
    });

    it('should throw AppError if Du\'a request not found', async () => {
      mockedPrisma.duaRequest.findUnique.mockResolvedValue(null);

      await expect(
        communityService.completeDuaRequest('nonexistent', 'completer123')
      ).rejects.toThrow(AppError);
    });

    it('should throw AppError if Du\'a request is already completed', async () => {
      const mockDuaRequest = {
        id: 'dua1',
        userId: 'user123',
        requestText: 'Please pray for my health',
        status: 'completed',
        createdAt: new Date(),
        completedAt: new Date(),
      };
      mockedPrisma.duaRequest.findUnique.mockResolvedValue(mockDuaRequest);

      await expect(
        communityService.completeDuaRequest('dua1', 'completer123')
      ).rejects.toThrow(AppError);
    });

    it('should throw AppError if completing Du\'a request fails', async () => {
      const mockDuaRequest = {
        id: 'dua1',
        userId: 'user123',
        requestText: 'Please pray for my health',
        status: 'pending',
        createdAt: new Date(),
        completedAt: null,
      };
      mockedPrisma.duaRequest.findUnique.mockResolvedValue(mockDuaRequest);
      mockedPrisma.duaRequest.update.mockRejectedValue(new Error('DB error'));

      await expect(
        communityService.completeDuaRequest('dua1', 'completer123')
      ).rejects.toThrow(AppError);
    });
  });

  describe('matchPeerSupporter', () => {
    it('should return a mock peer supporter ID', async () => {
      const result = await communityService.matchPeerSupporter('user123');
      expect(result).toBe('mock_peer_supporter_123');
    });

    // TODO: Add a test case for error handling once matchPeerSupporter has actual logic that can fail.
    // The current implementation always returns a mock ID or throws a generic AppError from its catch block.
    // To properly test the catch block, an internal dependency of matchPeerSupporter would need to be mocked to fail.
  });
});
