/**
 * Journey Service Unit Tests
 * Tests journey service with mocked dependencies
 */

import { JourneyService } from '../../src/services/journey.service';
import { getSupabase } from '../../src/config/supabase';

// Mock dependencies
jest.mock('../../src/config/supabase');

// Create a proper mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Journey Service Unit Tests', () => {
  let journeyService: JourneyService;
  let mockSupabase: any;

  beforeEach(() => {
    journeyService = new JourneyService();
    mockSupabase = global.mockSupabaseClient;
    (getSupabase as jest.Mock).mockReturnValue(mockSupabase);

    // Mock crypto.randomUUID
    global.crypto = {
      randomUUID: jest.fn(() => 'test-uuid-123'),
    } as any;
  });

  describe('createPersonalizedJourney', () => {
    const userId = 'test-user-123';
    const assessmentId = 'assessment-456';
    const mockAssessment = {
      id: assessmentId,
      user_id: userId,
      diagnosis_data: {
        overallSeverity: 'moderate',
        primaryLayer: 'nafs',
        affectedLayers: ['nafs', 'qalb'],
        crisisLevel: 'none',
      },
    };
    const mockUserProfile = {
      awarenessLevel: 'intermediate',
      ruqyaFamiliarity: 'basic',
      profession: 'teacher',
      culturalBackground: 'arab',
      timeAvailability: 'moderate',
      learningStyle: 'visual',
    };

    beforeEach(() => {
      jest.clearAllMocks();

      // Create fresh mock query builders for each test
      const assessmentBuilder = createMockQueryBuilder();
      const userProfileBuilder = createMockQueryBuilder();
      const insertBuilder = createMockQueryBuilder();

      // Mock assessment retrieval
      assessmentBuilder.single.mockResolvedValue({
        data: mockAssessment,
        error: null,
      });

      // Mock user profile retrieval
      userProfileBuilder.single.mockResolvedValue({
        data: mockUserProfile,
        error: null,
      });

      // Mock database insertion
      insertBuilder.single.mockResolvedValue({
        data: {
          id: 'test-uuid-123',
          user_id: userId,
          assessment_id: assessmentId,
          type: 'comprehensive',
          status: 'created',
          title: 'Healing the Nafs: A 21-Day Journey',
          current_day: 1,
          completed_days: [],
          total_progress: 0,
          configuration: {
            duration: 21,
            dailyTimeCommitment: 30,
            primaryLayer: 'nafs',
            secondaryLayers: ['qalb'],
            ruqyaIntegrationLevel: 'intermediate',
            communityIntegration: true,
            crisisSupport: false,
          },
          days: Array.from({ length: 21 }, (_, i) => ({
            day: i + 1,
            title: `Day ${i + 1}: Foundation`,
            practices: [],
            content: [],
          })),
        },
        error: null,
      });

      // Setup mockSupabase.from to return appropriate builders
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'spiritual_diagnoses') return assessmentBuilder;
        if (table === 'user_profiles') return userProfileBuilder;
        if (table === 'journeys') return insertBuilder;
        return createMockQueryBuilder();
      });

      // Mock AI service responses
      global.mockAiService.generateJourneyParameters.mockResolvedValue({
        duration: 21,
        timeCommitment: 30,
        primaryLayer: 'nafs',
        secondaryLayers: ['qalb'],
        ruqyaLevel: 'intermediate',
        communitySupport: true,
        culturalAdaptations: ['arabic_content'],
        type: 'comprehensive',
      });

      global.mockAiService.generateJourneyContent.mockResolvedValue({
        title: 'Healing the Nafs: A 21-Day Journey',
        description:
          'A comprehensive journey to heal your nafs through Islamic practices',
        personalizedWelcome: 'Welcome to your personalized healing journey...',
        days: Array.from({ length: 21 }, (_, i) => ({
          day: i + 1,
          title: `Day ${i + 1}: Foundation`,
          practices: [],
          content: [],
        })),
      });
    });

    it('should create personalized journey successfully', async () => {
      const result = await journeyService.createPersonalizedJourney(
        userId,
        assessmentId
      );

      expect(result).toMatchObject({
        id: 'test-uuid-123',
        user_id: userId,
        assessment_id: assessmentId,
        type: 'comprehensive',
        status: 'created',
        title: 'Healing the Nafs: A 21-Day Journey',
        current_day: 1,
        completed_days: [],
        total_progress: 0,
      });

      expect(result.configuration).toMatchObject({
        duration: 21,
        dailyTimeCommitment: 30,
        primaryLayer: 'nafs',
        secondaryLayers: ['qalb'],
        ruqyaIntegrationLevel: 'intermediate',
        communityIntegration: true,
        crisisSupport: false,
      });

      expect(result.days).toHaveLength(21);
      expect(
        global.mockAiService.generateJourneyParameters
      ).toHaveBeenCalledWith({
        assessment: mockAssessment.diagnosis_data,
        userProfile: mockUserProfile,
        preferences: {},
      });
    });

    it('should apply user preferences to journey configuration', async () => {
      const preferences = {
        duration: 14,
        dailyTimeCommitment: 20,
        ruqyaIntegrationLevel: 'advanced',
        communityIntegration: false,
      };

      await journeyService.createPersonalizedJourney(
        userId,
        assessmentId,
        preferences
      );

      expect(
        global.mockAiService.generateJourneyParameters
      ).toHaveBeenCalledWith({
        assessment: mockAssessment.diagnosis_data,
        userProfile: mockUserProfile,
        preferences,
      });
    });

    it('should enable crisis support for high-risk assessments', async () => {
      const crisisAssessment = {
        ...mockAssessment,
        diagnosis_data: {
          ...mockAssessment.diagnosis_data,
          crisisLevel: 'high',
        },
      };

      const crisisAssessmentBuilder = createMockQueryBuilder();
      const crisisUserProfileBuilder = createMockQueryBuilder();
      const crisisInsertBuilder = createMockQueryBuilder();

      crisisAssessmentBuilder.single.mockResolvedValue({
        data: crisisAssessment,
        error: null,
      });

      crisisUserProfileBuilder.single.mockResolvedValue({
        data: mockUserProfile,
        error: null,
      });

      crisisInsertBuilder.single.mockResolvedValue({
        data: {
          id: 'test-uuid-123',
          user_id: userId,
          assessment_id: assessmentId,
          type: 'comprehensive',
          status: 'created',
          title: 'Healing the Nafs: A 21-Day Journey',
          current_day: 1,
          completed_days: [],
          total_progress: 0,
          configuration: {
            duration: 21,
            dailyTimeCommitment: 30,
            primaryLayer: 'nafs',
            secondaryLayers: ['qalb'],
            ruqyaIntegrationLevel: 'intermediate',
            communityIntegration: true,
            crisisSupport: true, // This should be true for high crisis level
          },
          days: Array.from({ length: 21 }, (_, i) => ({
            day: i + 1,
            title: `Day ${i + 1}: Foundation`,
            practices: [],
            content: [],
          })),
        },
        error: null,
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'spiritual_diagnoses') return crisisAssessmentBuilder;
        if (table === 'user_profiles') return crisisUserProfileBuilder;
        if (table === 'journeys') return crisisInsertBuilder;
        return createMockQueryBuilder();
      });

      const result = await journeyService.createPersonalizedJourney(
        userId,
        assessmentId
      );

      expect(result.configuration.crisisSupport).toBe(true);
    });

    it('should handle assessment not found error', async () => {
      const errorBuilder = createMockQueryBuilder();
      errorBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'spiritual_diagnoses') return errorBuilder;
        return createMockQueryBuilder();
      });

      await expect(
        journeyService.createPersonalizedJourney(userId, 'invalid-assessment')
      ).rejects.toThrow('Assessment not found');
    });

    it('should handle AI service errors', async () => {
      // Set up normal mocks for assessment and user profile
      const normalAssessmentBuilder = createMockQueryBuilder();
      const normalUserProfileBuilder = createMockQueryBuilder();

      normalAssessmentBuilder.single.mockResolvedValue({
        data: mockAssessment,
        error: null,
      });

      normalUserProfileBuilder.single.mockResolvedValue({
        data: mockUserProfile,
        error: null,
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'spiritual_diagnoses') return normalAssessmentBuilder;
        if (table === 'user_profiles') return normalUserProfileBuilder;
        return createMockQueryBuilder();
      });

      // Mock AI service to throw error
      global.mockAiService.generateJourneyParameters.mockRejectedValueOnce(
        new Error('AI service unavailable')
      );

      await expect(
        journeyService.createPersonalizedJourney(userId, assessmentId)
      ).rejects.toThrow('Failed to create personalized journey');
    });

    it('should handle database insertion errors', async () => {
      // Set up normal mocks for assessment and user profile
      const normalAssessmentBuilder = createMockQueryBuilder();
      const normalUserProfileBuilder = createMockQueryBuilder();
      const errorInsertBuilder = createMockQueryBuilder();

      normalAssessmentBuilder.single.mockResolvedValue({
        data: mockAssessment,
        error: null,
      });

      normalUserProfileBuilder.single.mockResolvedValue({
        data: mockUserProfile,
        error: null,
      });

      // Mock journey insertion to fail
      errorInsertBuilder.single.mockResolvedValue({
        data: null,
        error: { message: 'Database insertion failed' },
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'spiritual_diagnoses') return normalAssessmentBuilder;
        if (table === 'user_profiles') return normalUserProfileBuilder;
        if (table === 'journeys') return errorInsertBuilder;
        return createMockQueryBuilder();
      });

      await expect(
        journeyService.createPersonalizedJourney(userId, assessmentId)
      ).rejects.toThrow('Failed to create personalized journey');
    });
  });

  describe('startJourney', () => {
    const journeyId = 'journey-123';
    const userId = 'user-456';

    it('should start journey successfully', async () => {
      const existingJourney = {
        id: journeyId,
        user_id: userId,
        status: 'created',
        title: 'Test Journey',
      };

      const updatedJourney = {
        id: journeyId,
        user_id: userId,
        status: 'active',
        title: 'Test Journey',
        started_at: new Date().toISOString(),
        last_active_at: new Date().toISOString(),
      };

      // Create separate builders for fetch and update operations
      let callCount = 0;
      const journeyBuilder = createMockQueryBuilder();

      journeyBuilder.single.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          // First call: fetch existing journey
          return Promise.resolve({
            data: existingJourney,
            error: null,
          });
        } else {
          // Second call: update journey
          return Promise.resolve({
            data: updatedJourney,
            error: null,
          });
        }
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return journeyBuilder;
        return createMockQueryBuilder();
      });

      const result = await journeyService.startJourney(journeyId, userId);

      expect(result).toEqual(updatedJourney);
      expect(journeyBuilder.update).toHaveBeenCalledWith({
        status: 'active',
        started_at: expect.any(String),
        last_active_at: expect.any(String),
      });
    });

    it('should handle journey not found error', async () => {
      const errorBuilder = createMockQueryBuilder();

      // Mock the first call (fetch journey) to return not found error
      errorBuilder.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' },
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return errorBuilder;
        return createMockQueryBuilder();
      });

      await expect(
        journeyService.startJourney(journeyId, userId)
      ).rejects.toThrow('Journey not found');
    });

    it('should handle database update errors', async () => {
      mockSupabase
        .from()
        .update()
        .eq()
        .eq()
        .select()
        .single.mockRejectedValueOnce(new Error('Database update failed'));

      await expect(
        journeyService.startJourney(journeyId, userId)
      ).rejects.toThrow('Failed to start journey');
    });
  });

  describe('recordSimpleDayCompletion', () => {
    const journeyId = 'journey-123';
    const userId = 'user-123'; // Added userId for the new method signature
    const dayNumber = 5;
    const practiceResults = {
      dhikr_completed: true,
      prayer_on_time: true,
      quran_reading: false,
      reflection_notes: 'Felt more peaceful today',
    };

    beforeEach(() => {
      jest.clearAllMocks();

      // Set up default mock journey data
      const mockJourney = {
        id: journeyId,
        user_id: 'user-123',
        completed_days: [1, 2, 3, 4],
        total_progress: 19,
        duration: 21,
        days: Array.from({ length: 21 }, (_, i) => ({ day: i + 1 })),
      };

      // Create builders for different operations
      const journeySelectBuilder = createMockQueryBuilder();
      const progressInsertBuilder = createMockQueryBuilder();
      const journeyUpdateBuilder = createMockQueryBuilder();

      // Mock journey fetch
      journeySelectBuilder.single.mockResolvedValue({
        data: mockJourney,
        error: null,
      });

      // Mock progress insertion
      progressInsertBuilder.single.mockResolvedValue({
        data: {
          id: 'progress-123',
          journey_id: journeyId,
          day_number: dayNumber,
          practices_completed: [practiceResults],
          created_at: new Date().toISOString(),
        },
        error: null,
      });

      // Mock journey update - the update operation doesn't return data directly
      // The service just calls update().eq() without expecting a return value

      // Setup mockSupabase.from to return appropriate builders
      let journeyCallCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') {
          journeyCallCount++;
          if (journeyCallCount === 1) {
            return journeySelectBuilder; // First call: select journey
          } else {
            return createMockQueryBuilder(); // Second call: update journey (just needs to chain)
          }
        }
        if (table === 'journey_progress') {
          return progressInsertBuilder;
        }
        return createMockQueryBuilder();
      });
    });

    it('should update journey progress successfully', async () => {
      const result = await journeyService.recordSimpleDayCompletion(
        journeyId,
        userId,
        dayNumber,
        practiceResults
      );

      expect(result).toMatchObject({
        dayNumber: 5,
        completed: true,
        progress: expect.any(Number),
        practiceResults,
      });
    });

    it('should not duplicate completed days', async () => {
      // Day 4 already completed in the mock data
      const result = await journeyService.recordSimpleDayCompletion(
        journeyId,
        userId,
        4, // This day is already in completed_days: [1, 2, 3, 4]
        practiceResults
      );

      expect(result.completed).toBe(true);
      expect(result.dayNumber).toBe(4);
      expect(result.progress).toBeGreaterThan(0);
    });

    it('should complete journey when all days finished', async () => {
      // Create a special mock for this test with 20 days completed
      const nearCompleteJourney = {
        id: journeyId,
        user_id: 'user-123',
        completed_days: Array.from({ length: 20 }, (_, i) => i + 1), // Days 1-20 completed
        total_progress: 95,
        duration: 21,
        days: Array.from({ length: 21 }, (_, i) => ({ day: i + 1 })),
      };

      const completionSelectBuilder = createMockQueryBuilder();
      const completionProgressBuilder = createMockQueryBuilder();
      const completionUpdateBuilder = createMockQueryBuilder();

      completionSelectBuilder.single.mockResolvedValue({
        data: nearCompleteJourney,
        error: null,
      });

      completionProgressBuilder.single.mockResolvedValue({
        data: {
          id: 'progress-123',
          journey_id: journeyId,
          day_number: 21,
          practices_completed: [practiceResults],
        },
        error: null,
      });

      // Update builder doesn't need mockResolvedValue - it just chains

      // Override the mock for this specific test
      let callCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') {
          callCount++;
          if (callCount === 1) {
            return completionSelectBuilder; // First call: select journey
          } else {
            return completionUpdateBuilder; // Second call: update journey
          }
        }
        if (table === 'journey_progress') {
          return completionProgressBuilder;
        }
        return createMockQueryBuilder();
      });

      const result = await journeyService.recordSimpleDayCompletion(
        journeyId,
        userId,
        21, // Final day
        practiceResults
      );

      expect(result.completed).toBe(true);
      expect(result.progress).toBe(100);
      expect(result.dayNumber).toBe(21);
    });

    it('should handle journey not found error', async () => {
      const errorBuilder = createMockQueryBuilder();
      errorBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return errorBuilder;
        return createMockQueryBuilder();
      });

      await expect(
        journeyService.recordSimpleDayCompletion(
          journeyId,
          userId,
          dayNumber,
          practiceResults
        )
      ).rejects.toThrow('Failed to save simple day completion record'); // Updated error message
    });

    it('should handle progress insertion errors', async () => {
      const normalJourneyBuilder = createMockQueryBuilder();
      const errorProgressBuilder = createMockQueryBuilder();

      // Mock successful journey fetch
      normalJourneyBuilder.single.mockResolvedValue({
        data: {
          id: journeyId,
          user_id: 'user-123',
          completed_days: [1, 2, 3, 4],
          duration: 21,
        },
        error: null,
      });

      // Mock failed progress insertion
      errorProgressBuilder.single.mockResolvedValue({
        data: null,
        error: { message: 'Progress insertion failed' },
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return normalJourneyBuilder;
        if (table === 'journey_progress') return errorProgressBuilder;
        return createMockQueryBuilder();
      });

      await expect(
        journeyService.recordSimpleDayCompletion(
          journeyId,
          userId,
          dayNumber,
          practiceResults
        )
      ).rejects.toThrow('Failed to save simple day completion record'); // Updated error message
    });
  });

  describe('getUserJourneys', () => {
    const userId = 'user-123';

    it('should retrieve user journeys successfully', async () => {
      const mockJourneys = [
        {
          id: 'journey-1',
          title: 'Nafs Healing Journey',
          status: 'active',
          total_progress: 45,
        },
        {
          id: 'journey-2',
          title: 'Qalb Purification',
          status: 'completed',
          total_progress: 100,
        },
      ];

      const journeysBuilder = createMockQueryBuilder();

      // Mock the final promise resolution after the query chain
      journeysBuilder.order.mockResolvedValue({
        data: mockJourneys,
        error: null,
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return journeysBuilder;
        return createMockQueryBuilder();
      });

      const result = await journeyService.getUserJourneys(userId);

      expect(result).toEqual(mockJourneys);
      expect(journeysBuilder.select).toHaveBeenCalled();
      expect(journeysBuilder.eq).toHaveBeenCalledWith('user_id', userId);
      expect(journeysBuilder.order).toHaveBeenCalledWith('created_at', {
        ascending: false,
      });
    });

    it('should return empty array for user with no journeys', async () => {
      const emptyBuilder = createMockQueryBuilder();
      emptyBuilder.order.mockResolvedValue({
        data: [],
        error: null,
      });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return emptyBuilder;
        return createMockQueryBuilder();
      });

      const result = await journeyService.getUserJourneys(userId);

      expect(result).toEqual([]);
    });

    it('should handle database query errors', async () => {
      const errorBuilder = createMockQueryBuilder();
      errorBuilder.order.mockRejectedValue(new Error('Database query failed'));

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return errorBuilder;
        return createMockQueryBuilder();
      });

      await expect(journeyService.getUserJourneys(userId)).rejects.toThrow(
        'Failed to get user journeys'
      );
    });
  });

  describe('getJourneyAnalytics', () => {
    const journeyId = 'analytics-journey-123';
    const userId = 'analytics-user-123';

    const mockJourneyData = {
      id: journeyId,
      user_id: userId,
      status: 'active',
      completed_days: [1, 2, 3, 4, 5],
      configuration: {
        duration: 21,
        primaryLayer: 'qalb',
      },
      total_progress: (5 / 21) * 100,
    };

    const mockProgressRecords = [
      { journey_id: journeyId, day_number: 1, overall_rating: 8 },
      { journey_id: journeyId, day_number: 2, overall_rating: 7 },
      { journey_id: journeyId, day_number: 3, overall_rating: 9 },
      { journey_id: journeyId, day_number: 4, overall_rating: 8 },
      { journey_id: journeyId, day_number: 5, overall_rating: null }, // Day with no rating
    ];

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should retrieve and calculate journey analytics successfully', async () => {
      const journeySelectBuilder = createMockQueryBuilder();
      const progressSelectBuilder = createMockQueryBuilder();

      journeySelectBuilder.single.mockResolvedValue({ data: mockJourneyData, error: null });
      progressSelectBuilder.order.mockResolvedValue({ data: mockProgressRecords, error: null });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return journeySelectBuilder;
        if (table === 'journey_progress') return progressSelectBuilder;
        return createMockQueryBuilder();
      });

      const analytics = await journeyService.getJourneyAnalytics(journeyId, userId);

      expect(analytics).not.toBeNull();
      expect(analytics?.journeyId).toBe(journeyId);
      expect(analytics?.userId).toBe(userId);
      expect(analytics?.completedDays).toBe(5);
      expect(analytics?.completionRate).toBeCloseTo((5 / 21) * 100);
      expect(analytics?.averageDailyRating).toBeCloseTo((8 + 7 + 9 + 8) / 4); // (8+7+9+8)/4 = 8
      expect(analytics?.practiceAdherence).toBeCloseTo((5 / 21) * 100); // 5 days with progress out of 21
      expect(analytics?.streakDays).toBe(0); // Placeholder
      expect(analytics?.graduationReadiness).toBe(false);

      expect(journeySelectBuilder.eq).toHaveBeenCalledWith('id', journeyId);
      expect(journeySelectBuilder.eq).toHaveBeenCalledWith('user_id', userId);
      expect(progressSelectBuilder.eq).toHaveBeenCalledWith('journey_id', journeyId);
    });

    it('should handle journey not found for analytics', async () => {
      const journeySelectBuilder = createMockQueryBuilder();
      journeySelectBuilder.single.mockResolvedValue({ data: null, error: { code: 'PGRST116' } });

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return journeySelectBuilder;
        return createMockQueryBuilder();
      });

      await expect(journeyService.getJourneyAnalytics(journeyId, userId)).rejects.toThrow('Journey not found for analytics');
    });

    it('should handle missing progress records gracefully', async () => {
      const journeySelectBuilder = createMockQueryBuilder();
      const progressSelectBuilder = createMockQueryBuilder();

      journeySelectBuilder.single.mockResolvedValue({ data: mockJourneyData, error: null });
      progressSelectBuilder.order.mockResolvedValue({ data: [], error: null }); // No progress records

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return journeySelectBuilder;
        if (table === 'journey_progress') return progressSelectBuilder;
        return createMockQueryBuilder();
      });

      const analytics = await journeyService.getJourneyAnalytics(journeyId, userId);

      expect(analytics).not.toBeNull();
      expect(analytics?.averageDailyRating).toBeNull(); // or 0 depending on implementation if no rated records
      expect(analytics?.practiceAdherence).toBeCloseTo(0);
    });


    it('should return null if journey data is null without specific error', async () => {
      const journeySelectBuilder = createMockQueryBuilder();
      journeySelectBuilder.single.mockResolvedValue({ data: null, error: null }); // Journey is null

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'journeys') return journeySelectBuilder;
        return createMockQueryBuilder();
      });

      const analytics = await journeyService.getJourneyAnalytics(journeyId, userId);
      expect(analytics).toBeNull();
    });


  });

  describe('Placeholder Log Methods', () => {
    let consoleLogSpy: jest.SpyInstance;
    let consoleWarnSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
      consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
      // Note: journeyService is an instance of JourneyService, need to access private methods via 'as any'
    });

    afterEach(() => {
      consoleLogSpy.mockRestore();
      consoleWarnSpy.mockRestore();
    });

    it('sendJourneyWelcome should log a message', async () => {
      const mockJourney = { id: 'j1', userId: 'u1', title: 'Test Welcome Journey' } as any;
      // Accessing private method for test purposes
      await (journeyService as any).sendJourneyWelcome(mockJourney);
      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('[JourneyService] INFO: Sending welcome'));
      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining(mockJourney.title));
    });

    it('triggerCrisisResponse should log a warning', async () => {
      const crisisAnalysis = { level: 'high', indicators: ['severe distress'] };
      // Accessing private method for test purposes
      await (journeyService as any).triggerCrisisResponse('j1', 'u1', crisisAnalysis);
      expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('[JourneyService] CRITICAL: Triggering crisis response'));
      expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('j1'));
    });

    it('generateCompletionAnalytics should log a message', async () => {
      // Accessing private method for test purposes
      await (journeyService as any).generateCompletionAnalytics('j1');
      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('[JourneyService] INFO: Generating completion analytics'));
      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('j1'));
    });

    it('sendCompletionCelebration should log a message', async () => {
      // Accessing private method for test purposes
      await (journeyService as any).sendCompletionCelebration('j1');
      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('[JourneyService] INFO: Sending completion celebration'));
      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('j1'));
    });
  });
});
