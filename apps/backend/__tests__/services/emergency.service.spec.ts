import { emergencyService, EmergencyService } from '../../src/services/emergency.service';
import { AppError } from '../../src/middleware/errorHandler';
import { prisma } from '../../src/config/database'; // Import prisma client
import { triggerN8nWorkflow } from '../../src/services/n8n.service';
import axios from 'axios';
// No longer need to mock communityService for requestDua if it's removed from emergencyService
// import { communityService as actualCommunityService } from '../../src/services/community.service';

jest.mock('../../src/utils/logger', () => ({
  logger: { info: jest.fn(), warn: jest.fn(), error: jest.fn() },
}));
jest.mock('../../src/services/n8n.service');
jest.mock('axios');

// Mock Prisma Client
jest.mock('../../src/config/database', () => ({
  prisma: {
    emergencySession: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    profile: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    breathingExercise: {
      findFirst: jest.fn(),
    },
    ruqyahVerse: {
      findMany: jest.fn(),
    },
    duaRequestLog: {
      create: jest.fn(),
    },
    peerSupportRequest: {
      create: jest.fn(),
    },
    emergencyHelpline: { // Added for getProfessionalContacts
      findMany: jest.fn(),
    }
    // Add other models as needed for tests
  },
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;
// const mockedCommunityService = actualCommunityService as jest.Mocked<typeof actualCommunityService>; // If still used for other things
const mockedPrisma = prisma as jest.Mocked<typeof prisma>;

describe('EmergencyService', () => {
  let serviceInstance: EmergencyService;

  beforeEach(() => {
    jest.clearAllMocks();
    serviceInstance = new EmergencyService();
  });

  describe('startQalbRescueSession', () => {
    const userId = 'user-test-123';
    const triggerType = 'manual';
    const currentSymptoms = ['anxiety'];
    const mockSessionId = 'session-uuid-123';
    const mockInitialStepContent = { title: 'Step 1: Grounding', description: 'Initial grounding content' };

    it('should start a session and return initial content using Prisma', async () => {
      mockedPrisma.emergencySession.create.mockResolvedValueOnce({
        id: mockSessionId, userId, triggerType, currentSymptoms,
        currentStep: 'grounding', log: [], status: 'active',
        startTime: new Date(), createdAt: new Date(), updatedAt: new Date(),
        endTime: null, escalationReason: null, recommendedActions: [], estimatedDuration: null, effectivenessRating: null, feedback: null,
      } as any); // Cast to any to satisfy Prisma return type if complex

      jest.spyOn(serviceInstance, 'getStepContent').mockResolvedValueOnce(mockInitialStepContent as any);

      const result = await serviceInstance.startQalbRescueSession(userId, triggerType, currentSymptoms);

      expect(mockedPrisma.emergencySession.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: userId,
          triggerType: triggerType,
          currentSymptoms: currentSymptoms,
          status: 'active',
          currentStep: 'grounding',
        }),
      });
      expect(result.session.id).toBe(mockSessionId);
      expect(result.initialStepContent.title).toBe('Step 1: Grounding');
      expect(serviceInstance.getStepContent).toHaveBeenCalledWith('grounding', userId, mockSessionId);
    });

    it('should throw AppError if Prisma create fails', async () => {
      mockedPrisma.emergencySession.create.mockRejectedValueOnce(new Error('DB create failed'));
      await expect(serviceInstance.startQalbRescueSession(userId, triggerType, currentSymptoms))
        .rejects.toThrow(AppError); // Error message might be more specific in actual impl.
    });
  });

  describe('progressQalbRescueSession', () => {
    const sessionId = 'session-progress-123';
    const userId = 'user-progress-123';

    beforeEach(() => {
        jest.spyOn(serviceInstance, 'getStepContent').mockImplementation(
            (stepName) => Promise.resolve({ title: `Content for ${stepName}`, description: `Desc for ${stepName}` } as any)
        );
    });

    it('should progress to the next step and return new content using Prisma', async () => {
      mockedPrisma.emergencySession.findUnique.mockResolvedValueOnce({
        id: sessionId, userId, currentStep: 'grounding', status: 'active', log: []
      } as any);
      mockedPrisma.emergencySession.update.mockResolvedValueOnce({} as any);

      const result = await serviceInstance.progressQalbRescueSession(sessionId, userId, 'grounding');

      expect(result).toBeDefined();
      expect(result?.currentStep).toBe('breathing');
      expect(result?.nextStepContent.title).toBe('Content for breathing');
      expect(mockedPrisma.emergencySession.update).toHaveBeenCalledWith(expect.objectContaining({
        where: { id: sessionId },
        data: expect.objectContaining({ currentStep: 'breathing' }),
      }));
    });

    it('should complete session if last step is "connection" using Prisma', async () => {
      mockedPrisma.emergencySession.findUnique.mockResolvedValueOnce({
        id: sessionId, userId, currentStep: 'connection', status: 'active', log: []
      } as any);
      const endSessionSpy = jest.spyOn(serviceInstance, 'endQalbRescueSession').mockResolvedValueOnce(undefined);

      const result = await serviceInstance.progressQalbRescueSession(sessionId, userId, 'connection');
      expect(result).toBeNull();
      expect(endSessionSpy).toHaveBeenCalledWith(sessionId, userId, 'completed_all_steps');
    });
  });

  describe('getStepContent', () => {
    const userId = 'user-content-123';
    const sessionId = 'session-content-123';

    it('should fetch breathing exercise using Prisma', async () => {
      mockedPrisma.breathingExercise.findFirst.mockResolvedValueOnce({
        name: 'Test Breathing', instructions: ['breathe in', 'breathe out'], audioUrl: 'test.mp3'
      } as any);
      const content = await serviceInstance.getStepContent('breathing', userId, sessionId);
      expect(content.title).toBe('Test Breathing');
    });

    it('should call AI service for "comfort" and use Prisma for fallback', async () => {
      mockedPrisma.profile.findUnique.mockResolvedValueOnce({ preferences: { preferred_themes: ['hope'] } } as any);
      mockedPrisma.emergencySession.findUnique.mockResolvedValueOnce({ currentSymptoms: ['anxiety'], log: [] } as any);

      const mockAiResponse = {
        data: {
          quran_verses: [{ id: 'ai_q_1', arabic_text: 'AI Verse', translation_en: 'AI Trans', theme: 'hope', audio_url: 'ai.mp3' }],
          dhikr_phrases: [],
          reasoning: 'AI recommended'
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockAiResponse);

      const content = await serviceInstance.getStepContent('comfort', userId, sessionId);
      expect(content.title).toContain('Personalized');
      expect(content.items?.[0].id).toBe('ai_q_1');
      expect(mockedAxios.post).toHaveBeenCalled();
    });

    it('should use Prisma DB fallback for "comfort" if AI fails', async () => {
      mockedPrisma.profile.findUnique.mockResolvedValueOnce({} as any); // Mock profile fetch
      mockedPrisma.emergencySession.findUnique.mockResolvedValueOnce({} as any); // Mock session fetch
      mockedAxios.post.mockRejectedValueOnce(new Error('AI service down'));

      mockedPrisma.ruqyahVerse.findMany.mockResolvedValueOnce([
        { id: 'db_q_1', arabic: 'DB Verse', translation: 'DB Trans', surah: 'Fatiha', ayah: 1, audioUrl: 'db.mp3' }
      ] as any);

      const content = await serviceInstance.getStepContent('comfort', userId, sessionId);
      expect(content.title).toContain('General');
      expect(content.items?.[0].id).toBe('db_q_1');
    });
  });

  describe('requestDuaFromCommunity', () => {
    const sessionId = 'session-dua-123';
    const userId = 'user-dua-123';

    it('should log dua request to dua_request_logs using Prisma', async () => {
      mockedPrisma.duaRequestLog.create.mockResolvedValueOnce({} as any);
      // No longer calling communityService.requestDua

      await serviceInstance.requestDuaFromCommunity(sessionId, userId);
      expect(mockedPrisma.duaRequestLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          sessionId: sessionId,
          userId: userId,
        }),
      });
    });
  });

  describe('findPeerSupporter', () => {
    const sessionId = 'session-peer-123';
    const userId = 'user-peer-123';

    it('should return unavailable if no supporters are found using Prisma', async () => {
      mockedPrisma.profile.findMany.mockResolvedValueOnce([]);
      const result = await serviceInstance.findPeerSupporter(sessionId, userId);
      expect(result.status).toBe('unavailable');
    });

    it('should create a request using Prisma if supporter found', async () => {
      const mockSupporter = { id: 'supporter-xyz', fullName: 'Peer Supporter', email: '<EMAIL>' };
      mockedPrisma.profile.findMany.mockResolvedValueOnce([mockSupporter] as any);

      const mockPeerRequest = { id: 'peer-req-abc', sessionId, userId, supporterId: mockSupporter.id, status: 'pending_acceptance' };
      mockedPrisma.peerSupportRequest.create.mockResolvedValueOnce(mockPeerRequest as any);

      const result = await serviceInstance.findPeerSupporter(sessionId, userId);
      expect(result.status).toBe('pending_acceptance');
      expect(result.peerSupportRequestId).toBe('peer-req-abc');
      expect(mockedPrisma.peerSupportRequest.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          supporterId: mockSupporter.id,
          status: 'pending_acceptance'
        }),
      });
    });
  });

  // TODO: Add tests for escalateToProfessional, getProfessionalContacts adapting to Prisma mocks
  // TODO: Add tests for recordUserFeedback, endQalbRescueSession using Prisma mocks
});