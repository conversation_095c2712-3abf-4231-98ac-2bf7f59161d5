/**
 * Crisis Detection Service Unit Tests
 * Tests crisis detection service with mocked dependencies
 */

import { CrisisDetectionService } from '../../src/services/crisis-detection.service';
import { getSupabase } from '../../src/config/supabase';

// Mock dependencies
jest.mock('../../src/config/supabase');
jest.mock('../../src/services/ai.service');
jest.mock('../../src/services/n8n.service');

describe('Crisis Detection Service Unit Tests', () => {
  let crisisDetectionService: CrisisDetectionService;
  let mockSupabase: any;

  beforeEach(() => {
    crisisDetectionService = new CrisisDetectionService();
    mockSupabase = global.mockSupabaseClient;
    (getSupabase as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('analyzeResponse', () => {
    const mockResponses = {
      mental_health_screening: {
        suicidal_thoughts: 'often',
        self_harm: 'yes',
        hopelessness: 'severe',
        support_system: 'none',
      },
    };
    const step = 'mental_health_screening';

    it('should detect high-risk crisis indicators', async () => {
      // Mock AI service crisis detection
      global.mockAiService.detectCrisisIndicators.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'high',
        indicators: ['suicidal_ideation', 'self_harm', 'hopelessness'],
        confidence: 0.95,
        recommendations: ['immediate_professional_help', 'emergency_contacts'],
      });

      const result = await crisisDetectionService.analyzeResponse(mockResponses, step);

      expect(result).toMatchObject({
        isCrisis: true,
        severity: 'high',
        indicators: expect.arrayContaining(['suicidal_ideation', 'self_harm', 'hopelessness']),
        confidence: 0.95,
        riskLevel: 'immediate',
        recommendations: expect.arrayContaining(['immediate_professional_help']),
        emergencyProtocol: true,
      });

      expect(global.mockAiService.detectCrisisIndicators).toHaveBeenCalledWith(
        expect.stringContaining('suicidal_thoughts: often')
      );
    });

    it('should detect moderate-risk indicators', async () => {
      const moderateResponses = {
        mental_health_screening: {
          anxiety: 'severe',
          depression: 'high',
          panic_attacks: 'frequent',
          sleep_disturbance: 'severe',
        },
      };

      global.mockAiService.detectCrisisIndicators.mockResolvedValueOnce({
        isCrisis: false,
        severity: 'moderate',
        indicators: ['severe_anxiety', 'depression'],
        confidence: 0.82,
        recommendations: ['professional_counseling', 'islamic_support'],
      });

      const result = await crisisDetectionService.analyzeResponse(moderateResponses, step);

      expect(result).toMatchObject({
        isCrisis: false,
        severity: 'moderate',
        indicators: expect.arrayContaining(['severe_anxiety', 'depression']),
        riskLevel: 'elevated',
        recommendations: expect.arrayContaining(['professional_counseling']),
        emergencyProtocol: false,
      });
    });

    it('should handle low-risk responses', async () => {
      const lowRiskResponses = {
        mental_health_screening: {
          mood: 'slightly_low',
          stress: 'manageable',
          support_system: 'good',
          coping_mechanisms: 'effective',
        },
      };

      global.mockAiService.detectCrisisIndicators.mockResolvedValueOnce({
        isCrisis: false,
        severity: 'low',
        indicators: [],
        confidence: 0.88,
        recommendations: ['self_care', 'dhikr_practice'],
      });

      const result = await crisisDetectionService.analyzeResponse(lowRiskResponses, step);

      expect(result).toMatchObject({
        isCrisis: false,
        severity: 'low',
        indicators: [],
        riskLevel: 'normal',
        recommendations: expect.arrayContaining(['self_care']),
        emergencyProtocol: false,
      });
    });

    it('should handle AI service errors gracefully', async () => {
      global.mockAiService.detectCrisisIndicators.mockRejectedValueOnce(
        new Error('AI service unavailable')
      );

      // Should fall back to rule-based detection
      const result = await crisisDetectionService.analyzeResponse(mockResponses, step);

      expect(result).toMatchObject({
        isCrisis: true, // Rule-based detection should catch suicidal thoughts
        severity: 'high',
        indicators: expect.arrayContaining(['suicidal_ideation']),
        fallbackDetection: true,
      });

      expect(global.mockLogger.warn).toHaveBeenCalledWith(
        'AI crisis detection failed, using fallback rules',
        expect.any(Object)
      );
    });

    it('should apply rule-based detection for specific keywords', async () => {
      const keywordResponses = {
        open_response: {
          feelings: 'I want to kill myself and end this pain',
          thoughts: 'Nobody would miss me if I was gone',
        },
      };

      // Mock AI service to return low risk to test rule override
      global.mockAiService.detectCrisisIndicators.mockResolvedValueOnce({
        isCrisis: false,
        severity: 'low',
        indicators: [],
        confidence: 0.3,
      });

      const result = await crisisDetectionService.analyzeResponse(keywordResponses, 'open_response');

      expect(result.isCrisis).toBe(true);
      expect(result.severity).toBe('high');
      expect(result.indicators).toContain('suicidal_ideation');
      expect(result.ruleBasedOverride).toBe(true);
    });

    it('should log crisis detection events', async () => {
      global.mockAiService.detectCrisisIndicators.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'high',
        indicators: ['suicidal_ideation'],
        confidence: 0.95,
      });

      await crisisDetectionService.analyzeResponse(mockResponses, step);

      expect(global.mockLogger.warn).toHaveBeenCalledWith(
        'Crisis indicators detected',
        expect.objectContaining({
          severity: 'high',
          indicators: expect.arrayContaining(['suicidal_ideation']),
          step,
        })
      );
    });
  });

  describe('escalateCrisis', () => {
    const mockCrisisData = {
      userId: 'user-123',
      severity: 'high',
      indicators: ['suicidal_ideation', 'self_harm'],
      context: {
        sessionId: 'assessment-session-123',
        step: 'mental_health_screening',
      },
      immediateRisk: true,
    };

    beforeEach(() => {
      // Mock database operations
      mockSupabase.from().insert.mockResolvedValue({
        data: null,
        error: null,
      });

      // Mock N8N workflow trigger
      global.mockN8nService.triggerWorkflow.mockResolvedValue({
        success: true,
        workflowId: 'crisis-escalation-workflow',
        executionId: 'exec-123',
      });
    });

    it('should escalate high-severity crisis successfully', async () => {
      const result = await crisisDetectionService.escalateCrisis(mockCrisisData);

      expect(result).toMatchObject({
        escalationId: expect.any(String),
        severity: 'high',
        actions: expect.arrayContaining([
          'emergency_contacts_notified',
          'professional_help_contacted',
          'crisis_resources_provided',
        ]),
        resources: expect.arrayContaining([
          'suicide_hotline',
          'emergency_services',
          'islamic_counselor',
        ]),
        followUpRequired: true,
        emergencyProtocol: true,
      });

      expect(mockSupabase.from().insert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'user-123',
          severity: 'high',
          indicators: ['suicidal_ideation', 'self_harm'],
          escalation_id: expect.any(String),
          immediate_risk: true,
          status: 'active',
        })
      );

      expect(global.mockN8nService.triggerWorkflow).toHaveBeenCalledWith(
        'crisis-escalation',
        expect.objectContaining({
          userId: 'user-123',
          severity: 'high',
          escalationId: expect.any(String),
        })
      );
    });

    it('should handle moderate-severity crisis appropriately', async () => {
      const moderateCrisisData = {
        ...mockCrisisData,
        severity: 'moderate',
        indicators: ['severe_anxiety', 'depression'],
        immediateRisk: false,
      };

      const result = await crisisDetectionService.escalateCrisis(moderateCrisisData);

      expect(result).toMatchObject({
        severity: 'moderate',
        actions: expect.arrayContaining([
          'counseling_recommended',
          'support_resources_provided',
        ]),
        resources: expect.arrayContaining([
          'mental_health_hotline',
          'islamic_counselor',
        ]),
        followUpRequired: true,
        emergencyProtocol: false,
      });

      expect(result.actions).not.toContain('emergency_contacts_notified');
    });

    it('should generate unique escalation IDs', async () => {
      const result1 = await crisisDetectionService.escalateCrisis(mockCrisisData);
      const result2 = await crisisDetectionService.escalateCrisis(mockCrisisData);

      expect(result1.escalationId).not.toBe(result2.escalationId);
      expect(result1.escalationId).toMatch(/^crisis_\d+_[a-z0-9]+$/);
    });

    it('should handle database insertion errors', async () => {
      mockSupabase.from().insert.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database insertion failed' },
      });

      await expect(
        crisisDetectionService.escalateCrisis(mockCrisisData)
      ).rejects.toThrow('Failed to log crisis escalation');
    });

    it('should handle N8N workflow errors gracefully', async () => {
      global.mockN8nService.triggerWorkflow.mockRejectedValueOnce(
        new Error('N8N service unavailable')
      );

      const result = await crisisDetectionService.escalateCrisis(mockCrisisData);

      expect(result).toBeDefined();
      expect(result.escalationId).toBeDefined();
      expect(global.mockLogger.error).toHaveBeenCalledWith(
        'Failed to trigger crisis workflow',
        expect.any(Object)
      );
    });

    it('should include Islamic crisis resources', async () => {
      const result = await crisisDetectionService.escalateCrisis(mockCrisisData);

      expect(result.resources).toEqual(
        expect.arrayContaining([
          'islamic_counselor',
          'imam_consultation',
          'islamic_crisis_hotline',
        ])
      );
    });

    it('should schedule follow-up for all crisis levels', async () => {
      const lowCrisisData = {
        ...mockCrisisData,
        severity: 'low',
        indicators: ['mild_anxiety'],
        immediateRisk: false,
      };

      const result = await crisisDetectionService.escalateCrisis(lowCrisisData);

      expect(result.followUpRequired).toBe(true);
      expect(result.followUpSchedule).toBeDefined();
    });
  });

  describe('checkForCrisisIndicators', () => {
    it('should identify crisis keywords in text', () => {
      const crisisTexts = [
        'I want to kill myself',
        'I am going to end my life',
        'I want to die',
        'I am planning to hurt myself',
        'Nobody would miss me if I was gone',
      ];

      crisisTexts.forEach(text => {
        const result = crisisDetectionService.checkForCrisisIndicators(text);
        expect(result.hasCrisisKeywords).toBe(true);
        expect(result.indicators).toContain('suicidal_ideation');
      });
    });

    it('should not flag normal expressions', () => {
      const normalTexts = [
        'I feel sad today',
        'I am struggling with work',
        'I feel overwhelmed sometimes',
        'I need help with my studies',
      ];

      normalTexts.forEach(text => {
        const result = crisisDetectionService.checkForCrisisIndicators(text);
        expect(result.hasCrisisKeywords).toBe(false);
        expect(result.indicators).toHaveLength(0);
      });
    });

    it('should detect self-harm indicators', () => {
      const selfHarmTexts = [
        'I have been cutting myself',
        'I hurt myself when I feel bad',
        'I want to harm myself',
      ];

      selfHarmTexts.forEach(text => {
        const result = crisisDetectionService.checkForCrisisIndicators(text);
        expect(result.hasCrisisKeywords).toBe(true);
        expect(result.indicators).toContain('self_harm');
      });
    });

    it('should be case-insensitive', () => {
      const text = 'I WANT TO KILL MYSELF';
      const result = crisisDetectionService.checkForCrisisIndicators(text);
      
      expect(result.hasCrisisKeywords).toBe(true);
      expect(result.indicators).toContain('suicidal_ideation');
    });

    it('should handle empty or null input', () => {
      expect(() => {
        crisisDetectionService.checkForCrisisIndicators('');
      }).not.toThrow();

      expect(() => {
        crisisDetectionService.checkForCrisisIndicators(null as any);
      }).not.toThrow();
    });
  });

  describe('getCrisisResources', () => {
    it('should return appropriate resources for high severity', () => {
      const resources = crisisDetectionService.getCrisisResources('high', 'US');

      expect(resources).toEqual(
        expect.objectContaining({
          hotlines: expect.arrayContaining([
            expect.objectContaining({
              name: 'National Suicide Prevention Lifeline',
              phone: expect.any(String),
              available: '24/7',
            }),
          ]),
          islamicCounselors: expect.any(Array),
          emergencyPrayers: expect.arrayContaining([
            expect.objectContaining({
              title: expect.any(String),
              arabic: expect.any(String),
              transliteration: expect.any(String),
              translation: expect.any(String),
            }),
          ]),
          selfCareSteps: expect.any(Array),
        })
      );
    });

    it('should include location-specific resources', () => {
      const usResources = crisisDetectionService.getCrisisResources('high', 'US');
      const ukResources = crisisDetectionService.getCrisisResources('high', 'UK');

      expect(usResources.hotlines[0].phone).toContain('988');
      expect(ukResources.hotlines[0].phone).toContain('116');
    });

    it('should provide Islamic-specific resources', () => {
      const resources = crisisDetectionService.getCrisisResources('moderate', 'US');

      expect(resources.islamicCounselors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            name: expect.any(String),
            specialization: expect.stringContaining('Islamic'),
            contact: expect.any(String),
          }),
        ])
      );

      expect(resources.emergencyPrayers).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            arabic: expect.any(String),
            transliteration: expect.any(String),
          }),
        ])
      );
    });
  });
});
