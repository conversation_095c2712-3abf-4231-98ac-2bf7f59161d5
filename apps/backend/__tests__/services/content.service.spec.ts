import {
  generateSignedUrl,
  validateContentAccess,
  trackContentMetrics,
  getContentRecommendations,
} from '../../src/services/content.service';
import { AppError } from '../../src/middleware/errorHandler';

// Mock dependencies
jest.mock('../../src/config/supabase');
jest.mock('../../src/utils/logger');

const mockSupabase = {
  storage: {
    from: jest.fn().mockReturnThis(),
    createSignedUrl: jest.fn(),
  },
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  not: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  rpc: jest.fn(),
};

const createMockQueryBuilder = () => ({
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  not: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  rpc: jest.fn(),
});

describe('Content Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock getSupabase to return our mock
    require('../../src/config/supabase').getSupabase = jest.fn(() => mockSupabase);
  });

  describe('generateSignedUrl', () => {
    it('should generate a signed URL successfully', async () => {
      const mockSignedUrl = 'https://storage.supabase.co/signed-url/content/audio/dhikr-123.mp3?token=abc123';
      
      mockSupabase.storage.createSignedUrl.mockResolvedValueOnce({
        data: { signedUrl: mockSignedUrl },
        error: null,
      });

      const result = await generateSignedUrl('audio/dhikr-123.mp3', 'audio/mpeg');

      expect(result).toBe(mockSignedUrl);
      expect(mockSupabase.storage.from).toHaveBeenCalledWith('content');
      expect(mockSupabase.storage.createSignedUrl).toHaveBeenCalledWith(
        'audio/dhikr-123.mp3',
        3600
      );
    });

    it('should handle storage errors when generating signed URL', async () => {
      mockSupabase.storage.createSignedUrl.mockResolvedValueOnce({
        data: null,
        error: { message: 'File not found' },
      });

      await expect(
        generateSignedUrl('audio/nonexistent.mp3', 'audio/mpeg')
      ).rejects.toThrow(AppError);
      await expect(
        generateSignedUrl('audio/nonexistent.mp3', 'audio/mpeg')
      ).rejects.toThrow('Failed to generate content access URL');
    });

    it('should handle Supabase connection failures', async () => {
      require('../../src/config/supabase').getSupabase = jest.fn(() => {
        throw new Error('Supabase connection failed');
      });

      await expect(
        generateSignedUrl('audio/dhikr-123.mp3', 'audio/mpeg')
      ).rejects.toThrow(AppError);
      await expect(
        generateSignedUrl('audio/dhikr-123.mp3', 'audio/mpeg')
      ).rejects.toThrow('Service temporarily unavailable');
    });

    it('should handle different content types', async () => {
      const mockVideoUrl = 'https://storage.supabase.co/signed-url/content/video/quran-recitation.mp4?token=xyz789';
      
      mockSupabase.storage.createSignedUrl.mockResolvedValueOnce({
        data: { signedUrl: mockVideoUrl },
        error: null,
      });

      const result = await generateSignedUrl('video/quran-recitation.mp4', 'video/mp4');

      expect(result).toBe(mockVideoUrl);
      expect(mockSupabase.storage.createSignedUrl).toHaveBeenCalledWith(
        'video/quran-recitation.mp4',
        3600
      );
    });
  });

  describe('validateContentAccess', () => {
    it('should allow access to free content for any user', async () => {
      const mockUserProfile = {
        subscription_tier: 'free',
        journey_type: 'basic',
      };

      const mockContent = {
        access_level: 'free',
        required_tier: null,
      };

      const userQuery = createMockQueryBuilder();
      userQuery.single.mockResolvedValueOnce({ data: mockUserProfile, error: null });
      
      const contentQuery = createMockQueryBuilder();
      contentQuery.single.mockResolvedValueOnce({ data: mockContent, error: null });

      mockSupabase.from
        .mockReturnValueOnce(userQuery)
        .mockReturnValueOnce(contentQuery);

      const result = await validateContentAccess('user-123', 'content-456');

      expect(result).toBe(true);
      expect(mockSupabase.from).toHaveBeenCalledWith('profiles');
      expect(mockSupabase.from).toHaveBeenCalledWith('content_items');
      expect(userQuery.select).toHaveBeenCalledWith('subscription_tier, journey_type');
      expect(contentQuery.select).toHaveBeenCalledWith('access_level, required_tier');
    });

    it('should allow access to premium content for premium users', async () => {
      const mockUserProfile = {
        subscription_tier: 'premium',
        journey_type: 'advanced',
      };

      const mockContent = {
        access_level: 'premium',
        required_tier: 'premium',
      };

      const userQuery = createMockQueryBuilder();
      userQuery.single.mockResolvedValueOnce({ data: mockUserProfile, error: null });
      
      const contentQuery = createMockQueryBuilder();
      contentQuery.single.mockResolvedValueOnce({ data: mockContent, error: null });

      mockSupabase.from
        .mockReturnValueOnce(userQuery)
        .mockReturnValueOnce(contentQuery);

      const result = await validateContentAccess('user-123', 'premium-content-456');

      expect(result).toBe(true);
    });

    it('should deny access to premium content for free users', async () => {
      const mockUserProfile = {
        subscription_tier: 'free',
        journey_type: 'basic',
      };

      const mockContent = {
        access_level: 'premium',
        required_tier: 'premium',
      };

      const userQuery = createMockQueryBuilder();
      userQuery.single.mockResolvedValueOnce({ data: mockUserProfile, error: null });
      
      const contentQuery = createMockQueryBuilder();
      contentQuery.single.mockResolvedValueOnce({ data: mockContent, error: null });

      mockSupabase.from
        .mockReturnValueOnce(userQuery)
        .mockReturnValueOnce(contentQuery);

      const result = await validateContentAccess('user-123', 'premium-content-456');

      expect(result).toBe(false);
    });

    it('should deny access to non-existent content', async () => {
      const mockUserProfile = {
        subscription_tier: 'premium',
        journey_type: 'advanced',
      };

      const userQuery = createMockQueryBuilder();
      userQuery.single.mockResolvedValueOnce({ data: mockUserProfile, error: null });
      
      const contentQuery = createMockQueryBuilder();
      contentQuery.single.mockResolvedValueOnce({ data: null, error: null });

      mockSupabase.from
        .mockReturnValueOnce(userQuery)
        .mockReturnValueOnce(contentQuery);

      const result = await validateContentAccess('user-123', 'nonexistent-content');

      expect(result).toBe(false);
    });

    it('should handle database errors gracefully', async () => {
      const userQuery = createMockQueryBuilder();
      userQuery.single.mockRejectedValueOnce(new Error('Database connection failed'));

      mockSupabase.from.mockReturnValueOnce(userQuery);

      const result = await validateContentAccess('user-123', 'content-456');

      expect(result).toBe(false);
    });
  });

  describe('trackContentMetrics', () => {
    it('should track content metrics successfully', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({ data: null, error: null });

      await trackContentMetrics('content-123', 'view');

      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_content_metrics', {
        content_id: 'content-123',
        interaction_type: 'view',
      });
    });

    it('should handle different interaction types', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({ data: null, error: null });

      await trackContentMetrics('content-456', 'complete');

      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_content_metrics', {
        content_id: 'content-456',
        interaction_type: 'complete',
      });
    });

    it('should not throw errors when metrics tracking fails', async () => {
      mockSupabase.rpc.mockRejectedValueOnce(new Error('RPC call failed'));

      // Should not throw an error
      await expect(
        trackContentMetrics('content-123', 'view')
      ).resolves.toBeUndefined();
    });

    it('should handle Supabase connection failures gracefully', async () => {
      require('../../src/config/supabase').getSupabase = jest.fn(() => {
        throw new Error('Supabase connection failed');
      });

      // Should not throw an error
      await expect(
        trackContentMetrics('content-123', 'view')
      ).resolves.toBeUndefined();
    });
  });

  describe('getContentRecommendations', () => {
    it('should get content recommendations based on user history', async () => {
      const mockInteractions = [
        { content_id: 'content-1', interaction_type: 'view' },
        { content_id: 'content-2', interaction_type: 'complete' },
        { content_id: 'content-3', interaction_type: 'like' },
      ];

      const mockRecommendations = [
        { id: 'content-4' },
        { id: 'content-5' },
        { id: 'content-6' },
      ];

      const interactionsQuery = createMockQueryBuilder();
      interactionsQuery.limit.mockResolvedValueOnce({ 
        data: mockInteractions, 
        error: null 
      });

      const recommendationsQuery = createMockQueryBuilder();
      recommendationsQuery.limit.mockResolvedValueOnce({ 
        data: mockRecommendations, 
        error: null 
      });

      mockSupabase.from
        .mockReturnValueOnce(interactionsQuery)
        .mockReturnValueOnce(recommendationsQuery);

      const result = await getContentRecommendations('user-123', 5);

      expect(result).toEqual(['content-4', 'content-5', 'content-6']);
      expect(mockSupabase.from).toHaveBeenCalledWith('content_interactions');
      expect(mockSupabase.from).toHaveBeenCalledWith('content_items');
      expect(interactionsQuery.eq).toHaveBeenCalledWith('user_id', 'user-123');
      expect(interactionsQuery.order).toHaveBeenCalledWith('interaction_date', { ascending: false });
      expect(recommendationsQuery.not).toHaveBeenCalledWith('id', 'in', '(content-1,content-2,content-3)');
      expect(recommendationsQuery.eq).toHaveBeenCalledWith('status', 'published');
      expect(recommendationsQuery.limit).toHaveBeenCalledWith(5);
    });

    it('should handle users with no interaction history', async () => {
      const mockRecommendations = [
        { id: 'content-1' },
        { id: 'content-2' },
      ];

      const interactionsQuery = createMockQueryBuilder();
      interactionsQuery.limit.mockResolvedValueOnce({ 
        data: [], 
        error: null 
      });

      const recommendationsQuery = createMockQueryBuilder();
      recommendationsQuery.limit.mockResolvedValueOnce({ 
        data: mockRecommendations, 
        error: null 
      });

      mockSupabase.from
        .mockReturnValueOnce(interactionsQuery)
        .mockReturnValueOnce(recommendationsQuery);

      const result = await getContentRecommendations('new-user-123');

      expect(result).toEqual(['content-1', 'content-2']);
      expect(recommendationsQuery.not).toHaveBeenCalledWith('id', 'in', '()');
    });

    it('should use default limit when not specified', async () => {
      const interactionsQuery = createMockQueryBuilder();
      interactionsQuery.limit.mockResolvedValueOnce({ data: [], error: null });

      const recommendationsQuery = createMockQueryBuilder();
      recommendationsQuery.limit.mockResolvedValueOnce({ data: [], error: null });

      mockSupabase.from
        .mockReturnValueOnce(interactionsQuery)
        .mockReturnValueOnce(recommendationsQuery);

      await getContentRecommendations('user-123');

      expect(recommendationsQuery.limit).toHaveBeenCalledWith(10);
    });

    it('should handle database errors gracefully', async () => {
      const interactionsQuery = createMockQueryBuilder();
      interactionsQuery.limit.mockRejectedValueOnce(new Error('Database error'));

      mockSupabase.from.mockReturnValueOnce(interactionsQuery);

      const result = await getContentRecommendations('user-123');

      expect(result).toEqual([]);
    });

    it('should handle Supabase connection failures', async () => {
      require('../../src/config/supabase').getSupabase = jest.fn(() => {
        throw new Error('Supabase connection failed');
      });

      const result = await getContentRecommendations('user-123');

      expect(result).toEqual([]);
    });
  });
});
