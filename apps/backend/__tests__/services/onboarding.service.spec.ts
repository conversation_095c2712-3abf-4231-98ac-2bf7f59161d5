import { OnboardingService } from '../../src/services/onboarding.service';
import { AppError } from '../../src/middleware/errorHandler';

import { Prisma } from '@prisma/client';
import { OnboardingService } from '../../src/services/onboarding.service';
import { AIService } from '../../src/services/ai.service';
import { CrisisDetectionService } from '../../src/services/crisis-detection.service';
import { AppError } from '../../src/middleware/errorHandler';
import { UserProfile } from '../../src/models/UserProfile'; // Import UserProfile
import { prisma as db } from '../../src/config/database'; // Import the actual prisma instance

// Mock dependencies
jest.mock('../../src/utils/logger');
jest.mock('../../src/services/ai.service');
jest.mock('../../src/services/crisis-detection.service');
jest.mock('../../src/config/database', () => ({
  prisma: {
    onboardingSession: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    userProfileDetailed: {
      upsert: jest.fn(),
    },
  },
}));

// Typed mock for Prisma
const mockPrisma = db as jest.Mocked<typeof db>;

// Helper function to create UserProfile mocks
const createMockUserProfile = (overrides: Partial<UserProfile> = {}): UserProfile => {
  const baseProfile: UserProfile = {
    id: 'test-profile-id',
    userId: 'test-user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    completionStatus: 'complete',
    profileVersion: '1.0',
    mentalHealthAwareness: { level: 'symptom_aware' },
    ruqyaKnowledge: { level: 'aware' },
    professionalContext: { field: 'other' },
    demographics: { ageRange: '26-35', gender: 'prefer_not_to_specify' },
    lifeCircumstances: { situations: [], islamicJourneyStage: 'practicing' },
    crisisIndicators: { level: 'none', indicators: [], immediateHelpRequested: false },
    preferences: { contentStyle: 'moderate', islamicTerminology: 'basic', learningPace: 'moderate', communityEngagement: 'low', timeAvailability: '10-20' },
    featureAccessibility: { feature1Level: 'standard', feature2Complexity: 'moderate', ruqyaIntegration: 'optional', communityAccess: 'participant', crisisSupport: 'standard'},
    learningHistory: [],
    privacySettings: { dataSharing: false, anonymityLevel: 'full' },
    spiritualOptimizer: undefined, // Explicitly undefined if not overridden
    // Add any other non-optional fields from UserProfile with sensible defaults
  };
  return { ...baseProfile, ...overrides };
};


// Mock AI Service Instance
const mockAIServiceInstance = {
  generateProfileFromOnboarding: jest.fn(),
  analyzeCrisisIndicators: jest.fn(), // This was in the old mockAIService, ensure it's on the instance if used
};

// Mock Crisis Detection Service Instance
const mockCrisisServiceInstance = {
  analyzeResponse: jest.fn(),
};

describe('OnboardingService', () => {
  let onboardingService: OnboardingService;
  let aiServiceMock: jest.Mocked<AIService>;
  let crisisServiceMock: jest.Mocked<CrisisDetectionService>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create new instances of actual services but we will mock their methods
    // The constructor of OnboardingService creates instances of AIService and CrisisDetectionService.
    // So we need to ensure these are using our mocks.
    // The easiest way is to mock the constructors of AIService and CrisisDetectionService
    // to return our controlled mock instances.
    (AIService as jest.Mock).mockImplementation(() => mockAIServiceInstance as any);
    (CrisisDetectionService as jest.Mock).mockImplementation(() => mockCrisisServiceInstance as any);
    
    onboardingService = new OnboardingService();

    // Retrieve the mocked instances that OnboardingService constructor would have created and used
    aiServiceMock = (onboardingService as any).aiService;
    crisisServiceMock = (onboardingService as any).crisisService;

  });

  describe('startOnboarding', () => {
    it('should start a new onboarding session successfully using Prisma', async () => {
      const mockDeviceInfo = { platform: 'web', userAgent: 'TestAgent' };
      const mockUserId = 'user-test-123';
      const mockCreatedSession = {
        // id: 'db_session_id', // Prisma auto-generated ID
        sessionId: expect.stringMatching(/^onb_\d+_[a-z0-9]+$/), // Service generated
        userId: mockUserId,
        startedAt: new Date(),
        currentStep: 'welcome',
        steps: [],
        totalTimeSpent: 0,
        deviceInfo: mockDeviceInfo,
        completedAt: null,
        // createdAt: new Date(), // Prisma auto-generated
        // updatedAt: new Date(), // Prisma auto-generated
      };

      mockPrisma.onboardingSession.create.mockResolvedValue(mockCreatedSession as any);

      const result = await onboardingService.startOnboarding(mockUserId, mockDeviceInfo);

      expect(result).toEqual(expect.objectContaining({
        sessionId: expect.stringMatching(/^onb_\d+_[a-z0-9]+$/),
        userId: mockUserId,
        currentStep: 'welcome',
        deviceInfo: mockDeviceInfo,
      }));
      expect(mockPrisma.onboardingSession.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: mockUserId,
          currentStep: 'welcome',
          deviceInfo: mockDeviceInfo,
          sessionId: expect.any(String),
          startedAt: expect.any(Date),
          steps: [], // Prisma expects JSON, ensure this is compatible
          totalTimeSpent: 0,
        }),
      });
    });

    it('should handle Prisma errors when starting onboarding', async () => {
      mockPrisma.onboardingSession.create.mockRejectedValue(new Error('Prisma connection failed'));

      await expect(
        onboardingService.startOnboarding('user-123')
      ).rejects.toThrow(AppError);
      await expect(
        onboardingService.startOnboarding('user-123')
      ).rejects.toThrow('Failed to start onboarding session');
    });

     it('should start onboarding without device info using Prisma', async () => {
      const mockUserId = 'user-test-456';
       const mockCreatedSession = {
        sessionId: expect.stringMatching(/^onb_\d+_[a-z0-9]+$/),
        userId: mockUserId,
        startedAt: new Date(),
        currentStep: 'welcome',
        steps: [],
        totalTimeSpent: 0,
        deviceInfo: Prisma.JsonNull, 
      };
      mockPrisma.onboardingSession.create.mockResolvedValue(mockCreatedSession as any);

      const result = await onboardingService.startOnboarding(mockUserId);

      expect(result.userId).toBe(mockUserId);
      expect(result.deviceInfo).toEqual(Prisma.JsonNull);
      expect(mockPrisma.onboardingSession.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: mockUserId,
          deviceInfo: Prisma.JsonNull,
        }),
      });
    });
  });

  describe('getNextQuestion', () => {
    const mockBaseSessionData = { // Data that Prisma would return
      id: 'db_id_123',
      sessionId: 'onb_prisma_123',
      userId: 'user-prisma',
      currentStep: 'welcome',
      steps: [], 
      totalTimeSpent: 0,
      startedAt: new Date(),
      completedAt: null,
      deviceInfo: Prisma.JsonNull,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    beforeEach(() => {
      mockPrisma.onboardingSession.findUnique.mockImplementation(async ({ where }) => {
        if (where.sessionId === mockBaseSessionData.sessionId) {
          return mockBaseSessionData; 
        }
        return null;
      });
    });

    it('should return the next question for welcome step', async () => {
      const result = await onboardingService.getNextQuestion(mockBaseSessionData.sessionId, {});

      expect(result).toMatchObject({
        step: 'mental_health_awareness',
        question: expect.objectContaining({ id: 'mental_health_awareness' }),
        progress: expect.any(Number), // Progress calculation might need its own tests
      });
       expect(mockPrisma.onboardingSession.findUnique).toHaveBeenCalledWith({
        where: { sessionId: mockBaseSessionData.sessionId },
      });
    });

    it('should handle adaptive flow based on responses (e.g. to spiritual_optimizer_clinical)', async () => {
       const sessionAtMHA = { ...mockBaseSessionData, currentStep: 'mental_health_awareness' };
       mockPrisma.onboardingSession.findUnique.mockResolvedValue(sessionAtMHA);

      const responses = { 'mental_health_awareness': { mental_health_awareness: 'clinical_integration' } };
      const determineNextStepSpy = jest.spyOn(onboardingService as any, 'determineNextStep');

      const result = await onboardingService.getNextQuestion(sessionAtMHA.sessionId, responses);
      
      expect(determineNextStepSpy).toHaveBeenCalledWith('mental_health_awareness', responses);
      expect(result.step).toBe('spiritual_optimizer_clinical');
      expect(result.question.id).toBe('spiritual_optimizer_clinical');
    });

    it('should complete onboarding when no next step (e.g., after life_circumstances)', async () => {
      const sessionAtLastStep = { ...mockBaseSessionData, currentStep: 'life_circumstances' };
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(sessionAtLastStep);

      const mockCompleteOnboardingResult = {
        profile: { id: 'prof1', userId: 'user-prisma', completionStatus: 'complete' } as any,
        recommendedPathway: 'standard_healing_journey',
        featureConfiguration: {} as any, nextSteps: [], warnings: [],
      };
      const completeOnboardingSpy = jest.spyOn(onboardingService, 'completeOnboarding').mockResolvedValue(mockCompleteOnboardingResult);
      
      jest.spyOn(onboardingService as any, 'determineNextStep').mockReturnValue(null);

      const result = await onboardingService.getNextQuestion(sessionAtLastStep.sessionId, { /* relevant responses */ });

      expect(completeOnboardingSpy).toHaveBeenCalledWith(sessionAtLastStep.sessionId);
      expect(result).toEqual(mockCompleteOnboardingResult);
    });
  });

  describe('submitResponse', () => {
    const mockSessionId = 'onb_submit_prisma';
    const mockUserId = 'user-submit';
    const initialMockSessionData = { // Prisma data
      id: 'db_submit_id', sessionId: mockSessionId, userId: mockUserId,
      currentStep: 'mental_health_awareness', steps: [], totalTimeSpent: 0,
      startedAt: new Date(), completedAt: null, deviceInfo: Prisma.JsonNull,
      createdAt: new Date(), updatedAt: new Date(),
    };

    beforeEach(() => {
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(initialMockSessionData);
      crisisServiceMock.analyzeResponse.mockResolvedValue({ isCrisis: false });
      mockPrisma.onboardingSession.update.mockResolvedValue({ ...initialMockSessionData, totalTimeSpent: 45 } as any);
    });

    it('should submit response, update session with Prisma, and get next question', async () => {
      const stepId = 'mental_health_awareness';
      const responsePayload = { mental_health_awareness: 'symptom_aware' };
      const timeSpent = 45;

      const mockNextQuestionResult = {
        step: 'ruqya_knowledge',
        question: { id: 'ruqya_knowledge', title: 'Ruqya Question' },
        progress: 40,
      };
      const getNextQuestionSpy = jest.spyOn(onboardingService, 'getNextQuestion').mockResolvedValue(mockNextQuestionResult);
      
      const result = await onboardingService.submitResponse(mockSessionId, stepId, responsePayload, timeSpent);

      expect(crisisServiceMock.analyzeResponse).toHaveBeenCalledWith(responsePayload, stepId);
      expect(mockPrisma.onboardingSession.update).toHaveBeenCalledWith({
        where: { sessionId: mockSessionId },
        data: expect.objectContaining({
          steps: expect.arrayContaining([
            expect.objectContaining({ stepId: stepId, responses: responsePayload, timeSpent: timeSpent, isCompleted: true }),
          ]),
          totalTimeSpent: initialMockSessionData.totalTimeSpent + timeSpent,
          currentStep: 'ruqya_knowledge', 
        }),
      });
      expect(getNextQuestionSpy).toHaveBeenCalledWith(mockSessionId, expect.objectContaining({ [stepId]: responsePayload }));
      expect(result).toEqual(mockNextQuestionResult);
    });

    it('should handle crisis detection during response submission (Prisma)', async () => {
      const crisisResponsePayload = { mental_health_awareness: 'crisis' };
      crisisServiceMock.analyzeResponse.mockResolvedValue({ isCrisis: true, level: 'high', indicators: ['suicidal_ideation'] });

      const mockHandleCrisisResult = { type: 'crisis_detected', level: 'high', message: 'Emergency support initiated.', actions: [{ id: 'emergency_sakina' }], nextStep: 'crisis_support' };
      jest.spyOn(onboardingService as any, 'handleCrisisDetection').mockResolvedValue(mockHandleCrisisResult);

      const result = await onboardingService.submitResponse(mockSessionId, 'mental_health_awareness', crisisResponsePayload, 30);

      expect(result).toEqual(mockHandleCrisisResult);
      expect(mockPrisma.onboardingSession.update).not.toHaveBeenCalled();
    });

    it('should handle Prisma errors when submitting response', async () => {
      mockPrisma.onboardingSession.update.mockRejectedValue(new Error('Prisma update failed'));
      await expect(onboardingService.submitResponse(mockSessionId, 'mha', {}, 30)).rejects.toThrow(AppError);
      await expect(onboardingService.submitResponse(mockSessionId, 'mha', {}, 30)).rejects.toThrow('Failed to save response');
    });
  });

  describe('completeOnboarding', () => {
    const mockSessionId = 'onb_complete_prisma';
    const mockUserId = 'user-complete';
    const mockSessionPrismaData = { // Data Prisma returns for the session
      id: 'db_comp_id', sessionId: mockSessionId, userId: mockUserId,
      currentStep: 'life_circumstances', 
      steps: [ { stepId: 'mental_health_awareness', responses: { mental_health_awareness: 'symptom_aware' }, isCompleted: true, completedAt: new Date(), timeSpent: 30 }, /* other steps */ ],
      totalTimeSpent: 180, startedAt: new Date(), completedAt: null, 
      deviceInfo: Prisma.JsonNull, createdAt: new Date(), updatedAt: new Date(),
    };

    const mockAiProfileResponse = {
        profileData: { mentalHealthAwareness: { level: 'symptom_aware' }, ruqyaKnowledge: { level: 'aware' }, professionalContext: {field: 'other'}, demographics: {ageRange: '26-35', gender: 'female'}, lifeCircumstances: {situations: [], islamicJourneyStage: 'practicing'}, crisisIndicators: {level: 'none', indicators: [], immediateHelpRequested: false}, preferences: {contentStyle: 'moderate', islamicTerminology: 'basic', learningPace: 'moderate', communityEngagement: 'low', timeAvailability: '10-20'}},
        recommendedPathway: 'ai_suggested_pathway',
        personalizationSettings: { content_complexity: 'moderate', islamic_terminology: 'basic' }
    };
    
    const expectedUserProfileStructure = {
        userId: mockUserId,
        completionStatus: 'complete',
        mentalHealthAwareness: { level: 'symptom_aware' },
        ruqyaKnowledge: { level: 'aware' },
        featureAccessibility: expect.any(Object), // mapPersonalizationToFeatureAccessibility result
        profileVersion: '1.0.0',
    };


    beforeEach(() => {
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(mockSessionPrismaData);
      aiServiceMock.generateProfileFromOnboarding.mockResolvedValue(mockAiProfileResponse as any);
      mockPrisma.userProfileDetailed.upsert.mockResolvedValue({ id: 'detail_prof_id', userId: mockUserId, profileData: {} as any, completionStatus: 'complete', profileVersion: '1.0.0', createdAt: new Date(), updatedAt: new Date() });
      mockPrisma.onboardingSession.update.mockResolvedValue({ ...mockSessionPrismaData, currentStep: 'complete', completedAt: new Date() } as any);
    });

    it('should complete onboarding, generate profile with AI, save with Prisma, and mark session complete', async () => {
      const result = await onboardingService.completeOnboarding(mockSessionId);
      
      const allResponses = (onboardingService as any).getAllResponses(mockSessionPrismaData); // Use the prisma data structure
      expect(aiServiceMock.generateProfileFromOnboarding).toHaveBeenCalledWith(allResponses, mockUserId, mockSessionId);
      
      expect(mockPrisma.userProfileDetailed.upsert).toHaveBeenCalledWith(expect.objectContaining({
          where: { userId: mockUserId },
          create: expect.objectContaining({ userId: mockUserId, profileData: expect.objectContaining(expectedUserProfileStructure) }),
          update: expect.objectContaining({ profileData: expect.objectContaining(expectedUserProfileStructure) }),
      }));

      expect(mockPrisma.onboardingSession.update).toHaveBeenCalledWith(expect.objectContaining({
          where: { sessionId: mockSessionId }, data: { completedAt: expect.any(Date), currentStep: 'complete' },
      }));
      
      expect(result.profile).toMatchObject(expectedUserProfileStructure);
      expect(result.recommendedPathway).toBe('ai_suggested_pathway');
    });
    
    it('should handle Prisma error during profile save', async () => {
        mockPrisma.userProfileDetailed.upsert.mockRejectedValue(new Error("Prisma upsert failed"));
        await expect(onboardingService.completeOnboarding(mockSessionId)).rejects.toThrow('Failed to save profile data');
    });

    it('should re-throw AppError if markSessionComplete fails with a non-P2025 Prisma error', async () => {
        const genericError = new Prisma.PrismaClientKnownRequestError("Generic Prisma Update Error", {code: "P2000", clientVersion: "test"});
        mockPrisma.onboardingSession.update.mockRejectedValue(genericError);
        await expect(onboardingService.completeOnboarding(mockSessionId)).rejects.toThrow('Failed to finalize session completion');
    });

    it('should not throw if markSessionComplete fails with P2025 (Session not found)', async () => {
        const prismaNotFoundError = new Prisma.PrismaClientKnownRequestError('Session not found', { code: 'P2025', clientVersion: 'test'});
        mockPrisma.onboardingSession.update.mockRejectedValue(prismaNotFoundError);
        const result = await onboardingService.completeOnboarding(mockSessionId); // Should not throw
        expect(result.profile).toBeDefined(); 
    });
  });


  describe('getSession (internal method using Prisma)', () => {
    it('should retrieve a session using Prisma', async () => {
      const mockSessionData = { sessionId: 's1', userId: 'u1', currentStep: 'test', steps: [], id:'dbid' } as any; // Cast to any to satisfy Prisma return type
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(mockSessionData);

      const session = await (onboardingService as any).getSession('s1');
      expect(session).toEqual(mockSessionData);
      expect(mockPrisma.onboardingSession.findUnique).toHaveBeenCalledWith({ where: { sessionId: 's1' } });
    });

    it('should throw AppError if session not found by Prisma', async () => {
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(null);
      await expect((onboardingService as any).getSession('s1')).rejects.toThrow('Onboarding session not found');
    });
  });
  
  describe('saveUserProfile (internal method using Prisma)', () => {
    it('should save/update a user profile using Prisma upsert', async () => {
        const mockProfile: UserProfile = {
            id: 'profile_user123_ts', userId: 'user123',
            mentalHealthAwareness: { level: 'symptom_aware' }, ruqyaKnowledge: { level: 'unaware' },
            professionalContext: { field: 'other' }, demographics: { ageRange: '26-35', gender: 'female' },
            lifeCircumstances: { situations: [], islamicJourneyStage: 'practicing'},
            crisisIndicators: { level: 'none', indicators: [], immediateHelpRequested: false },
            preferences: { contentStyle: 'moderate', islamicTerminology: 'basic', learningPace: 'moderate', communityEngagement: 'low', timeAvailability: '10-20' },
            featureAccessibility: { feature1Level: 'standard', feature2Complexity: 'moderate', ruqyaIntegration: 'optional', communityAccess: 'participant', crisisSupport: 'standard' },
            learningHistory: [], privacySettings: { dataSharing: false, anonymityLevel: 'full' },
            completionStatus: 'complete', profileVersion: '1.0.0', createdAt: new Date(), updatedAt: new Date(),
        };
        // Prisma upsert resolves with the created/updated record
        mockPrisma.userProfileDetailed.upsert.mockResolvedValue({ ...mockProfile, profileData: mockProfile as any, id: "db_prof_id", createdAt: new Date(), updatedAt: new Date() } as any);

        await (onboardingService as any).saveUserProfile(mockProfile);

        expect(mockPrisma.userProfileDetailed.upsert).toHaveBeenCalledWith({
            where: { userId: 'user123' },
            create: { userId: 'user123', profileData: mockProfile as any, completionStatus: 'complete', profileVersion: '1.0.0' },
            update: { profileData: mockProfile as any, completionStatus: 'complete', profileVersion: '1.0.0' },
        });
    });
  });

  describe('markSessionComplete (internal method using Prisma)', () => {
    it('should mark a session as complete using Prisma update', async () => {
        const sessionId = 'sComplete';
        mockPrisma.onboardingSession.update.mockResolvedValue({ sessionId, completedAt: new Date(), currentStep: 'complete' } as any);
        await (onboardingService as any).markSessionComplete(sessionId);
        expect(mockPrisma.onboardingSession.update).toHaveBeenCalledWith({
            where: { sessionId: sessionId }, data: { completedAt: expect.any(Date), currentStep: 'complete' },
        });
    });
  });

  describe('determineNextStep (detailed logic check)', () => {
    const service = new OnboardingService(); // Fresh instance for these specific tests

    it('welcome -> mental_health_awareness', () => {
      expect((service as any).determineNextStep('welcome', {})).toBe('mental_health_awareness');
    });
    it('mha -> mha_conditions if response is "clinical_aware"', () => {
      expect((service as any).determineNextStep('mental_health_awareness', { 'mental_health_awareness': { 'mental_health_awareness': 'clinical_aware' } })).toBe('mha_conditions');
    });
    // mental_health_awareness branching
    it('mha -> mha_conditions if mental_health_awareness response is "clinical_aware"', () => {
      const responses = { 'mental_health_awareness': { 'mental_health_awareness': 'clinical_aware' } };
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('mha_conditions');
    });
     it('mha -> mha_conditions if mental_health_awareness response is "clinical_aware" (direct string)', () => {
      const responses = { 'mental_health_awareness': 'clinical_aware' }; 
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('mha_conditions');
    });
    it('mha -> mha_experiences if "symptom_aware"', () => {
      const responses = { 'mental_health_awareness': { 'mental_health_awareness': 'symptom_aware' } };
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('mha_experiences');
    });
    it('mha -> spiritual_optimizer_clinical if "clinical_integration"', () => {
      const responses = { 'mental_health_awareness': { 'mental_health_awareness': 'clinical_integration' } };
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('spiritual_optimizer_clinical');
    });
    it('mha -> spiritual_optimizer_traditional if "traditional_bridge"', () => {
      const responses = { 'mental_health_awareness': { 'mental_health_awareness': 'traditional_bridge' } };
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('spiritual_optimizer_traditional');
    });
    it('mha -> ruqya_knowledge for "crisis" mha response', () => {
      const responses = { 'mental_health_awareness': { 'mental_health_awareness': 'crisis' } };
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('ruqya_knowledge');
    });
    it('mha -> ruqya_knowledge for "spiritual_growth" mha response', () => {
      const responses = { 'mental_health_awareness': { 'mental_health_awareness': 'spiritual_growth' } };
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('ruqya_knowledge');
    });
    it('mha -> ruqya_knowledge for "new_muslim" mha response', () => {
      const responses = { 'mental_health_awareness': { 'mental_health_awareness': 'new_muslim' } };
      expect((service as any).determineNextStep('mental_health_awareness', responses)).toBe('ruqya_knowledge');
    });
     it('mha -> ruqya_knowledge if mha response is missing or malformed', () => {
      const responsesMalformed = { 'mental_health_awareness': { 'some_other_key': 'value' } };
      expect((service as any).determineNextStep('mental_health_awareness', responsesMalformed)).toBe('ruqya_knowledge');
      const responsesMissing = {};
      expect((service as any).determineNextStep('mental_health_awareness', responsesMissing)).toBe('ruqya_knowledge');
    });

    // mha_conditions -> mha_therapy (fixed transition)
    it('mha_conditions -> mha_therapy', () => {
      expect((service as any).determineNextStep('mha_conditions', {})).toBe('mha_therapy');
    });

    // mha_therapy branching (depends on original MHA response)
    it('mha_therapy -> ruqya_knowledge (if original MHA was "clinical_aware")', () => {
        const allResponses = { 'mental_health_awareness': { 'mental_health_awareness': 'clinical_aware' } };
        expect((service as any).determineNextStep('mha_therapy', allResponses)).toBe('ruqya_knowledge');
    });
    it('mha_therapy -> spiritual_optimizer_clinical (if original MHA was "clinical_integration")', () => {
        const allResponses = { 'mental_health_awareness': { 'mental_health_awareness': 'clinical_integration' } };
        expect((service as any).determineNextStep('mha_therapy', allResponses)).toBe('spiritual_optimizer_clinical');
    });
    it('mha_therapy -> spiritual_optimizer_traditional (if original MHA was "traditional_bridge")', () => {
        const allResponses = { 'mental_health_awareness': { 'mental_health_awareness': 'traditional_bridge' } };
        expect((service as any).determineNextStep('mha_therapy', allResponses)).toBe('spiritual_optimizer_traditional');
    });
    it('mha_therapy -> default to ruqya_knowledge if original MHA response is missing or not matching special cases', () => {
        expect((service as any).determineNextStep('mha_therapy', {})).toBe('ruqya_knowledge'); // Missing
        const otherMha = { 'mental_health_awareness': { 'mental_health_awareness': 'symptom_aware' } };
        expect((service as any).determineNextStep('mha_therapy', otherMha)).toBe('ruqya_knowledge'); // Not clinical_integration or traditional_bridge
    });
    
    // mha_experiences -> mha_concepts_familiarity (fixed transition)
    it('mha_experiences -> mha_concepts_familiarity', () => {
      expect((service as any).determineNextStep('mha_experiences', {})).toBe('mha_concepts_familiarity');
    });

    // mha_concepts_familiarity branching (depends on original MHA response)
    it('mha_concepts_familiarity -> ruqya_knowledge (if original MHA was "symptom_aware")', () => {
        const allResponses = { 'mental_health_awareness': { 'mental_health_awareness': 'symptom_aware' } };
        expect((service as any).determineNextStep('mha_concepts_familiarity', allResponses)).toBe('ruqya_knowledge');
    });
    it('mha_concepts_familiarity -> spiritual_optimizer_clinical (if original MHA was "clinical_integration")', () => {
        const allResponses = { 'mental_health_awareness': { 'mental_health_awareness': 'clinical_integration' } };
        expect((service as any).determineNextStep('mha_concepts_familiarity', allResponses)).toBe('spiritual_optimizer_clinical');
    });
    it('mha_concepts_familiarity -> spiritual_optimizer_traditional (if original MHA was "traditional_bridge")', () => {
        const allResponses = { 'mental_health_awareness': { 'mental_health_awareness': 'traditional_bridge' } };
        expect((service as any).determineNextStep('mha_concepts_familiarity', allResponses)).toBe('spiritual_optimizer_traditional');
    });
     it('mha_concepts_familiarity -> default to ruqya_knowledge if original MHA response is missing or not matching special cases', () => {
        expect((service as any).determineNextStep('mha_concepts_familiarity', {})).toBe('ruqya_knowledge'); // Missing
        const otherMha = { 'mental_health_awareness': { 'mental_health_awareness': 'clinical_aware' } }; // Not clinical_integration or traditional_bridge
        expect((service as any).determineNextStep('mha_concepts_familiarity', otherMha)).toBe('ruqya_knowledge');
    });

    // spiritual_optimizer_clinical -> ruqya_knowledge (fixed transition)
    it('spiritual_optimizer_clinical -> ruqya_knowledge', () => {
      expect((service as any).determineNextStep('spiritual_optimizer_clinical', {})).toBe('ruqya_knowledge');
    });
    // spiritual_optimizer_traditional -> ruqya_knowledge (fixed transition)
    it('spiritual_optimizer_traditional -> ruqya_knowledge', () => {
      expect((service as any).determineNextStep('spiritual_optimizer_traditional', {})).toBe('ruqya_knowledge');
    });

    // ruqya_knowledge branching
    it('ruqya_knowledge -> rk_expert_aspects if "expert"', () => {
      const responses = { 'ruqya_knowledge': { 'ruqya_knowledge': 'expert' } };
      expect((service as any).determineNextStep('ruqya_knowledge', responses)).toBe('rk_expert_aspects');
    });
    it('ruqya_knowledge -> rk_practitioner_duration if "practitioner"', () => {
      const responses = { 'ruqya_knowledge': { 'ruqya_knowledge': 'practitioner' } };
      expect((service as any).determineNextStep('ruqya_knowledge', responses)).toBe('rk_practitioner_duration');
    });
    it('ruqya_knowledge -> rk_unaware_openness if "unaware"', () => {
      const responses = { 'ruqya_knowledge': { 'ruqya_knowledge': 'unaware' } };
      expect((service as any).determineNextStep('ruqya_knowledge', responses)).toBe('rk_unaware_openness');
    });
    it('ruqya_knowledge -> professional_context for "aware"', () => {
      const responses = { 'ruqya_knowledge': { 'ruqya_knowledge': 'aware' } };
      expect((service as any).determineNextStep('ruqya_knowledge', responses)).toBe('professional_context');
    });
    it('ruqya_knowledge -> professional_context for "skeptical"', () => {
      const responses = { 'ruqya_knowledge': { 'ruqya_knowledge': 'skeptical' } };
      expect((service as any).determineNextStep('ruqya_knowledge', responses)).toBe('professional_context');
    });
     it('ruqya_knowledge -> professional_context if response malformed or missing', () => {
      const responsesMalformed = { 'ruqya_knowledge': { 'some_other_key': 'value' } };
      expect((service as any).determineNextStep('ruqya_knowledge', responsesMalformed)).toBe('professional_context');
      const responsesMissing = {};
      expect((service as any).determineNextStep('ruqya_knowledge', responsesMissing)).toBe('professional_context');
    });

    // Fixed transitions from Ruqya knowledge sub-paths
    it('rk_expert_aspects -> rk_expert_tools', () => { expect((service as any).determineNextStep('rk_expert_aspects', {})).toBe('rk_expert_tools'); });
    it('rk_expert_tools -> professional_context', () => { expect((service as any).determineNextStep('rk_expert_tools', {})).toBe('professional_context'); });
    it('rk_practitioner_duration -> professional_context', () => { expect((service as any).determineNextStep('rk_practitioner_duration', {})).toBe('professional_context'); });
    it('rk_unaware_openness -> rk_unaware_comfort', () => { expect((service as any).determineNextStep('rk_unaware_openness', {})).toBe('rk_unaware_comfort'); });
    it('rk_unaware_comfort -> professional_context', () => { expect((service as any).determineNextStep('rk_unaware_comfort', {})).toBe('professional_context'); });
    
    // Fixed transitions from professional_context and demographics
    it('professional_context -> pc_work_challenges', () => { expect((service as any).determineNextStep('professional_context', {})).toBe('pc_work_challenges'); });
    it('pc_work_challenges -> demographics', () => { expect((service as any).determineNextStep('pc_work_challenges', {})).toBe('demographics'); });
    it('demographics -> life_circumstances', () => { expect((service as any).determineNextStep('demographics', {})).toBe('life_circumstances'); });
    
    // End of flow
    it('life_circumstances -> null (end of onboarding)', () => {
      expect((service as any).determineNextStep('life_circumstances', {})).toBeNull();
    });

    // Unknown step
    it('unknown_step -> null', () => {
      expect((service as any).determineNextStep('some_unknown_step_id', {})).toBeNull();
    });
  });

  describe('determineRecommendedPathway (logic check)', () => {
    const service = new OnboardingService(); // Re-use instance from outer scope or create new if needed

    it('should recommend crisis_support for high crisis indicators', () => {
      const profile = createMockUserProfile({ crisisIndicators: { level: 'high', indicators: [], immediateHelpRequested: false } });
      expect((service as any).determineRecommendedPathway(profile)).toBe('crisis_support');
    });
    it('should recommend crisis_support for critical crisis indicators', () => {
      const profile = createMockUserProfile({ crisisIndicators: { level: 'critical', indicators: [], immediateHelpRequested: false } });
      expect((service as any).determineRecommendedPathway(profile)).toBe('crisis_support');
    });
    it('should use AI recommended pathway if available and crisis is not high/critical', () => {
        const profile = createMockUserProfile({ crisisIndicators: { level: 'moderate', indicators: [], immediateHelpRequested: false } });
        const aiPathway = "ai_path_recommendation";
        expect((service as any).determineRecommendedPathway(profile, aiPathway)).toBe(aiPathway);
    });
    it('AI pathway should be overridden by high/critical crisis', () => {
        const profile = createMockUserProfile({ crisisIndicators: { level: 'high', indicators: [], immediateHelpRequested: false } });
        const aiPathway = "ai_path_should_be_ignored";
        expect((service as any).determineRecommendedPathway(profile, aiPathway)).toBe('crisis_support');
    });
    it('should recommend clinical_islamic_integration for spiritual optimizer clinical type (no crisis)', () => {
      const profile = createMockUserProfile({ spiritualOptimizer: { type: 'clinical_integration', goals:[], islamicPsychologyLevel: 'basic' } });
      expect((service as any).determineRecommendedPathway(profile)).toBe('clinical_islamic_integration');
    });
    it('should recommend traditional_modern_bridge for spiritual optimizer traditional type (no crisis)', () => {
      const profile = createMockUserProfile({ spiritualOptimizer: { type: 'traditional_bridge', goals: [], clinicalComfort: 'limited' } });
      expect((service as any).determineRecommendedPathway(profile)).toBe('traditional_modern_bridge');
    });
    it('should recommend advanced_clinical_ruqya for clinically_aware and expert ruqya (no crisis)', () => {
      const profile = createMockUserProfile({ 
        mentalHealthAwareness: { level: 'clinically_aware' }, 
        ruqyaKnowledge: { level: 'expert' } 
      });
      expect((service as any).determineRecommendedPathway(profile)).toBe('advanced_clinical_ruqya');
    });
    it('should recommend gentle_introduction for symptom_aware and unaware ruqya (no crisis)', () => {
      const profile = createMockUserProfile({ 
        mentalHealthAwareness: { level: 'symptom_aware' }, 
        ruqyaKnowledge: { level: 'unaware' } 
      });
      expect((service as any).determineRecommendedPathway(profile)).toBe('gentle_introduction');
    });
    it('should default to standard_healing_journey for other cases (no crisis)', () => {
      const profile = createMockUserProfile({ /* Using defaults from helper */ }); 
      expect((service as any).determineRecommendedPathway(profile)).toBe('standard_healing_journey');
    });
  });
  
  describe('generateUserProfile internal logic', () => {
    const mockUserId = 'user-gen-prof';
    const mockSessionId = 'sess-gen-prof';
    const baseResponses = {
      'mental_health_awareness': { 'mental_health_awareness': 'symptom_aware' },
      'ruqya_knowledge': { 'ruqya_knowledge': 'unaware' },
    };

    it('should correctly map AI response and defaults to UserProfile', async () => {
      const mockAiResp = {
        profileData: {
          demographics: { ageRange: '18-25', gender: 'male' },
          preferences: { contentStyle: 'simple', islamicTerminology: 'minimal' },
          // other fields AI might return
        },
        recommendedPathway: 'ai_path',
        personalizationSettings: { content_complexity: 'simple', islamic_terminology: 'basic' } // for featureAccessibility
      };
      aiServiceMock.generateProfileFromOnboarding.mockResolvedValue(mockAiResp as any);

      const { profile, recommendedPathway, featureConfiguration } = await (onboardingService as any).generateUserProfile(mockSessionId, mockUserId, baseResponses);

      expect(aiServiceMock.generateProfileFromOnboarding).toHaveBeenCalledWith(baseResponses, mockUserId, mockSessionId);
      expect(profile.userId).toBe(mockUserId);
      expect(profile.demographics.ageRange).toBe('18-25');
      expect(profile.preferences.contentStyle).toBe('simple');
      expect(profile.completionStatus).toBe('complete');
      expect(profile.featureAccessibility.feature1Level).toBe('basic'); // from mapPersonalizationToFeatureAccessibility
      expect(recommendedPathway).toBe('ai_path');
      expect(featureConfiguration).toEqual(profile.featureAccessibility);
    });

    it('should use emptyProfile defaults if AI provides minimal data', async () => {
      const mockAiResp = {
        profileData: { }, // AI returns very little
        recommendedPathway: 'ai_minimal_path',
        personalizationSettings: null // No personalization from AI
      };
      aiServiceMock.generateProfileFromOnboarding.mockResolvedValue(mockAiResp as any);
      const emptyProfileDefaults = createEmptyProfile(mockUserId);


      const { profile } = await (onboardingService as any).generateUserProfile(mockSessionId, mockUserId, baseResponses);
      
      expect(profile.mentalHealthAwareness.level).toBe(emptyProfileDefaults.mentalHealthAwareness.level);
      expect(profile.ruqyaKnowledge.level).toBe(emptyProfileDefaults.ruqyaKnowledge.level);
      expect(profile.demographics.ageRange).toBe(emptyProfileDefaults.demographics.ageRange); // e.g. '26-35'
      expect(profile.featureAccessibility.crisisSupport).toBe('standard'); // Default from mapPersonalization
    });
  });

  describe('mapPersonalizationToFeatureAccessibility internal logic', () => {
    const service = new OnboardingService();
    const defaultAccess: FeatureAccessibility = { feature1Level: 'standard', feature2Complexity: 'moderate', ruqyaIntegration: 'optional', communityAccess: 'participant', crisisSupport: 'standard'};

    it('should return defaults if no personalizationSettings provided', () => {
      expect((service as any).mapPersonalizationToFeatureAccessibility(null, undefined)).toEqual(defaultAccess);
    });
    it('should map content_complexity "detailed" to advanced features', () => {
      const settings = { content_complexity: 'detailed' };
      const access = (service as any).mapPersonalizationToFeatureAccessibility(settings, undefined);
      expect(access.feature1Level).toBe('advanced');
      expect(access.feature2Complexity).toBe('comprehensive');
    });
    it('should map content_complexity "simple" to basic features', () => {
      const settings = { content_complexity: 'simple' };
      const access = (service as any).mapPersonalizationToFeatureAccessibility(settings, undefined);
      expect(access.feature1Level).toBe('basic');
      expect(access.feature2Complexity).toBe('simple');
    });
    it('should map islamic_terminology "extensive" to advanced ruqya', () => {
      const settings = { islamic_terminology: 'extensive' };
      const access = (service as any).mapPersonalizationToFeatureAccessibility(settings, undefined);
      expect(access.ruqyaIntegration).toBe('advanced');
    });
    it('should map islamic_terminology "basic" to no ruqya', () => {
      const settings = { islamic_terminology: 'basic' };
      const access = (service as any).mapPersonalizationToFeatureAccessibility(settings, undefined);
      expect(access.ruqyaIntegration).toBe('none');
    });
    it('should set intensive crisisSupport for "critical" crisis level', () => {
      const crisis: CrisisIndicators = { level: 'critical', indicators: [], immediateHelpRequested: false };
      const access = (service as any).mapPersonalizationToFeatureAccessibility({}, crisis);
      expect(access.crisisSupport).toBe('intensive');
    });
    it('should set enhanced crisisSupport for "moderate" crisis level', () => {
      const crisis: CrisisIndicators = { level: 'moderate', indicators: [], immediateHelpRequested: false };
      const access = (service as any).mapPersonalizationToFeatureAccessibility({}, crisis);
      expect(access.crisisSupport).toBe('enhanced');
    });
  });
  
  describe('getAllResponses internal logic', () => {
    it('should correctly aggregate responses from session steps', () => {
      const session = {
        steps: [
          { stepId: 'step1', responses: { answer: 'A' } },
          { stepId: 'step2', responses: { choice: 'B' } },
          { stepId: 'step1', responses: { answer: 'A_updated' } }, // Test if later steps overwrite earlier if IDs collide (they shouldn't by design)
        ]
      } as any;
      const responses = (onboardingService as any).getAllResponses(session);
      expect(responses).toEqual({
        'step1': { answer: 'A_updated' }, // Last one wins if IDs are same (though stepIds should be unique in flow)
        'step2': { choice: 'B' },
      });
    });
  });

  describe('getQuestionForStep internal logic', () => {
    it('should return null for an invalid stepId', () => {
      expect((onboardingService as any).getQuestionForStep('invalid_step_id', {})).toBeNull();
    });
    it('should return question object for a valid stepId (e.g. welcome)', () => {
      const question = (onboardingService as any).getQuestionForStep('welcome', {});
      expect(question).not.toBeNull();
      expect(question.id).toBe('welcome');
    });
  });


  describe('Error Handling (Prisma specific)', () => {
    it('getSession should throw AppError with specific message if Prisma findUnique fails unexpectedly', async () => {
      mockPrisma.onboardingSession.findUnique.mockRejectedValue(new Error('DB connection error'));
      await expect((onboardingService as any).getSession('s1')).rejects.toThrow('Failed to retrieve session data');
    });

    it('saveUserProfile should throw AppError if Prisma upsert fails', async () => {
        mockPrisma.userProfileDetailed.upsert.mockRejectedValue(new Error('DB upsert error'));
        const dummyProfile = createEmptyProfile('user1') as UserProfile;
        await expect((onboardingService as any).saveUserProfile(dummyProfile)).rejects.toThrow('Failed to save profile data');
    });
  });
});
