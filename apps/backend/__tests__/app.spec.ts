/**
 * Main App E2E Smoke Tests
 * Tests application initialization and basic endpoints
 */

import request from 'supertest';
import app from '../src/main';

describe('Main App E2E Smoke Tests', () => {
  describe('Application Initialization', () => {
    it('should initialize without errors', () => {
      expect(app).toBeDefined();
      expect(typeof app).toBe('function');
    });

    it('should have Express app properties', () => {
      expect(app.listen).toBeDefined();
      expect(app.use).toBeDefined();
      expect(app.get).toBeDefined();
      expect(app.post).toBeDefined();
    });
  });

  describe('Health Check Endpoint', () => {
    it('should respond to health check', async () => {
      const response = await request(app).get('/health');

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        message: 'Qalb Healing API is running',
        timestamp: expect.any(String),
        version: '1.0.0',
      });
    });

    it('should return valid timestamp format', async () => {
      const response = await request(app).get('/health');

      const timestamp = new Date(response.body.timestamp);
      expect(timestamp).toBeInstanceOf(Date);
      expect(timestamp.getTime()).not.toBeNaN();
    });

    it('should respond quickly', async () => {
      const startTime = Date.now();
      await request(app).get('/health');
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should respond within 1 second
    });
  });

  describe('API Routes', () => {
    it('should have auth routes mounted', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'password' });

      // Should not return 404 (route exists)
      expect(response.status).not.toBe(404);
    });

    it('should have assessment routes mounted', async () => {
      const response = await request(app)
        .post('/api/assessment/start')
        .send({});

      // Should not return 404 (route exists), but should return 401 (auth required)
      expect(response.status).not.toBe(404);
      expect(response.status).toBe(401);
    });

    it('should have onboarding routes mounted', async () => {
      const response = await request(app)
        .get('/api/onboarding/questions')
        .send({});

      // Should not return 404 (route exists)
      expect(response.status).not.toBe(404);
    });

    it('should have journey routes mounted', async () => {
      const response = await request(app)
        .get('/api/journey/user-journeys')
        .send({});

      // Should not return 404 (route exists), but should return 401 (auth required)
      expect(response.status).not.toBe(404);
      expect(response.status).toBe(401);
    });

    it('should have emergency routes mounted', async () => {
      const response = await request(app)
        .get('/api/emergency/resources')
        .send({});

      // Should not return 404 (route exists)
      expect(response.status).not.toBe(404);
    });

    it('should have content routes mounted', async () => {
      const response = await request(app)
        .get('/api/content/featured')
        .send({});

      // Should not return 404 (route exists)
      expect(response.status).not.toBe(404);
    });

    it('should have analytics routes mounted', async () => {
      const response = await request(app)
        .get('/api/analytics/dashboard')
        .send({});

      // Should not return 404 (route exists), but should return 401 (auth required)
      expect(response.status).not.toBe(404);
      expect(response.status).toBe(401);
    });

    it('should have community routes mounted', async () => {
      const response = await request(app)
        .get('/api/community/posts')
        .send({});

      // Should not return 404 (route exists)
      expect(response.status).not.toBe(404);
    });
  });

  describe('404 Handler', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app).get('/api/non-existent-route');

      expect(response.status).toBe(404);
      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Route not found',
      });
    });

    it('should return 404 for non-existent nested routes', async () => {
      const response = await request(app).get('/api/auth/non-existent');

      expect(response.status).toBe(404);
      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Route not found',
      });
    });

    it('should return 404 for invalid HTTP methods', async () => {
      const response = await request(app).patch('/health');

      expect(response.status).toBe(404);
    });
  });

  describe('CORS Configuration', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .get('/health')
        .set('Origin', 'http://localhost:3000');

      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });

    it('should handle preflight requests', async () => {
      const response = await request(app)
        .options('/api/auth/login')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST');

      expect(response.status).toBe(204);
      expect(response.headers['access-control-allow-methods']).toBeDefined();
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app).get('/health');

      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
    });

    it('should compress responses', async () => {
      const response = await request(app)
        .get('/health')
        .set('Accept-Encoding', 'gzip');

      // Response should be compressed or at least have compression headers
      expect(response.headers['content-encoding'] || response.headers['vary']).toBeDefined();
    });
  });

  describe('Request Parsing', () => {
    it('should parse JSON requests', async () => {
      const testData = { test: 'data', number: 123 };
      
      const response = await request(app)
        .post('/api/auth/signup')
        .send(testData);

      // Should not return 400 for JSON parsing error
      expect(response.status).not.toBe(400);
      // Should return validation error instead (email/password required)
      expect(response.body.status).toBe('fail');
    });

    it('should handle large JSON payloads', async () => {
      const largeData = {
        content: 'x'.repeat(1000000), // 1MB of data
      };

      const response = await request(app)
        .post('/api/auth/signup')
        .send(largeData);

      // Should handle large payloads (up to 10MB limit)
      expect(response.status).not.toBe(413); // Payload too large
    });

    it('should parse URL-encoded requests', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .type('form')
        .send('email=<EMAIL>&password=password123');

      // Should parse form data correctly
      expect(response.status).not.toBe(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('{"malformed": json}');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('status', 'error');
    });

    it('should handle missing Content-Type header', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send('some data');

      // Should not crash the application
      expect(response.status).toBeLessThan(500);
    });

    it('should handle very long URLs', async () => {
      const longPath = '/api/' + 'a'.repeat(2000);
      
      const response = await request(app).get(longPath);

      // Should handle gracefully (either 404 or 414 URI Too Long)
      expect([404, 414]).toContain(response.status);
    });
  });

  describe('API Documentation', () => {
    it('should serve Swagger documentation', async () => {
      const response = await request(app).get('/api-docs/');

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('text/html');
    });

    it('should serve Swagger JSON spec', async () => {
      const response = await request(app).get('/api-docs/swagger.json');

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('application/json');
    });
  });

  describe('Performance', () => {
    it('should respond to multiple concurrent requests', async () => {
      const requests = Array(10).fill(null).map(() => 
        request(app).get('/health')
      );

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    it('should handle rapid sequential requests', async () => {
      const responses = [];
      
      for (let i = 0; i < 5; i++) {
        const response = await request(app).get('/health');
        responses.push(response);
      }

      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });
});
