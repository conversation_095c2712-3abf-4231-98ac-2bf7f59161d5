/**
 * Error Handler Middleware Unit Tests
 * Tests error handling middleware with req/res/next mocks
 */

import { Request, Response, NextFunction } from 'express';
import { AppError, errorHandler } from '../../src/middleware/errorHandler';

describe('Error Handler Middleware Unit Tests', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockJson: jest.Mock;
  let mockStatus: jest.Mock;

  beforeEach(() => {
    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    
    mockRequest = {
      url: '/api/test',
      method: 'POST',
      ip: '127.0.0.1',
      get: jest.fn().mockReturnValue('Mozilla/5.0 Test Browser'),
    };
    
    mockResponse = {
      status: mockStatus,
      json: mockJson,
    };
    
    mockNext = jest.fn();
    
    // Reset environment
    delete process.env.NODE_ENV;
  });

  describe('AppError Class', () => {
    it('should create AppError with correct properties', () => {
      const error = new AppError('Test error message', 400);
      
      expect(error.message).toBe('Test error message');
      expect(error.statusCode).toBe(400);
      expect(error.status).toBe('fail');
      expect(error.isOperational).toBe(true);
      expect(error.name).toBe('Error');
    });

    it('should set status to "error" for 5xx status codes', () => {
      const error = new AppError('Server error', 500);
      
      expect(error.status).toBe('error');
      expect(error.statusCode).toBe(500);
    });

    it('should set status to "fail" for 4xx status codes', () => {
      const error = new AppError('Client error', 404);
      
      expect(error.status).toBe('fail');
      expect(error.statusCode).toBe(404);
    });

    it('should capture stack trace', () => {
      const error = new AppError('Test error', 400);
      
      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('AppError');
    });
  });

  describe('errorHandler Function', () => {
    describe('Development Environment', () => {
      beforeEach(() => {
        process.env.NODE_ENV = 'development';
      });

      it('should handle AppError with full details in development', () => {
        const error = new AppError('Validation failed', 400);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockStatus).toHaveBeenCalledWith(400);
        expect(mockJson).toHaveBeenCalledWith({
          status: 'fail',
          error: error,
          message: 'Validation failed',
          stack: error.stack,
        });
        expect(global.mockLogger.error).toHaveBeenCalledWith(
          'Error occurred:',
          expect.objectContaining({
            message: 'Validation failed',
            stack: error.stack,
            url: '/api/test',
            method: 'POST',
            ip: '127.0.0.1',
            userAgent: 'Mozilla/5.0 Test Browser',
          })
        );
      });

      it('should handle generic Error with full details in development', () => {
        const error = new Error('Generic error');
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockStatus).toHaveBeenCalledWith(500);
        expect(mockJson).toHaveBeenCalledWith({
          status: 'error',
          error: error,
          message: 'Generic error',
          stack: error.stack,
        });
      });

      it('should handle error without stack trace in development', () => {
        const error = new AppError('No stack error', 422);
        delete error.stack;
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockJson).toHaveBeenCalledWith({
          status: 'fail',
          error: error,
          message: 'No stack error',
          stack: undefined,
        });
      });
    });

    describe('Production Environment', () => {
      beforeEach(() => {
        process.env.NODE_ENV = 'production';
      });

      it('should handle AppError with limited details in production', () => {
        const error = new AppError('Validation failed', 400);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockStatus).toHaveBeenCalledWith(400);
        expect(mockJson).toHaveBeenCalledWith({
          status: 'fail',
          message: 'Validation failed',
        });
        expect(global.mockLogger.error).toHaveBeenCalled();
      });

      it('should hide 500 error details in production', () => {
        const error = new Error('Database connection failed');
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockStatus).toHaveBeenCalledWith(500);
        expect(mockJson).toHaveBeenCalledWith({
          status: 'error',
          message: 'Something went wrong!',
        });
      });

      it('should show 4xx error messages in production', () => {
        const error = new AppError('User not found', 404);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockStatus).toHaveBeenCalledWith(404);
        expect(mockJson).toHaveBeenCalledWith({
          status: 'fail',
          message: 'User not found',
        });
      });

      it('should handle generic 500 errors in production', () => {
        const error = new AppError('Internal server error', 500);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockStatus).toHaveBeenCalledWith(500);
        expect(mockJson).toHaveBeenCalledWith({
          status: 'error',
          message: 'Something went wrong!',
        });
      });
    });

    describe('Test Environment', () => {
      beforeEach(() => {
        process.env.NODE_ENV = 'test';
      });

      it('should handle errors in test environment like development', () => {
        const error = new AppError('Test error', 400);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockJson).toHaveBeenCalledWith({
          status: 'fail',
          error: error,
          message: 'Test error',
          stack: error.stack,
        });
      });
    });

    describe('Logging Behavior', () => {
      it('should log all error details', () => {
        const error = new AppError('Test error', 400);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(global.mockLogger.error).toHaveBeenCalledWith(
          'Error occurred:',
          {
            message: 'Test error',
            stack: error.stack,
            url: '/api/test',
            method: 'POST',
            ip: '127.0.0.1',
            userAgent: 'Mozilla/5.0 Test Browser',
          }
        );
      });

      it('should handle missing request properties gracefully', () => {
        const error = new AppError('Test error', 400);
        const minimalRequest = {} as Request;
        
        errorHandler(error, minimalRequest, mockResponse as Response, mockNext);
        
        expect(global.mockLogger.error).toHaveBeenCalledWith(
          'Error occurred:',
          expect.objectContaining({
            message: 'Test error',
            stack: error.stack,
          })
        );
      });

      it('should handle missing User-Agent header', () => {
        const error = new AppError('Test error', 400);
        (mockRequest.get as jest.Mock).mockReturnValue(undefined);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(global.mockLogger.error).toHaveBeenCalledWith(
          'Error occurred:',
          expect.objectContaining({
            userAgent: undefined,
          })
        );
      });
    });

    describe('Edge Cases', () => {
      it('should handle error without message', () => {
        const error = new AppError('', 400);
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockJson).toHaveBeenCalledWith(
          expect.objectContaining({
            message: '',
          })
        );
      });

      it('should handle error with undefined statusCode', () => {
        const error = new Error('Generic error') as any;
        delete error.statusCode;
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockStatus).toHaveBeenCalledWith(500);
      });

      it('should handle error with undefined status', () => {
        const error = new Error('Generic error') as any;
        error.statusCode = 400;
        delete error.status;
        
        errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        
        expect(mockJson).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'error',
          })
        );
      });

      it('should handle null error object', () => {
        const error = null as any;
        
        expect(() => {
          errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        }).not.toThrow();
      });

      it('should handle error with circular references in development', () => {
        const error = new AppError('Circular error', 400) as any;
        error.circular = error; // Create circular reference
        
        process.env.NODE_ENV = 'development';
        
        expect(() => {
          errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
        }).not.toThrow();
        
        expect(mockStatus).toHaveBeenCalledWith(400);
      });
    });
  });
});
