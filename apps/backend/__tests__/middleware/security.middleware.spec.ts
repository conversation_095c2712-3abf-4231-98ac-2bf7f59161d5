/**
 * Security Middleware Unit Tests
 * Tests security middleware with req/res/next mocks
 */

import { Request, Response, NextFunction } from 'express';
import { securityMiddleware, authLimiter } from '../../src/middleware/security';

describe('Security Middleware Unit Tests', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      ip: '127.0.0.1',
      headers: {},
      body: {},
      query: {},
      params: {},
    };
    
    mockResponse = {
      setHeader: jest.fn(),
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    
    mockNext = jest.fn();
  });

  describe('securityMiddleware', () => {
    it('should apply security headers', () => {
      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-Content-Type-Options',
        'nosniff'
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-Frame-Options',
        'DENY'
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-XSS-Protection',
        '1; mode=block'
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('should sanitize request body', () => {
      mockRequest.body = {
        message: '<script>alert("xss")</script>Hello',
        nested: {
          content: '<img src="x" onerror="alert(1)">',
        },
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.body.message).not.toContain('<script>');
      expect(mockRequest.body.nested.content).not.toContain('onerror');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should sanitize query parameters', () => {
      mockRequest.query = {
        search: '<script>alert("xss")</script>',
        filter: 'normal text',
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.query.search).not.toContain('<script>');
      expect(mockRequest.query.filter).toBe('normal text');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should validate email format in body', () => {
      mockRequest.body = {
        email: 'invalid-email-format',
        validEmail: '<EMAIL>',
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('Invalid email format'),
          statusCode: 400,
        })
      );
    });

    it('should allow valid email formats', () => {
      mockRequest.body = {
        email: '<EMAIL>',
        name: 'John Doe',
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(); // No error
    });

    it('should handle requests without body', () => {
      delete mockRequest.body;

      expect(() => {
        securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);
      }).not.toThrow();

      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle requests with null body', () => {
      mockRequest.body = null;

      expect(() => {
        securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);
      }).not.toThrow();

      expect(mockNext).toHaveBeenCalled();
    });

    it('should prevent SQL injection patterns', () => {
      mockRequest.body = {
        query: "'; DROP TABLE users; --",
        search: "1' OR '1'='1",
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('Potentially malicious input detected'),
          statusCode: 400,
        })
      );
    });

    it('should allow safe SQL-like content', () => {
      mockRequest.body = {
        content: "This is a normal text with some 'quotes' and numbers like 1=1 in context",
        title: 'My Article Title',
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(); // No error for safe content
    });

    it('should handle deeply nested objects', () => {
      mockRequest.body = {
        level1: {
          level2: {
            level3: {
              malicious: '<script>alert("deep")</script>',
              safe: 'normal text',
            },
          },
        },
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.body.level1.level2.level3.malicious).not.toContain('<script>');
      expect(mockRequest.body.level1.level2.level3.safe).toBe('normal text');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle arrays in request body', () => {
      mockRequest.body = {
        items: [
          '<script>alert("xss")</script>',
          'safe item',
          { nested: '<img src="x" onerror="alert(1)">' },
        ],
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.body.items[0]).not.toContain('<script>');
      expect(mockRequest.body.items[1]).toBe('safe item');
      expect(mockRequest.body.items[2].nested).not.toContain('onerror');
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('authLimiter', () => {
    it('should be configured with correct rate limits', () => {
      expect(authLimiter).toBeDefined();
      // Rate limiter configuration is tested through integration tests
      // as it involves Redis and complex middleware setup
    });

    it('should allow requests within rate limit', async () => {
      // Mock rate limiter to allow request
      const mockRateLimiter = jest.fn((req, res, next) => next());
      
      await mockRateLimiter(mockRequest, mockResponse, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
    });

    it('should block requests exceeding rate limit', async () => {
      // Mock rate limiter to block request
      const mockRateLimiter = jest.fn((req, res, next) => {
        res.status(429).json({ error: 'Too many requests' });
      });
      
      await mockRateLimiter(mockRequest, mockResponse, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(429);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Too many requests' });
    });
  });

  describe('Input Validation Edge Cases', () => {
    it('should handle very long strings', () => {
      const longString = 'a'.repeat(10000);
      mockRequest.body = {
        content: longString,
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('Input too long'),
          statusCode: 400,
        })
      );
    });

    it('should handle special characters safely', () => {
      mockRequest.body = {
        content: 'Text with émojis 🎉 and spëcial chars: àáâãäå',
        arabic: 'النص العربي',
        symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?',
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.body.content).toContain('🎉');
      expect(mockRequest.body.arabic).toContain('النص');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle numeric values', () => {
      mockRequest.body = {
        age: 25,
        price: 99.99,
        count: 0,
        negative: -10,
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.body.age).toBe(25);
      expect(mockRequest.body.price).toBe(99.99);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle boolean values', () => {
      mockRequest.body = {
        isActive: true,
        isDeleted: false,
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.body.isActive).toBe(true);
      expect(mockRequest.body.isDeleted).toBe(false);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle null and undefined values', () => {
      mockRequest.body = {
        nullValue: null,
        undefinedValue: undefined,
        emptyString: '',
      };

      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.body.nullValue).toBeNull();
      expect(mockRequest.body.undefinedValue).toBeUndefined();
      expect(mockRequest.body.emptyString).toBe('');
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('Performance and Error Handling', () => {
    it('should handle malformed JSON gracefully', () => {
      // Simulate malformed request that somehow passed JSON parsing
      mockRequest.body = { toString: () => { throw new Error('Malformed'); } };

      expect(() => {
        securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);
      }).not.toThrow();

      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle circular references', () => {
      const circular: any = { name: 'test' };
      circular.self = circular;
      mockRequest.body = circular;

      expect(() => {
        securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);
      }).not.toThrow();

      expect(mockNext).toHaveBeenCalled();
    });

    it('should complete within reasonable time for large objects', () => {
      const largeObject: any = {};
      for (let i = 0; i < 1000; i++) {
        largeObject[`key${i}`] = `value${i}`;
      }
      mockRequest.body = largeObject;

      const startTime = Date.now();
      securityMiddleware(mockRequest as Request, mockResponse as Response, mockNext);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
      expect(mockNext).toHaveBeenCalled();
    });
  });
});
