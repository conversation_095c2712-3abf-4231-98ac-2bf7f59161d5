/**
 * Auth Middleware Unit Tests
 * Tests authentication middleware with req/res/next mocks
 */

import { Request, Response, NextFunction } from 'express';
import {
  authMiddleware,
  authenticateToken,
  authenticateUser,
} from '../../src/middleware/auth';
import { getSupabase } from '../../src/config/supabase';
import { AppError } from '../../src/middleware/errorHandler';

// Mock the Supabase config
jest.mock('../../src/config/supabase');

describe('Auth Middleware Unit Tests', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockSupabase: any;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      user: undefined,
    };
    mockResponse = {};
    mockNext = jest.fn();
    mockSupabase = global.mockSupabaseClient;
    (getSupabase as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('authMiddleware', () => {
    it('should authenticate user with valid Bearer token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer valid-token-123',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('valid-token-123');
      expect(mockRequest.user).toEqual(global.testUser);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should reject request without authorization header', async () => {
      mockRequest.headers = {};

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'No token provided',
          statusCode: 401,
        })
      );
      expect(mockRequest.user).toBeUndefined();
    });

    it('should reject request with malformed authorization header', async () => {
      mockRequest.headers = {
        authorization: 'InvalidFormat token-123',
      };

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'No token provided',
          statusCode: 401,
        })
      );
      expect(mockRequest.user).toBeUndefined();
    });

    it('should reject request with Bearer but no token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer ',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid or expired token',
          statusCode: 401,
        })
      );
    });

    it('should reject request with invalid token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('invalid-token');
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid or expired token',
          statusCode: 401,
        })
      );
      expect(mockRequest.user).toBeUndefined();
    });

    it('should reject request with expired token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer expired-token',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: null,
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid or expired token',
          statusCode: 401,
        })
      );
      expect(mockRequest.user).toBeUndefined();
    });

    it('should handle Supabase service errors', async () => {
      mockRequest.headers = {
        authorization: 'Bearer valid-token',
      };

      mockSupabase.auth.getUser.mockRejectedValueOnce(
        new Error('Supabase service unavailable')
      );

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Supabase service unavailable',
        })
      );
      expect(mockRequest.user).toBeUndefined();
    });

    it('should handle network timeout errors', async () => {
      mockRequest.headers = {
        authorization: 'Bearer valid-token',
      };

      const timeoutError = new Error('Network timeout');
      timeoutError.name = 'TimeoutError';
      mockSupabase.auth.getUser.mockRejectedValueOnce(timeoutError);

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(timeoutError);
      expect(mockRequest.user).toBeUndefined();
    });

    it('should extract token correctly from complex authorization header', async () => {
      mockRequest.headers = {
        authorization:
          'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockSupabase.auth.getUser).toHaveBeenCalledWith(
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
      );
      expect(mockRequest.user).toEqual(global.testUser);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should handle case-insensitive Bearer token', async () => {
      mockRequest.headers = {
        authorization: 'bearer valid-token-123',
      };

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'No token provided',
          statusCode: 401,
        })
      );
    });
  });

  describe('Middleware Aliases', () => {
    it('should have authenticateToken as alias for authMiddleware', () => {
      expect(authenticateToken).toBe(authMiddleware);
    });

    it('should have authenticateUser as alias for authMiddleware', () => {
      expect(authenticateUser).toBe(authMiddleware);
    });

    it('should work with authenticateToken alias', async () => {
      mockRequest.headers = {
        authorization: 'Bearer valid-token',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      await authenticateToken(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.user).toEqual(global.testUser);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should work with authenticateUser alias', async () => {
      mockRequest.headers = {
        authorization: 'Bearer valid-token',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      await authenticateUser(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.user).toEqual(global.testUser);
      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty string token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer ',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid or expired token',
          statusCode: 401,
        })
      );
    });

    it('should handle whitespace-only token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer    ',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid or expired token',
          statusCode: 401,
        })
      );
    });

    it('should handle multiple Bearer keywords', async () => {
      mockRequest.headers = {
        authorization: 'Bearer Bearer token-123',
      };

      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('Bearer');
      expect(mockRequest.user).toEqual(global.testUser);
    });
  });
});
