/**
 * Assessment Controller Integration Tests
 * Tests assessment endpoints via HTTP requests
 */

/**
 * Assessment Controller Integration Tests
 * Tests assessment endpoints via HTTP requests
 */
import request from 'supertest';
import { app } from '../../src/main'; // Adjust if your app export is different
import { Request, Response } from 'express';
import { assessment<PERSON>ontroller, AssessmentController } from '../../src/controllers/assessment.controller';
// assessmentService is imported by the controller.
// For integration tests using request(app), we rely on mocks for assessmentService's
// own dependencies (like Supabase/Prisma, other services) as configured in setup.ts.

// Note: assessmentService, AppError, PrismaClient are not directly used in this spec file after refactor.
// logger is mocked in setup.ts

// Global mocks from setup.ts will be used:
// - mockSupabaseClient (via getSupabase)
// - mockAiService
// - mockCrisisDetectionService
// - mockLogger

// jest.mock('../../src/utils/logger', () => ({ // Already mocked in setup.ts
//   logger: {
//     info: jest.fn(),
//     warn: jest.fn(),
//     error: jest.fn(),
//     debug: jest.fn(),
//   },
// }));


// This describe block was for conceptual unit tests.
// The actual tests are integration tests using request(app).
describe('AssessmentController Unit Test Section (Conceptual)', () => {
  let controller: AssessmentController;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  // Global testUser and testSession are defined in setup.ts
  // No need for beforeAll here to define them.

  beforeEach(() => {
    controller = assessmentController; // Using singleton instance
    mockRequest = {
      user: { id: (global as any).testUser.id, email: (global as any).testUser.email } as any,
      body: {},
      params: {},
      query: {},
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();

    // Global mocks (Supabase, AI, Crisis, Logger) are reset in setup.ts's beforeEach.
    // No need to reset them here again.
  });

  describe('startAssessment (Unit Test Style Example)', () => {
    it('should demonstrate unit test structure (not executed as part of integration suite)', () => {
      // This test is a placeholder to illustrate how a direct unit test might look.
      // For actual execution, it would require directly mocking assessmentService,
      // e.g., jest.spyOn(assessmentService, 'startAssessment').mockResolvedValueOnce(...);
      // await controller.startAssessment(mockRequest as Request, mockResponse as Response, mockNext);
      // expect(assessmentService.startAssessment).toHaveBeenCalledWith(...);
      // expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(true).toBe(true); // No-op to make test runner happy.
    });
  });

  });

  describe('POST /api/assessment/welcome', () => {
    const userProfileData = {
      awarenessLevel: 'beginner',
      ruqyaFamiliarity: 'none',
      // ... other profile fields from global.testProfile or a specific one for this test
    };

    it('should generate personalized welcome message with valid profile in body', async () => {
      global.mockAiService.getPersonalizedWelcome.mockResolvedValueOnce({
        userType: 'beginner_unfamiliar',
        greeting: 'Assalamu Alaikum',
        introduction: 'Welcome to your spiritual assessment.',
        explanation: 'This will help understand your state.',
      });

      const response = await request(app)
        .post('/api/assessment/welcome')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ userProfile: userProfileData });

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.welcome).toBeDefined();
      expect(response.body.data.welcome.userType).toBe('beginner_unfamiliar');
      expect(global.mockAiService.getPersonalizedWelcome).toHaveBeenCalledWith(global.testUser.id, { ...userProfileData, user_id: global.testUser.id });
    });

    it('should use req.user.profile if userProfile not in body but available on req.user', async () => {
      // Simulate profile being on req.user (global.testUser has a profile by default in setup)
      global.mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
          data: { user: { ...global.testUser, profile: global.testProfile } }, // Ensure req.user has .profile
          error: null,
      });
      global.mockAiService.getPersonalizedWelcome.mockResolvedValueOnce({
        userType: 'intermediate_familiar', // Example
        greeting: 'Welcome back',
        introduction: 'Let us continue.',
        explanation: 'We value your input.',
      });

      const response = await request(app)
        .post('/api/assessment/welcome')
        .set('Authorization', `Bearer ${validToken}`)
        .send({}); // Empty body

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.welcome.userType).toBe('intermediate_familiar');
      // The service receives the profile from req.user, augmented with user_id
      expect(global.mockAiService.getPersonalizedWelcome).toHaveBeenCalledWith(global.testUser.id, { ...global.testProfile, user_id: global.testUser.id });
    });

    it('should return 400 if userProfile is not in body and not on req.user.profile', async () => {
      global.mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
          data: { user: { ...global.testUser, profile: null } }, // req.user will not have .profile
          error: null,
      });

      const response = await request(app)
        .post('/api/assessment/welcome')
        .set('Authorization', `Bearer ${validToken}`)
        .send({}); // Empty body

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('error'); // Controller error handling for this case
      expect(response.body.message).toContain('User profile is required');
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post('/api/assessment/welcome')
        .send({ userProfile: userProfileData });

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/assessment/:sessionId/questions/:step', () => {
    const sessionId = 'session-qs-123';
    const step = 'physical_symptoms';
    const mockQuestions = [
      { id: 'q1', text: 'Do you feel tired?', type: 'multiple_choice', options: ['Yes', 'No'] },
      { id: 'q2', text: 'Rate your headache level.', type: 'slider', min: 0, max: 10 },
    ];

    it('should retrieve assessment questions successfully', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValueOnce({
          data: { id: sessionId, user_id: global.testUser.id, current_step: step },
          error: null,
      });
      global.mockSupabaseClient.from('assessment_questions')
          .select.mockReturnValueOnce({
              eq: jest.fn().mockReturnThis(),
              order: jest.fn().mockResolvedValueOnce({ data: mockQuestions, error: null }),
          } as any);


      const response = await request(app)
        .get(`/api/assessment/${sessionId}/questions/${step}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.questions).toEqual(mockQuestions);
    });

    it('should return 403 if user does not own the session (service level check)', async () => {
       global.mockSupabaseClient.from('assessment_sessions')
          .select()
          .eq('id', sessionId)
          .single.mockResolvedValueOnce({ data: { id: sessionId, user_id: 'another-user-id' }, error: null });

      // This test relies on the assessmentService.getAssessmentQuestions method
      // to perform an ownership check, likely by calling getSession which itself checks ownership.
      // If the service's getSession method (when called by getAssessmentQuestions) throws an AppError(..., 403),
      // this test will pass.

      const response = await request(app)
        .get(`/api/assessment/${sessionId}/questions/${step}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied'); // Or specific error from service
    });

    it('should return 200 with empty array if questions for step not found by service', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValueOnce({
          data: { id: sessionId, user_id: global.testUser.id, current_step: 'some_other_step' },
          error: null,
      });
      global.mockSupabaseClient.from('assessment_questions')
          .select.mockReturnValueOnce({
              eq: jest.fn().mockReturnThis(),
              order: jest.fn().mockResolvedValueOnce({ data: [], error: null }),
          }as any);

      const response = await request(app)
        .get(`/api/assessment/${sessionId}/questions/non_existent_step`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.questions).toEqual([]);
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .get(`/api/assessment/${sessionId}/questions/${step}`);
      expect(response.status).toBe(401);
    });
  });

  // TODO: Add tests for submitFeedback
  // TODO: Add tests for getSession (endpoint)

  describe('POST /api/assessment/diagnosis/:diagnosisId/feedback', () => {
    const diagnosisId = 'diag-feedback-123';
    const validFeedback = {
      accuracy: 5,
      helpfulness: 4,
      comments: 'Very insightful!',
    };

    it('should submit diagnosis feedback successfully', async () => {
      global.mockSupabaseClient.from('spiritual_diagnoses')
        .select()
        .eq('id', diagnosisId)
        .single.mockResolvedValueOnce({ data: { id: diagnosisId, user_id: global.testUser.id }, error: null });

      global.mockSupabaseClient.from('diagnosis_feedback')
        .insert.mockResolvedValueOnce({ error: null } as any);

      const response = await request(app)
        .post(`/api/assessment/diagnosis/${diagnosisId}/feedback`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(validFeedback);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.message).toContain('Thank you for your feedback');
    });

    it('should return 400 for invalid feedback data', async () => {
      const invalidFeedback = { accuracy: 6, helpfulness: 0 };
      const response = await request(app)
        .post(`/api/assessment/diagnosis/${diagnosisId}/feedback`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(invalidFeedback);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Invalid feedback');
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post(`/api/assessment/diagnosis/${diagnosisId}/feedback`)
        .send(validFeedback);
      expect(response.status).toBe(401);
    });

    it('should return 403 if user does not own the diagnosis (service level check)', async () => {
      global.mockSupabaseClient.from('spiritual_diagnoses')
        .select()
        .eq('id', diagnosisId)
        .single.mockResolvedValueOnce({ data: { id: diagnosisId, user_id: 'another-user-id' }, error: null });

      const response = await request(app)
        .post(`/api/assessment/diagnosis/${diagnosisId}/feedback`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(validFeedback);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });
  });

  describe('GET /api/assessment/session/:sessionId', () => {
    const sessionId = 'get-session-123';
    const mockSessionData = {
      id: sessionId,
      user_id: global.testUser.id,
      current_step: 'some_step',
    };

    it('should retrieve assessment session details successfully', async () => {
      global.mockSupabaseClient.from('assessment_sessions')
        .select()
        .eq('id', sessionId)
        .single.mockResolvedValueOnce({ data: mockSessionData, error: null });

      const response = await request(app)
        .get(`/api/assessment/session/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.session).toEqual(mockSessionData);
    });

    it('should return 404 if session not found', async () => {
      global.mockSupabaseClient.from('assessment_sessions')
        .select()
        .eq('id', sessionId)
        .single.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } });

      const response = await request(app)
        .get(`/api/assessment/session/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
    });

    it('should return 403 if user does not own the session', async () => {
      global.mockSupabaseClient.from('assessment_sessions')
        .select()
        .eq('id', sessionId)
        .single.mockResolvedValueOnce({ data: { ...mockSessionData, user_id: 'another-user-id' }, error: null });

      const response = await request(app)
        .get(`/api/assessment/session/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .get(`/api/assessment/session/${sessionId}`);
      expect(response.status).toBe(401);
    });
  });
  // TODO: Add tests for updateSession
  // TODO: Add tests for forceGenerateDiagnosis
  describe('PUT /api/assessment/session/:sessionId', () => {
    const sessionId = 'update-session-123';
    const updatePayload = { current_step: 'new_step', session_data: { mood: 'updated' } };
    const mockUpdatedSession = { id: sessionId, user_id: global.testUser.id, ...updatePayload };

    it('should update assessment session details successfully', async () => {
       global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValueOnce({
           data: { id: sessionId, user_id: global.testUser.id }, error: null
       });
      global.mockSupabaseClient.from('assessment_sessions').update(updatePayload).eq('id', sessionId).select().single.mockResolvedValueOnce({
          data: mockUpdatedSession, error: null
      });


      const response = await request(app)
        .put(`/api/assessment/session/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(updatePayload);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.session).toEqual(mockUpdatedSession);
    });

    it('should return 403 if user does not own the session they are trying to update', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValueOnce({
          data: { id: sessionId, user_id: 'another-user-id' },
          error: null
      });

      const response = await request(app)
        .put(`/api/assessment/session/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(updatePayload);

      expect(response.status).toBe(403);
    });

    it('should return 404 if session to update not found', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValueOnce({
          data: null, error: { code: 'PGRST116'}
      });

      const response = await request(app)
        .put(`/api/assessment/session/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(updatePayload);

      expect(response.status).toBe(404);
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .put(`/api/assessment/session/${sessionId}`)
        .send(updatePayload);
      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/assessment/:sessionId/generate-diagnosis', () => {
    const sessionId = 'force-diag-session-123';
    const mockGeneratedDiagnosis = { id: 'forced-diag-456', session_id: sessionId, user_id: global.testUser.id };

    beforeEach(() => {
        global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValue({
            data: { id: sessionId, user_id: global.testUser.id },
            error: null,
        });
        global.mockAiService.analyzeSpiritualLandscape.mockResolvedValue({
            layerAnalysis: { nafs: { score: 1} }, overallSeverity: 'high', recommendedJourneyType: 'intensive', confidence: 0.9,
            islamicInsights: [], educationalContent: [], crisisLevel: 'none', crisisIndicators: [], immediateActions: [], nextSteps: []
        });
        global.mockSupabaseClient.from('spiritual_diagnoses').insert(expect.any(Object)).select().single.mockResolvedValue({
            data: mockGeneratedDiagnosis, error: null
        });
        global.mockSupabaseClient.from('assessment_sessions').update(expect.any(Object)).eq('id', sessionId).mockResolvedValue({ error: null });
    });

    it('should force generate diagnosis successfully', async () => {
      const response = await request(app)
        .post(`/api/assessment/${sessionId}/generate-diagnosis`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.diagnosis).toEqual(mockGeneratedDiagnosis);
      expect(response.body.message).toContain('Diagnosis force-generated successfully.');
    });

    it('should return 403 if user does not own the session', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
          data: { id: sessionId, user_id: 'another-user-id' },
          error: null,
      });

      const response = await request(app)
        .post(`/api/assessment/${sessionId}/generate-diagnosis`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(403);
    });

    it('should return 404 if session not found', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
          data: null, error: { code: 'PGRST116' }
      });
      const response = await request(app)
        .post(`/api/assessment/${sessionId}/generate-diagnosis`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post(`/api/assessment/${sessionId}/generate-diagnosis`);
      expect(response.status).toBe(401);
    });
  });
  // TODO: Add tests for resumeAssessment
  // TODO: Add tests for abandonAssessment
  describe('POST /api/assessment/resume/:sessionId', () => {
    const sessionId = 'resume-session-123';
    const mockSessionToResume = {
      id: sessionId,
      user_id: global.testUser.id,
      current_step: 'emotional_state',
      completed_at: null,
      abandoned_at: null,
    };
    const mockQuestionsForStep = [{ id: 'q3', text: 'How are you feeling?' }];

    beforeEach(() => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValue({
          data: mockSessionToResume, error: null
      });
      global.mockSupabaseClient.from('assessment_questions').select().eq('step', mockSessionToResume.current_step).order().mockResolvedValue({
          data: mockQuestionsForStep, error: null
      });
    });

    it('should resume an incomplete assessment successfully', async () => {
      const response = await request(app)
        .post(`/api/assessment/resume/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.session.id).toBe(sessionId);
      expect(response.body.data.currentStep).toBe(mockSessionToResume.current_step);
      expect(response.body.data.questions).toEqual(mockQuestionsForStep);
      expect(response.body.data.progress).toBeDefined();
      expect(response.body.message).toContain('Assessment resumed successfully');
    });

    it('should return 400 if trying to resume an already completed session', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
        data: { ...mockSessionToResume, completed_at: new Date().toISOString() }, error: null
      });
      const response = await request(app)
        .post(`/api/assessment/resume/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);
      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Assessment session already completed');
    });

    it('should return 400 if trying to resume an abandoned session', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
        data: { ...mockSessionToResume, abandoned_at: new Date().toISOString() }, error: null
      });
      const response = await request(app)
        .post(`/api/assessment/resume/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);
      expect(response.status).toBe(400);
      expect(response.body.message).toContain('This assessment session was abandoned');
    });

    it('should return 404 if session to resume not found', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
          data: null, error: { code: 'PGRST116' }
      });
      const response = await request(app)
        .post(`/api/assessment/resume/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);
      expect(response.status).toBe(404);
    });

    it('should return 403 if user does not own the session', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
        data: { ...mockSessionToResume, user_id: 'another-user-id' }, error: null
      });
      const response = await request(app)
        .post(`/api/assessment/resume/${sessionId}`)
        .set('Authorization', `Bearer ${validToken}`);
      expect(response.status).toBe(403);
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post(`/api/assessment/resume/${sessionId}`);
      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/assessment/:sessionId/abandon', () => {
    const sessionId = 'abandon-session-123';
    const reasonPayload = { reason: 'No longer needed' };

    beforeEach(() => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockResolvedValue({
          data: { id: sessionId, user_id: global.testUser.id, completed_at: null, abandoned_at: null },
          error: null,
      });
      global.mockSupabaseClient.from('assessment_sessions').update(expect.objectContaining({ abandoned_at: expect.any(String), abandonment_reason: reasonPayload.reason })).eq('id', sessionId).mockResolvedValue({
          error: null
      });
    });

    it('should abandon an assessment session successfully', async () => {
      const response = await request(app)
        .post(`/api/assessment/${sessionId}/abandon`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(reasonPayload);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.message).toContain('Assessment session successfully abandoned.');
    });

    it('should return 403 if user does not own the session', async () => {
       global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
          data: { id: sessionId, user_id: 'another-user-id' }, error: null
      });
      const response = await request(app)
        .post(`/api/assessment/${sessionId}/abandon`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(reasonPayload);
      expect(response.status).toBe(403);
    });

    it('should return 404 if session to abandon not found', async () => {
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionId).single.mockReset().mockResolvedValueOnce({
          data: null, error: { code: 'PGRST116'}
      });
      const response = await request(app)
        .post(`/api/assessment/${sessionId}/abandon`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(reasonPayload);
      expect(response.status).toBe(404);
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post(`/api/assessment/${sessionId}/abandon`)
        .send(reasonPayload);
      expect(response.status).toBe(401);
    });
  });
  // TODO: Add tests for resetSession (admin)
  describe('POST /api/assessment/admin/reset/:sessionId', () => {
    const sessionIdToReset = 'reset-this-session-123';

    const setupAdminUser = () => {
      global.mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: {
          user: {
            ...global.testUser,
            app_metadata: { ...global.testUser.app_metadata, roles: ['admin'] }
          }
        },
        error: null,
      });
    };

    const setupNonAdminUser = () => {
      global.mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: {
          user: {
            ...global.testUser,
            app_metadata: { ...global.testUser.app_metadata, roles: ['user'] }
          }
        },
        error: null,
      });
    };

    beforeEach(() => {
      setupAdminUser();
      global.mockSupabaseClient.from('assessment_sessions')
        .delete.mockReturnValueOnce({
          eq: jest.fn().mockResolvedValueOnce({ error: null }),
        } as any);
    });

    it('should allow an admin to reset an assessment session', async () => {
      setupAdminUser();

      const response = await request(app)
        .post(`/api/assessment/admin/reset/${sessionIdToReset}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.message).toContain(`Assessment session ${sessionIdToReset} reset successfully.`);
    });

    it('should return 403 if a non-admin tries to reset a session', async () => {
      setupNonAdminUser();

      const response = await request(app)
        .post(`/api/assessment/admin/reset/${sessionIdToReset}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(403);
    });

    it('should handle session not found for reset (e.g. service throws error)', async () => {
      setupAdminUser();
      // Simulate service `resetSession` throwing an error if session not found.
      // This is hard to mock purely at DB level for a delete operation.
      // We assume `assessmentService.resetSession` would internally check and throw.
      // For this test, we can mock the DB delete to return an error that the service might convert to 404.
      global.mockSupabaseClient.from('assessment_sessions')
          .delete.mockReset().mockReturnValueOnce({
              eq: jest.fn().mockResolvedValueOnce({ error: { message: 'Not found', code: 'PGRST116' } }), // Simulate error
          } as any);

      // If assessmentService.resetSession catches this and throws an AppError(..., 404)
      // then the controller would return 404. If not, it might be a 500.
      // This depends on service implementation. Let's expect a 500 if service doesn't map.
      // Or, if the service is robust, it might do a select first.
      // For now, a specific 404 for delete is hard to guarantee at this level.
      // The controller itself doesn't catch specific DB errors to convert to 404 for reset.
      // So, if service's resetSession fails (e.g. DB error), it will likely be a 500.
      // A true 404 would imply the service checks existence first.
      // Let's assume for now the service is simple and just tries to delete.
      // If the `assessmentService.resetSession` is robust and first checks existence:
      global.mockSupabaseClient.from('assessment_sessions').select().eq('id', sessionIdToReset).single.mockResolvedValueOnce({ data: null, error: {code: 'PGRST116'} });

      const response = await request(app)
        .post(`/api/assessment/admin/reset/${sessionIdToReset}`)
        .set('Authorization', `Bearer ${validToken}`);
      expect(response.status).toBe(404); // This assumes service checks existence and throws 404.
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post(`/api/assessment/admin/reset/${sessionIdToReset}`);
      expect(response.status).toBe(401);
    });
  });
});


// Local createMockQueryBuilder is removed, global one from setup.ts is used implicitly by mockSupabaseClient.
// const createMockQueryBuilder = () => { ... } // REMOVED

describe('Assessment Controller Integration Tests', () => {
  // mockSupabase is part of global.mockSupabaseClient, accessed via getSupabase()
  // const mockSupabase = getSupabase() as any; // Not needed directly, use global.mockSupabaseClient for specific setups
  const validToken = global.testSession.access_token;

  // The beforeEach in setup.ts already clears all mocks and sets up default mock behaviors.
  // We only need to override specific mock behaviors per test if the default isn't suitable.
  beforeEach(() => {
    // Example: If a test needs a specific user setup different from global.testUser for auth
    // global.mockSupabaseClient.auth.getUser.mockResolvedValueOnce({ ... });
    // Default user profile mock from setup.ts should be sufficient for most cases.
  });

  // afterEach is also handled by setup.ts for clearing mocks, no need for a local one unless specific cleanup.

  describe('POST /api/assessment/start', () => {
    const validUserProfile = {
      awarenessLevel: 'intermediate',
      ruqyaFamiliarity: 'basic',
      profession: 'teacher',
      culturalBackground: 'arab',
      timeAvailability: 'moderate',
      learningStyle: 'visual',
    };

    it('should start new assessment successfully', async () => {
      // Default mocks from setup.ts:
      // - global.mockSupabaseClient.auth.getUser resolves with global.testUser (which has a profile)
      // - global.mockSupabaseClient.from().select()...single() for session check needs to be specific for "not found"
      // - global.mockSupabaseClient.from().insert() for session creation needs to be specific for "success"

      global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({ // For checking existing session
          eq: jest.fn().mockReturnThis(),
          is: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          limit: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } }), // No existing session
        } as any);

      global.mockSupabaseClient.from('assessment_sessions')
        .insert.mockReturnValueOnce({ // For creating new session
          // data should reflect what the service expects post-insert, typically just error: null for success
          single: jest.fn().mockResolvedValueOnce({ data: { id: 'new-session-id', user_id: global.testUser.id, /* other fields */ }, error: null })
        } as any);

      // AI service mock for getPersonalizedWelcome (used by assessmentService.startAssessment)
      // This is set globally in setup.ts, but can be overridden if needed.
      // global.mockAiService.getAIPersonalizedWelcome.mockResolvedValueOnce({ ... });


      const response = await request(app)
        .post('/api/assessment/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ userProfile: validUserProfile });

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          session: expect.objectContaining({
            // id: 'new-session-id', // Or expect.any(String) if ID is dynamic from service
            id: expect.any(String),
            userId: global.testUser.id,
            currentStep: 'welcome', // This comes from assessmentService logic
            totalSteps: expect.any(Number), // This comes from assessmentService logic
          }),
          welcome: expect.objectContaining({
            userType: expect.any(String), // Determined by AI service / assessmentService
            greeting: expect.any(String),
            introduction: expect.any(String),
            explanation: expect.any(String),
          }),
        },
      });
    });

    it('should resume existing assessment session', async () => {
      const existingSession = {
        id: 'existing-session-123',
        user_id: global.testUser.id,
        current_step: 'symptoms_physical',
        total_steps: 8, // Example value
        session_data: { /* some data */ },
        // Ensure all fields expected by the service/controller are present
      };

      global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({ // For checking existing session
          eq: jest.fn().mockReturnThis(),
          is: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          limit: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: existingSession, error: null }),
        } as any);

      // AI service might still be called for welcome message regeneration or validation
      // Ensure global.mockAiService.getAIPersonalizedWelcome provides a suitable response.

      const response = await request(app)
        .post('/api/assessment/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ userProfile: validUserProfile });

      expect(response.status).toBe(200);
      // The controller returns the existing session and a welcome message.
      // The service might generate a fresh welcome message.
      expect(response.body.data.session.id).toEqual(existingSession.id);
      expect(response.body.data.session.current_step).toEqual(existingSession.current_step);
      expect(response.body.data.welcome).toBeDefined();
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .post('/api/assessment/start')
        .send({ userProfile: validUserProfile });

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('No token provided'); // Assuming auth middleware handles this
    });

    it('should return 400 without user profile', async () => {
      // The controller properly throws "User profile required" error as seen in logs

      // Override the global auth mock for this specific test
      global.mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: {
          user: {
            ...global.testUser,
            // Simulate profile not being available on req.user.profile
            // The controller logic is: req.body.userProfile || (req.user as any)?.profile;
            // So, if req.body.userProfile is also empty, it should fail.
            // The service itself might also fetch profile if needed, ensure that mock path is covered.
            profile: null,
          },
        },
        error: null,
      });

      const response = await request(app)
        .post('/api/assessment/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send({}); // No userProfile in body

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('User profile required');
    }, 10000);

    it('should handle database errors during session check', async () => {
      // The controller properly throws "Database connection failed" error as seen in logs

      global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({ // For checking existing session
          eq: jest.fn().mockReturnThis(),
          is: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          limit: jest.fn().mockReturnThis(),
          single: jest.fn().mockRejectedValueOnce(new Error('Database connection failed')),
        } as any);

      const response = await request(app)
        .post('/api/assessment/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ userProfile: validUserProfile });

      expect(response.status).toBe(500); // Or whatever error code AppError maps it to
      expect(response.body.status).toBe('error'); // Or 'fail' depending on error handler
      // expect(response.body.message).toContain('Database connection failed'); // Or a generic message
    }, 10000);
  });

  describe('POST /api/assessment/:sessionId/submit', () => {
    const sessionId = 'test-session-123';
    const validSubmission = {
      step: 'physical_experiences',
      responses: {
        fatigue: 'severe',
        headaches: 'moderate',
        sleep_issues: 'mild',
      },
      timeSpent: 120,
    };

    // Default successful mock setup for session retrieval and update for most tests in this block.
    // Individual tests can override if they need to test different scenarios (e.g., session not found).
    beforeEach(() => {
      global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValue({ // For retrieving the session to submit to
          eq: jest.fn((col, val) => { // Allow chaining for multiple .eq calls
            if (col === 'id') return { single: jest.fn().mockResolvedValue({ data: { id: sessionId, user_id: global.testUser.id, current_step: 'physical_experiences', session_data: { responses: {} /* other necessary fields */ } }, error: null }) } as any;
            return this;
          }),
          single: jest.fn().mockResolvedValue({ data: { id: sessionId, user_id: global.testUser.id, current_step: 'physical_experiences', session_data: { responses: {} /* other fields */ } }, error: null }),
        } as any);

      global.mockSupabaseClient.from('assessment_sessions')
        .update.mockReturnValue({ // For updating the session
          eq: jest.fn().mockResolvedValue({ error: null }), // Assuming update is successful
        } as any);

      // Default crisis detection: no crisis
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValue({
        isCrisis: false,
        severity: 'low',
        indicators: [],
        // Ensure all fields expected by the service are here
        crisisLevel: 'none', message: '', emergencyActions: [], urgency: 'low', crisisIndicators: []
      });
    });

    it('should submit assessment response successfully', async () => {
      // Mocks are set in the beforeEach of this describe block.
      // If specific return values for nextStep/progress are needed, mock assessmentService.submitAssessmentResponse
      // or ensure the underlying logic (including AI calls if any) produces them.
      // For now, we assume the service works and returns some valid nextStep and progress.

      const response = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(validSubmission);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          nextStep: expect.any(String), // Or specific if known and stable
          progress: expect.any(Number), // Or specific
        },
      });

      expect(
        global.mockCrisisDetectionService.analyzeResponse
      ).toHaveBeenCalledWith(validSubmission.responses, validSubmission.step);
    });

    it('should handle crisis detection', async () => {
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'high',
        indicators: ['suicidal_ideation'],
        recommendations: ['immediate_professional_help'], // This field might not be directly on the object returned by service
        // Ensure the mock returns what the controller expects for a crisis_detected status:
        // crisisLevel, message, emergencyActions, urgency, crisisIndicators
        crisisLevel: 'high',
        message: 'Crisis detected, please seek help.',
        emergencyActions: [{ id: 'contact_support', text: 'Contact Support Now', type: 'button' }],
        urgency: 'high',
        crisisIndicators: ['suicidal_ideation'],
        // Potentially nextStep and progress might also be part of the crisis response
        nextStep: null, // Or a specific crisis step
        progress: expect.any(Number),
      });

      const response = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(validSubmission);

      expect(response.status).toBe(200); // Controller returns 200 but with crisis_detected status
      expect(response.body.status).toBe('crisis_detected');
      expect(response.body.data).toHaveProperty('crisisLevel', 'high');
      expect(response.body.data).toHaveProperty('emergencyActions');
      // expect(response.body.data).toHaveProperty('emergencyResources'); // This was in old test, check controller logic
    });

    it('should return 400 for invalid session ID format (if validation exists early)', async () => {
      // This tests if the route parameter validation (e.g. UUID format) catches it before DB lookup.
      // If no such validation, it would proceed to DB lookup and likely result in 404 or error from DB mock.
      const response = await request(app)
        .post('/api/assessment/invalid-session-id-format/submit')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validSubmission);

      // This status depends on whether express path params have regex or if service validates format.
      // For now, assume it might pass to service. If service throws AppError for format, it's 400.
      // If it goes to DB and DB mock is not hit (because 'invalid-session-id-format' doesn't match 'test-session-123'),
      // the default DB mock might return null, leading to "session not found" (404).
      // Let's assume a 404 is more likely if the ID format is valid but not found.
      // If the ID format itself is invalid (e.g. not a UUID), then a 400 from a validator middleware is possible.
      // The test name implies format is the issue.
      // The current controller does not have specific format validation for sessionId param itself,
      // it's passed to the service. So, this test might be more about "session not found".
      // Let's adjust to "session not found" by mocking DB to return null.

      global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } }), // Session not found
        } as any);


      const responseNotFound = await request(app)
        .post(`/api/assessment/non-existent-session/submit`) // Use a different ID for clarity
        .set('Authorization', `Bearer ${validToken}`)
        .send(validSubmission);

      expect(responseNotFound.status).toBe(404); // Assuming service throws 404 if session not found
      expect(responseNotFound.body.status).toBe('fail'); // Or 'error'
    });

    it('should return 403 for session not owned by user', async () => {
      global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: { id: sessionId, user_id: 'different-user-id', current_step: 'physical_experiences' }, error: null }),
        } as any);

      const response = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(validSubmission);

      expect(response.status).toBe(403);
      expect(response.body.status).toBe('fail'); // Or 'error'
      expect(response.body.message).toContain('Access denied'); // Or similar from service/controller
    });

    it('should return 400 for missing required fields in body', async () => {
      const invalidSubmission = {
        step: 'physical_experiences',
        // Missing responses and timeSpent
      };

      const response = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(invalidSubmission);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail'); // Or 'error'
      expect(response.body.message).toContain('Missing required fields');
    });

    it('should handle assessment completion (final step submission)', async () => {
      // Mock that this submission is for the final step.
      // The assessmentService.submitAssessmentResponse should determine it's the final step
      // and trigger diagnosis generation.

      // Simulate session is on the step before final, so this submission completes it.
      // The beforeEach mock for session retrieval might need adjustment for this test.
       global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: { id: sessionId, user_id: global.testUser.id, current_step: 'almost_final_step', session_data: { responses: {} } }, error: null }),
        } as any);


      // Mock AI service for diagnosis generation (called by assessmentService)
      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({ // Or analyzeAssessmentResponses depending on service impl
        // ... structure expected by diagnosis creation ...
        layerAnalysis: { nafs: { impactScore: 75, symptoms: ['anxiety'] } },
        overallSeverity: 'moderate',
        recommendedJourneyType: 'standard',
        confidence: 0.8,
        // Ensure it includes fields needed for SpiritualDiagnosis creation
        islamicInsights: [], educationalContent: [], crisisLevel: 'none', crisisIndicators: [], immediateActions: [], nextSteps: []
      });

      // Mock successful diagnosis insertion
      global.mockSupabaseClient.from('spiritual_diagnoses')
        .insert.mockReturnValueOnce({
          // single: jest.fn().mockResolvedValueOnce({ data: { id: 'new-diagnosis-id', /* ... */ }, error: null })
          // insert might return an array with select()
           select: jest.fn().mockReturnValueOnce({
             single: jest.fn().mockResolvedValueOnce({ data: { id: 'new-diagnosis-id', /* ... */ }, error: null })
           })
        } as any);

      // Mock successful session update to completed
      global.mockSupabaseClient.from('assessment_sessions')
        .update.mockReturnValueOnce({
          eq: jest.fn().mockResolvedValue({ error: null }), // Update to completed
        } as any);


      const finalSubmissionPayload = {
        ...validSubmission,
        step: 'final_step', // Assuming 'final_step' is a recognized step name
      };

      const response = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(finalSubmissionPayload);

      expect(response.status).toBe(200);
      expect(response.body.data.nextStep).toBeNull(); // Or a specific "completed" indicator
      expect(response.body.data.progress).toBe(100);
      expect(response.body.data.message).toContain('Assessment completed');
    });
  });

  describe('GET /api/assessment/:sessionId/diagnosis', () => {
    const sessionId = 'test-session-123';

    it('should retrieve diagnosis successfully', async () => {
      const mockDiagnosisData = {
        id: 'diagnosis-123',
        user_id: global.testUser.id,
        session_id: sessionId,
        // ... other diagnosis fields ...
        diagnosis_data: { overallSeverity: 'moderate', primaryLayer: { layer: 'nafs', impactScore: 75 }, confidence: 0.85 },
        created_at: new Date().toISOString(),
      };

      // Mock getSession to return a session that HAS a diagnosis
       global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: { id: sessionId, user_id: global.testUser.id, diagnosis_id: 'diagnosis-123', diagnosis: mockDiagnosisData }, error: null }),
        } as any);


      // Mock getDiagnosisDelivery (if it involves another DB call or service call)
      // Assuming assessmentService.getDiagnosisDelivery also uses AI or has its content
      global.mockAiService.generatePersonalizedContent.mockResolvedValueOnce({ // Or whatever AI method getDiagnosisDelivery uses
          title: "Your Diagnosis Insights",
          summary: "Summary of your state.",
          // ... other delivery fields
      });


      const response = await request(app)
        .get(`/api/assessment/${sessionId}/diagnosis`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          diagnosis: expect.objectContaining({
            id: 'diagnosis-123',
            // overallSeverity: 'moderate', // These are inside diagnosis_data
            // primaryLayer: expect.objectContaining({ layer: 'nafs', impactScore: 75 }),
            // confidence: 0.85,
             diagnosis_data: expect.objectContaining({
                overallSeverity: 'moderate',
             })
          }),
          delivery: expect.any(Object), // Check structure of delivery object
        },
      });
    });

    it('should return 404 if diagnosis not yet available for session', async () => {
       global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: { id: sessionId, user_id: global.testUser.id, diagnosis_id: null, diagnosis: null }, error: null }), // No diagnosis linked
        } as any);

      const response = await request(app)
        .get(`/api/assessment/${sessionId}/diagnosis`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
      expect(response.body.status).toBe('fail'); // Or 'error'
      expect(response.body.message).toContain('Diagnosis not yet available');
    });

    it('should return 403 if user does not own the session (and thus the diagnosis)', async () => {
       global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValueOnce({ data: { id: sessionId, user_id: 'another-user-id', diagnosis_id: 'diag-abc' }, error: null }),
        } as any);
      // Note: Controller's getDiagnosis first calls assessmentService.getSession(sessionId).
      // The service should handle ownership check for the session. If session not owned, it should throw.
      // If session is owned, but diagnosis (if fetched separately by ID) is not owned, that's another check.
      // Current controller logic: gets session, then if session.diagnosis exists, calls getDiagnosisDelivery(session.diagnosis.id).
      // The ownership is primarily on the session.

      const response = await request(app)
        .get(`/api/assessment/${sessionId}/diagnosis`)
        .set('Authorization', `Bearer ${validToken}`);

      // This depends on where the 403 is thrown. If getSession throws 403, then that's it.
      // The assessmentService.getSession itself should enforce ownership.
      // Let's assume assessmentService.getSession() throws 403 if session.userId !== req.user.id
      // We need to ensure the service's getSession is mocked to throw this or the DB mock leads to it.
      // The DB mock above will return a session for 'another-user-id'. The service should then compare this to req.user.id.
      // This requires the service's getSession to be robust.

      expect(response.status).toBe(403); // This implies the service layer correctly denied access
      expect(response.body.message).toContain('Access denied');
    });

  });

  describe('GET /api/assessment/history', () => {
    it('should retrieve user assessment history', async () => {
      const mockHistoryData = [
        { id: 'session-1', user_id: global.testUser.id, started_at: '2024-01-01T00:00:00.000Z', completed_at: '2024-01-01T01:00:00.000Z', diagnosis_id: 'diag-1', /* ... other fields ... */ },
        { id: 'session-2', user_id: global.testUser.id, started_at: '2024-01-15T00:00:00.000Z', completed_at: null, diagnosis_id: null, /* ... */ },
      ];

      // Mock for assessmentService.getAssessmentHistory which uses Supabase
      global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          range: jest.fn().mockResolvedValueOnce({ data: mockHistoryData, error: null }), // For pagination
        } as any);

      global.mockSupabaseClient.from('assessment_sessions') // For the count query in service
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          count: jest.fn().mockResolvedValueOnce({ count: mockHistoryData.length, error: null })
        } as any);

      const response = await request(app)
        .get('/api/assessment/history')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.assessments.length).toBe(mockHistoryData.length);
      // The controller now returns data directly from service, which includes {assessments, total, page, limit}
      expect(response.body.data.total).toBe(mockHistoryData.length);
      expect(response.body.data.assessments[0].id).toBe('session-1');
      // The service maps to status: 'completed' or 'incomplete'.
      // This mapping happens in assessmentService.getAssessmentHistory, so mock output should reflect that if testing controller output.
      // Or, test that the raw data is passed and trust service does its job.
      // For this test, let's assume the service returns the mapped data.
      // So, the mockHistoryData should ideally be what the service *returns*, not what the DB returns.
      // However, current mock is for DB. Controller calls service. Service transforms.
      // To simplify, let's assume the service output matches the shape after transformation.
      // The controller itself does not add 'status'. It's in the service.
      // So, the mock for global.mockSupabaseClient.from('assessment_sessions')... should lead to service returning the final shape.
      // This highlights that testing the controller means we trust the service to format data correctly.
      // The mock above is for the DB call inside the service. The service then processes this.
      // The test output should match what the *controller* sends, which is what the *service* returns.
    });

    it('should return empty array for user with no assessments', async () => {
       global.mockSupabaseClient.from('assessment_sessions')
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          range: jest.fn().mockResolvedValueOnce({ data: [], error: null }),
        } as any);
       global.mockSupabaseClient.from('assessment_sessions') // For count
        .select.mockReturnValueOnce({
          eq: jest.fn().mockReturnThis(),
          count: jest.fn().mockResolvedValueOnce({ count: 0, error: null })
        } as any);

      const response = await request(app)
        .get('/api/assessment/history')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.assessments).toEqual([]);
      expect(response.body.data.total).toBe(0);
    });
  });
});
