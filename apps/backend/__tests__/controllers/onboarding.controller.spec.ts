/**
 * Onboarding Controller Integration Tests
 * Tests onboarding endpoints via HTTP requests
 */

import { Request, Response, NextFunction } from 'express';
import { Prisma, PrismaClientKnownRequestError } from '@prisma/client';
import * as onboardingController from '../../src/controllers/onboarding.controller';
import { OnboardingService } from '../../src/services/onboarding.service';
import { CrisisDetectionService } from '../../src/services/crisis-detection.service';
import { AppError } from '../../src/middleware/errorHandler';
import { prisma as db } from '../../src/config/database'; // Import actual prisma instance
import { UserProfile, createEmptyProfile } from '../../src/models/UserProfile';


// Mock services and Prisma
jest.mock('../../src/services/onboarding.service');
jest.mock('../../src/services/crisis-detection.service');
jest.mock('../../src/config/database', () => ({
  prisma: {
    userProfileDetailed: {
      findUnique: jest.fn(),
      upsert: jest.fn(),
      update: jest.fn(),
    },
    onboardingSession: {
      findUnique: jest.fn(),
      // Add other methods if directly used by controller
    },
  },
}));
jest.mock('../../src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));


const mockPrisma = db as jest.Mocked<typeof db>;

describe('Onboarding Controller Unit Tests', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockOnboardingService: jest.Mocked<OnboardingService>;
  let mockCrisisService: jest.Mocked<CrisisDetectionService>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockRequest = {
      body: {},
      params: {},
      user: { id: 'test-user-id' } as any, 
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();

    // Get the mocked constructor from the jest.mock above
    mockOnboardingService = new OnboardingService() as jest.Mocked<OnboardingService>;
    mockCrisisService = new CrisisDetectionService() as jest.Mocked<CrisisDetectionService>;

    // Ensure prototype methods are instances of jest.fn() if OnboardingService constructor doesn't assign them
    // This is important because the controller calls methods on an *instance* of OnboardingService
     if (!jest.isMockFunction(mockOnboardingService.startOnboarding)) {
        mockOnboardingService.startOnboarding = jest.fn();
     }
     if (!jest.isMockFunction(mockOnboardingService.submitResponse)) {
        mockOnboardingService.submitResponse = jest.fn();
     }
     if (!jest.isMockFunction(mockOnboardingService.getNextQuestion)) {
        mockOnboardingService.getNextQuestion = jest.fn();
     }
     // getSessionStatus is directly on prisma, not on the service anymore in the controller
     // if (!jest.isMockFunction(mockOnboardingService.getSessionStatus)) {
     //    mockOnboardingService.getSessionStatus = jest.fn();
     // }
     if (!jest.isMockFunction(mockCrisisService.logCrisisEvent)) {
        mockCrisisService.logCrisisEvent = jest.fn();
     }
      // Assign the mocked service instances to where the controller will look for them
      // This depends on how they are instantiated or injected in the actual controller file.
      // For this setup, we assume the controller creates `new OnboardingService()`.
      // So, the `jest.mock` at the top handles this by replacing the class with its mock version.
      // The instances `mockOnboardingService` and `mockCrisisService` are created from these mocked classes.
  });


  describe('startOnboarding', () => {
    it('should start an onboarding session and return the first question', async () => {
      const mockSessionResult = { sessionId: 'session123', currentStep: 'welcome', startedAt: new Date() };
      const mockFirstQuestionResult = { id: 'welcome', text: 'Welcome!', step: 'welcome', progress: 0 };
      
      (mockOnboardingService.startOnboarding as jest.Mock).mockResolvedValue(mockSessionResult);
      (mockOnboardingService.getNextQuestion as jest.Mock).mockResolvedValue(mockFirstQuestionResult);

      mockRequest.body = { deviceInfo: { platform: 'test' } };
      mockRequest.user = { id: 'user1' } as any;

      await onboardingController.startOnboarding(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockOnboardingService.startOnboarding).toHaveBeenCalledWith('user1', { platform: 'test' });
      expect(mockOnboardingService.getNextQuestion).toHaveBeenCalledWith(mockSessionResult.sessionId, {});
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'success',
        data: {
          session: {
            sessionId: mockSessionResult.sessionId,
            startedAt: mockSessionResult.startedAt,
            currentStep: mockSessionResult.currentStep,
          },
          question: mockFirstQuestionResult,
        },
      });
    });

     it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      await onboardingController.startOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('User authentication required to start onboarding.', 401));
    });

    it('should call next with error if startOnboarding service fails', async () => {
      const error = new AppError('Service failure', 500);
      (mockOnboardingService.startOnboarding as jest.Mock).mockRejectedValue(error);
      mockRequest.body = { deviceInfo: {} };
      mockRequest.user = { id: 'user1' } as any;

      await onboardingController.startOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('submitResponse', () => {
    const submitBody = { sessionId: 's1', stepId: 'q1', response: { answer: 'A' }, timeSpent: 10 };

    it('should submit a response and return next question if status is continue', async () => {
      const serviceResponse = { question: { id: 'q2', text: 'Next?' }, progress: 50, step: 'q2' };
      (mockOnboardingService.submitResponse as jest.Mock).mockResolvedValue(serviceResponse);
      mockRequest.body = submitBody;
      mockRequest.user = { id: 'user123' } as any;

      await onboardingController.submitResponse(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockOnboardingService.submitResponse).toHaveBeenCalledWith(
        submitBody.sessionId, submitBody.stepId, submitBody.response, submitBody.timeSpent
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ status: 'continue', data: serviceResponse });
    });

    it('should return completed status if onboarding is finished', async () => {
      const serviceResponse = { profile: { id: 'profile1' }, recommendedPathway: 'pathA', /* ... */ };
      (mockOnboardingService.submitResponse as jest.Mock).mockResolvedValue(serviceResponse);
      mockRequest.body = submitBody;
      mockRequest.user = { id: 'user123' } as any;


      await onboardingController.submitResponse(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({ status: 'completed', data: serviceResponse }));
    });

    it('should handle crisis detection and log event', async () => {
      const crisisDataFromService = { type: 'crisis_detected', level: 'high', message: 'Crisis!', actions: [], nextStep: 'crisis_support' };
      (mockOnboardingService.submitResponse as jest.Mock).mockResolvedValue(crisisDataFromService);
      (mockCrisisService.logCrisisEvent as jest.Mock).mockResolvedValue(undefined);
      mockRequest.body = submitBody;
      mockRequest.user = { id: 'user-crisis' } as any;

      await onboardingController.submitResponse(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCrisisService.logCrisisEvent).toHaveBeenCalledWith(
        'user-crisis', crisisDataFromService, 
        { sessionId: submitBody.sessionId, stepId: submitBody.stepId, response: submitBody.response }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ status: 'crisis_detected', data: crisisDataFromService });
    });
     it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      await onboardingController.submitResponse(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('User authentication required.', 401));
    });
  });

  describe('resumeOnboarding', () => {
     it('should resume an onboarding session and return the next question', async () => {
      const mockSessionId = 'resumeSession123';
      const mockNextQuestion = { id: 'q3', text: 'Question 3?', step: 'q3', progress: 30 };
      (mockOnboardingService.getNextQuestion as jest.Mock).mockResolvedValue(mockNextQuestion);
      mockRequest.body = { sessionId: mockSessionId };
      mockRequest.user = { id: 'userResume' } as any;

      await onboardingController.resumeOnboarding(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockOnboardingService.getNextQuestion).toHaveBeenCalledWith(mockSessionId, {});
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'success',
        data: { sessionId: mockSessionId, question: mockNextQuestion },
      });
    });
     it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      await onboardingController.resumeOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('User authentication required.', 401));
    });
  });

  describe('submitOnboarding (Legacy with Prisma)', () => {
    const userId = 'test-user-id';
    const validOnboardingData = {
      personalInfo: { awarenessLevel: 'intermediate', ruqyaFamiliarity: 'basic', profession: 'dev', age: '26-35', gender: 'male', culturalBackground: 'asian' },
      preferences: { timeAvailability: '10-20', learningStyle: 'visual', communityParticipation: 'moderate' },
    };

    it('should submit onboarding data and upsert profile using Prisma', async () => {
      mockRequest.user = { id: userId } as any;
      mockRequest.body = validOnboardingData;
      const upsertedProfileData = { userId, profileData: expect.any(Object), completionStatus: 'complete', updatedAt: expect.any(Date) };
      mockPrisma.userProfileDetailed.upsert.mockResolvedValue(upsertedProfileData as any);

      await onboardingController.submitOnboarding(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockPrisma.userProfileDetailed.upsert).toHaveBeenCalledWith({
        where: { userId },
        create: expect.objectContaining({ userId, completionStatus: 'complete', profileVersion: '1.0.0' }),
        update: expect.objectContaining({ completionStatus: 'complete' }),
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'success',
        data: { profile: upsertedProfileData, nextStep: 'assessment' },
      });
    });

    it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      mockRequest.body = validOnboardingData;
      await onboardingController.submitOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Authentication required', 401));
    });
    
    it('should handle Prisma errors during upsert', async () => {
      mockRequest.user = { id: userId } as any;
      mockRequest.body = validOnboardingData;
      const dbError = new PrismaClientKnownRequestError('DB error', { code: 'P2002', clientVersion: 'test' });
      mockPrisma.userProfileDetailed.upsert.mockRejectedValue(dbError);

      await onboardingController.submitOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Database error during onboarding submission.', 500, dbError));
    });
  });

  describe('getSessionStatus (Prisma)', () => {
    const sessionId = 'session123';
    const userId = 'userTest';

    it('should return session status if user is authorized', async () => {
      mockRequest.params = { sessionId };
      mockRequest.user = { id: userId } as any;
      const mockSessionData = { sessionId, userId, currentStep: 'details', completedAt: null, totalTimeSpent: 50 };
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(mockSessionData as any);

      await onboardingController.getSessionStatus(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockPrisma.onboardingSession.findUnique).toHaveBeenCalledWith({ where: { sessionId } });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ status: 'success', data: mockSessionData });
    });

    it('should return 403 if user is not authorized for the session', async () => {
      mockRequest.params = { sessionId };
      mockRequest.user = { id: 'anotherUser' } as any;
      const mockSessionData = { sessionId, userId, currentStep: 'details' }; // userId is 'userTest'
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(mockSessionData as any);

      await onboardingController.getSessionStatus(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Forbidden: You do not have access to this session', 403));
    });
    
    it('should return 404 if session not found', async () => {
      mockRequest.params = { sessionId };
      mockRequest.user = { id: userId } as any;
      mockPrisma.onboardingSession.findUnique.mockResolvedValue(null);

      await onboardingController.getSessionStatus(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Session not found', 404));
    });
     it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      mockRequest.params = { sessionId };
      await onboardingController.getSessionStatus(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('User authentication required.', 401));
    });
  });

  describe('getOnboardingStatus (Prisma)', () => {
    const userId = 'userTest';
    it('should return completed status if profile exists and is complete', async () => {
      mockRequest.user = { id: userId } as any;
      const profileData = { someData: 'value' };
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue({ userId, completionStatus: 'complete', profileData } as any);

      await onboardingController.getOnboardingStatus(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'success',
        data: { onboardingCompleted: true, profile: profileData, nextStep: 'assessment' },
      });
    });

    it('should return incomplete status if profile exists but not complete', async () => {
      mockRequest.user = { id: userId } as any;
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue({ userId, completionStatus: 'incomplete', profileData: null } as any);
      await onboardingController.getOnboardingStatus(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'success',
        data: { onboardingCompleted: false, profile: null, nextStep: 'onboarding' },
      });
    });
    
    it('should return incomplete status if profile not found', async () => {
      mockRequest.user = { id: userId } as any;
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue(null);
      await onboardingController.getOnboardingStatus(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'success',
        data: { onboardingCompleted: false, profile: null, nextStep: 'onboarding' },
      });
    });
     it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      await onboardingController.getOnboardingStatus(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Authentication required', 401));
    });
  });

  describe('updateOnboarding (Preferences - Prisma)', () => {
    const userId = 'userTest';
    const preferences = { timeAvailability: '30-60', learningStyle: 'auditory', communityParticipation: 'high' };

    it('should update preferences in profileData using Prisma', async () => {
      mockRequest.user = { id: userId } as any;
      mockRequest.body = { preferences };
      const existingProfile = createEmptyProfile(userId); // Use the actual model helper
      existingProfile.preferences.timeAvailability = '10-20'; // Original value
      
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue({ userId, profileData: existingProfile } as any);
      mockPrisma.userProfileDetailed.update.mockImplementation(async ({ data }) => {
        return { userId, profileData: data?.profileData, updatedAt: new Date() } as any;
      });

      await onboardingController.updateOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockPrisma.userProfileDetailed.findUnique).toHaveBeenCalledWith({ where: { userId } });
      expect(mockPrisma.userProfileDetailed.update).toHaveBeenCalledWith(expect.objectContaining({
        where: { userId },
        data: { 
            profileData: expect.objectContaining({
                preferences: expect.objectContaining(preferences) // Ensure new preferences are there
            })
        },
      }));
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: { profile: expect.objectContaining({ preferences: expect.objectContaining(preferences) }) },
      }));
    });

    it('should return 404 if profile not found for update', async () => {
      mockRequest.user = { id: userId } as any;
      mockRequest.body = { preferences };
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue(null);
      await onboardingController.updateOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Profile not found to update.', 404));
    });
     it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      mockRequest.body = { preferences };
      await onboardingController.updateOnboarding(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Authentication required', 401));
    });
  });
  
  describe('updateProfile (Prisma)', () => {
    const userId = 'userTest';
    const profileUpdates: Partial<UserProfile> = { professionalContext: { field: 'engineering' } };

    it('should update profileData using Prisma', async () => {
      mockRequest.user = { id: userId } as any;
      mockRequest.body = { profileUpdates, reason: 'Changed profession' };
      const existingProfile = createEmptyProfile(userId);
      existingProfile.professionalContext.field = 'arts';

      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue({ userId, profileData: existingProfile } as any);
      mockPrisma.userProfileDetailed.update.mockImplementation(async ({data}) => {
         return { userId, profileData: data?.profileData, updatedAt: new Date()} as any;
      });

      await onboardingController.updateProfile(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockPrisma.userProfileDetailed.update).toHaveBeenCalledWith(expect.objectContaining({
        data: { profileData: expect.objectContaining({ professionalContext: { field: 'engineering' } }) }
      }));
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ profile: expect.objectContaining({ professionalContext: { field: 'engineering' } }) })
      }));
    });
     it('should call next with AppError if user is not authenticated', async () => {
      mockRequest.user = undefined;
      mockRequest.body = { profileUpdates };
      await onboardingController.updateProfile(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Authentication required', 401));
    });
     it('should return 400 if profileUpdates is empty', async () => {
      mockRequest.user = { id: userId } as any;
      mockRequest.body = { profileUpdates: {} };
      await onboardingController.updateProfile(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Invalid or empty profileUpdates provided', 400));
    });
     it('should return 404 if profile not found for update', async () => {
      mockRequest.user = { id: userId }as any;
      mockRequest.body = { profileUpdates };
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue(null);
      await onboardingController.updateProfile(mockRequest as Request, mockResponse as Response, mockNext);
      expect(mockNext).toHaveBeenCalledWith(new AppError('Profile not found to update.', 404));
    });
  });
});

// Remove old integration tests as they are not relevant for unit testing with mocks
/*
... (old integration tests commented out or removed)
*/
