/**
 * Emergency Controller Integration Tests
 * Tests crisis/emergency endpoints via HTTP requests
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Emergency Controller Integration Tests', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = global.testSession.access_token;

  beforeEach(() => {
    // Mock authentication for protected routes
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: global.testUser },
      error: null,
    });
  });

  describe('GET /api/emergency/resources', () => {
    it('should return emergency resources successfully', async () => {
      const response = await request(app).get('/api/emergency/resources');

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          resources: expect.objectContaining({
            hotlines: expect.arrayContaining([
              expect.objectContaining({
                name: expect.any(String),
                phone: expect.any(String),
                description: expect.any(String),
                availability: expect.any(String),
              }),
            ]),
            islamicCounselors: expect.arrayContaining([
              expect.objectContaining({
                name: expect.any(String),
                contact: expect.any(String),
                specialization: expect.any(String),
                location: expect.any(String),
              }),
            ]),
            emergencyPrayers: expect.arrayContaining([
              expect.objectContaining({
                title: expect.any(String),
                arabic: expect.any(String),
                transliteration: expect.any(String),
                translation: expect.any(String),
              }),
            ]),
            selfCareSteps: expect.any(Array),
          }),
        },
      });
    });

    it('should return resources without authentication', async () => {
      const response = await request(app).get('/api/emergency/resources');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
    });

    it('should include location-based resources', async () => {
      const response = await request(app)
        .get('/api/emergency/resources')
        .query({ location: 'US' });

      expect(response.status).toBe(200);
      expect(response.body.data.resources.hotlines).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            region: expect.stringContaining('US'),
          }),
        ])
      );
    });

    it('should include Islamic emergency resources', async () => {
      const response = await request(app).get('/api/emergency/resources');

      expect(response.status).toBe(200);
      expect(response.body.data.resources).toHaveProperty('emergencyPrayers');
      expect(response.body.data.resources).toHaveProperty('islamicCounselors');
      expect(response.body.data.resources.emergencyPrayers).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            arabic: expect.any(String),
            transliteration: expect.any(String),
            translation: expect.any(String),
          }),
        ])
      );
    });
  });

  describe('POST /api/emergency/crisis-report', () => {
    const validCrisisReport = {
      severity: 'high',
      indicators: ['suicidal_ideation', 'self_harm'],
      userResponse: 'I have been having thoughts of ending my life',
      assessmentContext: {
        sessionId: 'assessment-session-123',
        step: 'mental_health_screening',
      },
      immediateRisk: true,
    };

    it('should handle crisis report successfully', async () => {
      // Mock crisis log insertion with .insert().select().single() chaining
      const insertBuilder = createMockQueryBuilder();
      insertBuilder.insert.mockReturnValue(insertBuilder);
      insertBuilder.select.mockReturnValue(insertBuilder);
      insertBuilder.single.mockResolvedValue({
        data: {
          escalation_id: 'crisis-escalation-123',
          user_id: global.testUser.id,
          severity: 'high',
          status: 'active',
          created_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(insertBuilder);

      const response = await request(app)
        .post('/api/emergency/crisis-report')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validCrisisReport);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          escalationId: expect.stringMatching(
            /^crisis-escalation-\d+-[a-z0-9]+$/
          ),
          immediateActions: expect.arrayContaining([
            'immediate_professional_help',
          ]),
          emergencyResources: expect.objectContaining({
            hotlines: expect.any(Array),
            islamicCounselors: expect.any(Array),
            emergencyPrayers: expect.any(Array),
          }),
          followUpScheduled: true,
        },
      });

      // Verify that the crisis escalation was created in the database
      expect(insertBuilder.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: global.testUser.id,
          severity: 'high',
          indicators: ['suicidal_ideation', 'self_harm'],
          immediate_risk: true,
          status: 'active',
        })
      );
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteReport = {
        severity: 'high',
        // Missing indicators and userResponse
      };

      const response = await request(app)
        .post('/api/emergency/crisis-report')
        .set('Authorization', `Bearer ${validToken}`)
        .send(incompleteReport);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('required');
    });

    it('should return 400 for invalid severity level', async () => {
      const invalidReport = {
        ...validCrisisReport,
        severity: 'invalid_level',
      };

      const response = await request(app)
        .post('/api/emergency/crisis-report')
        .set('Authorization', `Bearer ${validToken}`)
        .send(invalidReport);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('severity');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .post('/api/emergency/crisis-report')
        .send(validCrisisReport);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });

    it('should handle database errors', async () => {
      // Mock database error during crisis escalation insertion
      const errorInsertBuilder = createMockQueryBuilder();
      errorInsertBuilder.insert.mockReturnValue(errorInsertBuilder);
      errorInsertBuilder.select.mockReturnValue(errorInsertBuilder);
      errorInsertBuilder.single.mockResolvedValue({
        data: null,
        error: {
          message: 'Database connection failed',
          code: 'CONNECTION_ERROR',
        },
      });
      mockSupabase.from.mockReturnValueOnce(errorInsertBuilder);

      const response = await request(app)
        .post('/api/emergency/crisis-report')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validCrisisReport);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should log crisis report to database', async () => {
      const logInsertBuilder = createMockQueryBuilder();
      logInsertBuilder.insert.mockReturnValue(logInsertBuilder);
      logInsertBuilder.select.mockReturnValue(logInsertBuilder);
      logInsertBuilder.single.mockResolvedValue({
        data: {
          escalation_id: expect.stringMatching(/^crisis-escalation-/),
          user_id: global.testUser.id,
          severity: 'high',
          indicators: ['suicidal_ideation', 'self_harm'],
          immediate_risk: true,
          status: 'active',
          actions_taken: ['emergency_contacts_notified', 'resources_provided'],
          created_at: expect.any(String),
          updated_at: expect.any(String),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(logInsertBuilder);

      await request(app)
        .post('/api/emergency/crisis-report')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validCrisisReport);

      expect(logInsertBuilder.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: global.testUser.id,
          severity: 'high',
          indicators: ['suicidal_ideation', 'self_harm'],
          escalation_id: expect.stringMatching(/^crisis-escalation-/),
          immediate_risk: true,
        })
      );
    });
  });

  describe('GET /api/emergency/crisis-status/:escalationId', () => {
    const escalationId = 'crisis-escalation-123';

    it('should return crisis status successfully', async () => {
      // Mock first check for escalation existence and ownership
      const checkBuilder = createMockQueryBuilder();
      checkBuilder.select.mockReturnValue(checkBuilder);
      checkBuilder.eq.mockReturnValue(checkBuilder);
      checkBuilder.single.mockResolvedValue({
        data: {
          user_id: global.testUser.id,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(checkBuilder);

      // Mock second query for full escalation data
      const statusSelectBuilder = createMockQueryBuilder();
      statusSelectBuilder.select.mockReturnValue(statusSelectBuilder);
      statusSelectBuilder.eq.mockReturnValue(statusSelectBuilder);
      statusSelectBuilder.eq.mockReturnValue(statusSelectBuilder);
      statusSelectBuilder.single.mockResolvedValue({
        data: {
          escalation_id: escalationId,
          user_id: global.testUser.id,
          severity: 'high',
          status: 'active',
          actions_taken: ['emergency_contacts_notified', 'resources_provided'],
          follow_up_scheduled: true,
          created_at: '2024-01-01T00:00:00.000Z',
          updated_at: '2024-01-01T01:00:00.000Z',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(statusSelectBuilder);

      const response = await request(app)
        .get(`/api/emergency/crisis-status/${escalationId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          escalation: expect.objectContaining({
            id: escalationId,
            severity: 'high',
            status: 'active',
            actionsTaken: ['emergency_contacts_notified', 'resources_provided'],
            followUpScheduled: true,
          }),
        },
      });
    });

    it('should return 404 for non-existent escalation', async () => {
      const notFoundBuilder = createMockQueryBuilder();
      notFoundBuilder.select.mockReturnValue(notFoundBuilder);
      notFoundBuilder.eq.mockReturnValue(notFoundBuilder);
      notFoundBuilder.eq.mockReturnValue(notFoundBuilder);
      notFoundBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });
      mockSupabase.from.mockReturnValueOnce(notFoundBuilder);

      const response = await request(app)
        .get(`/api/emergency/crisis-status/${escalationId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('not found');
    });

    it('should return 403 for escalation not owned by user', async () => {
      // Mock first check showing escalation exists but belongs to different user
      const forbiddenBuilder = createMockQueryBuilder();
      forbiddenBuilder.select.mockReturnValue(forbiddenBuilder);
      forbiddenBuilder.eq.mockReturnValue(forbiddenBuilder);
      forbiddenBuilder.single.mockResolvedValue({
        data: {
          user_id: 'different-user-id',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(forbiddenBuilder);

      const response = await request(app)
        .get(`/api/emergency/crisis-status/${escalationId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(403);
      expect(response.body.status).toBe('fail');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app).get(
        `/api/emergency/crisis-status/${escalationId}`
      );

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });
  });

  describe('POST /api/emergency/follow-up', () => {
    const followUpData = {
      escalationId: 'crisis-escalation-123',
      status: 'improved',
      notes: 'Feeling better after speaking with counselor',
      additionalSupport: false,
    };

    it('should submit follow-up successfully', async () => {
      // Mock escalation exists and belongs to user
      const followUpSelectBuilder = createMockQueryBuilder();
      followUpSelectBuilder.select.mockReturnValue(followUpSelectBuilder);
      followUpSelectBuilder.eq.mockReturnValue(followUpSelectBuilder);
      followUpSelectBuilder.eq.mockReturnValue(followUpSelectBuilder);
      followUpSelectBuilder.single.mockResolvedValue({
        data: {
          id: 'escalation-db-id-123', // This is the database primary key
          user_id: global.testUser.id,
          status: 'active',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(followUpSelectBuilder);

      // Mock follow-up insertion with .insert().select().single() chaining
      const followUpInsertBuilder = createMockQueryBuilder();
      followUpInsertBuilder.insert.mockReturnValue(followUpInsertBuilder);
      followUpInsertBuilder.select.mockReturnValue(followUpInsertBuilder);
      followUpInsertBuilder.single.mockResolvedValue({
        data: {
          id: 'follow-up-123',
          escalation_id: followUpData.escalationId,
          user_id: global.testUser.id,
          status: 'improved',
          created_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(followUpInsertBuilder);

      // Mock escalation status update
      const updateBuilder = createMockQueryBuilder();
      updateBuilder.update.mockReturnValue(updateBuilder);
      updateBuilder.eq.mockResolvedValue({
        data: null,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(updateBuilder);

      const response = await request(app)
        .post('/api/emergency/follow-up')
        .set('Authorization', `Bearer ${validToken}`)
        .send(followUpData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          followUpRecorded: true,
          nextSteps: expect.any(Array),
        },
      });

      expect(followUpInsertBuilder.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          escalation_id: 'escalation-db-id-123', // Uses the database primary key
          user_id: global.testUser.id,
          status: 'improved',
          notes: followUpData.notes,
        })
      );
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteData = {
        escalationId: 'crisis-escalation-123',
        // Missing status
      };

      const response = await request(app)
        .post('/api/emergency/follow-up')
        .set('Authorization', `Bearer ${validToken}`)
        .send(incompleteData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 404 for non-existent escalation', async () => {
      const notFoundFollowUpBuilder = createMockQueryBuilder();
      notFoundFollowUpBuilder.select.mockReturnValue(notFoundFollowUpBuilder);
      notFoundFollowUpBuilder.eq.mockReturnValue(notFoundFollowUpBuilder);
      notFoundFollowUpBuilder.eq.mockReturnValue(notFoundFollowUpBuilder);
      notFoundFollowUpBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });
      mockSupabase.from.mockReturnValueOnce(notFoundFollowUpBuilder);

      const response = await request(app)
        .post('/api/emergency/follow-up')
        .set('Authorization', `Bearer ${validToken}`)
        .send(followUpData);

      expect(response.status).toBe(404);
      expect(response.body.status).toBe('fail');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .post('/api/emergency/follow-up')
        .send(followUpData);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });
  });
});
