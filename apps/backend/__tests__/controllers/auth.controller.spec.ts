/**
 * Auth Controller Integration Tests
 * Tests authentication endpoints via HTTP requests
 */

import request from 'supertest';
import app from '../../src/main'; // Express app
import { getSupabase } from '../../src/config/supabase';
import { prisma } from '../../src/config/database'; // Import prisma client

// Mock Prisma
jest.mock('../../src/config/database', () => ({
  prisma: {
    profile: {
      create: jest.fn(),
      findUnique: jest.fn(), // Add other methods if needed by other auth functions
      update: jest.fn(),
    },
    // Mock other models if necessary
  },
}));

// Mock Supabase
jest.mock('../../src/config/supabase', () => ({
  getSupabase: jest.fn(() => ({
    auth: {
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      getUser: jest.fn(),
      // Add other auth methods if needed
    },
    from: jest.fn(() => createMockQueryBuilder()), // Keep using if other db interactions are via supabase client
  })),
}));


// Helper function to create mock query builder (if still needed for other tests)
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Auth Controller Integration Tests', () => {
  // Get the mocked instances
  const mockSupabaseClient = getSupabase();
  const mockPrismaClient = prisma;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Default Supabase auth.signUp mock
    (mockSupabaseClient.auth.signUp as jest.Mock).mockResolvedValue({
      data: {
        user: { id: 'test-user-id', email: '<EMAIL>' },
        session: { access_token: 'test-access-token', refresh_token: 'test-refresh-token' }
      },
      error: null,
    });

    // Default Prisma profile.create mock
    (mockPrismaClient.profile.create as jest.Mock).mockResolvedValue({
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      fullName: 'Test User',
      avatarUrl: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  });

  describe('POST /api/auth/signup', () => {
    const baseSignupData = {
      email: '<EMAIL>',
      password: 'securePassword123',
      firstName: 'New',
      lastName: 'User',
    };

    it('should register a new user successfully with firstName and lastName', async () => {
      const signupData = { ...baseSignupData };
      const expectedSupabaseUser = { id: 'new-user-id', email: signupData.email };
      const expectedSession = { access_token: 'new-access-token', refresh_token: 'new-refresh-token' };

      (mockSupabaseClient.auth.signUp as jest.Mock).mockResolvedValueOnce({
        data: { user: expectedSupabaseUser, session: expectedSession },
        error: null,
      });

      const expectedProfile = {
        id: expectedSupabaseUser.id,
        email: signupData.email,
        firstName: signupData.firstName,
        lastName: signupData.lastName,
        // ... other fields that are defaulted or null
      };
      (mockPrismaClient.profile.create as jest.Mock).mockResolvedValueOnce(expectedProfile);

      const response = await request(app)
        .post('/api/auth/signup')
        .send(signupData);

      expect(response.status).toBe(201);
      expect(response.body).toEqual({ // Use toEqual for deep equality check
        status: 'success',
        data: {
          user: {
            id: expectedSupabaseUser.id,
            email: signupData.email,
            firstName: signupData.firstName,
            lastName: signupData.lastName,
          },
          token: expectedSession.access_token,
          refreshToken: expectedSession.refresh_token,
          requiresEmailConfirmation: false, // Assuming session means email is confirmed or not required
        },
      });

      expect(mockSupabaseClient.auth.signUp).toHaveBeenCalledWith({
        email: signupData.email,
        password: signupData.password,
      });
      expect(mockPrismaClient.profile.create).toHaveBeenCalledWith({
        data: {
          id: expectedSupabaseUser.id,
          email: signupData.email,
          firstName: signupData.firstName,
          lastName: signupData.lastName,
        },
      });
    });

    it('should register a new user successfully with firstName only (lastName is optional)', async () => {
      const { lastName, ...signupDataWithoutLastName } = baseSignupData; // Create data without lastName
      const expectedSupabaseUser = { id: 'new-user-id-2', email: signupDataWithoutLastName.email };
      const expectedSession = { access_token: 'new-access-token-2', refresh_token: 'new-refresh-token-2' };

      (mockSupabaseClient.auth.signUp as jest.Mock).mockResolvedValueOnce({
        data: { user: expectedSupabaseUser, session: expectedSession },
        error: null,
      });

      const expectedProfile = {
        id: expectedSupabaseUser.id,
        email: signupDataWithoutLastName.email,
        firstName: signupDataWithoutLastName.firstName,
        lastName: null, // Expect lastName to be null in DB
      };
      (mockPrismaClient.profile.create as jest.Mock).mockResolvedValueOnce(expectedProfile);

      const response = await request(app)
        .post('/api/auth/signup')
        .send(signupDataWithoutLastName);

      expect(response.status).toBe(201);
      expect(response.body).toEqual({
        status: 'success',
        data: {
          user: {
            id: expectedSupabaseUser.id,
            email: signupDataWithoutLastName.email,
            firstName: signupDataWithoutLastName.firstName,
            lastName: null, // Expect lastName to be null in response
          },
          token: expectedSession.access_token,
          refreshToken: expectedSession.refresh_token,
          requiresEmailConfirmation: false,
        },
      });

      expect(mockSupabaseClient.auth.signUp).toHaveBeenCalledWith({
        email: signupDataWithoutLastName.email,
        password: signupDataWithoutLastName.password,
      });
      expect(mockPrismaClient.profile.create).toHaveBeenCalledWith({
        data: {
          id: expectedSupabaseUser.id,
          email: signupDataWithoutLastName.email,
          firstName: signupDataWithoutLastName.firstName,
          lastName: null, // Ensure controller passes null for lastName
        },
      });
    });

    // The old "it('should register a new user successfully', async () => { ... });" is removed
    // as it's covered by the new more specific tests.

    it('should return 400 if firstName is missing', async () => {
      const { firstName, ...signupDataWithoutFirstName } = baseSignupData;

      const response = await request(app)
        .post('/api/auth/signup')
        .send(signupDataWithoutFirstName);

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        status: 'fail',
        message: 'First name is required', // Matches controller logic
        // Optionally, if your AppError structure includes a 'details' array:
        // details: expect.any(Array)
      });
      expect(mockSupabaseClient.auth.signUp).not.toHaveBeenCalled();
      expect(mockPrismaClient.profile.create).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid email format', async () => {
      const response = await request(app).post('/api/auth/signup').send({
        email: 'invalid-email',
        password: 'securePassword123',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 400 for short password', async () => {
      const response = await request(app).post('/api/auth/signup').send({
        email: '<EMAIL>',
        password: '123',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should handle Supabase auth.signUp errors', async () => {
      (mockSupabaseClient.auth.signUp as jest.Mock).mockResolvedValueOnce({
        data: { user: null, session: null }, // Or simply data: null
        error: { message: 'Supabase signup failed: User already registered', status: 400 },
      });

      const response = await request(app)
        .post('/api/auth/signup')
        .send(baseSignupData); // Use baseSignupData which includes firstName

      expect(response.status).toBe(400); // Or whatever status Supabase error implies
      expect(response.body).toEqual(expect.objectContaining({ // Use expect.objectContaining if not all fields are certain
        status: 'fail',
        message: 'Supabase signup failed: User already registered',
      }));
      expect(mockPrismaClient.profile.create).not.toHaveBeenCalled();
    });

    it('should handle Prisma profile.create errors after successful Supabase signup', async () => {
      const expectedSupabaseUser = { id: 'new-user-id-3', email: baseSignupData.email };
      const expectedSession = { access_token: 'new-access-token-3', refresh_token: 'new-refresh-token-3' };

      (mockSupabaseClient.auth.signUp as jest.Mock).mockResolvedValueOnce({
        data: { user: expectedSupabaseUser, session: expectedSession },
        error: null,
      });

      (mockPrismaClient.profile.create as jest.Mock).mockRejectedValueOnce(
        new Error('Prisma profile creation failed') // Simulate a generic error
      );

      const response = await request(app)
        .post('/api/auth/signup')
        .send(baseSignupData);

      expect(response.status).toBe(500);
    });
  });

  describe('POST /api/auth/login', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'securePassword123',
    };

    it('should login user successfully', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: { user: global.testUser, session: global.testSession },
        error: null,
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: global.testUser.id,
            email: global.testUser.email,
          }),
          session: expect.objectContaining({
            access_token: expect.any(String),
          }),
        },
      });

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: validLoginData.email,
        password: validLoginData.password,
      });
    });

    it('should return 400 for invalid email format', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: 'invalid-email',
        password: 'password123',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 401 for invalid credentials', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: null,
        error: { message: 'Invalid login credentials' },
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('Invalid login credentials');
    });

    it('should require password field', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
      });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });
  });

  describe('GET /api/auth/profile', () => {
    it('should return user profile successfully', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const profileSelectBuilder = createMockQueryBuilder();
      profileSelectBuilder.select.mockReturnValue(profileSelectBuilder);
      profileSelectBuilder.eq.mockReturnValue(profileSelectBuilder);
      profileSelectBuilder.single.mockResolvedValue({
        data: global.testProfile,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(profileSelectBuilder);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: global.testUser.id,
            email: global.testUser.email,
          }),
          profile: expect.objectContaining({
            user_id: global.testProfile.user_id,
            email: global.testProfile.email,
          }),
        },
      });
    });

    it('should return 401 without authorization header', async () => {
      const response = await request(app).get('/api/auth/profile');

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('No token provided');
    });

    it('should return 401 with invalid token', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: null,
        error: { message: 'Invalid token' },
      });

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('Invalid or expired token');
    });

    it('should handle profile not found gracefully', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const notFoundSelectBuilder = createMockQueryBuilder();
      notFoundSelectBuilder.select.mockReturnValue(notFoundSelectBuilder);
      notFoundSelectBuilder.eq.mockReturnValue(notFoundSelectBuilder);
      notFoundSelectBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116', message: 'No rows found' },
      });
      mockSupabase.from.mockReturnValueOnce(notFoundSelectBuilder);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`);

      expect(response.status).toBe(200);
      expect(response.body.data.profile).toBeNull();
    });
  });

  describe('PATCH /api/auth/profile', () => {
    const validUpdateData = {
      selectedLayers: ['jism', 'nafs', 'qalb'],
      journeyType: 'comprehensive',
    };

    it('should update profile successfully', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const updatedProfile = {
        ...global.testProfile,
        ...validUpdateData,
        updated_at: new Date().toISOString(),
      };

      const upsertBuilder = createMockQueryBuilder();
      upsertBuilder.upsert.mockReturnValue(upsertBuilder);
      upsertBuilder.select.mockReturnValue(upsertBuilder);
      upsertBuilder.single.mockResolvedValue({
        data: updatedProfile,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(upsertBuilder);

      const response = await request(app)
        .patch('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`)
        .send(validUpdateData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          profile: expect.objectContaining({
            selected_layers: validUpdateData.selectedLayers,
            journey_type: validUpdateData.journeyType,
          }),
        },
      });
    });

    it('should return 400 for invalid selectedLayers', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const response = await request(app)
        .patch('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`)
        .send({
          selectedLayers: 'not-an-array',
          journeyType: 'comprehensive',
        });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 401 without authorization', async () => {
      const response = await request(app)
        .patch('/api/auth/profile')
        .send(validUpdateData);

      expect(response.status).toBe(401);
    });

    it('should handle database update errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValueOnce({
        data: { user: global.testUser },
        error: null,
      });

      const errorUpsertBuilder = createMockQueryBuilder();
      errorUpsertBuilder.upsert.mockReturnValue(errorUpsertBuilder);
      errorUpsertBuilder.select.mockReturnValue(errorUpsertBuilder);
      errorUpsertBuilder.single.mockResolvedValue({
        data: null,
        error: { message: 'Database update failed' },
      });
      mockSupabase.from.mockReturnValueOnce(errorUpsertBuilder);

      const response = await request(app)
        .patch('/api/auth/profile')
        .set('Authorization', `Bearer ${global.testSession.access_token}`)
        .send(validUpdateData);

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Database update failed');
    });
  });
});
