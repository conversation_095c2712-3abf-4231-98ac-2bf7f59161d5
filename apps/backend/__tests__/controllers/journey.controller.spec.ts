/**
 * Journey Controller Integration Tests
 * Tests journey endpoints via HTTP requests
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder (copied from setup.ts)
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Journey Controller Integration Tests', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = global.testSession.access_token;

  beforeEach(() => {
    // Mock authentication
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: global.testUser },
      error: null,
    });
  });

  describe('POST /api/journey/create', () => {
    const validJourneyData = {
      assessmentId: '550e8400-e29b-41d4-a716-************', // Valid UUID format
      preferences: {
        duration: 21,
        dailyTimeCommitment: 30,
        ruqyaIntegrationLevel: 'intermediate',
        communityIntegration: true,
      },
    };

    it('should create personalized journey successfully', async () => {
      // Mock assessment exists
      const assessmentSelectBuilder = createMockQueryBuilder();
      assessmentSelectBuilder.select.mockReturnValue(assessmentSelectBuilder);
      assessmentSelectBuilder.eq.mockReturnValue(assessmentSelectBuilder);
      assessmentSelectBuilder.single.mockResolvedValue({
        data: {
          id: '550e8400-e29b-41d4-a716-************',
          user_id: global.testUser.id,
          diagnosis_data: {
            overallSeverity: 'moderate',
            primaryLayer: 'nafs',
            affectedLayers: ['nafs', 'qalb'],
            crisisLevel: 'none',
            crisisIndicators: [],
            immediateActions: [],
            nextSteps: ['Begin your healing journey'],
            recommendedJourneyType: 'comprehensive',
            estimatedHealingDuration: 21,
            confidence: 0.85,
          },
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(assessmentSelectBuilder);

      // Mock journey creation
      const insertBuilder = createMockQueryBuilder();
      insertBuilder.insert.mockReturnValue(insertBuilder);
      insertBuilder.select.mockReturnValue(insertBuilder);
      insertBuilder.single.mockResolvedValue({
        data: {
          id: 'journey-123',
          user_id: global.testUser.id,
          assessment_id: '550e8400-e29b-41d4-a716-************',
          type: 'comprehensive',
          status: 'created',
          title: 'Healing the Nafs: A 21-Day Journey',
          description: 'A comprehensive journey to heal your nafs',
          personalized_welcome:
            'Welcome to your personalized healing journey...',
          days: Array.from({ length: 21 }, (_, i) => ({
            day: i + 1,
            title: `Day ${i + 1}: Foundation`,
            practices: [],
            content: [],
          })),
          created_at: new Date().toISOString(),
        },
        error: null,
      });

      // Mock any additional database calls that might happen
      mockSupabase.from.mockReturnValue(insertBuilder);

      // Mock AI service responses
      global.mockAiService.generateJourneyParameters.mockResolvedValueOnce({
        duration: 21,
        timeCommitment: 30,
        primaryLayer: 'nafs',
        secondaryLayers: ['qalb'],
        ruqyaLevel: 'intermediate',
        communitySupport: true,
        type: 'comprehensive',
      });

      global.mockAiService.generateJourneyContent.mockResolvedValueOnce({
        title: 'Healing the Nafs: A 21-Day Journey',
        description: 'A comprehensive journey to heal your nafs',
        personalizedWelcome: 'Welcome to your personalized healing journey...',
        days: Array.from({ length: 21 }, (_, i) => ({
          day: i + 1,
          title: `Day ${i + 1}: Foundation`,
          practices: [],
          content: [],
        })),
      });

      const response = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validJourneyData);

      console.log('Journey creation response:', response.status, response.body);
      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          journey: expect.objectContaining({
            id: expect.any(String),
            user_id: global.testUser.id,
            assessment_id: '550e8400-e29b-41d4-a716-************',
            type: 'comprehensive',
            status: 'created',
            title: 'Healing the Nafs: A 21-Day Journey',
            description: expect.any(String),
            days: expect.any(Array),
          }),
        },
      });
    });

    it('should return 400 for missing assessment ID', async () => {
      const response = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ preferences: validJourneyData.preferences });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('Valid assessment ID required');
    });

    it('should return 404 for non-existent assessment', async () => {
      const notFoundAssessmentBuilder = createMockQueryBuilder();
      notFoundAssessmentBuilder.select.mockReturnValue(
        notFoundAssessmentBuilder
      );
      notFoundAssessmentBuilder.eq.mockReturnValue(notFoundAssessmentBuilder);
      notFoundAssessmentBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });
      mockSupabase.from.mockReturnValueOnce(notFoundAssessmentBuilder);

      const response = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send(validJourneyData);

      expect(response.status).toBe(404);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('Assessment not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .post('/api/journey/create')
        .send(validJourneyData);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });
  });

  describe('POST /api/journey/:journeyId/start', () => {
    const journeyId = '123e4567-e89b-12d3-a456-426614174000';

    it('should start journey successfully', async () => {
      // Mock journey exists and belongs to user
      const journeySelectBuilder = createMockQueryBuilder();
      journeySelectBuilder.select.mockReturnValue(journeySelectBuilder);
      journeySelectBuilder.eq.mockReturnValue(journeySelectBuilder);
      journeySelectBuilder.eq.mockReturnValue(journeySelectBuilder);
      journeySelectBuilder.single.mockResolvedValue({
        data: {
          id: journeyId,
          user_id: global.testUser.id,
          status: 'created',
          title: 'Test Journey',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(journeySelectBuilder);

      // Mock journey update
      const journeyUpdateBuilder = createMockQueryBuilder();
      journeyUpdateBuilder.update.mockReturnValue(journeyUpdateBuilder);
      journeyUpdateBuilder.eq.mockReturnValue(journeyUpdateBuilder);
      journeyUpdateBuilder.eq.mockReturnValue(journeyUpdateBuilder);
      journeyUpdateBuilder.select.mockReturnValue(journeyUpdateBuilder);
      journeyUpdateBuilder.single.mockResolvedValue({
        data: {
          id: journeyId,
          user_id: global.testUser.id,
          status: 'active',
          title: 'Test Journey',
          started_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(journeyUpdateBuilder);

      const response = await request(app)
        .post(`/api/journey/${journeyId}/start`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          journey: expect.objectContaining({
            id: journeyId,
            status: 'active',
            started_at: expect.any(String),
          }),
        },
      });
    });

    it('should return 404 for non-existent journey', async () => {
      const notFoundJourneyBuilder = createMockQueryBuilder();
      notFoundJourneyBuilder.select.mockReturnValue(notFoundJourneyBuilder);
      notFoundJourneyBuilder.eq.mockReturnValue(notFoundJourneyBuilder);
      notFoundJourneyBuilder.eq.mockReturnValue(notFoundJourneyBuilder);
      notFoundJourneyBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });
      mockSupabase.from.mockReturnValueOnce(notFoundJourneyBuilder);

      const response = await request(app)
        .post(`/api/journey/${journeyId}/start`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
      expect(response.body.status).toBe('fail');
    });

    it('should return 403 for journey not owned by user', async () => {
      const forbiddenJourneyBuilder = createMockQueryBuilder();
      forbiddenJourneyBuilder.select.mockReturnValue(forbiddenJourneyBuilder);
      forbiddenJourneyBuilder.eq.mockReturnValue(forbiddenJourneyBuilder);
      forbiddenJourneyBuilder.eq.mockReturnValue(forbiddenJourneyBuilder);
      forbiddenJourneyBuilder.single.mockResolvedValue({
        data: {
          id: journeyId,
          user_id: 'different-user-id',
          status: 'created',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(forbiddenJourneyBuilder);

      const response = await request(app)
        .post(`/api/journey/${journeyId}/start`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(403);
      expect(response.body.status).toBe('fail');
    });
  });

  describe('POST /api/journey/:journeyId/progress', () => {
    const journeyId = '123e4567-e89b-12d3-a456-426614174000';
    const validProgressData = {
      dayNumber: 5,
      practiceResults: {
        dhikr_completed: true,
        prayer_on_time: true,
        quran_reading: false,
        reflection_notes: 'Felt more peaceful today',
      },
    };

    it('should update journey progress successfully', async () => {
      // Mock journey exists
      const progressJourneyBuilder = createMockQueryBuilder();
      progressJourneyBuilder.select.mockReturnValue(progressJourneyBuilder);
      progressJourneyBuilder.eq.mockReturnValue(progressJourneyBuilder);
      progressJourneyBuilder.single.mockResolvedValue({
        data: {
          id: journeyId,
          user_id: global.testUser.id,
          completed_days: [1, 2, 3, 4],
          total_progress: 19,
          duration: 21,
          days: Array.from({ length: 21 }, (_, i) => ({ day: i + 1 })),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(progressJourneyBuilder);

      // Mock progress insertion
      const progressInsertBuilder = createMockQueryBuilder();
      progressInsertBuilder.insert.mockReturnValue(progressInsertBuilder);
      progressInsertBuilder.select.mockReturnValue(progressInsertBuilder);
      progressInsertBuilder.single.mockResolvedValue({
        data: {
          id: 'progress-123',
          journey_id: journeyId,
          user_id: global.testUser.id,
          day_number: 5,
          practices_completed: [validProgressData.practiceResults],
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(progressInsertBuilder);

      // Mock journey update
      const updateBuilder = createMockQueryBuilder();
      updateBuilder.update.mockReturnValue(updateBuilder);
      updateBuilder.eq.mockReturnValue(updateBuilder);
      updateBuilder.single.mockResolvedValue({
        data: null,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(updateBuilder);

      const response = await request(app)
        .post(`/api/journey/${journeyId}/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(validProgressData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          progress: expect.objectContaining({
            dayNumber: 5,
            completed: true,
          }),
        },
      });
    });

    it('should return 400 for invalid day number', async () => {
      const response = await request(app)
        .post(`/api/journey/${journeyId}/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          dayNumber: 0,
          practiceResults: validProgressData.practiceResults,
        });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 400 for missing practice results', async () => {
      const response = await request(app)
        .post(`/api/journey/${journeyId}/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send({ dayNumber: 5 });

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });
  });

  describe('GET /api/journey/user-journeys', () => {
    it('should retrieve user journeys successfully', async () => {
      const mockJourneys = [
        {
          id: 'journey-1',
          title: 'Nafs Healing Journey',
          status: 'active',
          total_progress: 45,
          created_at: '2024-01-01T00:00:00.000Z',
        },
        {
          id: 'journey-2',
          title: 'Qalb Purification',
          status: 'completed',
          total_progress: 100,
          created_at: '2024-01-15T00:00:00.000Z',
        },
      ];

      const selectBuilder = createMockQueryBuilder();
      selectBuilder.select.mockReturnValue(selectBuilder);
      selectBuilder.eq.mockReturnValue(selectBuilder);
      selectBuilder.order.mockResolvedValue({
        data: mockJourneys,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(selectBuilder);

      const response = await request(app)
        .get('/api/journey/user-journeys')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          journeys: expect.arrayContaining([
            expect.objectContaining({
              id: 'journey-1',
              status: 'active',
              total_progress: 45,
            }),
            expect.objectContaining({
              id: 'journey-2',
              status: 'completed',
              total_progress: 100,
            }),
          ]),
        },
      });
    });

    it('should return empty array for user with no journeys', async () => {
      const emptySelectBuilder = createMockQueryBuilder();
      emptySelectBuilder.select.mockReturnValue(emptySelectBuilder);
      emptySelectBuilder.eq.mockReturnValue(emptySelectBuilder);
      emptySelectBuilder.order.mockResolvedValue({
        data: [],
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(emptySelectBuilder);

      const response = await request(app)
        .get('/api/journey/user-journeys')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.journeys).toEqual([]);
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app).get('/api/journey/user-journeys');

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });
  });
});
