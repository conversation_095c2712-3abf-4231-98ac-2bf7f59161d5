/**
 * Feature 02: Personalized Healing Journeys - Integration Tests
 */

import request from 'supertest';
import app from '../../src/main'; // Assuming 'app' is the exported Express app instance
import { getSupabase } from '../../src/config/supabase'; // To access the mocked Supabase

// Helper to access the mocked Supabase client if not already global or easily accessible
// const mockSupabase = getSupabase(); // This is already globally mocked in setup.ts

describe('Feature 02 - Personalized Healing Journeys Integration Tests', () => {
  const validToken = 'test-feature02-token'; // Use a consistent test token
  const testUserId = 'feature02-user-uuid';
  const testAssessmentId = 'assessment-uuid-for-f002';
  let createdJourneyId: string | null = null;

  beforeAll(() => {
    // Ensure global mocks from setup.ts are active if needed,
    // especially for services not directly re-mocked in beforeEach.
  });

  beforeEach(() => {
    // Reset mocks before each test to ensure isolation
    jest.clearAllMocks();

    // Mock authentication for all requests in this suite
    (getSupabase() as any).auth.getUser.mockResolvedValue({
      data: { user: { id: testUserId, email: '<EMAIL>' } },
      error: null,
    });
  });

  describe('POST /api/journey/create (Personalized Journey Creation)', () => {
    it('should successfully create a personalized journey (Happy Path)', async () => {
      // Mock dependencies:
      // 1. prisma.spiritualDiagnoses.findUnique (for assessment)
      (getSupabase() as any).from('spiritual_diagnoses').select().eq().single.mockResolvedValueOnce({
        data: { id: testAssessmentId, user_id: testUserId, diagnosis_data: { primaryLayer: 'qalb', crisisLevel: 'none', overallSeverityScore: 3 } /* ...other fields */ },
        error: null,
      });

      // 2. prisma.userProfileDetailed.findUnique (for user profile, via journeyService.getUserProfile)
      (getSupabase() as any).from('user_profiles_detailed').select().eq().single.mockResolvedValueOnce({ // Adjusted table name
        data: { userId: testUserId, profile_data: { name: 'Test User', profession: 'developer', awarenessLevel: 'symptom_aware' } },
        error: null,
      });

      // 3. aiService.generateJourneyParameters
      (global as any).mockAiService.generateJourneyParameters.mockResolvedValueOnce({
        type: 'heart_purification', duration: 14, timeCommitment: 20,
        primaryLayer: 'qalb', secondaryLayers: ['aql'], ruqyaLevel: 'basic',
        communitySupport: false, culturalAdaptations: [], recommendations: ['Focus on dhikr'],
      });

      // 4. aiService.generateJourneyContent
      (global as any).mockAiService.generateJourneyContent.mockResolvedValueOnce({
        title: 'Test Journey Title', description: 'Test Journey Description',
        personalizedWelcome: 'Welcome Test User!',
        days: [{ dayNumber: 1, theme: 'Test Theme', practices: [{id: 'p1', title: 'Test Practice'}] }], // Simplified
      });

      // 5. prisma.journey.create
      const mockJourneyData = {
        id: 'new-journey-uuid-123', userId: testUserId, assessmentId: testAssessmentId,
        title: 'Test Journey Title', status: 'created', type: 'heart_purification',
        duration: 14, /* ... other fields based on AI params ... */
        created_at: new Date().toISOString(), updated_at: new Date().toISOString()
      };
      (getSupabase() as any).from('journeys').insert().select().single.mockResolvedValueOnce({
        data: mockJourneyData,
        error: null,
      });

      // Mock JourneyDay and DailyPracticeInJourney creations (simplified: assume they work if journey creation is mocked)
      (getSupabase() as any).from('journey_days').insert().select().single.mockResolvedValue({ data: { id: 'day-uuid' }, error: null });
      (getSupabase() as any).from('daily_practices_journey').insert().select().single.mockResolvedValue({ data: { id: 'dp-uuid' }, error: null });

      // Mock findUnique for the final return (after creating days/practices)
       (getSupabase() as any).from('journeys').select().eq().single.mockResolvedValueOnce({
        data: { ...mockJourneyData, journeyDays: [{id: 'day-uuid', dailyPracticesInJourney: [{id: 'dp-uuid'}] }] }, // include relations
        error: null,
      });


      const response = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          assessmentId: testAssessmentId,
          preferences: { duration: 14, dailyTimeCommitment: 20 },
        });

      expect(response.status).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.data.journey.id).toBe('new-journey-uuid-123');
      expect(response.body.data.journey.title).toBe('Test Journey Title');
      createdJourneyId = response.body.data.journey.id; // Save for subsequent tests
    });

    it('should return 404 if assessment is not found', async () => {
      (getSupabase() as any).from('spiritual_diagnoses').select().eq().single.mockResolvedValueOnce({
        data: null, error: { message: 'Assessment not found', code: 'PGRST116' } // Or just null data
      });

      const response = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ assessmentId: 'non-existent-assessment-uuid' });

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('Assessment not found');
    });

    it('should return 400 for invalid preferences', async () => {
      const response = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ assessmentId: testAssessmentId, preferences: { duration: 1 } }); // Duration too short

      expect(response.status).toBe(400);
      // Further assert specific error message if desired based on validator
    });
  });

  describe('POST /api/journey/{journeyId}/progress', () => {
    beforeEach(() => {
      // Assume createdJourneyId is set by a successful create test or set manually for isolated test
      if (!createdJourneyId) {
          createdJourneyId = "fallback-journey-id-for-progress";
          // In real jest, you might skip these tests if createdJourneyId is null, or ensure create runs first.
      }
    });

    it('should successfully update journey progress (Happy Path)', async () => {
      // Mocks for journeyService.recordDailyProgress -> prisma.journeyProgress.create
      (getSupabase() as any).from('journey_progress').insert().select().single.mockResolvedValueOnce({
        data: { id: 'progress-uuid', journeyId: createdJourneyId, dayNumber: 1, /* ... other fields ... */ },
        error: null,
      });
      // Mocks for _updateJourneyAggregateProgress
      (getSupabase() as any).from('journeys').select().eq().single.mockResolvedValueOnce({
        data: { id: createdJourneyId, userId: testUserId, completedDays: [], duration: 14, currentDay: 1, status: 'active' },
        error: null,
      });
      (getSupabase() as any).from('journeys').update().eq().select().single.mockResolvedValueOnce({
        data: { id: createdJourneyId, currentDay: 2, totalProgress: 100/14 },
        error: null,
      });
      // Mock AI service calls within recordDailyProgress
      (global as any).mockAiService.analyzeCrisisIndicators.mockResolvedValueOnce({ level: 'none' });
      (global as any).mockAiService.generateAdaptiveRecommendations.mockResolvedValueOnce({ recommendations: [], adjustments: [] });

      const response = await request(app)
        .post(`/api/journey/${createdJourneyId}/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          journeyId: createdJourneyId, // Often journeyId is in body for such endpoints too
          dayNumber: 1,
          practiceResults: { "somePractice": "completed" },
          overallRating: 5,
          moodAfter: 8
        });

      expect(response.status).toBe(201); // As per controller
      expect(response.body.status).toBe('success');
      expect(response.body.data.progress.journeyId).toBe(createdJourneyId);
    });

    it('should return 404 if journey not found for progress update', async () => {
      (getSupabase() as any).from('journey_progress').insert().select().single.mockImplementationOnce(() => {
        // This mock might not even be reached if the service checks journey existence first.
        // Let's assume journeyService.recordDailyProgress (or its first DB call) fails to find the journey.
        // This requires mocking the initial findUnique within recordDailyProgress if it exists,
        // or the first call that would fail. For simplicity, assume the create fails due to FK or it's caught.
        // More accurate: journeyService.recordDailyProgress would throw AppError.
        // For now, mock the call that would be made by the controller to the service.
         throw new Error("Simulating journey not found before progress creation"); // This needs better mocking of service layer
      });

      // To properly test this, we'd mock journeyService.recordDailyProgress to throw AppError
      // For now, this test is conceptual for what to assert.
      // A more direct approach:
      // jest.spyOn(journeyService, 'recordDailyProgress').mockRejectedValueOnce(new AppError('Journey not found', 404));


      // This test is tricky without directly mocking the service layer method called by controller.
      // The current controller calls journeyService.recordDailyProgress.
      // Let's assume for now the endpoint itself will return 404 if journeyService throws.
      // A proper mock would be:
      // const journeyServiceInstance = require('../../src/services/journey.service').journeyService;
      // jest.spyOn(journeyServiceInstance, 'recordDailyProgress').mockRejectedValueOnce(new AppError('Journey not found', 404));

      const response = await request(app)
        .post(`/api/journey/non-existent-journey/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send({ dayNumber: 1, practiceResults: {} });

      // This assertion depends on how the error bubbles up and is handled.
      // If journeyService.recordDailyProgress throws AppError(404), and errorHandler handles it.
      expect(response.status).toBe(404); // Or 500 if not caught gracefully by service throwing AppError
      // expect(response.body.message).toContain('Journey not found');
    });
  });

  describe('GET /api/analytics/journey?journeyId={journeyId}', () => {
    it('should retrieve analytics for a specific journey (Happy Path)', async () => {
      // Mocks for analyticsService.getJourneyAnalytics
      (getSupabase() as any).from('journeys').select().eq().single.mockResolvedValueOnce({
         data: { id: createdJourneyId || "test-journey-id", userId: testUserId, duration: 14, completedDays: [1,2], currentDay: 3, /* other journey fields */ },
         error: null
      });
      (getSupabase() as any).from('journey_progress').select().eq().order.mockResolvedValueOnce({
          data: [ { dayNumber:1, overallRating: 5}, {dayNumber:2, overallRating: 4} ], // sample progress
          error: null
      });
      // Mock any RPC calls if analyticsService uses them directly for aggregation

      const response = await request(app)
        .get(`/api/analytics/journey?journeyId=${createdJourneyId || "test-journey-id"}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data).toHaveProperty('currentJourney'); // Or whatever structure analyticsService returns
      // expect(response.body.data.currentJourney.id).toBe(createdJourneyId || "test-journey-id");
    });

    it('should return 404 if journey not found for analytics', async () => {
       (getSupabase() as any).from('journeys').select().eq().single.mockResolvedValueOnce({
         data: null, error: {message: "Not found", code: "PGRST116"}
      });

      const response = await request(app)
        .get(`/api/analytics/journey?journeyId=non-existent-analytics-journey`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
      // expect(response.body.message).toContain('Journey not found');
    });
  });

});
