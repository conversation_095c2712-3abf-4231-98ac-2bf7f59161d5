/**
 * Journey Integration Tests (Feature 2)
 * Tests complete healing journey workflows and progress tracking
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Journey Integration Tests (Feature 2)', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = 'valid-journey-token-123';
  const testUser = {
    id: 'journey-user-id-123',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00.000Z',
  };

  const assessmentResults = {
    layerPriorities: ['Nafs', 'Qalb', 'Aql', 'Ruh', 'Jism'],
    overallSeverity: 'moderate',
    recommendedJourneyType: 'comprehensive',
    spiritualLandscape: {
      personalizedMessage:
        'Your journey focuses on purifying the Nafs and strengthening the Qalb',
      islamicInsights: [
        'Focus on dhikr for Nafs purification',
        'Strengthen Salah connection',
      ],
      layerAnalysis: {
        Nafs: { severity: 'high', indicators: ['anger', 'pride', 'desires'] },
        Qalb: {
          severity: 'moderate',
          indicators: ['spiritual_disconnect', 'doubt'],
        },
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock authentication
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: testUser },
      error: null,
    });
  });

  describe('Complete Journey Creation and Execution Workflow', () => {
    it('should create and start a personalized healing journey from assessment results', async () => {
      // Step 1: Create personalized journey
      const createJourneyData = {
        assessmentId: 'assessment-session-456', // Use a valid assessment ID
        preferences: {
          duration: 21,
          dailyTimeCommitment: 30, // minutes per day
          ruqyaIntegrationLevel: 'intermediate',
          communityIntegration: true,
        },
      };

      // Mock AI service journey generation
      global.mockAiService.generateJourneyParameters.mockResolvedValueOnce({
        duration: 21,
        timeCommitment: 30,
        primaryLayer: 'Nafs',
        secondaryLayers: ['Qalb', 'Aql'],
        ruqyaLevel: 'intermediate',
        communitySupport: true,
        type: 'comprehensive',
        difficulty: 'moderate',
      });

      global.mockAiService.generateJourneyContent.mockResolvedValueOnce({
        title: 'Purifying the Nafs: A 21-Day Spiritual Healing Journey',
        description:
          'A comprehensive journey focusing on Nafs purification and Qalb strengthening through Islamic practices',
        personalizedWelcome:
          'Assalamu alaikum! Your personalized journey begins with understanding and taming your Nafs...',
        days: Array.from({ length: 21 }, (_, i) => ({
          day: i + 1,
          title: `Day ${i + 1}: ${
            i < 7 ? 'Foundation' : i < 14 ? 'Purification' : 'Integration'
          }`,
          theme:
            i < 7
              ? 'building_awareness'
              : i < 14
              ? 'active_purification'
              : 'spiritual_integration',
          practices: [
            {
              type: 'dhikr',
              name: 'Morning Tasbih',
              duration: 10,
              instructions:
                'Recite SubhanAllah 33 times, Alhamdulillah 33 times, Allahu Akbar 34 times',
            },
            {
              type: 'reflection',
              name: 'Nafs Awareness',
              duration: 15,
              instructions:
                'Reflect on moments when your Nafs influenced your actions today',
            },
          ],
          content: [
            {
              type: 'education',
              title: 'Understanding the Nafs',
              content:
                'The Nafs represents our ego, desires, and lower self...',
            },
          ],
          goals: [
            'Increase awareness of Nafs patterns',
            'Strengthen dhikr practice',
          ],
        })),
        milestones: [
          {
            day: 7,
            title: 'Foundation Complete',
            description: 'Basic awareness established',
          },
          {
            day: 14,
            title: 'Purification Midpoint',
            description: 'Active transformation underway',
          },
          {
            day: 21,
            title: 'Journey Complete',
            description: 'Integration and new habits formed',
          },
        ],
      });

      // Mock journey creation in database
      const journeyBuilder = createMockQueryBuilder();
      journeyBuilder.insert.mockReturnValue(journeyBuilder);
      journeyBuilder.single.mockResolvedValue({
        data: {
          journey_id: 'journey-123-abc',
          user_id: testUser.id,
          title: 'Purifying the Nafs: A 21-Day Spiritual Healing Journey',
          type: 'comprehensive',
          duration: 21,
          primary_layer: 'Nafs',
          secondary_layers: ['Qalb', 'Aql'],
          status: 'created',
          created_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(journeyBuilder);

      const createResponse = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send(createJourneyData);

      expect(createResponse.status).toBe(201);
      expect(createResponse.body).toMatchObject({
        status: 'success',
        data: {
          journey: expect.objectContaining({
            journeyId: 'journey-123-abc',
            title: expect.stringContaining('Nafs'),
            duration: 21,
            primaryLayer: 'Nafs',
            status: 'created',
          }),
          content: expect.objectContaining({
            personalizedWelcome: expect.stringContaining('Assalamu alaikum'),
            days: expect.arrayContaining([
              expect.objectContaining({
                day: 1,
                practices: expect.arrayContaining([
                  expect.objectContaining({
                    type: 'dhikr',
                    name: expect.any(String),
                  }),
                ]),
              }),
            ]),
          }),
          nextStep: 'start_journey',
        },
      });

      const journeyId = createResponse.body.data.journey.journeyId;

      // Step 2: Start the journey
      const startBuilder = createMockQueryBuilder();
      startBuilder.update.mockReturnValue(startBuilder);
      startBuilder.eq.mockReturnValue(startBuilder);
      startBuilder.select.mockReturnValue(startBuilder);
      startBuilder.single.mockResolvedValue({
        data: {
          journey_id: journeyId,
          status: 'active',
          started_at: new Date().toISOString(),
          current_day: 1,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(startBuilder);

      const startResponse = await request(app)
        .post(`/api/journey/${journeyId}/start`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(startResponse.status).toBe(200);
      expect(startResponse.body).toMatchObject({
        status: 'success',
        data: {
          journeyStarted: true,
          currentDay: 1,
          todaysPractices: expect.arrayContaining([
            expect.objectContaining({
              type: 'dhikr',
              name: 'Morning Tasbih',
            }),
          ]),
          welcomeMessage: expect.stringContaining('journey begins'),
        },
      });

      // Step 3: Complete Day 1 practices and submit progress
      const day1Progress = {
        dayNumber: 1,
        practiceResults: {
          dhikr_morning_tasbih: {
            completed: true,
            duration: 12, // minutes
            quality: 'good',
            notes: 'Felt peaceful during recitation',
            difficulty: 'easy',
          },
          reflection_nafs_awareness: {
            completed: true,
            duration: 18,
            quality: 'excellent',
            notes: 'Noticed my Nafs pushing me to skip Fajr prayer',
            insights: ['Awareness is the first step to change'],
            difficulty: 'moderate',
          },
        },
        overallExperience: {
          mood: 'hopeful',
          spiritualConnection: 'improved',
          challenges: ['waking_up_early'],
          gratitude: ['opportunity_to_heal', 'islamic_guidance'],
        },
        timeSpent: 30,
      };

      // Mock progress tracking
      const progressBuilder = createMockQueryBuilder();
      progressBuilder.insert.mockReturnValue(progressBuilder);
      progressBuilder.single.mockResolvedValue({
        data: {
          progress_id: 'progress-day1-456',
          journey_id: journeyId,
          user_id: testUser.id,
          day_number: 1,
          completed_at: new Date().toISOString(),
          practice_results: day1Progress.practiceResults,
          overall_experience: day1Progress.overallExperience,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(progressBuilder);

      // Mock AI analysis of progress
      global.mockAiService.generateAdaptiveRecommendations.mockResolvedValueOnce(
        [
          {
            type: 'encouragement',
            message:
              'Excellent start! Your awareness of the Nafs is already developing.',
          },
          {
            type: 'adjustment',
            message:
              'Consider setting a gentle alarm 10 minutes before Fajr to ease the transition.',
          },
          {
            type: 'next_focus',
            message:
              'Tomorrow we will deepen your dhikr practice with breath awareness.',
          },
        ]
      );

      const progressResponse = await request(app)
        .post(`/api/journey/${journeyId}/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(day1Progress);

      expect(progressResponse.status).toBe(200);
      expect(progressResponse.body).toMatchObject({
        status: 'success',
        data: {
          progressRecorded: true,
          dayCompleted: 1,
          nextDay: 2,
          adaptiveRecommendations: expect.arrayContaining([
            expect.objectContaining({
              type: 'encouragement',
              message: expect.stringContaining('Excellent'),
            }),
          ]),
          streakCount: 1,
          overallProgress: expect.any(Number),
        },
      });

      // Step 4: Get journey status and progress overview
      const statusBuilder = createMockQueryBuilder();
      statusBuilder.select.mockReturnValue(statusBuilder);
      statusBuilder.eq.mockReturnValue(statusBuilder);
      statusBuilder.single.mockResolvedValue({
        data: {
          journey_id: journeyId,
          status: 'active',
          current_day: 2,
          days_completed: 1,
          streak_count: 1,
          total_time_spent: 30,
          completion_percentage: 4.76, // 1/21 * 100
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(statusBuilder);

      const statusResponse = await request(app)
        .get(`/api/journey/${journeyId}/status`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(statusResponse.status).toBe(200);
      expect(statusResponse.body).toMatchObject({
        status: 'success',
        data: {
          journey: expect.objectContaining({
            journeyId,
            status: 'active',
            currentDay: 2,
            daysCompleted: 1,
            streakCount: 1,
            completionPercentage: expect.any(Number),
          }),
          progress: expect.objectContaining({
            totalTimeSpent: 30,
            averageDailyTime: 30,
          }),
          nextActions: expect.arrayContaining([
            'Continue to Day 2',
            "Review yesterday's insights",
          ]),
        },
      });
    });

    it('should handle journey modification and adaptation', async () => {
      const journeyId = 'modifiable-journey-789';

      // Test journey modification (extend duration)
      const modificationData = {
        action: 'extend',
        additionalDays: 7,
        reason: 'Need more time for Nafs purification',
      };

      const modifyBuilder = createMockQueryBuilder();
      modifyBuilder.update.mockReturnValue(modifyBuilder);
      modifyBuilder.eq.mockReturnValue(modifyBuilder);
      modifyBuilder.select.mockReturnValue(modifyBuilder);
      modifyBuilder.single.mockResolvedValue({
        data: {
          journey_id: journeyId,
          duration: 28, // Extended from 21 to 28
          status: 'active',
          modified_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(modifyBuilder);

      const modifyResponse = await request(app)
        .patch('/api/journey/modify')
        .set('Authorization', `Bearer ${validToken}`)
        .send(modificationData);

      expect(modifyResponse.status).toBe(200);
      expect(modifyResponse.body).toMatchObject({
        status: 'success',
        data: {
          journeyModified: true,
          newDuration: 28,
          additionalContent: expect.any(Array),
          message: expect.stringContaining('extended'),
        },
      });
    });

    it('should handle journey completion and transition to next phase', async () => {
      const journeyId = 'completing-journey-999';

      // Mock final day completion
      const finalDayProgress = {
        dayNumber: 21,
        practiceResults: {
          final_reflection: {
            completed: true,
            duration: 45,
            quality: 'excellent',
            notes: 'Profound transformation in my relationship with my Nafs',
            insights: [
              'I can observe my desires without being controlled by them',
            ],
          },
        },
        overallExperience: {
          mood: 'grateful',
          spiritualConnection: 'significantly_improved',
          transformation: 'substantial',
        },
      };

      // Mock journey completion
      global.mockAiService.generateAdaptiveRecommendations.mockResolvedValueOnce(
        [
          {
            type: 'completion',
            message:
              'Congratulations! You have completed your Nafs purification journey.',
          },
          {
            type: 'next_phase',
            message: 'You are ready to begin the Qalb strengthening journey.',
          },
        ]
      );

      const completionBuilder = createMockQueryBuilder();
      completionBuilder.update.mockReturnValue(completionBuilder);
      completionBuilder.eq.mockReturnValue(completionBuilder);
      completionBuilder.select.mockReturnValue(completionBuilder);
      completionBuilder.single.mockResolvedValue({
        data: {
          journey_id: journeyId,
          status: 'completed',
          completed_at: new Date().toISOString(),
          days_completed: 21,
          completion_percentage: 100,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(completionBuilder);

      const completionResponse = await request(app)
        .post(`/api/journey/${journeyId}/progress`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(finalDayProgress);

      expect(completionResponse.status).toBe(200);
      expect(completionResponse.body).toMatchObject({
        status: 'success',
        data: {
          journeyCompleted: true,
          completionCertificate: expect.objectContaining({
            title: expect.any(String),
            completedAt: expect.any(String),
            achievements: expect.any(Array),
          }),
          nextJourneyRecommendations: expect.arrayContaining([
            expect.objectContaining({
              layer: 'Qalb',
              type: expect.any(String),
            }),
          ]),
          transformationSummary: expect.objectContaining({
            beforeAfter: expect.any(Object),
            keyInsights: expect.any(Array),
            spiritualGrowth: expect.any(String),
          }),
        },
      });
    });
  });

  describe('Islamic-Specific Journey Features', () => {
    it('should include authentic Islamic practices and content', async () => {
      const journeyId = 'islamic-journey-456';

      const dayContentResponse = await request(app)
        .get(`/api/journey/${journeyId}/day/1`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(dayContentResponse.status).toBe(200);
      expect(dayContentResponse.body.data.practices).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'dhikr',
            name: expect.any(String),
            arabicText: expect.any(String),
            transliteration: expect.any(String),
            translation: expect.any(String),
          }),
          expect.objectContaining({
            type: 'dua',
            category: 'protection',
            source: expect.stringMatching(/Quran|Hadith/),
          }),
        ])
      );
    });

    it('should provide Ruqya integration based on user level', async () => {
      const ruqyaJourneyData = {
        assessmentResults: {
          ...assessmentResults,
          ruqyaRecommendations: ['basic_protection', 'healing_verses'],
        },
        preferences: {
          ruqyaLevel: 'intermediate',
          focusLayers: ['Qalb', 'Ruh'],
        },
      };

      global.mockAiService.generateJourneyContent.mockResolvedValueOnce({
        title: 'Spiritual Healing with Ruqya Integration',
        days: [
          {
            day: 1,
            practices: [
              {
                type: 'ruqya',
                name: 'Morning Protection',
                verses: ['Al-Fatiha', 'Ayat al-Kursi', "Al-Mu'awwidhatayn"],
                instructions:
                  "Recite with intention of seeking Allah's protection",
                level: 'intermediate',
              },
            ],
          },
        ],
      });

      const journeyBuilder = createMockQueryBuilder();
      journeyBuilder.insert.mockReturnValue(journeyBuilder);
      journeyBuilder.single.mockResolvedValue({
        data: {
          journey_id: 'ruqya-journey-789',
          type: 'ruqya_integrated',
          ruqya_level: 'intermediate',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(journeyBuilder);

      const createResponse = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send(ruqyaJourneyData);

      expect(createResponse.status).toBe(201);
      expect(createResponse.body.data.content.days[0].practices).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'ruqya',
            verses: expect.arrayContaining(['Al-Fatiha', 'Ayat al-Kursi']),
            level: 'intermediate',
          }),
        ])
      );
    });
  });
});
