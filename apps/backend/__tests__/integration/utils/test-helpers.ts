/**
 * Integration Test Utilities
 * Shared helpers and utilities for integration tests
 */

import { jest } from '@jest/globals';

/**
 * Creates a mock Supabase query builder with all necessary methods
 */
export const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

/**
 * Test data factories for consistent test data creation
 */
export const TestDataFactory = {
  createTestUser: (overrides = {}) => ({
    id: 'test-user-id-123',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z',
    email_confirmed_at: '2024-01-01T00:00:00.000Z',
    ...overrides,
  }),

  createTestSession: (user = null, overrides = {}) => ({
    access_token: 'test-access-token-123',
    refresh_token: 'test-refresh-token-123',
    expires_in: 3600,
    expires_at: Date.now() + 3600000,
    token_type: 'bearer',
    user: user || TestDataFactory.createTestUser(),
    ...overrides,
  }),

  createOnboardingData: (overrides = {}) => ({
    personalInfo: {
      awarenessLevel: 'intermediate',
      ruqyaFamiliarity: 'basic',
      age: 28,
      profession: 'teacher',
      culturalBackground: 'arab',
      gender: 'female',
      islamicKnowledgeLevel: 'intermediate',
      ...overrides.personalInfo,
    },
    preferences: {
      timeAvailability: 'moderate',
      learningStyle: 'visual',
      communityParticipation: true,
      languagePreference: 'english',
      notificationPreferences: {
        prayer_reminders: true,
        dhikr_reminders: true,
        journey_updates: true,
      },
      ...overrides.preferences,
    },
  }),

  createAssessmentResponses: (layer = 'physical_experiences', overrides = {}) => {
    const baseResponses = {
      physical_experiences: {
        sleep_quality: 'fair',
        energy_levels: 'moderate',
        physical_symptoms: ['occasional_headaches'],
        appetite_changes: 'normal',
      },
      emotional_experiences: {
        mood_patterns: 'generally_stable',
        anxiety_levels: 'mild',
        emotional_regulation: 'manageable',
        self_worth: 'moderate',
      },
      mental_experiences: {
        concentration_issues: 'mild',
        memory_problems: 'none',
        decision_making: 'good',
        negative_thoughts: 'occasional',
      },
      spiritual_experiences: {
        prayer_connection: 'strong',
        dhikr_practice: 'regular',
        quran_relationship: 'growing',
        spiritual_emptiness: 'rare',
      },
    };

    return {
      step: layer,
      responses: {
        ...baseResponses[layer],
        ...overrides,
      },
      timeSpent: 180,
    };
  },

  createJourneyData: (overrides = {}) => ({
    assessmentResults: {
      layerPriorities: ['Qalb', 'Nafs', 'Aql', 'Ruh', 'Jism'],
      overallSeverity: 'moderate',
      recommendedJourneyType: 'comprehensive',
      ...overrides.assessmentResults,
    },
    preferences: {
      duration: 21,
      timeCommitment: 30,
      focusLayers: ['Qalb', 'Nafs'],
      ruqyaLevel: 'basic',
      communitySupport: true,
      ...overrides.preferences,
    },
  }),

  createCrisisData: (severity = 'high', overrides = {}) => ({
    triggerType: 'manual',
    currentSymptoms: ['panic_attack', 'anxiety', 'spiritual_crisis'],
    severity,
    location: 'US',
    ...overrides,
  }),
};

/**
 * Mock response factories for AI service and other external services
 */
export const MockResponseFactory = {
  createAiAnalysisResponse: (overrides = {}) => ({
    personalizedMessage: 'Your spiritual analysis is complete',
    islamicInsights: ['Focus on dhikr for peace', 'Strengthen prayer connection'],
    educationalContent: ['Understanding the 5 layers', 'Islamic psychology basics'],
    crisisLevel: 'none',
    crisisIndicators: [],
    immediateActions: [],
    nextSteps: ['Begin your healing journey'],
    recommendedJourneyType: 'comprehensive',
    estimatedHealingDuration: 21,
    confidence: 0.85,
    ...overrides,
  }),

  createJourneyContentResponse: (overrides = {}) => ({
    title: 'Healing the Heart: A 21-Day Journey',
    description: 'A comprehensive journey to heal your spiritual heart',
    personalizedWelcome: 'Welcome to your personalized healing journey...',
    days: Array.from({ length: 21 }, (_, i) => ({
      day: i + 1,
      title: `Day ${i + 1}: Foundation`,
      theme: 'building_awareness',
      practices: [
        {
          type: 'dhikr',
          name: 'Morning Tasbih',
          duration: 10,
          instructions: 'Recite SubhanAllah 33 times...',
        },
      ],
      content: [
        {
          type: 'education',
          title: 'Understanding the Heart',
          content: 'The Qalb represents our spiritual heart...',
        },
      ],
      goals: ['Increase spiritual awareness'],
    })),
    milestones: [
      { day: 7, title: 'Foundation Complete' },
      { day: 14, title: 'Midpoint Reached' },
      { day: 21, title: 'Journey Complete' },
    ],
    ...overrides,
  }),

  createCrisisResourcesResponse: (location = 'US', overrides = {}) => ({
    hotlines: [
      {
        name: 'National Suicide Prevention Lifeline',
        phone: '988',
        available: '24/7',
        description: 'Free and confidential emotional support',
        region: location,
      },
    ],
    islamicCounselors: [
      {
        name: 'Islamic Crisis Support',
        contact: '******-ISLAMIC',
        specialization: 'Islamic Mental Health',
        location,
        available: '24/7',
      },
    ],
    emergencyPrayers: [
      {
        title: 'Dua for Distress',
        arabic: 'لا إله إلا الله العظيم الحليم',
        transliteration: 'La ilaha illa Allah al-Azeem al-Haleem',
        translation: 'There is no god but Allah, the Great, the Gentle',
        source: 'Hadith - Bukhari and Muslim',
      },
    ],
    selfCareSteps: [
      'Take deep breaths',
      'Recite dhikr',
      'Seek professional help if needed',
    ],
    ...overrides,
  }),
};

/**
 * Authentication helpers for integration tests
 */
export const AuthHelpers = {
  /**
   * Creates a valid authorization header for requests
   */
  createAuthHeader: (token = 'test-token-123') => ({
    Authorization: `Bearer ${token}`,
  }),

  /**
   * Mocks successful authentication for a test user
   */
  mockAuthentication: (mockSupabase: any, user = null) => {
    const testUser = user || TestDataFactory.createTestUser();
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: testUser },
      error: null,
    });
    return testUser;
  },

  /**
   * Mocks failed authentication
   */
  mockAuthenticationFailure: (mockSupabase: any, error = 'Invalid token') => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: { message: error },
    });
  },
};

/**
 * Database helpers for integration tests
 */
export const DatabaseHelpers = {
  /**
   * Sets up a successful database operation mock
   */
  mockSuccessfulQuery: (mockSupabase: any, tableName: string, data: any) => {
    const builder = createMockQueryBuilder();
    builder.single.mockResolvedValue({ data, error: null });
    mockSupabase.from.mockReturnValueOnce(builder);
    return builder;
  },

  /**
   * Sets up a failed database operation mock
   */
  mockFailedQuery: (mockSupabase: any, tableName: string, error: string) => {
    const builder = createMockQueryBuilder();
    builder.single.mockResolvedValue({ data: null, error: { message: error } });
    mockSupabase.from.mockReturnValueOnce(builder);
    return builder;
  },

  /**
   * Sets up a not found database operation mock
   */
  mockNotFoundQuery: (mockSupabase: any, tableName: string) => {
    const builder = createMockQueryBuilder();
    builder.single.mockResolvedValue({ data: null, error: { code: 'PGRST116' } });
    mockSupabase.from.mockReturnValueOnce(builder);
    return builder;
  },
};

/**
 * Assertion helpers for common test patterns
 */
export const AssertionHelpers = {
  /**
   * Asserts that a response has the expected success structure
   */
  expectSuccessResponse: (response: any, expectedData = {}) => {
    expect(response.body).toMatchObject({
      status: 'success',
      data: expect.objectContaining(expectedData),
    });
  },

  /**
   * Asserts that a response has the expected error structure
   */
  expectErrorResponse: (response: any, expectedMessage?: string) => {
    expect(response.body).toMatchObject({
      status: expect.stringMatching(/error|fail/),
      message: expectedMessage ? expect.stringContaining(expectedMessage) : expect.any(String),
    });
  },

  /**
   * Asserts that Islamic content is properly formatted
   */
  expectIslamicContent: (content: any) => {
    if (content.arabic) {
      expect(content).toMatchObject({
        arabic: expect.any(String),
        transliteration: expect.any(String),
        translation: expect.any(String),
        source: expect.stringMatching(/Quran|Hadith|Sunnah/),
      });
    }
  },

  /**
   * Asserts that crisis response has required safety elements
   */
  expectCrisisResponse: (response: any) => {
    expect(response.body.data).toMatchObject({
      crisisLevel: expect.stringMatching(/low|moderate|high|critical/),
      emergencyResources: expect.objectContaining({
        hotlines: expect.any(Array),
      }),
      immediateActions: expect.any(Array),
    });
  },
};

export default {
  createMockQueryBuilder,
  TestDataFactory,
  MockResponseFactory,
  AuthHelpers,
  DatabaseHelpers,
  AssertionHelpers,
};
