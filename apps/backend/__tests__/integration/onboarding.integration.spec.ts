import request from 'supertest';
import { Express } from 'express';
import { prisma }
    from '../../src/config/database';
import { app } from '../../src/main'; // Assuming your Express app is exported from main.ts
import { OnboardingService } from '../../src/services/onboarding.service';
import { AIService } from '../../src/services/ai.service';
import { CrisisDetectionService } from '../../src/services/crisis-detection.service';
import { User } from '@supabase/supabase-js'; // For mocking req.user

// Mock external services that OnboardingService depends on
jest.mock('../../src/services/ai.service');
jest.mock('../../src/services/crisis-detection.service');

// Mock authentication middleware
let mockUser: User | null = null;
const mockAuthenticateUser = (req: any, res: any, next: any) => {
    if (mockUser) {
        req.user = mockUser;
        next();
    } else {
        res.status(401).json({ message: 'Unauthorized - Mock Auth' });
    }
};
// Apply the mock to the actual path used by Express router
jest.mock('../../src/middleware/auth', () => ({
    authenticateUser: jest.fn((req, res, next) => mockAuthenticateUser(req, res, next)),
    // Mock other exports from '../middleware/auth' if needed, e.g., specific permission checks
}));


const MockedAIService = AIService as jest.MockedClass<typeof AIService>;
const MockedCrisisDetectionService = CrisisDetectionService as jest.MockedClass<typeof CrisisDetectionService>;

// Hold instances of mocks to control their behavior per test
let mockAiServiceInstance: jest.Mocked<AIService>;
let mockCrisisServiceInstance: jest.Mocked<CrisisDetectionService>;


describe('Onboarding API Integration Tests', () => {
    let expressApp: Express;
    let testUser: User;

    beforeAll(async () => {
        expressApp = app; 
        
        // Correctly get the singleton instance of OnboardingService that the controller uses
        // and then replace its internal service instances with our mocks.
        // This requires OnboardingService to expose its dependencies or use a DI pattern.
        // For now, we'll rely on jest.mock effectively replacing the constructors globally.
        mockAiServiceInstance = new MockedAIService() as jest.Mocked<AIService>;
        mockCrisisServiceInstance = new MockedCrisisDetectionService() as jest.Mocked<CrisisDetectionService>;

        // If OnboardingService is a singleton, we'd mock its internal dependencies directly.
        // Example: (onboardingServiceInstance as any).aiService = mockAiServiceInstance;
        // However, the controller creates `new OnboardingService()`.
        // The module-level jest.mock ensures that `new AIService()` inside OnboardingService constructor
        // returns our mockAiServiceInstance if AIService constructor is mocked to return it.
        MockedAIService.mockImplementation(() => mockAiServiceInstance);
        MockedCrisisDetectionService.mockImplementation(() => mockCrisisServiceInstance);
    });

    beforeEach(async () => {
        jest.clearAllMocks(); 

        testUser = {
            id: `test-user-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
            app_metadata: { provider: 'email', providers: ['email'] },
            user_metadata: { name: 'Test User' },
            aud: 'authenticated',
            created_at: new Date().toISOString(),
            // Add any other fields your User type might have from Supabase
        };
        mockUser = testUser; 

        // Reset method mocks on the instances
        mockAiServiceInstance.generateProfileFromOnboarding = jest.fn();
        mockCrisisServiceInstance.analyzeResponse = jest.fn();
        mockCrisisServiceInstance.logCrisisEvent = jest.fn();
        
        // Default service mocks
        mockAiServiceInstance.generateProfileFromOnboarding.mockResolvedValue({
            profileData: {userId: testUser.id, completionStatus: 'complete', mentalHealthAwareness: {level: 'symptom_aware'}, ruqyaKnowledge: {level: 'unaware'}},
            recommendedPathway: "gentle_introduction",
            personalizationSettings: { contentStyle: 'simple' }
        } as any);
        mockCrisisServiceInstance.analyzeResponse.mockResolvedValue({ isCrisis: false });


        // Clean the database 
        const tableNames = ['OnboardingStep', 'OnboardingSession', 'UserProfileDetailed', 'User', 'Profile'];
        for (const tableName of tableNames) {
            if (prisma[tableName]) {
                 await prisma[tableName].deleteMany({});
            } else {
                // Attempt to delete from tables that might exist with different casing or are actual SQL table names
                try {
                    await prisma.$executeRawUnsafe(`DELETE FROM "${tableName}";`);
                } catch (e) {
                   // console.warn(`Could not clear table ${tableName}: ${e.message}`);
                }
            }
        }
         // Create a corresponding user and profile if your auth mock doesn't fully bypass this
        // For true integration, you might need a user in the 'users' table (auth.users in Supabase)
        // and a profile in your public 'Profiles' table if they are linked.
        // This setup assumes 'authenticateUser' mock is sufficient.
    });

    afterAll(async () => {
        mockUser = null;
        await prisma.$disconnect();
    });

    describe('POST /api/onboarding/start', () => {
        it('should start a new onboarding session and return the first question', async () => {
            const deviceInfo = { platform: 'test-platform', screenSize: "1024x768" };
            
            const response = await request(expressApp)
                .post('/api/onboarding/start')
                .send({ deviceInfo })
                .set('Accept', 'application/json');

            expect(response.status).toBe(201);
            expect(response.body.status).toBe('success');
            const sessionData = response.body.data.session;
            const questionData = response.body.data.question;

            expect(sessionData.sessionId).toBeDefined();
            expect(sessionData.currentStep).toBe('welcome');
            expect(questionData.question.id).toBe('welcome'); 
            expect(questionData.step).toBe('mental_health_awareness'); // The next step to be taken

            const dbSession = await prisma.onboardingSession.findUnique({
                where: { sessionId: sessionData.sessionId },
            });
            expect(dbSession).not.toBeNull();
            expect(dbSession?.userId).toBe(testUser.id);
            expect(dbSession?.deviceInfo).toEqual(deviceInfo);
        });

        it('should return 401 if user is not authenticated', async () => {
            mockUser = null; 
            const response = await request(expressApp)
                .post('/api/onboarding/start')
                .send({ deviceInfo: {} });
            expect(response.status).toBe(401);
        });
    });

    describe('POST /api/onboarding/submit (Legacy Direct Submission)', () => {
        const legacySubmitData = {
            personalInfo: { awarenessLevel: 'intermediate', ruqyaFamiliarity: 'basic', profession: 'engineer', age: 30, gender: 'male', culturalBackground: 'middle-eastern' },
            preferences: { timeAvailability: '30-60', learningStyle: 'kinesthetic', communityParticipation: 'high' },
        };

        it('should create a new profile via legacy submit if one does not exist', async () => {
            const response = await request(expressApp)
                .post('/api/onboarding/submit')
                .send(legacySubmitData);

            expect(response.status).toBe(200);
            expect(response.body.status).toBe('success');
            expect(response.body.data.profile.userId).toBe(testUser.id);
            expect(response.body.data.profile.completionStatus).toBe('complete');
            
            const dbProfile = await prisma.userProfileDetailed.findUnique({ where: { userId: testUser.id } });
            expect(dbProfile).not.toBeNull();
            expect(dbProfile?.completionStatus).toBe('complete');
            const profileData = dbProfile?.profileData as any;
            expect(profileData.mentalHealthAwareness.level).toBe('intermediate');
            expect(profileData.preferences.learningPace).toBe('kinesthetic');
        });

        it('should update an existing profile via legacy submit', async () => {
            // Create an initial minimal profile
            await prisma.userProfileDetailed.create({
                data: {
                    userId: testUser.id,
                    profileData: { mentalHealthAwareness: { level: 'beginner'} } as any,
                    completionStatus: 'incomplete',
                    profileVersion: '0.9'
                }
            });

            const response = await request(expressApp)
                .post('/api/onboarding/submit')
                .send(legacySubmitData);

            expect(response.status).toBe(200);
            const dbProfile = await prisma.userProfileDetailed.findUnique({ where: { userId: testUser.id } });
            expect(dbProfile?.completionStatus).toBe('complete');
            const profileData = dbProfile?.profileData as any;
            expect(profileData.mentalHealthAwareness.level).toBe('intermediate'); // Updated
            expect(profileData.professionalContext.field).toBe('engineer'); 
        });
        
        it('should return 401 if user is not authenticated', async () => {
            mockUser = null;
            const response = await request(expressApp)
                .post('/api/onboarding/submit')
                .send(legacySubmitData);
            expect(response.status).toBe(401);
        });
        // Add validation error tests if input validation is more complex for this route
    });

    describe('PUT /api/onboarding/update (Preferences)', () => {
        const initialPreferences = { timeAvailability: '10-20', learningPace: 'moderate', communityEngagement: 'low' };
        const updatedPreferencesPayload = { preferences: { timeAvailability: '60+', learningStyle: 'visual', communityParticipation: 'high' } };

        beforeEach(async () => {
            // Create an initial profile
            await prisma.userProfileDetailed.create({
                data: {
                    userId: testUser.id,
                    profileData: { preferences: initialPreferences, otherData: "test" } as any,
                    completionStatus: 'complete',
                    profileVersion: '1.0'
                }
            });
        });

        it('should update user preferences in profileData', async () => {
            const response = await request(expressApp)
                .put('/api/onboarding/update')
                .send(updatedPreferencesPayload);

            expect(response.status).toBe(200);
            expect(response.body.status).toBe('success');
            const profileData = response.body.data.profile as any;
            expect(profileData.preferences.timeAvailability).toBe('60+');
            expect(profileData.preferences.learningPace).toBe('visual'); // learningStyle maps to learningPace
            expect(profileData.preferences.communityEngagement).toBe('high');
            expect(profileData.otherData).toBe("test"); // Ensure other parts of profileData are preserved

            const dbProfile = await prisma.userProfileDetailed.findUnique({ where: { userId: testUser.id } });
            const dbProfileData = dbProfile?.profileData as any;
            expect(dbProfileData.preferences.timeAvailability).toBe('60+');
        });

        it('should return 404 if profile to update does not exist', async () => {
            await prisma.userProfileDetailed.deleteMany({ where: { userId: testUser.id }});
            const response = await request(expressApp)
                .put('/api/onboarding/update')
                .send(updatedPreferencesPayload);
            expect(response.status).toBe(404);
        });
        
        it('should return 401 if user is not authenticated', async () => {
            mockUser = null;
            const response = await request(expressApp)
                .put('/api/onboarding/update')
                .send(updatedPreferencesPayload);
            expect(response.status).toBe(401);
        });
    });
    
    describe('PATCH /api/onboarding/profile (General Profile Update)', () => {
        const initialProfileData = { professionalContext: { field: 'arts' }, demographics: { ageRange: '26-35' } };
        const profileUpdates = { professionalContext: { field: 'technology' }, crisisIndicators: { level: 'moderate' } };

        beforeEach(async () => {
            await prisma.userProfileDetailed.create({
                data: {
                    userId: testUser.id,
                    profileData: initialProfileData as any,
                    completionStatus: 'complete',
                    profileVersion: '1.0'
                }
            });
        });

        it('should partially update the user profileData', async () => {
            const response = await request(expressApp)
                .patch('/api/onboarding/profile')
                .send({ profileUpdates, reason: "Career change" });

            expect(response.status).toBe(200);
            expect(response.body.status).toBe('success');
            const returnedProfile = response.body.data.profile as any;
            expect(returnedProfile.professionalContext.field).toBe('technology');
            expect(returnedProfile.demographics.ageRange).toBe('26-35'); // Original demographic data preserved
            expect(returnedProfile.crisisIndicators.level).toBe('moderate'); // New section added

            const dbProfile = await prisma.userProfileDetailed.findUnique({ where: { userId: testUser.id } });
            const dbProfileData = dbProfile?.profileData as any;
            expect(dbProfileData.professionalContext.field).toBe('technology');
            expect(dbProfileData.crisisIndicators.level).toBe('moderate');
        });
        
        it('should return 400 if profileUpdates is empty', async () => {
            const response = await request(expressApp)
                .patch('/api/onboarding/profile')
                .send({ profileUpdates: {} });
            expect(response.status).toBe(400);
        });

        it('should return 404 if profile to update does not exist', async () => {
            await prisma.userProfileDetailed.deleteMany({ where: { userId: testUser.id }});
            const response = await request(expressApp)
                .patch('/api/onboarding/profile')
                .send({ profileUpdates });
            expect(response.status).toBe(404);
        });
    });

    describe('POST /api/onboarding/skip', () => {
        it('should skip onboarding and return a minimal profile structure', async () => {
            const response = await request(expressApp)
                .post('/api/onboarding/skip')
                .send({ reason: 'crisis' });

            expect(response.status).toBe(200);
            expect(response.body.status).toBe('success');
            expect(response.body.data.profile).toBeDefined();
            expect(response.body.data.recommendedPathway).toBe('crisis_support');
            // Note: This endpoint in controller currently doesn't save to DB, just constructs a response.
            // If it were to save, we would add DB assertions here.
        });
        
         it('should use default reason if none provided and return gentle_introduction', async () => {
            const response = await request(expressApp)
                .post('/api/onboarding/skip')
                .send({});
            expect(response.status).toBe(200);
            expect(response.body.data.recommendedPathway).toBe('gentle_introduction');
        });
    });

    describe('POST /api/onboarding/respond', () => {
        let currentSessionId: string;

        beforeEach(async () => {
            // Start a session to get a valid sessionId for response submissions
            const startResponse = await request(expressApp)
                .post('/api/onboarding/start')
                .send({ deviceInfo: { platform: 'integration-test' } });
            currentSessionId = startResponse.body.data.session.sessionId;
        });

        it('should submit a response and get the next question', async () => {
            const stepId = 'welcome'; 
            const userResponsePayload = { action: 'begin' }; 
            
            const respondResponse = await request(expressApp)
                .post('/api/onboarding/respond')
                .send({ sessionId: currentSessionId, stepId, response: userResponsePayload, timeSpent: 10 });

            expect(respondResponse.status).toBe(200);
            expect(respondResponse.body.status).toBe('continue');
            expect(respondResponse.body.data.question.question.id).toBe('mental_health_awareness');
            expect(respondResponse.body.data.step).toBe('ruqya_knowledge'); // Next logical step after mha
            expect(respondResponse.body.data.progress).toBeDefined();

            const dbSession = await prisma.onboardingSession.findUnique({ where: { sessionId: currentSessionId } });
            expect(dbSession?.steps).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({ stepId: 'welcome', responses: userResponsePayload })
                ])
            );
            // The currentStep in DB should be what's determined for the *next* question after this response
            expect(dbSession?.currentStep).toBe('ruqya_knowledge'); 
        });

        it('should handle onboarding completion', async () => {
            // Simulate responses to reach near completion
            await request(expressApp).post('/api/onboarding/respond').send({ sessionId: currentSessionId, stepId: 'welcome', response: {action: 'begin'}, timeSpent: 5 });
            await request(expressApp).post('/api/onboarding/respond').send({ sessionId: currentSessionId, stepId: 'mental_health_awareness', response: {mental_health_awareness: 'symptom_aware'}, timeSpent: 5 });
            await request(expressApp).post('/api/onboarding/respond').send({ sessionId: currentSessionId, stepId: 'ruqya_knowledge', response: {ruqya_knowledge: 'unaware'}, timeSpent: 5 });
            await request(expressApp).post('/api/onboarding/respond').send({ sessionId: currentSessionId, stepId: 'professional_context', response: {professional_context: 'student_pc'}, timeSpent: 5 });
            await request(expressApp).post('/api/onboarding/respond').send({ sessionId: currentSessionId, stepId: 'pc_work_challenges', response: {pc_work_challenges: ['none_challenges_pc']}, timeSpent: 5 });
            
            // The next response to 'demographics' should lead to 'life_circumstances', then completion
            const demographicsResponse = { demographics: { age_range_section: "18_25_age", gender_section: "male_gender", family_status_section: "single_fs"}};
            await request(expressApp).post('/api/onboarding/respond').send({ sessionId: currentSessionId, stepId: 'demographics', response: demographicsResponse, timeSpent: 5 });

            // Final response for 'life_circumstances'
            const lifeCircumstancesResponse = { life_circumstances: ["academic_work_pressure_lc"] };
            const completeResponse = await request(expressApp)
                .post('/api/onboarding/respond')
                .send({ sessionId: currentSessionId, stepId: 'life_circumstances', response: lifeCircumstancesResponse, timeSpent: 20 });
            
            expect(completeResponse.status).toBe(200);
            expect(completeResponse.body.status).toBe('completed');
            expect(completeResponse.body.data.profile).toBeDefined();
            expect(completeResponse.body.data.profile.userId).toBe(testUser.id);
            // Pathway and personalization come from the AIService mock
            expect(completeResponse.body.data.recommendedPathway).toBe('gentle_introduction'); 
            expect(completeResponse.body.data.featureConfiguration.contentStyle).toBe('simple');


            const dbSession = await prisma.onboardingSession.findUnique({ where: { sessionId: currentSessionId } });
            expect(dbSession?.currentStep).toBe('complete');
            expect(dbSession?.completedAt).not.toBeNull();

            const dbProfile = await prisma.userProfileDetailed.findUnique({ where: { userId: testUser.id } });
            expect(dbProfile).not.toBeNull();
            expect(dbProfile?.completionStatus).toBe('complete');
            const profileData = dbProfile?.profileData as any;
            expect(profileData.demographics.ageRange).toBe("18_25_age");
            expect(profileData.lifeCircumstances.situations).toContain("academic_work_pressure_lc");
        });
        
        it('should handle crisis detection during /respond', async () => {
            mockCrisisServiceInstance.analyzeResponse.mockResolvedValue({ isCrisis: true, level: 'high', indicators: ['test_crisis_indicator'] });
            
            const crisisStepResponse = await request(expressApp)
                .post('/api/onboarding/respond')
                .send({ sessionId: currentSessionId, stepId: 'mental_health_awareness', response: { text: 'I am in a big crisis help me now' }, timeSpent: 5 });
            
            expect(crisisStepResponse.status).toBe(200);
            expect(crisisStepResponse.body.status).toBe('crisis_detected');
            expect(crisisStepResponse.body.data.level).toBe('high');
            expect(mockCrisisServiceInstance.logCrisisEvent).toHaveBeenCalled();
        });
    });

    // TODO: Add tests for /resume, /session-status/:sessionId, /status (user overall), /submit (legacy), /update, /profile (PATCH), /skip

    describe('POST /api/onboarding/resume', () => {
        let existingSessionId: string;

        beforeEach(async () => {
            // Create an incomplete session
            const startResponse = await request(expressApp)
                .post('/api/onboarding/start')
                .send({ deviceInfo: { platform: 'integration-test-resume' } });
            existingSessionId = startResponse.body.data.session.sessionId;

            // Submit one response to move past 'welcome'
            await request(expressApp)
                .post('/api/onboarding/respond')
                .send({ sessionId: existingSessionId, stepId: 'welcome', response: { action: 'begin' }, timeSpent: 5 });
            // Session is now at 'mental_health_awareness', next step to be determined is 'ruqya_knowledge'
        });

        it('should resume an incomplete session and return the current question', async () => {
            const response = await request(expressApp)
                .post('/api/onboarding/resume')
                .send({ sessionId: existingSessionId });

            expect(response.status).toBe(200);
            expect(response.body.status).toBe('success');
            expect(response.body.data.sessionId).toBe(existingSessionId);
            // The question returned by /resume via getNextQuestion for currentStep 'ruqya_knowledge' (after 'mental_health_awareness')
            // is the question for 'ruqya_knowledge' itself (based on service logic for getQuestionForStep(session.currentStep))
            // and the next step determined would be professional_context
            expect(response.body.data.question.question.id).toBe('ruqya_knowledge');
            expect(response.body.data.question.step).toBe('professional_context');
        });

        it('should return 404 if session to resume does not exist', async () => {
            const response = await request(expressApp)
                .post('/api/onboarding/resume')
                .send({ sessionId: 'non-existent-session' });
            expect(response.status).toBe(404); // Service getSession throws 404
        });
         it('should return 401 if user is not authenticated', async () => {
            mockUser = null;
            const response = await request(expressApp)
                .post('/api/onboarding/resume')
                .send({ sessionId: existingSessionId });
            expect(response.status).toBe(401);
        });
    });

    describe('GET /api/onboarding/session-status/:sessionId', () => {
        let existingSessionId: string;
        let otherUserSessionId: string;

        beforeEach(async () => {
            const session = await prisma.onboardingSession.create({
                data: {
                    sessionId: `session-${testUser.id}-${Date.now()}`,
                    userId: testUser.id,
                    currentStep: 'demographics',
                    startedAt: new Date(),
                    steps: [{stepId: 'welcome', responses: {}, completedAt: new Date(), isCompleted: true, stepName:'Welcome', timeSpent: 10}],
                    totalTimeSpent: 10,
                }
            });
            existingSessionId = session.sessionId;
            
            const otherUser = { id: 'other-user-id', aud:'authenticated', app_metadata:{}, user_metadata:{}, created_at: new Date().toISOString()};
            const otherSession = await prisma.onboardingSession.create({
                 data: {
                    sessionId: `session-other-${Date.now()}`,
                    userId: otherUser.id,
                    currentStep: 'welcome',
                    startedAt: new Date(),
                    steps: [],
                    totalTimeSpent: 0,
                }
            });
            otherUserSessionId = otherSession.sessionId;
        });

        it('should get the status of an existing session for the authenticated user', async () => {
            const response = await request(expressApp)
                .get(`/api/onboarding/session-status/${existingSessionId}`);
            
            expect(response.status).toBe(200);
            expect(response.body.status).toBe('success');
            expect(response.body.data.sessionId).toBe(existingSessionId);
            expect(response.body.data.userId).toBe(testUser.id);
            expect(response.body.data.currentStep).toBe('demographics');
        });

        it('should return 404 if session does not exist', async () => {
            const response = await request(expressApp)
                .get('/api/onboarding/session-status/non-existent-session');
            expect(response.status).toBe(404);
        });

        it('should return 403 if user tries to access another user session', async () => {
             const response = await request(expressApp)
                .get(`/api/onboarding/session-status/${otherUserSessionId}`);
            expect(response.status).toBe(403);
        });
        
        it('should return 401 if user is not authenticated', async () => {
            mockUser = null;
            const response = await request(expressApp)
                .get(`/api/onboarding/session-status/${existingSessionId}`);
            expect(response.status).toBe(401);
        });
    });


    describe('GET /api/onboarding/status (Overall User Onboarding Status)', () => {
        it('should return onboardingCompleted: false if no profile exists', async () => {
            const response = await request(expressApp).get('/api/onboarding/status');
            expect(response.status).toBe(200);
            expect(response.body.data.onboardingCompleted).toBe(false);
            expect(response.body.data.profile).toBeNull();
            expect(response.body.data.nextStep).toBe('onboarding');
        });

        it('should return onboardingCompleted: false if profile exists but is incomplete', async () => {
            await prisma.userProfileDetailed.create({
                data: {
                    userId: testUser.id,
                    profileData: { details: "some partial data" } as any,
                    completionStatus: 'incomplete',
                    profileVersion: '1.0'
                }
            });
            const response = await request(expressApp).get('/api/onboarding/status');
            expect(response.status).toBe(200);
            expect(response.body.data.onboardingCompleted).toBe(false);
            expect(response.body.data.profile).toEqual({ details: "some partial data" });
            expect(response.body.data.nextStep).toBe('onboarding');
        });

        it('should return onboardingCompleted: true if profile is complete', async () => {
            const profileDetails = { complete: true, pathway: 'test_path' };
            await prisma.userProfileDetailed.create({
                data: {
                    userId: testUser.id,
                    profileData: profileDetails as any,
                    completionStatus: 'complete',
                    profileVersion: '1.0'
                }
            });
            const response = await request(expressApp).get('/api/onboarding/status');
            expect(response.status).toBe(200);
            expect(response.body.data.onboardingCompleted).toBe(true);
            expect(response.body.data.profile).toEqual(profileDetails);
            expect(response.body.data.nextStep).toBe('assessment');
        });

        it('should return 401 if user is not authenticated', async () => {
            mockUser = null;
            const response = await request(expressApp).get('/api/onboarding/status');
            expect(response.status).toBe(401);
        });
    });
});
