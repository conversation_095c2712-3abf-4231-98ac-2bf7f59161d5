/**
 * Assessment Integration Tests (Feature 1)
 * Tests complete assessment workflows and spiritual landscape analysis
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Assessment Integration Tests (Feature 1)', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = 'valid-assessment-token-123';
  const testUser = {
    id: 'assessment-user-id-123',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00.000Z',
  };

  const userProfile = {
    user_id: testUser.id,
    awareness_level: 'intermediate',
    ruqya_familiarity: 'basic',
    cultural_background: 'arab',
    onboarding_completed: true,
    recommended_journey_type: 'comprehensive',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock authentication
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: testUser },
      error: null,
    });
  });

  describe('Complete Assessment Workflow', () => {
    it('should complete full 5-layer spiritual assessment from start to journey transition', async () => {
      // Step 1: Start assessment session
      const startData = {
        userProfile,
      };

      // Mock assessment session creation
      const sessionBuilder = createMockQueryBuilder();
      sessionBuilder.insert.mockReturnValue(sessionBuilder);
      sessionBuilder.single.mockResolvedValue({
        data: {
          session_id: 'assessment-session-123',
          user_id: testUser.id,
          started_at: new Date().toISOString(),
          current_step: 'welcome',
          status: 'active',
          user_profile: userProfile,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(sessionBuilder);

      const startResponse = await request(app)
        .post('/api/assessment/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send(startData);

      expect(startResponse.status).toBe(200);
      expect(startResponse.body).toMatchObject({
        status: 'success',
        data: {
          message: 'Assessment session started successfully',
          session: expect.objectContaining({
            session_id: 'assessment-session-123',
            current_step: 'welcome',
            status: 'active',
          }),
          welcome: expect.objectContaining({
            greeting: expect.stringContaining('As-salamu alaykum'),
            explanation: expect.any(String),
          }),
        },
      });

      const sessionId = startResponse.body.data.session.session_id;

      // Mock session retrieval for subsequent calls
      const getSessionBuilder = createMockQueryBuilder();
      getSessionBuilder.select.mockReturnValue(getSessionBuilder);
      getSessionBuilder.eq.mockReturnValue(getSessionBuilder);
      getSessionBuilder.single.mockResolvedValue({
        data: {
          id: sessionId,
          user_id: testUser.id,
          status: 'active',
          current_step: 'welcome',
          session_data: {
            id: sessionId,
            userId: testUser.id,
            userProfile: {
              awareness_level: 'intermediate',
              cultural_background: 'arab',
            },
            currentStep: 'welcome',
            totalSteps: 5,
            timeSpentPerStep: {},
            totalTimeSpent: 0,
            responses: {},
            physicalExperiences: {},
            emotionalExperiences: {},
            mentalExperiences: {},
            spiritualExperiences: {},
            reflections: {},
          },
        },
        error: null,
      });
      mockSupabase.from.mockReturnValue(getSessionBuilder);

      // Step 2: Get questions for physical experiences (Jism layer)
      const physicalQuestionsResponse = await request(app)
        .get(`/api/assessment/${sessionId}/questions/physical_experiences`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(physicalQuestionsResponse.status).toBe(200);
      expect(physicalQuestionsResponse.body).toMatchObject({
        status: 'success',
        data: {
          questions: expect.arrayContaining([
            expect.objectContaining({
              id: 'physical',
              category: 'physical',
              layer: 'jism',
              title: expect.stringContaining('body'),
              symptoms: expect.arrayContaining([
                expect.objectContaining({
                  id: expect.any(String),
                  text: expect.any(String),
                  primaryLayer: 'jism',
                }),
              ]),
            }),
          ]),
        },
      });

      // Step 3: Submit physical experiences responses
      const physicalResponses = {
        step: 'physical_experiences',
        responses: {
          sleep_quality: 'poor',
          energy_levels: 'very_low',
          physical_symptoms: ['headaches', 'fatigue', 'muscle_tension'],
          appetite_changes: 'decreased',
          physical_pain: 'moderate',
          body_awareness: 'disconnected',
        },
        timeSpent: 180, // 3 minutes
      };

      // Mock response processing and AI analysis
      global.mockAiService.analyzeAssessmentResponses.mockResolvedValueOnce({
        layerAnalysis: {
          Jism: {
            severity: 'moderate',
            indicators: ['sleep_disruption', 'low_energy', 'physical_tension'],
            recommendations: [
              'sleep_hygiene',
              'gentle_exercise',
              'relaxation_techniques',
            ],
            confidence: 0.85,
          },
        },
        overallSeverity: 'moderate',
        nextStep: 'emotional_experiences',
        confidence: 0.85,
      });

      const responseBuilder = createMockQueryBuilder();
      responseBuilder.insert.mockReturnValue(responseBuilder);
      responseBuilder.single.mockResolvedValue({
        data: {
          response_id: 'response-physical-123',
          session_id: sessionId,
          step: 'physical_experiences',
          responses: physicalResponses.responses,
          analysis: {
            layer: 'Jism',
            severity: 'moderate',
            indicators: ['sleep_disruption', 'low_energy'],
          },
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(responseBuilder);

      const physicalSubmitResponse = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(physicalResponses);

      expect(physicalSubmitResponse.status).toBe(200);
      expect(physicalSubmitResponse.body).toMatchObject({
        status: 'success',
        data: {
          nextStep: 'emotional_experiences',
          layerAnalysis: expect.objectContaining({
            Jism: expect.objectContaining({
              severity: 'moderate',
              indicators: expect.arrayContaining(['sleep_disruption']),
            }),
          }),
          progress: expect.any(Number),
        },
      });

      // Step 4: Complete emotional experiences (Nafs layer)
      const emotionalResponses = {
        step: 'emotional_experiences',
        responses: {
          mood_patterns: 'frequently_sad',
          anxiety_levels: 'high',
          anger_frequency: 'occasional',
          emotional_regulation: 'difficult',
          self_worth: 'low',
          emotional_numbness: 'sometimes',
          guilt_shame: 'frequent',
        },
        timeSpent: 240,
      };

      global.mockAiService.analyzeAssessmentResponses.mockResolvedValueOnce({
        layerAnalysis: {
          Jism: {
            severity: 'moderate',
            indicators: ['sleep_disruption', 'low_energy'],
          },
          Nafs: {
            severity: 'high',
            indicators: ['depression_symptoms', 'anxiety', 'low_self_worth'],
            recommendations: [
              'emotional_regulation',
              'self_compassion',
              'dhikr_practice',
            ],
            confidence: 0.88,
          },
        },
        overallSeverity: 'high',
        nextStep: 'mental_experiences',
        confidence: 0.87,
      });

      const emotionalResponseBuilder = createMockQueryBuilder();
      emotionalResponseBuilder.insert.mockReturnValue(emotionalResponseBuilder);
      emotionalResponseBuilder.single.mockResolvedValue({
        data: {
          response_id: 'response-emotional-123',
          session_id: sessionId,
          step: 'emotional_experiences',
          responses: emotionalResponses.responses,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(emotionalResponseBuilder);

      const emotionalSubmitResponse = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(emotionalResponses);

      expect(emotionalSubmitResponse.status).toBe(200);
      expect(
        emotionalSubmitResponse.body.data.layerAnalysis.Nafs.severity
      ).toBe('high');

      // Step 5: Complete mental experiences (Aql layer)
      const mentalResponses = {
        step: 'mental_experiences',
        responses: {
          concentration_issues: 'severe',
          memory_problems: 'moderate',
          decision_making: 'very_difficult',
          negative_thoughts: 'constant',
          rumination: 'excessive',
          cognitive_clarity: 'very_poor',
        },
        timeSpent: 200,
      };

      // Step 6: Complete spiritual experiences (Qalb layer)
      const spiritualResponses = {
        step: 'spiritual_experiences',
        responses: {
          prayer_connection: 'disconnected',
          dhikr_practice: 'irregular',
          quran_relationship: 'distant',
          spiritual_emptiness: 'profound',
          divine_connection: 'lost',
          islamic_practices: 'struggling',
          spiritual_guidance: 'seeking',
        },
        timeSpent: 300,
      };

      // Step 7: Complete soul experiences (Ruh layer)
      const soulResponses = {
        step: 'soul_experiences',
        responses: {
          life_purpose: 'unclear',
          existential_questions: 'overwhelming',
          soul_peace: 'absent',
          divine_love: 'yearning',
          spiritual_awakening: 'blocked',
          inner_light: 'dimmed',
        },
        timeSpent: 250,
      };

      // Mock final comprehensive analysis
      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({
        personalizedMessage:
          'Your spiritual assessment reveals significant challenges across multiple layers, with particular focus needed on emotional regulation and spiritual reconnection.',
        islamicInsights: [
          'Your Nafs (ego/desires) requires purification through consistent dhikr',
          'Reconnecting with Salah will help restore your Qalb (heart) connection',
          'The Ruh (soul) seeks divine purpose - consider Islamic counseling',
        ],
        educationalContent: [
          'Understanding the 5 layers of Islamic psychology',
          'Dhikr practices for emotional regulation',
          'Ruqya for spiritual protection and healing',
        ],
        layerPriorities: ['Nafs', 'Qalb', 'Aql', 'Ruh', 'Jism'],
        crisisLevel: 'moderate',
        crisisIndicators: ['severe_depression', 'spiritual_crisis'],
        immediateActions: [
          'Begin daily dhikr practice',
          'Seek Islamic counseling support',
          'Start gentle Ruqya practices',
        ],
        nextSteps: [
          'Create personalized healing journey',
          'Access crisis support if needed',
        ],
        recommendedJourneyType: 'comprehensive_with_support',
        estimatedHealingDuration: 28,
        confidence: 0.89,
      });

      // Mock assessment completion
      const completionBuilder = createMockQueryBuilder();
      completionBuilder.update.mockReturnValue(completionBuilder);
      completionBuilder.eq.mockReturnValue(completionBuilder);
      completionBuilder.select.mockReturnValue(completionBuilder);
      completionBuilder.single.mockResolvedValue({
        data: {
          session_id: sessionId,
          user_id: testUser.id,
          status: 'completed',
          completed_at: new Date().toISOString(),
          final_analysis: {
            layerPriorities: ['Nafs', 'Qalb', 'Aql', 'Ruh', 'Jism'],
            overallSeverity: 'high',
            recommendedJourneyType: 'comprehensive_with_support',
          },
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(completionBuilder);

      const completionResponse = await request(app)
        .post(`/api/assessment/${sessionId}/complete`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(completionResponse.status).toBe(200);
      expect(completionResponse.body).toMatchObject({
        status: 'success',
        data: {
          assessmentComplete: true,
          spiritualLandscape: expect.objectContaining({
            personalizedMessage: expect.stringContaining(
              'spiritual assessment'
            ),
            islamicInsights: expect.arrayContaining([
              expect.stringContaining('Nafs'),
              expect.stringContaining('Qalb'),
            ]),
            layerPriorities: ['Nafs', 'Qalb', 'Aql', 'Ruh', 'Jism'],
            recommendedJourneyType: 'comprehensive_with_support',
          }),
          nextSteps: expect.arrayContaining([
            'Create personalized healing journey',
          ]),
          transitionToFeature2: true,
        },
      });
    });

    it('should detect and handle crisis during assessment', async () => {
      const sessionId = 'crisis-assessment-session-456';
      const crisisResponses = {
        step: 'emotional_experiences',
        responses: {
          suicidal_thoughts: 'daily',
          self_harm: 'recent',
          hopelessness: 'complete',
          isolation: 'total',
          spiritual_abandonment: 'absolute',
        },
        timeSpent: 120,
      };

      // Mock crisis detection
      global.mockCrisisDetectionService.analyzeResponse.mockResolvedValueOnce({
        isCrisis: true,
        severity: 'critical',
        indicators: ['suicidal_ideation', 'self_harm', 'severe_depression'],
        confidence: 0.97,
        riskLevel: 'immediate',
        recommendations: ['emergency_intervention', 'crisis_hotline'],
        emergencyProtocol: true,
      });

      const crisisSubmitResponse = await request(app)
        .post(`/api/assessment/${sessionId}/submit`)
        .set('Authorization', `Bearer ${validToken}`)
        .send(crisisResponses);

      expect(crisisSubmitResponse.status).toBe(200);
      expect(crisisSubmitResponse.body).toMatchObject({
        status: 'crisis_detected',
        data: {
          crisisLevel: 'critical',
          emergencyProtocol: true,
          immediateActions: expect.arrayContaining([
            'Contact emergency services',
            'Access Sakina Mode immediately',
          ]),
          resources: expect.objectContaining({
            hotlines: expect.arrayContaining([
              expect.objectContaining({
                name: expect.any(String),
                phone: expect.any(String),
                available: '24/7',
              }),
            ]),
            islamicCounselors: expect.any(Array),
            emergencyPrayers: expect.any(Array),
          }),
          assessmentSuspended: true,
          followUpRequired: true,
        },
      });
    });
  });

  describe('Islamic-Specific Assessment Features', () => {
    it('should include Islamic spiritual assessment questions', async () => {
      const sessionId = 'islamic-assessment-123';

      const spiritualQuestionsResponse = await request(app)
        .get(`/api/assessment/${sessionId}/questions/spiritual_experiences`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(spiritualQuestionsResponse.status).toBe(200);
      expect(spiritualQuestionsResponse.body.data.questions).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'prayer_connection',
            question: expect.stringContaining('Salah'),
            category: 'spiritual',
          }),
          expect.objectContaining({
            id: 'quran_relationship',
            question: expect.stringContaining('Quran'),
            category: 'spiritual',
          }),
          expect.objectContaining({
            id: 'dhikr_practice',
            question: expect.stringContaining('dhikr'),
            category: 'spiritual',
          }),
        ])
      );
    });

    it('should provide Islamic insights in assessment results', async () => {
      const sessionId = 'islamic-insights-session-789';

      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({
        personalizedMessage:
          'Your assessment shows a strong foundation in Islamic knowledge but challenges in spiritual practice.',
        islamicInsights: [
          'Your Qalb (heart) seeks deeper connection through consistent Salah',
          'The Nafs requires taming through regular fasting and dhikr',
          'Consider seeking guidance from a knowledgeable Islamic scholar',
        ],
        educationalContent: [
          'The science of Tazkiyah (spiritual purification)',
          'Understanding the diseases of the heart in Islamic psychology',
          'Prophetic medicine for mental and spiritual wellness',
        ],
        ruqyaRecommendations: [
          'Begin with basic protective duas',
          'Recite Surah Al-Fatiha for general healing',
          'Practice morning and evening adhkar',
        ],
        recommendedJourneyType: 'islamic_traditional',
        confidence: 0.91,
      });

      const completionBuilder = createMockQueryBuilder();
      completionBuilder.update.mockReturnValue(completionBuilder);
      completionBuilder.eq.mockReturnValue(completionBuilder);
      completionBuilder.select.mockReturnValue(completionBuilder);
      completionBuilder.single.mockResolvedValue({
        data: {
          session_id: sessionId,
          status: 'completed',
          final_analysis: {
            recommendedJourneyType: 'islamic_traditional',
          },
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(completionBuilder);

      const completionResponse = await request(app)
        .post(`/api/assessment/${sessionId}/complete`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(completionResponse.status).toBe(200);
      expect(completionResponse.body.data.spiritualLandscape).toMatchObject({
        islamicInsights: expect.arrayContaining([
          expect.stringContaining('Qalb'),
          expect.stringContaining('Nafs'),
        ]),
        ruqyaRecommendations: expect.arrayContaining([
          expect.stringContaining('duas'),
          expect.stringContaining('Surah'),
        ]),
        recommendedJourneyType: 'islamic_traditional',
      });
    });
  });
});
