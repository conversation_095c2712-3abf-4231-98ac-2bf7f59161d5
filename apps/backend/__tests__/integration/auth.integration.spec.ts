/**
 * Authentication Integration Tests
 * Tests complete authentication flows and user management
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Import test helpers
import { createMockQueryBuilder } from './utils/test-helpers';

describe('Authentication Integration Tests', () => {
  const mockSupabase = getSupabase() as any;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('User Registration Flow', () => {
    const validSignupData = {
      email: '<EMAIL>',
      password: 'SecurePassword123!',
    };

    it('should register a new user successfully', async () => {
      const newUser = {
        id: 'new-user-id-456',
        email: validSignupData.email,
        created_at: new Date().toISOString(),
        email_confirmed_at: null,
      };

      const newSession = {
        access_token: 'new-access-token-123',
        refresh_token: 'new-refresh-token-123',
        expires_in: 3600,
        token_type: 'bearer',
        user: newUser,
      };

      // Mock successful signup
      mockSupabase.auth.signUp.mockResolvedValueOnce({
        data: { user: newUser, session: newSession },
        error: null,
      });

      // Mock profile creation
      const profileBuilder = createMockQueryBuilder();
      profileBuilder.insert.mockReturnValue(profileBuilder);
      profileBuilder.single.mockResolvedValue({
        data: {
          user_id: newUser.id,
          email: newUser.email,
          created_at: new Date().toISOString(),
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(profileBuilder);

      const response = await request(app)
        .post('/api/auth/signup')
        .send(validSignupData);

      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: newUser.id,
            email: validSignupData.email,
          }),
        },
      });

      // Verify profile was created
      expect(profileBuilder.insert).toHaveBeenCalledWith([
        expect.objectContaining({
          user_id: newUser.id,
          email: newUser.email,
        }),
      ]);
    });

    it('should return 400 for invalid email format', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'SecurePassword123!',
      };

      const response = await request(app)
        .post('/api/auth/signup')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('Invalid input data');
    });

    it('should return 400 for weak password', async () => {
      const weakPasswordData = {
        email: '<EMAIL>',
        password: '123', // Too short
      };

      const response = await request(app)
        .post('/api/auth/signup')
        .send(weakPasswordData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 400 for duplicate email', async () => {
      mockSupabase.auth.signUp.mockResolvedValueOnce({
        data: { user: null, session: null },
        error: { message: 'User already registered' },
      });

      const response = await request(app)
        .post('/api/auth/signup')
        .send(validSignupData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('User already registered');
    });

    it('should handle profile creation failure gracefully', async () => {
      const newUser = {
        id: 'new-user-id-789',
        email: validSignupData.email,
        created_at: new Date().toISOString(),
      };

      mockSupabase.auth.signUp.mockResolvedValueOnce({
        data: { user: newUser, session: null },
        error: null,
      });

      // Mock profile creation failure
      const errorProfileBuilder = createMockQueryBuilder();
      errorProfileBuilder.insert.mockResolvedValue({
        data: null,
        error: { message: 'Profile creation failed' },
      });
      mockSupabase.from.mockReturnValueOnce(errorProfileBuilder);

      const response = await request(app)
        .post('/api/auth/signup')
        .send(validSignupData);

      expect(response.status).toBe(500);
      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Profile creation failed');
    });
  });

  describe('User Login Flow', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'ExistingPassword123!',
    };

    it('should login existing user successfully', async () => {
      const existingUser = {
        id: 'existing-user-id-123',
        email: validLoginData.email,
        created_at: '2024-01-01T00:00:00.000Z',
        email_confirmed_at: '2024-01-01T00:00:00.000Z',
      };

      const loginSession = {
        access_token: 'login-access-token-456',
        refresh_token: 'login-refresh-token-456',
        expires_in: 3600,
        token_type: 'bearer',
        user: existingUser,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: { user: existingUser, session: loginSession },
        error: null,
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: existingUser.id,
            email: validLoginData.email,
          }),
          session: expect.objectContaining({
            access_token: expect.any(String),
            token_type: 'bearer',
          }),
        },
      });
    });

    it('should return 401 for invalid credentials', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValueOnce({
        data: { user: null, session: null },
        error: { message: 'Invalid login credentials' },
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
      expect(response.body.message).toContain('Invalid login credentials');
    });

    it('should return 400 for missing email', async () => {
      const incompleteData = {
        password: 'SomePassword123!',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(incompleteData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });

    it('should return 400 for missing password', async () => {
      const incompleteData = {
        email: '<EMAIL>',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(incompleteData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('fail');
    });
  });

  describe('Profile Management Flow', () => {
    const validToken = 'valid-access-token-789';
    const testUser = {
      id: 'profile-user-id-123',
      email: '<EMAIL>',
      created_at: '2024-01-01T00:00:00.000Z',
    };

    beforeEach(() => {
      // Mock authentication for profile endpoints
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: testUser },
        error: null,
      });
    });

    it('should get user profile successfully', async () => {
      const userProfile = {
        user_id: testUser.id,
        email: testUser.email,
        selected_layers: ['qalb', 'nafs'],
        journey_type: 'comprehensive',
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
      };

      const profileBuilder = createMockQueryBuilder();
      profileBuilder.select.mockReturnValue(profileBuilder);
      profileBuilder.eq.mockReturnValue(profileBuilder);
      profileBuilder.single.mockResolvedValue({
        data: userProfile,
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(profileBuilder);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: testUser.id,
            email: testUser.email,
          }),
          profile: expect.objectContaining({
            user_id: testUser.id,
            selected_layers: ['qalb', 'nafs'],
            journey_type: 'comprehensive',
          }),
        },
      });
    });

    it('should handle user with no profile', async () => {
      const noProfileBuilder = createMockQueryBuilder();
      noProfileBuilder.select.mockReturnValue(noProfileBuilder);
      noProfileBuilder.eq.mockReturnValue(noProfileBuilder);
      noProfileBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' }, // Not found
      });
      mockSupabase.from.mockReturnValueOnce(noProfileBuilder);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        status: 'success',
        data: {
          user: expect.objectContaining({
            id: testUser.id,
          }),
          profile: null,
        },
      });
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app).get('/api/auth/profile');

      expect(response.status).toBe(401);
      expect(response.body.status).toBe('fail');
    });
  });
});
