/**
 * Cross-Service Integration Tests
 * Tests complete workflows across multiple features and services
 */

import request from 'supertest';
import app from '../../src/main';
import { getSupabase } from '../../src/config/supabase';

// Helper function to create mock query builder
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: [], error: null }),
    geojson: jest.fn().mockResolvedValue({ data: [], error: null }),
    explain: jest.fn().mockResolvedValue({ data: [], error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn().mockReturnThis(),
  };

  // Make sure all methods return the builder for chaining
  Object.keys(mockBuilder).forEach((key) => {
    if (
      typeof mockBuilder[key] === 'function' &&
      ![
        'single',
        'maybeSingle',
        'csv',
        'geojson',
        'explain',
        'rollback',
      ].includes(key)
    ) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

describe('Cross-Service Integration Tests', () => {
  const mockSupabase = getSupabase() as any;
  const validToken = 'valid-cross-service-token-123';
  const testUser = {
    id: 'cross-service-user-id-123',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00.000Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock authentication
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: testUser },
      error: null,
    });
  });

  describe('Complete User Journey: Onboarding → Assessment → Journey Creation', () => {
    it('should complete the full workflow from new user to active healing journey', async () => {
      // PHASE 1: User Registration and Authentication
      const signupData = {
        email: testUser.email,
        password: 'SecurePassword123!',
      };

      // Mock user signup
      mockSupabase.auth.signUp.mockResolvedValueOnce({
        data: { user: testUser, session: { access_token: validToken } },
        error: null,
      });

      const profileBuilder = createMockQueryBuilder();
      profileBuilder.insert.mockReturnValue(profileBuilder);
      profileBuilder.single.mockResolvedValue({
        data: { user_id: testUser.id, email: testUser.email },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(profileBuilder);

      const signupResponse = await request(app)
        .post('/api/auth/signup')
        .send(signupData);

      expect(signupResponse.status).toBe(201);
      const userToken = signupResponse.body.data.session.access_token;

      // PHASE 2: Complete Onboarding (Feature 0)
      const onboardingData = {
        personalInfo: {
          awarenessLevel: 'intermediate',
          ruqyaFamiliarity: 'basic',
          age: 28,
          profession: 'teacher',
          culturalBackground: 'south_asian',
          gender: 'female',
          islamicKnowledgeLevel: 'intermediate',
        },
        preferences: {
          timeAvailability: 'moderate',
          learningStyle: 'visual',
          communityParticipation: true,
          languagePreference: 'english',
          notificationPreferences: {
            prayer_reminders: true,
            dhikr_reminders: true,
            journey_updates: true,
          },
        },
      };

      // Mock onboarding completion
      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({
        personalizedMessage: 'Your onboarding profile shows readiness for comprehensive spiritual assessment',
        islamicInsights: ['Strong foundation in Islamic knowledge', 'Ready for structured learning'],
        nextSteps: ['Begin Feature 1: Spiritual Assessment'],
        recommendedJourneyType: 'comprehensive',
        confidence: 0.88,
      });

      const onboardingBuilder = createMockQueryBuilder();
      onboardingBuilder.upsert.mockReturnValue(onboardingBuilder);
      onboardingBuilder.select.mockReturnValue(onboardingBuilder);
      onboardingBuilder.single.mockResolvedValue({
        data: {
          user_id: testUser.id,
          ...onboardingData.personalInfo,
          ...onboardingData.preferences,
          onboarding_completed: true,
          recommended_journey_type: 'comprehensive',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(onboardingBuilder);

      const onboardingResponse = await request(app)
        .post('/api/onboarding/submit')
        .set('Authorization', `Bearer ${userToken}`)
        .send(onboardingData);

      expect(onboardingResponse.status).toBe(200);
      expect(onboardingResponse.body.data.nextStep).toBe('assessment');

      const userProfile = onboardingResponse.body.data.profile;

      // PHASE 3: Complete Spiritual Assessment (Feature 1)
      const assessmentStartData = { userProfile };

      // Mock assessment session creation
      const assessmentSessionBuilder = createMockQueryBuilder();
      assessmentSessionBuilder.insert.mockReturnValue(assessmentSessionBuilder);
      assessmentSessionBuilder.single.mockResolvedValue({
        data: {
          session_id: 'assessment-session-456',
          user_id: testUser.id,
          current_step: 'welcome',
          status: 'active',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(assessmentSessionBuilder);

      const assessmentStartResponse = await request(app)
        .post('/api/assessment/start')
        .set('Authorization', `Bearer ${userToken}`)
        .send(assessmentStartData);

      expect(assessmentStartResponse.status).toBe(201);
      const sessionId = assessmentStartResponse.body.data.sessionId;

      // Complete assessment layers
      const layerResponses = [
        {
          step: 'physical_experiences',
          responses: {
            sleep_quality: 'fair',
            energy_levels: 'moderate',
            physical_symptoms: ['occasional_headaches'],
            appetite_changes: 'normal',
          },
        },
        {
          step: 'emotional_experiences',
          responses: {
            mood_patterns: 'generally_stable',
            anxiety_levels: 'mild',
            emotional_regulation: 'manageable',
            self_worth: 'moderate',
          },
        },
        {
          step: 'mental_experiences',
          responses: {
            concentration_issues: 'mild',
            memory_problems: 'none',
            decision_making: 'good',
            negative_thoughts: 'occasional',
          },
        },
        {
          step: 'spiritual_experiences',
          responses: {
            prayer_connection: 'strong',
            dhikr_practice: 'regular',
            quran_relationship: 'growing',
            spiritual_emptiness: 'rare',
          },
        },
      ];

      // Submit each layer response
      for (const layerResponse of layerResponses) {
        const responseBuilder = createMockQueryBuilder();
        responseBuilder.insert.mockReturnValue(responseBuilder);
        responseBuilder.single.mockResolvedValue({
          data: {
            response_id: `response-${layerResponse.step}`,
            session_id: sessionId,
            step: layerResponse.step,
            responses: layerResponse.responses,
          },
          error: null,
        });
        mockSupabase.from.mockReturnValueOnce(responseBuilder);

        global.mockAiService.analyzeAssessmentResponses.mockResolvedValueOnce({
          layerAnalysis: {
            [layerResponse.step]: {
              severity: 'mild',
              indicators: ['minor_concerns'],
              confidence: 0.85,
            },
          },
          overallSeverity: 'mild',
          nextStep: 'continue',
          confidence: 0.85,
        });

        const submitResponse = await request(app)
          .post(`/api/assessment/${sessionId}/submit`)
          .set('Authorization', `Bearer ${userToken}`)
          .send({ ...layerResponse, timeSpent: 180 });

        expect(submitResponse.status).toBe(200);
      }

      // Complete assessment
      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({
        personalizedMessage: 'Your assessment reveals a balanced spiritual state with room for growth',
        islamicInsights: [
          'Your strong prayer connection is a foundation for healing',
          'Focus on deepening dhikr practice for emotional regulation',
          'Your Qalb shows openness to spiritual growth',
        ],
        layerPriorities: ['Qalb', 'Nafs', 'Aql', 'Ruh', 'Jism'],
        crisisLevel: 'none',
        recommendedJourneyType: 'comprehensive',
        estimatedHealingDuration: 21,
        confidence: 0.89,
      });

      const completionBuilder = createMockQueryBuilder();
      completionBuilder.update.mockReturnValue(completionBuilder);
      completionBuilder.eq.mockReturnValue(completionBuilder);
      completionBuilder.select.mockReturnValue(completionBuilder);
      completionBuilder.single.mockResolvedValue({
        data: {
          session_id: sessionId,
          status: 'completed',
          final_analysis: {
            layerPriorities: ['Qalb', 'Nafs', 'Aql', 'Ruh', 'Jism'],
            recommendedJourneyType: 'comprehensive',
          },
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(completionBuilder);

      const assessmentCompletionResponse = await request(app)
        .post(`/api/assessment/${sessionId}/complete`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(assessmentCompletionResponse.status).toBe(200);
      expect(assessmentCompletionResponse.body.data.transitionToFeature2).toBe(true);

      const assessmentResults = assessmentCompletionResponse.body.data.spiritualLandscape;

      // PHASE 4: Create and Start Healing Journey (Feature 2)
      const journeyCreationData = {
        assessmentResults,
        preferences: {
          duration: 21,
          timeCommitment: 30,
          focusLayers: ['Qalb', 'Nafs'],
          ruqyaLevel: 'basic',
          communitySupport: true,
        },
      };

      // Mock journey generation
      global.mockAiService.generateJourneyParameters.mockResolvedValueOnce({
        duration: 21,
        timeCommitment: 30,
        primaryLayer: 'Qalb',
        secondaryLayers: ['Nafs', 'Aql'],
        ruqyaLevel: 'basic',
        communitySupport: true,
        type: 'comprehensive',
      });

      global.mockAiService.generateJourneyContent.mockResolvedValueOnce({
        title: 'Strengthening the Qalb: A 21-Day Spiritual Journey',
        description: 'A personalized journey to strengthen your heart connection with Allah',
        personalizedWelcome: 'Based on your assessment, this journey will focus on deepening your spiritual connection...',
        days: Array.from({ length: 21 }, (_, i) => ({
          day: i + 1,
          title: `Day ${i + 1}: Heart Purification`,
          practices: [
            {
              type: 'dhikr',
              name: 'Heart-centered Dhikr',
              duration: 15,
              instructions: 'Focus on your heart while reciting La ilaha illa Allah',
            },
          ],
        })),
      });

      const journeyBuilder = createMockQueryBuilder();
      journeyBuilder.insert.mockReturnValue(journeyBuilder);
      journeyBuilder.single.mockResolvedValue({
        data: {
          journey_id: 'journey-789-def',
          user_id: testUser.id,
          title: 'Strengthening the Qalb: A 21-Day Spiritual Journey',
          type: 'comprehensive',
          status: 'created',
          primary_layer: 'Qalb',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(journeyBuilder);

      const journeyCreationResponse = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${userToken}`)
        .send(journeyCreationData);

      expect(journeyCreationResponse.status).toBe(201);
      expect(journeyCreationResponse.body.data.journey.primaryLayer).toBe('Qalb');

      const journeyId = journeyCreationResponse.body.data.journey.journeyId;

      // Start the journey
      const journeyStartBuilder = createMockQueryBuilder();
      journeyStartBuilder.update.mockReturnValue(journeyStartBuilder);
      journeyStartBuilder.eq.mockReturnValue(journeyStartBuilder);
      journeyStartBuilder.select.mockReturnValue(journeyStartBuilder);
      journeyStartBuilder.single.mockResolvedValue({
        data: {
          journey_id: journeyId,
          status: 'active',
          started_at: new Date().toISOString(),
          current_day: 1,
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(journeyStartBuilder);

      const journeyStartResponse = await request(app)
        .post(`/api/journey/${journeyId}/start`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(journeyStartResponse.status).toBe(200);
      expect(journeyStartResponse.body.data.journeyStarted).toBe(true);

      // PHASE 5: Verify Complete User State
      const userJourneysBuilder = createMockQueryBuilder();
      userJourneysBuilder.select.mockReturnValue(userJourneysBuilder);
      userJourneysBuilder.eq.mockReturnValue(userJourneysBuilder);
      userJourneysBuilder.single.mockResolvedValue({
        data: [
          {
            journey_id: journeyId,
            title: 'Strengthening the Qalb: A 21-Day Spiritual Journey',
            status: 'active',
            current_day: 1,
            primary_layer: 'Qalb',
          },
        ],
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(userJourneysBuilder);

      const userJourneysResponse = await request(app)
        .get('/api/journey/user-journeys')
        .set('Authorization', `Bearer ${userToken}`);

      expect(userJourneysResponse.status).toBe(200);
      expect(userJourneysResponse.body.data.journeys).toHaveLength(1);
      expect(userJourneysResponse.body.data.journeys[0]).toMatchObject({
        journey_id: journeyId,
        status: 'active',
        primary_layer: 'Qalb',
      });

      // Verify the complete workflow created a coherent user experience
      expect(onboardingResponse.body.data.profile.recommended_journey_type).toBe('comprehensive');
      expect(assessmentResults.recommendedJourneyType).toBe('comprehensive');
      expect(journeyCreationResponse.body.data.journey.type).toBe('comprehensive');
    });
  });

  describe('AI Service Integration Across Features', () => {
    it('should maintain context and personalization across all AI interactions', async () => {
      const userContext = {
        culturalBackground: 'arab',
        islamicKnowledgeLevel: 'advanced',
        ruqyaFamiliarity: 'intermediate',
        languagePreference: 'arabic',
      };

      // Test AI consistency in onboarding
      global.mockAiService.analyzeSpiritualLandscape.mockResolvedValueOnce({
        personalizedMessage: 'مرحباً بك في رحلة الشفاء الروحي (Welcome to your spiritual healing journey)',
        islamicInsights: [
          'Your advanced Islamic knowledge will be leveraged in your journey',
          'Ruqya practices will be integrated at intermediate level',
        ],
        culturalAdaptations: ['Arabic language content', 'Arab cultural context'],
        confidence: 0.92,
      });

      // Test AI consistency in assessment
      global.mockAiService.analyzeAssessmentResponses.mockResolvedValueOnce({
        layerAnalysis: {
          culturalContext: 'arab',
          languagePreference: 'arabic',
          adaptations: ['Arabic terminology', 'Cultural sensitivity'],
        },
        confidence: 0.90,
      });

      // Test AI consistency in journey creation
      global.mockAiService.generateJourneyContent.mockResolvedValueOnce({
        title: 'رحلة تقوية القلب - Strengthening the Heart Journey',
        culturalAdaptations: {
          language: 'arabic_english_mix',
          practices: 'culturally_appropriate',
          references: 'arab_islamic_scholars',
        },
        days: [
          {
            day: 1,
            practices: [
              {
                type: 'dhikr',
                name: 'الذكر الصباحي - Morning Dhikr',
                arabicInstructions: 'ركز على قلبك أثناء الذكر',
                culturalNotes: 'Traditional Arab dhikr methodology',
              },
            ],
          },
        ],
      });

      // Verify AI maintains cultural context throughout
      expect(global.mockAiService.analyzeSpiritualLandscape).toHaveBeenCalledWith(
        expect.objectContaining({
          culturalBackground: 'arab',
          languagePreference: 'arabic',
        })
      );

      expect(global.mockAiService.generateJourneyContent).toHaveBeenCalledWith(
        expect.objectContaining({
          culturalContext: expect.objectContaining({
            background: 'arab',
            language: 'arabic',
          }),
        })
      );
    });
  });

  describe('Database Consistency Across Services', () => {
    it('should maintain data consistency across all service interactions', async () => {
      const userId = testUser.id;

      // Verify onboarding data is accessible to assessment
      const onboardingStatusBuilder = createMockQueryBuilder();
      onboardingStatusBuilder.select.mockReturnValue(onboardingStatusBuilder);
      onboardingStatusBuilder.eq.mockReturnValue(onboardingStatusBuilder);
      onboardingStatusBuilder.single.mockResolvedValue({
        data: {
          user_id: userId,
          onboarding_completed: true,
          awareness_level: 'intermediate',
          cultural_background: 'south_asian',
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(onboardingStatusBuilder);

      const onboardingStatusResponse = await request(app)
        .get('/api/onboarding/status')
        .set('Authorization', `Bearer ${validToken}`);

      expect(onboardingStatusResponse.status).toBe(200);

      // Verify assessment can access onboarding data
      const assessmentWithProfileBuilder = createMockQueryBuilder();
      assessmentWithProfileBuilder.insert.mockReturnValue(assessmentWithProfileBuilder);
      assessmentWithProfileBuilder.single.mockResolvedValue({
        data: {
          session_id: 'consistency-session-123',
          user_profile: {
            awareness_level: 'intermediate',
            cultural_background: 'south_asian',
          },
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(assessmentWithProfileBuilder);

      const assessmentStartResponse = await request(app)
        .post('/api/assessment/start')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          userProfile: onboardingStatusResponse.body.data.profile,
        });

      expect(assessmentStartResponse.status).toBe(201);

      // Verify journey can access both onboarding and assessment data
      const journeyWithHistoryBuilder = createMockQueryBuilder();
      journeyWithHistoryBuilder.insert.mockReturnValue(journeyWithHistoryBuilder);
      journeyWithHistoryBuilder.single.mockResolvedValue({
        data: {
          journey_id: 'consistency-journey-456',
          user_id: userId,
          based_on_onboarding: true,
          based_on_assessment: true,
          cultural_adaptations: ['south_asian_context'],
        },
        error: null,
      });
      mockSupabase.from.mockReturnValueOnce(journeyWithHistoryBuilder);

      const journeyCreationResponse = await request(app)
        .post('/api/journey/create')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          assessmentResults: { layerPriorities: ['Qalb'] },
          preferences: { culturalAdaptation: true },
        });

      expect(journeyCreationResponse.status).toBe(201);

      // Verify all services can access user's complete history
      expect(mockSupabase.from).toHaveBeenCalledWith('user_profiles');
      expect(mockSupabase.from).toHaveBeenCalledWith('assessment_sessions');
      expect(mockSupabase.from).toHaveBeenCalledWith('healing_journeys');
    });
  });
});
