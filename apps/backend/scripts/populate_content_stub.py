"""
Content Population Script Stub for Qalb Healing Backend

This script provides a structure and sample data for populating the database
with content for Names of Allah, Quranic Verses, and Sunnah Practices.
It does not perform actual database operations but simulates them by printing
actions or generating SQL INSERT statements (commented out).

In a real scenario, this script would use Prisma Client (if run in a Node.js
environment with a Python bridge) or a Python ORM connected to the database,
or it could generate a .sql file to be executed.
"""
import json
import datetime # Not strictly needed for this stub, but good for real scripts
import uuid # For generating IDs if not predefined in samples

# --- Sample Data (from previous design step) ---

sample_names_of_allah = [
    {
        "id": "noa-sample-001",
        "name": "<PERSON><PERSON><PERSON><PERSON>", "arabicScript": "ٱلرَّحْمَـٰنُ", "audioUrl": "https://example.com/audio/ar_rahman.mp3",
        "meaning": "The Entirely Merciful, The Most Beneficent",
        "significance": "He who wills goodness and mercy for all His creatures. This name signifies <PERSON>'s universal mercy.",
        "reflectionPrompt": "How have you witnessed <PERSON>'s Ra<PERSON>ah (mercy) in your life today, big or small?",
        "practicalApplication": "Practice showing mercy to yourself and others. Forgive someone or choose gentleness in an interaction.",
        "dhikrCount": 100, "layerFocus": ["qalb", "ruh"],
        "benefits": ["Hope", "Comfort", "Connection to Divine Mercy"],
        "sourceReference": "Quran, Al-Fatiha (1:1)", "isActive": True
    },
    {
        "id": "noa-sample-002",
        "name": "Al-Mumin", "arabicScript": "ٱلْمُؤْمِنُ", "audioUrl": "https://example.com/audio/al_mumin.mp3",
        "meaning": "The One Who gives Emaan and Security",
        "significance": "He who provides security and removes fear. Reflecting on this name brings peace to the heart.",
        "reflectionPrompt": "In what areas of your life do you seek more security and peace from Al-Mumin?",
        "practicalApplication": "Strive to be a source of security and trustworthiness for those around you.",
        "dhikrCount": 100, "layerFocus": ["qalb", "nafs"],
        "benefits": ["Security", "Peace", "Trust"],
        "sourceReference": "Quran, Al-Hashr (59:23)", "isActive": True
    },
    {
        "id": "noa-sample-003",
        "name": "As-Sabur", "arabicScript": "ٱلصَّبُورُ", "audioUrl": "https://example.com/audio/as_sabur.mp3",
        "meaning": "The Patient, The Timeless",
        "significance": "He who is most patient and does not hasten to punish. This teaches us the virtue of patience in adversity.",
        "reflectionPrompt": "How can you cultivate more Sabr (patience) in response to current challenges?",
        "practicalApplication": "Practice patience in a situation today where you might normally feel rushed or frustrated.",
        "dhikrCount": 100, "layerFocus": ["nafs", "aql"],
        "benefits": ["Patience", "Resilience", "Steadfastness"], "isActive": True
    }
]

sample_quranic_verses = [
    {
        "id": "qvr-sample-001", "surahNumber": 2, "ayahNumber": 286,
        "arabicText": "لَا يُكَلِّفُ اللَّهُ نَفْسًا إِلَّا وُسْعَهَا ۚ لَهَا مَا كَسَبَتْ وَعَلَيْهَا مَا اكْتَسَبَتْ ۗ...",
        "audioUrl": "https://example.com/audio/baqarah_286.mp3",
        "reciterOptions": {"Mishary Alafasy": "url1", "Abdurrahman As-Sudais": "url2"},
        "translationEn": "Allah does not charge a soul except [with that within] its capacity...",
        "translationUr": "اللہ کسی جان پر اس کی طاقت سے زیادہ بوجھ نہیں ڈالتا۔",
        "tafsirSource": "Tafsir Ibn Kathir (Summarized)",
        "tafsirEn": "This verse emphasizes Allah's justice and mercy...",
        "contextualExplanation": "A powerful reminder of personal accountability...",
        "reflectionPrompts": ["What current burden feels heavy...?", "How can you focus on what is within your capacity...?"],
        "practicalApplication": "When feeling overwhelmed, recite this verse...",
        "layerFocus": ["qalb", "aql", "nafs"], "themes": ["Tawakkul", "Ease", "Responsibility", "Mercy"],
        "relatedHadith": ["Hadith about not overburdening oneself..."], "isActive": True
    },
    {
        "id": "qvr-sample-002", "surahNumber": 94, "ayahNumber": 5,
        "arabicText": "فَإِنَّ مَعَ الْعُسْرِ يُسْرًا", "audioUrl": "https://example.com/audio/ash_sharh_5.mp3",
        "translationEn": "For indeed, with hardship [will be] ease.",
        "tafsirSource": "Maariful Quran (Summarized)",
        "tafsirEn": "A reassurance from Allah that every difficulty is accompanied by ease...",
        "contextualExplanation": "This verse is a source of immense hope during challenging times...",
        "reflectionPrompts": ["Recall a past hardship. How did ease eventually manifest?", "How does this verse help you face current difficulties?"],
        "practicalApplication": "When facing a difficulty, remind yourself of this verse...",
        "layerFocus": ["qalb", "nafs"], "themes": ["Hope", "Resilience", "Ease after Hardship"], "isActive": True
    }
]

sample_sunnah_practices = [
    {
        "id": "sp-sample-001", "title": "Smiling at Your Brother/Sister", "category": "Social",
        "description": "The Prophet Muhammad (peace be upon him) said, 'Your smiling in the face of your brother is charity.'...",
        "stepsJson": [{"step": 1, "instruction": "Recall the intention..."}, {"step": 2, "instruction": "When you meet... offer a smile."}],
        "duasJson": None,
        "intention": "To practice a beloved Sunnah, spread positivity...",
        "reflection": "How did it feel to consciously smile...?",
        "benefits": ["Sadaqah (Charity)", "Spreads Positivity", "Strengthens Bonds"],
        "estimatedDuration": 1, "difficultyLevel": "beginner", "sourceReference": "Tirmidhi",
        "layerFocus": ["qalb", "nafs"], "relatedContentIds": [], "isActive": True
    },
    {
        "id": "sp-sample-002", "title": "Mindful Eating - Sunnahs of Eating", "category": "Physical",
        "description": "Eating according to the Sunnah involves mindfulness, gratitude...",
        "stepsJson": [{"step": 1, "instruction": "Wash hands..."}, {"step": 2, "instruction": "Say 'Bismillah'..."}],
        "duasJson": [{"name": "Dua before eating", "arabic": "بِسْمِ اللهِ", "translation": "In the name of Allah."}],
        "intention": "To nourish the body as an Amaanah (trust) from Allah...",
        "reflection": "How does eating mindfully... change your relationship with food...?",
        "benefits": ["Mindfulness", "Gratitude", "Better Digestion", "Barakah in Food"],
        "estimatedDuration": 15, "difficultyLevel": "beginner", "sourceReference": "Various Hadith",
        "layerFocus": ["jism", "nafs", "qalb"], "isActive": True
    }
]

# --- Placeholder Database Interaction Functions ---

def generate_sql_insert(table_name: str, data: dict) -> str:
    """Generates a generic SQL INSERT statement string."""
    columns = ', '.join(data.keys())

    values_list = []
    for value in data.values():
        if isinstance(value, str):
            values_list.append(f"'{value.replace(\"'\", \"''\")}'") # Escape single quotes
        elif isinstance(value, bool):
            values_list.append(str(value).upper())
        elif isinstance(value, (list, dict)): # For JSON arrays/objects
            values_list.append(f"'{json.dumps(value)}'")
        elif value is None:
            values_list.append("NULL")
        else:
            values_list.append(str(value))

    values = ', '.join(values_list)
    return f"INSERT INTO {table_name} ({columns}) VALUES ({values});"

def populate_names_of_allah(data_list: list):
    print("\n--- Populating Names of Allah Content (Stub) ---")
    table_name = "name_of_allah_content" # As per schema.prisma
    for item in data_list:
        # Simulate DB interaction (e.g., using Prisma client)
        # prisma.nameOfAllahContent.create({ data: item })
        print(f"Simulating: Inserting Name of Allah - {item.get('name')}")
        # print(generate_sql_insert(table_name, item)) # Uncomment to see SQL
    print(f"--- Finished Populating {len(data_list)} Names of Allah ---")

def populate_quranic_verses(data_list: list):
    print("\n--- Populating Quranic Verse Content (Stub) ---")
    table_name = "quranic_verse_content" # As per schema.prisma
    for item in data_list:
        # Simulate DB interaction
        # prisma.quranicVerseContent.create({ data: item })
        print(f"Simulating: Inserting Quranic Verse - Surah {item.get('surahNumber')}:{item.get('ayahNumber')}")
        # print(generate_sql_insert(table_name, item)) # Uncomment to see SQL
    print(f"--- Finished Populating {len(data_list)} Quranic Verses ---")

def populate_sunnah_practices(data_list: list):
    print("\n--- Populating Sunnah Practice Content (Stub) ---")
    table_name = "sunnah_practice_content" # As per schema.prisma
    for item in data_list:
        # Simulate DB interaction
        # prisma.sunnahPracticeContent.create({ data: item })
        print(f"Simulating: Inserting Sunnah Practice - {item.get('title')}")
        # print(generate_sql_insert(table_name, item)) # Uncomment to see SQL
    print(f"--- Finished Populating {len(data_list)} Sunnah Practices ---")

# --- Main Execution ---
if __name__ == "__main__":
    print("Starting Content Population Stub Script...")

    # Generate UUIDs for items if 'id' is not provided or needs to be overridden
    # For this stub, we are using predefined string IDs for simplicity.
    # In a real script, you might do:
    # for item in sample_names_of_allah:
    #     if "id" not in item: item["id"] = str(uuid.uuid4())
    # etc. for other lists

    populate_names_of_allah(sample_names_of_allah)
    populate_quranic_verses(sample_quranic_verses)
    populate_sunnah_practices(sample_sunnah_practices)

    print("\nContent Population Stub Script Finished.")
    print("Note: This script only simulates operations. No actual database changes were made.")
    print("Uncomment 'print(generate_sql_insert(...))' lines to see example SQL statements.")
