declare module 'compression' {
  import { RequestHand<PERSON> } from 'express';
  function compression(): RequestHandler;
  export = compression;
}

declare module 'xss-clean' {
  import { RequestHandler } from 'express';
  function xssClean(): RequestHandler;
  export = xssClean;
}

declare module 'validator' {
  export function escape(str: string): string;
  export function isEmail(str: string): boolean;
  export function isLength(str: string, options?: { min?: number; max?: number }): boolean;
  // Add other validator functions as needed
}
