import { Prisma } from '@prisma/client';
import { AISpiritualLandscapeResponse, AILayerAnalysisOutput, AIServiceUserProfileData } from '../../services/ai.service'; // Adjust
import { DiagnosisDelivery } from '../../models/Assessment'; // Adjust
import { formatDiagnosisForDelivery } from '../diagnosisDelivery.presenter'; // Adjust
import * as UserProfileHelper from '../../utils/assessmentUserProfile.helper'; // Adjust

// Mock the imported helper functions
jest.mock('../../utils/assessmentUserProfile.helper', () => ({
  prepareProfileForAI: jest.fn(),
  determineUserTypeFromProfile: jest.fn(),
  getDeliveryStyleForUserType: jest.fn(),
}));

describe('diagnosisDelivery.presenter', () => {
  const mockDiagnosisRecord = {
    id: 'diag-123',
    generatedAt: new Date('2023-01-01T10:00:00.000Z'),
  };
  const mockUserId = 'user-xyz';
  const mockUserProfileJson = { type: 'test' } as Prisma.JsonObject;

  const mockAIProfile: AIServiceUserProfileData = { user_id: mockUserId, mental_health_awareness: {level: 'basic'} };

  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementations
    (UserProfileHelper.prepareProfileForAI as jest.Mock).mockReturnValue(mockAIProfile);
    (UserProfileHelper.determineUserTypeFromProfile as jest.Mock).mockReturnValue('symptom_aware');
    (UserProfileHelper.getDeliveryStyleForUserType as jest.Mock).mockReturnValue('gentle');
  });

  const minimalAIResponse: AISpiritualLandscapeResponse = {
    id: 'ai-resp-min',
    primary_layer: 'qalb',
    layer_insights: {
      qalb: { layer: 'qalb', layer_name: 'Qalb', severity_score: 60, insights: ['Minimal insight.'], affected_symptoms: [], recommendations: [], islamic_context: [] },
    },
    personalized_message: 'Minimal personalized message.',
    // other fields will be undefined/null for minimal test
  };

  const comprehensiveAIResponse: AISpiritualLandscapeResponse = {
    id: 'ai-resp-comp',
    primary_layer: 'nafs',
    layer_insights: {
      nafs: { layer: 'nafs', layer_name: 'Nafs (Ego)', severity_score: 80, insights: ['Nafs requires discipline.'], affected_symptoms: ['Anger'], recommendations: ['Fasting'], islamic_context: ['Hadith on ego'] },
      aql: { layer: 'aql', layer_name: 'Aql (Intellect)', severity_score: 70, insights: ['Aql needs clarity.'], affected_symptoms: ['Overthinking'], recommendations: ['Seek knowledge'], islamic_context: ['Quran on knowledge'] },
      jism: { layer: 'jism', layer_name: 'Jism (Body)', severity_score: 30, insights: ['Body needs care.'], affected_symptoms: ['Lethargy'], recommendations: ['Exercise'], islamic_context: ['Body is a trust'] },
    },
    personalized_message: 'A detailed message for you.',
    educational_content: 'In-depth educational content about layers.',
    islamic_insights: ['Key Islamic insight 1.', 'Key Islamic insight 2.'],
    next_steps: ['Actionable Step 1', 'Actionable Step 2'],
    recommended_journey_type: 'advanced_nafs_purification',
    estimated_healing_duration: '6 months',
    confidence: 0.85,
    crisis_level: 'medium',
    five_layers_explanation: 'Detailed explanation of the five layers of the self.',
    additional_resources: [{ title: 'Book on Nafs', url: 'http://example.com/nafs' }],
  };

  it('should call helper functions with correct arguments', () => {
    formatDiagnosisForDelivery(mockDiagnosisRecord, minimalAIResponse, mockUserProfileJson, mockUserId);
    expect(UserProfileHelper.prepareProfileForAI).toHaveBeenCalledWith(mockUserProfileJson, mockUserId);
    expect(UserProfileHelper.determineUserTypeFromProfile).toHaveBeenCalledWith(mockAIProfile);
    expect(UserProfileHelper.getDeliveryStyleForUserType).toHaveBeenCalledWith('symptom_aware'); // Default mock return
  });

  it('should correctly map fields for a minimal AI response', () => {
    const result = formatDiagnosisForDelivery(mockDiagnosisRecord, minimalAIResponse, mockUserProfileJson, mockUserId);

    expect(result.userId).toBe(mockUserId);
    expect(result.userType).toBe('symptom_aware'); // From mock
    expect(result.deliveryStyle).toBe('gentle'); // From mock
    expect(result.generatedAt).toEqual(mockDiagnosisRecord.generatedAt);
    expect(result.diagnosisId).toBe(mockDiagnosisRecord.id);

    expect(result.diagnosis.id).toBe(minimalAIResponse.id);
    expect(result.diagnosis.primary_layer).toBe('qalb');
    expect(result.layerIntroduction).toBe('Minimal personalized message.');
    expect(result.educationalDiagnosis).toBe('Understanding the layers of the self (Jism, Nafs, Aql, Qalb, Ruh) can help in your journey towards healing and growth.'); // Default
    expect(result.islamicInsightsSummary).toBe("Reflect on Allah's guidance and seek knowledge to deepen your understanding."); // Default
    expect(result.nextStepsSummary).toBe("Continue your journey with recommended actions. Further details can be explored in your personalized plan."); // Default
    expect(result.journeyRecommendation).toBe('Recommended Journey: General Wellness Program (Estimated Duration: Varies)'); // Defaults
    expect(result.primaryLayerAnalysisSummary).toContain('Primary Focus: The Qalb Layer. Insight: Minimal insight.');
    expect(result.secondaryLayersSummary).toBe('No significant secondary areas of concern noted at this time.');
    expect(result.additionalResources).toEqual([]);
    expect(result.crisisInfo.level).toBe('low'); // Default from AISpiritualLandscapeResponse if not set, or from presenter
    expect(result.fiveLayersOverview).toBe("Islam teaches us about five interconnected layers of our being: Jism (Body), Nafs (Ego), Aql (Intellect), Qalb (Spiritual Heart), and Ruh (Soul). Understanding these helps in holistic healing."); // Default
  });

  it('should correctly map fields for a comprehensive AI response', () => {
    (UserProfileHelper.determineUserTypeFromProfile as jest.Mock).mockReturnValue('clinically_aware');
    (UserProfileHelper.getDeliveryStyleForUserType as jest.Mock).mockReturnValue('clinical');

    const result = formatDiagnosisForDelivery(mockDiagnosisRecord, comprehensiveAIResponse, mockUserProfileJson, mockUserId);

    expect(result.userType).toBe('clinically_aware');
    expect(result.deliveryStyle).toBe('clinical');
    expect(result.diagnosis.id).toBe(comprehensiveAIResponse.id);
    expect(result.diagnosis.primary_layer).toBe('nafs');
    expect(result.diagnosis.crisis_level).toBe('medium');
    expect(result.layerIntroduction).toBe('A detailed message for you.');
    expect(result.educationalDiagnosis).toBe('In-depth educational content about layers.');
    expect(result.islamicInsightsSummary).toBe("Key Islamic Insights:\n• Key Islamic insight 1.\n• Key Islamic insight 2.");
    expect(result.nextStepsSummary).toBe("Recommended Next Steps:\n- Actionable Step 1\n- Actionable Step 2");
    expect(result.journeyRecommendation).toBe('Recommended Journey: advanced_nafs_purification (Estimated Duration: 6 months)');
    expect(result.primaryLayerAnalysisSummary).toContain('Primary Focus: The Nafs (Ego) Layer. Insight: Nafs requires discipline.');
    expect(result.secondaryLayersSummary).toContain('Secondary Areas: Aql (Intellect) (Severity Score: 70).');
    expect(result.additionalResources).toEqual([{ title: 'Book on Nafs', url: 'http://example.com/nafs' }]);
    expect(result.crisisInfo.level).toBe('medium');
    expect(result.crisisInfo.message).toContain('Your assessment indicates a need for immediate attention.');
    expect(result.fiveLayersOverview).toBe('Detailed explanation of the five layers of the self.');
  });

  it('should handle primary_layer being null or "none"', () => {
    const noPrimaryLayerAIResp = { ...minimalAIResponse, primary_layer: null };
    let result = formatDiagnosisForDelivery(mockDiagnosisRecord, noPrimaryLayerAIResp, mockUserProfileJson, mockUserId);
    expect(result.primaryLayerAnalysisSummary).toBe('Your assessment indicates a general state of balance. Focus on consistent spiritual nourishment and practices for continued well-being.');

    const nonePrimaryLayerAIResp = { ...minimalAIResponse, primary_layer: 'none' as 'none' };
    result = formatDiagnosisForDelivery(mockDiagnosisRecord, nonePrimaryLayerAIResp, mockUserProfileJson, mockUserId);
    expect(result.primaryLayerAnalysisSummary).toBe('Your assessment indicates a general state of balance. Focus on consistent spiritual nourishment and practices for continued well-being.');
  });

  it('should handle missing insights in primary layer summary', () => {
    const noInsightPrimaryLayer: AISpiritualLandscapeResponse = {
        ...minimalAIResponse,
        layer_insights: {
            qalb: { ...minimalAIResponse.layer_insights.qalb, insights: [] }
        }
    };
    const result = formatDiagnosisForDelivery(mockDiagnosisRecord, noInsightPrimaryLayer, mockUserProfileJson, mockUserId);
    expect(result.primaryLayerAnalysisSummary).toContain('Primary Focus: The Qalb Layer. Insight: Personalized guidance is available.');
  });

  it('should correctly format layer_name if available, else layer key', () => {
    const responseWithSpecificLayerName: AISpiritualLandscapeResponse = {
        ...minimalAIResponse,
        layer_insights: {
          qalb_spiritual: { layer: 'qalb_spiritual', layer_name: 'Spiritual Heart Core', severity_score: 60, insights: ['Core insight.'], affected_symptoms: [], recommendations: [], islamic_context: [] },
        },
        primary_layer: 'qalb_spiritual'
    };
    const result = formatDiagnosisForDelivery(mockDiagnosisRecord, responseWithSpecificLayerName, mockUserProfileJson, mockUserId);
    expect(result.primaryLayerAnalysisSummary).toContain('Primary Focus: The Spiritual Heart Core Layer.');
  });
});
