import { Prisma } from '../config/database';
import { DiagnosisDelivery } from '../models/Assessment';
import { AISpiritualLandscapeResponse, AILayerAnalysisOutput } from '../services/ai.service';
import { AIServiceUserProfileData } from '../services/ai.service'; // Assuming this type is needed
import { determineUserTypeFromProfile, getDeliveryStyleForUserType, prepareProfileForAI } from '../utils/assessmentUserProfile.helper'; // Assuming this is where user type logic is

// Helper to format layer names (can be local or imported)
function formatLayerName(layerKey: string | null | undefined): string {
  if (!layerKey || layerKey === 'none') return 'General Well-being';
  return layerKey.charAt(0).toUpperCase() + layerKey.slice(1);
}

function getLayerSummary(aiResponse: AISpiritualLandscapeResponse, isPrimary: boolean): string {
  if (isPrimary) {
    if (aiResponse.primary_layer && aiResponse.primary_layer !== 'none' && aiResponse.layer_insights[aiResponse.primary_layer]) {
      const layer = aiResponse.layer_insights[aiResponse.primary_layer];
      // Ensure insights is an array and access its first element safely
      const firstInsight = Array.isArray(layer.insights) && layer.insights.length > 0 ? layer.insights[0] : 'Personalized guidance is available.';
      return `Primary Focus: The ${formatLayerName(layer.layer_name || layer.layer)} Layer. Insight: ${firstInsight}`;
    }
    return 'Your assessment indicates a general state of balance. Focus on consistent spiritual nourishment and practices for continued well-being.';
  } else {
    const secondaryInsights = Object.values(aiResponse.layer_insights)
      .filter(l => l.layer !== aiResponse.primary_layer && l.severity_score >= 40) // Example threshold
      .map(l => `${formatLayerName(l.layer_name || l.layer)} (Severity Score: ${l.severity_score})`)
      .join(', ');
    return secondaryInsights.length > 0 ? `Secondary Areas: ${secondaryInsights}.` : 'No significant secondary areas of concern noted at this time.';
  }
}

export function formatDiagnosisForDelivery(
  diagnosisRecord: { generatedAt: Date; id: string }, // Simplified relevant fields from PrismaSpiritualDiagnosis
  aiResponse: AISpiritualLandscapeResponse,
  userProfileFromSession: Prisma.JsonValue,
  userId: string
): DiagnosisDelivery {
  // Prepare the profile in the format expected by determineUserTypeFromProfile
  const preparedProfile: AIServiceUserProfileData = prepareProfileForAI(userProfileFromSession, userId);
  const userType = determineUserTypeFromProfile(preparedProfile);
  const deliveryStyle = getDeliveryStyleForUserType(userType);

  // Construct the DiagnosisDelivery object
  // Ensure all fields from the DiagnosisDelivery model are covered
  return {
    userId: userId,
    userType: userType,
    deliveryStyle: deliveryStyle,
    generatedAt: diagnosisRecord.generatedAt,
    diagnosisId: diagnosisRecord.id, // Added diagnosisId

    // Assuming 'diagnosis' object within DiagnosisDelivery directly mirrors AISpiritualLandscapeResponse
    // or specific parts of it. Adjust as per actual DiagnosisDelivery model in models/Assessment.ts
    diagnosis: {
        id: aiResponse.id, // AI response might have its own ID
        // Map all fields from AISpiritualLandscapeResponse that are part of DiagnosisDelivery.diagnosis
        primary_layer: aiResponse.primary_layer,
        layer_insights: aiResponse.layer_insights,
        personalized_message: aiResponse.personalized_message,
        educational_content: aiResponse.educational_content,
        islamic_insights: aiResponse.islamic_insights,
        next_steps: aiResponse.next_steps,
        recommended_journey_type: aiResponse.recommended_journey_type,
        estimated_healing_duration: aiResponse.estimated_healing_duration,
        confidence: aiResponse.confidence,
        crisis_level: aiResponse.crisis_level,
        // ... any other fields from AISpiritualLandscapeResponse needed by the client
    },

    // Summaries and educational content based on the AI response
    layerIntroduction: aiResponse.personalized_message || 'Please review your detailed spiritual diagnosis below. It offers insights into different aspects of your well-being.',
    educationalDiagnosis: aiResponse.educational_content || 'Understanding the layers of the self (Jism, Nafs, Aql, Qalb, Ruh) can help in your journey towards healing and growth.', // Renamed from educationalContent

    // Assuming islamicInsights and nextStepsGuidance are derived from arrays in aiResponse
    islamicInsightsSummary: Array.isArray(aiResponse.islamic_insights) && aiResponse.islamic_insights.length > 0
        ? "Key Islamic Insights:\n• " + aiResponse.islamic_insights.join('\n• ')
        : "Reflect on Allah's guidance and seek knowledge to deepen your understanding.",

    nextStepsSummary: Array.isArray(aiResponse.next_steps) && aiResponse.next_steps.length > 0
        ? "Recommended Next Steps:\n" + aiResponse.next_steps.map(step => `- ${step}`).join('\n')
        : "Continue your journey with recommended actions. Further details can be explored in your personalized plan.",

    journeyRecommendation: `Recommended Journey: ${aiResponse.recommended_journey_type || 'General Wellness Program'} (Estimated Duration: ${aiResponse.estimated_healing_duration || 'Varies'})`,

    primaryLayerAnalysisSummary: getLayerSummary(aiResponse, true),
    secondaryLayersSummary: getLayerSummary(aiResponse, false),

    // Populate if AI provides this or if derived elsewhere
    additionalResources: aiResponse.additional_resources || [],

    // Make sure all fields defined in DiagnosisDelivery model are present
    crisisInfo: { // Added crisisInfo
        level: aiResponse.crisis_level || 'low',
        message: aiResponse.crisis_level && aiResponse.crisis_level !== 'low' ? 'Your assessment indicates a need for immediate attention. Please see resources provided.' : 'No immediate crisis detected.'
    },
    fiveLayersOverview: aiResponse.five_layers_explanation || "Islam teaches us about five interconnected layers of our being: Jism (Body), Nafs (Ego), Aql (Intellect), Qalb (Spiritual Heart), and Ruh (Soul). Understanding these helps in holistic healing." // Added fiveLayersOverview
  };
}
