-- Feature 2: Personalized Healing Journeys Database Schema
-- Complete journey system with AI-powered personalization

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Journey Types Enum
CREATE TYPE journey_type AS ENUM (
  'tranquil_mind',
  'heart_purification', 
  'ego_purification',
  'spiritual_optimization',
  'crisis_recovery',
  'maintenance_program'
);

-- Journey Status Enum
CREATE TYPE journey_status AS ENUM (
  'created',
  'active',
  'paused',
  'completed',
  'abandoned',
  'evolved'
);

-- Practice Types Enum
CREATE TYPE practice_type AS ENUM (
  'dhikr',
  'prayer',
  'reflection',
  'study',
  'community',
  'ruqya',
  'mindfulness',
  'gratitude'
);

-- Layer Focus Enum
CREATE TYPE layer_focus AS ENUM (
  'jism',
  'nafs', 
  'aql',
  'qalb',
  'ruh'
);

-- Crisis Level Enum
CREATE TYPE crisis_level AS ENUM (
  'none',
  'low',
  'moderate',
  'high',
  'critical'
);

-- Main Journeys Table
CREATE TABLE journeys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  assessment_id UUID NOT NULL REFERENCES assessments(id),
  type journey_type NOT NULL,
  status journey_status NOT NULL DEFAULT 'created',
  
  -- Journey Content
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  personalized_welcome TEXT NOT NULL,
  
  -- Configuration
  duration INTEGER NOT NULL CHECK (duration >= 7 AND duration <= 90),
  daily_time_commitment INTEGER NOT NULL CHECK (daily_time_commitment >= 5 AND daily_time_commitment <= 60),
  primary_layer layer_focus NOT NULL,
  secondary_layers layer_focus[] DEFAULT '{}',
  ruqya_integration_level TEXT NOT NULL DEFAULT 'none',
  community_integration BOOLEAN DEFAULT false,
  professional_context TEXT,
  cultural_adaptations TEXT[] DEFAULT '{}',
  crisis_support BOOLEAN DEFAULT false,
  
  -- Progress Tracking
  current_day INTEGER DEFAULT 1,
  completed_days INTEGER[] DEFAULT '{}',
  total_progress DECIMAL(5,2) DEFAULT 0.00 CHECK (total_progress >= 0 AND total_progress <= 100),
  
  -- Personalization Data
  user_profile JSONB NOT NULL,
  
  -- Community Integration
  community_group UUID,
  mentor_id UUID REFERENCES auth.users(id),
  peer_connections UUID[] DEFAULT '{}',
  
  -- AI Features
  ai_recommendations TEXT[] DEFAULT '{}',
  adaptive_adjustments JSONB[] DEFAULT '{}',
  
  -- Crisis Management
  crisis_flags JSONB[] DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  last_active_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Journey Days Table (stores daily content)
CREATE TABLE journey_days (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  journey_id UUID NOT NULL REFERENCES journeys(id) ON DELETE CASCADE,
  day_number INTEGER NOT NULL,
  theme TEXT NOT NULL,
  learning_objective TEXT NOT NULL,
  reflection_prompts TEXT[] DEFAULT '{}',
  community_activity TEXT,
  progress_milestone TEXT,
  adaptive_content JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(journey_id, day_number)
);

-- Daily Practices Table
CREATE TABLE daily_practices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  journey_day_id UUID NOT NULL REFERENCES journey_days(id) ON DELETE CASCADE,
  type practice_type NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  duration INTEGER NOT NULL, -- minutes
  instructions TEXT NOT NULL,
  arabic_text TEXT,
  transliteration TEXT,
  translation TEXT,
  benefits TEXT[] DEFAULT '{}',
  layer_focus layer_focus NOT NULL,
  difficulty_level TEXT NOT NULL DEFAULT 'beginner',
  ruqya_component BOOLEAN DEFAULT false,
  professional_context TEXT,
  cultural_notes TEXT,
  order_index INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Journey Progress Table
CREATE TABLE journey_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  journey_id UUID NOT NULL REFERENCES journeys(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  day_number INTEGER NOT NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  
  -- Practice Completion
  practices_completed JSONB[] DEFAULT '{}',
  
  -- Daily Metrics
  overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
  mood_before INTEGER CHECK (mood_before >= 1 AND mood_before <= 10),
  mood_after INTEGER CHECK (mood_after >= 1 AND mood_after <= 10),
  spiritual_connection INTEGER CHECK (spiritual_connection >= 1 AND spiritual_connection <= 10),
  stress_level INTEGER CHECK (stress_level >= 1 AND stress_level <= 10),
  
  -- Reflection
  daily_reflection TEXT,
  gratitude TEXT[] DEFAULT '{}',
  challenges TEXT[] DEFAULT '{}',
  insights TEXT[] DEFAULT '{}',
  
  -- Community Engagement
  community_participation BOOLEAN DEFAULT false,
  community_contribution TEXT,
  peer_support BOOLEAN DEFAULT false,
  
  -- Adaptive Feedback
  content_relevance INTEGER CHECK (content_relevance >= 1 AND content_relevance <= 5),
  practice_effectiveness INTEGER CHECK (practice_effectiveness >= 1 AND practice_effectiveness <= 5),
  time_appropriate BOOLEAN,
  suggested_adjustments TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(journey_id, user_id, day_number)
);

-- Journey Analytics Table
CREATE TABLE journey_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  journey_id UUID NOT NULL REFERENCES journeys(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Completion Metrics
  completion_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
  average_daily_rating DECIMAL(3,2),
  practice_adherence DECIMAL(5,2) NOT NULL DEFAULT 0.00,
  community_engagement DECIMAL(5,2) NOT NULL DEFAULT 0.00,
  
  -- Healing Outcomes
  symptom_improvement JSONB NOT NULL DEFAULT '{}',
  
  -- Spiritual Growth
  spiritual_development JSONB NOT NULL DEFAULT '{}',
  
  -- Personalization Effectiveness
  personalization_success JSONB NOT NULL DEFAULT '{}',
  
  -- Recommendations
  next_step_recommendations TEXT[] DEFAULT '{}',
  graduation_readiness BOOLEAN DEFAULT false,
  
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(journey_id, user_id)
);

-- Community Groups Table
CREATE TABLE community_groups (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  group_type TEXT NOT NULL, -- 'peer_support', 'professional', 'cultural', etc.
  matching_criteria JSONB NOT NULL DEFAULT '{}',
  max_members INTEGER DEFAULT 20,
  current_members INTEGER DEFAULT 0,
  mentor_id UUID REFERENCES auth.users(id),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Community Memberships Table
CREATE TABLE community_memberships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  group_id UUID NOT NULL REFERENCES community_groups(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member', -- 'member', 'mentor', 'leader'
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  
  UNIQUE(user_id, group_id)
);

-- Indexes for performance
CREATE INDEX idx_journeys_user_id ON journeys(user_id);
CREATE INDEX idx_journeys_status ON journeys(status);
CREATE INDEX idx_journeys_type ON journeys(type);
CREATE INDEX idx_journeys_assessment_id ON journeys(assessment_id);
CREATE INDEX idx_journey_days_journey_id ON journey_days(journey_id);
CREATE INDEX idx_journey_days_day_number ON journey_days(day_number);
CREATE INDEX idx_daily_practices_journey_day_id ON daily_practices(journey_day_id);
CREATE INDEX idx_journey_progress_journey_id ON journey_progress(journey_id);
CREATE INDEX idx_journey_progress_user_id ON journey_progress(user_id);
CREATE INDEX idx_journey_progress_date ON journey_progress(date);
CREATE INDEX idx_journey_analytics_journey_id ON journey_analytics(journey_id);
CREATE INDEX idx_community_groups_type ON community_groups(group_type);
CREATE INDEX idx_community_memberships_user_id ON community_memberships(user_id);
CREATE INDEX idx_community_memberships_group_id ON community_memberships(group_id);

-- Row Level Security (RLS)
ALTER TABLE journeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE journey_days ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_practices ENABLE ROW LEVEL SECURITY;
ALTER TABLE journey_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE journey_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_memberships ENABLE ROW LEVEL SECURITY;

-- RLS Policies for journeys
CREATE POLICY "Users can view their own journeys" ON journeys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own journeys" ON journeys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own journeys" ON journeys
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for journey_days
CREATE POLICY "Users can view journey days for their journeys" ON journey_days
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM journeys 
      WHERE journeys.id = journey_days.journey_id 
      AND journeys.user_id = auth.uid()
    )
  );

-- RLS Policies for daily_practices
CREATE POLICY "Users can view practices for their journey days" ON daily_practices
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM journey_days 
      JOIN journeys ON journeys.id = journey_days.journey_id
      WHERE journey_days.id = daily_practices.journey_day_id 
      AND journeys.user_id = auth.uid()
    )
  );

-- RLS Policies for journey_progress
CREATE POLICY "Users can view their own progress" ON journey_progress
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own progress" ON journey_progress
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own progress" ON journey_progress
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for journey_analytics
CREATE POLICY "Users can view their own analytics" ON journey_analytics
  FOR SELECT USING (auth.uid() = user_id);

-- RLS Policies for community groups
CREATE POLICY "Users can view active community groups" ON community_groups
  FOR SELECT USING (is_active = true);

-- RLS Policies for community memberships
CREATE POLICY "Users can view their own memberships" ON community_memberships
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own memberships" ON community_memberships
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own memberships" ON community_memberships
  FOR UPDATE USING (auth.uid() = user_id);

-- Functions for journey management
CREATE OR REPLACE FUNCTION update_journey_progress()
RETURNS TRIGGER AS $$
BEGIN
  -- Update journey progress when daily progress is recorded
  UPDATE journeys 
  SET 
    completed_days = array_append(
      COALESCE(completed_days, '{}'), 
      NEW.day_number
    ),
    total_progress = (
      array_length(
        array_append(COALESCE(completed_days, '{}'), NEW.day_number), 
        1
      ) * 100.0 / duration
    ),
    current_day = GREATEST(current_day, NEW.day_number + 1),
    last_active_at = NOW(),
    updated_at = NOW()
  WHERE id = NEW.journey_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic progress updates
CREATE TRIGGER trigger_update_journey_progress
  AFTER INSERT ON journey_progress
  FOR EACH ROW
  EXECUTE FUNCTION update_journey_progress();

-- Function to check journey completion
CREATE OR REPLACE FUNCTION check_journey_completion()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if journey is completed
  IF NEW.total_progress >= 100 AND NEW.status != 'completed' THEN
    NEW.status = 'completed';
    NEW.completed_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for journey completion
CREATE TRIGGER trigger_check_journey_completion
  BEFORE UPDATE ON journeys
  FOR EACH ROW
  EXECUTE FUNCTION check_journey_completion();
