-- Enable PostgreSQL extensions
create extension if not exists "uuid-ossp";

-- User journeys table
create table public.user_journeys (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    journey_type text not null,
    focus_layers text[] not null,
    duration_days integer not null,
    start_date timestamp with time zone not null,
    end_date timestamp with time zone,
    status text not null default 'active',
    current_day integer not null default 1,
    modules_plan jsonb not null default '[]',
    last_activity_date timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_journey_type
    check (journey_type in ('7-day', '14-day', '40-day', 'custom')),

    constraint valid_status
    check (status in ('active', 'completed', 'paused', 'abandoned')),

    constraint valid_duration
    check (duration_days > 0 and duration_days <= 90)
);

-- Journey modules table
create table public.journey_modules (
    id uuid default uuid_generate_v4() primary key,
    journey_id uuid references public.user_journeys not null,
    user_id uuid references auth.users not null,
    title text not null,
    type text not null,
    day_number integer not null,
    content jsonb not null,
    focus_layer text not null,
    estimated_duration integer,
    sequence_order integer not null,
    prerequisites uuid[],
    is_advanced boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_focus_layer
    check (focus_layer in ('Qalb', 'Ruh', 'Nafs', 'Aql', 'Jism')),

    constraint valid_module_type
    check (type in ('dhikr', 'reflection', 'practice', 'knowledge', 'assessment'))
);

-- Module completions table
create table public.module_completions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    module_id uuid references public.journey_modules not null,
    status text not null,
    reflections text,
    challenges jsonb,
    completion_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_completion_status
    check (status in ('completed', 'skipped', 'in_progress'))
);

-- Daily check-ins table
create table public.daily_check_ins (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    mood text not null,
    dhikr_count integer default 0,
    prayer_consistency integer,
    notes text,
    check_in_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_mood
    check (mood in ('excellent', 'good', 'neutral', 'challenging', 'difficult')),

    constraint valid_prayer_consistency
    check (prayer_consistency >= 0 and prayer_consistency <= 5)
);

-- User streaks table
create table public.user_streaks (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null unique,
    current_streak integer not null default 0,
    longest_streak integer not null default 0,
    last_check_in timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_streaks
    check (current_streak >= 0 and longest_streak >= 0)
);

-- Achievements table
create table public.achievements (
    id uuid default uuid_generate_v4() primary key,
    name text not null unique,
    description text not null,
    category text not null,
    requirement_type text not null,
    requirement_value integer not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- User achievements table
create table public.user_achievements (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    journey_id uuid references public.user_journeys not null,
    achievement_type text not null,
    earned_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint unique_user_achievement
    unique (user_id, achievement_type)
);

-- Add RLS (Row Level Security) policies
alter table public.user_journeys enable row level security;
alter table public.journey_modules enable row level security;
alter table public.module_completions enable row level security;
alter table public.daily_check_ins enable row level security;
alter table public.user_streaks enable row level security;
alter table public.user_achievements enable row level security;

-- Policies for user_journeys
create policy "Users can view their own journeys"
    on public.user_journeys for select
    using (auth.uid() = user_id);

create policy "Users can create their own journeys"
    on public.user_journeys for insert
    with check (auth.uid() = user_id);

create policy "Users can update their own journeys"
    on public.user_journeys for update
    using (auth.uid() = user_id);

-- Policies for journey_modules
create policy "Users can view their own modules"
    on public.journey_modules for select
    using (auth.uid() = user_id);

create policy "Users can create their own modules"
    on public.journey_modules for insert
    with check (auth.uid() = user_id);

-- Policies for module_completions
create policy "Users can view their own completions"
    on public.module_completions for select
    using (auth.uid() = user_id);

create policy "Users can create their own completions"
    on public.module_completions for insert
    with check (auth.uid() = user_id);

-- Policies for daily_check_ins
create policy "Users can view their own check-ins"
    on public.daily_check_ins for select
    using (auth.uid() = user_id);

create policy "Users can create their own check-ins"
    on public.daily_check_ins for insert
    with check (auth.uid() = user_id);

-- Policies for user_streaks
create policy "Users can view their own streaks"
    on public.user_streaks for select
    using (auth.uid() = user_id);

create policy "Users can update their own streaks"
    on public.user_streaks for update
    using (auth.uid() = user_id);

-- Policies for user_achievements
create policy "Users can view their own achievements"
    on public.user_achievements for select
    using (auth.uid() = user_id);

create policy "Users can earn achievements"
    on public.user_achievements for insert
    with check (auth.uid() = user_id);

-- Create indexes for better query performance
create index idx_user_journeys_user_id on public.user_journeys(user_id);
create index idx_journey_modules_journey_id on public.journey_modules(journey_id);
create index idx_module_completions_user_id on public.module_completions(user_id);
create index idx_daily_check_ins_user_id on public.daily_check_ins(user_id);
create index idx_user_achievements_user_id on public.user_achievements(user_id);