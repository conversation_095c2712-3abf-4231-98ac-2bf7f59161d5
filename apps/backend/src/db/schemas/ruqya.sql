-- Enable PostgreSQL extensions
create extension if not exists "uuid-ossp";

-- Ruqya assessment questions table
create table public.ruqya_assessment_questions (
    id uuid default uuid_generate_v4() primary key,
    question_text text not null,
    category text not null,
    sequence_order integer not null,
    response_type text not null,
    options jsonb,
    status text default 'active',
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_response_type
    check (response_type in ('scale', 'multiple_choice', 'text', 'boolean'))
);

-- Ruqya assessments table
create table public.ruqya_assessments (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    responses jsonb not null,
    additional_notes text,
    emergency_contact jsonb,
    assessment_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Ruqya analysis table
create table public.ruqya_analysis (
    id uuid default uuid_generate_v4() primary key,
    assessment_id uuid references public.ruqya_assessments not null,
    user_id uuid references auth.users not null,
    risk_level text not null,
    recommended_practices text[] not null,
    healing_focus text[] not null,
    spiritual_insights jsonb not null,
    analysis_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_risk_level
    check (risk_level in ('mild', 'moderate', 'severe', 'critical'))
);

-- Ruqya treatment plans table
create table public.ruqya_treatment_plans (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    risk_level text not null,
    healing_focus text[] not null,
    duration_weeks integer not null,
    start_date timestamp with time zone not null,
    end_date timestamp with time zone,
    status text not null default 'active',
    last_modified timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_status
    check (status in ('active', 'completed', 'paused', 'discontinued'))
);

-- Ruqya practices table
create table public.ruqya_practices (
    id uuid default uuid_generate_v4() primary key,
    plan_id uuid references public.ruqya_treatment_plans not null,
    user_id uuid references auth.users not null,
    type text not null,
    title text not null,
    description text not null,
    instructions text not null,
    duration_minutes integer,
    frequency text not null,
    prerequisites uuid[],
    contraindications text[],
    is_milestone boolean default false,
    sequence_order integer not null,
    arabic_content jsonb,
    audio_url text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_practice_type
    check (type in ('dua', 'dhikr', 'quran', 'meditation', 'physical', 'dietary'))
);

-- Ruqya practice progress table
create table public.ruqya_practice_progress (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    practice_id uuid references public.ruqya_practices not null,
    completed boolean not null default false,
    effectiveness integer,
    notes text,
    completion_date timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_effectiveness
    check (effectiveness >= 1 and effectiveness <= 5)
);

-- Ruqya duas table
create table public.ruqya_duas (
    id uuid default uuid_generate_v4() primary key,
    title text not null,
    arabic_text text not null,
    translation text not null,
    transliteration text,
    category text not null,
    benefits text[],
    source text,
    audio_url text,
    status text default 'active',
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- User favorite duas table
create table public.user_favorite_duas (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    dua_id uuid references public.ruqya_duas not null,
    category text not null,
    notes text,
    saved_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint unique_user_dua
    unique (user_id, dua_id)
);

-- Ruqya resources table
create table public.ruqya_resources (
    id uuid default uuid_generate_v4() primary key,
    title text not null,
    description text not null,
    category text not null,
    content_type text not null,
    content_url text not null,
    prerequisites text[],
    status text default 'active',
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_content_type
    check (content_type in ('article', 'video', 'audio', 'pdf', 'infographic'))
);

-- Ruqya concerns table
create table public.ruqya_concerns (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    concern_type text not null,
    description text not null,
    severity integer not null,
    is_emergency boolean not null default false,
    emergency_assessment jsonb,
    needs_immediate_attention boolean default false,
    report_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_severity
    check (severity >= 1 and severity <= 5)
);

-- Ruqya consultations table
create table public.ruqya_consultations (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    preferred_date timestamp with time zone not null,
    consultation_type text not null,
    primary_concern text not null,
    previous_treatment boolean,
    status text not null default 'pending',
    practitioner_id uuid,
    scheduled_date timestamp with time zone,
    notes text,
    request_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_consultation_type
    check (consultation_type in ('general', 'emergency', 'follow-up')),

    constraint valid_status
    check (status in ('pending', 'scheduled', 'completed', 'cancelled'))
);

-- Ruqya safety guidelines table
create table public.ruqya_safety_guidelines (
    id uuid default uuid_generate_v4() primary key,
    title text not null,
    description text not null,
    instructions text[] not null,
    priority integer not null,
    status text default 'active',
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Ruqya user precautions table
create table public.ruqya_user_precautions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    plan_id uuid references public.ruqya_treatment_plans not null,
    precautions jsonb not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Ruqya daily protection table
create table public.ruqya_daily_protection (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    practice_type text not null,
    completed boolean not null,
    completion_time timestamp with time zone not null,
    submission_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_practice_type
    check (practice_type in ('morning_adhkar', 'evening_adhkar', 'general_protection'))
);

-- User protection streaks table
create table public.user_protection_streaks (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null unique,
    current_streak integer not null default 0,
    longest_streak integer not null default 0,
    total_completions integer not null default 0,
    last_completion timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Ruqya effectiveness assessments table
create table public.ruqya_effectiveness_assessments (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    plan_id uuid references public.ruqya_treatment_plans not null,
    metrics jsonb not null,
    assessment_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Add RLS (Row Level Security) policies
alter table public.ruqya_assessments enable row level security;
alter table public.ruqya_analysis enable row level security;
alter table public.ruqya_treatment_plans enable row level security;
alter table public.ruqya_practices enable row level security;
alter table public.ruqya_practice_progress enable row level security;
alter table public.user_favorite_duas enable row level security;
alter table public.ruqya_concerns enable row level security;
alter table public.ruqya_consultations enable row level security;
alter table public.ruqya_user_precautions enable row level security;
alter table public.ruqya_daily_protection enable row level security;
alter table public.user_protection_streaks enable row level security;
alter table public.ruqya_effectiveness_assessments enable row level security;

-- Create policies for user-specific data access
create policy "Users can view their own assessments"
    on public.ruqya_assessments for select
    using (auth.uid() = user_id);

create policy "Users can create their own assessments"
    on public.ruqya_assessments for insert
    with check (auth.uid() = user_id);

-- Similar policies for other tables...

-- Create indexes for better query performance
create index idx_ruqya_assessments_user_id on public.ruqya_assessments(user_id);
create index idx_ruqya_treatment_plans_user_id on public.ruqya_treatment_plans(user_id);
create index idx_ruqya_practices_plan_id on public.ruqya_practices(plan_id);
create index idx_ruqya_practice_progress_user_id on public.ruqya_practice_progress(user_id);
create index idx_ruqya_concerns_user_id on public.ruqya_concerns(user_id);
create index idx_ruqya_consultations_user_id on public.ruqya_consultations(user_id);
create index idx_ruqya_daily_protection_user_id on public.ruqya_daily_protection(user_id);