-- Enable PostgreSQL extensions
create extension if not exists "uuid-ossp";

-- Content items table
create table public.content_items (
    id uuid default uuid_generate_v4() primary key,
    title text not null,
    description text not null,
    content_type text not null,
    healing_layer text not null,
    category text not null,
    storage_path text not null,
    duration integer,
    access_level text not null default 'basic',
    requires_subscription boolean default false,
    series_id uuid references public.content_series(id),
    part_number integer,
    focus_areas text[] not null default '{}',
    view_count integer default 0,
    completion_count integer default 0,
    average_rating numeric(3,2),
    status text not null default 'draft',
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_content_type
    check (content_type in ('article', 'video', 'audio', 'pdf', 'infographic')),

    constraint valid_healing_layer
    check (healing_layer in ('Qalb', 'Ruh', 'Nafs', 'Aql', 'Jism')),

    constraint valid_access_level
    check (access_level in ('basic', 'standard', 'premium')),

    constraint valid_status
    check (status in ('draft', 'review', 'published', 'archived'))
);

-- Content metadata table
create table public.content_metadata (
    id uuid default uuid_generate_v4() primary key,
    content_id uuid references public.content_items not null,
    language text not null default 'en',
    author text,
    source text,
    references text[],
    keywords text[],
    difficulty_level text,
    estimated_duration integer,
    prerequisites uuid[],
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_difficulty
    check (difficulty_level in ('beginner', 'intermediate', 'advanced'))
);

-- Content tags table
create table public.content_tags (
    id uuid default uuid_generate_v4() primary key,
    content_id uuid references public.content_items not null,
    tag_name text not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint unique_content_tag
    unique (content_id, tag_name)
);

-- Content series table
create table public.content_series (
    id uuid default uuid_generate_v4() primary key,
    title text not null,
    description text not null,
    total_parts integer not null,
    category text not null,
    healing_layer text not null,
    difficulty_level text not null,
    prerequisites text[],
    status text not null default 'active',
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_difficulty
    check (difficulty_level in ('beginner', 'intermediate', 'advanced')),

    constraint valid_status
    check (status in ('active', 'completed', 'archived'))
);

-- Content interactions table
create table public.content_interactions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    content_id uuid references public.content_items not null,
    interaction_type text not null,
    duration integer,
    progress integer,
    rating integer,
    interaction_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_interaction_type
    check (interaction_type in ('view', 'complete', 'bookmark', 'share')),

    constraint valid_rating
    check (rating >= 1 and rating <= 5),

    constraint valid_progress
    check (progress >= 0 and progress <= 100)
);

-- Content progress table
create table public.content_progress (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    content_id uuid references public.content_items not null,
    progress_percentage integer not null,
    last_accessed timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_progress
    check (progress_percentage >= 0 and progress_percentage <= 100),

    constraint unique_user_content_progress
    unique (user_id, content_id)
);

-- Series progress table
create table public.series_progress (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    series_id uuid references public.content_series not null,
    completed_parts integer not null default 0,
    is_completed boolean not null default false,
    last_updated timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint unique_user_series_progress
    unique (user_id, series_id)
);

-- Content completions table
create table public.content_completions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    content_id uuid references public.content_items not null,
    completion_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint unique_user_content_completion
    unique (user_id, content_id)
);

-- Series completions table
create table public.series_completions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    series_id uuid references public.content_series not null,
    completion_date timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint unique_user_series_completion
    unique (user_id, series_id)
);

-- Content issues table
create table public.content_issues (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    content_id uuid references public.content_items not null,
    issue_type text not null,
    description text not null,
    status text not null default 'pending',
    resolution text,
    report_date timestamp with time zone not null,
    resolved_date timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_issue_type
    check (issue_type in ('inappropriate', 'incorrect', 'technical', 'other')),

    constraint valid_status
    check (status in ('pending', 'investigating', 'resolved', 'rejected'))
);

-- Add RLS (Row Level Security) policies
alter table public.content_items enable row level security;
alter table public.content_metadata enable row level security;
alter table public.content_tags enable row level security;
alter table public.content_series enable row level security;
alter table public.content_interactions enable row level security;
alter table public.content_progress enable row level security;
alter table public.series_progress enable row level security;
alter table public.content_completions enable row level security;
alter table public.series_completions enable row level security;
alter table public.content_issues enable row level security;

-- Policies for content_items
create policy "Published content is viewable by all users"
    on public.content_items for select
    using (status = 'published');

-- Policies for content_interactions
create policy "Users can view their own interactions"
    on public.content_interactions for select
    using (auth.uid() = user_id);

create policy "Users can create their own interactions"
    on public.content_interactions for insert
    with check (auth.uid() = user_id);

-- Policies for content_progress
create policy "Users can view their own progress"
    on public.content_progress for select
    using (auth.uid() = user_id);

create policy "Users can update their own progress"
    on public.content_progress for update
    using (auth.uid() = user_id);

-- Create indexes for better query performance
create index idx_content_items_type on public.content_items(content_type);
create index idx_content_items_layer on public.content_items(healing_layer);
create index idx_content_items_category on public.content_items(category);
create index idx_content_interactions_user on public.content_interactions(user_id);
create index idx_content_progress_user on public.content_progress(user_id);
create index idx_series_progress_user on public.series_progress(user_id);

-- Function to update content metrics
create or replace function update_content_metrics()
returns trigger as $$
begin
    if NEW.interaction_type = 'view' then
        update content_items
        set view_count = view_count + 1
        where id = NEW.content_id;
    elsif NEW.interaction_type = 'complete' then
        update content_items
        set completion_count = completion_count + 1
        where id = NEW.content_id;
    end if;
    return NEW;
end;
$$ language plpgsql;

-- Trigger for updating content metrics
create trigger content_metrics_update
    after insert on content_interactions
    for each row
    execute function update_content_metrics();