-- Enable PostgreSQL extensions
create extension if not exists "uuid-ossp";
create extension if not exists "timescaledb"; -- For time-series data

-- Health assessments table
create table public.health_assessments (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    metrics jsonb not null,
    assessment_date timestamp with time zone not null,
    notes text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- User interactions table
create table public.user_interactions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    interaction_type text not null,
    layer text,
    engagement_score integer,
    duration_seconds integer,
    interaction_date timestamp with time zone not null,
    metadata jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_interaction_type
    check (interaction_type in ('practice', 'reflection', 'dua', 'content', 'assessment')),

    constraint valid_layer
    check (layer in ('Qalb', 'Ruh', 'Nafs', 'Aql', 'Jism')),

    constraint valid_engagement_score
    check (engagement_score >= 0 and engagement_score <= 100)
);

-- Daily practices table
create table public.daily_practices (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    practice_type text not null,
    layer text not null,
    practice_date date not null,
    completion_status text not null,
    duration_minutes integer,
    effectiveness_rating integer,
    notes text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_completion_status
    check (completion_status in ('completed', 'partial', 'missed')),

    constraint valid_effectiveness_rating
    check (effectiveness_rating >= 1 and effectiveness_rating <= 5)
);

-- Journey milestones table
create table public.journey_milestones (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    journey_id uuid references public.user_journeys not null,
    name text not null,
    description text not null,
    milestone_type text not null,
    sequence_order integer not null,
    requirements jsonb not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Milestone completions table
create table public.milestone_completions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    milestone_id uuid references public.journey_milestones not null,
    completion_date timestamp with time zone not null,
    reflection text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint unique_user_milestone
    unique (user_id, milestone_id)
);

-- Analytics metrics table (time-series)
create table public.analytics_metrics (
    time timestamp with time zone not null,
    user_id uuid references auth.users not null,
    metric_name text not null,
    metric_value numeric not null,
    metadata jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create hypertable for time-series data
select create_hypertable('analytics_metrics', 'time');

-- Analytics webhook subscriptions table
create table public.analytics_webhooks (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    url text not null,
    events text[] not null,
    status text not null default 'active',
    last_triggered timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_status
    check (status in ('active', 'inactive', 'error'))
);

-- Analytics exports table
create table public.analytics_exports (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    export_type text not null,
    timeframe text not null,
    status text not null default 'pending',
    file_url text,
    export_date timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_export_type
    check (export_type in ('csv', 'pdf', 'json')),

    constraint valid_status
    check (status in ('pending', 'processing', 'completed', 'error'))
);

-- Add RLS (Row Level Security) policies
alter table public.health_assessments enable row level security;
alter table public.user_interactions enable row level security;
alter table public.daily_practices enable row level security;
alter table public.journey_milestones enable row level security;
alter table public.milestone_completions enable row level security;
alter table public.analytics_metrics enable row level security;
alter table public.analytics_webhooks enable row level security;
alter table public.analytics_exports enable row level security;

-- Policies for health_assessments
create policy "Users can view their own health assessments"
    on public.health_assessments for select
    using (auth.uid() = user_id);

create policy "Users can create their own health assessments"
    on public.health_assessments for insert
    with check (auth.uid() = user_id);

-- Policies for user_interactions
create policy "Users can view their own interactions"
    on public.user_interactions for select
    using (auth.uid() = user_id);

create policy "Users can create their own interactions"
    on public.user_interactions for insert
    with check (auth.uid() = user_id);

-- Policies for daily_practices
create policy "Users can view their own practices"
    on public.daily_practices for select
    using (auth.uid() = user_id);

create policy "Users can manage their own practices"
    on public.daily_practices for all
    using (auth.uid() = user_id);

-- Create indexes for better query performance
create index idx_health_assessments_user_date on public.health_assessments(user_id, assessment_date);
create index idx_user_interactions_user_date on public.user_interactions(user_id, interaction_date);
create index idx_daily_practices_user_date on public.daily_practices(user_id, practice_date);
create index idx_analytics_metrics_user_metric on public.analytics_metrics(user_id, metric_name, time);

-- Functions for analytics calculations

-- Function to calculate streak
create or replace function calculate_streak(user_id uuid)
returns table (current_streak integer, longest_streak integer)
language plpgsql as $$
declare
    current_streak integer := 0;
    longest_streak integer := 0;
    last_practice_date date := null;
begin
    -- Get practice dates ordered
    for last_practice_date in 
        select practice_date
        from daily_practices
        where daily_practices.user_id = calculate_streak.user_id
        and completion_status = 'completed'
        order by practice_date desc
    loop
        if last_practice_date is null then
            current_streak := 0;
        elsif last_practice_date = current_date - current_streak then
            current_streak := current_streak + 1;
        else
            exit;
        end if;
    end loop;

    -- Get longest streak
    select max(streak_length)
    into longest_streak
    from (
        select 
            count(*) as streak_length
        from (
            select 
                practice_date,
                row_number() over (order by practice_date) as rn
            from (
                select distinct practice_date
                from daily_practices
                where daily_practices.user_id = calculate_streak.user_id
                and completion_status = 'completed'
            ) t
        ) numbered
        group by date_trunc('day', practice_date) - (interval '1 day' * rn)
    ) streaks;

    return query select current_streak, longest_streak;
end;
$$;