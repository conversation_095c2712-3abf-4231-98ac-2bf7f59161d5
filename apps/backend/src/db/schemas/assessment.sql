-- Feature 1: Understanding Your Inner Landscape - Database Schema
-- Spiritual assessment and diagnosis system

-- Enable PostgreSQL extensions
create extension if not exists "uuid-ossp";

-- Assessment sessions table
create table public.assessment_sessions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    user_profile jsonb not null,

    -- Session metadata
    started_at timestamp with time zone not null,
    completed_at timestamp with time zone,
    abandoned_at timestamp with time zone,
    abandonment_reason text,

    -- Progress tracking
    current_step text not null default 'welcome',
    total_steps integer not null default 10,

    -- Assessment data
    physical_experiences jsonb default '{}',
    emotional_experiences jsonb default '{}',
    mental_experiences jsonb default '{}',
    spiritual_experiences jsonb default '{}',
    reflections jsonb default '{}',

    -- Time tracking
    time_spent_per_step jsonb default '{}',
    total_time_spent integer default 0,

    -- Session data (full session object)
    session_data jsonb,

    -- Device and context
    device_info jsonb,

    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_current_step
    check (current_step in (
        'welcome',
        'layer_education',
        'physical_experiences',
        'emotional_experiences',
        'mental_experiences',
        'spiritual_experiences',
        'reflections',
        'analysis',
        'diagnosis_delivery',
        'next_steps',
        'complete',
        'crisis_support'
    ))
);

-- Spiritual diagnoses table
create table public.spiritual_diagnoses (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    assessment_id uuid references public.assessment_sessions(id) not null,

    -- Core diagnosis data
    diagnosis_data jsonb not null,

    -- Quick access fields
    primary_layer text not null,
    secondary_layers text[],
    overall_severity text not null,
    crisis_level text not null default 'none',
    confidence numeric(3,2) not null default 0.8,

    -- Recommendations
    recommended_journey_type text,
    estimated_healing_duration integer, -- days
    next_steps text[],

    -- User feedback
    user_feedback jsonb,

    generated_at timestamp with time zone not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_primary_layer
    check (primary_layer in ('jism', 'nafs', 'aql', 'qalb', 'ruh')),

    constraint valid_overall_severity
    check (overall_severity in ('mild', 'moderate', 'severe', 'critical')),

    constraint valid_crisis_level
    check (crisis_level in ('none', 'low', 'moderate', 'high', 'critical')),

    constraint valid_confidence
    check (confidence >= 0 and confidence <= 1)
);

-- Layer analyses table (detailed breakdown)
create table public.layer_analyses (
    id uuid default uuid_generate_v4() primary key,
    diagnosis_id uuid references public.spiritual_diagnoses(id) not null,

    -- Layer information
    layer text not null,
    layer_name text not null,
    impact_score integer not null default 0,
    priority text not null,

    -- Analysis details
    affected_symptoms text[],
    insights text[],
    recommendations text[],
    islamic_context text,

    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_layer
    check (layer in ('jism', 'nafs', 'aql', 'qalb', 'ruh')),

    constraint valid_priority
    check (priority in ('primary', 'secondary', 'tertiary')),

    constraint valid_impact_score
    check (impact_score >= 0 and impact_score <= 100)
);

-- Assessment questions library
create table public.assessment_questions (
    id uuid default uuid_generate_v4() primary key,

    -- Question metadata
    category text not null,
    layer text not null,
    step text not null,

    -- Question content
    title text not null,
    description text,
    reflection_prompt text,
    reflection_required boolean default false,

    -- Configuration
    allow_multiple_selection boolean default true,
    intensity_scale boolean default true,
    custom_input_allowed boolean default true,

    -- Adaptive logic
    show_conditions jsonb,

    -- Question order and grouping
    display_order integer default 0,
    is_active boolean default true,

    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_category
    check (category in ('physical', 'emotional', 'mental', 'spiritual')),

    constraint valid_layer
    check (layer in ('jism', 'nafs', 'aql', 'qalb', 'ruh'))
);

-- Assessment questions library (Updated to match Prisma Model for Feature 1)
create table public.assessment_questions (
    id uuid default uuid_generate_v4() primary key,
    step_key text not null, -- Key identifying the assessment step
    category text not null, -- Soul layer (e.g., "jism", "nafs", "aql", "qalb", "ruh") or "reflection"
    "order" integer not null, -- Order of question within the step/category (quoted because "order" is a keyword)
    text text not null, -- The question text or prompt
    description text, -- Optional further explanation
    question_type text not null, -- E.g., "symptom_multi_choice", "reflection_text_input"
    is_adaptive boolean default false,
    is_enabled boolean default true,
    required boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
    -- Removed specific constraints like valid_category, valid_layer as they are more for app logic or Prisma enum
    -- @@index([step, order]) and @@index([category]) from Prisma are represented by specific index creations later
);

-- Assessment question choices table (New table based on Prisma Model for Feature 1)
create table public.assessment_question_choices (
    id uuid default uuid_generate_v4() primary key,
    question_id uuid references public.assessment_questions(id) on delete cascade not null,
    text text not null, -- Display text for the choice
    value text not null, -- The actual value stored if chosen
    "order" integer not null, -- Order of this choice within the question (quoted)
    description text, -- Optional further explanation for the choice
    icon_name text, -- Optional icon name
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
    -- @@index([questionId, order]) from Prisma is represented by specific index creation later
);

-- Assessment analytics table
create table public.assessment_analytics (
    id uuid default uuid_generate_v4() primary key,
    date date not null,

    -- Session statistics
    total_sessions integer not null default 0,
    completed_sessions integer not null default 0,
    abandoned_sessions integer not null default 0,

    -- Diagnosis statistics
    total_diagnoses integer not null default 0,
    crisis_detections integer not null default 0,

    -- Layer distribution
    primary_layer_distribution jsonb,
    severity_distribution jsonb,

    -- Performance metrics
    average_completion_time integer, -- seconds
    average_accuracy_rating numeric(3,2),
    average_helpfulness_rating numeric(3,2),

    -- Step analytics
    step_completion_rates jsonb,
    step_average_times jsonb,

    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    unique(date)
);

-- Indexes for performance
create index idx_assessment_sessions_user_id on public.assessment_sessions(user_id);
create index idx_assessment_sessions_started_at on public.assessment_sessions(started_at);
create index idx_assessment_sessions_current_step on public.assessment_sessions(current_step);
create index idx_assessment_sessions_completed_at on public.assessment_sessions(completed_at);

create index idx_spiritual_diagnoses_user_id on public.spiritual_diagnoses(user_id);
create index idx_spiritual_diagnoses_assessment_id on public.spiritual_diagnoses(assessment_id);
create index idx_spiritual_diagnoses_primary_layer on public.spiritual_diagnoses(primary_layer);
create index idx_spiritual_diagnoses_overall_severity on public.spiritual_diagnoses(overall_severity);
create index idx_spiritual_diagnoses_crisis_level on public.spiritual_diagnoses(crisis_level);
create index idx_spiritual_diagnoses_generated_at on public.spiritual_diagnoses(generated_at);

create index idx_layer_analyses_diagnosis_id on public.layer_analyses(diagnosis_id);
create index idx_layer_analyses_layer on public.layer_analyses(layer);
create index idx_layer_analyses_priority on public.layer_analyses(priority);

-- Updated Indexes for assessment_questions
create index idx_assessment_questions_step_key_order on public.assessment_questions(step_key, "order");
create index idx_assessment_questions_category on public.assessment_questions(category);
create index idx_assessment_questions_is_enabled on public.assessment_questions(is_enabled);

-- Indexes for new assessment_question_choices table
create index idx_assessment_question_choices_question_id_order on public.assessment_question_choices(question_id, "order");

create index idx_assessment_analytics_date on public.assessment_analytics(date);

-- Row Level Security (RLS) policies
alter table public.assessment_sessions enable row level security;
alter table public.spiritual_diagnoses enable row level security;
alter table public.layer_analyses enable row level security;

-- Users can only access their own assessment sessions
create policy "Users can view own assessment sessions" on public.assessment_sessions
    for select using (auth.uid() = user_id);

create policy "Users can insert own assessment sessions" on public.assessment_sessions
    for insert with check (auth.uid() = user_id);

create policy "Users can update own assessment sessions" on public.assessment_sessions
    for update using (auth.uid() = user_id);

-- Users can only access their own diagnoses
create policy "Users can view own diagnoses" on public.spiritual_diagnoses
    for select using (auth.uid() = user_id);

create policy "Users can insert own diagnoses" on public.spiritual_diagnoses
    for insert with check (auth.uid() = user_id);

create policy "Users can update own diagnoses" on public.spiritual_diagnoses
    for update using (auth.uid() = user_id);

-- Users can view layer analyses for their diagnoses
create policy "Users can view own layer analyses" on public.layer_analyses
    for select using (
        auth.uid() in (
            select user_id from public.spiritual_diagnoses
            where id = layer_analyses.diagnosis_id
        )
    );

-- Public read access to questions and choices (no user-specific data)
create policy "Public read access to assessment questions" on public.assessment_questions
    for select using (is_enabled = true); -- Changed from is_active to is_enabled

create policy "Public read access to assessment question choices" on public.assessment_question_choices
    for select using (true); -- Assuming choices are public if the question is accessible

-- Functions for automatic timestamp updates
create or replace function public.handle_updated_at()
returns trigger as $$
begin
    new.updated_at = timezone('utc'::text, now());
    return new;
end;
$$ language plpgsql;

-- Triggers for automatic timestamp updates
create trigger handle_assessment_sessions_updated_at
    before update on public.assessment_sessions
    for each row execute procedure public.handle_updated_at();

create trigger handle_spiritual_diagnoses_updated_at
    before update on public.spiritual_diagnoses
    for each row execute procedure public.handle_updated_at();

create trigger handle_assessment_questions_updated_at
    before update on public.assessment_questions
    for each row execute procedure public.handle_updated_at();

create trigger handle_assessment_question_choices_updated_at
    before update on public.assessment_question_choices
    for each row execute procedure public.handle_updated_at();
