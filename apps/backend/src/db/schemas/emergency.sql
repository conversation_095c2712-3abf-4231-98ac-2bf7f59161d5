-- Emergency System Database Schema
-- Complete crisis management and emergency support system

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Emergency Sessions Table (Sakina Mode)
CREATE TABLE emergency_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  trigger_type TEXT NOT NULL DEFAULT 'manual',
  current_symptoms TEXT[] DEFAULT '{}',
  start_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  end_time TIMESTAMPTZ,
  status TEXT NOT NULL DEFAULT 'active',
  recommended_actions TEXT[] DEFAULT '{}',
  estimated_duration INTEGER DEFAULT 15, -- minutes
  effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
  feedback TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT valid_trigger_type CHECK (trigger_type IN ('manual', 'automatic', 'scheduled')),
  CONSTRAINT valid_status CHECK (status IN ('active', 'completed', 'interrupted'))
);

-- Crisis Escalations Table
CREATE TABLE crisis_escalations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  escalation_id TEXT UNIQUE NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  severity TEXT NOT NULL,
  indicators TEXT[] NOT NULL DEFAULT '{}',
  context JSONB DEFAULT '{}',
  immediate_risk BOOLEAN DEFAULT false,
  status TEXT NOT NULL DEFAULT 'active',
  actions_taken TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT valid_severity CHECK (severity IN ('low', 'moderate', 'high', 'critical')),
  CONSTRAINT valid_escalation_status CHECK (status IN ('active', 'resolved', 'escalated'))
);

-- Crisis Follow-ups Table
CREATE TABLE crisis_followups (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  escalation_id UUID NOT NULL REFERENCES crisis_escalations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL,
  notes TEXT,
  additional_support BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT valid_followup_status CHECK (status IN ('improved', 'same', 'worse'))
);

-- Emergency Content Tables
CREATE TABLE breathing_exercises (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  instructions TEXT[] NOT NULL DEFAULT '{}',
  duration INTEGER NOT NULL, -- seconds
  intensity TEXT NOT NULL DEFAULT 'medium',
  audio_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT valid_intensity CHECK (intensity IN ('low', 'medium', 'high'))
);

CREATE TABLE dhikr_content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  arabic TEXT NOT NULL,
  transliteration TEXT NOT NULL,
  translation TEXT NOT NULL,
  count INTEGER DEFAULT 1,
  category TEXT NOT NULL DEFAULT 'general',
  priority INTEGER DEFAULT 0,
  audio_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT valid_category CHECK (category IN ('emergency', 'general', 'protection', 'healing'))
);

CREATE TABLE ruqyah_verses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  surah TEXT NOT NULL,
  ayah INTEGER NOT NULL,
  arabic TEXT NOT NULL,
  translation TEXT NOT NULL,
  category TEXT NOT NULL DEFAULT 'general',
  sequence_order INTEGER DEFAULT 0,
  audio_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT valid_ruqyah_category CHECK (category IN ('emergency', 'general', 'protection', 'healing'))
);

CREATE TABLE emergency_helplines (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  phone TEXT NOT NULL,
  description TEXT,
  availability TEXT DEFAULT '24/7',
  country TEXT NOT NULL DEFAULT 'US',
  is_islamic BOOLEAN DEFAULT false,
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Journal Entries Table (for saving emergency sessions)
CREATE TABLE journal_entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  entry_type TEXT NOT NULL DEFAULT 'general',
  tags TEXT[] DEFAULT '{}',
  related_data JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT valid_entry_type CHECK (entry_type IN ('general', 'emergency_session', 'journey', 'reflection'))
);

-- Indexes for performance
CREATE INDEX idx_emergency_sessions_user_id ON emergency_sessions(user_id);
CREATE INDEX idx_emergency_sessions_status ON emergency_sessions(status);
CREATE INDEX idx_emergency_sessions_start_time ON emergency_sessions(start_time);

CREATE INDEX idx_crisis_escalations_user_id ON crisis_escalations(user_id);
CREATE INDEX idx_crisis_escalations_escalation_id ON crisis_escalations(escalation_id);
CREATE INDEX idx_crisis_escalations_severity ON crisis_escalations(severity);
CREATE INDEX idx_crisis_escalations_status ON crisis_escalations(status);

CREATE INDEX idx_crisis_followups_escalation_id ON crisis_followups(escalation_id);
CREATE INDEX idx_crisis_followups_user_id ON crisis_followups(user_id);

CREATE INDEX idx_breathing_exercises_intensity ON breathing_exercises(intensity);
CREATE INDEX idx_breathing_exercises_is_active ON breathing_exercises(is_active);

CREATE INDEX idx_dhikr_content_category ON dhikr_content(category);
CREATE INDEX idx_dhikr_content_priority ON dhikr_content(priority);
CREATE INDEX idx_dhikr_content_is_active ON dhikr_content(is_active);

CREATE INDEX idx_ruqyah_verses_category ON ruqyah_verses(category);
CREATE INDEX idx_ruqyah_verses_sequence_order ON ruqyah_verses(sequence_order);
CREATE INDEX idx_ruqyah_verses_is_active ON ruqyah_verses(is_active);

CREATE INDEX idx_emergency_helplines_country ON emergency_helplines(country);
CREATE INDEX idx_emergency_helplines_is_active ON emergency_helplines(is_active);

CREATE INDEX idx_journal_entries_user_id ON journal_entries(user_id);
CREATE INDEX idx_journal_entries_entry_type ON journal_entries(entry_type);
CREATE INDEX idx_journal_entries_created_at ON journal_entries(created_at);

-- Row Level Security (RLS)
ALTER TABLE emergency_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE crisis_escalations ENABLE ROW LEVEL SECURITY;
ALTER TABLE crisis_followups ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_entries ENABLE ROW LEVEL SECURITY;

-- RLS Policies for emergency_sessions
CREATE POLICY "Users can view their own emergency sessions" ON emergency_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own emergency sessions" ON emergency_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own emergency sessions" ON emergency_sessions
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for crisis_escalations
CREATE POLICY "Users can view their own crisis escalations" ON crisis_escalations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can create crisis escalations" ON crisis_escalations
  FOR INSERT WITH CHECK (true); -- Service account can insert

-- RLS Policies for crisis_followups
CREATE POLICY "Users can view their own crisis followups" ON crisis_followups
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own crisis followups" ON crisis_followups
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for journal_entries
CREATE POLICY "Users can view their own journal entries" ON journal_entries
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own journal entries" ON journal_entries
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own journal entries" ON journal_entries
  FOR UPDATE USING (auth.uid() = user_id);

-- Public read access for content tables (no RLS needed)
-- breathing_exercises, dhikr_content, ruqyah_verses, emergency_helplines

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_emergency_sessions_updated_at
  BEFORE UPDATE ON emergency_sessions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_crisis_escalations_updated_at
  BEFORE UPDATE ON crisis_escalations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_journal_entries_updated_at
  BEFORE UPDATE ON journal_entries
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default emergency content
INSERT INTO breathing_exercises (name, instructions, duration, intensity) VALUES
('4-7-8 Breathing', ARRAY[
  'Sit comfortably with your back straight',
  'Place the tip of your tongue against the ridge behind your upper teeth',
  'Exhale completely through your mouth',
  'Close your mouth and inhale through your nose for 4 counts',
  'Hold your breath for 7 counts',
  'Exhale through your mouth for 8 counts',
  'Repeat this cycle 3-4 times'
], 300, 'medium');

INSERT INTO dhikr_content (arabic, transliteration, translation, count, category, priority) VALUES
('لا إله إلا الله', 'La ilaha illa Allah', 'There is no god but Allah', 100, 'emergency', 1),
('استغفر الله', 'Astaghfirullah', 'I seek forgiveness from Allah', 33, 'emergency', 2),
('حسبنا الله ونعم الوكيل', 'Hasbunallahu wa ni''mal wakeel', 'Allah is sufficient for us and He is the best disposer of affairs', 7, 'emergency', 3);

INSERT INTO ruqyah_verses (surah, ayah, arabic, translation, category, sequence_order) VALUES
('Al-Fatiha', 1, 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ', 'In the name of Allah, the Entirely Merciful, the Especially Merciful', 'emergency', 1),
('Al-Baqarah', 255, 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ', 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence', 'emergency', 2);

INSERT INTO emergency_helplines (name, phone, description, availability, country, is_islamic, priority) VALUES
('National Suicide Prevention Lifeline', '988', '24/7 crisis support', '24/7', 'US', false, 1),
('Crisis Text Line', 'Text HOME to 741741', 'Free 24/7 crisis support via text', '24/7', 'US', false, 2),
('Islamic Crisis Support', '******-ISLAMIC', 'Islamic mental health crisis support', '24/7', 'US', true, 3);
