-- Enable PostgreSQL extensions
create extension if not exists "uuid-ossp";

-- Onboarding sessions table
create table public.onboarding_sessions (
    id uuid default uuid_generate_v4() primary key,
    session_id text unique not null,
    user_id uuid references auth.users not null,
    started_at timestamp with time zone not null,
    completed_at timestamp with time zone,
    current_step text not null,
    steps jsonb not null default '[]',
    total_time_spent integer not null default 0,
    abandoned_at timestamp with time zone,
    abandonment_reason text,
    device_info jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_current_step
    check (current_step in (
        'welcome', 
        'mental_health_awareness', 
        'spiritual_optimizer_clinical',
        'spiritual_optimizer_traditional',
        'ruqya_knowledge', 
        'professional_context', 
        'demographics', 
        'life_circumstances',
        'complete',
        'crisis_support'
    ))
);

-- User profiles table
create table public.user_profiles (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users unique not null,
    profile_data jsonb not null,
    completion_status text not null default 'incomplete',
    profile_version text not null default '1.0.0',
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_completion_status
    check (completion_status in ('incomplete', 'complete', 'needs_update'))
);

-- Crisis events table for monitoring and intervention
create table public.crisis_events (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    session_id text references public.onboarding_sessions(session_id),
    crisis_level text not null,
    indicators text[] not null,
    confidence numeric(3,2) not null,
    urgency text not null,
    context jsonb,
    response_actions text[],
    intervention_triggered boolean default false,
    resolved_at timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_crisis_level
    check (crisis_level in ('none', 'low', 'moderate', 'high', 'critical')),

    constraint valid_urgency
    check (urgency in ('low', 'moderate', 'urgent', 'immediate')),

    constraint valid_confidence
    check (confidence >= 0 and confidence <= 1)
);

-- Onboarding analytics table for tracking performance
create table public.onboarding_analytics (
    id uuid default uuid_generate_v4() primary key,
    date date not null,
    total_sessions integer not null default 0,
    completed_sessions integer not null default 0,
    abandoned_sessions integer not null default 0,
    crisis_detections integer not null default 0,
    average_completion_time integer, -- in seconds
    step_dropoff_data jsonb,
    pathway_distribution jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    unique(date)
);

-- Profile updates table for tracking changes
create table public.profile_updates (
    id uuid default uuid_generate_v4() primary key,
    profile_id uuid references public.user_profiles(id) not null,
    updates jsonb not null,
    reason text,
    confidence numeric(3,2),
    applied_at timestamp with time zone default timezone('utc'::text, now()) not null,
    applied_by uuid references auth.users,

    constraint valid_confidence
    check (confidence >= 0 and confidence <= 1)
);

-- Indexes for performance
create index idx_onboarding_sessions_user_id on public.onboarding_sessions(user_id);
create index idx_onboarding_sessions_session_id on public.onboarding_sessions(session_id);
create index idx_onboarding_sessions_current_step on public.onboarding_sessions(current_step);
create index idx_onboarding_sessions_started_at on public.onboarding_sessions(started_at);

create index idx_user_profiles_user_id on public.user_profiles(user_id);
create index idx_user_profiles_completion_status on public.user_profiles(completion_status);
create index idx_user_profiles_updated_at on public.user_profiles(updated_at);

create index idx_crisis_events_user_id on public.crisis_events(user_id);
create index idx_crisis_events_crisis_level on public.crisis_events(crisis_level);
create index idx_crisis_events_created_at on public.crisis_events(created_at);
create index idx_crisis_events_intervention_triggered on public.crisis_events(intervention_triggered);

create index idx_onboarding_analytics_date on public.onboarding_analytics(date);

create index idx_profile_updates_profile_id on public.profile_updates(profile_id);
create index idx_profile_updates_applied_at on public.profile_updates(applied_at);

-- Row Level Security (RLS) policies
alter table public.onboarding_sessions enable row level security;
alter table public.user_profiles enable row level security;
alter table public.crisis_events enable row level security;
alter table public.profile_updates enable row level security;

-- Users can only access their own onboarding sessions
create policy "Users can view own onboarding sessions" on public.onboarding_sessions
    for select using (auth.uid() = user_id);

create policy "Users can insert own onboarding sessions" on public.onboarding_sessions
    for insert with check (auth.uid() = user_id);

create policy "Users can update own onboarding sessions" on public.onboarding_sessions
    for update using (auth.uid() = user_id);

-- Users can only access their own profiles
create policy "Users can view own profile" on public.user_profiles
    for select using (auth.uid() = user_id);

create policy "Users can insert own profile" on public.user_profiles
    for insert with check (auth.uid() = user_id);

create policy "Users can update own profile" on public.user_profiles
    for update using (auth.uid() = user_id);

-- Crisis events - users can view their own, admins can view all
create policy "Users can view own crisis events" on public.crisis_events
    for select using (auth.uid() = user_id);

create policy "Service can insert crisis events" on public.crisis_events
    for insert with check (true); -- Service account can insert

-- Profile updates - users can view their own
create policy "Users can view own profile updates" on public.profile_updates
    for select using (
        auth.uid() in (
            select user_id from public.user_profiles 
            where id = profile_updates.profile_id
        )
    );

-- Analytics table - admin only (no RLS policy for regular users)
-- This will be handled at the application level

-- Functions for automatic timestamp updates
create or replace function public.handle_updated_at()
returns trigger as $$
begin
    new.updated_at = timezone('utc'::text, now());
    return new;
end;
$$ language plpgsql;

-- Triggers for automatic timestamp updates
create trigger handle_onboarding_sessions_updated_at
    before update on public.onboarding_sessions
    for each row execute procedure public.handle_updated_at();

create trigger handle_user_profiles_updated_at
    before update on public.user_profiles
    for each row execute procedure public.handle_updated_at();

-- Function to calculate onboarding completion rate
create or replace function public.calculate_completion_rate(start_date date, end_date date)
returns numeric as $$
declare
    total_sessions integer;
    completed_sessions integer;
begin
    select count(*) into total_sessions
    from public.onboarding_sessions
    where started_at::date between start_date and end_date;
    
    select count(*) into completed_sessions
    from public.onboarding_sessions
    where started_at::date between start_date and end_date
    and completed_at is not null;
    
    if total_sessions = 0 then
        return 0;
    end if;
    
    return round((completed_sessions::numeric / total_sessions::numeric) * 100, 2);
end;
$$ language plpgsql;

-- Function to get pathway distribution
create or replace function public.get_pathway_distribution(start_date date, end_date date)
returns jsonb as $$
declare
    result jsonb;
begin
    select jsonb_object_agg(
        coalesce(profile_data->>'recommendedPathway', 'unknown'),
        count(*)
    ) into result
    from public.user_profiles up
    join public.onboarding_sessions os on up.user_id = os.user_id
    where os.started_at::date between start_date and end_date
    and os.completed_at is not null;
    
    return coalesce(result, '{}'::jsonb);
end;
$$ language plpgsql;

-- Function to update daily analytics
create or replace function public.update_daily_analytics(target_date date)
returns void as $$
declare
    session_stats record;
    crisis_count integer;
    avg_time integer;
    dropoff_data jsonb;
    pathway_dist jsonb;
begin
    -- Get session statistics
    select 
        count(*) as total,
        count(completed_at) as completed,
        count(*) - count(completed_at) as abandoned
    into session_stats
    from public.onboarding_sessions
    where started_at::date = target_date;
    
    -- Get crisis detection count
    select count(*) into crisis_count
    from public.crisis_events
    where created_at::date = target_date;
    
    -- Get average completion time
    select avg(total_time_spent)::integer into avg_time
    from public.onboarding_sessions
    where started_at::date = target_date
    and completed_at is not null;
    
    -- Get step dropoff data (simplified)
    select jsonb_object_agg(current_step, count(*)) into dropoff_data
    from public.onboarding_sessions
    where started_at::date = target_date
    and completed_at is null
    and abandoned_at is not null
    group by current_step;
    
    -- Get pathway distribution
    select public.get_pathway_distribution(target_date, target_date) into pathway_dist;
    
    -- Insert or update analytics
    insert into public.onboarding_analytics (
        date,
        total_sessions,
        completed_sessions,
        abandoned_sessions,
        crisis_detections,
        average_completion_time,
        step_dropoff_data,
        pathway_distribution
    ) values (
        target_date,
        session_stats.total,
        session_stats.completed,
        session_stats.abandoned,
        crisis_count,
        avg_time,
        coalesce(dropoff_data, '{}'::jsonb),
        coalesce(pathway_dist, '{}'::jsonb)
    )
    on conflict (date) do update set
        total_sessions = excluded.total_sessions,
        completed_sessions = excluded.completed_sessions,
        abandoned_sessions = excluded.abandoned_sessions,
        crisis_detections = excluded.crisis_detections,
        average_completion_time = excluded.average_completion_time,
        step_dropoff_data = excluded.step_dropoff_data,
        pathway_distribution = excluded.pathway_distribution;
end;
$$ language plpgsql;
