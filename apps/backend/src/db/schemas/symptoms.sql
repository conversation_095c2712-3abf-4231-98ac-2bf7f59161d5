-- Enable PostgreSQL extensions
create extension if not exists "uuid-ossp";

-- Symptom submissions table
create table public.symptom_submissions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    jism_symptoms jsonb not null default '[]',
    nafs_symptoms jsonb not null default '[]',
    aql_symptoms jsonb not null default '[]',
    qalb_symptoms jsonb not null default '[]',
    ruh_symptoms jsonb not null default '[]',
    intensity_ratings jsonb not null,
    duration text not null,
    submission_date timestamp with time zone default timezone('utc'::text, now()) not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_symptoms_json 
    check (
        jsonb_typeof(jism_symptoms) = 'array' and
        jsonb_typeof(nafs_symptoms) = 'array' and
        jsonb_typeof(aql_symptoms) = 'array' and
        jsonb_typeof(qalb_symptoms) = 'array' and
        jsonb_typeof(ruh_symptoms) = 'array' and
        jsonb_typeof(intensity_ratings) = 'object'
    )
);

-- User diagnoses table
create table public.user_diagnoses (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    submission_id uuid references public.symptom_submissions not null,
    layers_affected text[] not null,
    spotlight text not null,
    recommended_journey text not null,
    severity_level text not null,
    diagnosis_date timestamp with time zone default timezone('utc'::text, now()) not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_severity_level
    check (severity_level in ('mild', 'moderate', 'severe', 'critical'))
);

-- Symptom tracking table
create table public.symptom_tracking (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users not null,
    symptom_id uuid references public.symptom_submissions not null,
    intensity integer not null,
    notes text,
    tracking_date timestamp with time zone default timezone('utc'::text, now()) not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,

    constraint valid_intensity_range
    check (intensity >= 1 and intensity <= 10)
);

-- Add RLS (Row Level Security) policies
alter table public.symptom_submissions enable row level security;
alter table public.user_diagnoses enable row level security;
alter table public.symptom_tracking enable row level security;

-- Policies for symptom_submissions
create policy "Users can view their own symptom submissions"
    on public.symptom_submissions for select
    using (auth.uid() = user_id);

create policy "Users can insert their own symptom submissions"
    on public.symptom_submissions for insert
    with check (auth.uid() = user_id);

-- Policies for user_diagnoses
create policy "Users can view their own diagnoses"
    on public.user_diagnoses for select
    using (auth.uid() = user_id);

create policy "Users can insert their own diagnoses"
    on public.user_diagnoses for insert
    with check (auth.uid() = user_id);

-- Policies for symptom_tracking
create policy "Users can view their own symptom tracking"
    on public.symptom_tracking for select
    using (auth.uid() = user_id);

create policy "Users can insert their own symptom tracking"
    on public.symptom_tracking for insert
    with check (auth.uid() = user_id);

-- Create indexes for better query performance
create index idx_symptom_submissions_user_id on public.symptom_submissions(user_id);
create index idx_user_diagnoses_user_id on public.user_diagnoses(user_id);
create index idx_symptom_tracking_user_id on public.symptom_tracking(user_id);
create index idx_symptom_tracking_symptom_id on public.symptom_tracking(symptom_id);