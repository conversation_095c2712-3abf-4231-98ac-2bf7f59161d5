import { Request, Response } from 'express';
import { assessment<PERSON>ontroller, AssessmentController } from '../assessment.controller'; // Adjust path
import { assessmentService } from '../../services/assessment.service'; // Adjust path
import { AppError } from '../../middleware/errorHandler'; // Adjust path
import { PersonalizedWelcome } from '../../models/Assessment';

// Mock the assessmentService
jest.mock('../../services/assessment.service', () => ({
  assessmentService: {
    startAssessment: jest.fn(),
    getPersonalizedWelcome: jest.fn(),
    getAssessmentQuestions: jest.fn(),
    submitAssessmentResponse: jest.fn(),
    getSession: jest.fn(),
    getDiagnosisDelivery: jest.fn(),
    submitDiagnosisFeedback: jest.fn(),
    updateSession: jest.fn(),
    forceGenerateDiagnosis: jest.fn(),
    getAssessmentHistory: jest.fn(),
    // resumeAssessment: jest.fn(), // resumeAssessment in controller calls getSession and getAssessmentQuestions
    abandonAssessment: jest.fn(),
    resetSession: jest.fn(),
    // Add mocks for any other methods used by the controller
  },
}));

// Mock logger if it's used directly in controller for anything other than error logging handled by middleware
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(), // Error should ideally be caught by global error handler
    debug: jest.fn(),
  },
}));


describe('AssessmentController', () => {
  let controllerInstance: AssessmentController;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    controllerInstance = new AssessmentController(); // Or use assessmentController singleton
    mockReq = {
      user: { id: 'user123', email: '<EMAIL>' } as any, // Mock user object
      body: {},
      params: {},
      query: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn(); // For error handling middleware
  });

  describe('startAssessment', () => {
    it('should start an assessment and return session and welcome message', async () => {
      mockReq.body = { userProfile: { type: 'test' } };
      const serviceResult = {
        session: { id: 'session1' },
        welcome: { greeting: 'Hello' } as PersonalizedWelcome,
      };
      (assessmentService.startAssessment as jest.Mock).mockResolvedValue(serviceResult);

      await controllerInstance.startAssessment(mockReq as Request, mockRes as Response);

      expect(assessmentService.startAssessment).toHaveBeenCalledWith(
        'user123',
        '<EMAIL>',
        { type: 'test' }
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: expect.objectContaining({
          session: serviceResult.session,
          welcome: serviceResult.welcome,
        }),
      }));
    });

    it('should throw AppError if user is not authenticated', async () => {
      mockReq.user = undefined; // Simulate unauthenticated user
      // We expect the controller to throw, which should be caught by an error handling middleware
      // For direct testing of throw, we'd wrap the call.
      // However, usually the global error handler (mockNext) is expected to be called.
      // Let's assume the controller re-throws or calls next(error).
      try {
        await controllerInstance.startAssessment(mockReq as Request, mockRes as Response);
      } catch (error) {
        expect(error).toBeInstanceOf(AppError);
        expect((error as AppError).message).toBe('User not authenticated');
        expect((error as AppError).statusCode).toBe(401);
      }
    });

    it('should throw AppError if userProfile is missing', async () => {
      mockReq.body = {}; // No userProfile
       try {
        await controllerInstance.startAssessment(mockReq as Request, mockRes as Response);
      } catch (error) {
        expect(error).toBeInstanceOf(AppError);
        expect((error as AppError).message).toContain('User profile required');
        expect((error as AppError).statusCode).toBe(400);
      }
    });
  });

  describe('generateWelcome', () => {
    const mockWelcomeResponse: PersonalizedWelcome = {
        userType: 'symptom_aware',
        greeting: 'As-salamu alaykum, Test User!',
        introduction: 'Let\'s explore your inner landscape.',
        assessmentFocus: 'General well-being and spiritual insights.',
        buttons: [{ text: 'Begin Assessment', action: 'start' }],
    };

    it('should generate personalized welcome content', async () => {
      mockReq.body = { userProfile: { type: 'customProfile', user_id: 'user123' } };
      (assessmentService.getPersonalizedWelcome as jest.Mock).mockResolvedValue(mockWelcomeResponse);

      await controllerInstance.generateWelcome(mockReq as Request, mockRes as Response);

      expect(assessmentService.getPersonalizedWelcome).toHaveBeenCalledWith(
        'user123',
        // The controller ensures user_id from token is used in profileForAIService
        { type: 'customProfile', user_id: 'user123' }
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'success',
        data: { welcome: mockWelcomeResponse },
      });
    });

    it('should use fallback profile from req.user if body.userProfile is missing but req.user.profile exists', async () => {
        mockReq.body = {}; // No userProfile in body
        (mockReq.user as any).profile = { type: 'fallbackProfile', someKey: 'someValue' }; // Add profile to req.user
        (assessmentService.getPersonalizedWelcome as jest.Mock).mockResolvedValue(mockWelcomeResponse);

        await controllerInstance.generateWelcome(mockReq as Request, mockRes as Response);

        expect(assessmentService.getPersonalizedWelcome).toHaveBeenCalledWith(
            'user123', // from req.user.id
            // The controller creates profileToSend ensuring user_id from token
            { type: 'fallbackProfile', someKey: 'someValue', user_id: 'user123' }
        );
        expect(mockRes.status).toHaveBeenCalledWith(200);
    });


    it('should return 400 if userProfile is missing from body and req.user.profile', async () => {
        mockReq.body = {}; // No userProfile in body
        (mockReq.user as any).profile = undefined; // No profile in req.user either

        // In this case, the controller itself sends the response, not throws.
        await controllerInstance.generateWelcome(mockReq as Request, mockRes as Response);

        expect(assessmentService.getPersonalizedWelcome).not.toHaveBeenCalled();
        expect(mockRes.status).toHaveBeenCalledWith(400);
        expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
            status: 'error',
            message: 'User profile is required in the request body.',
        }));
    });
  });

  describe('getQuestions', () => {
    it('should return assessment questions for a step', async () => {
      mockReq.params = { sessionId: 's1', step: 'physical' };
      const mockQuestions = [{ id: 'q1', title: 'Question 1' }];
      (assessmentService.getAssessmentQuestions as jest.Mock).mockResolvedValue(mockQuestions);

      await controllerInstance.getQuestions(mockReq as Request, mockRes as Response);

      expect(assessmentService.getAssessmentQuestions).toHaveBeenCalledWith('s1', 'physical');
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'success',
        data: { questions: mockQuestions },
      });
    });
  });

  describe('submitResponse', () => {
    beforeEach(() => {
        mockReq.params = { sessionId: 's1' };
        mockReq.body = { step: 'physical', responses: { q1: 'a1' }, timeSpent: 100 };
    });

    it('should submit response and return next step', async () => {
      const serviceResult = { nextStep: 'emotional', progress: 50, crisisDetected: false };
      (assessmentService.submitAssessmentResponse as jest.Mock).mockResolvedValue(serviceResult);

      await controllerInstance.submitResponse(mockReq as Request, mockRes as Response);

      expect(assessmentService.submitAssessmentResponse).toHaveBeenCalledWith('s1', 'physical', { q1: 'a1' }, 100);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: expect.objectContaining({ nextStep: 'emotional', progress: 50 }),
      }));
    });

    it('should return crisis detected if service indicates crisis', async () => {
      const serviceResult = {
        crisisDetected: true,
        crisisLevel: 'high',
        message: 'Crisis!',
        emergencyActions: [],
        urgency: 'immediate',
        crisisIndicators: ['indicator1'],
        nextStep: null,
        progress: 20
      };
      (assessmentService.submitAssessmentResponse as jest.Mock).mockResolvedValue(serviceResult);

      await controllerInstance.submitResponse(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200); // Controller sends 200 even for crisis
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'crisis_detected',
        data: expect.objectContaining({ crisisLevel: 'high' }),
      }));
    });

    it('should throw AppError if required fields are missing from body', async () => {
        mockReq.body = { step: 'physical' }; // Missing responses and timeSpent
        try {
            await controllerInstance.submitResponse(mockReq as Request, mockRes as Response);
        } catch (error) {
            expect(error).toBeInstanceOf(AppError);
            expect((error as AppError).statusCode).toBe(400);
            expect((error as AppError).message).toContain('Missing required fields');
        }
    });
  });

  describe('getDiagnosis', () => {
    beforeEach(() => {
        mockReq.params = { sessionId: 's1' };
    });

    it('should return diagnosis and delivery data', async () => {
      const mockSessionWithDiagnosis = { id: 's1', diagnosis: { id: 'd1' } };
      const mockDiagnosisDelivery = { diagnosisId: 'd1', content: 'details' };
      (assessmentService.getSession as jest.Mock).mockResolvedValue(mockSessionWithDiagnosis as any);
      (assessmentService.getDiagnosisDelivery as jest.Mock).mockResolvedValue(mockDiagnosisDelivery as any);

      await controllerInstance.getDiagnosis(mockReq as Request, mockRes as Response);

      expect(assessmentService.getSession).toHaveBeenCalledWith('s1');
      expect(assessmentService.getDiagnosisDelivery).toHaveBeenCalledWith('d1');
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        data: {
          diagnosis: mockSessionWithDiagnosis.diagnosis,
          delivery: mockDiagnosisDelivery,
        },
      }));
    });

    it('should throw AppError if diagnosis not yet available (session.diagnosis is null)', async () => {
        const mockSessionNoDiagnosis = { id: 's1', diagnosis: null };
        (assessmentService.getSession as jest.Mock).mockResolvedValue(mockSessionNoDiagnosis as any);
        try {
            await controllerInstance.getDiagnosis(mockReq as Request, mockRes as Response);
        } catch (error) {
            expect(error).toBeInstanceOf(AppError);
            expect((error as AppError).statusCode).toBe(404);
            expect((error as AppError).message).toContain('Diagnosis not yet available');
        }
    });
  });

  describe('submitFeedback', () => {
    beforeEach(() => {
        mockReq.params = { diagnosisId: 'd1' };
        mockReq.body = { accuracy: 5, helpfulness: 5, comments: 'Great!'};
    });
    it('should submit feedback successfully', async () => {
      (assessmentService.submitDiagnosisFeedback as jest.Mock).mockResolvedValue(undefined);
      await controllerInstance.submitFeedback(mockReq as Request, mockRes as Response);
      expect(assessmentService.submitDiagnosisFeedback).toHaveBeenCalledWith('d1', expect.objectContaining({
        userId: 'user123', accuracy: 5, helpfulness: 5, comments: 'Great!', diagnosisId: 'd1'
      }));
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({ status: 'success' }));
    });
    it('should throw AppError for invalid feedback data', async () => {
        mockReq.body = { accuracy: 0, helpfulness: 5}; // Invalid accuracy
        try {
            await controllerInstance.submitFeedback(mockReq as Request, mockRes as Response);
        } catch (error) {
            expect(error).toBeInstanceOf(AppError);
            expect((error as AppError).statusCode).toBe(400);
        }
    });
  });

  describe('getSession (controller method)', () => {
    it('should retrieve and return a session', async () => {
        mockReq.params = { sessionId: 's1' };
        const mockSession = { id: 's1', userId: 'user123' };
        (assessmentService.getSession as jest.Mock).mockResolvedValue(mockSession as any);
        await controllerInstance.getSession(mockReq as Request, mockRes as Response);
        expect(assessmentService.getSession).toHaveBeenCalledWith('s1');
        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledWith({ status: 'success', data: { session: mockSession }});
    });
    it('should throw AppError if user does not own session', async () => {
        mockReq.params = { sessionId: 's1' };
        const mockSession = { id: 's1', userId: 'otherUser' };
        (assessmentService.getSession as jest.Mock).mockResolvedValue(mockSession as any);
        try {
            await controllerInstance.getSession(mockReq as Request, mockRes as Response);
        } catch (error) {
            expect(error).toBeInstanceOf(AppError);
            expect((error as AppError).statusCode).toBe(403);
        }
    });
  });

  describe('updateSession', () => {
    it('should update a session', async () => {
        mockReq.params = { sessionId: 's1' };
        mockReq.body = { currentStep: 'new_step' };
        const updatedSession = { id: 's1', currentStep: 'new_step' };
        (assessmentService.updateSession as jest.Mock).mockResolvedValue(updatedSession as any);
        await controllerInstance.updateSession(mockReq as Request, mockRes as Response);
        expect(assessmentService.updateSession).toHaveBeenCalledWith('s1', 'user123', mockReq.body);
        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledWith({ status: 'success', data: { session: updatedSession }});
    });
  });

  describe('forceGenerateDiagnosis', () => {
    it('should force generate a diagnosis', async () => {
        mockReq.params = { sessionId: 's1' };
        // Need to mock getSession first for the ownership check in controller
        (assessmentService.getSession as jest.Mock).mockResolvedValue({ id: 's1', userId: 'user123' } as any);
        const mockDiagnosis = { id: 'd1', content: 'forced diagnosis' };
        (assessmentService.forceGenerateDiagnosis as jest.Mock).mockResolvedValue(mockDiagnosis as any);

        await controllerInstance.forceGenerateDiagnosis(mockReq as Request, mockRes as Response);

        expect(assessmentService.getSession).toHaveBeenCalledWith('s1');
        expect(assessmentService.forceGenerateDiagnosis).toHaveBeenCalledWith('s1');
        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({ data: { diagnosis: mockDiagnosis }}));
    });
     it('should deny access if user does not own session for force generate', async () => {
        mockReq.params = { sessionId: 's1' };
        (assessmentService.getSession as jest.Mock).mockResolvedValue({ id: 's1', userId: 'otherUser' } as any);

        try {
            await controllerInstance.forceGenerateDiagnosis(mockReq as Request, mockRes as Response);
        } catch (error) {
            expect(error).toBeInstanceOf(AppError);
            expect((error as AppError).statusCode).toBe(403);
        }
        expect(assessmentService.forceGenerateDiagnosis).not.toHaveBeenCalled();
    });
  });

  describe('getAssessmentHistory', () => {
    it('should retrieve assessment history', async () => {
        mockReq.query = { page: '1', limit: '5' };
        const history = { sessions: [], pagination: {} };
        (assessmentService.getAssessmentHistory as jest.Mock).mockResolvedValue(history);
        await controllerInstance.getAssessmentHistory(mockReq as Request, mockRes as Response);
        expect(assessmentService.getAssessmentHistory).toHaveBeenCalledWith('user123', 1, 5);
        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({ data: history }));
    });
  });

  describe('resumeAssessment', () => {
    const sessionId = 'resumeS1';
    const mockSession = { id: sessionId, userId: 'user123', currentStep: 'physical', completedAt: null, abandonedAt: null };
    const mockQuestions = [{id: 'q1'}];

    beforeEach(() => {
        mockReq.params = { sessionId };
        (assessmentService.getSession as jest.Mock).mockResolvedValue(mockSession as any);
        (assessmentService.getAssessmentQuestions as jest.Mock).mockResolvedValue(mockQuestions as any);
        // Mock the private _calculateProgress if it's directly called or make it part of a public method to test
        // For now, assuming the controller constructs progress or it comes from service.
        // The controller code has: const progress = (assessmentService as any)._calculateProgress(currentStep);
        // So we need to mock it on the service mock.
        (assessmentService as any)._calculateProgress = jest.fn().mockReturnValue(25);
    });

    it('should resume an assessment', async () => {
        await controllerInstance.resumeAssessment(mockReq as Request, mockRes as Response);
        expect(assessmentService.getSession).toHaveBeenCalledWith(sessionId);
        expect(assessmentService.getAssessmentQuestions).toHaveBeenCalledWith(sessionId, 'physical');
        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
            data: expect.objectContaining({ session: mockSession, questions: mockQuestions, progress: 25 })
        }));
    });
    it('should throw error if session completed', async () => {
        (assessmentService.getSession as jest.Mock).mockResolvedValue({ ...mockSession, completedAt: new Date() } as any);
        try {
            await controllerInstance.resumeAssessment(mockReq as Request, mockRes as Response);
        } catch (error) {
            expect(error).toBeInstanceOf(AppError);
            expect((error as AppError).message).toContain('Assessment session already completed');
        }
    });
  });

  describe('abandonAssessment', () => {
    it('should abandon an assessment', async () => {
        mockReq.params = { sessionId: 's1' };
        mockReq.body = { reason: 'user busy' };
        (assessmentService.abandonAssessment as jest.Mock).mockResolvedValue(undefined);
        await controllerInstance.abandonAssessment(mockReq as Request, mockRes as Response);
        expect(assessmentService.abandonAssessment).toHaveBeenCalledWith('s1', 'user123', 'user busy');
        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({ data: { message: expect.any(String)}}));
    });
  });

  describe('resetSession (Admin)', () => {
    it('should reset a session', async () => {
        mockReq.params = { sessionId: 'sR1' };
        // Assume admin role check would pass or is handled by a middleware not tested here
        (assessmentService.resetSession as jest.Mock).mockResolvedValue(undefined);
        await controllerInstance.resetSession(mockReq as Request, mockRes as Response);
        expect(assessmentService.resetSession).toHaveBeenCalledWith('sR1');
        expect(mockRes.status).toHaveBeenCalledWith(200);
    });
  });
// }); // Removed this potentially extra closing brace
