/**
 * Enhanced Symptoms Controller for Qalb Healing API
 * Updated to use enhanced response system and Islamic context
 */

import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { sendSuccess, sendError, sendNotFound, sendValidationError } from '../utils/response';
import { islamicLogger } from '../utils/enhancedLogger';
import { asyncHandler, createValidationError } from '../middleware/enhancedErrorHandler';
import { triggerN8nWorkflow } from '../services/n8n.service';

/**
 * Submit symptoms for AI analysis across five layers
 */
export const submitSymptoms = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please review your symptoms carefully. Allah knows what is in your heart, and accurate information helps us provide better guidance.'
    );
    return;
  }

  const { jism, nafs, aql, qalb, ruh, intensity, duration } = req.body;
  const userId = req.user?.id;
  const supabase = getSupabase();

  islamicLogger.journey('Symptoms submission started', {
    userId,
    layers: { jism: jism.length, nafs: nafs.length, aql: aql.length, qalb: qalb.length, ruh: ruh.length }
  });

  try {
    // Store symptoms in Supabase
    const { data: symptomEntry, error: symptomError } = await supabase
      .from('symptom_submissions')
      .insert({
        user_id: userId,
        jism_symptoms: jism,
        nafs_symptoms: nafs,
        aql_symptoms: aql,
        qalb_symptoms: qalb,
        ruh_symptoms: ruh,
        intensity_ratings: intensity,
        duration: duration,
        submission_date: new Date()
      })
      .select()
      .single();

    if (symptomError) {
      islamicLogger.error('Failed to store symptoms', { 
        userId, 
        error: symptomError.message 
      });
      
      sendError(
        res,
        'INTERNAL_ERROR',
        'Failed to store symptoms',
        500,
        { supabaseError: symptomError.message },
        'We encountered an issue while saving your symptoms. Please try again, and remember that Allah is the ultimate healer.'
      );
      return;
    }

    // Trigger n8n workflow for AI analysis
    let aiAnalysis;
    try {
      aiAnalysis = await triggerN8nWorkflow('symptom-analysis', {
        submissionId: symptomEntry.id,
        symptoms: { jism, nafs, aql, qalb, ruh, intensity, duration },
        userId
      });
    } catch (aiError) {
      islamicLogger.error('AI analysis failed', { 
        userId, 
        submissionId: symptomEntry.id,
        error: (aiError as Error).message 
      });
      
      // Continue without AI analysis for now
      aiAnalysis = {
        layers_affected: ['qalb'], // Default to heart layer
        spotlight: 'Unable to complete AI analysis at this time',
        recommended_journey: 'comprehensive',
        severity_level: 'moderate'
      };
    }

    // Store AI diagnosis
    const { error: diagnosisError } = await supabase
      .from('user_diagnoses')
      .insert({
        user_id: userId,
        submission_id: symptomEntry.id,
        layers_affected: (aiAnalysis as any).layers_affected,
        spotlight: (aiAnalysis as any).spotlight,
        recommended_journey: (aiAnalysis as any).recommended_journey,
        severity_level: (aiAnalysis as any).severity_level,
        diagnosis_date: new Date()
      });

    if (diagnosisError) {
      islamicLogger.error('Failed to store diagnosis', { 
        userId, 
        submissionId: symptomEntry.id,
        error: diagnosisError.message 
      });
    }

    islamicLogger.success('Symptoms submitted and analyzed successfully', { 
      userId, 
      submissionId: symptomEntry.id,
      layersAffected: (aiAnalysis as any).layers_affected
    });

    sendSuccess(
      res,
      {
        submissionId: symptomEntry.id,
        analysis: {
          primaryLayer: (aiAnalysis as any).layers_affected[0],
          severity: (aiAnalysis as any).severity_level,
          recommendations: (aiAnalysis as any).recommended_journey,
          spotlight: (aiAnalysis as any).spotlight
        }
      },
      'Your symptoms have been analyzed successfully. May Allah grant you healing and peace.',
      201,
      'healing'
    );
  } catch (error) {
    islamicLogger.error('Unexpected error in symptoms submission', {
      userId,
      error: (error as Error).message,
      stack: (error as Error).stack
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'An unexpected error occurred while processing your symptoms',
      500,
      undefined,
      'We encountered an unexpected issue. Please try again later, and remember that Allah is the source of all healing.'
    );
  }
});

/**
 * Get user's symptom submission history
 */
export const getSymptomHistory = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const userId = req.user?.id;
  const supabase = getSupabase();

  islamicLogger.info('Retrieving symptom history', { userId });

  const { data, error } = await supabase
    .from('symptom_submissions')
    .select(`
      *,
      user_diagnoses (*)
    `)
    .eq('user_id', userId)
    .order('submission_date', { ascending: false });

  if (error) {
    islamicLogger.error('Failed to retrieve symptom history', { 
      userId, 
      error: error.message 
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'Failed to retrieve symptom history',
      500,
      { supabaseError: error.message },
      'We could not retrieve your symptom history at this time. Please try again later.'
    );
    return;
  }

  islamicLogger.info('Symptom history retrieved successfully', { 
    userId, 
    entryCount: data?.length || 0 
  });

  sendSuccess(
    res,
    { history: data || [] },
    'Your symptom history has been retrieved successfully.',
    200,
    'success'
  );
});

/**
 * Get the latest AI diagnosis for the user
 */
export const getLatestDiagnosis = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const userId = req.user?.id;
  const supabase = getSupabase();

  islamicLogger.info('Retrieving latest diagnosis', { userId });

  const { data, error } = await supabase
    .from('user_diagnoses')
    .select(`
      *,
      symptom_submissions (*)
    `)
    .eq('user_id', userId)
    .order('diagnosis_date', { ascending: false })
    .limit(1)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 = not found
    islamicLogger.error('Failed to retrieve latest diagnosis', { 
      userId, 
      error: error.message 
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'Failed to retrieve diagnosis',
      500,
      { supabaseError: error.message }
    );
    return;
  }

  if (!data) {
    islamicLogger.info('No diagnosis found for user', { userId });
    
    sendNotFound(
      res,
      'Diagnosis',
      'No diagnosis has been completed yet. Please submit your symptoms first to receive guidance on your spiritual healing journey.'
    );
    return;
  }

  islamicLogger.info('Latest diagnosis retrieved successfully', { 
    userId, 
    diagnosisId: data.id 
  });

  sendSuccess(
    res,
    {
      diagnosisId: data.id,
      primaryLayer: data.layers_affected[0],
      severity: data.severity_level,
      recommendations: data.recommended_journey,
      spotlight: data.spotlight,
      createdAt: data.diagnosis_date
    },
    'Your latest diagnosis has been retrieved successfully.',
    200,
    'guidance'
  );
});

/**
 * Update symptom tracking progress
 */
export const trackSymptomProgress = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please provide accurate tracking information to monitor your healing progress effectively.'
    );
    return;
  }

  const { symptomId, intensity, notes } = req.body;
  const userId = req.user?.id;
  const supabase = getSupabase();

  islamicLogger.journey('Symptom progress tracking started', {
    userId,
    symptomId,
    intensity
  });

  const { data, error } = await supabase
    .from('symptom_tracking')
    .insert({
      user_id: userId,
      symptom_id: symptomId,
      intensity: intensity,
      notes: notes,
      tracking_date: new Date()
    })
    .select();

  if (error) {
    islamicLogger.error('Failed to track symptom progress', { 
      userId, 
      symptomId,
      error: error.message 
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'Failed to track symptom progress',
      500,
      { supabaseError: error.message },
      'We could not save your progress tracking. Please try again, and remember that every step towards healing is blessed by Allah.'
    );
    return;
  }

  islamicLogger.success('Symptom progress tracked successfully', { 
    userId, 
    symptomId,
    trackingId: data[0]?.id
  });

  sendSuccess(
    res,
    {
      trackingId: data[0]?.id,
      updatedAt: data[0]?.tracking_date
    },
    'Your symptom progress has been tracked successfully. May Allah continue to guide your healing journey.',
    200,
    'success'
  );
});