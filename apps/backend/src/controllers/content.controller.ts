import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { triggerN8nWorkflow } from '../services/n8n.service';
import { generateSignedUrl } from '../services/content.service'; // This service might need updates if it relies on Supabase indirectly

export const getPersonalizedFeed = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { page = 1, limit = 20, contentType = 'all', tags } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Get user preferences and history
    const userProfile = await prisma.profile.findUnique({
      where: { id: userId },
      select: { content_preferences: true, healing_focus: true }, // Adjust field names if needed based on schema
    });

    // Get personalized content recommendations
    const recommendations = await triggerN8nWorkflow(
      'get-content-recommendations',
      {
        userId,
        preferences: userProfile?.content_preferences,
        healingFocus: userProfile?.healing_focus,
        contentType,
        tags,
      }
    );

    const recommendedIds = (recommendations as any)?.contentIds || [];
    if (recommendedIds.length === 0) {
      res.status(200).json({
        status: 'success',
        data: { content: [], page: Number(page), limit: Number(limit), total: 0 },
      });
      return;
    }

    // Fetch recommended content
    const queryOptions: any = {
      where: {
        id: { in: recommendedIds },
        status: 'published',
      },
      include: {
        metadata: true, // Assuming relation name is 'metadata' for 'content_metadata'
        tags: { select: { tagName: true } }, // Assuming relation name is 'tags' for 'content_tags'
      },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit),
      orderBy: { createdAt: 'desc' },
    };

    if (contentType !== 'all') {
      queryOptions.where.contentType = contentType;
    }

    const content = await prisma.contentItem.findMany(queryOptions);

    // Generate signed URLs for protected content
    const contentWithUrls = await Promise.all(
      content.map(async (item: any) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storagePath // Ensure field names match Prisma schema
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
        total: recommendedIds.length, // This should be the total count from recommendations before pagination
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getContentById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { contentId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const content = await prisma.contentItem.findUnique({
      where: { id: contentId },
      include: {
        metadata: true,
        tags: { select: { tagName: true } },
        series: { // Assuming relation name is 'series' for 'content_series'
          select: {
            id: true,
            title: true,
            description: true,
            totalParts: true,
          },
        },
      },
    });

    if (!content) throw new AppError('Content not found', 404);

    // Generate signed URL for content access
    (content as any).accessUrl = await generateSignedUrl(
      content.storagePath
    );

    // Track content view
    await prisma.contentInteraction.create({
      data: {
        userId,
        contentId,
        interactionType: 'view',
        interactionDate: new Date(),
      },
    });

    res.status(200).json({
      status: 'success',
      data: {
        content,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getContentByCategory = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { category } = req.params;
    const { page = 1, limit = 20, sort = 'recent' } = req.query;
    const userId = req.user?.id; // Needed for personalized sorting

    const queryOptions: any = {
      where: {
        category,
        status: 'published',
      },
      include: {
        metadata: true,
        tags: { select: { tagName: true } },
      },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit),
    };

    // Apply sorting
    switch (sort) {
      case 'popular':
        queryOptions.orderBy = { viewCount: 'desc' };
        break;
      case 'recommended':
        if (userId) {
          const recommendations = await triggerN8nWorkflow(
            'sort-category-content',
            { userId, category }
          );
          const sortedIds = (recommendations as any)?.sortedIds;
          if (sortedIds && sortedIds.length > 0) {
            queryOptions.where.id = { in: sortedIds };
            // Prisma doesn't guarantee order for `in` unless explicitly ordered by a field that reflects that.
            // If n8n returns them in order, and we fetch all and then slice, that's one way.
            // Or, add a specific field for personalized sort order if possible.
            // For now, we'll rely on the IN clause and the default orderBy if any.
          }
        }
        // Default to recent if no user or recommendations
        if (!queryOptions.orderBy && !(queryOptions.where.id && queryOptions.where.id.in)) {
             queryOptions.orderBy = { createdAt: 'desc' };
        }
        break;
      default: // recent
        queryOptions.orderBy = { createdAt: 'desc' };
    }

    const content = await prisma.contentItem.findMany(queryOptions);

    // Generate signed URLs for content
    const contentWithUrls = await Promise.all(
      content.map(async (item: any) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storagePath
        ),
      }))
    );

    const totalCount = await prisma.contentItem.count({ where: queryOptions.where });

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
        total: totalCount,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const searchContent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { q, type = 'all', page = 1, limit = 20 } = req.query;
    const userId = req.user?.id;

    const searchResults = await triggerN8nWorkflow('search-content', {
      query: q,
      contentType: type,
      userId,
    });

    const contentIds = (searchResults as any)?.contentIds || [];
    if (contentIds.length === 0) {
       res.status(200).json({
        status: 'success',
        data: { content: [], page: Number(page), limit: Number(limit), total: 0 },
      });
      return;
    }

    const queryOptions: any = {
      where: {
        id: { in: contentIds },
        status: 'published',
      },
      include: {
        metadata: true,
        tags: { select: { tagName: true } },
      },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit),
      // Consider how to order results if n8n provides an order
    };

    if (type !== 'all') {
      queryOptions.where.contentType = type;
    }

    const content = await prisma.contentItem.findMany(queryOptions);

    const contentWithUrls = await Promise.all(
      content.map(async (item: any) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storagePath
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
        total: contentIds.length, // Total from search results
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getContentSeries = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { seriesId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const series = await prisma.contentSeries.findUnique({
      where: { id: seriesId },
      include: {
        items: { // Assuming relation name is 'items' for 'content_items' in ContentSeries model
          where: { status: 'published' }, // Filter for published items within the series
          select: {
            id: true,
            title: true,
            description: true,
            contentType: true,
            duration: true,
            partNumber: true,
            status: true,
            storagePath: true, // Needed for signed URL
          },
          orderBy: { partNumber: 'asc' },
        },
      },
    });

    if (!series) throw new AppError('Series not found', 404);

    const progress = await prisma.seriesProgress.findUnique({
      where: {
        userId_seriesId: { // Prisma default composite key name
          userId,
          seriesId,
        },
      },
    });

    const contentWithUrls = await Promise.all(
      (series.items || []).map(async (item: any) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storagePath
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        series: {
          ...series,
          items: contentWithUrls, // Use 'items' consistent with Prisma include
        },
        userProgress: progress || null,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const trackInteraction = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Map validation errors to a more structured format if desired
      throw new AppError('Invalid input data', 400, errors.array());
    }

    const { contentId, interactionType, duration, progress, rating } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const interaction = await prisma.contentInteraction.create({
      data: {
        userId,
        contentId,
        interactionType,
        duration,
        progress,
        rating,
        interactionDate: new Date(),
      },
    });

    if (!interaction) throw new AppError('Failed to record interaction', 400);

    // Replace RPC call with direct Prisma updates
    if (interactionType === 'complete') {
      await prisma.contentItem.update({
        where: { id: contentId },
        data: { completionCount: { increment: 1 } },
      });
    } else if (interactionType === 'view') {
      await prisma.contentItem.update({
        where: { id: contentId },
        data: { viewCount: { increment: 1 } },
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        interaction,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getBookmarkedContent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const bookmarks = await prisma.contentInteraction.findMany({
      where: {
        userId,
        interactionType: 'bookmark',
      },
      select: {
        contentId: true,
        interactionDate: true,
        contentItem: { // Assuming relation name is 'contentItem'
          include: {
            metadata: true,
            tags: { select: { tagName: true } },
          },
        },
      },
      orderBy: { interactionDate: 'desc' },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit),
    });

    const bookmarksWithUrls = await Promise.all(
      bookmarks.map(async (bookmark: any) => {
        if (!bookmark.contentItem) return bookmark; // Should not happen if data is consistent
        return {
          ...bookmark,
          contentItem: { // Changed from content_items to contentItem
            ...bookmark.contentItem,
            accessUrl: await generateSignedUrl(
              bookmark.contentItem.storagePath
            ),
          },
        };
      })
    );

    const totalBookmarks = await prisma.contentInteraction.count({
      where: { userId, interactionType: 'bookmark' },
    });

    res.status(200).json({
      status: 'success',
      data: {
        bookmarks: bookmarksWithUrls,
        page: Number(page),
        limit: Number(limit),
        total: totalBookmarks,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getRecommendations = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { contentType = 'all', limit = 10 } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const recommendations = await triggerN8nWorkflow(
      'get-content-recommendations',
      {
        userId,
        contentType,
        limit: Number(limit),
      }
    );

    const recommendedIds = (recommendations as any)?.contentIds || [];
    if (recommendedIds.length === 0) {
      res.status(200).json({
        status: 'success',
        data: { recommendations: [] },
      });
      return;
    }

    const queryOptions: any = {
        where: {
            id: { in: recommendedIds },
            status: 'published',
        },
        include: {
            metadata: true,
            tags: { select: { tagName: true } },
        },
        take: Number(limit) // n8n might return more than limit, so ensure final list respects it
    };

    if (contentType !== 'all') {
      queryOptions.where.contentType = contentType;
    }

    const content = await prisma.contentItem.findMany(queryOptions);

    const contentWithUrls = await Promise.all(
      content.map(async (item: any) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storagePath
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        recommendations: contentWithUrls,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const reportContentIssue = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400, errors.array());
    }

    const { contentId, issueType, description } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const report = await prisma.contentIssue.create({
      data: {
        userId,
        contentId,
        issueType,
        description,
        status: 'pending',
        reportDate: new Date(),
      },
    });

    if (!report) throw new AppError('Failed to report issue', 400);

    await triggerN8nWorkflow('notify-content-issue', {
      reportId: report.id,
      contentId,
      issueType,
      description,
      userId, // Pass userId for context in n8n
    });

    res.status(201).json({
      status: 'success',
      data: {
        report,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getLayerContent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { layerType } = req.params;
    const { page = 1, limit = 20, focus } = req.query;

    const queryOptions: any = {
      where: {
        healingLayer: layerType, // Ensure this field name matches Prisma schema
        status: 'published',
      },
      include: {
        metadata: true,
        tags: { select: { tagName: true } },
      },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit),
      orderBy: { createdAt: 'desc' },
    };

    if (focus && typeof focus === 'string' && focus.length > 0) {
      // Assuming 'focus' is a single string, adjust if it can be multiple
      // Prisma 'contains' is for array fields. If 'focusAreas' is an array:
      queryOptions.where.focusAreas = { has: focus };
      // If focusAreas is a string and you want partial match:
      // queryOptions.where.focusAreas = { contains: focus, mode: 'insensitive' };
    } else if (focus && Array.isArray(focus) && focus.length > 0) {
        queryOptions.where.focusAreas = { hasSome: focus };
    }


    const content = await prisma.contentItem.findMany(queryOptions);
    const totalCount = await prisma.contentItem.count({ where: queryOptions.where });


    const contentWithUrls = await Promise.all(
      content.map(async (item: any) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storagePath
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        content: contentWithUrls,
        page: Number(page),
        limit: Number(limit),
        total: totalCount
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getDailyInspiration = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const userProfile = await prisma.profile.findUnique({
      where: { id: userId },
      select: { healing_focus: true, content_preferences: true }, // Adjust field names
    });

    const currentJourney = await prisma.userJourney.findFirst({
      where: {
        userId,
        status: 'active',
      },
      select: { currentDay: true, focusLayers: true }, // Adjust field names
    });

    const inspiration = await triggerN8nWorkflow('get-daily-inspiration', {
      userId,
      healingFocus: userProfile?.healing_focus,
      currentJourneyDay: currentJourney?.currentDay,
      focusLayers: currentJourney?.focusLayers,
    });

    const inspirationIds = (inspiration as any)?.contentIds || [];
    if (inspirationIds.length === 0) {
       res.status(200).json({
        status: 'success',
        data: { inspiration: [] },
      });
      return;
    }

    const content = await prisma.contentItem.findMany({
      where: {
        id: { in: inspirationIds },
        status: 'published',
      },
      include: {
        metadata: true,
        tags: { select: { tagName: true } },
      },
    });

    const contentWithUrls = await Promise.all(
      content.map(async (item: any) => ({
        ...item,
        accessUrl: await generateSignedUrl(
          item.storagePath
        ),
      }))
    );

    res.status(200).json({
      status: 'success',
      data: {
        inspiration: contentWithUrls,
      },
    });
  } catch (error) {
    next(error);
  }
};
