import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';
import { triggerN8nWorkflow } from '../services/n8n.service';

export const submitSymptoms = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { jism, nafs, aql, qalb, ruh, intensity, duration } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Store symptoms in Supabase
    const { data: symptomEntry, error: symptomError } = await supabase
      .from('symptom_submissions')
      .insert({
        user_id: userId,
        jism_symptoms: jism,
        nafs_symptoms: nafs,
        aql_symptoms: aql,
        qalb_symptoms: qalb,
        ruh_symptoms: ruh,
        intensity_ratings: intensity,
        duration: duration,
        submission_date: new Date()
      })
      .select()
      .single();

    if (symptomError) throw new AppError(symptomError.message, 400);

    // Trigger n8n workflow for AI analysis
    const aiAnalysis = await triggerN8nWorkflow('symptom-analysis', {
      submissionId: symptomEntry.id,
      symptoms: {
        jism, nafs, aql, qalb, ruh,
        intensity, duration
      },
      userId
    });

    // Store AI diagnosis
    const { error: diagnosisError } = await supabase
      .from('user_diagnoses')
      .insert({
        user_id: userId,
        submission_id: symptomEntry.id,
        layers_affected: (aiAnalysis as any).layers_affected,
        spotlight: (aiAnalysis as any).spotlight,
        recommended_journey: (aiAnalysis as any).recommended_journey,
        severity_level: (aiAnalysis as any).severity_level,
        diagnosis_date: new Date()
      });

    if (diagnosisError) throw new AppError(diagnosisError.message, 400);

    logger.info('Symptoms submitted and analyzed', { userId, submissionId: symptomEntry.id });

    res.status(201).json({
      status: 'success',
      data: {
        submission: symptomEntry,
        diagnosis: {
          layers_affected: (aiAnalysis as any).layers_affected,
          spotlight: (aiAnalysis as any).spotlight,
          recommended_journey: (aiAnalysis as any).recommended_journey
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

export const getSymptomHistory = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const supabase = getSupabase();
    const { data, error } = await supabase
      .from('symptom_submissions')
      .select(`
        *,
        user_diagnoses (*)
      `)
      .eq('user_id', req.user?.id)
      .order('submission_date', { ascending: false });

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        history: data
      }
    });
  } catch (error) {
    next(error);
  }
};

export const getLatestDiagnosis = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const supabase = getSupabase();
    const { data, error } = await supabase
      .from('user_diagnoses')
      .select(`
        *,
        symptom_submissions (*)
      `)
      .eq('user_id', req.user?.id)
      .order('diagnosis_date', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = not found
      throw new AppError(error.message, 400);
    }

    if (!data) {
      res.status(404).json({
        status: 'error',
        message: 'No diagnosis found'
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        diagnosis: data
      }
    });
  } catch (error) {
    next(error);
  }
};

export const trackSymptomProgress = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { symptomId, intensity, notes } = req.body;
    const supabase = getSupabase();

    const { data, error } = await supabase
      .from('symptom_tracking')
      .insert({
        user_id: req.user?.id,
        symptom_id: symptomId,
        intensity: intensity,
        notes: notes,
        tracking_date: new Date()
      })
      .select();

    if (error) throw new AppError(error.message, 400);

    logger.info('Symptom progress tracked', { userId: req.user?.id, symptomId });

    res.status(200).json({
      status: 'success',
      data: {
        tracking: data
      }
    });
  } catch (error) {
    next(error);
  }
};
