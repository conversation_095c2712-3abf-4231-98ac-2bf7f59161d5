import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { communityService } from '../services/community.service';

export const getHeartCircles = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { category, tags, search, page = 1, limit = 20 } = req.query;
    const supabase = getSupabase();

    let query = supabase
      .from('heart_circles')
      .select(
        `
        *,
        circle_members!inner(count)
      `
      )
      .eq('is_active', true);

    // Apply filters
    if (category) {
      query = query.eq('category', category);
    }

    if (tags && Array.isArray(tags) && tags.length > 0) {
      query = query.contains('tags', tags);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    const { data: circles, error } = await query;

    if (error) throw new AppError(error.message, 400);

    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('heart_circles')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    res.status(200).json({
      status: 'success',
      data: {
        circles: circles || [],
        pagination: {
          page: Number(page),
          totalPages: Math.ceil((totalCount || 0) / Number(limit)),
          totalItems: totalCount || 0,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const createHeartCircle = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const {
      name,
      description,
      category,
      tags,
      maxMembers = 20,
      isPrivate = false,
    } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check if user already has too many circles
    const { data: userCircles } = await supabase
      .from('heart_circles')
      .select('id')
      .eq('created_by', userId)
      .eq('is_active', true);

    if (userCircles && userCircles.length >= 3) {
      throw new AppError('You can only create up to 3 active circles', 400);
    }

    // Create the circle
    const { data: circle, error } = await supabase
      .from('heart_circles')
      .insert({
        name,
        description,
        category,
        tags: tags || [],
        max_members: maxMembers,
        is_private: isPrivate,
        created_by: userId,
        is_active: true,
        created_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    // Add creator as first member
    await supabase.from('circle_members').insert({
      circle_id: circle.id,
      user_id: userId,
      role: 'admin',
      joined_at: new Date(),
    });

    logger.info('Heart Circle created', { userId, circleId: circle.id, name });

    res.status(201).json({
      status: 'success',
      data: {
        circleId: circle.id,
        name: circle.name,
        createdAt: circle.created_at,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const joinHeartCircle = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { circleId } = req.params;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check if circle exists and has space
    const { data: circle, error: circleError } = await supabase
      .from('heart_circles')
      .select(
        `
        *,
        circle_members!inner(count)
      `
      )
      .eq('id', circleId)
      .eq('is_active', true)
      .single();

    if (circleError) throw new AppError('Circle not found', 404);

    const memberCount = circle.circle_members?.[0]?.count || 0;
    if (memberCount >= circle.max_members) {
      throw new AppError('Circle is full', 400);
    }

    // Check if user is already a member
    const { data: existingMember } = await supabase
      .from('circle_members')
      .select('id')
      .eq('circle_id', circleId)
      .eq('user_id', userId)
      .single();

    if (existingMember) {
      throw new AppError('You are already a member of this circle', 400);
    }

    // Add user to circle
    const { error: joinError } = await supabase.from('circle_members').insert({
      circle_id: circleId,
      user_id: userId,
      role: 'member',
      joined_at: new Date(),
    });

    if (joinError) throw new AppError(joinError.message, 400);

    logger.info('User joined Heart Circle', { userId, circleId });

    res.status(200).json({
      status: 'success',
      message: 'Successfully joined the Heart Circle',
    });
  } catch (error) {
    next(error);
  }
};

export const leaveHeartCircle = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { circleId } = req.params;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check if user is a member
    const { data: membership, error: memberError } = await supabase
      .from('circle_members')
      .select('*')
      .eq('circle_id', circleId)
      .eq('user_id', userId)
      .single();

    if (memberError)
      throw new AppError('You are not a member of this circle', 404);

    // Remove user from circle
    const { error: leaveError } = await supabase
      .from('circle_members')
      .delete()
      .eq('circle_id', circleId)
      .eq('user_id', userId);

    if (leaveError) throw new AppError(leaveError.message, 400);

    // If user was admin and there are other members, transfer admin to another member
    if (membership.role === 'admin') {
      const { data: otherMembers } = await supabase
        .from('circle_members')
        .select('user_id')
        .eq('circle_id', circleId)
        .limit(1);

      if (otherMembers && otherMembers.length > 0) {
        await supabase
          .from('circle_members')
          .update({ role: 'admin' })
          .eq('circle_id', circleId)
          .eq('user_id', otherMembers[0].user_id);
      } else {
        // No other members, deactivate circle
        await supabase
          .from('heart_circles')
          .update({ is_active: false })
          .eq('id', circleId);
      }
    }

    logger.info('User left Heart Circle', { userId, circleId });

    res.status(200).json({
      status: 'success',
      message: 'Successfully left the Heart Circle',
    });
  } catch (error) {
    next(error);
  }
};

export const getCircleMessages = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { circleId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check if user is a member of the circle
    const { data: membership } = await supabase
      .from('circle_members')
      .select('id')
      .eq('circle_id', circleId)
      .eq('user_id', userId)
      .single();

    if (!membership) {
      throw new AppError('You must be a member to view messages', 403);
    }

    // Get messages
    const offset = (Number(page) - 1) * Number(limit);
    const { data: messages, error } = await supabase
      .from('circle_messages')
      .select(
        `
        *,
        profiles!inner(id, display_name)
      `
      )
      .eq('circle_id', circleId)
      .order('created_at', { ascending: false })
      .range(offset, offset + Number(limit) - 1);

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        messages: messages || [],
        page: Number(page),
        limit: Number(limit),
      },
    });
  } catch (error) {
    next(error);
  }
};

export const sendCircleMessage = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { circleId } = req.params;
    const { message, messageType = 'text' } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check if user is a member of the circle
    const { data: membership } = await supabase
      .from('circle_members')
      .select('id')
      .eq('circle_id', circleId)
      .eq('user_id', userId)
      .single();

    if (!membership) {
      throw new AppError('You must be a member to send messages', 403);
    }

    // Check message rate limiting (max 10 messages per hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const { data: recentMessages } = await supabase
      .from('circle_messages')
      .select('id')
      .eq('circle_id', circleId)
      .eq('user_id', userId)
      .gte('created_at', oneHourAgo.toISOString());

    if (recentMessages && recentMessages.length >= 10) {
      throw new AppError(
        'Message rate limit exceeded. Please wait before sending more messages.',
        429
      );
    }

    // Send message
    const { data: newMessage, error } = await supabase
      .from('circle_messages')
      .insert({
        circle_id: circleId,
        user_id: userId,
        message,
        message_type: messageType,
        created_at: new Date(),
      })
      .select(
        `
        *,
        profiles!inner(id, display_name)
      `
      )
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Circle message sent', { userId, circleId, messageType });

    res.status(201).json({
      status: 'success',
      data: {
        message: newMessage,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const requestDua = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { requestText } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const duaRequest = await communityService.requestDua(userId, requestText);

    res.status(201).json({
      status: 'success',
      message: 'Dua request created successfully',
      data: duaRequest,
    });
  } catch (error) {
    next(error);
  }
};

export const getPendingDuaRequests = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    // Only allow admins or moderators to view pending Du'a requests
    if (req.user?.role !== 'admin' && req.user?.role !== 'moderator') {
      throw new AppError('Forbidden: Insufficient permissions', 403);
    }

    const pendingRequests = await communityService.getPendingDuaRequests();

    res.status(200).json({
      status: 'success',
      data: pendingRequests,
    });
  } catch (error) {
    next(error);
  }
};

export const completeDuaRequest = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { requestId } = req.params;
    const completerId = req.user?.id;

    if (!completerId) {
      throw new AppError('User not authenticated', 401);
    }

    // Only allow admins or moderators to complete Du'a requests
    if (req.user?.role !== 'admin' && req.user?.role !== 'moderator') {
      throw new AppError('Forbidden: Insufficient permissions', 403);
    }

    const completedDua = await communityService.completeDuaRequest(requestId, completerId);

    res.status(200).json({
      status: 'success',
      message: 'Du\'a request marked as completed',
      data: completedDua,
    });
  } catch (error) {
    next(error);
  }
};

export const matchPeerSupporter = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const supporterId = await communityService.matchPeerSupporter(userId);

    if (!supporterId) {
      res.status(404).json({
        status: 'fail',
        message: 'No peer supporter found at this time.',
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      message: 'Peer supporter matched successfully',
      data: { supporterId },
    });
  } catch (error) {
    next(error);
  }
};
