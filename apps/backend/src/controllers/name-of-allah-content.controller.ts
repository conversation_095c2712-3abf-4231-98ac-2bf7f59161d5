import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { NameOfAllahContentService } from '../services/name-of-allah-content.service';
// Assuming a custom HttpException utility might exist or can be added later
// For now, we'll rely on standard error handling or simple status codes.

// Initialize Prisma Client - This should ideally be a singleton managed elsewhere (e.g., in db config and imported)
// For simplicity in this step, creating a new instance here.
const prisma = new PrismaClient();
const nameOfAllahContentService = new NameOfAllahContentService(prisma);

export const createNameOfAllahContent = async (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const content = await nameOfAllahContentService.create(req.body);
    res.status(201).json({ message: 'Name of Allah content created successfully', data: content });
  } catch (error) {
    // Log the error for server-side inspection
    console.error('Error in createNameOfAllahContent:', error);
    // Check if it's a Prisma known error for unique constraint
    if (error.code === 'P2002' && error.meta?.target?.includes('name')) {
         return res.status(409).json({ message: 'Name of Allah content with this name already exists.' });
    }
    // Forward to a generic error handler or send a generic message
    res.status(500).json({ message: error.message || 'Failed to create Name of Allah content.' });
  }
};

export const getAllNameOfAllahContent = async (req: Request, res: Response, next: NextFunction) => {
  const { skip, take, where, orderBy } = req.query;
  const params: any = {};
  if (skip) params.skip = parseInt(skip as string, 10);
  if (take) params.take = parseInt(take as string, 10);
  if (where) {
    try {
      params.where = JSON.parse(where as string);
    } catch (e) {
      return res.status(400).json({ message: 'Invalid "where" query parameter: not a valid JSON string.' });
    }
  }
  if (orderBy) {
     try {
      params.orderBy = JSON.parse(orderBy as string);
    } catch (e) {
      return res.status(400).json({ message: 'Invalid "orderBy" query parameter: not a valid JSON string.' });
    }
  }

  try {
    const contentList = await nameOfAllahContentService.findAll(params);
    res.status(200).json({ message: 'Names of Allah content retrieved successfully', data: contentList, total: contentList.length });
  } catch (error) {
    console.error('Error in getAllNameOfAllahContent:', error);
    res.status(500).json({ message: error.message || 'Failed to retrieve Names of Allah content.' });
  }
};

export const getNameOfAllahContentById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const content = await nameOfAllahContentService.findOne(req.params.id);
    if (!content) {
        return res.status(404).json({ message: 'Name of Allah content not found.' });
    }
    res.status(200).json({ message: 'Name of Allah content retrieved successfully', data: content });
  } catch (error) {
    console.error('Error in getNameOfAllahContentById:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    res.status(500).json({ message: error.message || 'Failed to retrieve Name of Allah content.' });
  }
};

export const updateNameOfAllahContent = async (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const content = await nameOfAllahContentService.update(req.params.id, req.body);
    res.status(200).json({ message: 'Name of Allah content updated successfully', data: content });
  } catch (error) {
    console.error('Error in updateNameOfAllahContent:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    if (error.code === 'P2002' && error.meta?.target?.includes('name')) {
         return res.status(409).json({ message: 'Name of Allah content with this name already exists.' });
    }
    res.status(500).json({ message: error.message || 'Failed to update Name of Allah content.' });
  }
};

export const deleteNameOfAllahContent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await nameOfAllahContentService.remove(req.params.id);
    res.status(200).json({ message: 'Name of Allah content deleted successfully' });
  } catch (error) {
    console.error('Error in deleteNameOfAllahContent:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    res.status(500).json({ message: error.message || 'Failed to delete Name of Allah content.' });
  }
};
