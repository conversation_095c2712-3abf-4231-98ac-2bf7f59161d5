import { Request, Response, NextFunction } from 'express';
import { User } from '@supabase/supabase-js';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { analyticsService } from '../services/analytics.service';
import { moduleService } from '../services/module.service';
import { AuthenticatedRequest } from '../types/authenticated-request';

interface DashboardData {
  todaysFocus: {
    nameOfAllah: any;
    quranVerse: any;
    reflectionPrompt: any;
    dailyDhikr: any;
  };
  progress: {
    currentJourney: any;
    completionRate: number;
    streakDays: number;
    layerProgress: Record<string, number>;
  };
  quickActions: any[];
  recentActivity: any[];
  upcomingTasks: any[];
  spiritualInsights: any[];
}

/**
 * Dashboard Controller - Provides unified dashboard experience
 */
export class DashboardController {
  /**
   * @desc    Get today's dashboard focus and content
   * @route   GET /api/dashboard/today
   * @access  Private
   */
  async getTodayDashboard(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user.id;
      const supabase = getSupabase();

      // Get user profile to understand preferences and journey type
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (profileError) throw new AppError(profileError.message, 400);

      // Get active journey information
      const { data: activeJourney, error: journeyError } = await supabase
        .from('user_journeys')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      // Get today's scheduled content and focus
      const today = new Date();
      const dayNumber = activeJourney
        ? Math.ceil(
            (today.getTime() - new Date(activeJourney.start_date).getTime()) /
              (1000 * 60 * 60 * 24)
          )
        : 1;

      const { data: dailyContent, error: contentError } = await supabase
        .from('journey_daily_content')
        .select(
          `
          *,
          name_spotlight:names_of_allah(*),
          quranic_verse:quranic_verses(*),
          reflection_prompt:reflection_prompts(*)
        `
        )
        .eq('journey_id', activeJourney?.id)
        .eq('day_number', dayNumber)
        .single();

      // Get user's progress analytics
      const progressData = await analyticsService.getUserProgress(
        userId,
        'week'
      );

      // Get recent activity
      const { data: recentActivity } = await supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5);

      // Get upcoming tasks/modules
      const upcomingModules = await moduleService.getAvailableModules(userId);

      // Prepare dashboard data
      const dashboardData: DashboardData = {
        todaysFocus: {
          nameOfAllah:
            dailyContent?.name_spotlight || (await this.getRandomNameOfAllah()),
          quranVerse:
            dailyContent?.quranic_verse || (await this.getRandomQuranVerse()),
          reflectionPrompt:
            dailyContent?.reflection_prompt ||
            (await this.getRandomReflectionPrompt()),
          dailyDhikr: await this.getDailyDhikr(),
        },
        progress: {
          currentJourney: activeJourney,
          completionRate: progressData.completionRate,
          streakDays: progressData.streakDays,
          layerProgress: progressData.layerProgress,
        },
        quickActions: this.getQuickActions(profile, activeJourney),
        recentActivity: recentActivity || [],
        upcomingTasks: upcomingModules.slice(0, 3),
        spiritualInsights: await this.getSpiritualInsights(
          userId,
          progressData
        ),
      };

      logger.info('Today dashboard retrieved', {
        userId,
        journeyActive: !!activeJourney,
      });

      res.json({
        status: 'success',
        data: dashboardData,
      });
    } catch (error) {
      logger.error('Error getting today dashboard', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * @desc    Get weekly progress summary
   * @route   GET /api/dashboard/weekly-summary
   * @access  Private
   */
  async getWeeklySummary(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user.id;

      // Get comprehensive analytics for the week
      const weeklyAnalytics = await analyticsService.getDashboardAnalytics(
        userId,
        'week'
      );

      logger.info('Weekly summary retrieved', { userId });

      res.json({
        status: 'success',
        data: weeklyAnalytics,
      });
    } catch (error) {
      logger.error('Error getting weekly summary', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * @desc    Get personalized recommendations
   * @route   GET /api/dashboard/recommendations
   * @access  Private
   */
  async getRecommendations(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user.id;
      const supabase = getSupabase();

      // Get user's recent progress and activities
      const progressData = await analyticsService.getUserProgress(
        userId,
        'month'
      );
      const insights = await analyticsService.generateInsights(
        userId,
        progressData
      );

      // Get content recommendations based on user's current state
      const { data: contentRecommendations } = await supabase
        .from('content_recommendations')
        .select(
          `
          *,
          content:content_items(*)
        `
        )
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('priority', { ascending: true })
        .limit(5);

      const recommendations = {
        spiritual: insights.recommendations,
        content: contentRecommendations || [],
        practices: await this.getPracticeRecommendations(userId, progressData),
        modules: await this.getModuleRecommendations(userId),
      };

      logger.info('Recommendations retrieved', {
        userId,
        totalRecommendations: recommendations.spiritual.length,
      });

      res.json({
        status: 'success',
        data: recommendations,
      });
    } catch (error) {
      logger.error('Error getting recommendations', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * @desc    Update dashboard preferences
   * @route   PUT /api/dashboard/preferences
   * @access  Private
   */
  async updatePreferences(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user.id;
      const { preferences } = req.body;
      const supabase = getSupabase();

      const { error } = await supabase.from('user_preferences').upsert({
        user_id: userId,
        dashboard_preferences: preferences,
        updated_at: new Date().toISOString(),
      });

      if (error) throw new AppError(error.message, 400);

      logger.info('Dashboard preferences updated', { userId });

      res.json({
        status: 'success',
        message: 'Preferences updated successfully',
      });
    } catch (error) {
      logger.error('Error updating preferences', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Get random Name of Allah for daily focus
   */
  private async getRandomNameOfAllah(): Promise<any> {
    const supabase = getSupabase();
    const { data } = await supabase
      .from('names_of_allah')
      .select('*')
      .eq('is_active', true)
      .order('RANDOM()')
      .limit(1)
      .single();

    return data;
  }

  /**
   * Get random Quran verse for daily reflection
   */
  private async getRandomQuranVerse(): Promise<any> {
    const supabase = getSupabase();
    const { data } = await supabase
      .from('quranic_verses')
      .select('*')
      .eq('is_daily_reflection', true)
      .order('RANDOM()')
      .limit(1)
      .single();

    return data;
  }

  /**
   * Get random reflection prompt
   */
  private async getRandomReflectionPrompt(): Promise<any> {
    const supabase = getSupabase();
    const { data } = await supabase
      .from('reflection_prompts')
      .select('*')
      .eq('is_active', true)
      .order('RANDOM()')
      .limit(1)
      .single();

    return data;
  }

  /**
   * Get daily dhikr recommendation
   */
  private async getDailyDhikr(): Promise<any> {
    const supabase = getSupabase();
    const { data } = await supabase
      .from('dhikr_recommendations')
      .select('*')
      .eq('is_daily', true)
      .eq('is_active', true)
      .order('RANDOM()')
      .limit(1)
      .single();

    return data;
  }

  /**
   * Get quick actions based on user profile and journey
   */
  private getQuickActions(profile: any, activeJourney: any): any[] {
    const quickActions = [
      {
        id: 'emergency_support',
        title: 'Emergency Support',
        description: 'Access immediate Sakina mode',
        icon: 'emergency',
        route: '/emergency',
        priority: 1,
      },
      {
        id: 'daily_dhikr',
        title: 'Daily Dhikr',
        description: 'Start your dhikr practice',
        icon: 'dhikr',
        route: '/dhikr',
        priority: 2,
      },
      {
        id: 'symptom_check',
        title: 'Symptom Check-in',
        description: 'Track your current state',
        icon: 'symptoms',
        route: '/symptoms',
        priority: 3,
      },
    ];

    if (activeJourney) {
      quickActions.push({
        id: 'continue_journey',
        title: 'Continue Journey',
        description: `Continue ${activeJourney.journey_type} journey`,
        icon: 'journey',
        route: `/journeys/${activeJourney.id}`,
        priority: 1,
      });
    }

    return quickActions.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get spiritual insights based on user progress
   */
  private async getSpiritualInsights(
    userId: string,
    progressData: any
  ): Promise<any[]> {
    const insights = [];

    // Streak insights
    if (progressData.streakDays >= 7) {
      insights.push({
        type: 'achievement',
        title: 'Consistency Mastery',
        message: `Subhan Allah! You've maintained a ${progressData.streakDays}-day streak.`,
        icon: 'streak',
      });
    }

    // Layer progress insights
    const strongestLayer = Object.entries(progressData.layerProgress).sort(
      ([, a], [, b]) => (b as number) - (a as number)
    )[0];

    if (strongestLayer && (strongestLayer[1] as number) > 80) {
      insights.push({
        type: 'strength',
        title: 'Spiritual Strength',
        message: `Your ${strongestLayer[0]} layer shows excellent progress (${strongestLayer[1]}%).`,
        icon: 'strength',
      });
    }

    return insights;
  }

  /**
   * Get practice recommendations based on progress
   */
  private async getPracticeRecommendations(
    userId: string,
    progressData: any
  ): Promise<any[]> {
    const recommendations = [];

    // Recommend practices based on weak areas
    Object.entries(progressData.layerProgress).forEach(([layer, progress]) => {
      if ((progress as number) < 50) {
        switch (layer) {
          case 'nafs':
            recommendations.push({
              type: 'dhikr',
              title: 'Nafs Purification',
              description: 'Practice istighfar for nafs cleansing',
            });
            break;
          case 'qalb':
            recommendations.push({
              type: 'reflection',
              title: 'Heart Reflection',
              description: 'Engage in heart-centered meditation',
            });
            break;
        }
      }
    });

    return recommendations;
  }

  /**
   * Get module recommendations
   */
  private async getModuleRecommendations(userId: string): Promise<any[]> {
    const availableModules = await moduleService.getAvailableModules(userId);
    return availableModules.slice(0, 3);
  }
}

export const dashboardController = new DashboardController();
