/**
 * Assessment Controller for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Handles API endpoints for spiritual assessment and diagnosis
 */

import { Request, Response } from 'express';
import { assessmentService } from '../services/assessment.service';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

export class AssessmentController {
  /**
   * Start a new assessment session
   * POST /api/assessment/start
   */
  async startAssessment(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AppError('User not authenticated', 401);
      }

      // Get user profile from Feature 0
      const userProfile = req.body.userProfile || (req.user as any)?.profile;
      if (!userProfile) {
        throw new AppError(
          'User profile required. Please complete onboarding first.',
          400
        );
      }

      const userEmail = req.user?.email;
      if (!userEmail) {
        throw new AppError('User email not found in token', 401);
      }

      const result = await assessmentService.startAssessment(
        userId,
        userEmail,
        userProfile
      );

      logger.info('Assessment started', {
        userId,
        sessionId: result.session.id,
        userType: result.welcome.userType,
      });

      res.status(200).json({
        status: 'success',
        data: {
          session: result.session,
          welcome: result.welcome,
          message: 'Assessment session started successfully',
        },
      });
    } catch (error) {
      logger.error('Error starting assessment:', error);
      throw error;
    }
  }

  /**
   * Generate personalized welcome content for assessment
   * POST /api/assessment/welcome
   */
  async generateWelcome(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AppError('User not authenticated for welcome content', 401);
      }

      logger.info('=== WELCOME ENDPOINT CALLED ===', { userId });

      // Check if user has already completed an assessment
      logger.info('Checking for completed assessments...', { userId });
      const existingCompletedAssessment = await assessmentService.getLatestCompletedAssessment(userId);
      
      if (existingCompletedAssessment) {
        logger.info('FOUND COMPLETED ASSESSMENT - Returning completion status', { 
          userId, 
          sessionId: existingCompletedAssessment.id,
          completedAt: existingCompletedAssessment.completedAt 
        });

        // Return a different response for users who have already completed assessment
        res.status(200).json({
          status: 'success',
          data: {
            assessmentStatus: 'completed',
            lastCompletedAt: existingCompletedAssessment.completedAt,
            sessionId: existingCompletedAssessment.id,
            message: 'You have already completed your assessment.',
            actions: [
              {
                id: 'view_results',
                text: 'View My Results',
                description: 'See your spiritual diagnosis and insights'
              },
              {
                id: 'start_journey',
                text: 'Start Healing Journey',
                description: 'Begin your personalized healing journey'
              },
              {
                id: 'retake_assessment',
                text: 'Take New Assessment',
                description: 'Start a fresh assessment to track your progress'
              }
            ]
          }
        });
        return;
      }

      logger.info('NO COMPLETED ASSESSMENT FOUND - Generating regular welcome', { userId });

      // userProfile is expected in the request body now
      const userProfile = req.body;
      if (!userProfile || Object.keys(userProfile).length === 0) {
        // Attempt to fallback to req.user.profile if body is empty,
        // though client should explicitly send it.
        const fallbackProfile = (req.user as any)?.profile;
        if (!fallbackProfile) {
          logger.warn('User profile not found in request body or req.user.profile for /welcome', { userId });
          throw new AppError('User profile is required in the request body.', 400);
        }
        logger.info('Using fallback userProfile from req.user.profile for /welcome', { userId });
        // Ensure user_id is consistent if using fallback profile
        const profileToSend = { ...fallbackProfile, userId: userId };
        const welcome = await assessmentService.getPersonalizedWelcome(userId, profileToSend);
        res.status(200).json({ status: 'success', data: { welcome }});
        return;
      }

      // Ensure the userId within the provided userProfile matches the authenticated user
      const profileForService = { ...userProfile, userId: userId };

// Extract optional name from request body
const userName = req.body.name || req.body.userName || req.body.fullName;

      const welcome = await assessmentService.getPersonalizedWelcome(userId, profileForService, userName);

      logger.info('Personalized welcome content generated', { userId, userType: welcome.userType });

      res.status(200).json({
        status: 'success',
        data: { welcome },
      });
    } catch (error) {
      logger.error('Error generating personalized welcome content:', { userId: req.user?.id, errorMsg: error?.message, error });
      // Don't re-throw; allow global error handler to manage response
      if (error instanceof AppError) {
        res.status(error.statusCode).json({ status: 'error', message: error.message });
      } else {
        res.status(500).json({ status: 'error', message: 'Failed to generate welcome content.' });
      }
    }
  }

  /**
   * Get assessment questions for current step
   * GET /api/assessment/:sessionId/questions/:step
   */
  async getQuestions(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId, step } = req.params;

      const questions = await assessmentService.getAssessmentQuestions(
        sessionId,
        step
      );

      res.status(200).json({
        status: 'success',
        data: { questions },
      });
    } catch (error) {
      logger.error('Error getting assessment questions:', error);
      throw error;
    }
  }

  /**
   * Submit assessment response
   * POST /api/assessment/:sessionId/submit
   */
  async submitResponse(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const { step, responses, timeSpent } = req.body;

      // Basic validation
      if (!step || !responses || timeSpent === undefined) {
        throw new AppError(
          'Missing required fields: step, responses, timeSpent',
          400
        );
      }

      const result = await assessmentService.submitAssessmentResponse(
        sessionId,
        step,
        responses,
        timeSpent
      );

      // Handle crisis detection
      if (result.crisisDetected) {
        logger.warn('Crisis detected during assessment submission', {
          sessionId,
          userId: req.user?.id,
          crisisLevel: result.crisisLevel,
        });

        // The 'result' object from assessmentService.submitAssessmentResponse when crisisDetected is true
        // should contain all necessary fields for the mobile app's CrisisData state.
        // Let's assume 'result' now includes: crisisLevel, message, emergencyActions (as CrisisAction[]),
        // urgency, and crisisIndicators.
        logger.warn('Crisis detected during assessment submission by controller', {
          sessionId,
          userId: req.user?.id,
          crisisLevel: result.crisisLevel, // Assuming result has this
          urgency: result.urgency, // Assuming result has this
          indicators: result.crisisIndicators // Assuming result has this
        });

        res.status(200).json({
          status: 'crisis_detected',
          data: {
            crisisLevel: result.crisisLevel,
            message: result.message,
            emergencyActions: result.emergencyActions, // Use actions from the service result
            urgency: result.urgency,                 // Pass urgency from service result
            crisisIndicators: result.crisisIndicators, // Pass indicators from service result
            // nextStep and progress might not be relevant for the crisis modal itself
            // but could be part of the result object for other purposes.
            // For the crisis modal, the above fields are key.
            nextStep: result.nextStep,
            progress: result.progress
          },
        });
        return;
      }

      res.status(200).json({
        status: 'success',
        data: {
          nextStep: result.nextStep,
          progress: result.progress,
          message: result.nextStep
            ? 'Response submitted successfully'
            : 'Assessment completed! Generating your diagnosis...',
        },
      });
    } catch (error) {
      logger.error('Error submitting assessment response:', error);
      throw error;
    }
  }

  /**
   * Get spiritual diagnosis
   * GET /api/assessment/:sessionId/diagnosis
   */
  async getDiagnosis(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      // First check if diagnosis exists for this session
      const session = await assessmentService.getSession(sessionId);
      logger.info('Session diagnosis status:', { sessionId, hasDiagnosis: !!session.diagnosis });
      
      let diagnosis;
      if (!session.diagnosis) {
        // If no diagnosis exists, generate one automatically
        logger.info('No diagnosis found, generating new diagnosis...', { sessionId });
        diagnosis = await assessmentService.generateDiagnosis(sessionId);
      } else {
        diagnosis = session.diagnosis;
      }

      const diagnosisDelivery = await assessmentService.getDiagnosisDelivery(
        diagnosis.id
      );

      res.status(200).json({
        status: 'success',
        data: {
          diagnosis: diagnosis,
          delivery: diagnosisDelivery,
          message: 'Spiritual diagnosis retrieved successfully',
        },
      });
    } catch (error) {
      logger.error('Error getting diagnosis:', error);
      throw error;
    }
  }

  /**
   * Submit diagnosis feedback
   * POST /api/assessment/diagnosis/:diagnosisId/feedback
   */
  async submitFeedback(req: Request, res: Response): Promise<void> {
    try {
      const { diagnosisId } = req.params;
      const { accuracy, helpfulness, comments } = req.body;

      // Validate feedback
      if (
        !accuracy ||
        !helpfulness ||
        accuracy < 1 ||
        accuracy > 5 ||
        helpfulness < 1 ||
        helpfulness > 5
      ) {
        throw new AppError(
          'Invalid feedback: accuracy and helpfulness must be between 1-5',
          400
        );
      }

      // await assessmentService.submitDiagnosisFeedback(diagnosisId, {
      // Validate user
      const userId = req.user?.id;
      if (!userId) {
        throw new AppError('User not authenticated for feedback submission.', 401);
      }

      await assessmentService.submitDiagnosisFeedback(diagnosisId, {
        userId, // Pass the authenticated user's ID
        accuracy,
        helpfulness,
        comments,
        diagnosisId, // Added diagnosisId to feedback data payload
      });

      logger.info('Diagnosis feedback submitted', {
        diagnosisId,
        userId: req.user?.id,
        accuracy,
        helpfulness,
      });

      res.status(200).json({
        status: 'success',
        data: {
          message:
            'Thank you for your feedback! This helps us improve our assessments.',
        },
      });
    } catch (error) {
      logger.error('Error submitting diagnosis feedback:', error);
      throw error;
    }
  }

  /**
   * Get assessment session details
   * GET /api/assessment/session/:sessionId
   */
  async getSession(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.id;

      const session = await assessmentService.getSession(sessionId);

      // Verify user owns this session
      if (session.userId !== userId) {
        throw new AppError('Access denied', 403);
      }

      res.status(200).json({
        status: 'success',
        data: { session },
      });
    } catch (error) {
      logger.error('Error getting assessment session:', error);
      throw error;
    }
  }

  /**
   * Update assessment session details
   * PUT /api/assessment/session/:sessionId
   */
  async updateSession(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.id;
      const updateData = req.body;

      const session = await assessmentService.updateSession(
        sessionId,
        userId,
        updateData
      );

      res.status(200).json({
        status: 'success',
        data: { session },
      });
    } catch (error) {
      logger.error('Error updating assessment session:', error);
      throw error;
    }
  }

  /**
   * Force generate diagnosis for a session
   * POST /api/assessment/:sessionId/generate-diagnosis
   */
  async forceGenerateDiagnosis(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.id;

      // Optional: Add authorization check here if only certain users can force generate
      // For now, we'll allow it if the user owns the session
      const session = await assessmentService.getSession(sessionId);
      if (session.userId !== userId) {
        throw new AppError('Access denied', 403);
      }

      const diagnosis = await assessmentService.forceGenerateDiagnosis(sessionId);

      res.status(200).json({
        status: 'success',
        data: { diagnosis },
        message: 'Diagnosis force-generated successfully.',
      });
    } catch (error) {
      logger.error('Error force generating diagnosis:', error);
      throw error;
    }
  }

  /**
   * Get user's assessment history
   * GET /api/assessment/history
   */
  async getAssessmentHistory(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AppError('User not authenticated', 401);
      }

      const { page = 1, limit = 10 } = req.query;
      const historyData = await assessmentService.getAssessmentHistory(
        userId,
        parseInt(page as string, 10),
        parseInt(limit as string, 10)
      );

      res.status(200).json({
        status: 'success',
        // Ensure the response structure matches client expectations, e.g., data: historyData
        data: historyData,
      });
    } catch (error) {
      logger.error('Error getting assessment history:', error);
      throw error;
    }
  }

  /**
   * Resume incomplete assessment
   * POST /api/assessment/resume/:sessionId
   */
  async resumeAssessment(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.id;
      if (!userId) { // Added user check
        throw new AppError('User not authenticated to resume assessment.', 401);
      }

      const session = await assessmentService.getSession(sessionId);

      // Verify user owns this session
      if (session.userId !== userId) {
        throw new AppError('Access denied. You do not own this assessment session.', 403);
      }

      // Check if session is already completed
      if (session.completedAt) {
        // Consider if client should be redirected or shown the completed diagnosis
        throw new AppError('Assessment session already completed. View diagnosis instead.', 400);
      }
      if (session.abandonedAt) {
         throw new AppError('This assessment session was abandoned. Please start a new one.', 400);
      }

      // Get current step questions (or welcome if that's the step)
      const currentStep = session.currentStep || 'welcome'; // Default to welcome if no step
      const questions = await assessmentService.getAssessmentQuestions(
        sessionId,
        currentStep
      );
      const progress = (assessmentService as any)._calculateProgress(currentStep); // Access private for now, or make public if needed

      res.status(200).json({
        status: 'success',
        data: {
          session,
          currentStep,
          progress,
          questions,
          message: 'Assessment resumed successfully at step: ' + currentStep,
        },
      });
    } catch (error) {
      logger.error('Error resuming assessment:', error);
      throw error;
    }
  }

  /**
   * Abandon assessment session
   * POST /api/assessment/:sessionId/abandon
   */
  async abandonAssessment(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const { reason } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        throw new AppError('User not authenticated to abandon assessment.', 401);
      }

      await assessmentService.abandonAssessment(sessionId, userId, reason);

      logger.info('Assessment abandoned by user', {
        sessionId,
        userId,
        reason, // Fixed: use reason instead of undefined history
      });
      // Added a response to the client for successful abandonment
      res.status(200).json({
        status: 'success',
        data: {
          message: 'Assessment session successfully abandoned.',
        },
      });
    } catch (error) {
      logger.error('Error abandoning assessment:', error); // Fixed: Correct error message
      throw error;
    }
  }

  /**
   * Reset an assessment session (Admin only)
   * POST /api/assessment/admin/reset/:sessionId
   */
  async resetSession(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      // In a real application, you would add admin authentication/authorization here
      // For example, check if req.user has an 'admin' role

      await assessmentService.resetSession(sessionId);

      logger.info(`Assessment session ${sessionId} reset by admin.`, { sessionId });

      res.status(200).json({
        status: 'success',
        data: {
          message: `Assessment session ${sessionId} reset successfully.`,
        },
      });
    } catch (error) {
      logger.error(`Error resetting assessment session ${req.params.sessionId}:`, error);
      throw error;
    }
  }
}

export const assessmentController = new AssessmentController();