import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';
import { triggerN8nWorkflow } from '../services/n8n.service';

export const getSelfCheckQuestions = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    const { data: questions, error } = await supabase
      .from('ruqya_assessment_questions')
      .select('*')
      .eq('is_active', true)
      .order('category', { ascending: true })
      .order('sequence_order', { ascending: true });

    if (error) {
      // Fallback questions
      const fallbackQuestions = [
        {
          id: '1',
          question:
            'Do you experience unexplained nightmares or disturbing dreams?',
          category: 'spiritual_symptoms',
          answerType: 'yes_no',
        },
        {
          id: '2',
          question:
            'Do you feel unexplained anxiety or fear, especially during prayer?',
          category: 'spiritual_symptoms',
          answerType: 'yes_no',
        },
        {
          id: '3',
          question:
            'How often do you experience physical symptoms without medical cause?',
          category: 'physical_symptoms',
          answerType: 'scale',
        },
        {
          id: '4',
          question:
            'Do you have difficulty concentrating during dhikr or Quran recitation?',
          category: 'spiritual_symptoms',
          answerType: 'yes_no',
        },
        {
          id: '5',
          question: 'Rate your overall spiritual well-being (1-10)',
          category: 'general_assessment',
          answerType: 'scale',
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          questions: fallbackQuestions,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        questions: questions || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const submitSelfCheck = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { responses, additionalNotes, emergencyContact } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Store assessment responses
    const { data: assessment, error: assessmentError } = await supabase
      .from('ruqya_assessments')
      .insert({
        user_id: userId,
        responses,
        additional_notes: additionalNotes,
        emergency_contact: emergencyContact,
        assessment_date: new Date(),
      })
      .select()
      .single();

    if (assessmentError) throw new AppError(assessmentError.message, 400);

    // Analyze responses using AI
    const analysis = await triggerN8nWorkflow('ruqya-assessment-analysis', {
      userId,
      assessmentId: assessment.id,
      responses,
      additionalNotes,
    });

    // Store analysis results
    const { error: resultError } = await supabase
      .from('ruqya_assessment_results')
      .insert({
        assessment_id: assessment.id,
        user_id: userId,
        risk_level: (analysis as any).riskLevel || 'low',
        recommended_actions: (analysis as any).recommendedActions || [],
        treatment_plan_id: (analysis as any).treatmentPlanId,
        analysis_date: new Date(),
      })
      .select()
      .single();

    if (resultError) throw new AppError(resultError.message, 400);

    // Check for emergency situations
    const riskLevel = (analysis as any).riskLevel;
    if (riskLevel === 'emergency' || riskLevel === 'high') {
      // Trigger emergency protocols
      await triggerN8nWorkflow('ruqya-emergency-protocol', {
        userId,
        assessmentId: assessment.id,
        riskLevel,
        emergencyContact,
      });
    }

    logger.info('Ruqya self-assessment completed', {
      userId,
      assessmentId: assessment.id,
      riskLevel,
    });

    res.status(201).json({
      status: 'success',
      data: {
        assessmentId: assessment.id,
        riskLevel: (analysis as any).riskLevel,
        recommendedActions: (analysis as any).recommendedActions,
        treatmentPlanId: (analysis as any).treatmentPlanId,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getTreatmentPlan = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Get user's latest treatment plan
    const { data: treatmentPlan, error } = await supabase
      .from('ruqya_treatment_plans')
      .select(
        `
        *,
        treatment_practices (*)
      `
      )
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code === 'PGRST116') {
      // No treatment plan found, create a basic one
      const basicPlan = {
        planId: null,
        duration: 7,
        practices: [
          {
            id: 'basic-1',
            name: 'Morning Protection Adhkar',
            frequency: 'daily',
            duration: 10,
            verses: [
              {
                arabic: 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
                transliteration: "A'udhu billahi min ash-shaytani'r-rajim",
                translation: 'I seek refuge in Allah from Satan, the accursed',
              },
            ],
          },
          {
            id: 'basic-2',
            name: 'Evening Protection Adhkar',
            frequency: 'daily',
            duration: 10,
            verses: [
              {
                arabic:
                  'بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ وَلَا فِي السَّمَاءِ وَهُوَ السَّمِيعُ الْعَلِيمُ',
                transliteration:
                  "Bismillahi'lladhi la yadurru ma'a ismihi shay'un fi'l-ardi wa la fi's-sama'i wa huwa's-sami'u'l-'alim",
                translation:
                  'In the name of Allah with whose name nothing is harmed on earth nor in the heavens, and He is the Hearing, the Knowing',
              },
            ],
          },
        ],
      };

      res.status(200).json({
        status: 'success',
        data: basicPlan,
      });
      return;
    }

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        planId: treatmentPlan.id,
        duration: treatmentPlan.duration,
        practices: treatmentPlan.treatment_practices || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const updateTreatmentProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { practiceId, completed, effectiveness, notes } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Record practice progress
    const { data: progress, error } = await supabase
      .from('ruqya_practice_progress')
      .upsert({
        user_id: userId,
        practice_id: practiceId,
        completed,
        effectiveness_rating: effectiveness,
        notes,
        practice_date: new Date(),
        updated_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Ruqya practice progress updated', {
      userId,
      practiceId,
      completed,
      effectiveness,
    });

    res.status(200).json({
      status: 'success',
      data: {
        progress,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getRecommendedDuas = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // const userId = req.user?.id;
    const supabase = getSupabase();

    // Get user's current spiritual state for personalized recommendations
    // const { data: userProfile } = await supabase
    //   .from('profiles')
    //   .select('spiritual_focus, current_challenges')
    //   .eq('user_id', userId)
    //   .single();

    // Get recommended duas
    const { data: duas, error } = await supabase
      .from('ruqya_duas')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: true })
      .limit(10);

    if (error) {
      // Fallback duas
      const fallbackDuas = [
        {
          id: 'dua-1',
          arabic: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْهَمِّ وَالْحَزَنِ',
          transliteration: "Allahumma inni a'udhu bika min al-hammi wa'l-hazan",
          translation: 'O Allah, I seek refuge in You from anxiety and sorrow',
          category: 'anxiety_relief',
          benefits: ['Reduces anxiety', 'Brings peace of mind'],
        },
        {
          id: 'dua-2',
          arabic: 'حَسْبُنَا اللَّهُ وَنِعْمَ الْوَكِيلُ',
          transliteration: "Hasbunallahu wa ni'mal wakeel",
          translation:
            'Allah is sufficient for us and He is the best disposer of affairs',
          category: 'trust_in_allah',
          benefits: ['Increases trust in Allah', 'Provides spiritual strength'],
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          duas: fallbackDuas,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        duas: duas || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const saveFavoriteDua = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { duaId, category, notes } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    const { data: favorite, error } = await supabase
      .from('user_favorite_duas')
      .upsert({
        user_id: userId,
        dua_id: duaId,
        category,
        notes,
        saved_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Favorite dua saved', { userId, duaId, category });

    res.status(201).json({
      status: 'success',
      data: {
        favorite,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getHealingResources = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    const { data: resources, error } = await supabase
      .from('ruqya_resources')
      .select('*')
      .eq('is_active', true)
      .order('category', { ascending: true })
      .order('priority', { ascending: true });

    if (error) {
      // Fallback resources
      const fallbackResources = [
        {
          id: 'resource-1',
          title: 'Understanding Ruqya in Islam',
          description: 'Comprehensive guide to Islamic spiritual healing',
          category: 'educational',
          resourceType: 'article',
          url: null,
          content: 'Basic information about ruqya and its importance in Islam',
        },
        {
          id: 'resource-2',
          title: 'Signs of Spiritual Distress',
          description: 'How to recognize spiritual ailments',
          category: 'assessment',
          resourceType: 'guide',
          url: null,
          content: 'Guidelines for self-assessment of spiritual health',
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          resources: fallbackResources,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        resources: resources || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const reportConcern = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { concernType, description, severity, isEmergency } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    const { data: concern, error } = await supabase
      .from('ruqya_concerns')
      .insert({
        user_id: userId,
        concern_type: concernType,
        description,
        severity,
        is_emergency: isEmergency,
        status: 'pending',
        reported_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    // If emergency, trigger immediate response
    if (isEmergency) {
      await triggerN8nWorkflow('ruqya-emergency-concern', {
        userId,
        concernId: concern.id,
        description,
        severity,
      });
    }

    logger.info('Ruqya concern reported', {
      userId,
      concernId: concern.id,
      concernType,
      isEmergency,
    });

    res.status(201).json({
      status: 'success',
      data: {
        concernId: concern.id,
        status: concern.status,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getProgressTimeline = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const supabase = getSupabase();

    const { data: timeline, error } = await supabase
      .from('ruqya_progress_timeline')
      .select('*')
      .eq('user_id', userId)
      .order('date', { ascending: false })
      .limit(30);

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        timeline: timeline || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const scheduleConsultation = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const {
      preferredDate,
      consultationType,
      primaryConcern,
      previousTreatment,
    } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    const { data: consultation, error } = await supabase
      .from('ruqya_consultations')
      .insert({
        user_id: userId,
        preferred_date: preferredDate,
        consultation_type: consultationType,
        primary_concern: primaryConcern,
        previous_treatment: previousTreatment,
        status: 'pending',
        requested_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    // Notify consultation team
    await triggerN8nWorkflow('ruqya-consultation-request', {
      userId,
      consultationId: consultation.id,
      consultationType,
      primaryConcern,
    });

    logger.info('Ruqya consultation scheduled', {
      userId,
      consultationId: consultation.id,
      consultationType,
    });

    res.status(201).json({
      status: 'success',
      data: {
        consultationId: consultation.id,
        status: consultation.status,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getSafetyGuidelines = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    const { data: guidelines, error } = await supabase
      .from('ruqya_safety_guidelines')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: true });

    if (error) {
      // Fallback guidelines
      const fallbackGuidelines = [
        {
          id: 'safety-1',
          title: "Always Seek Allah's Protection",
          description:
            "Begin any ruqya practice with seeking Allah's protection",
          category: 'spiritual_safety',
          importance: 'critical',
        },
        {
          id: 'safety-2',
          title: 'Know Your Limits',
          description: 'Do not attempt advanced ruqya without proper knowledge',
          category: 'practice_safety',
          importance: 'high',
        },
        {
          id: 'safety-3',
          title: 'Consult Qualified Practitioners',
          description:
            'For serious cases, always consult qualified Islamic healers',
          category: 'professional_guidance',
          importance: 'critical',
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          guidelines: fallbackGuidelines,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        guidelines: guidelines || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const submitDailyProtection = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { practices } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Insert daily protection practices
    const practiceRecords = practices.map((practice: any) => ({
      user_id: userId,
      practice_type: practice.type,
      completed: practice.completed,
      practice_time: practice.time,
      recorded_at: new Date(),
    }));

    const { data: records, error } = await supabase
      .from('daily_protection_practices')
      .insert(practiceRecords)
      .select();

    if (error) throw new AppError(error.message, 400);

    logger.info('Daily protection practices submitted', {
      userId,
      practicesCount: practices.length,
    });

    res.status(201).json({
      status: 'success',
      data: {
        records: records || [],
      },
    });
  } catch (error) {
    next(error);
  }
};
