import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';

export const getProgressAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { timeframe = 'month', layers } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate: Date;

    switch (timeframe) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(0); // All time
    }

    // Get overall progress
    const { data: progressData } = await supabase.rpc(
      'get_user_progress_analytics',
      {
        user_id: userId,
        start_date: startDate.toISOString(),
        end_date: now.toISOString(),
        target_layers: layers || null,
      }
    );

    // Get layer-specific progress
    const { data: layerProgress } = await supabase.rpc('get_layer_progress', {
      user_id: userId,
      start_date: startDate.toISOString(),
      end_date: now.toISOString(),
    });

    // Get progress trends
    const { data: trends } = await supabase.rpc('get_progress_trends', {
      user_id: userId,
      start_date: startDate.toISOString(),
      end_date: now.toISOString(),
    });

    res.status(200).json({
      status: 'success',
      data: {
        overallProgress: progressData?.overall_progress || 0,
        layerProgress: layerProgress || {},
        trends: trends || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getJourneyAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { journeyId, includeCompleted = true } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    let query = supabase
      .from('user_journeys')
      .select(
        `
        *,
        journey_progress (*)
      `
      )
      .eq('user_id', userId);

    if (journeyId) {
      query = query.eq('id', journeyId);
    }

    if (!includeCompleted) {
      query = query.neq('status', 'completed');
    }

    const { data: journeys, error } = await query;

    if (error) throw new AppError(error.message, 400);

    // Calculate analytics
    const currentJourney = journeys?.find((j) => j.status === 'active');
    const completedJourneys =
      journeys?.filter((j) => j.status === 'completed') || [];

    const completionRate =
      completedJourneys.length > 0
        ? (completedJourneys.reduce(
            (acc, j) => acc + j.current_day / j.total_days,
            0
          ) /
            completedJourneys.length) *
          100
        : 0;

    // Calculate average session time from journey progress
    const allProgress =
      journeys?.flatMap((j) => j.journey_progress || []) || [];
    const completedSessions = allProgress.filter(
      (p) => p.status === 'completed'
    );
    const averageSessionTime =
      completedSessions.length > 0
        ? completedSessions.reduce(
            (acc, p) => acc + (p.session_duration || 0),
            0
          ) / completedSessions.length
        : 0;

    res.status(200).json({
      status: 'success',
      data: {
        currentJourney: currentJourney
          ? {
              id: currentJourney.id,
              progress:
                (currentJourney.current_day / currentJourney.total_days) * 100,
              daysCompleted: currentJourney.current_day,
              totalDays: currentJourney.total_days,
            }
          : null,
        completionRate: Math.round(completionRate * 100) / 100,
        averageSessionTime: Math.round(averageSessionTime * 100) / 100,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getHealingMetrics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { timeframe = 'month', metrics } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Get healing effectiveness metrics
    const { data: healingMetrics } = await supabase.rpc('get_healing_metrics', {
      user_id: userId,
      timeframe: timeframe as string,
      specific_metrics: metrics || null,
    });

    res.status(200).json({
      status: 'success',
      data: {
        metrics: healingMetrics || {},
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getAchievementAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { category, status = 'all' } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    let query = supabase
      .from('user_achievements')
      .select(
        `
        *,
        achievements (*)
      `
      )
      .eq('user_id', userId);

    if (category) {
      query = query.eq('achievements.category', category);
    }

    const { data: userAchievements, error } = await query;

    if (error) throw new AppError(error.message, 400);

    // Filter based on status
    let filteredAchievements = userAchievements || [];
    if (status === 'earned') {
      filteredAchievements = filteredAchievements.filter((a) => a.earned_at);
    } else if (status === 'in-progress') {
      filteredAchievements = filteredAchievements.filter(
        (a) => !a.earned_at && a.progress > 0
      );
    }

    // Calculate analytics
    const totalEarned = filteredAchievements.filter((a) => a.earned_at).length;
    const totalAvailable = filteredAchievements.length;
    const completionRate =
      totalAvailable > 0 ? (totalEarned / totalAvailable) * 100 : 0;

    res.status(200).json({
      status: 'success',
      data: {
        achievements: filteredAchievements,
        analytics: {
          totalEarned,
          totalAvailable,
          completionRate: Math.round(completionRate * 100) / 100,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getDashboardAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { modules, timeframe = 'week' } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Get dashboard analytics
    const { data: dashboardData } = await supabase.rpc(
      'get_dashboard_analytics',
      {
        user_id: userId,
        timeframe: timeframe as string,
        modules: modules || null,
      }
    );

    // Get recent activity
    const { data: recentActivity } = await supabase
      .from('user_activities')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    res.status(200).json({
      status: 'success',
      data: {
        dashboard: dashboardData || {},
        recentActivity: recentActivity || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getLayerAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { layerType } = req.params;
    const { timeframe = 'month', metrics } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Validate layer type
    const validLayers = ['jism', 'nafs', 'aql', 'qalb', 'ruh'];
    if (!validLayers.includes(layerType)) {
      throw new AppError('Invalid layer type', 400);
    }

    // Get layer-specific analytics
    const { data: layerAnalytics } = await supabase.rpc(
      'get_layer_specific_analytics',
      {
        user_id: userId,
        layer_type: layerType,
        timeframe: timeframe as string,
        specific_metrics: metrics || null,
      }
    );

    res.status(200).json({
      status: 'success',
      data: {
        layer: layerType,
        analytics: layerAnalytics || {},
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getStreakAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Get streak data
    const { data: streakData } = await supabase.rpc('get_user_streaks', {
      user_id: userId,
    });

    res.status(200).json({
      status: 'success',
      data: {
        currentStreak: streakData?.current_streak || 0,
        longestStreak: streakData?.longest_streak || 0,
        streakHistory: streakData?.streak_history || [],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getPracticeEffectiveness = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { practiceType, timeframe = 'month' } = req.query;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Get practice effectiveness data
    const { data: effectivenessData } = await supabase.rpc(
      'get_practice_effectiveness',
      {
        user_id: userId,
        practice_type: practiceType || null,
        timeframe: timeframe as string,
      }
    );

    res.status(200).json({
      status: 'success',
      data: {
        effectiveness: effectivenessData || {},
      },
    });
  } catch (error) {
    next(error);
  }
};

export const exportAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { format, timeframe = 'month', metrics } = req.query;
    const userId = req.user?.id;

    // For now, return JSON format (CSV and PDF export would require additional libraries)
    if (format === 'json') {
      const supabase = getSupabase();

      const { data: exportData } = await supabase.rpc('export_user_analytics', {
        user_id: userId,
        timeframe: timeframe as string,
        specific_metrics: metrics || null,
      });

      res.status(200).json({
        status: 'success',
        data: {
          exportData: exportData || {},
          format,
          generatedAt: new Date().toISOString(),
        },
      });
    } else {
      throw new AppError('Export format not yet supported', 400);
    }
  } catch (error) {
    next(error);
  }
};
