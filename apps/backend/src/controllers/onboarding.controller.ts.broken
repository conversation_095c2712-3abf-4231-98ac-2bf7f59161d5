import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { User as SupabaseUser } from '@supabase/supabase-js'; // Renamed to avoid conflict
import { Prisma } from '@prisma/client';
import { PrismaClientKnownRequestError as PrismaError } from '@prisma/client/runtime/library'; // Changed to regular import
import type { JsonObject } from '@prisma/client/runtime/library';
import { prisma } from '../config/database'; // Import Prisma client
import { AppError } from '../middleware/errorHandler';
import { OnboardingService } from '../services/onboarding.service';
import { CrisisDetectionService, CrisisAnalysis } from '../services/crisis-detection.service';
import logger from '../utils/logger';
import { UserProfile } from '../models/UserProfile'; // For UserProfile type
import { AuthenticatedRequest } from '../types/authenticated-request';

const onboardingService = new OnboardingService();
const crisisService = new CrisisDetectionService();

// Helper to create an empty profile if needed
function createEmptyProfile(userId: string): UserProfile {
  return {
    userId,
    // Defaulting to valid enum values from UserProfile model
    mentalHealthAwareness: { level: 'symptom_aware' },
    ruqyaKnowledge: { level: 'unaware' },
    professionalContext: { field: 'other' },
    demographics: { ageRange: '13-18', gender: 'prefer_not_to_specify', familyStatus: 'single' },
    lifeCircumstances: { culturalBackground: '', situations: [], islamicJourneyStage: 'new_muslim' },
    preferences: { timeAvailability: '5-10', learningPace: 'self_paced', communityEngagement: 'low', contentStyle: 'simple', islamicTerminology: 'basic' },
    completionStatus: 'incomplete',
    // Add missing mandatory fields from UserProfile with default values
    id: userId, // Assuming id can be same as userId for this helper
    crisisIndicators: { level: 'none', indicators: [], immediateHelpRequested: false },
    featureAccessibility: { feature1Level: 'basic', feature2Complexity: 'simple', ruqyaIntegration: 'none', communityAccess: 'observer', crisisSupport: 'standard'},
    learningHistory: { completedAssessments: [], behaviorPatterns: {}, progressMetrics: {}, lastUpdated: new Date() },
    privacySettings: { dataSharing: false, communityVisibility: 'private', analyticsConsent: false, crisisInterventionConsent: true },
    profileVersion: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Start a new onboarding session
 * POST /api/onboarding/start
 * This now primarily relies on OnboardingService which uses Prisma.
 */
export const startOnboarding = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Log validation errors
      const validationErrorDetails = errors.array().map(err => ({
        type: (err as any).type ?? '',
        msg: err.msg,
        path: (err as any).path ?? '',
        location: (err as any).location ?? '',
      }));
      logger.warn('Validation errors during startOnboarding', { clientIp: req.ip, errors: validationErrorDetails });
      throw new AppError('Invalid input data', 400, validationErrorDetails);
    }

    const { deviceInfo, forceRestart = false } = req.body;
    // req.user.id comes from auth middleware. If not present, it implies public access or different auth.
    // For onboarding, a user ID is crucial. Assume it's present or handle appropriately.
    if (!req.user?.id) {
        logger.warn('startOnboarding called without authenticated user ID.');
        throw new AppError('User authentication required to start onboarding.', 401);
    }
    const userIdFromToken = req.user.id;
    const userEmail = req.user.email; // Assuming email is available on req.user
    let effectiveUserId = userIdFromToken;

    // Check if a profile exists for the user ID from the token
    let userProfile = await prisma.profile.findUnique({
      where: { id: userIdFromToken },
    });

    if (!userProfile) {
      // No profile for this ID, check by email to see if it's a sync issue
      const profileByEmail = await prisma.profile.findUnique({
        where: { email: userEmail },
      });

      if (profileByEmail) {
        // A profile with this email exists but with a different ID.
        // This is the conflict state. We'll use the existing profile's ID.
        logger.warn(
          `User profile ID mismatch for email ${userEmail}. Token ID: ${userIdFromToken}, DB ID: ${profileByEmail.id}. Using existing DB profile to prevent data loss.`,
        );
        effectiveUserId = profileByEmail.id;
      } else {
        // No profile by ID or email, this is a genuinely new user. Create a profile.
        logger.info(`No profile found for user ${userIdFromToken}. Creating new profile.`);
        await prisma.profile.create({
          data: {
            id: userIdFromToken,
            email: userEmail,
          },
        });
        // effectiveUserId is already correctly set to userIdFromToken
      }
    }

    try {
      const { session, resumed } = await onboardingService.startOnboarding(effectiveUserId, deviceInfo, forceRestart);

      const firstQuestion = await onboardingService.getNextQuestion(
        session.sessionId,
        {}
      );

      res.status(201).json({
        status: 'success',
        data: {
          session: {
            sessionId: session.sessionId,
            startedAt: session.startedAt,
            currentStep: session.currentStep,
          },
          question: firstQuestion,
          resumed,
        },
      });

      logger.info('Onboarding started successfully (Prisma Service)', {
        userId: effectiveUserId,
        sessionId: session.sessionId,
      });
    } catch (serviceError: any) {
      // Handle the specific case where onboarding is already completed
      if (serviceError instanceof AppError && serviceError.statusCode === 409) {
        logger.info('Onboarding already completed for user', {
          userId: effectiveUserId,
          details: serviceError.details
        });
        
        res.status(409).json({
          status: 'already_completed',
          message: 'Onboarding has already been completed',
          data: {
            code: 'ONBOARDING_ALREADY_COMPLETED',
            completedAt: serviceError.details?.completedAt,
            message: 'User has already completed onboarding. To restart, use the restart endpoint.',
            actions: [
              {
                id: 'go_to_dashboard',
                text: 'Go to Dashboard',
                primary: true,
              },
              {
                id: 'restart_onboarding',
                text: 'Restart Onboarding',
                secondary: true,
              },
            ],
          },
        });
        return;
      }
      
      // Log the error for debugging
      logger.error('Error in startOnboarding controller', {
        userId: effectiveUserId,
        error: serviceError instanceof Error ? {
          name: serviceError.name,
          message: serviceError.message,
          stack: serviceError.stack
        } : serviceError
      });
      
      // Re-throw other errors to be handled by the global error handler
      throw serviceError;
    }
  } catch (error) {
    next(error); // Error already logged by service or will be by global handler
  }
};

/**
 * Submit response to current onboarding question
 * POST /api/onboarding/respond
 * Relies on OnboardingService.
 */
export const submitResponse = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const validationErrorDetails = errors.array().map(err => ({
        type: (err as any).type ?? '',
        msg: err.msg,
        path: (err as any).path ?? '',
        location: (err as any).location ?? '',
      }));
      logger.warn('Validation errors during submitResponse', { errors: validationErrorDetails });
      throw new AppError('Invalid input data', 400, validationErrorDetails);
    }

    const { sessionId, stepId, response, timeSpent } = req.body;
     if (!req.user?.id) {
        logger.warn('submitResponse called without authenticated user ID.');
        throw new AppError('User authentication required.', 401);
    }
    const userId = req.user.id; // Assuming user is authenticated

    const result = await onboardingService.submitResponse(
      sessionId,
      stepId,
      response,
      timeSpent || 0
    );

    // Add defensive check for undefined result
    if (!result) {
      logger.error('OnboardingService.submitResponse returned undefined/null result', {
        sessionId,
        stepId,
        userId
      });
      throw new AppError('Failed to process onboarding response', 500);
    }

    if (result.type === 'crisis_detected') {
      // CrisisDetectionService might also use Prisma if it logs to DB
      // Use the original crisis analysis if available, otherwise create a fallback
      const crisisAnalysis = result.originalCrisisAnalysis || {
        isCrisis: true,
        level: result.level || 'moderate',
        indicators: result.indicators || [],
        confidence: result.confidence || 0.8,
        recommendedActions: result.actions?.map((action: any) => action.id) || [],
        urgency: result.urgency || 'moderate'
      } as CrisisAnalysis;
      
      await crisisService.logCrisisEvent(userId, crisisAnalysis, {
        sessionId,
        stepId,
        response,
      });
      
      // Remove the originalCrisisAnalysis from the response to avoid exposing internal data
      const { originalCrisisAnalysis, ...responseData } = result;
      res.status(200).json({ status: 'crisis_detected', data: responseData });
      return;
    }

    if (result.profile) {
      res.status(200).json({
        status: 'completed',
        data: {
          profile: result.profile,
          recommendedPathway: result.recommendedPathway,
          featureConfiguration: result.featureConfiguration,
          nextSteps: result.nextSteps,
          warnings: result.warnings,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'continue',
      data: {
        question: result.question,
        progress: result.progress,
        step: result.step,
      },
    });
    logger.info('Onboarding response submitted (Prisma Service)', { userId, sessionId, stepId });
  } catch (error) {
    next(error);
  }
};

/**
 * Resume an incomplete onboarding session
 * POST /api/onboarding/resume
 * Relies on OnboardingService.
 */
export const resumeOnboarding = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sessionId } = req.body;
     if (!req.user?.id) {
        logger.warn('resumeOnboarding called without authenticated user ID.');
        throw new AppError('User authentication required.', 401);
    }
    const userId = req.user.id;

    const nextQuestion = await onboardingService.getNextQuestion(sessionId, {});
    res.status(200).json({
      status: 'success',
      data: { sessionId, question: nextQuestion },
    });
    logger.info('Onboarding resumed (Prisma Service)', { userId, sessionId });
  } catch (error) {
    next(error);
  }
};

/**
 * Restart onboarding from the beginning
 * POST /api/onboarding/restart
 * This abandons any existing sessions and starts fresh.
 */
export const restartOnboarding = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const validationErrorDetails = errors.array().map(err => ({
        type: (err as any).type ?? '',
        msg: err.msg,
        path: (err as any).path ?? '',
        location: (err as any).location ?? '',
      }));
      logger.warn('Validation errors during restartOnboarding', { errors: validationErrorDetails });
      throw new AppError('Invalid input data', 400, validationErrorDetails);
    }

    const { deviceInfo } = req.body;
    if (!req.user?.id) {
        logger.warn('restartOnboarding called without authenticated user ID.');
        throw new AppError('User authentication required.', 401);
    }
    const userId = req.user.id;

    // Force restart by passing true as the third parameter
    const { session, resumed } = await onboardingService.startOnboarding(userId, deviceInfo, true);

    const firstQuestion = await onboardingService.getNextQuestion(
      session.sessionId,
      {}
    );

    res.status(201).json({
      status: 'success',
      data: {
        session: {
          sessionId: session.sessionId,
          startedAt: session.startedAt,
          currentStep: session.currentStep,
        },
        question: firstQuestion,
        restarted: true,
      },
    });

    logger.info('Onboarding restarted successfully', {
      userId,
      sessionId: session.sessionId,
    });
  } catch (error) {
    next(error);
  }
};

// This specific submitOnboarding seems like a legacy direct submission.
// The main flow is through startOnboarding -> submitResponse (step-by-step).
// If this is still used, it needs to align with UserProfileDetailed and its profileData JSON field.
export const submitOnboarding = async (
  req: AuthenticatedRequest, // Changed to AuthenticatedRequest
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map((error) => error.msg).join(', ');
      logger.warn('Validation errors in submitOnboarding', { errors: errors.array() });
      throw new AppError(errorMessages, 400, errors.array());
    }

    const { personalInfo, preferences } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Construct the profileData object to be stored in the JSON field
    // of UserProfileDetailed model.
    const profileDetailsToStore: UserProfile = {
        userId,
        id: userId, // Assuming id can be the same as userId
        // Map personalInfo and preferences to UserProfile structure, ensuring all fields are valid enums
        mentalHealthAwareness: { level: personalInfo.awarenessLevel || 'symptom_aware' },
        ruqyaKnowledge: { level: personalInfo.ruqyaFamiliarity || 'unaware' },
        professionalContext: { field: personalInfo.profession || 'other' },
        demographics: {
            ageRange: personalInfo.age || '13-18',
            gender: personalInfo.gender || 'prefer_not_to_specify',
            familyStatus: personalInfo.familyStatus || 'single',
        },
        lifeCircumstances: {
            culturalBackground: personalInfo.culturalBackground || '',
            situations: personalInfo.situations || [],
            islamicJourneyStage: personalInfo.islamicJourneyStage || 'new_muslim',
        },
        preferences: {
            timeAvailability: preferences.timeAvailability || '5-10',
            learningPace: preferences.learningStyle || 'self_paced',
            communityEngagement: preferences.communityParticipation || 'low',
            contentStyle: preferences.contentStyle || 'simple',
            islamicTerminology: preferences.islamicTerminology || 'basic',
        },
        completionStatus: 'complete',
        // Add missing mandatory fields from UserProfile with default values
        crisisIndicators: { level: 'none', indicators: [], immediateHelpRequested: false },
        featureAccessibility: { feature1Level: 'basic', feature2Complexity: 'simple', ruqyaIntegration: 'none', communityAccess: 'observer', crisisSupport: 'standard'},
        learningHistory: { completedAssessments: [], behaviorPatterns: {}, progressMetrics: {}, lastUpdated: new Date() },
        privacySettings: { dataSharing: false, communityVisibility: 'private', analyticsConsent: false, crisisInterventionConsent: true },
        profileVersion: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
    };


    const upsertedProfile = await prisma.userProfileDetailed.upsert({
      where: { userId: userId },
      create: {
        userId: userId,
        profileData: profileDetailsToStore as any, // TODO: Use correct Prisma JsonValue type
        completionStatus: 'complete', // Or derive from profileDetailsToStore
        profileVersion: '1.0.0', // Default version
      },
      update: {
        profileData: profileDetailsToStore as any, // TODO: Use correct Prisma JsonValue type
        completionStatus: 'complete',
      },
    });

    res.status(200).json({
      status: 'success',
      data: {
        // Return data that matches UserProfileDetailed structure or a simplified view
        profile: {
            userId: upsertedProfile.userId,
            profileData: upsertedProfile.profileData,
            completionStatus: upsertedProfile.completionStatus,
            updatedAt: upsertedProfile.updatedAt,
        },
        nextStep: 'assessment',
      },
    });
    logger.info('Onboarding submitted successfully via direct route (Prisma)', { userId });
  } catch (error) {
     if (error instanceof PrismaError) { // Use imported PrismaError
        logger.error('Prisma error in submitOnboarding', { userId: req.user?.id, code: error.code, meta: error.meta });
        // Handle specific Prisma errors, e.g., unique constraint violation if not using upsert properly
        next(new AppError('Database error during onboarding submission.', 500, error));
    } else {
        next(error);
    }
  }
};

/**
 * Get session-specific onboarding status
 * GET /api/onboarding/session-status/:sessionId
 * Relies on OnboardingService.
 */
export const getSessionStatus = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sessionId } = req.params;
    if (!req.user?.id) {
        logger.warn('getSessionStatus called without authenticated user ID.');
        throw new AppError('User authentication required.', 401);
    }
    const userId = req.user.id;

    // onboardingService.getSessionStatus might not exist or need adjustment
    // For now, let's assume it fetches from prisma.onboardingSession
    const session = await prisma.onboardingSession.findUnique({
        where: { sessionId }
    });

    if (!session) {
        throw new AppError('Session not found', 404);
    }
    // Ensure only the owner of the session can access it, if that's a requirement
    if (session.userId !== userId) {
        logger.warn('User attempted to access unauthorized session status', { authUserId: userId, sessionUserId: session.userId, sessionId });
        throw new AppError('Forbidden: You do not have access to this session', 403);
    }

    res.status(200).json({
      status: 'success',
      data: { // Map to a defined DTO if needed
        sessionId: session.sessionId,
        userId: session.userId,
        currentStep: session.currentStep,
        completedAt: session.completedAt,
        totalTimeSpent: session.totalTimeSpent,
        // Add other relevant fields from OnboardingSession model
      },
    });
    logger.info('Session status retrieved (Prisma)', { userId, sessionId });
  } catch (error) {
     if (error instanceof PrismaError && error.code === 'P2025') { // Use imported PrismaError
        next(new AppError('Session not found', 404));
    } else {
        next(error);
    }
  }
};

/**
 * Check if user has any incomplete onboarding sessions
 * GET /api/onboarding/session-status
 */
export const getUserSessionStatus = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user?.id) {
        logger.warn('getUserSessionStatus called without authenticated user ID.');
        throw new AppError('User authentication required.', 401);
    }
    const userId = req.user.id;

    // Check for incomplete sessions
    const incompleteSession = await prisma.onboardingSession.findFirst({
      where: {
        userId,
        completedAt: null,
        abandonedAt: null,
      },
      orderBy: {
        startedAt: 'desc',
      },
    });

    res.status(200).json({
      status: 'success',
      data: {
        hasIncompleteSession: !!incompleteSession,
        session: incompleteSession ? {
          sessionId: incompleteSession.sessionId,
          currentStep: incompleteSession.currentStep,
          startedAt: incompleteSession.startedAt,
          totalTimeSpent: incompleteSession.totalTimeSpent,
        } : null,
      },
    });
    logger.info('User session status retrieved', { userId, hasIncompleteSession: !!incompleteSession });
  } catch (error) {
    logger.error('Error getting user session status', { userId: req.user?.id, error: error instanceof Error ? error.message : error });
    next(error);
  }
};

export const getOnboardingStatus = async (
  req: AuthenticatedRequest, // Changed to AuthenticatedRequest
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const userProfileDetailed = await prisma.userProfileDetailed.findUnique({
      where: { userId: userId },
    });

    if (!userProfileDetailed) {
      res.status(200).json({
        status: 'success',
        data: {
          userId: userId, // Added userId here
          onboardingCompleted: false,
          profile: null,
          nextStep: 'onboarding',
        },
      });
      return;
    }

    // Assuming 'onboarding_completed' is part of the profileData JSON or a direct field
    // For this example, let's assume it's derived from completionStatus
    const onboardingCompleted = userProfileDetailed.completionStatus === 'complete';

    res.status(200).json({
      status: 'success',
      data: {
        onboardingCompleted: onboardingCompleted,
        profile: userProfileDetailed.profileData, // Return the JSON profile data
        nextStep: onboardingCompleted ? 'assessment' : 'onboarding',
      },
    });
    logger.info('Onboarding status retrieved (Prisma)', { userId });
  } catch (error) {
    if (error instanceof PrismaError && error.code === 'P2025') { // Use imported PrismaError
        // This case should be handled by the !userProfileDetailed check above,
        // but good to have for other potential Prisma errors.
        logger.warn('User profile not found for getOnboardingStatus (Prisma)', { userId: req.user?.id }); // Use req.user?.id
        res.status(200).json({ // Consistent with "not found means not completed"
            status: 'success',
            data: { userId: req.user?.id, onboardingCompleted: false, profile: null, nextStep: 'onboarding' },
        });
        return;
    }
    logger.error('Error fetching onboarding status (Prisma)', { userId: req.user?.id, error: error instanceof Error ? error.message : error }); // Use req.user?.id
    next(error);
  }
};

export const updateOnboarding = async (
  req: AuthenticatedRequest, // Changed to AuthenticatedRequest
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map((error) => error.msg).join(', ');
      logger.warn('Validation errors in updateOnboarding', { errors: errors.array() });
      throw new AppError(errorMessages, 400, errors.array());
    }

    const { preferences } = req.body; // Assuming preferences are to be updated in profileData
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const existingProfile = await prisma.userProfileDetailed.findUnique({
        where: { userId }
    });

    if (!existingProfile) {
        throw new AppError('Profile not found to update.', 404);
    }

    // Merge new preferences into existing profileData
    const currentProfileData = (existingProfile.profileData as UserProfile | null) || createEmptyProfile(userId);
    const updatedProfileDataPart: Partial<UserProfile['preferences']> = {
        timeAvailability: preferences.timeAvailability,
        learningPace: preferences.learningStyle, // Map learningStyle to learningPace
        communityEngagement: preferences.communityParticipation,
    };

    const newProfileData = {
        ...currentProfileData,
        preferences: {
            ...(currentProfileData.preferences),
            ...updatedProfileDataPart,
        },
        updatedAt: new Date(), // Manually set updatedAt if it's part of JSON
    };

    const updatedProfile = await prisma.userProfileDetailed.update({
      where: { userId: userId },
      data: {
        profileData: newProfileData as any, // TODO: Use correct Prisma JsonValue type
        // completionStatus might also need update if this implies a change
      },
    });

    res.status(200).json({
      status: 'success',
      data: {
        profile: updatedProfile.profileData, // Return updated profile data
      },
    });
    logger.info('Onboarding preferences updated (Prisma)', { userId });
  } catch (error) {
    if (error instanceof PrismaError && error.code === 'P2025') { // Use imported PrismaError
        next(new AppError('Profile not found for update.', 404));
    } else {
        logger.error('Error updating onboarding preferences (Prisma)', { userId: req.user?.id, error: error instanceof Error ? error.message : error });
        next(error);
    }
  }
};

/**
 * Skip onboarding (emergency bypass)
 * POST /api/onboarding/skip
 * This typically doesn't involve DB writes for the skip itself, but might create a minimal profile.
 * The current implementation is a mock response; if it were to save this minimalProfile,
 * it would use prisma.userProfileDetailed.create or .upsert.
 */
export const skipOnboarding = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { reason } = req.body;
    const userId = req.user?.id ?? 'dev-user'; // Fallback for dev, ensure req.user.id in prod

    // Construct a minimal profile for skip
    const minimalProfile: UserProfile = {
      userId,
      id: userId, // Assuming id can be the same as userId
      completionStatus: 'skipped' as UserProfile['completionStatus'],
      // Provide default valid enum values for all mandatory fields
      mentalHealthAwareness: { level: 'symptom_aware' },
      ruqyaKnowledge: { level: 'unaware' },
      professionalContext: { field: 'other' },
      demographics: { ageRange: '13-18', gender: 'prefer_not_to_specify', familyStatus: 'single' },
      lifeCircumstances: { culturalBackground: '', situations: [], islamicJourneyStage: 'new_muslim' },
      preferences: { timeAvailability: '5-10', learningPace: 'self_paced', communityEngagement: 'low', contentStyle: 'simple', islamicTerminology: 'basic' },
      crisisIndicators: { level: 'none', indicators: [], immediateHelpRequested: false },
      featureAccessibility: { feature1Level: 'basic', feature2Complexity: 'simple', ruqyaIntegration: 'none', communityAccess: 'observer', crisisSupport: 'standard'},
      learningHistory: { completedAssessments: [], behaviorPatterns: {}, progressMetrics: {}, lastUpdated: new Date() },
      privacySettings: { dataSharing: false, communityVisibility: 'private', analyticsConsent: false, crisisInterventionConsent: true },
      profileVersion: '1.0.0',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    res.status(200).json({
      status: 'success',
      data: {
        profile: minimalProfile, // This is a locally constructed object
        recommendedPathway:
          reason === 'crisis' ? 'crisis_support' : 'gentle_introduction',
        nextSteps:
          reason === 'crisis'
            ? ['Access Qalb Rescue', 'Connect with crisis support']
            : ['Complete profile later', 'Begin gentle introduction'],
        warnings: ['Profile incomplete - limited personalization available'],
      },
    });
    logger.warn('Onboarding skipped', { userId, reason });
  } catch (error) {
    next(error);
  }
};

/**
 * Get onboarding analytics (admin only)
 * GET /api/onboarding/analytics
 * If this were to fetch real data, it would query Prisma models like OnboardingSession, UserProfileDetailed.
 */
export const getOnboardingAnalytics = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Admin check middleware should be in place for this route.
    // Example of what real data fetching might look like (conceptual):
    // const totalSessions = await prisma.onboardingSession.count();
    // const completedSessions = await prisma.onboardingSession.count({ where: { currentStep: 'complete' } });
    // const completionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0;
    // ... more complex aggregations for dropoffPoints, pathwayDistribution etc.

    const analytics = {
      totalSessions: 120,
      completionRate: 68.3,
      averageTimeToComplete: 240,  // seconds
      dropoffPoints: {
        welcome: 5,
        awareness: 12,
        ruqya_knowledge: 21,
        personal_context: 8,
        preferences: 2
      },
      pathwayDistribution: {
        beginner: 45,
        intermediate: 32,
        advanced: 23
      },
      crisisDetections: {
        total: 3,
        resolved: 2
      }
    };
    res.status(200).json({ status: 'success', data: analytics });
  } catch (error) {
    logger.error('Error fetching onboarding analytics', { error: error instanceof Error ? error.message : error });
    next(error);
  }
};

/**
 * Update user profile after onboarding
 * PUT /api/onboarding/profile
 * This is similar to updateOnboarding but might have a different scope of updates.
 */
export const updateProfile = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.warn('Validation errors in updateProfile (onboarding)', { errors: errors.array() });
      throw new AppError('Invalid input data', 400, errors.array());
    }

    const { profileUpdates, reason } = req.body as { profileUpdates: Partial<UserProfile>, reason?: string };
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }
    if (!profileUpdates || typeof profileUpdates !== 'object' || Object.keys(profileUpdates).length === 0) {
      throw new AppError('Invalid or empty profileUpdates provided', 400);
    }

    const existingProfileDetailed = await prisma.userProfileDetailed.findUnique({
        where: { userId }
    });

    if (!existingProfileDetailed) {
        throw new AppError('Profile not found to update.', 404);
    }

    // Merge updates into the existing profileData JSON
    const currentProfileData = (existingProfileDetailed.profileData as UserProfile | null) || createEmptyProfile(userId);

    // A more robust merge would be needed if profileUpdates contains nested objects to merge
    // For now, assuming profileUpdates are relatively flat or will overwrite sections.
    const newProfileData = {
        ...currentProfileData,
        ...profileUpdates,
        // updatedAt: new Date() // Update timestamp within the JSON if UserProfile model has it. UserProfile does not have updatedAt.
    };

    const updatedDbProfile = await prisma.userProfileDetailed.update({
      where: { userId: userId },
      data: {
        profileData: newProfileData as any, // TODO: Use correct Prisma JsonValue type
        // Potentially update completionStatus or profileVersion if relevant
        // completionStatus: newProfileData.completionStatus || existingProfileDetailed.completionStatus,
      },
    });

    res.status(200).json({
      status: 'success',
      data: {
        message: 'Profile updated successfully',
        profile: updatedDbProfile.profileData, // Return the updated profile_data
        updatedAt: updatedDbProfile.updatedAt, // This is UserProfileDetailed.updatedAt
      },
    });
    logger.info('Profile updated via onboarding route (Prisma)', { userId, reason, updatedFields: Object.keys(profileUpdates) });
  } catch (error) {
     if (error instanceof PrismaError && error.code === 'P2025') { // Use imported PrismaError
        next(new AppError('Profile not found for update.', 404));
    } else {
        logger.error('Error updating profile via onboarding route (Prisma)', { userId: req.user?.id, error: error instanceof Error ? error.message : error });
        next(error);
    }
  }
};

export const resetOnboardingSession = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const validationErrorDetails = errors.array().map(err => ({
        type: (err as any).type ?? '',
        msg: err.msg,
        path: (err as any).path ?? '',
        location: (err as any).location ?? '',
      }));
      logger.warn('Validation errors during resetOnboardingSession', { errors: validationErrorDetails });
      throw new AppError('Invalid input data', 400, validationErrorDetails);
    }

    const { userId } = req.body; // Get userId from request body for admin reset

    await onboardingService.resetOnboardingSession(userId);

    res.status(200).json({
      status: 'success',
      message: `Onboarding session for user ${userId} reset successfully.`,
    });
    logger.info('Onboarding session reset by admin', { adminUserId: req.user?.id, targetUserId: userId });
  } catch (error) {
    logger.error('Error resetting onboarding session by admin', { adminUserId: req.user?.id, error: error instanceof Error ? error.message : error });
    next(error);
  }
};
