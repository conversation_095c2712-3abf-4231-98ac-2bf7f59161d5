import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
// import { getSupabase } from '../config/supabase'; // Now using service for DB interactions
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
// import { triggerN8nWorkflow } from '../services/n8n.service'; // N8N calls are in service
import { emergencyService } from '../services/emergency.service'; // Import the refactored service

export const startQalbRescueSessionController = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Create a more detailed error message
      const errorMessages = errors.array().map(err => err.msg).join(', ');
      throw new AppError(`Invalid input: ${errorMessages}`, 400);
    }

    const { triggerType = 'manual', currentSymptoms = [] } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // TODO: Implement rate limiting here or ensure it's handled if moved to service.
    // For now, assuming rate limiting is less critical than getting the flow working.
    // The previous rate limiting logic:
    // const supabase = getSupabase();
    // const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    // const { data: recentSessions } = await supabase
    //   .from('emergency_sessions')
    //   .select('id')
    //   .eq('user_id', userId)
    //   .gte('start_time', oneHourAgo.toISOString());
    // if (recentSessions && recentSessions.length >= 5) {
    //   throw new AppError(
    //     'Too many emergency sessions started. Please wait before starting another.',
    //     429
    //   );
    // }

    const { session, initialStepContent } = await emergencyService.startQalbRescueSession(
      userId,
      triggerType,
      currentSymptoms
    );

    res.status(201).json({
      status: 'success',
      data: {
        sessionId: session.id,
        startTime: session.startTime,
        currentStep: session.currentStep,
        stepContent: initialStepContent,
        // recommendedActions: session.recommendedActions, // This might come from N8N via service or initialStepContent
        // estimatedDuration: session.estimatedDuration, // This might come from N8N via service or initialStepContent
      },
    });
  } catch (error) {
    next(error);
  }
};

export const progressQalbRescueSessionController = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(err => err.msg).join(', ');
      throw new AppError(`Invalid input: ${errorMessages}`, 400);
    }

    const { id: sessionId } = req.params;
    const { completedStep, timeSpentOnStep } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }
    if (!completedStep) {
        throw new AppError('completedStep is required in the request body', 400);
    }

    const result = await emergencyService.progressQalbRescueSession(
      sessionId,
      userId,
      completedStep,
      timeSpentOnStep
    );

    if (!result) {
      // Session likely completed
      res.status(200).json({
        status: 'success',
        message: 'Qalb Rescue session completed.',
        data: {
          sessionId,
          currentStep: null, // Or a specific 'completed' step name
          stepContent: null,
        }
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        sessionId,
        currentStep: result.currentStep,
        stepContent: result.nextStepContent,
      },
    });
  } catch (error) {
    next(error);
  }
};

// Content fetching methods like getGuidedBreathing, getDhikrOverlay, getEmergencyRuqyah
// are removed as content is now delivered via startQalbRescueSessionController and progressQalbRescueSessionController.
// If these endpoints are needed for other purposes, they can be kept, but they are not part of the core Qalb Rescue flow anymore.

export const updateQalbRescueSessionController = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(err => err.msg).join(', ');
      throw new AppError(`Invalid input: ${errorMessages}`, 400);
    }

    const { id: sessionId } = req.params;
    const { status, feedback, effectivenessRating, reasonForEnding } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    if (feedback || effectivenessRating !== undefined) {
      await emergencyService.recordUserFeedback(sessionId, userId, effectivenessRating, feedback);
    }

    if (status === 'completed' || status === 'aborted') {
      await emergencyService.endQalbRescueSession(sessionId, userId, reasonForEnding || status);
    } else if (status) {
      // Potentially handle other status updates if needed, though Qalb Rescue flow primarily uses active/completed/aborted.
      // For now, we only explicitly end the session if status is 'completed' or 'aborted'.
      // Direct status updates to 'active' without flow progression should be handled carefully.
      logger.warn('Received session status update without completion/abortion', { sessionId, status });
    }

    // Fetch the latest session state to return
    // This might be redundant if the service calls already return the updated session.
    // For now, let's assume the client just needs a success message.
    // const updatedSession = await emergencyService.getSessionState(sessionId, userId); // Assuming such a method exists

    res.status(200).json({
      status: 'success',
      message: 'Session updated successfully.',
      data: { sessionId }, // Potentially return updatedSession here
    });
  } catch (error) {
    next(error);
  }
};

// --- Community Feature Controllers (Phase 2 placeholders) ---
export const requestDuaController = async (
    req: Request,
    res: Response,
    next: NextFunction
): Promise<void> => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
             const errorMessages = errors.array().map(err => err.msg).join(', ');
             throw new AppError(`Invalid input: ${errorMessages}`, 400);
        }
        const { id: sessionId } = req.params;
        const userId = req.user?.id;

        if (!userId) {
            throw new AppError('User not authenticated', 401);
        }

        await emergencyService.requestDuaFromCommunity(sessionId, userId);
        res.status(200).json({ status: 'success', message: "Du'a request initiated." });
    } catch (error) {
        next(error);
    }
};

export const connectPeerSupporterController = async (
    req: Request,
    res: Response,
    next: NextFunction
): Promise<void> => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map(err => err.msg).join(', ');
            throw new AppError(`Invalid input: ${errorMessages}`, 400);
        }
        const { id: sessionId } = req.params;
        const userId = req.user?.id;
        const { criteria } = req.body; // Optional criteria for matching

        if (!userId) {
            throw new AppError('User not authenticated', 401);
        }

        const result = await emergencyService.findPeerSupporter(sessionId, userId, criteria);
        res.status(200).json({ status: 'success', data: result });
    } catch (error) {
        next(error);
    }
};


// Keeping saveSessionToJournal, getEmergencyHelplines, reportCrisis, getCrisisStatus, submitFollowUp as they are,
// assuming they might be used by other parts of the app or are general emergency features.
// They might need adaptation if their underlying Supabase calls are also moved to the service.
// For now, focus is on the core Qalb Rescue flow.

export const saveSessionToJournal = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(err => err.msg).join(', ');
      throw new AppError(`Invalid input: ${errorMessages}`, 400);
    }

    const { id } = req.params;
    const { notes, tags } = req.body;
    const userId = req.user?.id; // Added to ensure user context

    if (!userId) {
        throw new AppError('User not authenticated', 401);
    }
    // This function still uses direct Supabase calls.
    // TODO: Consider moving this logic to emergencyService if it's tightly coupled with EmergencySession.
    const supabase = require('../config/supabase').getSupabase();


    // Get session details
    const { data: session, error: sessionError } = await supabase
      .from('emergency_sessions')
      .select('*')
      .eq('id', id)
      .eq('user_id', req.user?.id)
      .single();

    if (sessionError) throw new AppError('Session not found', 404);

    // Create journal entry
    const { data: journalEntry, error: journalError } = await supabase
      .from('journal_entries')
      .insert({
        user_id: req.user?.id,
        title: `Emergency Session - ${new Date(
          session.start_time
        ).toLocaleDateString()}`,
        content: `Emergency session completed. ${
          notes || 'No additional notes.'
        }`,
        entry_type: 'emergency_session',
        tags: tags || ['emergency', 'sakina_mode'],
        related_data: {
          sessionId: id,
          duration: session.end_time
            ? Math.round(
                (new Date(session.end_time).getTime() -
                  new Date(session.start_time).getTime()) /
                  60000
              )
            : null,
          effectivenessRating: session.effectiveness_rating,
        },
        created_at: new Date(),
      })
      .select()
      .single();

    if (journalError) throw new AppError(journalError.message, 400);

    res.status(201).json({
      status: 'success',
      data: {
        journalEntryId: journalEntry.id,
        savedAt: journalEntry.created_at,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getEmergencyHelplines = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { country = 'US' } = req.query;
    // This function still uses direct Supabase calls.
    // TODO: Consider moving this logic to emergencyService.
    const supabase = require('../config/supabase').getSupabase();

    const { data: helplines, error } = await supabase
      .from('emergency_helplines')
      .select('*')
      .eq('country', country)
      .eq('is_active', true)
      .order('priority', { ascending: true });

    if (error) {
      // Fallback helplines
      const fallbackHelplines = [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          description: '24/7 crisis support',
          availability: '24/7',
          country: 'US',
          isIslamic: false,
        },
        {
          name: 'Crisis Text Line',
          phone: 'Text HOME to 741741',
          description: 'Free 24/7 crisis support via text',
          availability: '24/7',
          country: 'US',
          isIslamic: false,
        },
      ];

      res.status(200).json({
        status: 'success',
        data: {
          helplines: fallbackHelplines,
        },
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        helplines,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const reportCrisis = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors
        .array()
        .map((error) => error.msg)
        .join(', ');
      throw new AppError(errorMessages, 400);
    }

    const {
      severity,
      indicators,
      userResponse,
      assessmentContext,
      immediateRisk,
    } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Generate unique escalation ID
    const escalationId = `crisis-escalation-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Create crisis escalation record
    const { data: escalation, error } = await supabase
      .from('crisis_escalations')
      .insert({
        escalation_id: escalationId,
        user_id: userId,
        severity,
        indicators,
        context: assessmentContext || {},
        immediate_risk: immediateRisk || false,
        status: 'active',
        actions_taken: ['emergency_contacts_notified', 'resources_provided'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    logger.info('Crisis escalation created', {
      userId,
      escalationId,
      severity,
    });

    // Get emergency resources
    const emergencyResources = {
      hotlines: [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          availability: '24/7',
        },
      ],
      islamicCounselors: [
        {
          name: 'Islamic Crisis Support',
          contact: '******-ISLAMIC',
          specialization: 'Islamic Mental Health',
        },
      ],
      emergencyPrayers: [
        {
          title: 'Dua for Distress',
          arabic: 'لا إله إلا الله العظيم الحليم',
          transliteration: 'La ilaha illa Allah al-Azeem al-Haleem',
          translation: 'There is no god but Allah, the Great, the Gentle',
        },
      ],
    };

    res.status(200).json({
      status: 'success',
      data: {
        escalationId,
        immediateActions: ['immediate_professional_help', 'emergency_contacts'],
        emergencyResources,
        followUpScheduled: true,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getCrisisStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { escalationId } = req.params;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // First check if escalation exists at all
    const { data: escalationCheck, error: checkError } = await supabase
      .from('crisis_escalations')
      .select('user_id')
      .eq('escalation_id', escalationId)
      .single();

    if (checkError && checkError.code === 'PGRST116') {
      throw new AppError('Crisis escalation not found', 404);
    }
    if (checkError) throw new AppError(checkError.message, 400);

    // Check if escalation belongs to the user
    if (escalationCheck.user_id !== userId) {
      throw new AppError('Access denied to this escalation', 403);
    }

    // Get full escalation data
    const { data: escalation, error } = await supabase
      .from('crisis_escalations')
      .select('*')
      .eq('escalation_id', escalationId)
      .eq('user_id', userId)
      .single();

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        escalation: {
          id: escalation.escalation_id,
          severity: escalation.severity,
          status: escalation.status,
          actionsTaken: escalation.actions_taken,
          followUpScheduled: true,
          created_at: escalation.created_at,
          updated_at: escalation.updated_at,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const submitFollowUp = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors
        .array()
        .map((error) => error.msg)
        .join(', ');
      throw new AppError(errorMessages, 400);
    }

    const { escalationId, status, notes, additionalSupport } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Verify escalation exists and belongs to user
    const { data: escalation, error: escalationError } = await supabase
      .from('crisis_escalations')
      .select('id')
      .eq('escalation_id', escalationId)
      .eq('user_id', userId)
      .single();

    if (escalationError && escalationError.code === 'PGRST116') {
      throw new AppError('Crisis escalation not found', 404);
    }
    if (escalationError) throw new AppError(escalationError.message, 400);

    // Create follow-up record
    const { data: followUp, error: followUpError } = await supabase
      .from('crisis_followups')
      .insert({
        escalation_id: escalation.id,
        user_id: userId,
        status,
        notes,
        additional_support: additionalSupport || false,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (followUpError) throw new AppError(followUpError.message, 400);

    logger.info('Crisis follow-up submitted', { userId, escalationId, status });

    res.status(200).json({
      status: 'success',
      data: {
        followUpRecorded: true,
        nextSteps: ['continue_monitoring', 'schedule_check_in'],
      },
    });
  } catch (error) {
    next(error);
  }
};

export const escalateToProfessional = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { sessionId, escalationType, notes } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    await emergencyService.escalateToProfessional(userId, sessionId, escalationType, notes);

    res.status(200).json({
      status: 'success',
      message: 'Crisis session escalated successfully',
    });
  } catch (error) {
    next(error);
  }
};

export const getProfessionalContacts = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { specialization, location } = req.query;

    const professionals = await emergencyService.getProfessionalContacts(
      specialization as string,
      location as string
    );

    res.status(200).json({
      status: 'success',
      data: {
        professionals,
      },
    });
  } catch (error) {
    next(error);
  }
};
