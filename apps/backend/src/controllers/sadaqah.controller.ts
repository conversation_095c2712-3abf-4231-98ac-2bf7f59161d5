import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import crypto from 'crypto';
import { User } from '@supabase/supabase-js';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { triggerN8nWorkflow } from '../services/n8n.service';
import { logger } from '../utils/logger';
import { AuthenticatedRequest } from '../types/authenticated-request';

interface GiftJourney {
  id: string;
  donorId: string;
  recipientEmail: string;
  journeyType: string;
  durationDays: number;
  personalMessage?: string;
  invitationCode: string;
  status: 'pending' | 'claimed' | 'expired' | 'cancelled';
  expiryDate: Date;
  claimedAt?: Date;
  claimedBy?: string;
}

interface PaymentResult {
  success: boolean;
  transactionId?: string;
  message?: string;
}

/**
 * <PERSON>aqah Gift Controller - Manages gift journeys and premium passes
 */
export class Sadaq<PERSON><PERSON>ontroller {
  /**
   * @desc    Create a new gift journey
   * @route   POST /api/sadaqah/gifts/create
   * @access  Private
   */
  async createGiftJourney(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new AppError('Invalid input data', 400);
      }

      const donorId = req.user.id;
      const {
        recipientEmail,
        journeyType,
        durationDays,
        personalMessage,
        paymentMethod,
        paymentToken,
      } = req.body;

      // Generate unique invitation code
      const invitationCode = this.generateInvitationCode();

      // Process payment first
      const paymentResult = await this.processPayment(
        paymentMethod,
        paymentToken,
        journeyType,
        durationDays
      );

      if (!paymentResult.success) {
        throw new AppError(
          paymentResult.message || 'Payment processing failed',
          400
        );
      }

      const supabase = getSupabase();

      // Calculate expiry date (default: 60 days from now)
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 60);

      // Create gift record
      const { data: gift, error: giftError } = await supabase
        .from('gift_journeys')
        .insert({
          donor_id: donorId,
          recipient_email: recipientEmail,
          journey_type: journeyType,
          duration_days: durationDays,
          personal_message: personalMessage,
          invitation_code: invitationCode,
          status: 'pending',
          expiry_date: expiryDate.toISOString(),
          payment_transaction_id: paymentResult.transactionId,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (giftError) throw new AppError(giftError.message, 400);

      // Send invitation email
      await this.sendGiftInvitation(
        recipientEmail,
        invitationCode,
        personalMessage,
        donorId
      );

      // Log the gift creation
      await this.logSadaqahActivity(donorId, 'gift_created', {
        giftId: gift.id,
        recipientEmail,
        journeyType,
        durationDays,
      });

      logger.info('Gift journey created', {
        donorId,
        giftId: gift.id,
        recipientEmail,
        journeyType,
      });

      res.status(201).json({
        status: 'success',
        data: {
          giftId: gift.id,
          invitationCode,
          expiryDate,
          message: 'Gift journey created successfully',
        },
      });
    } catch (error) {
      logger.error('Error creating gift journey', {
        donorId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * @desc    Claim a gift journey
   * @route   POST /api/sadaqah/gifts/claim
   * @access  Public
   */
  async claimGiftJourney(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { invitationCode, userEmail } = req.body;

      if (!invitationCode || !userEmail) {
        throw new AppError('Invitation code and email are required', 400);
      }

      const supabase = getSupabase();

      // Find the gift by invitation code
      const { data: gift, error: giftError } = await supabase
        .from('gift_journeys')
        .select('*')
        .eq('invitation_code', invitationCode)
        .eq('status', 'pending')
        .single();

      if (giftError || !gift) {
        throw new AppError('Invalid or expired invitation code', 404);
      }

      // Check if gift has expired
      if (new Date() > new Date(gift.expiry_date)) {
        await supabase
          .from('gift_journeys')
          .update({ status: 'expired' })
          .eq('id', gift.id);

        throw new AppError('This gift invitation has expired', 400);
      }

      // Check if email matches (optional validation)
      if (gift.recipient_email && gift.recipient_email !== userEmail) {
        throw new AppError(
          'This gift is intended for a different email address',
          403
        );
      }

      // Create or get user account
      const userId = await this.getOrCreateUser(userEmail);

      // Create the journey for the user
      const { data: journey, error: journeyError } = await supabase
        .from('user_journeys')
        .insert({
          user_id: userId,
          journey_type: gift.journey_type,
          duration_days: gift.duration_days,
          status: 'active',
          is_gift: true,
          gift_id: gift.id,
          start_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (journeyError) throw new AppError(journeyError.message, 400);

      // Update gift status
      await supabase
        .from('gift_journeys')
        .update({
          status: 'claimed',
          claimed_at: new Date().toISOString(),
          claimed_by: userId,
        })
        .eq('id', gift.id);

      // Send thank you notification to donor
      await this.sendDonorNotification(gift.donor_id, gift.id, userEmail);

      // Log the claim
      await this.logSadaqahActivity(userId, 'gift_claimed', {
        giftId: gift.id,
        journeyType: gift.journey_type,
        donorId: gift.donor_id,
      });

      logger.info('Gift journey claimed', {
        giftId: gift.id,
        userId,
        userEmail,
        journeyType: gift.journey_type,
      });

      res.json({
        status: 'success',
        data: {
          journeyId: journey.id,
          journeyType: gift.journey_type,
          durationDays: gift.duration_days,
          personalMessage: gift.personal_message,
          message: 'Gift journey claimed successfully',
        },
      });
    } catch (error) {
      logger.error('Error claiming gift journey', {
        invitationCode: req.body?.invitationCode,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * @desc    Get user's gifted journeys (as donor)
   * @route   GET /api/sadaqah/gifts/sent
   * @access  Private
   */
  async getSentGifts(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const donorId = req.user.id;
      const supabase = getSupabase();

      const { data: gifts, error } = await supabase
        .from('gift_journeys')
        .select('*')
        .eq('donor_id', donorId)
        .order('created_at', { ascending: false });

      if (error) throw new AppError(error.message, 400);

      logger.info('Sent gifts retrieved', { donorId, count: gifts.length });

      res.json({
        status: 'success',
        data: gifts,
      });
    } catch (error) {
      logger.error('Error getting sent gifts', {
        donorId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * @desc    Get user's received gifts
   * @route   GET /api/sadaqah/gifts/received
   * @access  Private
   */
  async getReceivedGifts(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user.id;
      const supabase = getSupabase();

      const { data: gifts, error } = await supabase
        .from('gift_journeys')
        .select('*')
        .eq('claimed_by', userId)
        .order('claimed_at', { ascending: false });

      if (error) throw new AppError(error.message, 400);

      logger.info('Received gifts retrieved', { userId, count: gifts.length });

      res.json({
        status: 'success',
        data: gifts,
      });
    } catch (error) {
      logger.error('Error getting received gifts', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * @desc    Get sadaqah impact statistics
   * @route   GET /api/sadaqah/impact
   * @access  Private
   */
  async getSadaqahImpact(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user.id;
      const supabase = getSupabase();

      // Get gifts sent by user
      const { data: sentGifts } = await supabase
        .from('gift_journeys')
        .select('*')
        .eq('donor_id', userId);

      // Get impact metrics
      const totalGifts = sentGifts?.length || 0;
      const claimedGifts =
        sentGifts?.filter((g) => g.status === 'claimed').length || 0;
      const totalDaysGifted =
        sentGifts?.reduce((sum, g) => sum + (g.duration_days || 0), 0) || 0;

      // Get community impact (anonymized)
      const { data: communityStats } = await supabase
        .from('sadaqah_community_stats')
        .select('*')
        .single();

      const impactData = {
        personal: {
          totalGifts,
          claimedGifts,
          totalDaysGifted,
          impactScore: this.calculateImpactScore(sentGifts || []),
        },
        community: communityStats || {
          totalGiftsGiven: 0,
          totalPeopleBenefited: 0,
          totalHealingDays: 0,
        },
      };

      logger.info('Sadaqah impact retrieved', {
        userId,
        totalGifts,
        claimedGifts,
      });

      res.json({
        status: 'success',
        data: impactData,
      });
    } catch (error) {
      logger.error('Error getting sadaqah impact', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Generate unique invitation code
   */
  private generateInvitationCode(): string {
    return crypto.randomBytes(8).toString('hex').toUpperCase();
  }

  /**
   * Process payment for gift journey
   */
  private async processPayment(
    paymentMethod: string,
    paymentToken: string,
    journeyType: string,
    durationDays: number
  ): Promise<PaymentResult> {
    try {
      // Calculate amount based on journey type and duration
      const amount = this.calculateGiftAmount(journeyType, durationDays);

      // Trigger payment processing workflow
      const paymentResult = await triggerN8nWorkflow('process-payment', {
        paymentMethod,
        paymentToken,
        amount,
        description: `Gift Journey: ${journeyType} for ${durationDays} days`,
        metadata: {
          type: 'gift_journey',
          journeyType,
          durationDays,
        },
      });

      return {
        success: (paymentResult as any).success || false,
        transactionId: (paymentResult as any).transactionId,
        message: (paymentResult as any).message,
      };
    } catch (error) {
      logger.error('Payment processing error', {
        paymentMethod,
        journeyType,
        error: error.message,
      });
      return {
        success: false,
        message: 'Payment processing failed',
      };
    }
  }

  /**
   * Calculate gift amount based on journey type and duration
   */
  private calculateGiftAmount(
    journeyType: string,
    durationDays: number
  ): number {
    const baseRates = {
      anxiety_healing: 2.99,
      depression_support: 3.99,
      spiritual_growth: 1.99,
      comprehensive: 4.99,
    };

    const baseRate = baseRates[journeyType as keyof typeof baseRates] || 2.99;
    return Math.round(baseRate * (durationDays / 30) * 100) / 100; // Price per month
  }

  /**
   * Send gift invitation email
   */
  private async sendGiftInvitation(
    recipientEmail: string,
    invitationCode: string,
    personalMessage: string | undefined,
    donorId: string
  ): Promise<void> {
    await triggerN8nWorkflow('send-gift-invitation', {
      recipientEmail,
      invitationCode,
      personalMessage,
      donorId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send notification to donor when gift is claimed
   */
  private async sendDonorNotification(
    donorId: string,
    giftId: string,
    claimedByEmail: string
  ): Promise<void> {
    await triggerN8nWorkflow('send-donor-notification', {
      donorId,
      giftId,
      claimedByEmail,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get or create user account for gift recipient
   */
  private async getOrCreateUser(email: string): Promise<string> {
    const supabase = getSupabase();

    // Check if user exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return existingUser.id;
    }

    // Create new user account
    const { data: newUser, error } = await supabase
      .from('users')
      .insert({
        email,
        created_at: new Date().toISOString(),
        is_gift_recipient: true,
      })
      .select('id')
      .single();

    if (error) throw new AppError('Failed to create user account', 500);

    return newUser.id;
  }

  /**
   * Calculate impact score based on gifts sent
   */
  private calculateImpactScore(gifts: any[]): number {
    let score = 0;

    gifts.forEach((gift) => {
      if (gift.status === 'claimed') {
        score += gift.duration_days * 2; // 2 points per day gifted and claimed
      } else if (gift.status === 'pending') {
        score += gift.duration_days * 1; // 1 point per day gifted but not yet claimed
      }
    });

    return score;
  }

  /**
   * Log sadaqah activity
   */
  private async logSadaqahActivity(
    userId: string,
    activityType: string,
    metadata: any
  ): Promise<void> {
    const supabase = getSupabase();

    await supabase.from('sadaqah_activities').insert({
      user_id: userId,
      activity_type: activityType,
      metadata,
      created_at: new Date().toISOString(),
    });
  }
}

export const sadaqahController = new SadaqahController();
