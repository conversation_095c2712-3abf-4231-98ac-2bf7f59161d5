/**
 * Enhanced Auth Controller for Qalb Healing API
 * Updated to use enhanced response system and Islamic context
 */

import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { getSupabase } from '../config/supabase';
import { prisma } from '../utils/database';
import { sendSuccess, sendError, sendValidationError, sendNotFound } from '../utils/response';
import { islamicLogger } from '../utils/enhancedLogger';
import { asyncHandler, createValidationError } from '../middleware/enhancedErrorHandler';

/**
 * User signup with Islamic context
 */
export const signup = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    islamicLogger.auth('Signup validation failed', {
      ip: req.ip,
      errors: formattedErrors
    });
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please provide complete and accurate information to begin your spiritual journey with us.'
    );
    return;
  }

  const { email, password, firstName, lastName } = req.body;

  if (!firstName) {
    islamicLogger.auth('Signup failed - firstName required', { 
      ip: req.ip, 
      email 
    });
    
    sendError(
      res,
      'VALIDATION_FAILED',
      'First name is required',
      400,
      undefined,
      'Your name is important to us as it helps us provide personalized Islamic guidance. Please provide your first name.'
    );
    return;
  }

  islamicLogger.auth('Signup attempt started', { 
    email, 
    firstName,
    ip: req.ip 
  });

  const supabase = getSupabase();

  try {
    // Step 1: Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (authError) {
      islamicLogger.error('Supabase auth signup failed', {
        email,
        error: authError.message,
        status: authError.status
      });
      
      sendError(
        res,
        'AUTH_INVALID',
        authError.message,
        authError.status || 400,
        undefined,
        'We encountered an issue creating your account. Please try again, and trust that Allah will make the path easy for you.'
      );
      return;
    }

    if (!authData.user) {
      islamicLogger.error('Supabase signup did not return user', { email });
      
      sendError(
        res,
        'INTERNAL_ERROR',
        'User creation failed',
        500,
        undefined,
        'We encountered an unexpected issue. Please try again, and remember that Allah is the best of planners.'
      );
      return;
    }

    // Step 2: Create profile in our database using Prisma
    try {
      const fullName = lastName ? `${firstName} ${lastName}` : firstName;
      const profile = await prisma.profile.create({
        data: {
          id: authData.user.id,
          email: authData.user.email!,
          firstName,
          lastName: lastName || null,
          fullName,
        },
      });

      islamicLogger.success('User profile created successfully', {
        userId: authData.user.id,
        email: authData.user.email,
        firstName,
        lastName
      });

      const authResponse = {
        user: {
          id: authData.user.id,
          email: authData.user.email,
          firstName: firstName,
          lastName: lastName || null,
          fullName: profile.fullName
        },
        token: authData.session?.access_token || null,
        refreshToken: authData.session?.refresh_token || null,
        requiresEmailConfirmation: !authData.session,
      };

      sendSuccess(
        res,
        authResponse,
        'Welcome to Qalb Healing! Your account has been created successfully. May Allah bless your journey towards spiritual wellness.',
        201,
        'auth'
      );

    } catch (dbError) {
      islamicLogger.error('Profile creation failed after Supabase signup', {
        userId: authData.user.id,
        email: authData.user.email,
        error: dbError instanceof Error ? dbError.message : String(dbError)
      });

      sendError(
        res,
        'INTERNAL_ERROR',
        'Profile creation failed',
        500,
        undefined,
        'Your account was created but we encountered an issue setting up your profile. Please contact support, and we will resolve this quickly, insha\'Allah.'
      );
    }

  } catch (error) {
    islamicLogger.error('Unexpected error during signup', {
      email,
      error: (error as Error).message
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'An unexpected error occurred during signup',
      500,
      undefined,
      'We encountered an unexpected issue. Please try again later, and trust in Allah\'s timing.'
    );
  }
});

/**
 * Resend email confirmation with Islamic context
 */
export const resendConfirmationEmail = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please provide a valid email address to resend the confirmation.'
    );
    return;
  }

  const { email } = req.body;
  const supabase = getSupabase();

  islamicLogger.auth('Resend confirmation email requested', { 
    email, 
    ip: req.ip 
  });

  try {
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email,
    });

    if (error) {
      islamicLogger.warn('Supabase resend confirmation failed', {
        email,
        error: error.message
      });
      // For security, don't reveal if email exists or not
    }

    islamicLogger.info('Resend confirmation email processed', { email });
    
    sendSuccess(
      res,
      { email },
      'If an account with this email exists and requires confirmation, a new confirmation link has been sent. Please check your email, including spam folder.',
      200,
      'guidance'
    );

  } catch (error) {
    islamicLogger.error('Unexpected error during resend confirmation', {
      email,
      error: (error as Error).message
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'Failed to process confirmation email request',
      500,
      undefined,
      'We encountered an issue sending the confirmation email. Please try again later.'
    );
  }
});

/**
 * User login with Islamic context
 */
export const login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    islamicLogger.auth('Login validation failed', {
      ip: req.ip,
      errors: formattedErrors
    });
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please provide valid credentials to continue your spiritual journey.'
    );
    return;
  }

  const { email, password } = req.body;
  const supabase = getSupabase();

  islamicLogger.auth('Login attempt started', { 
    email, 
    ip: req.ip 
  });

  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError) {
      islamicLogger.warn('Login failed', {
        email,
        error: authError.message,
        ip: req.ip
      });
      
      sendError(
        res,
        'AUTH_INVALID',
        'Invalid email or password',
        401,
        undefined,
        'Please check your credentials and try again. If you\'ve forgotten your password, you can reset it. May Allah grant you ease in accessing your account.'
      );
      return;
    }

    if (!authData.user || !authData.session) {
      islamicLogger.error('Login did not return user or session', { email });
      
      sendError(
        res,
        'INTERNAL_ERROR',
        'Login failed',
        500,
        undefined,
        'We encountered an issue during login. Please try again.'
      );
      return;
    }

    // Fetch profile details from database
    let userProfile = null;
    try {
      userProfile = await prisma.profile.findUnique({
        where: { id: authData.user.id },
      });
    } catch (dbError) {
      islamicLogger.warn('Profile fetch failed during login', {
        userId: authData.user.id,
        error: dbError instanceof Error ? dbError.message : String(dbError)
      });
      // Continue without profile data
    }

    islamicLogger.success('User logged in successfully', { 
      userId: authData.user.id,
      email: authData.user.email 
    });

    const authResponse = {
      user: {
        id: authData.user.id,
        email: authData.user.email,
        firstName: userProfile?.firstName || null,
        lastName: userProfile?.lastName || null,
        fullName: userProfile?.fullName || null,
        avatarUrl: userProfile?.avatarUrl || null
      },
      token: authData.session.access_token,
      refreshToken: authData.session.refresh_token,
    };

    sendSuccess(
      res,
      authResponse,
      'Welcome back! May Allah bless your continued journey towards spiritual wellness.',
      200,
      'auth'
    );

  } catch (error) {
    islamicLogger.error('Unexpected error during login', {
      email,
      error: (error as Error).message
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'An unexpected error occurred during login',
      500,
      undefined,
      'We encountered an unexpected issue. Please try again later.'
    );
  }
});

/**
 * Get user profile with Islamic context
 */
export const getProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  if (!req.user?.id) {
    islamicLogger.warn('getProfile called without authenticated user');
    
    sendError(
      res,
      'AUTH_REQUIRED',
      'User not authenticated',
      401,
      undefined,
      'Please sign in to access your profile and continue your spiritual journey.'
    );
    return;
  }

  const userId = req.user.id;
  
  islamicLogger.info('Profile retrieval requested', { userId });

  try {
    const profile = await prisma.profile.findUnique({
      where: { id: userId },
    });

    if (!profile) {
      islamicLogger.warn('Profile not found', { userId });
      
      sendNotFound(
        res,
        'Profile',
        'Your profile could not be found. Please contact support if this issue persists.'
      );
      return;
    }

    const userResponse = {
      id: req.user.id,
      email: req.user.email || profile.email,
      firstName: profile.firstName,
      lastName: profile.lastName,
      fullName: profile.fullName,
      avatarUrl: profile.avatarUrl,
      createdAt: profile.createdAt,
      updatedAt: profile.updatedAt
    };

    islamicLogger.info('Profile retrieved successfully', { userId });

    sendSuccess(
      res,
      { user: userResponse },
      'Your profile has been retrieved successfully.',
      200,
      'success'
    );

  } catch (error) {
    if (error instanceof PrismaClientKnownRequestError) {
      islamicLogger.error('Database error in getProfile', {
        userId,
        code: error.code,
        error: error.message
      });
      
      if (error.code === 'P2025') {
        sendNotFound(res, 'Profile');
        return;
      }
    }

    islamicLogger.error('Unexpected error in getProfile', {
      userId,
      error: (error as Error).message
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'Failed to retrieve profile',
      500,
      undefined,
      'We encountered an issue retrieving your profile. Please try again.'
    );
  }
});

/**
 * Update user profile with Islamic context
 */
export const updateProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));
    
    sendValidationError(
      res, 
      formattedErrors,
      'Please provide valid information to update your profile.'
    );
    return;
  }

  if (!req.user?.id) {
    sendError(
      res,
      'AUTH_REQUIRED',
      'User not authenticated',
      401,
      undefined,
      'Please sign in to update your profile.'
    );
    return;
  }

  const userId = req.user.id;
  const { firstName, lastName, fullName, avatarUrl } = req.body;

  // Check if any update fields are provided
  if (firstName === undefined && lastName === undefined && fullName === undefined && avatarUrl === undefined) {
    // No update fields provided, return current profile
    try {
      const profile = await prisma.profile.findUnique({ 
        where: { id: userId } 
      });
      
      if (!profile) {
        sendNotFound(res, 'Profile');
        return;
      }

      sendSuccess(
        res,
        { profile },
        'No changes were made to your profile.',
        200,
        'success'
      );
      return;
    } catch (error) {
      sendError(
        res,
        'INTERNAL_ERROR',
        'Failed to retrieve current profile',
        500
      );
      return;
    }
  }

  islamicLogger.info('Profile update requested', { 
    userId,
    fields: { firstName, lastName, fullName, avatarUrl }
  });

  try {
    const dataToUpdate: any = {};
    
    if (firstName !== undefined) dataToUpdate.firstName = firstName;
    if (lastName !== undefined) dataToUpdate.lastName = lastName;
    if (fullName !== undefined) {
      dataToUpdate.fullName = fullName;
    } else if (firstName !== undefined || lastName !== undefined) {
      // Auto-generate fullName if firstName or lastName changed
      const currentProfile = await prisma.profile.findUnique({
        where: { id: userId }
      });
      const newFirstName = firstName !== undefined ? firstName : currentProfile?.firstName;
      const newLastName = lastName !== undefined ? lastName : currentProfile?.lastName;
      dataToUpdate.fullName = newLastName ? `${newFirstName} ${newLastName}` : newFirstName;
    }
    if (avatarUrl !== undefined) dataToUpdate.avatarUrl = avatarUrl;

    const updatedProfile = await prisma.profile.update({
      where: { id: userId },
      data: dataToUpdate,
    });

    islamicLogger.success('Profile updated successfully', { 
      userId,
      updatedFields: Object.keys(dataToUpdate)
    });

    sendSuccess(
      res,
      { profile: updatedProfile },
      'Your profile has been updated successfully. May Allah bless the positive changes in your life.',
      200,
      'success'
    );

  } catch (error) {
    if (error instanceof PrismaClientKnownRequestError) {
      islamicLogger.error('Database error in updateProfile', {
        userId,
        code: error.code,
        error: error.message
      });
      
      if (error.code === 'P2025') {
        sendNotFound(res, 'Profile to update');
        return;
      }
    }

    islamicLogger.error('Unexpected error in updateProfile', {
      userId,
      error: (error as Error).message
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'Failed to update profile',
      500,
      undefined,
      'We encountered an issue updating your profile. Please try again.'
    );
  }
});

/**
 * OAuth login initiation with Islamic context
 */
export const oauthLogin = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { provider } = req.params;
  
  if (!provider) {
    sendError(
      res,
      'VALIDATION_FAILED',
      'OAuth provider missing',
      400,
      undefined,
      'Please specify a valid authentication provider to continue.'
    );
    return;
  }

  islamicLogger.auth('OAuth login initiated', { 
    provider, 
    ip: req.ip 
  });

  const supabase = getSupabase();
  const redirectTo = process.env.SUPABASE_OAUTH_REDIRECT_URL;
  
  if (!redirectTo) {
    islamicLogger.error('OAuth redirect URL not configured');
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'OAuth configuration error',
      500,
      undefined,
      'OAuth authentication is not properly configured. Please contact support.'
    );
    return;
  }

  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: provider as any,
      options: { redirectTo },
    });

    if (error) {
      islamicLogger.error('OAuth login initiation failed', {
        provider,
        error: error.message
      });
      
      sendError(
        res,
        'EXTERNAL_API_ERROR',
        `OAuth login with ${provider} failed`,
        error.status || 500,
        { provider },
        `We encountered an issue with ${provider} authentication. Please try again or use email/password login.`
      );
      return;
    }

    if (!data.url) {
      islamicLogger.error('OAuth login did not return URL', { provider });
      
      sendError(
        res,
        'EXTERNAL_API_ERROR',
        'OAuth login failed',
        500,
        { provider },
        'Authentication service did not provide a login URL. Please try again.'
      );
      return;
    }

    islamicLogger.info('OAuth login URL generated', { provider });
    
    // Redirect to OAuth provider
    res.redirect(data.url);

  } catch (error) {
    islamicLogger.error('Unexpected error in OAuth login', {
      provider,
      error: (error as Error).message
    });
    
    sendError(
      res,
      'INTERNAL_ERROR',
      'OAuth login failed',
      500,
      undefined,
      'We encountered an unexpected issue with OAuth authentication. Please try again.'
    );
  }
});

/**
 * OAuth callback handler with Islamic context
 */
export const oauthCallback = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  islamicLogger.auth('OAuth callback received', { 
    ip: req.ip,
    query: req.query 
  });

  // For most OAuth flows, Supabase handles the token exchange client-side
  // This endpoint serves as a confirmation or redirect point
  
  sendSuccess(
    res,
    { 
      message: 'OAuth callback received',
      nextSteps: 'Complete authentication on the client side'
    },
    'OAuth authentication callback received successfully. Please complete the authentication process in your app.',
    200,
    'auth'
  );
});