import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { getSupabase } from '../config/supabase';
import { prisma } from '../config/database'; // Import Prisma client
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

export const signup = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const validationErrors = errors.array().map((err) => ({
        type: err.type,
        msg: err.msg,
        path: (err as any).path,
        location: (err as any).location,
      }));
      logger.warn('Validation errors during signup', {
        clientIp: req.ip,
        errors: validationErrors,
      });
      throw new AppError('Invalid input data', 400, validationErrors);
    }

    const { email, password, firstName, lastName } = req.body;

    if (!firstName) {
      logger.warn('Validation failed: firstName is required for signup', { clientIp: req.ip, email });
      throw new AppError('First name is required', 400);
    }

    const supabase = getSupabase();

    // Step 1: Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (authError) {
      logger.error('Supabase auth signup failed', {
        errorName: authError.name,
        errorMessage: authError.message,
        errorStatus: authError.status,
        errorStack: authError.stack,
      });
      throw new AppError(authError.message, authError.status || 400);
    }

    if (!authData.user) {
      logger.error('Supabase auth signup did not return a user object', {
        email,
      });
      throw new AppError('User creation failed: No user data returned', 500);
    }

    // Step 2: Create profile in our database using Prisma
    try {
      const fullName = lastName ? `${firstName} ${lastName}` : firstName;
      await prisma.profile.create({
        data: {
          id: authData.user.id, // Use the ID from Supabase auth
          email: authData.user.email!, // email is asserted as non-null by Supabase user creation
          firstName,
          lastName: lastName || null, // Set lastName to null if not provided
          fullName, // Set the fullName field by combining firstName and lastName
          // avatarUrl can be set to null or default initially
          // createdAt and updatedAt are handled by Prisma @default(now()) and @updatedAt
        },
      });
      logger.info('User profile created successfully in Prisma DB', {
        userId: authData.user.id,
        email: authData.user.email,
        firstName,
        lastName,
      });
    } catch (dbError) {
      logger.error('Prisma profile creation failed after Supabase signup', {
        userId: authData.user.id,
        email: authData.user.email,
        firstName,
        lastName,
        error:
          dbError instanceof Error
            ? {
                name: dbError.name,
                message: dbError.message,
                stack: dbError.stack,
                ...(dbError instanceof PrismaClientKnownRequestError && {
                  code: dbError.code,
                  meta: dbError.meta,
                  clientVersion: dbError.clientVersion,
                }),
              }
            : dbError,
      });
      // Potentially attempt to delete the Supabase auth user if profile creation fails,
      // though this adds complexity (e.g., what if deletion fails?).
      // For now, we'll log and report an error.
      throw new AppError('Profile creation failed in database', 500, dbError);
    }

    logger.info('User signed up successfully', { email });

    const authResponse = {
      user: {
        id: authData.user.id,
        email: authData.user.email,
        firstName: firstName,
        lastName: lastName || null,
      },
      token: authData.session?.access_token || null,
      refreshToken: authData.session?.refresh_token || null,
      requiresEmailConfirmation: !authData.session,
    };

    res.status(201).json({
      status: 'success',
      data: authResponse,
    });
  } catch (error) {
    next(error);
  }
};

export const resendConfirmationEmail = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const validationErrors = errors.array().map((err) => ({
        type: err.type,
        msg: err.msg,
        path: (err as any).path,
        location: (err as any).location,
      }));
      logger.warn(
        'Validation errors during resend confirmation email request',
        { clientIp: req.ip, errors: validationErrors }
      );
      throw new AppError('Invalid input data', 400, validationErrors);
    }

    const { email } = req.body;
    const supabase = getSupabase();

    const { error } = await supabase.auth.resend({
      type: 'signup',
      email,
    });

    if (error) {
      logger.error('Supabase error during resend confirmation email', {
        email,
        errorName: error.name,
        errorMessage: error.message,
        errorStatus: error.status,
        errorStack: error.stack,
      });
      // For security and to prevent email enumeration, return a generic success-like message.
      // Actual error handling strategy might depend on specific requirements.
    }

    logger.info('Resend confirmation email request processed for', { email });
    res.status(200).json({
      status: 'success',
      message:
        'If an account with this email exists and requires confirmation, a new confirmation link has been sent.',
    });
  } catch (error) {
    next(error);
  }
};

export const login = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const validationErrorDetails = errors.array().map((err) => ({
        type: err.type,
        msg: err.msg,
        path: (err as any).path,
        location: (err as any).location,
      }));
      logger.warn('Validation errors during login', {
        clientIp: req.ip,
        errors: validationErrorDetails,
      });
      throw new AppError('Invalid input data', 400, validationErrorDetails);
    }

    const { email, password } = req.body;
    const supabase = getSupabase();

    const { data: authData, error: authError } =
      await supabase.auth.signInWithPassword({
        email,
        password,
      });

    if (authError) {
      logger.warn('Supabase auth login failed', {
        email,
        errorName: authError.name,
        errorMessage: authError.message,
        errorStatus: authError.status,
      });
      // Use a generic message for failed login attempts for security
      throw new AppError('Invalid email or password', 401);
    }

    if (!authData.user || !authData.session) {
      logger.error('Supabase login did not return user or session', { email });
      throw new AppError('Login failed: Authentication data missing', 500);
    }

    logger.info('User logged in successfully', { email: authData.user.email });

    // Fetch profile details from Prisma DB to enrich the response
    // This part is optional based on what `login` response should contain.
    // If only auth details are needed, this can be skipped.
    let userProfile = null;
    try {
      userProfile = await prisma.profile.findUnique({
        where: { id: authData.user.id },
      });
    } catch (dbError) {
      logger.error('Prisma profile fetch failed during login', {
        userId: authData.user.id,
        error:
          dbError instanceof Error
            ? { name: dbError.name, message: dbError.message }
            : dbError,
      });
      // Continue without profile data if it fails, or handle as critical error
    }

    const authResponse = {
      user: {
        id: authData.user.id,
        email: authData.user.email,
        // Populate from userProfile if available and needed
        firstName: userProfile?.fullName?.split(' ')[0] || null,
        lastName:
          userProfile?.fullName?.split(' ').slice(1).join(' ') || null,
      },
      token: authData.session.access_token,
      refreshToken: authData.session.refresh_token,
    };

    res.status(200).json({
      status: 'success',
      data: authResponse,
    });
  } catch (error) {
    next(error);
  }
};

export const getProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user?.id) {
      logger.warn('getProfile called without authenticated user ID');
      throw new AppError('User not authenticated', 401);
    }

    const profile = await prisma.profile.findUnique({
      where: { id: req.user.id },
    });

    if (!profile) {
      logger.warn('Profile not found for user', { userId: req.user.id });
      throw new AppError('Profile not found', 404);
    }

    // Format user data to match expected structure
    // req.user comes from your auth middleware (e.g., JWT decoded token)
    const userResponse = {
      id: req.user.id,
      email: req.user.email || profile.email, // Prefer auth token email, fallback to profile
      firstName: profile.fullName?.split(' ')[0] || null,
      lastName: profile.fullName?.split(' ').slice(1).join(' ') || null,
    };

    res.status(200).json({
      status: 'success',
      data: {
        user: userResponse, // Contains basic info, potentially from JWT
        profile: {
          // Explicitly map fields from Prisma profile to ensure consistency
          id: profile.id,
          email: profile.email,
          fullName: profile.fullName,
          avatarUrl: profile.avatarUrl,
          createdAt: profile.createdAt,
          updatedAt: profile.updatedAt,
          // Add other fields from Profile model as needed
        },
      },
    });
  } catch (error) {
    if (error instanceof PrismaClientKnownRequestError) {
      logger.error('Prisma error in getProfile', {
        code: error.code,
        meta: error.meta,
        clientVersion: error.clientVersion,
        userId: req.user?.id,
      });
      // Handle specific Prisma errors if necessary, e.g., P2025 for record not found
      if (error.code === 'P2025') {
        return next(new AppError('Profile not found', 404));
      }
    }
    next(error);
  }
};

export const updateProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const validationErrorDetails = errors.array().map((err) => ({
        type: err.type,
        msg: err.msg,
        path: (err as any).path,
        location: (err as any).location,
      }));
      logger.warn('Validation errors during updateProfile', {
        clientIp: req.ip,
        userId: req.user?.id,
        errors: validationErrorDetails,
      });
      throw new AppError('Invalid input data', 400, validationErrorDetails);
    }

    if (!req.user?.id) {
      logger.warn('updateProfile called without authenticated user ID');
      throw new AppError('User not authenticated', 401);
    }

    // Accept empty string for fullName and avatarUrl, and allow both to be undefined (no-op update)
    const { fullName, avatarUrl } = req.body;
    if (fullName === undefined && avatarUrl === undefined) {
      // No update fields provided, treat as no-op and return current profile
      const profile = await prisma.profile.findUnique({ where: { id: req.user.id } });
      return res.status(200).json({
        status: 'success',
        data: {
          profile: {
            id: profile?.id,
            email: profile?.email,
            fullName: profile?.fullName,
            avatarUrl: profile?.avatarUrl,
            createdAt: profile?.createdAt,
            updatedAt: profile?.updatedAt,
          },
        },
      });
    }

    const dataToUpdate: { fullName?: string | null; avatarUrl?: string | null } = {};
    if (fullName !== undefined) dataToUpdate.fullName = fullName;
    if (avatarUrl !== undefined) dataToUpdate.avatarUrl = avatarUrl;

    const updatedProfile = await prisma.profile.update({
      where: { id: req.user.id },
      data: dataToUpdate,
    });

    logger.info('Profile updated successfully using Prisma', {
      userId: req.user.id,
    });

    res.status(200).json({
      status: 'success',
      data: {
        profile: {
          id: updatedProfile.id,
          email: updatedProfile.email,
          fullName: updatedProfile.fullName,
          avatarUrl: updatedProfile.avatarUrl,
          createdAt: updatedProfile.createdAt,
          updatedAt: updatedProfile.updatedAt,
        },
      },
    });
  } catch (error) {
    if (error instanceof PrismaClientKnownRequestError) {
      logger.error('Prisma error in updateProfile', {
        code: error.code,
        meta: error.meta,
        clientVersion: error.clientVersion,
        userId: req.user?.id,
      });
      if (error.code === 'P2025') {
        // Record to update not found
        return next(new AppError('Profile not found to update', 404));
      }
    }
    next(error);
  }
};

/**
 * Initiate OAuth login with a provider (e.g., Google)
 * Redirects to Supabase's OAuth URL for the provider
 */
export const oauthLogin = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { provider } = req.params;
    if (!provider) {
      throw new AppError('OAuth provider missing', 400);
    }
    const supabase = getSupabase();

    const redirectTo = process.env.SUPABASE_OAUTH_REDIRECT_URL;
    if (!redirectTo) {
      logger.error('SUPABASE_OAUTH_REDIRECT_URL is not set');
      throw new AppError(
        'OAuth configuration error: Missing redirect URL',
        500
      );
    }

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: provider as any, // Adjust if you have specific provider types
      options: {
        redirectTo,
      },
    });

    if (error) {
      logger.error('Supabase OAuth login initiation failed', {
        provider,
        errorName: error.name,
        errorMessage: error.message,
        errorStatus: error.status,
      });
      throw new AppError(
        `OAuth login with ${provider} failed: ${error.message}`,
        error.status || 500
      );
    }

    if (!data.url) {
      logger.error('Supabase OAuth login did not return a URL', { provider });
      throw new AppError(
        `OAuth login with ${provider} failed: No provider URL returned`,
        500
      );
    }
    // The URL is where the user should be redirected to start the OAuth flow.
    res.redirect(data.url);
  } catch (error) {
    next(error);
  }
};

/**
 * Handle OAuth callback from provider
 * Supabase handles the session creation client-side after redirect.
 * This backend endpoint is typically a redirect or a simple confirmation.
 * For mobile, this might be where you handle deep linking and token exchange if needed.
 * The crucial part is that after OAuth, a user should exist in Supabase auth,
 * and a corresponding profile should be created in your Prisma DB if it doesn't exist.
 * This is often handled by a trigger/webhook on Supabase auth events or by the client
 * after successful OAuth login.
 *
 * For this controller, we assume Supabase auth handles the user creation.
 * We might need a mechanism (e.g., client calls /profile after OAuth)
 * to ensure the profile is created in Prisma DB if it's a new user.
 * Or, a Supabase Function triggered on new auth.users could create the Prisma profile.
 */
export const oauthCallback = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // For many web flows, Supabase client SDK handles the hash fragment with the token.
    // The server might not need to do much here other than redirect to a success/failure page,
    // or provide an API for the client to call after it has the token.

    // If this endpoint is intended to exchange a code (server-side OAuth flow),
    // then it would interact with `supabase.auth.exchangeCodeForSession`.
    // However, the current setup seems to rely on client-side token handling.

    // For now, this remains a placeholder as per the original structure.
    // If profile creation for new OAuth users needs to be handled here,
    // it would involve checking if a user exists in Supabase auth (via client token)
    // and then creating a profile in Prisma if it's missing.
    logger.info('OAuth callback received. Client-side token handling expected.');
    res.status(200).json({
      status: 'success',
      message:
        'OAuth callback received. Complete token handling on the client side.',
    });
  } catch (error) {
    next(error);
  }
};