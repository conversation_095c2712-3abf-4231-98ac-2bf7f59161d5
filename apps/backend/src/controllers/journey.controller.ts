import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { getSupabase } from '../config/supabase';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { triggerN8nWorkflow } from '../services/n8n.service';
import { journeyService } from '../services/journey.service';
import { resourcesService } from '../services/resources.service';
import { z } from 'zod';

export const startJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { journeyType, focusLayers, customDuration } = req.body;
    const userId = req.user?.id;
    const supabase = getSupabase();

    // Check if user has an active journey
    const { data: activeJourney } = await supabase
      .from('user_journeys')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (activeJourney) {
      throw new AppError(
        'You already have an active journey. Complete or modify it first.',
        400
      );
    }

    // Calculate journey duration
    const durationMap: Record<string, number> = {
      '7-day': 7,
      '14-day': 14,
      '40-day': 40,
      custom: customDuration || 21,
    };

    const totalDays = durationMap[journeyType];
    const startDate = new Date();
    const endDate = new Date(
      startDate.getTime() + totalDays * 24 * 60 * 60 * 1000
    );

    // Generate journey modules using AI
    const journeyModules = await triggerN8nWorkflow(
      'generate-journey-modules',
      {
        userId,
        journeyType,
        focusLayers,
        totalDays,
      }
    );

    // Create journey record
    const { data: journey, error: journeyError } = await supabase
      .from('user_journeys')
      .insert({
        user_id: userId,
        journey_type: journeyType,
        focus_layers: focusLayers,
        total_days: totalDays,
        current_day: 1,
        start_date: startDate,
        end_date: endDate,
        status: 'active',
        modules: (journeyModules as any).modules || [],
      })
      .select()
      .single();

    if (journeyError) throw new AppError(journeyError.message, 400);

    logger.info('Journey started successfully', {
      userId,
      journeyId: journey.id,
    });

    res.status(201).json({
      status: 'success',
      data: {
        journey: {
          id: journey.id,
          journeyType,
          startDate,
          endDate,
          totalDays,
          focusLayers,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all completed milestones for a specific journey
 */
export const getJourneyCompletedMilestones = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const firstError = errors.array()[0];
      throw new AppError(firstError.msg, 400);
    }

    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const milestones = await journeyService.getJourneyCompletedMilestones(
      journeyId,
      userId
    );

    res.status(200).json({
      status: 'success',
      data: milestones,
      message: 'Journey completed milestones retrieved successfully',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all progress records for a specific journey
 */
export const getJourneyProgressHistory = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const firstError = errors.array()[0];
      throw new AppError(firstError.msg, 400);
    }

    const { journeyId } = req.params;
    const userId = req.user?.id;
    const limit = req.query.limit
      ? parseInt(req.query.limit as string)
      : undefined;
    const offset = req.query.offset
      ? parseInt(req.query.offset as string)
      : undefined;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const progressHistory = await journeyService.getJourneyProgressHistory(
      journeyId,
      userId,
      limit,
      offset
    );

    res.status(200).json({
      status: 'success',
      data: progressHistory,
      message: 'Journey progress history retrieved successfully',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Pause an active journey
 */
export const pausePersonalizedJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }
    if (!journeyId) {
      throw new AppError('Journey ID is required', 400);
    }

    const journey = await journeyService.pauseJourney(journeyId, userId);
    logger.info('Personalized journey paused', { userId, journeyId });

    res.status(200).json({
      status: 'success',
      data: { journey },
      message: 'Journey paused successfully',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Resume a paused journey
 */
export const resumePersonalizedJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }
    if (!journeyId) {
      throw new AppError('Journey ID is required', 400);
    }

    const journey = await journeyService.resumeJourney(journeyId, userId);
    logger.info('Personalized journey resumed', { userId, journeyId });

    res.status(200).json({
      status: 'success',
      data: { journey },
      message: 'Journey resumed successfully',
    });
  } catch (error) {
    next(error);
  }
};

export const getCurrentJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Use the journey service (lightweight version)
    const journey = await journeyService.getCurrentJourney(userId);

    if (!journey) {
      res.status(404).json({
        status: 'error',
        message: 'No active journey found',
      });
      return;
    }

    // Calculate progress percentage
    const progress = journey.totalProgress || 0;

    res.status(200).json({
      status: 'success',
      data: {
        journey: {
          ...journey,
          progress: Math.round(progress * 100) / 100,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getCurrentJourneyWithDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Use the journey service (heavy version with details)
    const journey = await journeyService.getCurrentJourneyWithDetails(userId);

    if (!journey) {
      res.status(404).json({
        status: 'error',
        message: 'No active journey found',
      });
      return;
    }

    // Calculate progress percentage
    const progress = journey.totalProgress || 0;

    res.status(200).json({
      status: 'success',
      data: {
        journey: {
          ...journey,
          progress: Math.round(progress * 100) / 100,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const updateProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { moduleId, status, reflections, challenges } = req.body;
    const supabase = getSupabase();

    // Update module progress
    const { data: progress, error } = await supabase
      .from('journey_progress')
      .upsert({
        user_id: req.user?.id,
        module_id: moduleId,
        status,
        reflections,
        challenges,
        completed_at: status === 'completed' ? new Date() : null,
        updated_at: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    // Update journey current day if module completed
    if (status === 'completed') {
      await supabase.rpc('advance_journey_day', {
        user_id: req.user?.id,
      });
    }

    logger.info('Journey progress updated', {
      userId: req.user?.id,
      moduleId,
      status,
    });

    res.status(200).json({
      status: 'success',
      data: {
        progress,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getJourneyAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // If journeyId is provided, get journey-specific analytics
    if (journeyId) {
      const analytics = await journeyService.generateJourneyAnalytics(journeyId, userId);
      
      res.status(200).json({
        status: 'success',
        data: analytics,
      });
    } else {
      // Fallback to general analytics (legacy support)
      const supabase = getSupabase();
      const { data: analytics } = await supabase.rpc('get_journey_analytics', {
        user_id: userId,
      });

      res.status(200).json({
        status: 'success',
        data: {
          analytics: analytics || {
            completionRate: 0,
            averageSessionTime: 0,
            streakDays: 0,
            layerProgress: {},
          },
        },
      });
    }
  } catch (error) {
    next(error);
  }
};

export const getAchievements = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const supabase = getSupabase();

    const { data: achievements } = await supabase
      .from('user_achievements')
      .select(
        `
        *,
        achievements (*)
      `
      )
      .eq('user_id', req.user?.id);

    const { data: availableAchievements } = await supabase
      .from('achievements')
      .select('*')
      .eq('is_active', true);

    const earned = achievements?.filter((a) => a.earned_at) || [];
    const available =
      availableAchievements?.filter(
        (a) => !earned.some((e) => e.achievement_id === a.id)
      ) || [];

    res.status(200).json({
      status: 'success',
      data: {
        earned,
        available,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const submitDailyCheckIn = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { mood, dhikrCount, prayerConsistency, notes } = req.body;
    const supabase = getSupabase();

    const { data: checkIn, error } = await supabase
      .from('daily_check_ins')
      .insert({
        user_id: req.user?.id,
        mood,
        dhikr_count: dhikrCount,
        prayer_consistency: prayerConsistency,
        notes,
        check_in_date: new Date(),
      })
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    res.status(201).json({
      status: 'success',
      data: {
        checkIn,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getRecommendedResources = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const { category, type, search } = req.query;

    let resources;
    if (search) {
      // Search resources
      resources = await resourcesService.searchResources(
        userId,
        search as string,
        category as string,
        type as string
      );
    } else {
      // Get recommended resources
      resources = await resourcesService.getJourneyResources(
        userId,
        undefined, // journeyId - will auto-detect current journey
        category as string,
        type as string
      );
    }

    logger.info('Resources retrieved', {
      userId,
      category,
      type,
      search,
      count: resources.length,
    });

    res.status(200).json({
      status: 'success',
      data: {
        resources,
        total: resources.length,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getResourceById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const { resourceId } = req.params;
    if (!resourceId) {
      throw new AppError('Resource ID is required', 400);
    }

    const resource = await resourcesService.getResourceById(resourceId, userId);

    if (!resource) {
      throw new AppError('Resource not found', 404);
    }

    logger.info('Resource retrieved by ID', { userId, resourceId });

    res.status(200).json({
      status: 'success',
      data: {
        resource,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const modifyJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid input data', 400);
    }

    const { action, additionalDays, newFocusLayers } = req.body;
    const supabase = getSupabase();

    const { data: journey } = await supabase
      .from('user_journeys')
      .select('*')
      .eq('user_id', req.user?.id)
      .eq('status', 'active')
      .single();

    if (!journey) {
      throw new AppError('No active journey found', 404);
    }

    let updateData: any = { updated_at: new Date() };

    switch (action) {
      case 'reset':
        updateData = {
          current_day: 1,
          start_date: new Date(),
          updated_at: new Date(),
        };
        break;
      case 'extend':
        updateData = {
          total_days: journey.total_days + (additionalDays || 7),
          end_date: new Date(
            journey.end_date.getTime() +
              (additionalDays || 7) * 24 * 60 * 60 * 1000
          ),
          updated_at: new Date(),
        };
        break;
      case 'modify_focus':
        updateData = {
          focus_layers: newFocusLayers,
          updated_at: new Date(),
        };
        break;
    }

    const { data: updatedJourney, error } = await supabase
      .from('user_journeys')
      .update(updateData)
      .eq('id', journey.id)
      .select()
      .single();

    if (error) throw new AppError(error.message, 400);

    res.status(200).json({
      status: 'success',
      data: {
        journey: updatedJourney,
      },
    });
  } catch (error) {
    next(error);
  }
};

// Feature 2: Personalized Healing Journeys Controllers

/**
 * Create a personalized healing journey based on assessment
 */
export const createPersonalizedJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const firstError = errors.array()[0];
      throw new AppError(firstError.msg, 400);
    }

    const { assessmentId, preferences } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Create personalized journey using the service
    const journey = await journeyService.createPersonalizedJourney(
      userId,
      assessmentId,
      preferences
    );

    logger.info('Personalized journey created', {
      userId,
      journeyId: journey.id,
    });

    res.status(201).json({
      status: 'success',
      data: {
        journey,
      },
      message: 'Personalized healing journey created successfully',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Start a personalized journey
 */
export const startPersonalizedJourney = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    if (!journeyId) {
      throw new AppError('Journey ID is required', 400);
    }

    const journey = await journeyService.startJourney(journeyId, userId);

    logger.info('Personalized journey started', { userId, journeyId });

    res.status(200).json({
      status: 'success',
      data: {
        journey,
      },
      message: 'Journey started successfully',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Record daily progress for personalized journey
 */
export const recordDailyProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError('Invalid progress data', 400);
    }

    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const progressData = {
      ...req.body,
      userId,
      date: new Date().toISOString(),
      peerSupport: false, // Default value
    };

    const progress = await journeyService.recordDailyProgress(
      req.body.journeyId,
      userId,
      progressData
    );

    logger.info('Daily progress recorded', {
      userId,
      journeyId: req.body.journeyId,
    });

    res.status(201).json({
      status: 'success',
      data: {
        progress,
        message: 'Daily progress recorded successfully',
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update journey progress for a specific day
 */
export const updateJourneyProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const firstError = errors.array()[0];
      throw new AppError(firstError.msg, 400);
    }

    const { journeyId } = req.params;
    const { dayNumber, practiceResults } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // The updateJourneyProgress method in JourneyService is private.
    // This controller action likely needs to call a public method in the service
    // or the logic needs to be re-evaluated.
    // For now, let's assume there's a public method or this needs adjustment.
    // This will likely still cause a runtime error if not addressed in the service.
    // Consider creating a public method like `updateDailyPractice` in JourneyService.
    const result = await (journeyService as any).updateJourneyProgress(
      journeyId,
      dayNumber,
      practiceResults
    );

    logger.info('Journey progress updated', {
      userId,
      journeyId,
      dayNumber,
    });

    res.status(200).json({
      status: 'success',
      data: {
        progress: result,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all journeys for the authenticated user
 */
export const getUserJourneys = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    const journeys = await journeyService.getUserJourneys(userId);

    res.status(200).json({
      status: 'success',
      data: {
        journeys,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get journey by ID (lightweight - no heavy progress data)
 */
export const getJourneyById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const firstError = errors.array()[0];
      throw new AppError(firstError.msg, 400);
    }

    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    // Get journey details (lightweight)
    const journey = await journeyService.getJourneyById(journeyId, userId);

    if (!journey) {
      throw new AppError('Journey not found', 404);
    }

    logger.info('Journey retrieved by ID', { userId, journeyId });

    res.status(200).json({
      status: 'success',
      data: {
        journey,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get journey progress and details (heavy - includes progress analytics)
 */
export const getJourneyProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { journeyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError('Authentication required', 401);
    }

    if (!journeyId) {
      throw new AppError('Journey ID is required', 400);
    }

    // Get journey details with progress analytics
    const progressData = await journeyService.getJourneyProgress(journeyId);

    if (!progressData) {
      throw new AppError('Journey progress not found', 404);
    }

    logger.info('Journey progress retrieved', { userId, journeyId });

    res.status(200).json({
      status: 'success',
      data: progressData,
    });
  } catch (error) {
    next(error);
  }
};