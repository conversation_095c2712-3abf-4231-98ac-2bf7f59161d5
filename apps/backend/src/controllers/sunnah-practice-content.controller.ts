import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { SunnahPracticeContentService } from '../services/sunnah-practice-content.service';

const prisma = new PrismaClient(); // Ideal: Singleton
const sunnahPracticeContentService = new SunnahPracticeContentService(prisma);

export const createSunnahPracticeContent = async (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const content = await sunnahPracticeContentService.create(req.body);
    res.status(201).json({ message: 'Sunnah practice content created successfully', data: content });
  } catch (error) {
    console.error('Error in createSunnahPracticeContent:', error);
    if (error.code === 'P2002' && error.meta?.target?.includes('title')) {
         return res.status(409).json({ message: 'Sunnah practice content with this title already exists.' });
    }
    res.status(500).json({ message: error.message || 'Failed to create Sunnah practice content.' });
  }
};

export const getAllSunnahPracticeContent = async (req: Request, res: Response, next: NextFunction) => {
  const { skip, take, where, orderBy } = req.query;
  const params: any = {};
  if (skip) params.skip = parseInt(skip as string, 10);
  if (take) params.take = parseInt(take as string, 10);
    if (where) {
    try {
      params.where = JSON.parse(where as string);
    } catch (e) {
      return res.status(400).json({ message: 'Invalid "where" query parameter: not a valid JSON string.' });
    }
  }
  if (orderBy) {
     try {
      params.orderBy = JSON.parse(orderBy as string);
    } catch (e) {
      return res.status(400).json({ message: 'Invalid "orderBy" query parameter: not a valid JSON string.' });
    }
  }

  try {
    const contentList = await sunnahPracticeContentService.findAll(params);
    res.status(200).json({ message: 'Sunnah practices content retrieved successfully', data: contentList, total: contentList.length });
  } catch (error) {
    console.error('Error in getAllSunnahPracticeContent:', error);
    res.status(500).json({ message: error.message || 'Failed to retrieve Sunnah practices content.' });
  }
};

export const getSunnahPracticeContentById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const content = await sunnahPracticeContentService.findOne(req.params.id);
    if (!content) {
        return res.status(404).json({ message: 'Sunnah practice content not found.' });
    }
    res.status(200).json({ message: 'Sunnah practice content retrieved successfully', data: content });
  } catch (error) {
    console.error('Error in getSunnahPracticeContentById:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    res.status(500).json({ message: error.message || 'Failed to retrieve Sunnah practice content.' });
  }
};

export const getSunnahPracticeContentByTitle = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const content = await sunnahPracticeContentService.findByTitle(req.params.title);
    if (!content) {
        return res.status(404).json({ message: 'Sunnah practice content not found by title.' });
    }
    res.status(200).json({ message: 'Sunnah practice content retrieved successfully', data: content });
  } catch (error) {
    console.error('Error in getSunnahPracticeContentByTitle:', error);
    res.status(500).json({ message: error.message || 'Failed to retrieve Sunnah practice content.' });
  }
};

export const updateSunnahPracticeContent = async (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const content = await sunnahPracticeContentService.update(req.params.id, req.body);
    res.status(200).json({ message: 'Sunnah practice content updated successfully', data: content });
  } catch (error) {
    console.error('Error in updateSunnahPracticeContent:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    if (error.code === 'P2002' && error.meta?.target?.includes('title')) {
         return res.status(409).json({ message: 'Update conflicts with an existing Sunnah practice (title may already exist).' });
    }
    res.status(500).json({ message: error.message || 'Failed to update Sunnah practice content.' });
  }
};

export const deleteSunnahPracticeContent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await sunnahPracticeContentService.remove(req.params.id);
    res.status(200).json({ message: 'Sunnah practice content deleted successfully' });
  } catch (error) {
    console.error('Error in deleteSunnahPracticeContent:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    res.status(500).json({ message: error.message || 'Failed to delete Sunnah practice content.' });
  }
};
