import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { QuranicVerseContentService } from '../services/quranic-verse-content.service';

const prisma = new PrismaClient(); // Ideal: Singleton
const quranicVerseContentService = new QuranicVerseContentService(prisma);

export const createQuranicVerseContent = async (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const content = await quranicVerseContentService.create(req.body);
    res.status(201).json({ message: 'Quranic verse content created successfully', data: content });
  } catch (error) {
    console.error('Error in createQuranicVerseContent:', error);
    if (error.code === 'P2002' && error.meta?.target?.includes('surahNumber_ayahNumber')) {
         return res.status(409).json({ message: 'Quranic verse with this Surah and Ayah number already exists.' });
    }
    res.status(500).json({ message: error.message || 'Failed to create Quranic verse content.' });
  }
};

export const getAllQuranicVerseContent = async (req: Request, res: Response, next: NextFunction) => {
  const { skip, take, where, orderBy } = req.query;
  const params: any = {};
  if (skip) params.skip = parseInt(skip as string, 10);
  if (take) params.take = parseInt(take as string, 10);
  if (where) {
    try {
      params.where = JSON.parse(where as string);
    } catch (e) {
      return res.status(400).json({ message: 'Invalid "where" query parameter: not a valid JSON string.' });
    }
  }
  if (orderBy) {
     try {
      params.orderBy = JSON.parse(orderBy as string);
    } catch (e) {
      return res.status(400).json({ message: 'Invalid "orderBy" query parameter: not a valid JSON string.' });
    }
  }

  try {
    const contentList = await quranicVerseContentService.findAll(params);
    res.status(200).json({ message: 'Quranic verses content retrieved successfully', data: contentList, total: contentList.length });
  } catch (error) {
    console.error('Error in getAllQuranicVerseContent:', error);
    res.status(500).json({ message: error.message || 'Failed to retrieve Quranic verses content.' });
  }
};

export const getQuranicVerseContentById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const content = await quranicVerseContentService.findOne(req.params.id);
     if (!content) {
        return res.status(404).json({ message: 'Quranic verse content not found.' });
    }
    res.status(200).json({ message: 'Quranic verse content retrieved successfully', data: content });
  } catch (error) {
    console.error('Error in getQuranicVerseContentById:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    res.status(500).json({ message: error.message || 'Failed to retrieve Quranic verse content.' });
  }
};

export const getQuranicVerseContentBySurahAyah = async (req: Request, res: Response, next: NextFunction) => {
  const { surahNumber, ayahNumber } = req.params;
  try {
    const content = await quranicVerseContentService.findBySurahAyah(parseInt(surahNumber,10), parseInt(ayahNumber,10));
    if (!content) {
        return res.status(404).json({ message: 'Quranic verse content not found for the given Surah and Ayah.' });
    }
    res.status(200).json({ message: 'Quranic verse content retrieved successfully', data: content });
  } catch (error) {
    console.error('Error in getQuranicVerseContentBySurahAyah:', error);
    res.status(500).json({ message: error.message || 'Failed to retrieve Quranic verse content.' });
  }
};


export const updateQuranicVerseContent = async (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const content = await quranicVerseContentService.update(req.params.id, req.body);
    res.status(200).json({ message: 'Quranic verse content updated successfully', data: content });
  } catch (error) {
    console.error('Error in updateQuranicVerseContent:', error);
    if (error.status === 404) return res.status(404).json({ message: error.message });
    if (error.code === 'P2002' && error.meta?.target?.includes('surahNumber_ayahNumber')) {
         return res.status(409).json({ message: 'Update conflicts with an existing Quranic verse (Surah/Ayah may already exist).' });
    }
    res.status(500).json({ message: error.message || 'Failed to update Quranic verse content.' });
  }
};

export const deleteQuranicVerseContent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await quranicVerseContentService.remove(req.params.id);
    res.status(200).json({ message: 'Quranic verse content deleted successfully' });
  } catch (error) {
    console.error('Error in deleteQuranicVerseContent:', error);
     if (error.status === 404) return res.status(404).json({ message: error.message });
    res.status(500).json({ message: error.message || 'Failed to delete Quranic verse content.' });
  }
};
