import { Request, Response, NextFunction } from 'express';
import { getSupabase } from '../config/supabase';
import { AppError } from './errorHandler';
import { User } from '@supabase/supabase-js';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('No token provided', 401);
    }

    const token = authHeader.split(' ')[1];
    // console.log(`Received token in authMiddleware: ${token}`);
    const supabase = getSupabase();

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (error || !user) {
      throw new AppError('Invalid or expired token', 401);
    }

    req.user = user;
    next();
  } catch (error) {
    // If it's already an AppError, pass it through
    if (error instanceof AppError) {
      next(error);
    } else {
      // For any other error (like Supabase auth errors), treat as invalid token
      next(new AppError('Invalid or expired token', 401));
    }
  }
};

// Alias for compatibility
export const authenticateToken = authMiddleware;
export const authenticateUser = authMiddleware;