/**
 * Enhanced Authentication and Role-Based Access Control (RBAC) Middleware
 * For Qalb Healing Islamic Wellness Platform
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { getSupabase } from '../config/supabase';
import { sendUnauthorized, sendForbidden } from '../utils/response';
import { logger } from '../utils/logger';
import { createAuthError, createForbiddenError } from './enhancedErrorHandler';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        permissions: Permission[];
        profile?: any;
        sessionId?: string;
      };
    }
  }
}

/**
 * User roles in the Islamic healing platform
 */
export enum UserRole {
  USER = 'user',                    // Regular users seeking healing
  MODERATOR = 'moderator',          // Community moderators
  SCHOLAR = 'scholar',              // Islamic scholars providing guidance
  THERAPIST = 'therapist',          // Licensed Islamic therapists
  ADMIN = 'admin',                  // Platform administrators
  SUPER_ADMIN = 'super_admin'       // System administrators
}

/**
 * Permissions for different actions
 */
export enum Permission {
  // User management
  READ_OWN_PROFILE = 'read_own_profile',
  UPDATE_OWN_PROFILE = 'update_own_profile',
  DELETE_OWN_ACCOUNT = 'delete_own_account',
  
  // Content access
  ACCESS_BASIC_CONTENT = 'access_basic_content',
  ACCESS_PREMIUM_CONTENT = 'access_premium_content',
  ACCESS_SCHOLAR_CONTENT = 'access_scholar_content',
  
  // Journey management
  CREATE_JOURNEY = 'create_journey',
  UPDATE_OWN_JOURNEY = 'update_own_journey',
  DELETE_OWN_JOURNEY = 'delete_own_journey',
  VIEW_JOURNEY_ANALYTICS = 'view_journey_analytics',
  
  // Emergency features
  ACCESS_EMERGENCY_SUPPORT = 'access_emergency_support',
  REQUEST_CRISIS_INTERVENTION = 'request_crisis_intervention',
  
  // Community features
  PARTICIPATE_COMMUNITY = 'participate_community',
  CREATE_COMMUNITY_CONTENT = 'create_community_content',
  MODERATE_COMMUNITY = 'moderate_community',
  
  // Assessment and diagnosis
  SUBMIT_ASSESSMENT = 'submit_assessment',
  VIEW_OWN_DIAGNOSIS = 'view_own_diagnosis',
  PROVIDE_DIAGNOSIS = 'provide_diagnosis',
  
  // Content management
  CREATE_ISLAMIC_CONTENT = 'create_islamic_content',
  EDIT_ISLAMIC_CONTENT = 'edit_islamic_content',
  DELETE_ISLAMIC_CONTENT = 'delete_islamic_content',
  APPROVE_CONTENT = 'approve_content',
  
  // User management (admin)
  VIEW_ALL_USERS = 'view_all_users',
  MANAGE_USER_ROLES = 'manage_user_roles',
  SUSPEND_USERS = 'suspend_users',
  
  // Analytics and reporting
  VIEW_PLATFORM_ANALYTICS = 'view_platform_analytics',
  EXPORT_DATA = 'export_data',
  
  // System administration
  MANAGE_SYSTEM_CONFIG = 'manage_system_config',
  ACCESS_LOGS = 'access_logs',
  MANAGE_BACKUPS = 'manage_backups'
}

/**
 * Role-permission mapping
 */
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.USER]: [
    Permission.READ_OWN_PROFILE,
    Permission.UPDATE_OWN_PROFILE,
    Permission.DELETE_OWN_ACCOUNT,
    Permission.ACCESS_BASIC_CONTENT,
    Permission.CREATE_JOURNEY,
    Permission.UPDATE_OWN_JOURNEY,
    Permission.DELETE_OWN_JOURNEY,
    Permission.VIEW_JOURNEY_ANALYTICS,
    Permission.ACCESS_EMERGENCY_SUPPORT,
    Permission.REQUEST_CRISIS_INTERVENTION,
    Permission.PARTICIPATE_COMMUNITY,
    Permission.SUBMIT_ASSESSMENT,
    Permission.VIEW_OWN_DIAGNOSIS
  ],
  
  [UserRole.MODERATOR]: [
    ...ROLE_PERMISSIONS[UserRole.USER],
    Permission.ACCESS_PREMIUM_CONTENT,
    Permission.CREATE_COMMUNITY_CONTENT,
    Permission.MODERATE_COMMUNITY,
    Permission.APPROVE_CONTENT
  ],
  
  [UserRole.SCHOLAR]: [
    ...ROLE_PERMISSIONS[UserRole.MODERATOR],
    Permission.ACCESS_SCHOLAR_CONTENT,
    Permission.CREATE_ISLAMIC_CONTENT,
    Permission.EDIT_ISLAMIC_CONTENT,
    Permission.PROVIDE_DIAGNOSIS
  ],
  
  [UserRole.THERAPIST]: [
    ...ROLE_PERMISSIONS[UserRole.USER],
    Permission.ACCESS_PREMIUM_CONTENT,
    Permission.ACCESS_SCHOLAR_CONTENT,
    Permission.PROVIDE_DIAGNOSIS,
    Permission.VIEW_PLATFORM_ANALYTICS
  ],
  
  [UserRole.ADMIN]: [
    ...ROLE_PERMISSIONS[UserRole.SCHOLAR],
    Permission.DELETE_ISLAMIC_CONTENT,
    Permission.VIEW_ALL_USERS,
    Permission.MANAGE_USER_ROLES,
    Permission.SUSPEND_USERS,
    Permission.VIEW_PLATFORM_ANALYTICS,
    Permission.EXPORT_DATA
  ],
  
  [UserRole.SUPER_ADMIN]: [
    ...Object.values(Permission)
  ]
};

/**
 * Enhanced authentication middleware
 */
export const enhancedAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createAuthError(
        'Authentication token required',
        'Please sign in to access this blessed content and continue your spiritual journey.'
      );
    }

    const token = authHeader.substring(7);
    
    // Verify JWT token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      logger.error('JWT_SECRET not configured');
      throw createAuthError('Authentication configuration error');
    }

    let decoded: any;
    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (jwtError: any) {
      if (jwtError.name === 'TokenExpiredError') {
        throw createAuthError(
          'Authentication token has expired',
          'Your session has expired. Please sign in again to continue receiving Allah\'s guidance.'
        );
      } else {
        throw createAuthError(
          'Invalid authentication token',
          'Your authentication token is invalid. Please sign in again to continue your spiritual journey.'
        );
      }
    }

    // Get user from Supabase
    const supabase = getSupabase();
    const { data: user, error } = await supabase.auth.getUser(token);
    
    if (error || !user.user) {
      throw createAuthError(
        'Invalid authentication token',
        'Your authentication is no longer valid. Please sign in again.'
      );
    }

    // Get user profile and role from database
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.user.id)
      .single();

    if (profileError || !profile) {
      throw createAuthError(
        'User profile not found',
        'Your profile could not be found. Please contact support if this issue persists.'
      );
    }

    // Determine user role (default to USER if not set)
    const userRole = (profile.role as UserRole) || UserRole.USER;
    const permissions = ROLE_PERMISSIONS[userRole] || ROLE_PERMISSIONS[UserRole.USER];

    // Attach user information to request
    req.user = {
      id: user.user.id,
      email: user.user.email || profile.email,
      role: userRole,
      permissions,
      profile,
      sessionId: decoded.sessionId
    };

    // Log successful authentication
    logger.info('User authenticated successfully', {
      userId: req.user.id,
      email: req.user.email,
      role: req.user.role,
      url: req.url,
      method: req.method,
      ip: req.ip
    });

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Role-based access control middleware
 */
export const requireRole = (...allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createAuthError('Authentication required');
    }

    if (!allowedRoles.includes(req.user.role)) {
      logger.warn('Access denied - insufficient role', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        url: req.url,
        method: req.method
      });

      throw createForbiddenError(
        'Insufficient permissions to access this resource',
        'You don\'t have the required permissions to access this content. May Allah grant you patience and understanding.'
      );
    }

    next();
  };
};

/**
 * Permission-based access control middleware
 */
export const requirePermission = (...requiredPermissions: Permission[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createAuthError('Authentication required');
    }

    const hasAllPermissions = requiredPermissions.every(permission =>
      req.user!.permissions.includes(permission)
    );

    if (!hasAllPermissions) {
      logger.warn('Access denied - insufficient permissions', {
        userId: req.user.id,
        userPermissions: req.user.permissions,
        requiredPermissions,
        url: req.url,
        method: req.method
      });

      throw createForbiddenError(
        'Insufficient permissions to perform this action',
        'You don\'t have the required permissions for this action. May Allah grant you patience and understanding.'
      );
    }

    next();
  };
};

/**
 * Resource ownership middleware (for accessing own resources)
 */
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createAuthError('Authentication required');
    }

    const resourceId = req.params[resourceIdParam];
    const userId = req.user.id;

    // Super admins can access any resource
    if (req.user.role === UserRole.SUPER_ADMIN) {
      next();
      return;
    }

    // Check if user owns the resource or has admin privileges
    if (resourceId !== userId && !req.user.permissions.includes(Permission.VIEW_ALL_USERS)) {
      logger.warn('Access denied - resource ownership required', {
        userId: req.user.id,
        resourceId,
        url: req.url,
        method: req.method
      });

      throw createForbiddenError(
        'You can only access your own resources',
        'You can only access your own content and data. May Allah grant you understanding.'
      );
    }

    next();
  };
};

/**
 * Optional authentication middleware (for public endpoints with optional auth)
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No authentication provided, continue without user
      next();
      return;
    }

    // Try to authenticate, but don't fail if it doesn't work
    await enhancedAuthMiddleware(req, res, next);
  } catch (error) {
    // Authentication failed, but continue without user for optional auth
    logger.info('Optional authentication failed, continuing without user', {
      url: req.url,
      method: req.method,
      error: (error as Error).message
    });
    next();
  }
};

/**
 * Admin check middleware (for admin-only endpoints)
 */
export const requireAdmin = requireRole(UserRole.ADMIN, UserRole.SUPER_ADMIN);

/**
 * Scholar check middleware (for scholar-only endpoints)
 */
export const requireScholar = requireRole(UserRole.SCHOLAR, UserRole.ADMIN, UserRole.SUPER_ADMIN);

/**
 * Moderator check middleware (for moderation endpoints)
 */
export const requireModerator = requireRole(
  UserRole.MODERATOR,
  UserRole.SCHOLAR,
  UserRole.ADMIN,
  UserRole.SUPER_ADMIN
);

/**
 * Session validation middleware
 */
export const validateSession = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.user?.sessionId) {
    next();
    return;
  }

  try {
    // Check if session is still valid in Supabase
    const supabase = getSupabase();
    const { data: session, error } = await supabase.auth.getSession();
    
    if (error || !session.session) {
      throw createAuthError(
        'Session has expired',
        'Your session has expired. Please sign in again to continue your spiritual journey.'
      );
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Rate limiting by user role
 */
export const roleBasedRateLimit = (
  limits: Partial<Record<UserRole, { windowMs: number; maxRequests: number }>>
) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    const userRole = req.user?.role || UserRole.USER;
    const limit = limits[userRole] || limits[UserRole.USER];

    if (!limit) {
      next();
      return;
    }

    const userId = req.user?.id || req.ip;
    const now = Date.now();

    // Clean up old entries
    for (const [key, value] of userRequests.entries()) {
      if (value.resetTime < now) {
        userRequests.delete(key);
      }
    }

    // Get or create user record
    let userRecord = userRequests.get(userId);
    if (!userRecord || userRecord.resetTime < now) {
      userRecord = { count: 0, resetTime: now + limit.windowMs };
      userRequests.set(userId, userRecord);
    }

    // Check rate limit
    if (userRecord.count >= limit.maxRequests) {
      throw createForbiddenError(
        'Rate limit exceeded for your user role',
        'You have exceeded the request limit for your account type. Please wait before trying again.'
      );
    }

    // Increment counter
    userRecord.count++;
    next();
  };
};

/**
 * Audit logging middleware
 */
export const auditLog = (action: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // Log the action for audit purposes
    logger.info('User action audit', {
      action,
      userId: req.user?.id,
      userRole: req.user?.role,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
      body: req.method !== 'GET' ? req.body : undefined,
      query: req.query,
      params: req.params
    });

    next();
  };
};