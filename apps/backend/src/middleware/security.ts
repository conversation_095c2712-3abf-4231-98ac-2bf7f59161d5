/**
 * Enhanced security middleware for Qalb Healing API
 */
import helmet from 'helmet';
import xss from 'xss-clean';
import { rateLimit } from 'express-rate-limit';
import { RedisStore } from 'rate-limit-redis';
import { redisClient } from './cache';
import { AppError } from './errorHandler';
import validator from 'validator';
import logger from '../utils/logger';
import { Request, Response, NextFunction } from 'express';

/**
 * Advanced Helmet configuration
 * Sets secure HTTP headers to protect against common web vulnerabilities
 */
const advancedHelmet = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", 'cdn.qalbhealing.com'],
      styleSrc: ["'self'", "'unsafe-inline'", 'cdn.qalbhealing.com'],
      imgSrc: ["'self'", 'data:', 'cdn.qalbhealing.com', '*.supabase.co'],
      connectSrc: ["'self'", 'api.qalbhealing.com', '*.supabase.co'],
      fontSrc: ["'self'", 'cdn.qalbhealing.com'],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", 'cdn.qalbhealing.com'],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});

/**
 * XSS protection middleware
 */
const xssProtection = xss();

/**
 * Input sanitization middleware
 */
const sanitizeInput = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    sanitizeObject(req.query);
  }

  next();
};

const sanitizeObject = (obj: any): void => {
  for (const key in obj) {
    if (typeof obj[key] === 'string') {
      obj[key] = validator.escape(obj[key]);
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      sanitizeObject(obj[key]);
    }
  }
};

/**
 * Rate limiting configurations
 */
const createRateLimiter = (windowMs: number, max: number, message: string) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    store: redisClient
      ? new RedisStore({
          sendCommand: (command: string, ...args: any[]) =>
            redisClient.call(command, ...args) as any,
        })
      : undefined,
  });
};

// Authentication rate limiter (stricter)
export const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // limit each IP to 5 requests per windowMs
  'Too many authentication attempts, please try again later'
);

// Content rate limiter (more lenient)
export const contentLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100, // limit each IP to 100 requests per windowMs
  'Too many requests, please try again later'
);

/**
 * Content security middleware
 */
export const contentSecurity = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Check for suspicious patterns in request
  const suspiciousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi,
  ];

  const requestString = JSON.stringify(req.body) + JSON.stringify(req.query);

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestString)) {
      logger.warn('Suspicious request detected', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url,
        method: req.method,
      });
      throw new AppError('Invalid request content', 400);
    }
  }

  next();
};

/**
 * Combined security middleware
 */
export const securityMiddleware = [
  advancedHelmet,
  xssProtection,
  sanitizeInput,
  contentSecurity,
];
