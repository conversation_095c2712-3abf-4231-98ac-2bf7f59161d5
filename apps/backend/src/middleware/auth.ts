/**
 * Legacy Authentication Middleware - Backward Compatibility
 * This file provides backward compatibility for existing routes
 * while the enhanced authentication system is being integrated
 */

import { enhancedAuthMiddleware, optionalAuth } from './enhancedAuth';

// Export enhanced auth as the main auth middleware
export const authMiddleware = enhancedAuthMiddleware;

// Aliases for compatibility with existing code
export const authenticateToken = enhancedAuthMiddleware;
export const authenticateUser = enhancedAuthMiddleware;

// Optional authentication for public endpoints
export const optionalAuthMiddleware = optionalAuth;

// Re-export enhanced auth utilities for new code
export {
  enhancedAuthMiddleware,
  requireRole,
  requirePermission,
  requireOwnership,
  requireAdmin,
  requireScholar,
  requireModerator,
  validateSession,
  roleBasedRateLimit,
  auditLog,
  UserRole,
  Permission
} from './enhancedAuth';