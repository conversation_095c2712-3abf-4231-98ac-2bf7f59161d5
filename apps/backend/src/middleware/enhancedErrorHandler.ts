/**
 * Enhanced Error Handling Middleware for Qalb Healing API
 * Provides comprehensive error handling with Islamic context and proper logging
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { sendError, sendInternalError, ERROR_CODES } from '../utils/response';
import { ValidationError } from 'express-validator';
import { PrismaClientKnownRequestError, PrismaClientValidationError } from '@prisma/client/runtime/library';

export class IslamicAppError extends Error {
  public statusCode: number;
  public status: string;
  public isOperational: boolean;
  public code: keyof typeof ERROR_CODES;
  public details?: unknown;
  public islamicGuidance?: string;

  constructor(
    message: string,
    statusCode: number,
    code: keyof typeof ERROR_CODES,
    details?: unknown,
    islamicGuidance?: string
  ) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.code = code;
    this.details = details;
    this.islamicGuidance = islamicGuidance;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Enhanced error handler with Islamic context
 */
export const enhancedErrorHandler = (
  err: Error | IslamicAppError | PrismaClientKnownRequestError | PrismaClientValidationError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Check if response has already been sent
  if (res.headersSent) {
    logger.error('Error occurred after response was sent:', {
      message: err.message,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id
    });
    return;
  }

  // Log the error with context
  logger.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    body: req.body,
    query: req.query,
    params: req.params
  });

  // Handle different types of errors
  if (err instanceof IslamicAppError) {
    handleIslamicAppError(err, res);
  } else if (err instanceof PrismaClientKnownRequestError) {
    handlePrismaError(err, res);
  } else if (err instanceof PrismaClientValidationError) {
    handlePrismaValidationError(err, res);
  } else if (err.name === 'ValidationError') {
    handleValidationError(err, res);
  } else if (err.name === 'JsonWebTokenError') {
    handleJWTError(err, res);
  } else if (err.name === 'TokenExpiredError') {
    handleTokenExpiredError(err, res);
  } else if (err.name === 'CastError') {
    handleCastError(err, res);
  } else if (err.name === 'MongoError' || err.name === 'MongoServerError') {
    handleDatabaseError(err, res);
  } else {
    handleGenericError(err, res);
  }
};

/**
 * Handle Islamic App Error
 */
function handleIslamicAppError(err: IslamicAppError, res: Response): void {
  sendError(
    res,
    err.code,
    err.message,
    err.statusCode,
    err.details,
    err.islamicGuidance
  );
}

/**
 * Handle Prisma database errors
 */
function handlePrismaError(err: PrismaClientKnownRequestError, res: Response): void {
  let message = 'Database operation failed';
  let code: keyof typeof ERROR_CODES = 'INTERNAL_ERROR';
  let statusCode = 500;

  switch (err.code) {
    case 'P2002':
      message = 'A record with this information already exists';
      code = 'VALIDATION_FAILED';
      statusCode = 409;
      break;
    case 'P2025':
      message = 'Record not found';
      code = 'RESOURCE_NOT_FOUND';
      statusCode = 404;
      break;
    case 'P2003':
      message = 'Foreign key constraint failed';
      code = 'VALIDATION_FAILED';
      statusCode = 400;
      break;
    case 'P2014':
      message = 'Invalid ID provided';
      code = 'INVALID_INPUT';
      statusCode = 400;
      break;
    default:
      message = 'Database error occurred';
      code = 'INTERNAL_ERROR';
      statusCode = 500;
  }

  sendError(res, code, message, statusCode, {
    prismaCode: err.code,
    meta: err.meta
  });
}

/**
 * Handle Prisma validation errors
 */
function handlePrismaValidationError(err: PrismaClientValidationError, res: Response): void {
  sendError(
    res,
    'VALIDATION_FAILED',
    'Invalid data provided to database',
    400,
    { originalError: err.message }
  );
}

/**
 * Handle express-validator validation errors
 */
function handleValidationError(err: any, res: Response): void {
  const errors = err.errors?.map((error: any) => ({
    field: error.path || error.param,
    message: error.msg || error.message
  })) || [];

  sendError(
    res,
    'VALIDATION_FAILED',
    'Validation failed',
    400,
    { validationErrors: errors }
  );
}

/**
 * Handle JWT errors
 */
function handleJWTError(err: Error, res: Response): void {
  sendError(
    res,
    'AUTH_INVALID',
    'Invalid authentication token',
    401,
    undefined,
    'Your authentication token is invalid. Please sign in again to continue your spiritual journey.'
  );
}

/**
 * Handle expired JWT tokens
 */
function handleTokenExpiredError(err: Error, res: Response): void {
  sendError(
    res,
    'AUTH_EXPIRED',
    'Authentication token has expired',
    401,
    undefined,
    'Your session has expired. Please sign in again to continue receiving Allah\'s guidance.'
  );
}

/**
 * Handle cast errors (invalid ObjectId, etc.)
 */
function handleCastError(err: any, res: Response): void {
  sendError(
    res,
    'INVALID_INPUT',
    `Invalid ${err.path}: ${err.value}`,
    400
  );
}

/**
 * Handle database connection errors
 */
function handleDatabaseError(err: Error, res: Response): void {
  sendError(
    res,
    'INTERNAL_ERROR',
    'Database connection error',
    500,
    undefined,
    'We are experiencing technical difficulties. Please try again later, and remember that Allah is the ultimate source of all solutions.'
  );
}

/**
 * Handle generic errors
 */
function handleGenericError(err: Error, res: Response): void {
  // Don't leak error details in production
  const message = process.env.NODE_ENV === 'production' 
    ? 'Something went wrong' 
    : err.message;

  const details = process.env.NODE_ENV === 'development' 
    ? { stack: err.stack, name: err.name }
    : undefined;

  sendInternalError(res, message, details);
}

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Create Islamic App Error helper functions
 */
export const createAuthError = (message: string, islamicGuidance?: string) => {
  return new IslamicAppError(
    message,
    401,
    'AUTH_REQUIRED',
    undefined,
    islamicGuidance || 'Please authenticate to access this blessed content.'
  );
};

export const createValidationError = (message: string, details?: any, islamicGuidance?: string) => {
  return new IslamicAppError(
    message,
    400,
    'VALIDATION_FAILED',
    details,
    islamicGuidance || 'Please check your input and try again. Allah loves those who are careful and precise.'
  );
};

export const createNotFoundError = (resource: string, islamicGuidance?: string) => {
  return new IslamicAppError(
    `${resource} not found`,
    404,
    'RESOURCE_NOT_FOUND',
    undefined,
    islamicGuidance || 'What you seek may not be available at this time. Trust in Allah\'s wisdom and timing.'
  );
};

export const createForbiddenError = (message: string, islamicGuidance?: string) => {
  return new IslamicAppError(
    message,
    403,
    'PERMISSION_DENIED',
    undefined,
    islamicGuidance || 'You don\'t have permission to access this content. May Allah grant you patience and understanding.'
  );
};

export const createCrisisError = (message: string, details?: any) => {
  return new IslamicAppError(
    message,
    500,
    'CRISIS_DETECTION_FAILED',
    details,
    'We\'re here to support you. Please reach out to our crisis support team or emergency services if needed.'
  );
};

export const createExternalServiceError = (service: string, details?: any) => {
  return new IslamicAppError(
    `${service} service is currently unavailable`,
    503,
    'EXTERNAL_API_ERROR',
    details,
    'Our external services are temporarily unavailable. Please try again later, and remember that Allah is the ultimate source of guidance.'
  );
};

/**
 * Error handling for unhandled promise rejections
 */
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Promise Rejection:', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString()
  });
  
  // In production, you might want to gracefully shut down
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

/**
 * Error handling for uncaught exceptions
 */
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception:', {
    message: error.message,
    stack: error.stack,
    name: error.name
  });
  
  // Gracefully shut down
  process.exit(1);
});