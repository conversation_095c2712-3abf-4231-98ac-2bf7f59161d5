/**
 * Comprehensive Validation Middleware for Qalb Healing API
 * Provides robust input validation with Islamic content awareness
 */

import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult, ValidationChain } from 'express-validator';
import { sendValidationError } from '../utils/response';
import { logger } from '../utils/logger';

/**
 * Handle validation results
 */
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg
    }));

    logger.warn('Validation failed:', {
      url: req.url,
      method: req.method,
      errors: formattedErrors,
      body: req.body,
      query: req.query,
      params: req.params
    });

    sendValidationError(res, formattedErrors);
    return;
  }
  
  next();
};

/**
 * Custom validators for Islamic content
 */
export const islamicValidators = {
  /**
   * Validate Arabic text
   */
  arabicText: (value: string): boolean => {
    if (!value) return false;
    // Arabic Unicode range: \u0600-\u06FF, \u0750-\u077F, \u08A0-\u08FF
    const arabicRegex = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\s\u060C\u061B\u061F\u0640]+$/;
    return arabicRegex.test(value.trim());
  },

  /**
   * Validate Quranic verse reference (Surah:Ayah format)
   */
  verseReference: (value: string): boolean => {
    if (!value) return false;
    const verseRegex = /^(\d{1,3}):(\d{1,3})$/;
    const match = value.match(verseRegex);
    if (!match) return false;
    
    const surah = parseInt(match[1]);
    const ayah = parseInt(match[2]);
    
    // Basic validation: Surah 1-114, Ayah 1-286 (max in Al-Baqarah)
    return surah >= 1 && surah <= 114 && ayah >= 1 && ayah <= 286;
  },

  /**
   * Validate Islamic layer (five layers of human existence)
   */
  islamicLayer: (value: string): boolean => {
    const validLayers = ['jism', 'nafs', 'aql', 'qalb', 'ruh'];
    return validLayers.includes(value.toLowerCase());
  },

  /**
   * Validate prayer time
   */
  prayerTime: (value: string): boolean => {
    const validPrayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    return validPrayers.includes(value.toLowerCase());
  },

  /**
   * Validate Islamic name (Names of Allah)
   */
  islamicName: (value: string): boolean => {
    if (!value) return false;
    // Allow Arabic and transliterated names
    const nameRegex = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FFa-zA-Z\s\-']+$/;
    return nameRegex.test(value.trim()) && value.trim().length >= 2;
  },

  /**
   * Validate intensity rating (1-10 scale)
   */
  intensityRating: (value: number): boolean => {
    return Number.isInteger(value) && value >= 1 && value <= 10;
  },

  /**
   * Validate mood rating (1-5 scale)
   */
  moodRating: (value: number): boolean => {
    return Number.isInteger(value) && value >= 1 && value <= 5;
  },

  /**
   * Validate duration string (e.g., "1 week", "3 days", "2 months")
   */
  duration: (value: string): boolean => {
    const durationRegex = /^(\d+)\s+(day|days|week|weeks|month|months|year|years)$/i;
    return durationRegex.test(value.trim());
  },

  /**
   * Validate crisis level
   */
  crisisLevel: (value: string): boolean => {
    const validLevels = ['none', 'low', 'moderate', 'high', 'critical'];
    return validLevels.includes(value.toLowerCase());
  }
};

/**
 * Common validation chains
 */
export const commonValidations = {
  /**
   * UUID parameter validation
   */
  uuidParam: (paramName: string): ValidationChain => 
    param(paramName)
      .isUUID()
      .withMessage(`${paramName} must be a valid UUID`),

  /**
   * Pagination validation
   */
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt(),
    query('sort')
      .optional()
      .isString()
      .withMessage('Sort must be a string'),
    query('order')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('Order must be asc or desc')
  ],

  /**
   * Email validation
   */
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  /**
   * Password validation
   */
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  /**
   * Name validation
   */
  name: (fieldName: string): ValidationChain =>
    body(fieldName)
      .isString()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage(`${fieldName} must be between 2 and 50 characters`)
      .matches(/^[a-zA-Z\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]+$/)
      .withMessage(`${fieldName} can only contain letters and spaces`),

  /**
   * Arabic text validation
   */
  arabicText: (fieldName: string, required: boolean = true): ValidationChain => {
    const validator = body(fieldName);
    
    if (!required) {
      validator.optional();
    }
    
    return validator
      .custom(islamicValidators.arabicText)
      .withMessage(`${fieldName} must contain valid Arabic text`);
  },

  /**
   * Islamic layer validation
   */
  islamicLayer: (fieldName: string): ValidationChain =>
    body(fieldName)
      .custom(islamicValidators.islamicLayer)
      .withMessage(`${fieldName} must be one of: jism, nafs, aql, qalb, ruh`),

  /**
   * Intensity rating validation
   */
  intensityRating: (fieldName: string): ValidationChain =>
    body(fieldName)
      .isInt({ min: 1, max: 10 })
      .withMessage(`${fieldName} must be an integer between 1 and 10`),

  /**
   * Array validation
   */
  array: (fieldName: string, itemValidator?: ValidationChain): ValidationChain => {
    const validator = body(fieldName)
      .isArray()
      .withMessage(`${fieldName} must be an array`);
    
    if (itemValidator) {
      validator.custom((arr: any[]) => {
        return arr.every(item => {
          // This is a simplified validation - in practice, you'd need more complex logic
          return typeof item === 'string' && item.length > 0;
        });
      }).withMessage(`All items in ${fieldName} must be valid`);
    }
    
    return validator;
  }
};

/**
 * Validation schemas for specific endpoints
 */
export const validationSchemas = {
  /**
   * Authentication validations
   */
  auth: {
    signup: [
      commonValidations.email,
      commonValidations.password,
      commonValidations.name('firstName'),
      body('lastName').optional().isString().trim()
        .isLength({ max: 50 })
        .withMessage('Last name must be less than 50 characters'),
      handleValidationErrors
    ],
    
    login: [
      commonValidations.email,
      body('password').exists().withMessage('Password is required')
        .isLength({ min: 1 })
        .withMessage('Password cannot be empty'),
      handleValidationErrors
    ],
    
    updateProfile: [
      body('firstName').optional().isString().trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]+$/)
        .withMessage('First name can only contain letters and spaces'),
      body('lastName').optional().isString().trim()
        .isLength({ max: 50 })
        .withMessage('Last name must be less than 50 characters')
        .matches(/^[a-zA-Z\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]*$/)
        .withMessage('Last name can only contain letters and spaces'),
      body('fullName').optional().isString().trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Full name must be between 2 and 100 characters'),
      body('avatarUrl').optional().isURL()
        .withMessage('Avatar URL must be a valid URL'),
      handleValidationErrors
    ]
  },

  /**
   * Symptom submission validations
   */
  symptoms: {
    submit: [
      body('jism').isArray().withMessage('Jism symptoms must be an array'),
      body('nafs').isArray().withMessage('Nafs symptoms must be an array'),
      body('aql').isArray().withMessage('Aql symptoms must be an array'),
      body('qalb').isArray().withMessage('Qalb symptoms must be an array'),
      body('ruh').isArray().withMessage('Ruh symptoms must be an array'),
      body('intensity')
        .isObject()
        .withMessage('Intensity ratings must be an object')
        .custom((obj) => {
          // Validate that all intensity values are between 1-10
          return Object.values(obj).every(val => 
            typeof val === 'number' && val >= 1 && val <= 10
          );
        })
        .withMessage('All intensity ratings must be between 1 and 10'),
      body('duration')
        .custom(islamicValidators.duration)
        .withMessage('Duration must be in format "X days/weeks/months"'),
      handleValidationErrors
    ],
    
    track: [
      commonValidations.uuidParam('symptomId'),
      commonValidations.intensityRating('intensity'),
      body('notes').optional().isString().trim(),
      handleValidationErrors
    ]
  },

  /**
   * Journey validations
   */
  journey: {
    create: [
      body('type').isIn(['comprehensive', 'focused', 'crisis', 'maintenance'])
        .withMessage('Journey type must be one of: comprehensive, focused, crisis, maintenance'),
      body('primaryLayer').custom(islamicValidators.islamicLayer)
        .withMessage('Primary layer must be valid Islamic layer'),
      body('secondaryLayers').optional().isArray()
        .custom((arr: string[]) => arr.every(islamicValidators.islamicLayer))
        .withMessage('All secondary layers must be valid Islamic layers'),
      body('duration').custom(islamicValidators.duration)
        .withMessage('Duration must be valid'),
      body('dailyTimeCommitment').isInt({ min: 5, max: 180 })
        .withMessage('Daily time commitment must be between 5 and 180 minutes'),
      handleValidationErrors
    ],
    
    updateProgress: [
      commonValidations.uuidParam('journeyId'),
      body('dayNumber').isInt({ min: 1 }).withMessage('Day number must be positive'),
      body('practicesCompleted').optional().isArray(),
      body('overallRating').optional().custom(islamicValidators.moodRating),
      body('moodBefore').optional().custom(islamicValidators.moodRating),
      body('moodAfter').optional().custom(islamicValidators.moodRating),
      body('dailyReflection').optional().isString().trim(),
      handleValidationErrors
    ]
  },

  /**
   * Emergency session validations
   */
  emergency: {
    start: [
      body('triggerType').optional()
        .isIn(['manual', 'automatic', 'scheduled'])
        .withMessage('Trigger type must be manual, automatic, or scheduled'),
      body('currentSymptoms').optional().isArray()
        .withMessage('Current symptoms must be an array'),
      handleValidationErrors
    ],
    
    update: [
      commonValidations.uuidParam('sessionId'),
      body('status').optional()
        .isIn(['active', 'completed', 'interrupted'])
        .withMessage('Status must be active, completed, or interrupted'),
      body('feedback').optional().isString().trim(),
      body('effectivenessRating').optional()
        .custom(islamicValidators.moodRating)
        .withMessage('Effectiveness rating must be between 1 and 5'),
      handleValidationErrors
    ]
  },

  /**
   * Islamic content validations
   */
  content: {
    quranicVerse: [
      commonValidations.arabicText('arabic'),
      body('translation').isString().trim().isLength({ min: 10 })
        .withMessage('Translation must be at least 10 characters'),
      body('transliteration').optional().isString().trim(),
      body('reference').custom(islamicValidators.verseReference)
        .withMessage('Reference must be in format "Surah:Ayah"'),
      body('category').optional().isString().trim(),
      handleValidationErrors
    ],
    
    nameOfAllah: [
      commonValidations.arabicText('arabic'),
      body('transliteration').isString().trim()
        .withMessage('Transliteration is required'),
      body('meaning').isString().trim().isLength({ min: 5 })
        .withMessage('Meaning must be at least 5 characters'),
      body('benefits').optional().isArray(),
      handleValidationErrors
    ],
    
    sunnahPractice: [
      body('title').isString().trim().isLength({ min: 5 })
        .withMessage('Title must be at least 5 characters'),
      body('description').isString().trim().isLength({ min: 20 })
        .withMessage('Description must be at least 20 characters'),
      body('instructions').isArray().withMessage('Instructions must be an array'),
      body('category').isString().trim(),
      body('duration').optional().isInt({ min: 1 })
        .withMessage('Duration must be positive'),
      handleValidationErrors
    ]
  },

  /**
   * Assessment validations
   */
  assessment: {
    start: [
      body('userProfile').isObject().withMessage('User profile is required'),
      handleValidationErrors
    ],
    
    submitStep: [
      commonValidations.uuidParam('sessionId'),
      body('step').isString().withMessage('Step is required'),
      body('responses').isObject().withMessage('Responses must be an object'),
      body('timeSpent').optional().isInt({ min: 0 })
        .withMessage('Time spent must be non-negative'),
      handleValidationErrors
    ]
  }
};

/**
 * Sanitization middleware
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    sanitizeObject(req.query);
  }

  next();
};

/**
 * Recursively sanitize object properties
 */
function sanitizeObject(obj: any): void {
  for (const key in obj) {
    if (typeof obj[key] === 'string') {
      // Trim whitespace
      obj[key] = obj[key].trim();
      
      // Remove null bytes
      obj[key] = obj[key].replace(/\0/g, '');
      
      // For Arabic text, preserve Arabic characters but remove potentially harmful content
      if (islamicValidators.arabicText(obj[key])) {
        // Keep Arabic text as is, but remove HTML tags
        obj[key] = obj[key].replace(/<[^>]*>/g, '');
      }
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      sanitizeObject(obj[key]);
    }
  }
}

/**
 * Rate limiting validation
 */
export const validateRateLimit = (
  windowMs: number,
  maxRequests: number,
  message?: string
) => {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction): void => {
    const clientId = req.ip || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean up old entries
    for (const [key, value] of requests.entries()) {
      if (value.resetTime < now) {
        requests.delete(key);
      }
    }
    
    // Get or create client record
    let clientRecord = requests.get(clientId);
    if (!clientRecord || clientRecord.resetTime < now) {
      clientRecord = { count: 0, resetTime: now + windowMs };
      requests.set(clientId, clientRecord);
    }
    
    // Check rate limit
    if (clientRecord.count >= maxRequests) {
      sendValidationError(res, [{
        field: 'rate_limit',
        message: message || 'Too many requests. Please try again later.'
      }], 'Patience is a virtue beloved by Allah. Please wait a moment before trying again.');
      return;
    }
    
    // Increment counter
    clientRecord.count++;
    next();
  };
};