/**
 * Rate Limiting Middleware
 * Protects API endpoints from abuse and ensures fair usage
 */

import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import Redis from 'ioredis';

// Create Redis client for rate limiting
const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  // retryDelayOnFailover: 100, // This option is not available in ioredis
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
});

// General API rate limiter
export const generalLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: async (command: string, ...args: string[]) => redisClient.call(command, args) as Promise<any>,
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict rate limiter for sensitive endpoints
export const strictLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: async (command: string, ...args: string[]) => redisClient.call(command, args) as Promise<any>,
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: {
    error: 'Too many requests for this sensitive operation, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Assessment specific rate limiter
export const assessmentLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: async (command: string, ...args: string[]) => redisClient.call(command, args) as Promise<any>,
  }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // limit each IP to 5 assessment starts per hour
  message: {
    error: 'Too many assessment attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Onboarding specific rate limiter
export const onboardingLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: async (command: string, ...args: string[]) => redisClient.call(command, args) as Promise<any>,
  }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 onboarding starts per hour
  message: {
    error: 'Too many onboarding attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Journey specific rate limiter
export const journeyLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: async (command: string, ...args: string[]) => redisClient.call(command, args) as Promise<any>,
  }),
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 10, // limit each IP to 10 journey operations per day
  message: {
    error: 'Too many journey operations, please try again tomorrow.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Crisis endpoint rate limiter (more lenient for emergency situations)
export const crisisLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: async (command: string, ...args: string[]) => redisClient.call(command, args) as Promise<any>,
  }),
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // allow more requests for crisis situations
  message: {
    error: 'Please wait a moment before requesting crisis support again.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export default {
  generalLimiter,
  strictLimiter,
  assessmentLimiter,
  onboardingLimiter,
  journeyLimiter,
  crisisLimiter,
};
