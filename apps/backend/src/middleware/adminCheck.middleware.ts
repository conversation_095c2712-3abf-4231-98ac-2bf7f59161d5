import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler'; // Assuming AppError is in this path
import logger from '../utils/logger'; // Assuming logger utility

// Define expected roles for admin-like functionalities for content management
const CONTENT_ADMIN_ROLES = ['admin', 'content_manager'];

export const adminRoleCheckMiddleware = ( // Renamed to be more generic or specific to content
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    logger.warn('adminRoleCheckMiddleware: Authentication required, but no user found on request.');
    return next(new AppError('Authentication required', 401));
  }

  // Supabase stores custom roles in user.app_metadata.roles as an array of strings
  const userRoles = req.user.app_metadata?.roles as string[];
  logger.debug(`adminRoleCheckMiddleware: User ${req.user.id} roles: ${JSON.stringify(userRoles)}`);

  if (!userRoles || !Array.isArray(userRoles)) {
    logger.warn(`adminRoleCheckMiddleware: Access denied for user ${req.user.id}. User roles not found or not an array.`);
    return next(new AppError('Access denied. User roles not properly configured.', 403));
  }

  const hasRequiredRole = userRoles.some(role => CONTENT_ADMIN_ROLES.includes(role.toLowerCase()));

  if (hasRequiredRole) {
    logger.info(`adminRoleCheckMiddleware: Access granted for user ${req.user.id} with roles ${JSON.stringify(userRoles)}.`);
    next(); // User has an admin-like role, proceed
  } else {
    logger.warn(`adminRoleCheckMiddleware: Access denied for user ${req.user.id}. Insufficient privileges. Roles: ${JSON.stringify(userRoles)}`);
    next(new AppError('Access denied. Insufficient privileges for this content operation.', 403));
  }
};

// More specific middleware if needed for other operations
export const adminOnlyMiddleware = (req: Request, res: Response, next: NextFunction): void => {
   if (!req.user) {
    logger.warn('adminOnlyMiddleware: Authentication required, but no user found on request.');
    return next(new AppError('Authentication required', 401));
  }
  const userRoles = req.user.app_metadata?.roles as string[];
  if (userRoles && userRoles.map(r => r.toLowerCase()).includes('admin')) {
    logger.info(`adminOnlyMiddleware: Access granted for user ${req.user.id}.`);
    next();
  } else {
    logger.warn(`adminOnlyMiddleware: Access denied for user ${req.user.id}. Admin role required. Roles: ${JSON.stringify(userRoles)}`);
    next(new AppError('Access denied. Admin role required.', 403));
  }
};
