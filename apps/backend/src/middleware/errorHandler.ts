import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export class AppError extends Error {
  public statusCode: number;
  public status: string;
  public isOperational: boolean;
  public details?: unknown;

  constructor(message: string, statusCode: number, details?: unknown) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.details = details;
    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  err: AppError | Error | unknown,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // Check if response has already been sent
  if (res.headersSent) {
    logger.error('Error occurred after response was sent:', {
      message: err instanceof Error ? err.message : 'Unknown error',
      url: req.url,
      method: req.method,
      ip: req.ip,
    });
    return;
  }

  // If err is falsy, create a generic error
  if (!err) {
    err = new Error('Unknown error occurred');
  }
  // If err is not an Error instance, wrap it
  if (!(err instanceof Error)) {
    err = new Error(typeof err === 'string' ? err : JSON.stringify(err));
  }
  // Now err is Error
  const appErr = err as AppError;
  const statusCode = appErr.statusCode || 500;
  const status = appErr.status || 'error';

  logger.error('Error occurred:', {
    message: appErr.message,
    stack: appErr.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production') {
    res.status(statusCode).json({
      status,
      message: statusCode === 500 ? 'Something went wrong!' : appErr.message
    });
  } else {
    res.status(statusCode).json({
      status,
      error: appErr,
      message: appErr.message,
      stack: appErr.stack
    });
  }
};
