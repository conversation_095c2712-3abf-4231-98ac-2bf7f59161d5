import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';

export const contentTypeMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Set appropriate content type headers based on the content being served
  const contentId = req.params.contentId;
  
  if (!contentId) {
    throw new AppError('Content ID is required', 400);
  }

  // Set security headers for content delivery
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  next();
};
