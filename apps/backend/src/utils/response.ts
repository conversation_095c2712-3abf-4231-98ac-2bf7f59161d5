/**
 * Standardized Response Utilities for Qalb Healing API
 * Provides consistent response format with Islamic context and cultural sensitivity
 */

import { Response } from 'express';
import { logger } from './logger';

export interface ApiResponse<T = any> {
  status: 'success' | 'error' | 'fail';
  message?: string;
  data?: T;
  meta?: {
    timestamp: string;
    requestId?: string;
    pagination?: PaginationMeta;
    islamicContext?: IslamicContext;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
    islamicGuidance?: string;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface IslamicContext {
  blessing?: string; // Islamic blessing or dua
  guidance?: string; // Relevant Islamic guidance
  verse?: {
    arabic: string;
    translation: string;
    reference: string;
  };
}

/**
 * Islamic blessings and guidance for different contexts
 */
const ISLAMIC_CONTEXTS = {
  success: {
    blessing: "الحمد لله رب العالمين", // <PERSON><PERSON><PERSON><PERSON><PERSON>i rabbil alameen
    translation: "All praise is due to <PERSON>, Lord of the worlds"
  },
  healing: {
    blessing: "وَإِذَا مَرِضْتُ فَهُوَ يَشْفِينِ", // Quran 26:80
    translation: "And when I am ill, it is He who cures me"
  },
  guidance: {
    blessing: "اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ", // Quran 1:6
    translation: "Guide us to the straight path"
  },
  protection: {
    blessing: "حَسْبُنَا اللَّهُ وَنِعْمَ الْوَكِيلُ", // Quran 3:173
    translation: "Allah is sufficient for us, and He is the best Disposer of affairs"
  },
  patience: {
    blessing: "وَاصْبِرْ وَمَا صَبْرُكَ إِلَّا بِاللَّهِ", // Quran 16:127
    translation: "And be patient, and your patience is not but through Allah"
  }
};

/**
 * Error codes specific to Islamic healing workflows
 */
export const ERROR_CODES = {
  // Authentication & Authorization
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID: 'AUTH_INVALID',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // Validation
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Islamic Content
  ISLAMIC_CONTENT_INVALID: 'ISLAMIC_CONTENT_INVALID',
  ARABIC_TEXT_INVALID: 'ARABIC_TEXT_INVALID',
  VERSE_REFERENCE_INVALID: 'VERSE_REFERENCE_INVALID',
  
  // Healing Journey
  JOURNEY_NOT_FOUND: 'JOURNEY_NOT_FOUND',
  JOURNEY_ALREADY_ACTIVE: 'JOURNEY_ALREADY_ACTIVE',
  ASSESSMENT_INCOMPLETE: 'ASSESSMENT_INCOMPLETE',
  
  // Emergency & Crisis
  CRISIS_DETECTION_FAILED: 'CRISIS_DETECTION_FAILED',
  EMERGENCY_SESSION_ACTIVE: 'EMERGENCY_SESSION_ACTIVE',
  ESCALATION_REQUIRED: 'ESCALATION_REQUIRED',
  
  // External Services
  AI_SERVICE_UNAVAILABLE: 'AI_SERVICE_UNAVAILABLE',
  EXTERNAL_API_ERROR: 'EXTERNAL_API_ERROR',
  
  // General
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED'
} as const;

/**
 * Success response with Islamic context
 */
export const sendSuccess = <T>(
  res: Response,
  data: T,
  message?: string,
  statusCode: number = 200,
  context?: 'success' | 'healing' | 'guidance' | 'protection',
  meta?: Partial<ApiResponse['meta']>
): Response => {
  const islamicContext = context ? ISLAMIC_CONTEXTS[context] : ISLAMIC_CONTEXTS.success;
  
  const response: ApiResponse<T> = {
    status: 'success',
    message: message || 'Operation completed successfully',
    data,
    meta: {
      timestamp: new Date().toISOString(),
      islamicContext: {
        blessing: islamicContext.blessing,
        guidance: islamicContext.translation
      },
      ...meta
    }
  };

  logger.info('Success response sent', {
    statusCode,
    message,
    context,
    dataType: typeof data
  });

  return res.status(statusCode).json(response);
};

/**
 * Error response with Islamic guidance
 */
export const sendError = (
  res: Response,
  code: keyof typeof ERROR_CODES,
  message: string,
  statusCode: number = 400,
  details?: any,
  islamicGuidance?: string
): Response => {
  const response: ApiResponse = {
    status: statusCode >= 500 ? 'error' : 'fail',
    error: {
      code,
      message,
      details,
      islamicGuidance: islamicGuidance || getIslamicGuidanceForError(code)
    },
    meta: {
      timestamp: new Date().toISOString(),
      islamicContext: {
        blessing: ISLAMIC_CONTEXTS.patience.blessing,
        guidance: ISLAMIC_CONTEXTS.patience.translation
      }
    }
  };

  logger.error('Error response sent', {
    statusCode,
    code,
    message,
    details
  });

  return res.status(statusCode).json(response);
};

/**
 * Paginated response
 */
export const sendPaginated = <T>(
  res: Response,
  data: T[],
  pagination: PaginationMeta,
  message?: string,
  context?: 'success' | 'healing' | 'guidance'
): Response => {
  return sendSuccess(
    res,
    data,
    message || 'Data retrieved successfully',
    200,
    context,
    { pagination }
  );
};

/**
 * Get Islamic guidance for specific error types
 */
function getIslamicGuidanceForError(code: keyof typeof ERROR_CODES): string {
  const guidanceMap: Record<string, string> = {
    AUTH_REQUIRED: "Authentication is required to access this blessed content. Please sign in to continue your healing journey.",
    AUTH_INVALID: "Your authentication has expired. Please sign in again to continue receiving Allah's guidance.",
    PERMISSION_DENIED: "You don't have permission to access this content. May Allah grant you patience and understanding.",
    VALIDATION_FAILED: "Please check your input and try again. Allah loves those who are careful and precise.",
    JOURNEY_NOT_FOUND: "The healing journey you're looking for cannot be found. Trust in Allah's plan for your spiritual growth.",
    CRISIS_DETECTION_FAILED: "We're here to support you. Please reach out to our crisis support team or emergency services if needed.",
    AI_SERVICE_UNAVAILABLE: "Our AI guidance service is temporarily unavailable. Please try again later, and remember that Allah is the ultimate healer.",
    RATE_LIMIT_EXCEEDED: "Please wait a moment before trying again. Patience is a virtue beloved by Allah.",
    INTERNAL_ERROR: "An unexpected error occurred. We seek Allah's forgiveness and are working to resolve this issue."
  };

  return guidanceMap[code] || "May Allah grant you ease in this difficulty. Please try again or contact our support team.";
}

/**
 * Create pagination metadata
 */
export const createPaginationMeta = (
  page: number,
  limit: number,
  total: number
): PaginationMeta => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

/**
 * Validation error response
 */
export const sendValidationError = (
  res: Response,
  errors: Array<{ field: string; message: string }>,
  islamicGuidance?: string
): Response => {
  return sendError(
    res,
    'VALIDATION_FAILED',
    'Please correct the following errors and try again',
    400,
    { validationErrors: errors },
    islamicGuidance || "Please review your input carefully. Allah loves those who strive for excellence in all they do."
  );
};

/**
 * Not found response
 */
export const sendNotFound = (
  res: Response,
  resource: string = 'Resource',
  islamicGuidance?: string
): Response => {
  return sendError(
    res,
    'RESOURCE_NOT_FOUND',
    `${resource} not found`,
    404,
    undefined,
    islamicGuidance || "What you seek may not be available at this time. Trust in Allah's wisdom and timing."
  );
};

/**
 * Unauthorized response
 */
export const sendUnauthorized = (
  res: Response,
  message: string = 'Authentication required',
  islamicGuidance?: string
): Response => {
  return sendError(
    res,
    'AUTH_REQUIRED',
    message,
    401,
    undefined,
    islamicGuidance || "Please authenticate to access this blessed content and continue your spiritual journey."
  );
};

/**
 * Forbidden response
 */
export const sendForbidden = (
  res: Response,
  message: string = 'Access denied',
  islamicGuidance?: string
): Response => {
  return sendError(
    res,
    'PERMISSION_DENIED',
    message,
    403,
    undefined,
    islamicGuidance || "You don't have permission to access this content. May Allah grant you patience and understanding."
  );
};

/**
 * Rate limit exceeded response
 */
export const sendRateLimitExceeded = (
  res: Response,
  retryAfter?: number,
  islamicGuidance?: string
): Response => {
  const response = sendError(
    res,
    'RATE_LIMIT_EXCEEDED',
    'Too many requests. Please try again later.',
    429,
    { retryAfter },
    islamicGuidance || "Patience is a virtue beloved by Allah. Please wait a moment before trying again."
  );

  if (retryAfter) {
    res.set('Retry-After', retryAfter.toString());
  }

  return response;
};

/**
 * Internal server error response
 */
export const sendInternalError = (
  res: Response,
  message: string = 'Internal server error',
  details?: any
): Response => {
  return sendError(
    res,
    'INTERNAL_ERROR',
    message,
    500,
    process.env.NODE_ENV === 'development' ? details : undefined,
    "An unexpected error occurred. We seek Allah's forgiveness and are working to resolve this issue."
  );
};