/**
 * Enhanced Logger for Qalb Healing API
 * Provides structured logging with Islamic context and comprehensive monitoring
 */

import winston from 'winston';
import path from 'path';

/**
 * Log levels with Islamic context
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly'
}

/**
 * Islamic context for different log types
 */
const ISLAMIC_LOG_CONTEXT = {
  error: {
    prefix: 'استغفر الله', // <PERSON>tag<PERSON><PERSON><PERSON><PERSON>
    meaning: 'I seek forgiveness from <PERSON>'
  },
  warn: {
    prefix: 'لا حول ولا قوة إلا بالله', // La hawla wa la quwwata illa billah
    meaning: 'There is no power except with Allah'
  },
  info: {
    prefix: 'الحمد لله', // Alhamdulillah
    meaning: 'All praise is due to Allah'
  },
  success: {
    prefix: 'بارك الله فيك', // Barakallahu feek
    meaning: 'May Allah bless you'
  },
  auth: {
    prefix: 'بسم الله', // Bismillah
    meaning: 'In the name of <PERSON>'
  },
  crisis: {
    prefix: 'حسبنا الله ونعم الوكيل', // Hasbunallahu wa ni'mal wakeel
    meaning: 'Allah is sufficient for us and He is the best Disposer of affairs'
  }
};

/**
 * Custom log format with Islamic context
 */
const islamicLogFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...meta } = info;
    
    // Add Islamic context for certain log types
    let islamicContext = '';
    if (meta.islamicContext && ISLAMIC_LOG_CONTEXT[meta.islamicContext as keyof typeof ISLAMIC_LOG_CONTEXT]) {
      const context = ISLAMIC_LOG_CONTEXT[meta.islamicContext as keyof typeof ISLAMIC_LOG_CONTEXT];
      islamicContext = ` [${context.prefix}]`;
    }
    
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level.toUpperCase()}]${islamicContext}: ${message} ${metaString}`;
  })
);

/**
 * Console format for development
 */
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...meta } = info;
    
    let islamicContext = '';
    if (meta.islamicContext && ISLAMIC_LOG_CONTEXT[meta.islamicContext as keyof typeof ISLAMIC_LOG_CONTEXT]) {
      const context = ISLAMIC_LOG_CONTEXT[meta.islamicContext as keyof typeof ISLAMIC_LOG_CONTEXT];
      islamicContext = ` 🤲 ${context.meaning}`;
    }
    
    const metaString = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    return `${timestamp} ${level}: ${message}${islamicContext}${metaString}`;
  })
);

/**
 * Create log directory if it doesn't exist
 */
const logDir = process.env.LOG_DIR || 'logs';
const fs = require('fs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * Enhanced Winston logger configuration
 */
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: islamicLogFormat,
  defaultMeta: {
    service: 'qalb-healing-api',
    version: process.env.API_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // Error logs
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      )
    }),
    
    // Combined logs
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // Authentication logs
    new winston.transports.File({
      filename: path.join(logDir, 'auth.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format((info) => {
          return info.category === 'auth' ? info : false;
        })()
      )
    }),
    
    // Crisis logs (for emergency situations)
    new winston.transports.File({
      filename: path.join(logDir, 'crisis.log'),
      level: 'warn',
      maxsize: 5242880, // 5MB
      maxFiles: 10, // Keep more crisis logs
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format((info) => {
          return info.category === 'crisis' || info.islamicContext === 'crisis' ? info : false;
        })()
      )
    }),
    
    // Audit logs
    new winston.transports.File({
      filename: path.join(logDir, 'audit.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 10,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format((info) => {
          return info.category === 'audit' ? info : false;
        })()
      )
    })
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      maxsize: 5242880,
      maxFiles: 5
    })
  ],
  
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      maxsize: 5242880,
      maxFiles: 5
    })
  ]
});

// Add console transport for non-production environments
if (process.env.NODE_ENV !== 'production') {
  logger.add(
    new winston.transports.Console({
      format: consoleFormat
    })
  );
}

/**
 * Enhanced logger with Islamic context methods
 */
class IslamicLogger {
  private winston: winston.Logger;
  
  constructor(winstonLogger: winston.Logger) {
    this.winston = winstonLogger;
  }
  
  /**
   * Log with Islamic context
   */
  private logWithContext(
    level: LogLevel,
    message: string,
    meta: any = {},
    islamicContext?: keyof typeof ISLAMIC_LOG_CONTEXT
  ): void {
    this.winston.log(level, message, {
      ...meta,
      islamicContext,
      timestamp: new Date().toISOString()
    });
  }
  
  /**
   * Error logging with Istighfar context
   */
  error(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.ERROR, message, meta, 'error');
  }
  
  /**
   * Warning logging with Tawakkul context
   */
  warn(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.WARN, message, meta, 'warn');
  }
  
  /**
   * Info logging with Hamdala context
   */
  info(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.INFO, message, meta, 'info');
  }
  
  /**
   * Debug logging
   */
  debug(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.DEBUG, message, meta);
  }
  
  /**
   * HTTP request logging
   */
  http(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.HTTP, message, meta);
  }
  
  /**
   * Authentication logging with Bismillah context
   */
  auth(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.INFO, message, {
      ...meta,
      category: 'auth'
    }, 'auth');
  }
  
  /**
   * Crisis logging with Hasbunallah context
   */
  crisis(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.WARN, message, {
      ...meta,
      category: 'crisis'
    }, 'crisis');
  }
  
  /**
   * Success logging with Barakallah context
   */
  success(message: string, meta: any = {}): void {
    this.logWithContext(LogLevel.INFO, message, meta, 'success');
  }
  
  /**
   * Audit logging for compliance
   */
  audit(action: string, meta: any = {}): void {
    this.logWithContext(LogLevel.INFO, `Audit: ${action}`, {
      ...meta,
      category: 'audit',
      auditTimestamp: new Date().toISOString()
    });
  }
  
  /**
   * Performance logging
   */
  performance(operation: string, duration: number, meta: any = {}): void {
    this.logWithContext(LogLevel.INFO, `Performance: ${operation}`, {
      ...meta,
      duration,
      category: 'performance'
    });
  }
  
  /**
   * Security logging
   */
  security(event: string, meta: any = {}): void {
    this.logWithContext(LogLevel.WARN, `Security: ${event}`, {
      ...meta,
      category: 'security'
    });
  }
  
  /**
   * Islamic content logging
   */
  islamicContent(action: string, content: any, meta: any = {}): void {
    this.logWithContext(LogLevel.INFO, `Islamic Content: ${action}`, {
      ...meta,
      content,
      category: 'islamic_content'
    });
  }
  
  /**
   * Journey logging
   */
  journey(event: string, journeyData: any, meta: any = {}): void {
    this.logWithContext(LogLevel.INFO, `Journey: ${event}`, {
      ...meta,
      journeyData,
      category: 'journey'
    });
  }
  
  /**
   * Emergency session logging
   */
  emergency(event: string, sessionData: any, meta: any = {}): void {
    this.logWithContext(LogLevel.WARN, `Emergency: ${event}`, {
      ...meta,
      sessionData,
      category: 'emergency'
    }, 'crisis');
  }
}

// Create enhanced logger instance
const islamicLogger = new IslamicLogger(logger);

/**
 * Request logging middleware
 */
export const requestLogger = (req: any, res: any, next: any): void => {
  const startTime = Date.now();
  
  // Log request start
  islamicLogger.http('Request started', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });
  
  // Log response
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const level = res.statusCode >= 400 ? 'error' : 'info';
    
    islamicLogger[level as 'error' | 'info']('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userId: req.user?.id
    });
  });
  
  next();
};

/**
 * Log rotation utility
 */
export const logRotation = {
  /**
   * Archive old logs
   */
  archiveLogs: async (): Promise<void> => {
    try {
      const archiveDir = path.join(logDir, 'archive');
      if (!fs.existsSync(archiveDir)) {
        fs.mkdirSync(archiveDir, { recursive: true });
      }
      
      // Implementation would move old logs to archive
      islamicLogger.info('Log archiving completed');
    } catch (error) {
      islamicLogger.error('Log archiving failed', { error: (error as Error).message });
    }
  },
  
  /**
   * Clean up old archived logs
   */
  cleanupOldLogs: async (retentionDays: number = 90): Promise<void> => {
    try {
      const cutoffDate = new Date(Date.now() - (retentionDays * 24 * 60 * 60 * 1000));
      
      // Implementation would delete logs older than cutoff date
      islamicLogger.info('Old log cleanup completed', { retentionDays, cutoffDate });
    } catch (error) {
      islamicLogger.error('Old log cleanup failed', { error: (error as Error).message });
    }
  }
};

/**
 * Export both the original winston logger and enhanced Islamic logger
 */
export { logger, islamicLogger };
export default islamicLogger;