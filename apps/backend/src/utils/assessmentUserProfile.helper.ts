import { Prisma } from '../config/database';
import { AIServiceUserProfileData } from '../services/ai.service';
import { DiagnosisDelivery } from '../models/Assessment';

export function prepareProfileForAI(
  userProfile: Prisma.JsonValue | null | undefined,
  userId: string
): AIServiceUserProfileData {
  const rawProfile = (userProfile as Prisma.JsonObject) || {}; // Cast to JsonObject
  
  // Handle nested profile structure - check if data is under 'userProfile' key
  let profile = rawProfile;
  if (rawProfile.userProfile && typeof rawProfile.userProfile === 'object') {
    profile = rawProfile.userProfile as Prisma.JsonObject;
  }
  
  // Ensure user_id in the profile data is the authenticated user's ID
  const result = {
    user_id: userId, // Always use the authenticated userId
    mental_health_awareness: (profile.mental_health_awareness || profile.mentalHealthAwareness) as any,
    ruqya_knowledge: (profile.ruqya_knowledge || profile.ruqyaKnowledge) as any,
    spiritual_optimizer: (profile.spiritual_optimizer || profile.spiritualOptimizer) as any,
    professional_context: (profile.professional_context || profile.professionalContext) as any,
    demographics: profile.demographics as any,
    life_circumstances: (profile.life_circumstances || profile.lifeCircumstances) as any,
    // Add any additional fields that might be in the profile
    preferences: profile.preferences as any,
    crisisIndicators: profile.crisisIndicators as any,
    featureAccessibility: profile.featureAccessibility as any,
    learningHistory: profile.learningHistory as any,
    privacySettings: profile.privacySettings as any,
  };
  
  
  return result;
}

export function determineUserTypeFromProfile(profile: AIServiceUserProfileData): string {
  
  // Handle the actual profile structure from the provided example
  const mentalHealthAwareness = profile.mental_health_awareness as any;
  const ruqyaKnowledge = profile.ruqya_knowledge as any;
  
  
  // Check for spiritual optimizer types first
  if (profile.spiritual_optimizer?.type === 'clinical_integration') return 'clinical_spiritual_optimizer';
  if (profile.spiritual_optimizer?.type === 'traditional_bridge') return 'traditional_spiritual_optimizer';
  
  // Handle mental_health_awareness with nested structure
  let awarenessLevel = '';
  if (mentalHealthAwareness?.level?.mental_health_awareness) {
    awarenessLevel = mentalHealthAwareness.level.mental_health_awareness;
  } else if (mentalHealthAwareness?.level) {
    awarenessLevel = mentalHealthAwareness.level;
  } else if (typeof mentalHealthAwareness === 'string') {
    awarenessLevel = mentalHealthAwareness;
  }
  
  // Handle ruqya_knowledge with nested structure
  let ruqyaLevel = '';
  if (ruqyaKnowledge?.level?.ruqya_knowledge) {
    ruqyaLevel = ruqyaKnowledge.level.ruqya_knowledge;
  } else if (ruqyaKnowledge?.level) {
    ruqyaLevel = ruqyaKnowledge.level;
  } else if (typeof ruqyaKnowledge === 'string') {
    ruqyaLevel = ruqyaKnowledge;
  }
  
  
  // Determine user type based on awareness and knowledge levels
  if (awarenessLevel === 'clinical_aware') {
    return 'clinically_aware';
  }
  
  if (ruqyaLevel === 'expert') {
    return 'ruqya_expert';
  }
  
  if (awarenessLevel === 'crisis') {
    return 'crisis_support';
  }
  
  if (awarenessLevel === 'spiritual_growth') {
    return 'spiritual_growth';
  }
  
  if (awarenessLevel === 'new_muslim') {
    return 'new_muslim';
  }
  
  if (awarenessLevel === 'clinical_integration') {
    return 'clinical_integration';
  }
  
  if (awarenessLevel === 'traditional_bridge') {
    return 'traditional_bridge';
  }
  
  // Default based on awareness level or fallback
  return awarenessLevel || 'symptom_aware';
}

export function getDeliveryStyleForUserType(userType: string): DiagnosisDelivery['deliveryStyle'] {
  const styles: Record<string, DiagnosisDelivery['deliveryStyle']> = {
    clinically_aware: 'clinical',
    ruqya_expert: 'advanced',
    clinical_spiritual_optimizer: 'clinical', // Can be 'professional' or 'advanced_clinical'
    traditional_spiritual_optimizer: 'traditional', // Can be 'leadership' or 'advanced_traditional'
    symptom_aware: 'gentle',
    new_muslim: 'gentle_educational', // Example for new muslim
  };
  return styles[userType] || 'gentle'; // Default style
}
