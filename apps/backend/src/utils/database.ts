/**
 * Database Optimization Utilities for Qalb Healing API
 * Provides connection pooling, query optimization, and performance monitoring
 */

import { PrismaClient } from '@prisma/client';
import { logger } from './logger';

/**
 * Enhanced Prisma client with connection pooling and optimization
 */
class DatabaseManager {
  private static instance: DatabaseManager;
  private prisma: PrismaClient;
  private connectionPool: {
    maxConnections: number;
    currentConnections: number;
    idleTimeout: number;
  };

  private constructor() {
    this.connectionPool = {
      maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
      currentConnections: 0,
      idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000')
    };

    this.prisma = new PrismaClient({
      log: [
        { level: 'query', emit: 'event' },
        { level: 'error', emit: 'event' },
        { level: 'info', emit: 'event' },
        { level: 'warn', emit: 'event' },
      ],
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });

    this.setupEventListeners();
    this.setupConnectionPooling();
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  public getClient(): PrismaClient {
    return this.prisma;
  }

  /**
   * Setup event listeners for query monitoring
   */
  private setupEventListeners(): void {
    this.prisma.$on('query', (e) => {
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Database query executed', {
          query: e.query,
          params: e.params,
          duration: e.duration,
          target: e.target
        });
      }

      // Log slow queries
      if (e.duration > 1000) {
        logger.warn('Slow query detected', {
          query: e.query,
          duration: e.duration,
          target: e.target
        });
      }
    });

    this.prisma.$on('error', (e) => {
      logger.error('Database error', {
        message: e.message,
        target: e.target
      });
    });

    this.prisma.$on('info', (e) => {
      logger.info('Database info', {
        message: e.message,
        target: e.target
      });
    });

    this.prisma.$on('warn', (e) => {
      logger.warn('Database warning', {
        message: e.message,
        target: e.target
      });
    });
  }

  /**
   * Setup connection pooling
   */
  private setupConnectionPooling(): void {
    // Monitor connection count
    setInterval(() => {
      this.logConnectionStats();
    }, 60000); // Log every minute
  }

  /**
   * Log connection statistics
   */
  private logConnectionStats(): void {
    logger.info('Database connection stats', {
      maxConnections: this.connectionPool.maxConnections,
      currentConnections: this.connectionPool.currentConnections,
      idleTimeout: this.connectionPool.idleTimeout
    });
  }

  /**
   * Health check for database connection
   */
  public async healthCheck(): Promise<{ status: string; latency: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      const latency = Date.now() - startTime;
      
      return {
        status: 'healthy',
        latency
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: 'unhealthy',
        latency,
        error: (error as Error).message
      };
    }
  }

  /**
   * Graceful shutdown
   */
  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      logger.info('Database connection closed gracefully');
    } catch (error) {
      logger.error('Error closing database connection', {
        error: (error as Error).message
      });
    }
  }
}

// Export singleton instance
export const dbManager = DatabaseManager.getInstance();
export const prisma = dbManager.getClient();

/**
 * Query optimization utilities
 */
export class QueryOptimizer {
  /**
   * Optimized pagination with cursor-based pagination for large datasets
   */
  static async paginateWithCursor<T>(
    model: any,
    options: {
      where?: any;
      orderBy?: any;
      select?: any;
      include?: any;
      cursor?: any;
      take: number;
      skip?: number;
    }
  ): Promise<{
    data: T[];
    hasMore: boolean;
    nextCursor?: any;
    totalCount?: number;
  }> {
    const { where, orderBy, select, include, cursor, take, skip } = options;

    // Get data with one extra item to check if there are more
    const data = await model.findMany({
      where,
      orderBy,
      select,
      include,
      cursor,
      take: take + 1,
      skip
    });

    const hasMore = data.length > take;
    if (hasMore) {
      data.pop(); // Remove the extra item
    }

    const nextCursor = hasMore && data.length > 0 
      ? { id: data[data.length - 1].id }
      : undefined;

    return {
      data,
      hasMore,
      nextCursor
    };
  }

  /**
   * Optimized search with full-text search capabilities
   */
  static async searchWithFullText<T>(
    model: any,
    searchTerm: string,
    options: {
      searchFields: string[];
      where?: any;
      orderBy?: any;
      select?: any;
      include?: any;
      take?: number;
      skip?: number;
    }
  ): Promise<T[]> {
    const { searchFields, where = {}, orderBy, select, include, take, skip } = options;

    // Create search conditions for each field
    const searchConditions = searchFields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive' as const
      }
    }));

    const searchWhere = {
      ...where,
      OR: searchConditions
    };

    return await model.findMany({
      where: searchWhere,
      orderBy,
      select,
      include,
      take,
      skip
    });
  }

  /**
   * Batch operations for better performance
   */
  static async batchCreate<T>(
    model: any,
    data: any[],
    batchSize: number = 100
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      const batchResults = await model.createMany({
        data: batch,
        skipDuplicates: true
      });
      results.push(...batchResults);
    }
    
    return results;
  }

  /**
   * Optimized aggregation queries
   */
  static async aggregateWithGrouping(
    model: any,
    options: {
      where?: any;
      groupBy: string[];
      aggregations: {
        _count?: any;
        _sum?: any;
        _avg?: any;
        _min?: any;
        _max?: any;
      };
    }
  ) {
    const { where, groupBy, aggregations } = options;

    return await model.groupBy({
      by: groupBy,
      where,
      ...aggregations
    });
  }
}

/**
 * Islamic content specific database utilities
 */
export class IslamicContentDB {
  /**
   * Search Quranic verses with Arabic text support
   */
  static async searchQuranicVerses(
    searchTerm: string,
    options: {
      searchInArabic?: boolean;
      searchInTranslation?: boolean;
      surahFilter?: number;
      take?: number;
      skip?: number;
    } = {}
  ) {
    const {
      searchInArabic = true,
      searchInTranslation = true,
      surahFilter,
      take = 20,
      skip = 0
    } = options;

    const searchConditions = [];

    if (searchInArabic) {
      searchConditions.push({
        arabic: {
          contains: searchTerm,
          mode: 'insensitive' as const
        }
      });
    }

    if (searchInTranslation) {
      searchConditions.push({
        translation: {
          contains: searchTerm,
          mode: 'insensitive' as const
        }
      });
    }

    const where: any = {
      OR: searchConditions
    };

    if (surahFilter) {
      where.surah = surahFilter;
    }

    return await prisma.quranicVerseContent.findMany({
      where,
      orderBy: [
        { surah: 'asc' },
        { ayah: 'asc' }
      ],
      take,
      skip
    });
  }

  /**
   * Get Names of Allah with benefits
   */
  static async getNamesOfAllahWithBenefits(
    category?: string,
    take: number = 99
  ) {
    return await prisma.nameOfAllahContent.findMany({
      where: category ? { category } : undefined,
      include: {
        benefits: true
      },
      orderBy: {
        sequenceOrder: 'asc'
      },
      take
    });
  }

  /**
   * Get user's healing journey progress with analytics
   */
  static async getUserJourneyAnalytics(userId: string) {
    const [journeys, progress, milestones] = await Promise.all([
      prisma.journey.findMany({
        where: { userId },
        include: {
          journeyProgress: {
            orderBy: { date: 'desc' },
            take: 30 // Last 30 days
          }
        }
      }),
      prisma.journeyProgress.aggregate({
        where: { userId },
        _avg: {
          overallRating: true,
          moodBefore: true,
          moodAfter: true,
          spiritualConnection: true
        },
        _count: {
          id: true
        }
      }),
      prisma.milestoneCompletion.count({
        where: { userId }
      })
    ]);

    return {
      journeys,
      averageRatings: progress._avg,
      totalProgressEntries: progress._count.id,
      milestonesCompleted: milestones
    };
  }

  /**
   * Get emergency session statistics
   */
  static async getEmergencySessionStats(userId: string, timeframe: 'week' | 'month' | 'year' = 'month') {
    const now = new Date();
    const timeframeMap = {
      week: 7,
      month: 30,
      year: 365
    };
    
    const startDate = new Date(now.getTime() - (timeframeMap[timeframe] * 24 * 60 * 60 * 1000));

    return await prisma.emergencySession.aggregate({
      where: {
        userId,
        startTime: {
          gte: startDate
        }
      },
      _count: {
        id: true
      },
      _avg: {
        effectivenessRating: true
      }
    });
  }
}

/**
 * Database indexing recommendations
 */
export const indexingRecommendations = {
  // Indexes for better query performance
  profiles: [
    'CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);',
    'CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);'
  ],
  
  journeys: [
    'CREATE INDEX IF NOT EXISTS idx_journeys_user_id ON journeys(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_journeys_status ON journeys(status);',
    'CREATE INDEX IF NOT EXISTS idx_journeys_created_at ON journeys(created_at);'
  ],
  
  journey_progress: [
    'CREATE INDEX IF NOT EXISTS idx_journey_progress_user_journey ON journey_progress(user_id, journey_id);',
    'CREATE INDEX IF NOT EXISTS idx_journey_progress_date ON journey_progress(date);'
  ],
  
  emergency_sessions: [
    'CREATE INDEX IF NOT EXISTS idx_emergency_sessions_user_id ON emergency_sessions(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_emergency_sessions_start_time ON emergency_sessions(start_time);',
    'CREATE INDEX IF NOT EXISTS idx_emergency_sessions_status ON emergency_sessions(status);'
  ],
  
  quranic_verse_content: [
    'CREATE INDEX IF NOT EXISTS idx_quranic_verses_surah_ayah ON quranic_verse_content(surah, ayah);',
    'CREATE INDEX IF NOT EXISTS idx_quranic_verses_category ON quranic_verse_content(category);'
  ],
  
  name_of_allah_content: [
    'CREATE INDEX IF NOT EXISTS idx_names_allah_category ON name_of_allah_content(category);',
    'CREATE INDEX IF NOT EXISTS idx_names_allah_sequence ON name_of_allah_content(sequence_order);'
  ]
};

/**
 * Apply database indexes
 */
export async function applyDatabaseIndexes(): Promise<void> {
  try {
    logger.info('Applying database indexes for optimization...');
    
    for (const [table, indexes] of Object.entries(indexingRecommendations)) {
      for (const indexQuery of indexes) {
        try {
          await prisma.$executeRawUnsafe(indexQuery);
          logger.info(`Applied index for ${table}`, { query: indexQuery });
        } catch (error) {
          // Index might already exist, log but don't fail
          logger.warn(`Index application failed for ${table}`, {
            query: indexQuery,
            error: (error as Error).message
          });
        }
      }
    }
    
    logger.info('Database indexes application completed');
  } catch (error) {
    logger.error('Failed to apply database indexes', {
      error: (error as Error).message
    });
  }
}

/**
 * Database maintenance utilities
 */
export class DatabaseMaintenance {
  /**
   * Clean up old data based on retention policies
   */
  static async cleanupOldData(): Promise<void> {
    const retentionDays = parseInt(process.env.DATA_RETENTION_DAYS || '365');
    const cutoffDate = new Date(Date.now() - (retentionDays * 24 * 60 * 60 * 1000));

    try {
      // Clean up old analytics data
      const deletedAnalytics = await prisma.analyticsMetric.deleteMany({
        where: {
          time: {
            lt: cutoffDate
          }
        }
      });

      // Clean up old emergency sessions (keep for longer due to importance)
      const emergencyRetentionDays = retentionDays * 2;
      const emergencyCutoffDate = new Date(Date.now() - (emergencyRetentionDays * 24 * 60 * 60 * 1000));
      
      const deletedEmergencySessions = await prisma.emergencySession.deleteMany({
        where: {
          startTime: {
            lt: emergencyCutoffDate
          },
          status: 'completed'
        }
      });

      logger.info('Data cleanup completed', {
        deletedAnalytics: deletedAnalytics.count,
        deletedEmergencySessions: deletedEmergencySessions.count,
        cutoffDate,
        emergencyCutoffDate
      });
    } catch (error) {
      logger.error('Data cleanup failed', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Analyze database performance
   */
  static async analyzePerformance(): Promise<any> {
    try {
      // Get table sizes
      const tableSizes = await prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
      `;

      // Get slow queries (if query logging is enabled)
      const slowQueries = await prisma.$queryRaw`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements 
        WHERE mean_time > 100
        ORDER BY mean_time DESC
        LIMIT 10;
      `;

      return {
        tableSizes,
        slowQueries,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Performance analysis failed', {
        error: (error as Error).message
      });
      return null;
    }
  }
}

// Graceful shutdown handler
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, closing database connections...');
  await dbManager.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, closing database connections...');
  await dbManager.disconnect();
  process.exit(0);
});