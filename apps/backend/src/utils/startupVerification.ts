/**
 * Startup Verification Utilities for Qalb Healing API
 * Ensures all enhanced systems are properly initialized
 */

import { islamicLogger } from './enhancedLogger';
import { dbManager } from './database';
import { cacheManager } from './cache';
import { healthManager } from './health';

/**
 * Verification results interface
 */
interface VerificationResult {
  component: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  details?: any;
}

/**
 * Startup verification manager
 */
export class StartupVerification {
  private results: VerificationResult[] = [];

  /**
   * Run all startup verifications
   */
  async runAllVerifications(): Promise<boolean> {
    islamicLogger.info('Starting enhanced backend verification...');
    
    this.results = [];
    
    // Run all verification checks
    await this.verifyDatabase();
    await this.verifyCache();
    await this.verifyHealthSystem();
    await this.verifyEnvironmentVariables();
    await this.verifyEnhancedFeatures();
    
    // Analyze results
    const hasErrors = this.results.some(r => r.status === 'error');
    const hasWarnings = this.results.some(r => r.status === 'warning');
    
    // Log summary
    this.logVerificationSummary();
    
    if (hasErrors) {
      islamicLogger.error('Startup verification failed with errors');
      return false;
    } else if (hasWarnings) {
      islamicLogger.warn('Startup verification completed with warnings');
      return true;
    } else {
      islamicLogger.success('All startup verifications passed successfully');
      return true;
    }
  }

  /**
   * Verify database connectivity and optimization
   */
  private async verifyDatabase(): Promise<void> {
    try {
      const healthCheck = await dbManager.healthCheck();
      
      if (healthCheck.status === 'healthy') {
        this.results.push({
          component: 'Database',
          status: 'success',
          message: 'Database connection healthy',
          details: { latency: healthCheck.latency }
        });
      } else {
        this.results.push({
          component: 'Database',
          status: 'error',
          message: 'Database connection failed',
          details: { error: healthCheck.error }
        });
      }
    } catch (error) {
      this.results.push({
        component: 'Database',
        status: 'error',
        message: 'Database verification failed',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Verify Redis cache connectivity
   */
  private async verifyCache(): Promise<void> {
    try {
      const healthCheck = await cacheManager.healthCheck();
      
      if (healthCheck.status === 'healthy') {
        this.results.push({
          component: 'Redis Cache',
          status: 'success',
          message: 'Redis cache connection healthy',
          details: { latency: healthCheck.latency }
        });
      } else {
        this.results.push({
          component: 'Redis Cache',
          status: 'warning',
          message: 'Redis cache connection failed - will operate without caching',
          details: { error: healthCheck.error }
        });
      }
    } catch (error) {
      this.results.push({
        component: 'Redis Cache',
        status: 'warning',
        message: 'Redis cache verification failed - will operate without caching',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Verify health monitoring system
   */
  private async verifyHealthSystem(): Promise<void> {
    try {
      const health = await healthManager.performHealthCheck();
      
      this.results.push({
        component: 'Health Monitoring',
        status: 'success',
        message: 'Health monitoring system operational',
        details: { 
          overallStatus: health.status,
          uptime: health.uptime
        }
      });
    } catch (error) {
      this.results.push({
        component: 'Health Monitoring',
        status: 'error',
        message: 'Health monitoring system failed',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Verify required environment variables
   */
  private async verifyEnvironmentVariables(): Promise<void> {
    const requiredVars = [
      'DATABASE_URL',
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY',
      'JWT_SECRET'
    ];

    const optionalVars = [
      'REDIS_HOST',
      'AI_SERVICE_URL',
      'OPENAI_API_KEY'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);
    const missingOptional = optionalVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
      this.results.push({
        component: 'Environment Variables',
        status: 'error',
        message: 'Required environment variables missing',
        details: { missing }
      });
    } else {
      this.results.push({
        component: 'Environment Variables',
        status: 'success',
        message: 'All required environment variables present',
        details: { 
          missingOptional: missingOptional.length > 0 ? missingOptional : undefined
        }
      });
    }

    if (missingOptional.length > 0) {
      this.results.push({
        component: 'Optional Features',
        status: 'warning',
        message: 'Some optional features may not work due to missing environment variables',
        details: { missingOptional }
      });
    }
  }

  /**
   * Verify enhanced features are working
   */
  private async verifyEnhancedFeatures(): Promise<void> {
    const features = [
      { name: 'Enhanced Logging', check: () => islamicLogger.info('Test log') },
      { name: 'Response Utilities', check: () => true }, // Basic check
      { name: 'Validation System', check: () => true }, // Basic check
      { name: 'Error Handling', check: () => true }, // Basic check
    ];

    let successCount = 0;
    const errors: string[] = [];

    for (const feature of features) {
      try {
        feature.check();
        successCount++;
      } catch (error) {
        errors.push(`${feature.name}: ${(error as Error).message}`);
      }
    }

    if (errors.length === 0) {
      this.results.push({
        component: 'Enhanced Features',
        status: 'success',
        message: 'All enhanced features operational',
        details: { featuresCount: features.length }
      });
    } else {
      this.results.push({
        component: 'Enhanced Features',
        status: 'warning',
        message: 'Some enhanced features may not be working properly',
        details: { errors, successCount, totalCount: features.length }
      });
    }
  }

  /**
   * Log verification summary
   */
  private logVerificationSummary(): void {
    const successCount = this.results.filter(r => r.status === 'success').length;
    const warningCount = this.results.filter(r => r.status === 'warning').length;
    const errorCount = this.results.filter(r => r.status === 'error').length;

    islamicLogger.info('Startup verification summary', {
      total: this.results.length,
      success: successCount,
      warnings: warningCount,
      errors: errorCount
    });

    // Log each result
    this.results.forEach(result => {
      const logMethod = result.status === 'error' ? 'error' : 
                      result.status === 'warning' ? 'warn' : 'info';
      
      islamicLogger[logMethod](`${result.component}: ${result.message}`, result.details);
    });

    // Islamic blessing based on results
    if (errorCount === 0 && warningCount === 0) {
      islamicLogger.success('الحمد لله - All systems are blessed and operational');
    } else if (errorCount === 0) {
      islamicLogger.info('الحمد لله - Systems operational with minor issues');
    } else {
      islamicLogger.error('استغفر الله - Critical issues detected, seeking Allah\'s guidance');
    }
  }

  /**
   * Get verification results
   */
  getResults(): VerificationResult[] {
    return [...this.results];
  }

  /**
   * Get verification summary
   */
  getSummary(): { success: number; warnings: number; errors: number; total: number } {
    return {
      success: this.results.filter(r => r.status === 'success').length,
      warnings: this.results.filter(r => r.status === 'warning').length,
      errors: this.results.filter(r => r.status === 'error').length,
      total: this.results.length
    };
  }
}

/**
 * Quick verification for development
 */
export async function quickVerification(): Promise<boolean> {
  const verifier = new StartupVerification();
  return await verifier.runAllVerifications();
}

/**
 * Detailed verification with results
 */
export async function detailedVerification(): Promise<{
  success: boolean;
  results: VerificationResult[];
  summary: { success: number; warnings: number; errors: number; total: number };
}> {
  const verifier = new StartupVerification();
  const success = await verifier.runAllVerifications();
  
  return {
    success,
    results: verifier.getResults(),
    summary: verifier.getSummary()
  };
}