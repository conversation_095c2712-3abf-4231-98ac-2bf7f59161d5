import { Prisma } from '@prisma/client'; // Assuming Prisma types might be used for profile structure
import { AIServiceUserProfileData } from '../../services/ai.service'; // Adjust path as necessary
import { DiagnosisDelivery } from '../../models/Assessment'; // Adjust path as necessary
import {
  prepareProfileForAI,
  determineUserTypeFromProfile,
  getDeliveryStyleForUserType,
} from '../assessmentUserProfile.helper'; // Adjust path as necessary

describe('assessmentUserProfile.helper', () => {
  const testUserId = 'user-123';

  describe('prepareProfileForAI', () => {
    it('should set user_id correctly and handle null profile', () => {
      const result = prepareProfileForAI(null, testUserId);
      expect(result.user_id).toBe(testUserId);
      expect(result.mental_health_awareness).toBeUndefined();
    });

    it('should use the passed userId even if profile contains a different user_id', () => {
      const profileWithId = { user_id: 'other-user-id' } as Prisma.JsonObject;
      const result = prepareProfileForAI(profileWithId, testUserId);
      expect(result.user_id).toBe(testUserId);
    });

    it('should map known profile fields', () => {
      const profile = {
        mentalHealthAwareness: { level: 'aware' },
        ruqyaKnowledge: { level: 'basic' },
        spiritualOptimizer: { type: 'personal' },
        professionalContext: { role: 'student' },
        demographics: { age: 30 },
        lifeCircumstances: { challenges: ['stress'] },
      } as Prisma.JsonObject;
      const result = prepareProfileForAI(profile, testUserId);
      expect(result.user_id).toBe(testUserId);
      expect(result.mental_health_awareness).toEqual({ level: 'aware' });
      expect(result.ruqya_knowledge).toEqual({ level: 'basic' });
      expect(result.spiritual_optimizer).toEqual({ type: 'personal' });
      expect(result.professional_context).toEqual({ role: 'student' });
      expect(result.demographics).toEqual({ age: 30 });
      expect(result.life_circumstances).toEqual({ challenges: ['stress'] });
    });

    it('should prefer direct properties like mental_health_awareness over mentalHealthAwareness if both exist', () => {
      // Based on current implementation: (profile.mental_health_awareness || profile.mentalHealthAwareness)
      // This means if `mental_health_awareness` is present, it's used.
      const profile = {
        mental_health_awareness: { level: 'direct' },
        mentalHealthAwareness: { level: 'camelCase' },
      } as Prisma.JsonObject;
      const result = prepareProfileForAI(profile, testUserId);
      expect(result.mental_health_awareness).toEqual({ level: 'direct' });
    });

    it('should use camelCase property like mentalHealthAwareness if direct is not present', () => {
      const profile = {
        mentalHealthAwareness: { level: 'camelCaseOnly' },
      } as Prisma.JsonObject;
      const result = prepareProfileForAI(profile, testUserId);
      expect(result.mental_health_awareness).toEqual({ level: 'camelCaseOnly' });
    });
  });

  describe('determineUserTypeFromProfile', () => {
    it('should identify "clinical_spiritual_optimizer"', () => {
      const profile: AIServiceUserProfileData = {
        user_id: testUserId,
        spiritual_optimizer: { type: 'clinical_integration' },
      };
      expect(determineUserTypeFromProfile(profile)).toBe('clinical_spiritual_optimizer');
    });

    it('should identify "traditional_spiritual_optimizer"', () => {
      const profile: AIServiceUserProfileData = {
        user_id: testUserId,
        spiritual_optimizer: { type: 'traditional_bridge' },
      };
      expect(determineUserTypeFromProfile(profile)).toBe('traditional_spiritual_optimizer');
    });

    it('should identify "clinically_aware"', () => {
      const profile: AIServiceUserProfileData = {
        user_id: testUserId,
        mental_health_awareness: { level: 'clinical_aware' },
      };
      expect(determineUserTypeFromProfile(profile)).toBe('clinically_aware');
    });

    it('should identify "ruqya_expert"', () => {
      const profile: AIServiceUserProfileData = {
        user_id: testUserId,
        ruqya_knowledge: { level: 'expert' },
      };
      expect(determineUserTypeFromProfile(profile)).toBe('ruqya_expert');
    });

    it('should identify "new_muslim" (example)', () => {
      const profile: AIServiceUserProfileData = { user_id: 'new_user_test-123' };
      expect(determineUserTypeFromProfile(profile)).toBe('new_muslim');
    });

    it('should default to "symptom_aware"', () => {
      const profile: AIServiceUserProfileData = { user_id: testUserId }; // Basic profile
      expect(determineUserTypeFromProfile(profile)).toBe('symptom_aware');
    });

    // --- New tests for precedence and edge cases ---
    it('should prioritize spiritual_optimizer over other roles', () => {
      const profile: AIServiceUserProfileData = {
        user_id: testUserId,
        spiritual_optimizer: { type: 'clinical_integration' },
        mental_health_awareness: { level: 'clinical_aware' },
        ruqya_knowledge: { level: 'expert' },
      };
      expect(determineUserTypeFromProfile(profile)).toBe('clinical_spiritual_optimizer');
    });

    it('should prioritize clinically_aware over ruqya_expert if optimizer not present', () => {
      const profile: AIServiceUserProfileData = {
        user_id: testUserId,
        mental_health_awareness: { level: 'clinical_aware' },
        ruqya_knowledge: { level: 'expert' },
      };
      expect(determineUserTypeFromProfile(profile)).toBe('clinically_aware');
    });

    it('should prioritize ruqya_expert over new_muslim if optimizer and clinical_aware not present', () => {
        const profile: AIServiceUserProfileData = {
          user_id: 'new_user_test-123', // also qualifies for new_muslim by user_id prefix
          ruqya_knowledge: { level: 'expert' },
        };
        expect(determineUserTypeFromProfile(profile)).toBe('ruqya_expert');
      });

    it('should handle missing type or level properties gracefully and default', () => {
      let profile: AIServiceUserProfileData = { user_id: testUserId, spiritual_optimizer: {} };
      expect(determineUserTypeFromProfile(profile)).toBe('symptom_aware');

      profile = { user_id: testUserId, mental_health_awareness: {} };
      expect(determineUserTypeFromProfile(profile)).toBe('symptom_aware');

      profile = { user_id: testUserId, ruqya_knowledge: {} };
      expect(determineUserTypeFromProfile(profile)).toBe('symptom_aware');
    });

    it('should correctly identify new_muslim via lifeCircumstances.islamicJourneyStage', () => {
        const profile: AIServiceUserProfileData = {
          user_id: testUserId,
          life_circumstances: { islamicJourneyStage: 'new_muslim' },
        };
        expect(determineUserTypeFromProfile(profile)).toBe('new_muslim');
      });

    // Note: The 'new_muslim_within_2_years' in 'situations' array is NOT part of the helper's logic.
    // The AI service's `determine_user_type` has that, but this backend helper does not.
    // So, a test for that specific path in *this* helper would fail unless the helper is updated.
    // For now, testing only what's in this helper.
    // If alignment is desired, this helper should be updated, then tested.

  });

  describe('getDeliveryStyleForUserType', () => {
    it('should return "clinical" for "clinically_aware"', () => {
      expect(getDeliveryStyleForUserType('clinically_aware')).toBe('clinical');
    });

    it('should return "advanced" for "ruqya_expert"', () => {
      expect(getDeliveryStyleForUserType('ruqya_expert')).toBe('advanced');
    });

    it('should return "clinical" for "clinical_spiritual_optimizer"', () => {
      expect(getDeliveryStyleForUserType('clinical_spiritual_optimizer')).toBe('clinical');
    });

    it('should return "traditional" for "traditional_spiritual_optimizer"', () => {
      expect(getDeliveryStyleForUserType('traditional_spiritual_optimizer')).toBe('traditional');
    });

    it('should return "gentle_educational" for "new_muslim"', () => {
      expect(getDeliveryStyleForUserType('new_muslim')).toBe('gentle_educational');
    });

    it('should return "gentle" for "symptom_aware"', () => {
      expect(getDeliveryStyleForUserType('symptom_aware')).toBe('gentle');
    });

    it('should return "gentle" for an unknown user type', () => {
      expect(getDeliveryStyleForUserType('unknown_type_123')).toBe('gentle');
    });
  });
});
