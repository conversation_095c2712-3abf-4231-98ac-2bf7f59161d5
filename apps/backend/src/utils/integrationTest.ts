/**
 * Integration Test for Enhanced Backend Architecture
 * Tests all enhanced features and Islamic context integration
 */

import axios from 'axios';
import { islamicLogger } from './enhancedLogger';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'skip';
  message: string;
  details?: any;
}

/**
 * Integration test suite for enhanced backend
 */
export class IntegrationTestSuite {
  private baseUrl: string;
  private results: TestResult[] = [];

  constructor(baseUrl: string = 'http://localhost:3333') {
    this.baseUrl = baseUrl;
  }

  /**
   * Run all integration tests
   */
  async runAllTests(): Promise<boolean> {
    islamicLogger.info('Starting enhanced backend integration tests...');
    
    this.results = [];
    
    // Test basic server functionality
    await this.testServerHealth();
    await this.testApiVersioning();
    await this.testHealthEndpoints();
    await this.testEnhancedResponses();
    await this.testIslamicContext();
    await this.testErrorHandling();
    await this.testValidation();
    
    // Log results
    this.logTestResults();
    
    const failedTests = this.results.filter(r => r.status === 'fail');
    return failedTests.length === 0;
  }

  /**
   * Test basic server health
   */
  private async testServerHealth(): Promise<void> {
    try {
      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: 5000
      });
      
      if (response.status === 200 && response.data.status === 'success') {
        this.results.push({
          test: 'Server Health',
          status: 'pass',
          message: 'Server is responding correctly',
          details: { responseTime: response.headers['x-response-time'] }
        });
      } else {
        this.results.push({
          test: 'Server Health',
          status: 'fail',
          message: 'Server health check failed',
          details: { status: response.status, data: response.data }
        });
      }
    } catch (error) {
      this.results.push({
        test: 'Server Health',
        status: 'fail',
        message: 'Server is not responding',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Test API versioning
   */
  private async testApiVersioning(): Promise<void> {
    try {
      // Test v1 endpoint
      const v1Response = await axios.get(`${this.baseUrl}/api/v1/info`, {
        timeout: 5000
      });
      
      if (v1Response.status === 200 && v1Response.headers['x-api-version'] === 'v1') {
        this.results.push({
          test: 'API Versioning',
          status: 'pass',
          message: 'API versioning working correctly',
          details: { version: v1Response.headers['x-api-version'] }
        });
      } else {
        this.results.push({
          test: 'API Versioning',
          status: 'fail',
          message: 'API versioning not working',
          details: { headers: v1Response.headers }
        });
      }
    } catch (error) {
      this.results.push({
        test: 'API Versioning',
        status: 'fail',
        message: 'API versioning test failed',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Test health endpoints
   */
  private async testHealthEndpoints(): Promise<void> {
    const endpoints = [
      '/health',
      '/health/detailed',
      '/health/ready',
      '/health/live',
      '/metrics'
    ];

    let passedCount = 0;
    const details: any = {};

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${this.baseUrl}${endpoint}`, {
          timeout: 5000
        });
        
        if (response.status === 200) {
          passedCount++;
          details[endpoint] = 'pass';
        } else {
          details[endpoint] = `fail (${response.status})`;
        }
      } catch (error) {
        details[endpoint] = `error (${(error as Error).message})`;
      }
    }

    if (passedCount === endpoints.length) {
      this.results.push({
        test: 'Health Endpoints',
        status: 'pass',
        message: 'All health endpoints working',
        details
      });
    } else {
      this.results.push({
        test: 'Health Endpoints',
        status: 'fail',
        message: `${passedCount}/${endpoints.length} health endpoints working`,
        details
      });
    }
  }

  /**
   * Test enhanced response format
   */
  private async testEnhancedResponses(): Promise<void> {
    try {
      const response = await axios.get(`${this.baseUrl}/info`);
      
      const hasStandardFormat = response.data.status && 
                               response.data.data && 
                               response.data.meta &&
                               response.data.meta.islamicContext;

      if (hasStandardFormat) {
        this.results.push({
          test: 'Enhanced Response Format',
          status: 'pass',
          message: 'Enhanced response format working correctly',
          details: { 
            hasIslamicContext: !!response.data.meta.islamicContext,
            hasMetadata: !!response.data.meta
          }
        });
      } else {
        this.results.push({
          test: 'Enhanced Response Format',
          status: 'fail',
          message: 'Enhanced response format not implemented',
          details: { responseStructure: Object.keys(response.data) }
        });
      }
    } catch (error) {
      this.results.push({
        test: 'Enhanced Response Format',
        status: 'fail',
        message: 'Enhanced response format test failed',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Test Islamic context integration
   */
  private async testIslamicContext(): Promise<void> {
    try {
      const response = await axios.get(`${this.baseUrl}/info`);
      
      const hasIslamicGreeting = response.data.data.islamicGreeting &&
                                response.data.data.islamicGreeting.arabic &&
                                response.data.data.islamicGreeting.translation;

      const hasIslamicMeta = response.data.meta.islamicContext &&
                            response.data.meta.islamicContext.blessing;

      if (hasIslamicGreeting && hasIslamicMeta) {
        this.results.push({
          test: 'Islamic Context Integration',
          status: 'pass',
          message: 'Islamic context properly integrated',
          details: { 
            hasGreeting: hasIslamicGreeting,
            hasMetaContext: hasIslamicMeta
          }
        });
      } else {
        this.results.push({
          test: 'Islamic Context Integration',
          status: 'fail',
          message: 'Islamic context not properly integrated',
          details: { 
            hasGreeting: hasIslamicGreeting,
            hasMetaContext: hasIslamicMeta
          }
        });
      }
    } catch (error) {
      this.results.push({
        test: 'Islamic Context Integration',
        status: 'fail',
        message: 'Islamic context test failed',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Test enhanced error handling
   */
  private async testErrorHandling(): Promise<void> {
    try {
      // Test 404 error
      const response = await axios.get(`${this.baseUrl}/api/v1/nonexistent`, {
        validateStatus: () => true // Don't throw on 4xx/5xx
      });
      
      const hasEnhancedError = response.status === 404 &&
                              response.data.status === 'fail' &&
                              response.data.error &&
                              response.data.error.islamicGuidance;

      if (hasEnhancedError) {
        this.results.push({
          test: 'Enhanced Error Handling',
          status: 'pass',
          message: 'Enhanced error handling working correctly',
          details: { 
            statusCode: response.status,
            hasIslamicGuidance: !!response.data.error.islamicGuidance
          }
        });
      } else {
        this.results.push({
          test: 'Enhanced Error Handling',
          status: 'fail',
          message: 'Enhanced error handling not working',
          details: { 
            statusCode: response.status,
            responseData: response.data
          }
        });
      }
    } catch (error) {
      this.results.push({
        test: 'Enhanced Error Handling',
        status: 'fail',
        message: 'Error handling test failed',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Test validation system
   */
  private async testValidation(): Promise<void> {
    try {
      // Test validation on a protected endpoint (if available)
      // This is a basic test - more comprehensive tests would require authentication
      
      this.results.push({
        test: 'Validation System',
        status: 'skip',
        message: 'Validation test skipped - requires authentication setup',
        details: { reason: 'Authentication required for validation testing' }
      });
    } catch (error) {
      this.results.push({
        test: 'Validation System',
        status: 'fail',
        message: 'Validation test failed',
        details: { error: (error as Error).message }
      });
    }
  }

  /**
   * Log test results
   */
  private logTestResults(): void {
    const passedTests = this.results.filter(r => r.status === 'pass').length;
    const failedTests = this.results.filter(r => r.status === 'fail').length;
    const skippedTests = this.results.filter(r => r.status === 'skip').length;

    islamicLogger.info('Integration test results', {
      total: this.results.length,
      passed: passedTests,
      failed: failedTests,
      skipped: skippedTests
    });

    // Log each test result
    this.results.forEach(result => {
      const logMethod = result.status === 'fail' ? 'error' : 
                       result.status === 'skip' ? 'warn' : 'info';
      
      islamicLogger[logMethod](`${result.test}: ${result.message}`, result.details);
    });

    // Islamic blessing based on results
    if (failedTests === 0) {
      islamicLogger.success('الحمد لله - All integration tests passed successfully');
    } else {
      islamicLogger.error(`استغفر الله - ${failedTests} integration tests failed`);
    }
  }

  /**
   * Get test results
   */
  getResults(): TestResult[] {
    return [...this.results];
  }

  /**
   * Get test summary
   */
  getSummary(): { passed: number; failed: number; skipped: number; total: number } {
    return {
      passed: this.results.filter(r => r.status === 'pass').length,
      failed: this.results.filter(r => r.status === 'fail').length,
      skipped: this.results.filter(r => r.status === 'skip').length,
      total: this.results.length
    };
  }
}

/**
 * Quick integration test
 */
export async function runQuickIntegrationTest(baseUrl?: string): Promise<boolean> {
  const testSuite = new IntegrationTestSuite(baseUrl);
  return await testSuite.runAllTests();
}

/**
 * Detailed integration test with results
 */
export async function runDetailedIntegrationTest(baseUrl?: string): Promise<{
  success: boolean;
  results: TestResult[];
  summary: { passed: number; failed: number; skipped: number; total: number };
}> {
  const testSuite = new IntegrationTestSuite(baseUrl);
  const success = await testSuite.runAllTests();
  
  return {
    success,
    results: testSuite.getResults(),
    summary: testSuite.getSummary()
  };
}