/**
 * Health Check and Monitoring Utilities for Qalb Healing API
 * Comprehensive system health monitoring with Islamic context
 */

import { Request, Response } from 'express';
import { dbManager } from './database';
import { cacheManager } from './cache';
import { logger } from './logger';
import { sendSuccess, sendError } from './response';
import axios from 'axios';

/**
 * Health check status types
 */
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy'
}

/**
 * Service health check interface
 */
interface ServiceHealth {
  status: HealthStatus;
  latency: number;
  error?: string;
  details?: any;
  lastChecked: string;
}

/**
 * Overall system health interface
 */
interface SystemHealth {
  status: HealthStatus;
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: ServiceHealth;
    cache: ServiceHealth;
    aiService: ServiceHealth;
    supabase: ServiceHealth;
  };
  metrics: {
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: any;
    activeConnections: number;
  };
  islamicContext: {
    blessing: string;
    guidance: string;
  };
}

/**
 * Health Check Manager
 */
class HealthCheckManager {
  private static instance: HealthCheckManager;
  private healthHistory: SystemHealth[] = [];
  private maxHistorySize = 100;
  private lastHealthCheck: SystemHealth | null = null;

  private constructor() {
    // Start periodic health checks
    this.startPeriodicHealthChecks();
  }

  public static getInstance(): HealthCheckManager {
    if (!HealthCheckManager.instance) {
      HealthCheckManager.instance = new HealthCheckManager();
    }
    return HealthCheckManager.instance;
  }

  /**
   * Start periodic health checks
   */
  private startPeriodicHealthChecks(): void {
    // Check health every 30 seconds
    setInterval(async () => {
      try {
        const health = await this.performHealthCheck();
        this.addToHistory(health);
        
        // Log critical issues
        if (health.status === HealthStatus.UNHEALTHY) {
          logger.error('System health check failed', { health });
        } else if (health.status === HealthStatus.DEGRADED) {
          logger.warn('System health degraded', { health });
        }
      } catch (error) {
        logger.error('Periodic health check failed', {
          error: (error as Error).message
        });
      }
    }, 30000);
  }

  /**
   * Add health check to history
   */
  private addToHistory(health: SystemHealth): void {
    this.healthHistory.push(health);
    this.lastHealthCheck = health;
    
    // Keep only the last N health checks
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory.shift();
    }
  }

  /**
   * Perform comprehensive health check
   */
  public async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now();
    
    try {
      // Check all services in parallel
      const [databaseHealth, cacheHealth, aiServiceHealth, supabaseHealth] = await Promise.allSettled([
        this.checkDatabaseHealth(),
        this.checkCacheHealth(),
        this.checkAIServiceHealth(),
        this.checkSupabaseHealth()
      ]);

      // Extract results
      const database = this.extractHealthResult(databaseHealth);
      const cache = this.extractHealthResult(cacheHealth);
      const aiService = this.extractHealthResult(aiServiceHealth);
      const supabase = this.extractHealthResult(supabaseHealth);

      // Determine overall status
      const overallStatus = this.determineOverallStatus([database, cache, aiService, supabase]);

      // Get system metrics
      const metrics = await this.getSystemMetrics();

      const health: SystemHealth = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.API_VERSION || '1.0.0',
        services: {
          database,
          cache,
          aiService,
          supabase
        },
        metrics,
        islamicContext: this.getIslamicContext(overallStatus)
      };

      return health;
    } catch (error) {
      logger.error('Health check failed', {
        error: (error as Error).message,
        duration: Date.now() - startTime
      });

      return {
        status: HealthStatus.UNHEALTHY,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.API_VERSION || '1.0.0',
        services: {
          database: { status: HealthStatus.UNHEALTHY, latency: 0, error: 'Health check failed', lastChecked: new Date().toISOString() },
          cache: { status: HealthStatus.UNHEALTHY, latency: 0, error: 'Health check failed', lastChecked: new Date().toISOString() },
          aiService: { status: HealthStatus.UNHEALTHY, latency: 0, error: 'Health check failed', lastChecked: new Date().toISOString() },
          supabase: { status: HealthStatus.UNHEALTHY, latency: 0, error: 'Health check failed', lastChecked: new Date().toISOString() }
        },
        metrics: {
          memoryUsage: process.memoryUsage(),
          cpuUsage: process.cpuUsage(),
          activeConnections: 0
        },
        islamicContext: this.getIslamicContext(HealthStatus.UNHEALTHY)
      };
    }
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<ServiceHealth> {
    try {
      const result = await dbManager.healthCheck();
      return {
        status: result.status === 'healthy' ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        latency: result.latency,
        error: result.error,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        latency: 0,
        error: (error as Error).message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Check cache health
   */
  private async checkCacheHealth(): Promise<ServiceHealth> {
    try {
      const result = await cacheManager.healthCheck();
      return {
        status: result.status === 'healthy' ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        latency: result.latency,
        error: result.error,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        latency: 0,
        error: (error as Error).message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Check AI service health
   */
  private async checkAIServiceHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      const aiServiceUrl = process.env.AI_SERVICE_URL;
      if (!aiServiceUrl) {
        return {
          status: HealthStatus.DEGRADED,
          latency: 0,
          error: 'AI service URL not configured',
          lastChecked: new Date().toISOString()
        };
      }

      const response = await axios.get(`${aiServiceUrl}/health`, {
        timeout: 5000
      });

      const latency = Date.now() - startTime;

      return {
        status: response.status === 200 ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        latency,
        details: response.data,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: HealthStatus.UNHEALTHY,
        latency,
        error: (error as Error).message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Check Supabase health
   */
  private async checkSupabaseHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      const supabaseUrl = process.env.SUPABASE_URL;
      if (!supabaseUrl) {
        return {
          status: HealthStatus.UNHEALTHY,
          latency: 0,
          error: 'Supabase URL not configured',
          lastChecked: new Date().toISOString()
        };
      }

      const response = await axios.get(`${supabaseUrl}/rest/v1/`, {
        timeout: 5000,
        headers: {
          'apikey': process.env.SUPABASE_ANON_KEY || ''
        }
      });

      const latency = Date.now() - startTime;

      return {
        status: response.status === 200 ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        latency,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: HealthStatus.UNHEALTHY,
        latency,
        error: (error as Error).message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Extract health result from Promise.allSettled
   */
  private extractHealthResult(result: PromiseSettledResult<ServiceHealth>): ServiceHealth {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return {
        status: HealthStatus.UNHEALTHY,
        latency: 0,
        error: result.reason?.message || 'Unknown error',
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Determine overall system status
   */
  private determineOverallStatus(services: ServiceHealth[]): HealthStatus {
    const unhealthyCount = services.filter(s => s.status === HealthStatus.UNHEALTHY).length;
    const degradedCount = services.filter(s => s.status === HealthStatus.DEGRADED).length;

    if (unhealthyCount > 0) {
      return HealthStatus.UNHEALTHY;
    } else if (degradedCount > 0) {
      return HealthStatus.DEGRADED;
    } else {
      return HealthStatus.HEALTHY;
    }
  }

  /**
   * Get system metrics
   */
  private async getSystemMetrics(): Promise<SystemHealth['metrics']> {
    return {
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      activeConnections: 0 // This would need to be tracked separately
    };
  }

  /**
   * Get Islamic context based on health status
   */
  private getIslamicContext(status: HealthStatus): { blessing: string; guidance: string } {
    switch (status) {
      case HealthStatus.HEALTHY:
        return {
          blessing: "الحمد لله رب العالمين",
          guidance: "All praise is due to Allah, Lord of the worlds. The system is functioning well by Allah's grace."
        };
      case HealthStatus.DEGRADED:
        return {
          blessing: "حسبنا الله ونعم الوكيل",
          guidance: "Allah is sufficient for us, and He is the best Disposer of affairs. Some services are experiencing issues."
        };
      case HealthStatus.UNHEALTHY:
        return {
          blessing: "إنا لله وإنا إليه راجعون",
          guidance: "Indeed we belong to Allah, and indeed to Him we will return. The system requires immediate attention."
        };
      default:
        return {
          blessing: "بسم الله الرحمن الرحيم",
          guidance: "In the name of Allah, the Most Gracious, the Most Merciful."
        };
    }
  }

  /**
   * Get health history
   */
  public getHealthHistory(): SystemHealth[] {
    return [...this.healthHistory];
  }

  /**
   * Get last health check
   */
  public getLastHealthCheck(): SystemHealth | null {
    return this.lastHealthCheck;
  }

  /**
   * Get health trends
   */
  public getHealthTrends(): any {
    if (this.healthHistory.length < 2) {
      return null;
    }

    const recent = this.healthHistory.slice(-10);
    const statusCounts = recent.reduce((acc, health) => {
      acc[health.status] = (acc[health.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgLatencies = {
      database: recent.reduce((sum, h) => sum + h.services.database.latency, 0) / recent.length,
      cache: recent.reduce((sum, h) => sum + h.services.cache.latency, 0) / recent.length,
      aiService: recent.reduce((sum, h) => sum + h.services.aiService.latency, 0) / recent.length,
      supabase: recent.reduce((sum, h) => sum + h.services.supabase.latency, 0) / recent.length
    };

    return {
      statusDistribution: statusCounts,
      averageLatencies: avgLatencies,
      totalChecks: recent.length,
      timeRange: {
        start: recent[0].timestamp,
        end: recent[recent.length - 1].timestamp
      }
    };
  }
}

// Export singleton instance
export const healthManager = HealthCheckManager.getInstance();

/**
 * Health check route handlers
 */
export const healthRoutes = {
  /**
   * Basic health check endpoint
   */
  basic: async (req: Request, res: Response): Promise<void> => {
    try {
      const health = await healthManager.performHealthCheck();
      
      if (health.status === HealthStatus.HEALTHY) {
        sendSuccess(res, health, 'System is healthy', 200, 'success');
      } else if (health.status === HealthStatus.DEGRADED) {
        sendSuccess(res, health, 'System is degraded', 200, 'guidance');
      } else {
        sendError(res, 'INTERNAL_ERROR', 'System is unhealthy', 503, health);
      }
    } catch (error) {
      sendError(res, 'INTERNAL_ERROR', 'Health check failed', 500, {
        error: (error as Error).message
      });
    }
  },

  /**
   * Detailed health check endpoint
   */
  detailed: async (req: Request, res: Response): Promise<void> => {
    try {
      const health = await healthManager.performHealthCheck();
      const trends = healthManager.getHealthTrends();
      const history = healthManager.getHealthHistory().slice(-20); // Last 20 checks

      const detailedHealth = {
        ...health,
        trends,
        recentHistory: history
      };

      sendSuccess(res, detailedHealth, 'Detailed health information', 200, 'success');
    } catch (error) {
      sendError(res, 'INTERNAL_ERROR', 'Detailed health check failed', 500, {
        error: (error as Error).message
      });
    }
  },

  /**
   * Readiness probe (for Kubernetes)
   */
  ready: async (req: Request, res: Response): Promise<void> => {
    try {
      const health = await healthManager.performHealthCheck();
      
      if (health.status === HealthStatus.UNHEALTHY) {
        res.status(503).json({
          status: 'not ready',
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(200).json({
          status: 'ready',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      res.status(503).json({
        status: 'not ready',
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      });
    }
  },

  /**
   * Liveness probe (for Kubernetes)
   */
  live: async (req: Request, res: Response): Promise<void> => {
    res.status(200).json({
      status: 'alive',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      pid: process.pid
    });
  },

  /**
   * Metrics endpoint (Prometheus format)
   */
  metrics: async (req: Request, res: Response): Promise<void> => {
    try {
      const health = await healthManager.performHealthCheck();
      const trends = healthManager.getHealthTrends();

      // Convert to Prometheus format
      const metrics = [
        `# HELP qalb_system_health Overall system health status`,
        `# TYPE qalb_system_health gauge`,
        `qalb_system_health{status="${health.status}"} ${health.status === HealthStatus.HEALTHY ? 1 : 0}`,
        '',
        `# HELP qalb_service_latency Service response latency in milliseconds`,
        `# TYPE qalb_service_latency gauge`,
        `qalb_service_latency{service="database"} ${health.services.database.latency}`,
        `qalb_service_latency{service="cache"} ${health.services.cache.latency}`,
        `qalb_service_latency{service="ai_service"} ${health.services.aiService.latency}`,
        `qalb_service_latency{service="supabase"} ${health.services.supabase.latency}`,
        '',
        `# HELP qalb_memory_usage Memory usage in bytes`,
        `# TYPE qalb_memory_usage gauge`,
        `qalb_memory_usage{type="rss"} ${health.metrics.memoryUsage.rss}`,
        `qalb_memory_usage{type="heapTotal"} ${health.metrics.memoryUsage.heapTotal}`,
        `qalb_memory_usage{type="heapUsed"} ${health.metrics.memoryUsage.heapUsed}`,
        '',
        `# HELP qalb_uptime Process uptime in seconds`,
        `# TYPE qalb_uptime counter`,
        `qalb_uptime ${health.uptime}`
      ].join('\n');

      res.set('Content-Type', 'text/plain');
      res.send(metrics);
    } catch (error) {
      res.status(500).send(`# Error generating metrics: ${(error as Error).message}`);
    }
  }
};

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static requestTimes: Map<string, number[]> = new Map();
  private static errorCounts: Map<string, number> = new Map();

  /**
   * Track request performance
   */
  static trackRequest(endpoint: string, duration: number): void {
    if (!this.requestTimes.has(endpoint)) {
      this.requestTimes.set(endpoint, []);
    }
    
    const times = this.requestTimes.get(endpoint)!;
    times.push(duration);
    
    // Keep only last 100 requests
    if (times.length > 100) {
      times.shift();
    }
  }

  /**
   * Track errors
   */
  static trackError(endpoint: string): void {
    const current = this.errorCounts.get(endpoint) || 0;
    this.errorCounts.set(endpoint, current + 1);
  }

  /**
   * Get performance statistics
   */
  static getStats(): any {
    const stats: any = {};
    
    for (const [endpoint, times] of this.requestTimes.entries()) {
      if (times.length > 0) {
        const sorted = [...times].sort((a, b) => a - b);
        stats[endpoint] = {
          count: times.length,
          avg: times.reduce((sum, time) => sum + time, 0) / times.length,
          min: sorted[0],
          max: sorted[sorted.length - 1],
          p50: sorted[Math.floor(sorted.length * 0.5)],
          p95: sorted[Math.floor(sorted.length * 0.95)],
          p99: sorted[Math.floor(sorted.length * 0.99)],
          errors: this.errorCounts.get(endpoint) || 0
        };
      }
    }
    
    return stats;
  }

  /**
   * Reset statistics
   */
  static reset(): void {
    this.requestTimes.clear();
    this.errorCounts.clear();
  }
}

/**
 * Performance monitoring middleware
 */
export const performanceMiddleware = (req: Request, res: Response, next: any): void => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const endpoint = `${req.method} ${req.route?.path || req.path}`;
    
    PerformanceMonitor.trackRequest(endpoint, duration);
    
    if (res.statusCode >= 400) {
      PerformanceMonitor.trackError(endpoint);
    }
    
    // Log slow requests
    if (duration > 1000) {
      logger.warn('Slow request detected', {
        endpoint,
        duration,
        statusCode: res.statusCode,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
  });
  
  next();
};