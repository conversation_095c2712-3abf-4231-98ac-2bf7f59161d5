/**
 * API Versioning Utilities for Qalb Healing API
 * Provides comprehensive API versioning strategy with backward compatibility
 */

import { Request, Response, NextFunction } from 'express';
import { sendError } from './response';
import { islamicLogger } from './enhancedLogger';

/**
 * Supported API versions
 */
export enum ApiVersion {
  V1 = 'v1',
  V2 = 'v2' // For future use
}

/**
 * Version configuration
 */
interface VersionConfig {
  version: ApiVersion;
  isSupported: boolean;
  isDeprecated: boolean;
  deprecationDate?: Date;
  sunsetDate?: Date;
  migrationGuide?: string;
}

/**
 * API version configurations
 */
const VERSION_CONFIGS: Record<ApiVersion, VersionConfig> = {
  [ApiVersion.V1]: {
    version: ApiVersion.V1,
    isSupported: true,
    isDeprecated: false
  },
  [ApiVersion.V2]: {
    version: ApiVersion.V2,
    isSupported: false,
    isDeprecated: false
  }
};

/**
 * Extract API version from request
 */
export const extractApiVersion = (req: Request): ApiVersion => {
  // Check URL path first (/api/v1/...)
  const pathVersion = req.path.match(/^\/api\/(v\d+)\//)?.[1] as ApiVersion;
  if (pathVersion && Object.values(ApiVersion).includes(pathVersion)) {
    return pathVersion;
  }

  // Check Accept header (application/vnd.qalb.v1+json)
  const acceptHeader = req.get('Accept');
  if (acceptHeader) {
    const headerVersion = acceptHeader.match(/application\/vnd\.qalb\.(v\d+)\+json/)?.[1] as ApiVersion;
    if (headerVersion && Object.values(ApiVersion).includes(headerVersion)) {
      return headerVersion;
    }
  }

  // Check custom header
  const customHeader = req.get('X-API-Version') as ApiVersion;
  if (customHeader && Object.values(ApiVersion).includes(customHeader)) {
    return customHeader;
  }

  // Default to v1
  return ApiVersion.V1;
};

/**
 * API version validation middleware
 */
export const validateApiVersion = (req: Request, res: Response, next: NextFunction): void => {
  const version = extractApiVersion(req);
  const config = VERSION_CONFIGS[version];

  // Attach version to request
  (req as any).apiVersion = version;

  if (!config.isSupported) {
    islamicLogger.warn('Unsupported API version requested', {
      version,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    sendError(
      res,
      'INVALID_INPUT',
      `API version ${version} is not supported`,
      400,
      {
        supportedVersions: Object.keys(VERSION_CONFIGS).filter(v => VERSION_CONFIGS[v as ApiVersion].isSupported),
        requestedVersion: version
      },
      'The requested API version is not available. Please use a supported version to continue your spiritual journey.'
    );
    return;
  }

  if (config.isDeprecated) {
    // Add deprecation headers
    res.set('X-API-Deprecated', 'true');
    if (config.deprecationDate) {
      res.set('X-API-Deprecation-Date', config.deprecationDate.toISOString());
    }
    if (config.sunsetDate) {
      res.set('X-API-Sunset-Date', config.sunsetDate.toISOString());
    }
    if (config.migrationGuide) {
      res.set('X-API-Migration-Guide', config.migrationGuide);
    }

    islamicLogger.warn('Deprecated API version used', {
      version,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      deprecationDate: config.deprecationDate,
      sunsetDate: config.sunsetDate
    });
  }

  // Add version headers to response
  res.set('X-API-Version', version);
  res.set('X-API-Supported-Versions', Object.keys(VERSION_CONFIGS).filter(v => VERSION_CONFIGS[v as ApiVersion].isSupported).join(', '));

  next();
};

/**
 * Version-specific feature flags
 */
export const getVersionFeatures = (version: ApiVersion): Record<string, boolean> => {
  const features: Record<ApiVersion, Record<string, boolean>> = {
    [ApiVersion.V1]: {
      enhancedErrorHandling: true,
      islamicContextResponses: true,
      comprehensiveValidation: true,
      rbacSupport: true,
      caching: true,
      healthChecks: true,
      performanceMonitoring: true,
      auditLogging: true,
      crisisDetection: true,
      emergencyEscalation: true,
      journeyAnalytics: true,
      islamicContentCaching: true,
      realTimeNotifications: false, // Future feature
      advancedAnalytics: false, // Future feature
      multiLanguageSupport: false // Future feature
    },
    [ApiVersion.V2]: {
      // Future features for v2
      enhancedErrorHandling: true,
      islamicContextResponses: true,
      comprehensiveValidation: true,
      rbacSupport: true,
      caching: true,
      healthChecks: true,
      performanceMonitoring: true,
      auditLogging: true,
      crisisDetection: true,
      emergencyEscalation: true,
      journeyAnalytics: true,
      islamicContentCaching: true,
      realTimeNotifications: true,
      advancedAnalytics: true,
      multiLanguageSupport: true,
      enhancedAI: true,
      blockchainIntegration: false
    }
  };

  return features[version] || features[ApiVersion.V1];
};

/**
 * Feature flag middleware
 */
export const requireFeature = (featureName: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const version = (req as any).apiVersion || ApiVersion.V1;
    const features = getVersionFeatures(version);

    if (!features[featureName]) {
      islamicLogger.warn('Feature not available in API version', {
        feature: featureName,
        version,
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      sendError(
        res,
        'INVALID_INPUT',
        `Feature '${featureName}' is not available in API version ${version}`,
        400,
        {
          feature: featureName,
          version,
          availableFeatures: Object.keys(features).filter(f => features[f])
        },
        'This feature is not available in your current API version. Please upgrade to access this functionality.'
      );
      return;
    }

    next();
  };
};

/**
 * Version-specific response transformer
 */
export const transformResponse = (data: any, version: ApiVersion): any => {
  switch (version) {
    case ApiVersion.V1:
      return transformV1Response(data);
    case ApiVersion.V2:
      return transformV2Response(data);
    default:
      return data;
  }
};

/**
 * Transform response for API v1
 */
function transformV1Response(data: any): any {
  // V1 response format is our current standard
  return data;
}

/**
 * Transform response for API v2 (future)
 */
function transformV2Response(data: any): any {
  // Future v2 transformations would go here
  // For example: enhanced metadata, different field names, etc.
  return {
    ...data,
    apiVersion: ApiVersion.V2,
    enhancedMetadata: {
      processingTime: Date.now(),
      features: ['enhanced-ai', 'real-time', 'multi-language']
    }
  };
}

/**
 * Backward compatibility utilities
 */
export class BackwardCompatibility {
  /**
   * Convert old request format to new format
   */
  static convertRequest(req: Request, fromVersion: ApiVersion, toVersion: ApiVersion): any {
    const body = req.body;
    
    // Example conversion logic
    if (fromVersion === ApiVersion.V1 && toVersion === ApiVersion.V2) {
      // Convert v1 request to v2 format
      return {
        ...body,
        metadata: {
          convertedFrom: fromVersion,
          convertedTo: toVersion,
          timestamp: new Date().toISOString()
        }
      };
    }
    
    return body;
  }

  /**
   * Convert new response format to old format
   */
  static convertResponse(data: any, fromVersion: ApiVersion, toVersion: ApiVersion): any {
    // Example conversion logic
    if (fromVersion === ApiVersion.V2 && toVersion === ApiVersion.V1) {
      // Remove v2-specific fields for v1 compatibility
      const { enhancedMetadata, ...v1Data } = data;
      return v1Data;
    }
    
    return data;
  }
}

/**
 * API documentation versioning
 */
export const getVersionedSwaggerConfig = (version: ApiVersion) => {
  const baseConfig = {
    openapi: '3.0.0',
    info: {
      title: 'Qalb Healing API',
      description: 'Backend API for Qalb-centric Islamic healing platform',
      contact: {
        name: 'Qalb Healing Team',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `${process.env.API_BASE_URL || 'http://localhost:3333'}/api/${version}`,
        description: `${version.toUpperCase()} API Server`,
      },
    ],
  };

  switch (version) {
    case ApiVersion.V1:
      return {
        ...baseConfig,
        info: {
          ...baseConfig.info,
          version: '1.0.0',
          description: `${baseConfig.info.description} - Version 1.0 with comprehensive Islamic healing features`
        }
      };
    case ApiVersion.V2:
      return {
        ...baseConfig,
        info: {
          ...baseConfig.info,
          version: '2.0.0',
          description: `${baseConfig.info.description} - Version 2.0 with enhanced AI and real-time features`
        }
      };
    default:
      return baseConfig;
  }
};

/**
 * Version deprecation utilities
 */
export class VersionDeprecation {
  /**
   * Mark a version as deprecated
   */
  static deprecateVersion(
    version: ApiVersion,
    deprecationDate: Date,
    sunsetDate: Date,
    migrationGuide: string
  ): void {
    VERSION_CONFIGS[version] = {
      ...VERSION_CONFIGS[version],
      isDeprecated: true,
      deprecationDate,
      sunsetDate,
      migrationGuide
    };

    islamicLogger.warn('API version deprecated', {
      version,
      deprecationDate,
      sunsetDate,
      migrationGuide
    });
  }

  /**
   * Remove support for a version
   */
  static removeVersionSupport(version: ApiVersion): void {
    VERSION_CONFIGS[version] = {
      ...VERSION_CONFIGS[version],
      isSupported: false
    };

    islamicLogger.warn('API version support removed', { version });
  }

  /**
   * Get deprecation status for all versions
   */
  static getDeprecationStatus(): Record<ApiVersion, VersionConfig> {
    return { ...VERSION_CONFIGS };
  }
}

/**
 * Content negotiation for different API versions
 */
export const contentNegotiation = (req: Request, res: Response, next: NextFunction): void => {
  const version = (req as any).apiVersion || ApiVersion.V1;
  const acceptHeader = req.get('Accept');

  // Set appropriate content type based on version
  if (acceptHeader?.includes('application/vnd.qalb')) {
    res.set('Content-Type', `application/vnd.qalb.${version}+json`);
  } else {
    res.set('Content-Type', 'application/json');
  }

  // Add version-specific headers
  res.set('X-Content-Version', version);
  res.set('Vary', 'Accept, X-API-Version');

  next();
};