/**
 * Redis Caching Layer for Qalb Healing API
 * Optimized caching for Islamic content and frequently accessed data
 */

import Redis from 'ioredis';
import { logger } from './logger';

/**
 * Cache configuration
 */
interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
  maxRetriesPerRequest: number;
}

/**
 * Cache key patterns for different content types
 */
export const CACHE_KEYS = {
  // Islamic content
  QURANIC_VERSE: 'quranic_verse',
  NAMES_OF_ALLAH: 'names_of_allah',
  SUNNAH_PRACTICE: 'sunnah_practice',
  DHIKR_CONTENT: 'dhikr_content',
  RUQYAH_VERSES: 'ruqyah_verses',
  
  // User data
  USER_PROFILE: 'user_profile',
  USER_JOURNEY: 'user_journey',
  USER_PROGRESS: 'user_progress',
  
  // Analytics
  JOURNEY_ANALYTICS: 'journey_analytics',
  PLATFORM_STATS: 'platform_stats',
  
  // Emergency
  EMERGENCY_RESOURCES: 'emergency_resources',
  CRISIS_PROTOCOLS: 'crisis_protocols',
  
  // Content collections
  FEATURED_CONTENT: 'featured_content',
  DAILY_CONTENT: 'daily_content',
  
  // API responses
  API_RESPONSE: 'api_response'
} as const;

/**
 * Cache TTL (Time To Live) in seconds
 */
export const CACHE_TTL = {
  // Islamic content (rarely changes)
  ISLAMIC_CONTENT: 24 * 60 * 60, // 24 hours
  QURANIC_VERSES: 7 * 24 * 60 * 60, // 7 days
  NAMES_OF_ALLAH: 7 * 24 * 60 * 60, // 7 days
  
  // User data (changes frequently)
  USER_PROFILE: 30 * 60, // 30 minutes
  USER_PROGRESS: 15 * 60, // 15 minutes
  
  // Analytics (updated periodically)
  ANALYTICS: 60 * 60, // 1 hour
  PLATFORM_STATS: 30 * 60, // 30 minutes
  
  // Emergency content (critical, but stable)
  EMERGENCY_CONTENT: 60 * 60, // 1 hour
  
  // API responses (short-lived)
  API_RESPONSE: 5 * 60, // 5 minutes
  
  // Daily content
  DAILY_CONTENT: 24 * 60 * 60, // 24 hours
  
  // Short-term cache
  SHORT: 60, // 1 minute
  MEDIUM: 15 * 60, // 15 minutes
  LONG: 60 * 60 // 1 hour
} as const;

/**
 * Enhanced Redis Cache Manager
 */
class CacheManager {
  private static instance: CacheManager;
  private redis: Redis;
  private isConnected: boolean = false;
  private config: CacheConfig;

  private constructor() {
    this.config = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'qalb:',
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: 3
    };

    this.redis = new Redis({
      ...this.config,
      lazyConnect: true,
      retryDelayOnFailover: this.config.retryDelayOnFailover,
      enableReadyCheck: this.config.enableReadyCheck,
      maxRetriesPerRequest: this.config.maxRetriesPerRequest
    });

    this.setupEventListeners();
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Setup Redis event listeners
   */
  private setupEventListeners(): void {
    this.redis.on('connect', () => {
      logger.info('Redis connected successfully');
      this.isConnected = true;
    });

    this.redis.on('ready', () => {
      logger.info('Redis ready for operations');
    });

    this.redis.on('error', (error) => {
      logger.error('Redis connection error', { error: error.message });
      this.isConnected = false;
    });

    this.redis.on('close', () => {
      logger.warn('Redis connection closed');
      this.isConnected = false;
    });

    this.redis.on('reconnecting', () => {
      logger.info('Redis reconnecting...');
    });
  }

  /**
   * Get Redis client
   */
  public getClient(): Redis {
    return this.redis;
  }

  /**
   * Check if Redis is connected
   */
  public isRedisConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Generate cache key
   */
  private generateKey(type: string, identifier: string): string {
    return `${this.config.keyPrefix}${type}:${identifier}`;
  }

  /**
   * Set cache with TTL
   */
  public async set(
    type: string,
    identifier: string,
    data: any,
    ttl: number = CACHE_TTL.MEDIUM
  ): Promise<boolean> {
    if (!this.isConnected) {
      logger.warn('Redis not connected, skipping cache set');
      return false;
    }

    try {
      const key = this.generateKey(type, identifier);
      const serializedData = JSON.stringify(data);
      
      await this.redis.setex(key, ttl, serializedData);
      
      logger.debug('Cache set successfully', {
        key,
        ttl,
        dataSize: serializedData.length
      });
      
      return true;
    } catch (error) {
      logger.error('Cache set failed', {
        type,
        identifier,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Get from cache
   */
  public async get<T>(type: string, identifier: string): Promise<T | null> {
    if (!this.isConnected) {
      logger.warn('Redis not connected, skipping cache get');
      return null;
    }

    try {
      const key = this.generateKey(type, identifier);
      const cachedData = await this.redis.get(key);
      
      if (!cachedData) {
        logger.debug('Cache miss', { key });
        return null;
      }
      
      const parsedData = JSON.parse(cachedData);
      logger.debug('Cache hit', { key });
      
      return parsedData as T;
    } catch (error) {
      logger.error('Cache get failed', {
        type,
        identifier,
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Delete from cache
   */
  public async delete(type: string, identifier: string): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const key = this.generateKey(type, identifier);
      const result = await this.redis.del(key);
      
      logger.debug('Cache delete', { key, deleted: result > 0 });
      return result > 0;
    } catch (error) {
      logger.error('Cache delete failed', {
        type,
        identifier,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Delete multiple keys by pattern
   */
  public async deletePattern(pattern: string): Promise<number> {
    if (!this.isConnected) {
      return 0;
    }

    try {
      const fullPattern = `${this.config.keyPrefix}${pattern}`;
      const keys = await this.redis.keys(fullPattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      const result = await this.redis.del(...keys);
      logger.info('Cache pattern delete', { pattern: fullPattern, deletedCount: result });
      
      return result;
    } catch (error) {
      logger.error('Cache pattern delete failed', {
        pattern,
        error: (error as Error).message
      });
      return 0;
    }
  }

  /**
   * Check if key exists
   */
  public async exists(type: string, identifier: string): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const key = this.generateKey(type, identifier);
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Cache exists check failed', {
        type,
        identifier,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Get TTL for a key
   */
  public async getTTL(type: string, identifier: string): Promise<number> {
    if (!this.isConnected) {
      return -1;
    }

    try {
      const key = this.generateKey(type, identifier);
      return await this.redis.ttl(key);
    } catch (error) {
      logger.error('Cache TTL check failed', {
        type,
        identifier,
        error: (error as Error).message
      });
      return -1;
    }
  }

  /**
   * Increment counter
   */
  public async increment(type: string, identifier: string, by: number = 1): Promise<number> {
    if (!this.isConnected) {
      return 0;
    }

    try {
      const key = this.generateKey(type, identifier);
      return await this.redis.incrby(key, by);
    } catch (error) {
      logger.error('Cache increment failed', {
        type,
        identifier,
        error: (error as Error).message
      });
      return 0;
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<{ status: string; latency: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      await this.redis.ping();
      const latency = Date.now() - startTime;
      
      return {
        status: 'healthy',
        latency
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: 'unhealthy',
        latency,
        error: (error as Error).message
      };
    }
  }

  /**
   * Get cache statistics
   */
  public async getStats(): Promise<any> {
    if (!this.isConnected) {
      return null;
    }

    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      
      return {
        memory: info,
        keyspace,
        connected: this.isConnected
      };
    } catch (error) {
      logger.error('Failed to get cache stats', {
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Disconnect from Redis
   */
  public async disconnect(): Promise<void> {
    try {
      await this.redis.quit();
      logger.info('Redis connection closed gracefully');
    } catch (error) {
      logger.error('Error closing Redis connection', {
        error: (error as Error).message
      });
    }
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();

/**
 * Islamic Content Cache Utilities
 */
export class IslamicContentCache {
  /**
   * Cache Quranic verse
   */
  static async cacheQuranicVerse(surah: number, ayah: number, data: any): Promise<boolean> {
    const identifier = `${surah}:${ayah}`;
    return await cacheManager.set(CACHE_KEYS.QURANIC_VERSE, identifier, data, CACHE_TTL.QURANIC_VERSES);
  }

  /**
   * Get cached Quranic verse
   */
  static async getQuranicVerse(surah: number, ayah: number): Promise<any | null> {
    const identifier = `${surah}:${ayah}`;
    return await cacheManager.get(CACHE_KEYS.QURANIC_VERSE, identifier);
  }

  /**
   * Cache Names of Allah
   */
  static async cacheNamesOfAllah(data: any[]): Promise<boolean> {
    return await cacheManager.set(CACHE_KEYS.NAMES_OF_ALLAH, 'all', data, CACHE_TTL.NAMES_OF_ALLAH);
  }

  /**
   * Get cached Names of Allah
   */
  static async getNamesOfAllah(): Promise<any[] | null> {
    return await cacheManager.get(CACHE_KEYS.NAMES_OF_ALLAH, 'all');
  }

  /**
   * Cache daily Islamic content
   */
  static async cacheDailyContent(date: string, content: any): Promise<boolean> {
    return await cacheManager.set(CACHE_KEYS.DAILY_CONTENT, date, content, CACHE_TTL.DAILY_CONTENT);
  }

  /**
   * Get daily Islamic content
   */
  static async getDailyContent(date: string): Promise<any | null> {
    return await cacheManager.get(CACHE_KEYS.DAILY_CONTENT, date);
  }

  /**
   * Cache emergency resources
   */
  static async cacheEmergencyResources(data: any): Promise<boolean> {
    return await cacheManager.set(CACHE_KEYS.EMERGENCY_RESOURCES, 'all', data, CACHE_TTL.EMERGENCY_CONTENT);
  }

  /**
   * Get emergency resources
   */
  static async getEmergencyResources(): Promise<any | null> {
    return await cacheManager.get(CACHE_KEYS.EMERGENCY_RESOURCES, 'all');
  }

  /**
   * Invalidate user-specific cache
   */
  static async invalidateUserCache(userId: string): Promise<void> {
    await Promise.all([
      cacheManager.delete(CACHE_KEYS.USER_PROFILE, userId),
      cacheManager.delete(CACHE_KEYS.USER_JOURNEY, userId),
      cacheManager.delete(CACHE_KEYS.USER_PROGRESS, userId),
      cacheManager.deletePattern(`${CACHE_KEYS.JOURNEY_ANALYTICS}:${userId}:*`)
    ]);
  }
}

/**
 * Cache middleware for Express routes
 */
export const cacheMiddleware = (
  type: string,
  ttl: number = CACHE_TTL.MEDIUM,
  keyGenerator?: (req: any) => string
) => {
  return async (req: any, res: any, next: any) => {
    try {
      // Generate cache key
      const identifier = keyGenerator 
        ? keyGenerator(req)
        : `${req.method}:${req.originalUrl}:${JSON.stringify(req.query)}`;

      // Try to get from cache
      const cachedData = await cacheManager.get(type, identifier);
      
      if (cachedData) {
        logger.debug('Cache hit for middleware', { type, identifier });
        return res.json(cachedData);
      }

      // Store original json method
      const originalJson = res.json;

      // Override json method to cache response
      res.json = function(data: any) {
        // Cache the response
        cacheManager.set(type, identifier, data, ttl).catch(error => {
          logger.error('Failed to cache response', { error: error.message });
        });

        // Call original json method
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error', { error: (error as Error).message });
      next();
    }
  };
};

/**
 * Cache warming utilities
 */
export class CacheWarmer {
  /**
   * Warm up Islamic content cache
   */
  static async warmIslamicContent(): Promise<void> {
    try {
      logger.info('Starting Islamic content cache warming...');

      // This would typically fetch from database and cache
      // Implementation depends on your data structure
      
      logger.info('Islamic content cache warming completed');
    } catch (error) {
      logger.error('Islamic content cache warming failed', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Warm up emergency resources cache
   */
  static async warmEmergencyResources(): Promise<void> {
    try {
      logger.info('Starting emergency resources cache warming...');
      
      // Implementation would fetch emergency resources and cache them
      
      logger.info('Emergency resources cache warming completed');
    } catch (error) {
      logger.error('Emergency resources cache warming failed', {
        error: (error as Error).message
      });
    }
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, closing Redis connection...');
  await cacheManager.disconnect();
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, closing Redis connection...');
  await cacheManager.disconnect();
});