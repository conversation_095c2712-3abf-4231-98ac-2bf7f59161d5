/**
 * Enhanced Qalb Healing Backend API Server
 * Islamic Mental Wellness Platform Backend with Comprehensive Architecture
 */

import express from 'express';
import cors from 'cors';
import compression from 'compression';
import { setupSupabase } from './config/supabase';
import { specs, swaggerUi, swaggerOptions } from './config/swagger';
import { enhancedErrorHandler } from './middleware/enhancedErrorHandler';
import { securityMiddleware } from './middleware/security';
import { islamicLogger, requestLogger } from './utils/enhancedLogger';
import { validateApiVersion, contentNegotiation, ApiVersion } from './utils/apiVersioning';
import { sanitizeInput } from './middleware/validation';
import { performanceMiddleware, healthRoutes } from './utils/health';
import { sendSuccess, sendError } from './utils/response';
import { applyDatabaseIndexes } from './utils/database';
import { CacheWarmer } from './utils/cache';
import { quickVerification } from './utils/startupVerification';

// Import route modules
import authRoutes from './routes/auth.routes';
import onboardingRoutes from './routes/onboarding.routes';
import assessmentRoutes from './routes/assessment.routes';
import symptomsRoutes from './routes/symptoms.routes';
import contentRoutes from './routes/content.routes';
import journeyRoutes from './routes/journey.routes';
import emergencyRoutes from './routes/emergency.routes';
import analyticsRoutes from './routes/analytics.routes';
import communityRoutes from './routes/community.routes';
import journalRoutes from './routes/journal.routes';
import ruqyaRoutes from './routes/ruqya.routes';
import dashboardRoutes from './routes/dashboard.routes';
import sadaqahRoutes from './routes/sadaqah.routes';

// New content routes for Feature 2
import nameOfAllahContentRoutes from './routes/name-of-allah-content.routes';
import quranicVerseContentRoutes from './routes/quranic-verse-content.routes';
import sunnahPracticeContentRoutes from './routes/sunnah-practice-content.routes';

const app = express();

/**
 * Initialize application
 */
async function initializeApp(): Promise<void> {
  try {
    islamicLogger.info('Initializing Qalb Healing API server...', {
      version: process.env.API_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    });

    // Initialize Supabase
    try {
      setupSupabase();
      islamicLogger.success('Supabase initialized successfully');
    } catch (error) {
      islamicLogger.warn('Supabase initialization failed (using test credentials)', {
        error: (error as Error).message
      });
    }

    // Apply database optimizations
    if (process.env.NODE_ENV === 'production') {
      await applyDatabaseIndexes();
    }

    // Warm up caches
    await CacheWarmer.warmIslamicContent();
    await CacheWarmer.warmEmergencyResources();

    // Run startup verification
    const verificationPassed = await quickVerification();
    if (!verificationPassed) {
      islamicLogger.error('Startup verification failed - some features may not work properly');
    }

    islamicLogger.success('Application initialization completed');
  } catch (error) {
    islamicLogger.error('Application initialization failed', {
      error: (error as Error).message,
      stack: (error as Error).stack
    });
    throw error;
  }
}\n\n/**\n * Configure middleware\n */\nfunction configureMiddleware(): void {\n  // Trust proxy for accurate IP addresses\n  app.set('trust proxy', 1);\n\n  // Basic middleware\n  app.use(compression());\n  \n  // Enhanced CORS configuration\n  app.use(\n    cors({\n      origin: (origin, callback) => {\n        // Allow requests with no origin (like mobile apps or curl)\n        if (!origin) return callback(null, true);\n        \n        // Allowed origins from environment\n        const allowedOrigins = (process.env.CORS_ORIGIN || '').split(',').map(o => o.trim());\n        \n        // Default allowed origins for development\n        const defaultAllowed = [\n          'http://localhost:3000',\n          'http://localhost:8081',\n          'http://localhost:19006',\n          'http://localhost:19000',\n        ];\n        \n        const allowed = [...allowedOrigins, ...defaultAllowed];\n        \n        if (allowed.includes(origin)) {\n          return callback(null, true);\n        }\n        \n        // Allow any localhost for dev\n        if (process.env.NODE_ENV === 'development' && /^http:\\/\\/localhost:\\d+$/.test(origin)) {\n          return callback(null, true);\n        }\n        \n        // Allow any 192.168.x.x for LAN dev\n        if (process.env.NODE_ENV === 'development' && /^http:\\/\\/192\\.168\\.\\d+\\.\\d+:\\d+$/.test(origin)) {\n          return callback(null, true);\n        }\n        \n        islamicLogger.security('CORS origin rejected', { origin, userAgent: 'unknown' });\n        return callback(new Error('Not allowed by CORS'), false);\n      },\n      credentials: true,\n      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],\n      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Version', 'X-Request-ID'],\n      exposedHeaders: ['X-API-Version', 'X-API-Deprecated', 'X-Rate-Limit-Remaining']\n    })\n  );\n\n  // Body parsing with size limits\n  app.use(express.json({ \n    limit: '10mb',\n    verify: (req, res, buf) => {\n      // Store raw body for webhook verification if needed\n      (req as any).rawBody = buf;\n    }\n  }));\n  app.use(express.urlencoded({ extended: true, limit: '10mb' }));\n\n  // Security middleware\n  app.use(securityMiddleware);\n  \n  // Input sanitization\n  app.use(sanitizeInput);\n  \n  // Request logging\n  app.use(requestLogger);\n  \n  // Performance monitoring\n  app.use(performanceMiddleware);\n  \n  // API versioning and content negotiation\n  app.use('/api', validateApiVersion);\n  app.use('/api', contentNegotiation);\n\n  islamicLogger.info('Middleware configuration completed');\n}\n\n/**\n * Configure API documentation\n */\nfunction configureDocumentation(): void {\n  // API Documentation\n  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerOptions));\n  \n  app.get('/api-docs/swagger.json', (req, res) => {\n    res.setHeader('Content-Type', 'application/json');\n    res.send(specs);\n  });\n\n  // Debug endpoint to check what swagger is finding\n  app.get('/debug/swagger', (req, res) => {\n    const specPaths = (specs as any).paths || {};\n    sendSuccess(res, {\n      paths: Object.keys(specPaths),\n      totalPaths: Object.keys(specPaths).length,\n      specs: specs,\n    }, 'Swagger debug information');\n  });\n\n  islamicLogger.info('API documentation configured');\n}\n\n/**\n * Configure health and monitoring endpoints\n */\nfunction configureHealthEndpoints(): void {\n  // Basic health check endpoint\n  app.get('/health', healthRoutes.basic);\n  \n  // Detailed health check\n  app.get('/health/detailed', healthRoutes.detailed);\n  \n  // Kubernetes probes\n  app.get('/health/ready', healthRoutes.ready);\n  app.get('/health/live', healthRoutes.live);\n  \n  // Metrics endpoint (Prometheus format)\n  app.get('/metrics', healthRoutes.metrics);\n  \n  // System information endpoint\n  app.get('/info', (req, res) => {\n    sendSuccess(res, {\n      name: 'Qalb Healing API',\n      version: process.env.API_VERSION || '1.0.0',\n      environment: process.env.NODE_ENV || 'development',\n      uptime: process.uptime(),\n      timestamp: new Date().toISOString(),\n      supportedApiVersions: [ApiVersion.V1],\n      features: {\n        islamicContent: true,\n        emergencySupport: true,\n        journeyTracking: true,\n        communityFeatures: true,\n        aiAnalysis: true,\n        realTimeNotifications: false // Future feature\n      },\n      islamicGreeting: {\n        arabic: 'السلام عليكم ورحمة الله وبركاته',\n        transliteration: 'Assalamu alaikum wa rahmatullahi wa barakatuh',\n        translation: 'Peace be upon you and the mercy of Allah and His blessings'\n      }\n    }, 'Qalb Healing API Information', 200, 'success');\n  });\n\n  islamicLogger.info('Health and monitoring endpoints configured');\n}\n\n/**\n * Configure API routes with versioning\n */\nfunction configureRoutes(): void {\n  // API v1 routes\n  const v1Router = express.Router();\n  \n  // Core routes\n  v1Router.use('/auth', authRoutes);\n  v1Router.use('/onboarding', onboardingRoutes);\n  v1Router.use('/assessment', assessmentRoutes);\n  v1Router.use('/symptoms', symptomsRoutes);\n  v1Router.use('/content', contentRoutes);\n  v1Router.use('/journey', journeyRoutes);\n  v1Router.use('/emergency', emergencyRoutes);\n  v1Router.use('/analytics', analyticsRoutes);\n  v1Router.use('/community', communityRoutes);\n  v1Router.use('/journal', journalRoutes);\n  v1Router.use('/ruqya', ruqyaRoutes);\n  v1Router.use('/dashboard', dashboardRoutes);\n  v1Router.use('/sadaqah', sadaqahRoutes);\n\n  // Islamic content routes\n  v1Router.use('/content-items/names-of-allah', nameOfAllahContentRoutes);\n  v1Router.use('/content-items/quranic-verses', quranicVerseContentRoutes);\n  v1Router.use('/content-items/sunnah-practices', sunnahPracticeContentRoutes);\n\n  // Mount versioned routes\n  app.use('/api/v1', v1Router);\n  \n  // Default to v1 for backward compatibility\n  app.use('/api', v1Router);\n\n  islamicLogger.info('API routes configured with versioning');\n}\n\n/**\n * Configure error handling\n */\nfunction configureErrorHandling(): void {\n  // 404 handler for API routes\n  app.use('/api/*', (req, res) => {\n    islamicLogger.warn('API route not found', {\n      url: req.url,\n      method: req.method,\n      ip: req.ip,\n      userAgent: req.get('User-Agent')\n    });\n    \n    sendError(\n      res,\n      'RESOURCE_NOT_FOUND',\n      'API endpoint not found',\n      404,\n      {\n        requestedUrl: req.url,\n        method: req.method,\n        availableVersions: [ApiVersion.V1]\n      },\n      'The requested API endpoint could not be found. Please check the URL and API version.'\n    );\n  });\n\n  // Global 404 handler\n  app.use('*', (req, res) => {\n    sendError(\n      res,\n      'RESOURCE_NOT_FOUND',\n      'Route not found',\n      404,\n      { requestedUrl: req.url },\n      'The requested resource could not be found. May Allah guide you to the right path.'\n    );\n  });\n\n  // Global error handler\n  app.use(enhancedErrorHandler);\n\n  islamicLogger.info('Error handling configured');\n}\n\n/**\n * Start the server\n */\nfunction startServer(): void {\n  const port = process.env.PORT || 3333;\n  \n  const server = app.listen(port, () => {\n    islamicLogger.success('Qalb Healing API server started successfully', {\n      port,\n      environment: process.env.NODE_ENV || 'development',\n      version: process.env.API_VERSION || '1.0.0',\n      documentation: `http://localhost:${port}/api-docs`,\n      health: `http://localhost:${port}/health`,\n      metrics: `http://localhost:${port}/metrics`\n    });\n    \n    islamicLogger.info('Server endpoints available', {\n      api: `http://localhost:${port}/api/v1`,\n      docs: `http://localhost:${port}/api-docs`,\n      health: `http://localhost:${port}/health`,\n      info: `http://localhost:${port}/info`\n    });\n  });\n\n  server.on('error', (error: any) => {\n    islamicLogger.error('Server error occurred', {\n      error: error.message,\n      code: error.code,\n      port\n    });\n    \n    if (error.code === 'EADDRINUSE') {\n      islamicLogger.error(`Port ${port} is already in use. Please use a different port.`);\n      process.exit(1);\n    }\n  });\n\n  // Graceful shutdown handlers\n  const gracefulShutdown = (signal: string) => {\n    islamicLogger.info(`Received ${signal}, initiating graceful shutdown...`);\n    \n    server.close((err) => {\n      if (err) {\n        islamicLogger.error('Error during server shutdown', { error: err.message });\n        process.exit(1);\n      }\n      \n      islamicLogger.info('Server closed successfully. May Allah bless this service.');\n      process.exit(0);\n    });\n    \n    // Force shutdown after 30 seconds\n    setTimeout(() => {\n      islamicLogger.error('Forced shutdown due to timeout');\n      process.exit(1);\n    }, 30000);\n  };\n\n  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));\n  process.on('SIGINT', () => gracefulShutdown('SIGINT'));\n  \n  // Handle uncaught exceptions\n  process.on('uncaughtException', (error) => {\n    islamicLogger.error('Uncaught exception', {\n      error: error.message,\n      stack: error.stack\n    });\n    gracefulShutdown('UNCAUGHT_EXCEPTION');\n  });\n  \n  // Handle unhandled promise rejections\n  process.on('unhandledRejection', (reason, promise) => {\n    islamicLogger.error('Unhandled promise rejection', {\n      reason: reason instanceof Error ? reason.message : String(reason),\n      stack: reason instanceof Error ? reason.stack : undefined\n    });\n    gracefulShutdown('UNHANDLED_REJECTION');\n  });\n}\n\n/**\n * Main application bootstrap\n */\nasync function bootstrap(): Promise<void> {\n  try {\n    islamicLogger.info('Starting Qalb Healing API server bootstrap...', {\n      nodeVersion: process.version,\n      platform: process.platform,\n      arch: process.arch,\n      pid: process.pid\n    });\n\n    // Initialize application\n    await initializeApp();\n    \n    // Configure middleware\n    configureMiddleware();\n    \n    // Configure documentation\n    configureDocumentation();\n    \n    // Configure health endpoints\n    configureHealthEndpoints();\n    \n    // Configure routes\n    configureRoutes();\n    \n    // Configure error handling\n    configureErrorHandling();\n    \n    // Start server\n    startServer();\n    \n  } catch (error) {\n    islamicLogger.error('Failed to start server', {\n      error: (error as Error).message,\n      stack: (error as Error).stack\n    });\n    process.exit(1);\n  }\n}\n\n// Start the application\nbootstrap();\n\nexport default app;"