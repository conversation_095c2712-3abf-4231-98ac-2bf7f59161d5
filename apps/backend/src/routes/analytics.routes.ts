import express from 'express';
import { query } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as analyticsController from '../controllers/analytics.controller';
import { analyticsCache } from '../middleware/cache';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Analytics
 *   description: User progress analytics and insights for spiritual healing journey
 */

// Apply auth middleware to all analytics routes
router.use(authMiddleware);

/**
 * @swagger
 * /analytics/progress:
 *   get:
 *     summary: Get user progress analytics across all healing layers
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [week, month, year, all]
 *           default: month
 *         description: Time period for analytics
 *       - in: query
 *         name: layers
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             enum: [jism, nafs, aql, qalb, ruh]
 *         description: Specific layers to analyze
 *     responses:
 *       200:
 *         description: Progress analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         overallProgress:
 *                           type: number
 *                           example: 75.5
 *                         layerProgress:
 *                           type: object
 *                           example:
 *                             jism: 80
 *                             nafs: 70
 *                             aql: 85
 *                             qalb: 90
 *                             ruh: 65
 *                         trends:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                               progress:
 *                                 type: number
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Get user progress analytics
router.get(
  '/progress',
  analyticsCache, // Cache analytics for 1 hour
  [
    query('timeframe')
      .optional()
      .isIn(['week', 'month', 'year', 'all'])
      .withMessage('Invalid timeframe'),
    query('layers').optional().isArray().withMessage('Layers must be an array'),
  ],
  analyticsController.getProgressAnalytics
);

/**
 * @swagger
 * /analytics/journey:
 *   get:
 *     summary: Get detailed journey analytics and progress
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: journeyId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Specific journey ID to analyze
 *       - in: query
 *         name: includeCompleted
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include completed journeys in analysis
 *     responses:
 *       200:
 *         description: Journey analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         currentJourney:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               format: uuid
 *                             progress:
 *                               type: number
 *                               example: 65.5
 *                             daysCompleted:
 *                               type: integer
 *                               example: 26
 *                             totalDays:
 *                               type: integer
 *                               example: 40
 *                         completionRate:
 *                           type: number
 *                           example: 85.2
 *                         averageSessionTime:
 *                           type: number
 *                           example: 25.5
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Get journey analytics
router.get(
  '/journey',
  analyticsCache,
  [
    query('journeyId').optional().isUUID().withMessage('Invalid journey ID'),
    query('includeCompleted')
      .optional()
      .isBoolean()
      .withMessage('includeCompleted must be boolean'),
  ],
  analyticsController.getJourneyAnalytics
);

/**
 * @swagger
 * /analytics/healing:
 *   get:
 *     summary: Get healing effectiveness metrics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [week, month, year, all]
 *           default: month
 *       - in: query
 *         name: metrics
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Specific metrics to include
 *     responses:
 *       200:
 *         description: Healing metrics retrieved successfully
 */
// Get healing metrics
router.get(
  '/healing',
  analyticsCache,
  [
    query('timeframe')
      .optional()
      .isIn(['week', 'month', 'year', 'all'])
      .withMessage('Invalid timeframe'),
    query('metrics')
      .optional()
      .isArray()
      .withMessage('Metrics must be an array'),
  ],
  analyticsController.getHealingMetrics
);

/**
 * @swagger
 * /analytics/achievements:
 *   get:
 *     summary: Get achievement analytics and progress
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Achievement category filter
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [earned, in-progress, all]
 *           default: all
 *     responses:
 *       200:
 *         description: Achievement analytics retrieved successfully
 */
// Get achievement analytics
router.get(
  '/achievements',
  analyticsCache,
  [
    query('category').optional().isString().withMessage('Invalid category'),
    query('status')
      .optional()
      .isIn(['earned', 'in-progress', 'all'])
      .withMessage('Invalid status'),
  ],
  analyticsController.getAchievementAnalytics
);

/**
 * @swagger
 * /analytics/dashboard:
 *   get:
 *     summary: Get dashboard analytics overview
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: modules
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Specific modules to include
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [day, week, month]
 *           default: week
 *     responses:
 *       200:
 *         description: Dashboard analytics retrieved successfully
 */
// Get dashboard analytics
router.get(
  '/dashboard',
  analyticsCache,
  [
    query('modules')
      .optional()
      .isArray()
      .withMessage('Modules must be an array'),
    query('timeframe')
      .optional()
      .isIn(['day', 'week', 'month'])
      .withMessage('Invalid timeframe'),
  ],
  analyticsController.getDashboardAnalytics
);

/**
 * @swagger
 * /analytics/layer/{layerType}:
 *   get:
 *     summary: Get layer-specific analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: layerType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [jism, nafs, aql, qalb, ruh]
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [week, month, year, all]
 *           default: month
 *       - in: query
 *         name: metrics
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *     responses:
 *       200:
 *         description: Layer analytics retrieved successfully
 */
// Get layer-specific analytics
router.get(
  '/layer/:layerType',
  analyticsCache,
  [
    query('timeframe')
      .optional()
      .isIn(['week', 'month', 'year', 'all'])
      .withMessage('Invalid timeframe'),
    query('metrics')
      .optional()
      .isArray()
      .withMessage('Metrics must be an array'),
  ],
  analyticsController.getLayerAnalytics
);

/**
 * @swagger
 * /analytics/streaks:
 *   get:
 *     summary: Get streak analytics and consistency metrics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Streak analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         currentStreak:
 *                           type: integer
 *                           example: 15
 *                         longestStreak:
 *                           type: integer
 *                           example: 42
 *                         streakHistory:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                               active:
 *                                 type: boolean
 */
// Get streak analytics
router.get('/streaks', analyticsCache, analyticsController.getStreakAnalytics);

/**
 * @swagger
 * /analytics/effectiveness:
 *   get:
 *     summary: Get practice effectiveness analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: practiceType
 *         schema:
 *           type: string
 *         description: Specific practice type to analyze
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [week, month, year, all]
 *           default: month
 *     responses:
 *       200:
 *         description: Practice effectiveness retrieved successfully
 */
// Get practice effectiveness
router.get(
  '/effectiveness',
  analyticsCache,
  [
    query('practiceType')
      .optional()
      .isString()
      .withMessage('Invalid practice type'),
    query('timeframe')
      .optional()
      .isIn(['week', 'month', 'year', 'all'])
      .withMessage('Invalid timeframe'),
  ],
  analyticsController.getPracticeEffectiveness
);

/**
 * @swagger
 * /analytics/export:
 *   post:
 *     summary: Export analytics data in various formats
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         required: true
 *         schema:
 *           type: string
 *           enum: [csv, pdf, json]
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [week, month, year, all]
 *           default: month
 *       - in: query
 *         name: metrics
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *     responses:
 *       200:
 *         description: Analytics data exported successfully
 */
// Export analytics data
router.post(
  '/export',
  [
    query('format')
      .isIn(['csv', 'pdf', 'json'])
      .withMessage('Invalid export format'),
    query('timeframe')
      .optional()
      .isIn(['week', 'month', 'year', 'all'])
      .withMessage('Invalid timeframe'),
    query('metrics')
      .optional()
      .isArray()
      .withMessage('Metrics must be an array'),
  ],
  analyticsController.exportAnalytics
);

export default router;
