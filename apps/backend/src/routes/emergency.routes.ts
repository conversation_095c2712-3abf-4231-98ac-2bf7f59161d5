import express from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as emergencyController from '../controllers/emergency.controller';
import { contentLimiter } from '../middleware/security';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Emergency
 *   description: Emergency support system (Sakina Mode) for immediate spiritual assistance
 */

/**
 * @swagger
 * /emergency/resources:
 *   get:
 *     summary: Get emergency resources (public endpoint)
 *     tags: [Emergency]
 *     parameters:
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Location for localized resources
 *     responses:
 *       200:
 *         description: Emergency resources retrieved successfully
 */
router.get('/resources', (req, res) => {
  const { location } = req.query;

  res.status(200).json({
    status: 'success',
    data: {
      resources: {
        hotlines: [
          {
            name: 'National Suicide Prevention Lifeline',
            phone: '988',
            description: '24/7 crisis support',
            availability: '24/7',
            region: location || 'US',
          },
        ],
        islamicCounselors: [
          {
            name: 'Islamic Crisis Support',
            contact: '******-ISLAMIC',
            specialization: 'Islamic Mental Health',
            location: location || 'US',
          },
        ],
        emergencyPrayers: [
          {
            title: 'Dua for Distress',
            arabic: 'لا إله إلا الله العظيم الحليم',
            transliteration: 'La ilaha illa Allah al-Azeem al-Haleem',
            translation: 'There is no god but Allah, the Great, the Gentle',
          },
        ],
        selfCareSteps: [
          'Take deep breaths',
          'Recite dhikr',
          'Seek professional help if needed',
        ],
      },
    },
  });
});

// Apply auth middleware to all other emergency routes (except /resources)
router.use(authMiddleware);

/**
 * @swagger
 * /emergency/start:
 *   post:
 *     summary: Start a Qalb Rescue session
 *     description: Initiates a Qalb Rescue session and returns the first step's content.
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               triggerType:
 *                 type: string
 *                 enum: [manual, automatic, scheduled]
 *                 example: manual
 *                 description: How the emergency session was triggered
 *               currentSymptoms:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["anxiety", "panic", "spiritual_distress"]
 *                 description: Current symptoms user is experiencing
 *     responses:
 *       201:
 *         description: Emergency session started successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         sessionId:
 *                           type: string
 *                           format: uuid
 *                           description: The unique identifier for the Qalb Rescue session.
 *                         startTime:
 *                           type: string
 *                           format: date-time
 *                           description: The timestamp when the session started.
 *                         currentStep:
 *                           type: string
 *                           description: The name of the initial step in the Qalb Rescue flow (e.g., 'grounding').
 *                           example: 'grounding'
 *                         stepContent:
 *                           type: object
 *                           description: Content for the initial step.
 *                           properties:
 *                             title:
 *                               type: string
 *                               example: "Step 1: Immediate Islamic Grounding"
 *                             description:
 *                               type: string
 *                               example: "Bismillah... breathe with me..."
 *                             audioUrl:
 *                               type: string
 *                               format: uri
 *                               nullable: true
 *                             items:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: string
 *                                   type:
 *                                     type: string
 *                                     enum: [verse, dhikr, prompt]
 *                                   text:
 *                                     type: string
 *                                   translation:
 *                                     type: string
 *                                     nullable: true
 *                                   audioUrl:
 *                                     type: string
 *                                     format: uri
 *                                     nullable: true
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       429:
 *         description: Too many emergency sessions started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  '/start',
  contentLimiter, // Using existing rate limiter
  [
    body('triggerType')
      .optional()
      .isString()
      .isIn(['manual', 'automatic', 'scheduled'])
      .withMessage('Invalid trigger type'),
    body('currentSymptoms')
      .optional()
      .isArray()
      .withMessage('Current symptoms must be an array'),
  ],
  emergencyController.startQalbRescueSessionController
);

// New route for progressing the Qalb Rescue session
/**
 * @swagger
 * /emergency/sessions/{id}/next-step:
 *   post:
 *     summary: Progress to the next step in a Qalb Rescue session
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the Qalb Rescue session.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - completedStep
 *             properties:
 *               completedStep:
 *                 type: string
 *                 description: The name of the step that was just completed.
 *                 example: "grounding"
 *               timeSpentOnStep:
 *                 type: integer
 *                 description: Time spent on the completed step in milliseconds (optional).
 *                 example: 30000
 *     responses:
 *       200:
 *         description: Successfully progressed to the next step or completed session.
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         sessionId:
 *                           type: string
 *                           format: uuid
 *                         currentStep:
 *                           type: string
 *                           nullable: true
 *                           description: The name of the next step, or null if session completed.
 *                           example: 'breathing'
 *                         stepContent:
 *                           type: object
 *                           nullable: true
 *                           description: Content for the next step, or null if session completed.
 *                           properties:
 *                             title:
 *                               type: string
 *                             description:
 *                               type: string
 *                             audioUrl:
 *                               type: string
 *                               format: uri
 *                               nullable: true
 *                             items:
 *                               type: array
 *                               items:
 *                                 type: object
 *                         message:
 *                           type: string
 *                           nullable: true
 *                           description: Optional message, e.g., 'Qalb Rescue session completed.'
 *       400:
 *         description: Invalid request data (e.g., missing completedStep)
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.post(
  '/sessions/:id/next-step',
  [
    param('id').isUUID().withMessage('Invalid session ID format.'),
    body('completedStep').isString().notEmpty().withMessage('completedStep is required.'),
    body('timeSpentOnStep').optional().isInt({ min: 0 }).withMessage('timeSpentOnStep must be a positive integer.'),
  ],
  emergencyController.progressQalbRescueSessionController
);


// Note: Routes for /emergency/breathing, /emergency/dhikr, /emergency/ruqyah are removed
// as content is now delivered through the main Qalb Rescue session flow (/start and /sessions/:id/next-step).
// If these are needed for other purposes, they can be reinstated, but they are not part of this specific flow.


/**
 * @swagger
 * /emergency/sessions/{id}:
 *   patch:
 *     summary: Update Qalb Rescue session (e.g., feedback, end session)
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [completed, aborted] # 'active' is managed by progression
 *                 description: The final status to set for the session.
 *                 example: completed
 *               feedback:
 *                 type: string
 *                 nullable: true
 *                 example: "The breathing exercise helped me feel calmer"
 *               effectivenessRating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 nullable: true
 *                 example: 4
 *               reasonForEnding:
 *                 type: string
 *                 nullable: true
 *                 description: Reason if the session was aborted by the user.
 *                 example: "User felt better and decided to stop early."
 *     responses:
 *       200:
 *         description: Qalb Rescue session updated successfully (feedback recorded or session ended).
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         sessionId:
 *                           type: string
 *                           format: uuid
 *                         message:
 *                           type: string
 *                           example: "Session updated successfully."
 */
router.patch(
  '/sessions/:id',
  [
    param('id').isUUID().withMessage('Invalid session ID'),
    body('status')
      .optional()
      .isString()
      .isIn(['active', 'completed', 'interrupted'])
      .withMessage('Invalid status'),
    body('feedback')
      .optional()
      .isString()
      .withMessage('Feedback must be a string'),
    body('effectivenessRating')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('Effectiveness rating must be between 1 and 5'),
    body('reasonForEnding') // Added for explicit abortion reasons
      .optional()
      .isString()
      .withMessage('Reason for ending must be a string'),
  ],
  emergencyController.updateQalbRescueSessionController
);

// Routes for Community Connection Features (Phase 2)
/**
 * @swagger
 * /emergency/sessions/{id}/request-dua:
 *   post:
 *     summary: Request Du'a from the community for a Qalb Rescue session
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the Qalb Rescue session.
 *     responses:
 *       200:
 *         description: Du'a request initiated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: "Du'a request initiated."
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.post(
  '/sessions/:id/request-dua',
  [param('id').isUUID().withMessage('Invalid session ID format.')],
  emergencyController.requestDuaController
);

/**
 * @swagger
 * /emergency/sessions/{id}/connect-peer:
 *   post:
 *     summary: Attempt to connect with a peer supporter for a Qalb Rescue session
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the Qalb Rescue session.
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               criteria:
 *                 type: object
 *                 description: Optional criteria for matching with a peer supporter.
 *     responses:
 *       200:
 *         description: Peer supporter connection status.
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           enum: [supporter_found, unavailable, pending_acceptance, error]
 *                           example: pending_acceptance
 *                         message:
 *                           type: string
 *                           example: "Peer supporter found and notified. Waiting for acceptance."
 *                         peerSupportRequestId:
 *                           type: string
 *                           format: uuid
 *                           nullable: true
 *                         supporterDetails:
 *                           type: object
 *                           nullable: true
 *                           properties:
 *                             id:
 *                               type: string
 *                               format: uuid
 *                             name:
 *                               type: string
 *                               nullable: true
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.post(
  '/sessions/:id/connect-peer',
  [
    param('id').isUUID().withMessage('Invalid session ID format.'),
    body('criteria').optional().isObject().withMessage('Criteria must be an object if provided.'),
  ],
  emergencyController.connectPeerSupporterController
);


/**
 * @swagger
 * /emergency/sessions/{id}/save:
 *   post:
 *     summary: Save emergency session to journal for future reference
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *                 example: "This session helped me during a panic attack"
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["panic_attack", "breathing", "effective"]
 *     responses:
 *       201:
 *         description: Emergency session saved to journal successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         journalEntryId:
 *                           type: string
 *                           format: uuid
 *                         savedAt:
 *                           type: string
 *                           format: date-time
 */
router.post(
  '/sessions/:id/save',
  [
    param('id').isUUID().withMessage('Invalid session ID'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
    body('tags').optional().isArray().withMessage('Tags must be an array'),
  ],
  emergencyController.saveSessionToJournal
);

/**
 * @swagger
 * /emergency/helplines:
 *   get:
 *     summary: Get emergency helpline information by country
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: country
 *         schema:
 *           type: string
 *           example: "US"
 *         description: Country code for localized helplines
 *     responses:
 *       200:
 *         description: Emergency helplines retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         helplines:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               name:
 *                                 type: string
 *                                 example: "National Suicide Prevention Lifeline"
 *                               phone:
 *                                 type: string
 *                                 example: "988"
 *                               description:
 *                                 type: string
 *                               availability:
 *                                 type: string
 *                                 example: "24/7"
 *                               country:
 *                                 type: string
 *                                 example: "US"
 *                               isIslamic:
 *                                 type: boolean
 *                                 example: false
 */
router.get(
  '/helplines',
  [
    query('country')
      .optional()
      .isString()
      .withMessage('Country must be a string'),
  ],
  emergencyController.getEmergencyHelplines
);

// /resources route moved above auth middleware

/**
 * @swagger
 * /emergency/crisis-report:
 *   post:
 *     summary: Report a crisis situation
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - severity
 *               - indicators
 *               - userResponse
 *             properties:
 *               severity:
 *                 type: string
 *                 enum: [low, moderate, high, critical]
 *               indicators:
 *                 type: array
 *                 items:
 *                   type: string
 *               userResponse:
 *                 type: string
 *               assessmentContext:
 *                 type: object
 *               immediateRisk:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Crisis report processed successfully
 */
router.post(
  '/crisis-report',
  [
    body('severity')
      .isIn(['low', 'moderate', 'high', 'critical'])
      .withMessage('Invalid severity'),
    body('indicators').isArray().withMessage('Indicators must be an array'),
    body('userResponse').isString().withMessage('User response required'),
    body('immediateRisk').optional().isBoolean(),
  ],
  emergencyController.reportCrisis
);

/**
 * @swagger
 * /emergency/crisis-status/{escalationId}:
 *   get:
 *     summary: Get crisis escalation status
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: escalationId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Crisis status retrieved successfully
 */
router.get(
  '/crisis-status/:escalationId',
  [param('escalationId').isString().withMessage('Escalation ID required')],
  emergencyController.getCrisisStatus
);

/**
 * @swagger
 * /emergency/follow-up:
 *   post:
 *     summary: Submit crisis follow-up
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - escalationId
 *               - status
 *             properties:
 *               escalationId:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [improved, same, worse]
 *               notes:
 *                 type: string
 *               additionalSupport:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Follow-up submitted successfully
 */
router.post(
  '/follow-up',
  [
    body('escalationId').isString().withMessage('Escalation ID required'),
    body('status')
      .isIn(['improved', 'same', 'worse'])
      .withMessage('Invalid status'),
    body('notes').optional().isString(),
    body('additionalSupport').optional().isBoolean(),
  ],
  emergencyController.submitFollowUp
);

/**
 * @swagger
 * /emergency/escalate:
 *   post:
 *     summary: Escalate a crisis session to a professional or emergency services
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *               - escalationType
 *             properties:
 *               sessionId:
 *                 type: string
 *                 format: uuid
 *                 description: The ID of the emergency session to escalate.
 *               escalationType:
 *                 type: string
 *                 enum: [mental_health_professional, emergency_services]
 *                 description: The type of escalation.
 *               notes:
 *                 type: string
 *                 description: Additional notes for the escalation.
 *     responses:
 *       200:
 *         description: Crisis session escalated successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/escalate',
  [
    body('sessionId').isUUID().withMessage('Invalid session ID'),
    body('escalationType')
      .isIn(['mental_health_professional', 'emergency_services'])
      .withMessage('Invalid escalation type'),
    body('notes').optional().isString(),
  ],
  emergencyController.escalateToProfessional
);

/**
 * @swagger
 * /emergency/professionals:
 *   get:
 *     summary: Get a list of verified Islamic mental health professionals
 *     tags: [Emergency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: specialization
 *         schema:
 *           type: string
 *         description: Filter by professional specialization
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Filter by location
 *     responses:
 *       200:
 *         description: Professionals retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         professionals:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/CrisisContactDTO'
 */
router.get(
  '/professionals',
  [
    query('specialization').optional().isString(),
    query('location').optional().isString(),
  ],
  emergencyController.getProfessionalContacts
);

export default router;
