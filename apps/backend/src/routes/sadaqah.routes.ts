import { Router } from 'express';
import { sadaqah<PERSON>ontroller } from '../controllers/sadaqah.controller';
import { authenticateToken } from '../middleware/auth';
import { body } from 'express-validator';

const router = Router();

/**
 * @route   POST /api/sadaqah/gifts/create
 * @desc    Create a new gift journey
 * @access  Private
 */
router.post('/gifts/create',
  authenticateToken,
  [
    body('recipientEmail').isEmail().withMessage('Valid recipient email is required'),
    body('journeyType').notEmpty().withMessage('Journey type is required'),
    body('durationDays').isInt({ min: 1, max: 365 }).withMessage('Duration must be between 1 and 365 days'),
    body('paymentMethod').notEmpty().withMessage('Payment method is required'),
    body('paymentToken').notEmpty().withMessage('Payment token is required'),
    body('personalMessage').optional().isLength({ max: 500 }).withMessage('Personal message must be less than 500 characters')
  ],
  sadaqahController.createGiftJourney.bind(sadaqahController)
);

/**
 * @route   POST /api/sadaqah/gifts/claim
 * @desc    Claim a gift journey
 * @access  Public
 */
router.post('/gifts/claim',
  [
    body('invitationCode').notEmpty().withMessage('Invitation code is required'),
    body('userEmail').isEmail().withMessage('Valid email is required')
  ],
  sadaqahController.claimGiftJourney.bind(sadaqahController)
);

/**
 * @route   GET /api/sadaqah/gifts/sent
 * @desc    Get user's gifted journeys (as donor)
 * @access  Private
 */
router.get('/gifts/sent', authenticateToken, sadaqahController.getSentGifts.bind(sadaqahController));

/**
 * @route   GET /api/sadaqah/gifts/received
 * @desc    Get user's received gifts
 * @access  Private
 */
router.get('/gifts/received', authenticateToken, sadaqahController.getReceivedGifts.bind(sadaqahController));

/**
 * @route   GET /api/sadaqah/impact
 * @desc    Get sadaqah impact statistics
 * @access  Private
 */
router.get('/impact', authenticateToken, sadaqahController.getSadaqahImpact.bind(sadaqahController));

export default router;
