import express from 'express';
import { body } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as ruqyaController from '../controllers/ruqya.controller';
import { contentLimiter } from '../middleware/security';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Ruqya
 *   description: Islamic spiritual healing and protection system with self-assessment and treatment plans
 */

// Apply auth middleware to all ruqya routes
router.use(authMiddleware);

/**
 * @swagger
 * /ruqya/self-check/questions:
 *   get:
 *     summary: Get self-assessment checklist questions
 *     description: Retrieve questions for spiritual self-assessment to determine appropriate ruqya treatment
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Self-assessment questions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         questions:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               question:
 *                                 type: string
 *                                 example: "Do you experience unexplained nightmares?"
 *                               category:
 *                                 type: string
 *                                 example: "spiritual_symptoms"
 *                               answerType:
 *                                 type: string
 *                                 enum: [yes_no, scale, text]
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Get self-assessment checklist
router.get('/self-check/questions', ruqyaController.getSelfCheckQuestions);

/**
 * @swagger
 * /ruqya/self-check/submit:
 *   post:
 *     summary: Submit self-assessment responses for spiritual evaluation
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - responses
 *             properties:
 *               responses:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     questionId:
 *                       type: string
 *                       format: uuid
 *                     answer:
 *                       type: string
 *                     intensity:
 *                       type: integer
 *                       minimum: 1
 *                       maximum: 10
 *               additionalNotes:
 *                 type: string
 *               emergencyContact:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   phone:
 *                     type: string
 *     responses:
 *       201:
 *         description: Self-assessment submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         assessmentId:
 *                           type: string
 *                           format: uuid
 *                         riskLevel:
 *                           type: string
 *                           enum: [low, moderate, high, emergency]
 *                         recommendedActions:
 *                           type: array
 *                           items:
 *                             type: string
 *                         treatmentPlanId:
 *                           type: string
 *                           format: uuid
 */
// Submit self-assessment
router.post(
  '/self-check/submit',
  contentLimiter,
  [
    body('responses').isArray().withMessage('Responses must be an array'),
    body('responses.*.questionId').isUUID().withMessage('Invalid question ID'),
    body('responses.*.answer')
      .isString()
      .notEmpty()
      .withMessage('Answer required'),
    body('responses.*.intensity')
      .isInt({ min: 1, max: 10 })
      .withMessage('Valid intensity required'),
    body('additionalNotes').optional().isString(),
    body('emergencyContact').optional().isObject(),
  ],
  ruqyaController.submitSelfCheck
);

/**
 * @swagger
 * /ruqya/treatment-plan:
 *   get:
 *     summary: Get personalized ruqya treatment plan
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Treatment plan retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         planId:
 *                           type: string
 *                           format: uuid
 *                         duration:
 *                           type: integer
 *                           example: 40
 *                         practices:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               name:
 *                                 type: string
 *                                 example: "Morning Protection Adhkar"
 *                               frequency:
 *                                 type: string
 *                                 example: "daily"
 *                               duration:
 *                                 type: integer
 *                                 example: 15
 *                               verses:
 *                                 type: array
 *                                 items:
 *                                   type: object
 */
// Get personalized treatment plan
router.get('/treatment-plan', ruqyaController.getTreatmentPlan);

/**
 * @swagger
 * /ruqya/treatment-plan/progress:
 *   patch:
 *     summary: Update treatment progress and effectiveness
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - practiceId
 *               - completed
 *               - effectiveness
 *             properties:
 *               practiceId:
 *                 type: string
 *                 format: uuid
 *               completed:
 *                 type: boolean
 *               effectiveness:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: How effective was this practice (1-5)
 *               notes:
 *                 type: string
 *                 example: "Felt more peaceful after reciting these verses"
 *     responses:
 *       200:
 *         description: Treatment progress updated successfully
 */
// Update treatment progress
router.patch(
  '/treatment-plan/progress',
  [
    body('practiceId').isUUID().withMessage('Valid practice ID required'),
    body('completed').isBoolean().withMessage('Completion status required'),
    body('effectiveness')
      .isInt({ min: 1, max: 5 })
      .withMessage('Valid effectiveness rating required'),
    body('notes').optional().isString(),
  ],
  ruqyaController.updateTreatmentProgress
);

/**
 * @swagger
 * /ruqya/duas:
 *   get:
 *     summary: Get recommended duas and adhkar for protection
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Recommended duas retrieved successfully
 */
// Get recommended duas and adhkar
router.get('/duas', ruqyaController.getRecommendedDuas);

/**
 * @swagger
 * /ruqya/duas/save:
 *   post:
 *     summary: Save a favorite dua for quick access
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - duaId
 *               - category
 *             properties:
 *               duaId:
 *                 type: string
 *                 format: uuid
 *               category:
 *                 type: string
 *                 example: "protection"
 *               notes:
 *                 type: string
 *                 example: "This dua helps me feel protected"
 *     responses:
 *       201:
 *         description: Favorite dua saved successfully
 */
// Save favorite dua
router.post(
  '/duas/save',
  [
    body('duaId').isUUID().withMessage('Valid dua ID required'),
    body('category').isString().notEmpty().withMessage('Category required'),
    body('notes').optional().isString(),
  ],
  ruqyaController.saveFavoriteDua
);

/**
 * @swagger
 * /ruqya/resources:
 *   get:
 *     summary: Get healing resources and educational content
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Healing resources retrieved successfully
 */
// Get healing resources
router.get('/resources', ruqyaController.getHealingResources);

/**
 * @swagger
 * /ruqya/report-concern:
 *   post:
 *     summary: Report spiritual concerns or symptoms
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - concernType
 *               - description
 *               - severity
 *               - isEmergency
 *             properties:
 *               concernType:
 *                 type: string
 *                 enum: [symptom, spiritual, emotional, physical]
 *               description:
 *                 type: string
 *                 example: "Experiencing recurring nightmares and feeling of being watched"
 *               severity:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *               isEmergency:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Concern reported successfully
 */
// Report symptoms or concerns
router.post(
  '/report-concern',
  contentLimiter,
  [
    body('concernType')
      .isIn(['symptom', 'spiritual', 'emotional', 'physical'])
      .withMessage('Valid concern type required'),
    body('description')
      .isString()
      .notEmpty()
      .withMessage('Description required'),
    body('severity')
      .isInt({ min: 1, max: 5 })
      .withMessage('Valid severity required'),
    body('isEmergency').isBoolean().withMessage('Emergency status required'),
  ],
  ruqyaController.reportConcern
);

/**
 * @swagger
 * /ruqya/progress:
 *   get:
 *     summary: Get progress timeline and healing journey
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Progress timeline retrieved successfully
 */
// Get progress timeline
router.get('/progress', ruqyaController.getProgressTimeline);

/**
 * @swagger
 * /ruqya/schedule-consultation:
 *   post:
 *     summary: Schedule professional consultation with qualified practitioner
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - preferredDate
 *               - consultationType
 *               - primaryConcern
 *             properties:
 *               preferredDate:
 *                 type: string
 *                 format: date-time
 *               consultationType:
 *                 type: string
 *                 enum: [general, emergency, follow-up]
 *               primaryConcern:
 *                 type: string
 *               previousTreatment:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Consultation scheduled successfully
 */
// Schedule professional consultation
router.post(
  '/schedule-consultation',
  [
    body('preferredDate').isISO8601().withMessage('Valid date required'),
    body('consultationType')
      .isIn(['general', 'emergency', 'follow-up'])
      .withMessage('Valid consultation type required'),
    body('primaryConcern')
      .isString()
      .notEmpty()
      .withMessage('Primary concern required'),
    body('previousTreatment').optional().isBoolean(),
  ],
  ruqyaController.scheduleConsultation
);

/**
 * @swagger
 * /ruqya/safety-guidelines:
 *   get:
 *     summary: Get safety guidelines for ruqya practice
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Safety guidelines retrieved successfully
 */
// Get safety guidelines
router.get('/safety-guidelines', ruqyaController.getSafetyGuidelines);

/**
 * @swagger
 * /ruqya/protection/daily:
 *   post:
 *     summary: Submit daily protection practices completion
 *     tags: [Ruqya]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - practices
 *             properties:
 *               practices:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [morning_adhkar, evening_adhkar, general_protection]
 *                     completed:
 *                       type: boolean
 *                     time:
 *                       type: string
 *                       format: date-time
 *     responses:
 *       201:
 *         description: Daily protection practices submitted successfully
 */
// Submit daily protection practices
router.post(
  '/protection/daily',
  [
    body('practices').isArray().withMessage('Practices must be an array'),
    body('practices.*.type')
      .isIn(['morning_adhkar', 'evening_adhkar', 'general_protection'])
      .withMessage('Valid practice type required'),
    body('practices.*.completed')
      .isBoolean()
      .withMessage('Completion status required'),
    body('practices.*.time').isISO8601().withMessage('Valid time required'),
  ],
  ruqyaController.submitDailyProtection
);

export default router;
