import express from 'express';
import { body, query } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as contentController from '../controllers/content.controller';
import { contentTypeMiddleware } from '../middleware/contentType';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Content
 *   description: Educational content management including articles, videos, audio, and personalized recommendations
 */

// Apply auth middleware to all content routes
router.use(authMiddleware);

/**
 * @swagger
 * /content/feed:
 *   get:
 *     summary: Get personalized content feed based on user preferences and journey
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *       - in: query
 *         name: contentType
 *         schema:
 *           type: string
 *           enum: [all, article, video, audio, infographic]
 *           default: all
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *     responses:
 *       200:
 *         description: Personalized content feed retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         content:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               title:
 *                                 type: string
 *                                 example: "Understanding the Layers of the Soul"
 *                               type:
 *                                 type: string
 *                                 enum: [article, video, audio, infographic]
 *                               description:
 *                                 type: string
 *                               duration:
 *                                 type: integer
 *                                 description: Duration in minutes
 *                               tags:
 *                                 type: array
 *                                 items:
 *                                   type: string
 *                               layer:
 *                                 type: string
 *                                 enum: [Jism, Nafs, Aql, Qalb, Ruh]
 *                               difficulty:
 *                                 type: string
 *                                 enum: [beginner, intermediate, advanced]
 *                         pagination:
 *                           type: object
 *                           properties:
 *                             page:
 *                               type: integer
 *                             totalPages:
 *                               type: integer
 *                             totalItems:
 *                               type: integer
 */
// Get personalized content feed
router.get(
  '/feed',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
    query('contentType')
      .optional()
      .isIn(['all', 'article', 'video', 'audio', 'infographic']),
    query('tags').optional().isArray(),
  ],
  contentController.getPersonalizedFeed
);

/**
 * @swagger
 * /content/{contentId}:
 *   get:
 *     summary: Get specific content piece by ID
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: contentId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Content retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         content:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               format: uuid
 *                             title:
 *                               type: string
 *                             content:
 *                               type: string
 *                             type:
 *                               type: string
 *                               enum: [article, video, audio, infographic]
 *                             accessUrl:
 *                               type: string
 *                               format: uri
 *                             metadata:
 *                               type: object
 *       404:
 *         description: Content not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Get specific content piece
router.get(
  '/:contentId',
  contentTypeMiddleware,
  contentController.getContentById
);

/**
 * @swagger
 * /content/category/{category}:
 *   get:
 *     summary: Get content by category
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [recent, popular, recommended]
 *           default: recent
 *     responses:
 *       200:
 *         description: Category content retrieved successfully
 */
// Get content by category
router.get(
  '/category/:category',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
    query('sort').optional().isIn(['recent', 'popular', 'recommended']),
  ],
  contentController.getContentByCategory
);

/**
 * @swagger
 * /content/search:
 *   get:
 *     summary: Search content using AI-powered semantic search
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           example: "anxiety relief through dhikr"
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, article, video, audio, infographic]
 *           default: all
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 */
// Search content
router.get(
  '/search',
  [
    query('q').isString().notEmpty(),
    query('type')
      .optional()
      .isIn(['all', 'article', 'video', 'audio', 'infographic']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
  ],
  contentController.searchContent
);

/**
 * @swagger
 * /content/series/{seriesId}:
 *   get:
 *     summary: Get educational content series
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: seriesId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Content series retrieved successfully
 */
// Get educational series
router.get('/series/:seriesId', contentController.getContentSeries);

/**
 * @swagger
 * /content/interact:
 *   post:
 *     summary: Track content interaction for analytics and recommendations
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contentId
 *               - interactionType
 *             properties:
 *               contentId:
 *                 type: string
 *                 format: uuid
 *               interactionType:
 *                 type: string
 *                 enum: [view, complete, bookmark, share]
 *               duration:
 *                 type: integer
 *                 minimum: 0
 *                 description: Time spent in seconds
 *               progress:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *                 description: Completion percentage
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *     responses:
 *       200:
 *         description: Interaction tracked successfully
 */
// Track content interaction
router.post(
  '/interact',
  [
    body('contentId').isUUID().withMessage('Valid content ID required'),
    body('interactionType')
      .isIn(['view', 'complete', 'bookmark', 'share'])
      .withMessage('Valid interaction type required'),
    body('duration').optional().isInt({ min: 0 }),
    body('progress').optional().isFloat({ min: 0, max: 100 }),
    body('rating').optional().isInt({ min: 1, max: 5 }),
  ],
  contentController.trackInteraction
);

/**
 * @swagger
 * /content/bookmarks:
 *   get:
 *     summary: Get user's bookmarked content
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *     responses:
 *       200:
 *         description: Bookmarked content retrieved successfully
 */
// Get bookmarked content
router.get(
  '/bookmarks',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
  ],
  contentController.getBookmarkedContent
);

/**
 * @swagger
 * /content/recommendations:
 *   get:
 *     summary: Get AI-powered content recommendations
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: contentType
 *         schema:
 *           type: string
 *           enum: [all, article, video, audio, infographic]
 *           default: all
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *           default: 10
 *     responses:
 *       200:
 *         description: Content recommendations retrieved successfully
 */
// Get content recommendations
router.get(
  '/recommendations',
  [
    query('contentType')
      .optional()
      .isIn(['all', 'article', 'video', 'audio', 'infographic']),
    query('limit').optional().isInt({ min: 1, max: 20 }),
  ],
  contentController.getRecommendations
);

/**
 * @swagger
 * /content/report:
 *   post:
 *     summary: Report content issues or inappropriate material
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contentId
 *               - issueType
 *               - description
 *             properties:
 *               contentId:
 *                 type: string
 *                 format: uuid
 *               issueType:
 *                 type: string
 *                 enum: [inappropriate, incorrect, technical, other]
 *               description:
 *                 type: string
 *                 example: "Content contains non-Islamic practices"
 *     responses:
 *       201:
 *         description: Content issue reported successfully
 */
// Report content issues
router.post(
  '/report',
  [
    body('contentId').isUUID().withMessage('Valid content ID required'),
    body('issueType')
      .isIn(['inappropriate', 'incorrect', 'technical', 'other'])
      .withMessage('Valid issue type required'),
    body('description')
      .isString()
      .notEmpty()
      .withMessage('Description required'),
  ],
  contentController.reportContentIssue
);

/**
 * @swagger
 * /content/layer/{layerType}:
 *   get:
 *     summary: Get content for specific healing layer (Jism, Nafs, Aql, Qalb, Ruh)
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: layerType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [Jism, Nafs, Aql, Qalb, Ruh]
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *       - in: query
 *         name: focus
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *     responses:
 *       200:
 *         description: Layer-specific content retrieved successfully
 */
// Get content for specific healing layer
router.get(
  '/layer/:layerType',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
    query('focus').optional().isArray(),
  ],
  contentController.getLayerContent
);

/**
 * @swagger
 * /content/daily-inspiration:
 *   get:
 *     summary: Get daily inspirational content based on user's journey
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Daily inspiration retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         inspiration:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               title:
 *                                 type: string
 *                               content:
 *                                 type: string
 *                               type:
 *                                 type: string
 *                               accessUrl:
 *                                 type: string
 *                                 format: uri
 */
// Get daily inspirational content
router.get('/daily-inspiration', contentController.getDailyInspiration);

export default router;
