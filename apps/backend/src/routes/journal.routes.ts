import express from 'express';
import { body, query, param } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as journalController from '../controllers/journal.controller';
import { contentLimiter } from '../middleware/security';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Journal
 *   description: Personal journaling and reflection system for spiritual growth
 */

// Apply auth middleware to all journal routes
router.use(authMiddleware);

/**
 * @swagger
 * /journal/entries:
 *   post:
 *     summary: Create a new journal entry
 *     tags: [Journal]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - content
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Today's Reflection on Qalb"
 *               content:
 *                 type: string
 *                 example: "Today I felt a deeper connection during my dhikr session..."
 *               entryType:
 *                 type: string
 *                 enum: [reflection, gratitude, general]
 *                 example: reflection
 *               mood:
 *                 type: string
 *                 example: peaceful
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dhikr", "qalb", "peace"]
 *               layers:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [jism, nafs, aql, qalb, ruh]
 *                 example: ["qalb", "ruh"]
 *               relatedData:
 *                 type: object
 *                 description: Additional metadata related to the entry
 *     responses:
 *       201:
 *         description: Journal entry created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         entryId:
 *                           type: string
 *                           format: uuid
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  '/entries',
  contentLimiter,
  [
    body('title').isString().notEmpty().withMessage('Title is required'),
    body('content').isString().notEmpty().withMessage('Content is required'),
    body('entryType')
      .optional()
      .isIn(['reflection', 'gratitude', 'general'])
      .withMessage('Invalid entry type'),
    body('mood').optional().isString(),
    body('tags').optional().isArray(),
    body('layers').optional().isArray(),
    body('relatedData').optional().isObject(),
  ],
  journalController.createJournalEntry
);

/**
 * @swagger
 * /journal/entries:
 *   get:
 *     summary: Get user's journal entries with filtering and pagination
 *     tags: [Journal]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: entryType
 *         schema:
 *           type: string
 *           enum: [reflection, gratitude, general]
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *       - in: query
 *         name: layers
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             enum: [jism, nafs, aql, qalb, ruh]
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in title and content
 *     responses:
 *       200:
 *         description: Journal entries retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         entries:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               title:
 *                                 type: string
 *                               content:
 *                                 type: string
 *                               entryType:
 *                                 type: string
 *                               mood:
 *                                 type: string
 *                               tags:
 *                                 type: array
 *                                 items:
 *                                   type: string
 *                               layers:
 *                                 type: array
 *                                 items:
 *                                   type: string
 *                               createdAt:
 *                                 type: string
 *                                 format: date-time
 *                               updatedAt:
 *                                 type: string
 *                                 format: date-time
 *                         pagination:
 *                           type: object
 *                           properties:
 *                             page:
 *                               type: integer
 *                             totalPages:
 *                               type: integer
 *                             totalItems:
 *                               type: integer
 */
router.get(
  '/entries',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('entryType')
      .optional()
      .isIn(['reflection', 'gratitude', 'general']),
    query('tags').optional().isArray(),
    query('layers').optional().isArray(),
    query('dateFrom').optional().isISO8601(),
    query('dateTo').optional().isISO8601(),
    query('search').optional().isString(),
  ],
  journalController.getJournalEntries
);

/**
 * @swagger
 * /journal/entries/{entryId}:
 *   get:
 *     summary: Get a specific journal entry
 *     tags: [Journal]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entryId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Journal entry retrieved successfully
 *       404:
 *         description: Journal entry not found
 *   patch:
 *     summary: Update a journal entry
 *     tags: [Journal]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entryId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               content:
 *                 type: string
 *               entryType:
 *                 type: string
 *                 enum: [reflection, gratitude, general]
 *               mood:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               layers:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [jism, nafs, aql, qalb, ruh]
 *     responses:
 *       200:
 *         description: Journal entry updated successfully
 *   delete:
 *     summary: Delete a journal entry
 *     tags: [Journal]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entryId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Journal entry deleted successfully
 */
router.get(
  '/entries/:entryId',
  [param('entryId').isUUID().withMessage('Invalid entry ID')],
  journalController.getJournalEntry
);

router.patch(
  '/entries/:entryId',
  [
    param('entryId').isUUID().withMessage('Invalid entry ID'),
    body('title').optional().isString(),
    body('content').optional().isString(),
    body('entryType')
      .optional()
      .isIn(['reflection', 'gratitude', 'general']),
    body('mood').optional().isString(),
    body('tags').optional().isArray(),
    body('layers').optional().isArray(),
  ],
  journalController.updateJournalEntry
);

router.delete(
  '/entries/:entryId',
  [param('entryId').isUUID().withMessage('Invalid entry ID')],
  journalController.deleteJournalEntry
);

export default router;
