import express from 'express';
import { body, param, query } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import { adminRoleCheckMiddleware } from '../middleware/adminCheck.middleware'; // Corrected import
import * as sunnahPracticeController from '../controllers/sunnah-practice-content.controller';
import { SunnahPracticeCategory } from '@prisma/client'; // Import enum for validation

const router = express.Router();

const validCategories = Object.values(SunnahPracticeCategory);

router.post(
  '/',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [
    body('title').isString().notEmpty().withMessage('Title is required'),
    body('category').isIn(validCategories).withMessage(`Category must be one of: ${validCategories.join(', ')}`),
    body('description').isString().notEmpty().withMessage('Description is required'),
    body('stepsJson').optional().isJSON().withMessage('Steps JSON must be valid JSON'),
    body('duasJson').optional().isJSON().withMessage('Duas JSON must be valid JSON'),
    // Add other fields from Prisma schema with validation
  ],
  sunnahPracticeController.createSunnahPracticeContent
);

router.get(
  '/',
  authMiddleware,
  [
      query('skip').optional().isInt({ min: 0 }).toInt(),
      query('take').optional().isInt({ min: 1, max: 100 }).toInt(),
      query('where').optional().isString(),
      query('orderBy').optional().isString(),
  ],
  sunnahPracticeController.getAllSunnahPracticeContent
);

router.get(
  '/lookup-by-title/:title', // Path param for title lookup
  authMiddleware,
  [param('title').isString().notEmpty().withMessage('Title is required for lookup')],
  sunnahPracticeController.getSunnahPracticeContentByTitle
);

router.get(
  '/:id',
  authMiddleware,
  [param('id').isUUID().withMessage('Valid UUID for ID is required')],
  sunnahPracticeController.getSunnahPracticeContentById
);

router.patch(
  '/:id',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [
    param('id').isUUID().withMessage('Valid UUID for ID is required'),
    // Add optional body validations
    body('title').optional().isString().notEmpty(),
    body('category').optional().isIn(validCategories),
  ],
  sunnahPracticeController.updateSunnahPracticeContent
);

router.delete(
  '/:id',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [param('id').isUUID().withMessage('Valid UUID for ID is required')],
  sunnahPracticeController.deleteSunnahPracticeContent
);

export default router;
