/**
 * Assessment Routes for Feature 1: Understanding Your Inner Landscape (REVISED)
 * API routes for spiritual assessment and diagnosis
 */

import { Router } from 'express';
import { assessmentController } from '../controllers/assessment.controller';
import { authenticateUser } from '../middleware/auth';
import { generalLimiter as rateLimiter } from '../middleware/rateLimiter';
import { adminOnlyMiddleware as isAdmin } from '../middleware/adminCheck.middleware';

const router = Router();

// Apply authentication to all routes
router.use(authenticateUser);

/**
 * @swagger
 * /api/assessment/start:
 *   post:
 *     summary: Start a new spiritual assessment session
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userProfile:
 *                 type: object
 *                 description: User profile from Feature 0 onboarding
 *     responses:
 *       201:
 *         description: Assessment session started successfully
 *       400:
 *         description: User profile required
 *       401:
 *         description: User not authenticated
 */
router.post('/start', rateLimiter, assessmentController.startAssessment);

/**
 * @swagger
 * /api/assessment/welcome:
 *   post:
 *     summary: Get personalized welcome content for the assessment
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userProfile:
 *                 type: object
 *                 description: User's profile data from onboarding (Feature 0)
 *             example:
 *               userProfile: { "mental_health_awareness": { "level": "symptom_aware" }, "demographics": { "ageRange": "26-35"} }
 *     responses:
 *       200:
 *         description: Personalized welcome content retrieved successfully
 *       400:
 *         description: User profile is required in the request body
 *       401:
 *         description: User not authenticated
 */
router.post('/welcome', rateLimiter, assessmentController.generateWelcome); // Renamed controller method

/**
 * @swagger
 * /api/assessment/{sessionId}/questions/{step}:
 *   get:
 *     summary: Get assessment questions for current step
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: step
 *         required: true
 *         schema:
 *           type: string
 *           enum: [welcome, physical_experiences, emotional_experiences, mental_experiences, spiritual_experiences, reflections]
 *     responses:
 *       200:
 *         description: Assessment questions retrieved successfully
 */
router.get(
  '/:sessionId/questions/:step',
  rateLimiter,
  assessmentController.getQuestions
);

/**
 * @swagger
 * /api/assessment/{sessionId}/submit:
 *   post:
 *     summary: Submit assessment response
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - step
 *               - responses
 *               - timeSpent
 *             properties:
 *               step:
 *                 type: string
 *                 description: Current assessment step
 *               responses:
 *                 type: object
 *                 description: User responses for the step
 *               timeSpent:
 *                 type: number
 *                 description: Time spent on this step in seconds
 *     responses:
 *       200:
 *         description: Response submitted successfully
 */
router.post(
  '/:sessionId/submit',
  rateLimiter,
  assessmentController.submitResponse
);

/**
 * @swagger
 * /api/assessment/{sessionId}/diagnosis:
 *   get:
 *     summary: Get spiritual diagnosis results
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Diagnosis retrieved successfully
 *       404:
 *         description: Diagnosis not yet available
 */
router.get(
  '/:sessionId/diagnosis',
  rateLimiter,
  assessmentController.getDiagnosis
);

/**
 * @swagger
 * /api/assessment/diagnosis/{diagnosisId}/feedback:
 *   post:
 *     summary: Submit feedback on diagnosis
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: diagnosisId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - accuracy
 *               - helpfulness
 *             properties:
 *               accuracy:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 5
 *                 description: How accurate was the diagnosis (1-5)
 *               helpfulness:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 5
 *                 description: How helpful was the diagnosis (1-5)
 *               comments:
 *                 type: string
 *                 description: Optional feedback comments
 *     responses:
 *       200:
 *         description: Feedback submitted successfully
 */
router.post(
  '/diagnosis/:diagnosisId/feedback',
  rateLimiter,
  assessmentController.submitFeedback
);

/**
 * @swagger
 * /api/assessment/session/{sessionId}:
 *   get:
 *     summary: Get assessment session details
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Session details retrieved successfully
 *       403:
 *         description: Access denied
 */
router.get('/session/:sessionId', rateLimiter, assessmentController.getSession);

/**
 * @swagger
 * /api/assessment/session/{sessionId}:
 *   put:
 *     summary: Update assessment session details
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               currentStep:
 *                 type: string
 *                 description: The new current step of the assessment
 *     responses:
 *       200:
 *         description: Session details updated successfully
 *       403:
 *         description: Access denied
 */
router.put('/session/:sessionId', rateLimiter, assessmentController.updateSession);

/**
 * @swagger
 * /api/assessment/{sessionId}/generate-diagnosis:
 *   post:
 *     summary: Force generate diagnosis for a session
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Diagnosis force-generated successfully
 *       403:
 *         description: Access denied
 *       500:
 *         description: Failed to generate diagnosis
 */
router.post(
  '/:sessionId/generate-diagnosis',
  rateLimiter,
  assessmentController.forceGenerateDiagnosis
);

/**
 * @swagger
 * /api/assessment/history:
 *   get:
 *     summary: Get user's assessment history
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Assessment history retrieved successfully
 */
router.get('/history', rateLimiter, assessmentController.getAssessmentHistory);

/**
 * @swagger
 * /api/assessment/resume/{sessionId}:
 *   post:
 *     summary: Resume incomplete assessment
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Assessment resumed successfully
 *       400:
 *         description: Assessment already completed
 *       403:
 *         description: Access denied
 */
router.post(
  '/resume/:sessionId',
  rateLimiter,
  assessmentController.resumeAssessment
);

/**
 * @swagger
 * /api/assessment/{sessionId}/abandon:
 *   post:
 *     summary: Abandon assessment session
 *     tags: [Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Reason for abandoning assessment
 *     responses:
 *       200:
 *         description: Assessment abandoned successfully
 */
router.post(
  '/:sessionId/abandon',
  rateLimiter,
  assessmentController.abandonAssessment
);

/**
 * @swagger
 * /api/assessment/admin/reset/{sessionId}:
 *   post:
 *     summary: Reset an assessment session (Admin only)
 *     tags: [Assessment, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Assessment session reset successfully
 *       404:
 *         description: Session not found
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/admin/reset/:sessionId',
  isAdmin,
  rateLimiter,
  assessmentController.resetSession
);

export default router;
