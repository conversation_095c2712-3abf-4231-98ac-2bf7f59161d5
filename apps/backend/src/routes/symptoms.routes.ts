import express from 'express';
import { body } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import * as symptomsController from '../controllers/symptoms.controller';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Symptoms
 *   description: Symptom tracking and analysis for the five layers of human existence
 */

// Apply auth middleware to all symptoms routes
router.use(authMiddleware);

/**
 * @swagger
 * /symptoms/submit:
 *   post:
 *     summary: Submit symptoms for AI analysis across five layers
 *     tags: [Symptoms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SymptomSubmission'
 *     responses:
 *       201:
 *         description: Symptoms submitted and analyzed successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         submissionId:
 *                           type: string
 *                           format: uuid
 *                         analysis:
 *                           type: object
 *                           properties:
 *                             primaryLayer:
 *                               type: string
 *                               example: qalb
 *                             severity:
 *                               type: string
 *                               enum: [mild, moderate, severe]
 *                             recommendations:
 *                               type: array
 *                               items:
 *                                 type: string
 *       400:
 *         description: Invalid symptom data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Submit symptoms for AI analysis
router.post(
  '/submit',
  [
    body('jism').isArray().withMessage('Jism symptoms must be an array'),
    body('nafs').isArray().withMessage('Nafs symptoms must be an array'),
    body('aql').isArray().withMessage('Aql symptoms must be an array'),
    body('qalb').isArray().withMessage('Qalb symptoms must be an array'),
    body('ruh').isArray().withMessage('Ruh symptoms must be an array'),
    body('intensity').isObject().withMessage('Intensity ratings required'),
    body('duration').isString().notEmpty().withMessage('Duration is required'),
  ],
  symptomsController.submitSymptoms
);

/**
 * @swagger
 * /symptoms/history:
 *   get:
 *     summary: Get user's symptom submission history
 *     tags: [Symptoms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Symptom history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           submittedAt:
 *                             type: string
 *                             format: date-time
 *                           symptoms:
 *                             $ref: '#/components/schemas/SymptomSubmission'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Get user's symptom history
router.get('/history', symptomsController.getSymptomHistory);

/**
 * @swagger
 * /symptoms/latest-diagnosis:
 *   get:
 *     summary: Get the latest AI diagnosis for the user
 *     tags: [Symptoms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Latest diagnosis retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         diagnosisId:
 *                           type: string
 *                           format: uuid
 *                         primaryLayer:
 *                           type: string
 *                           example: qalb
 *                         severity:
 *                           type: string
 *                           enum: [mild, moderate, severe]
 *                         recommendations:
 *                           type: array
 *                           items:
 *                             type: string
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *       404:
 *         description: No diagnosis found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Get latest diagnosis
router.get('/latest-diagnosis', symptomsController.getLatestDiagnosis);

/**
 * @swagger
 * /symptoms/track:
 *   patch:
 *     summary: Update symptom tracking progress
 *     tags: [Symptoms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symptomId
 *               - intensity
 *             properties:
 *               symptomId:
 *                 type: string
 *                 format: uuid
 *                 example: 123e4567-e89b-12d3-a456-************
 *               intensity:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 10
 *                 example: 7
 *               notes:
 *                 type: string
 *                 example: Feeling better after dhikr session
 *     responses:
 *       200:
 *         description: Symptom tracking updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         trackingId:
 *                           type: string
 *                           format: uuid
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *       400:
 *         description: Invalid tracking data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// Update symptom tracking
router.patch(
  '/track',
  [
    body('symptomId').isUUID().withMessage('Valid symptom ID required'),
    body('intensity')
      .isInt({ min: 1, max: 10 })
      .withMessage('Intensity must be between 1-10'),
    body('notes').optional().isString(),
  ],
  symptomsController.trackSymptomProgress
);

export default router;
