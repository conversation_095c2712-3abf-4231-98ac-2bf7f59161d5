import express from 'express';
import { body, param, query } from 'express-validator';
import { authMiddleware } from '../middleware/auth';
import { adminRoleCheckMiddleware } from '../middleware/adminCheck.middleware'; // Corrected import
import * as nameOfAllah<PERSON>ontentController from '../controllers/name-of-allah-content.controller';

const router = express.Router();

// Apply auth middleware to all routes, and admin role check for CUD operations
// Note: Role check middleware needs to be implemented or adapted from existing patterns if any.
// For now, adminRoleCheckMiddleware is a placeholder.

router.post(
  '/',
  authMiddleware, // General authentication
  adminRoleCheckMiddleware, // Placeholder for admin/content-manager role check
  [
    body('name').isString().notEmpty().withMessage('Name is required'),
    body('meaning').isString().notEmpty().withMessage('Meaning is required'),
    body('significance').isString().notEmpty().withMessage('Significance is required'),
    body('arabicScript').optional().isString(),
    body('audioUrl').optional().isURL().withMessage('Valid Audio URL required'),
    body('reflectionPrompt').optional().isString(),
    body('practicalApplication').optional().isString(),
    body('dhikrCount').optional().isInt({ min: 0 }),
    body('layerFocus').optional().isArray(),
    body('benefits').optional().isArray(),
    body('sourceReference').optional().isString(),
    body('isActive').optional().isBoolean(),
  ],
  nameOfAllahContentController.createNameOfAllahContent
);

router.get(
  '/',
  authMiddleware, // All authenticated users can list (e.g., for AI service to fetch)
  [
      query('skip').optional().isInt({ min: 0 }).toInt(),
      query('take').optional().isInt({ min: 1, max: 100 }).toInt(),
      // Basic validation for where/orderBy, actual parsing is in controller
      query('where').optional().isString(),
      query('orderBy').optional().isString(),
  ],
  nameOfAllahContentController.getAllNameOfAllahContent
);

router.get(
  '/:id',
  authMiddleware,
  [param('id').isUUID().withMessage('Valid UUID for ID is required')],
  nameOfAllahContentController.getNameOfAllahContentById
);

router.patch(
  '/:id',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [
    param('id').isUUID().withMessage('Valid UUID for ID is required'),
    body('name').optional().isString().notEmpty(),
    body('meaning').optional().isString().notEmpty(),
    body('significance').optional().isString().notEmpty(),
    // Add other fields as optional for patch
  ],
  nameOfAllahContentController.updateNameOfAllahContent
);

router.delete(
  '/:id',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [param('id').isUUID().withMessage('Valid UUID for ID is required')],
  nameOfAllahContentController.deleteNameOfAllahContent
);

export default router;
