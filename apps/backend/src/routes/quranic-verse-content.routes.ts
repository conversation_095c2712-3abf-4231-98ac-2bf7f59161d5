import express from 'express';
import { body, param, query, check, validationResult } from 'express-validator'; // Added validationResult
import { authMiddleware } from '../middleware/auth';
import { adminRoleCheckMiddleware } from '../middleware/adminCheck.middleware'; // Corrected import
import * as quranicVerseController from '../controllers/quranic-verse-content.controller';

const router = express.Router();

router.post(
  '/',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [
    body('surahNumber').isInt({ min: 1, max: 114 }).withMessage('Valid Surah number is required (1-114)'),
    body('ayahNumber').isInt({ min: 1 }).withMessage('Valid Ayah number is required'),
    body('arabicText').isString().notEmpty().withMessage('Arabic text is required'),
    body('translationEn').optional().isString(),
    body('audioUrl').optional().isURL(),
    // Add other fields from Prisma schema with validation as needed
  ],
  quranicVerseController.createQuranicVerseContent
);

router.get(
  '/',
  authMiddleware,
   [
      query('skip').optional().isInt({ min: 0 }).toInt(),
      query('take').optional().isInt({ min: 1, max: 100 }).toInt(),
      query('where').optional().isString(),
      query('orderBy').optional().isString(),
  ],
  quranicVerseController.getAllQuranicVerseContent
);

router.get(
  '/lookup', // Using query params for surah/ayah lookup
  authMiddleware,
  [
    query('surahNumber').isInt({ min: 1, max: 114 }).toInt().withMessage('Valid Surah number required'),
    query('ayahNumber').isInt({ min: 1 }).toInt().withMessage('Valid Ayah number required'),
  ],
  (req: express.Request, res: express.Response, next: express.NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    // Remap query params to path params for the controller function if needed, or adapt controller
    // For simplicity, we'll assume controller can handle req.query if we modify it or make a new one
    // Or, we can directly call the service method here after validation.
    // Let's adapt by creating a new controller function or modifying existing one if it expects path params.
    // For now, let's assume the controller `getQuranicVerseContentBySurahAyah` is adapted to use req.query
    // Or more cleanly, pass them directly:
    req.params.surahNumber = req.query.surahNumber as string;
    req.params.ayahNumber = req.query.ayahNumber as string;
    return quranicVerseController.getQuranicVerseContentBySurahAyah(req, res, next);
  }
);

router.get(
  '/:id',
  authMiddleware,
  [param('id').isUUID().withMessage('Valid UUID for ID is required')],
  quranicVerseController.getQuranicVerseContentById
);

router.patch(
  '/:id',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [
    param('id').isUUID().withMessage('Valid UUID for ID is required'),
    // Add optional body validations
  ],
  quranicVerseController.updateQuranicVerseContent
);

router.delete(
  '/:id',
  authMiddleware,
  adminRoleCheckMiddleware, // Placeholder
  [param('id').isUUID().withMessage('Valid UUID for ID is required')],
  quranicVerseController.deleteQuranicVerseContent
);

export default router;
