import { Router } from 'express';
import { dashboardController } from '../controllers/dashboard.controller';
import { authenticateToken } from '../middleware/auth';
import { body } from 'express-validator';

const router = Router();

/**
 * @route   GET /api/dashboard/today
 * @desc    Get today's dashboard focus and content
 * @access  Private
 */
router.get('/today', authenticateToken, dashboardController.getTodayDashboard.bind(dashboardController));

/**
 * @route   GET /api/dashboard/weekly-summary
 * @desc    Get weekly progress summary
 * @access  Private
 */
router.get('/weekly-summary', authenticateToken, dashboardController.getWeeklySummary.bind(dashboardController));

/**
 * @route   GET /api/dashboard/recommendations
 * @desc    Get personalized recommendations
 * @access  Private
 */
router.get('/recommendations', authenticateToken, dashboardController.getRecommendations.bind(dashboardController));

/**
 * @route   PUT /api/dashboard/preferences
 * @desc    Update dashboard preferences
 * @access  Private
 */
router.put('/preferences', 
  authenticateToken,
  [
    body('preferences').isObject().withMessage('Preferences must be an object')
  ],
  dashboardController.updatePreferences.bind(dashboardController)
);

export default router;
