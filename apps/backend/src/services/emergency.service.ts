import { prisma } from '../config/database'; // Using Prisma
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { triggerN8nWorkflow } from './n8n.service';
import axios from 'axios'; // Added for AI service calls
// communityService import might be removed if its requestDua is no longer called.
// For now, keeping it to evaluate during the requestDuaFromCommunity method refactor.
import { communityService } from './community.service';
import { Prisma } from '@prisma/client'; // For JsonNull type if needed for log


// AI Service Configuration
const AI_SERVICE_BASE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8001'; // Default if not set

// Define Qalb Rescue step names
const QALB_RESCUE_STEPS = [
  'grounding',
  'breathing',
  'comfort',
  'reflection',
  'connection',
] as const;

type QalbRescueStepName = (typeof QALB_RESCUE_STEPS)[number];

interface StepContent {
  title: string;
  description: string;
  audioUrl?: string;
  visualElements?: any; // Simplified for now
  interactiveElements?: any; // Simplified for now
  items?: Array<{
    id: string;
    type: 'verse' | 'dhikr' | 'prompt';
    text: string;
    translation?: string;
    audioUrl?: string;
  }>;
}

interface EmergencySessionData {
  id: string;
  userId: string;
  triggerType: string;
  currentStep?: QalbRescueStepName | null;
  currentSymptoms: string[];
  startTime: Date;
  endTime?: Date | null;
  status: string;
  escalationReason?: string | null;
  recommendedActions: string[];
  estimatedDuration?: number | null;
  effectivenessRating?: number | null;
  feedback?: string | null;
  log?: any; // JSONB
  createdAt: Date;
  updatedAt: Date;
}
interface CrisisContactDTO {
  id: string;
  name: string;
  type: 'hotline' | 'imam' | 'counselor' | 'emergency';
  phone: string;
  availability: string;
  specialization: string[];
  isIslamic: boolean;
}


/**
 * Emergency Service - Handles crisis support and Qalb Rescue (Sakina mode)
 */
export class EmergencyService {
  // private supabase; // Removed Supabase client instance

  constructor() {
    // this.supabase = getSupabase(); // Removed Supabase initialization
  }

  private async _logEvent(sessionId: string, eventType: string, eventData: any) {
    try {
      const session = await prisma.emergencySession.findUnique({
        where: { id: sessionId },
        select: { log: true },
      });

      if (!session) {
        logger.error('Failed to fetch session for logging event', { sessionId, eventType });
        return;
      }

      const currentLog = (session.log as Prisma.JsonArray) || [];
      const newLogEntry = { type: eventType, timestamp: new Date().toISOString(), ...eventData };
      const updatedLog = [...currentLog, newLogEntry];

      await prisma.emergencySession.update({
        where: { id: sessionId },
        data: { log: updatedLog },
      });

    } catch (e: any) {
        logger.error('Error in _logEvent', { error: e.message });
    }
  }


  async startQalbRescueSession(
    userId: string,
    triggerType: string = 'manual',
    currentSymptoms: string[] = []
  ): Promise<{ session: EmergencySessionData; initialStepContent: StepContent }> {
    try {
      const initialStep: QalbRescueStepName = 'grounding';
      const sessionData = await prisma.emergencySession.create({
        data: {
          userId: userId,
          triggerType: triggerType,
          currentSymptoms: currentSymptoms,
          startTime: new Date(), // Prisma handles ISO conversion
          status: 'active',
          currentStep: initialStep,
          log: [{ type: 'session_started', step: initialStep, timestamp: new Date().toISOString() }] as Prisma.JsonArray,
        },
      });

      const initialStepContent = await this.getStepContent(initialStep, userId, sessionData.id);

      // Potentially trigger N8N workflow for initial recommended actions if still needed
      // await triggerN8nWorkflow('emergency-response', { userId, sessionId: sessionData.id, triggerType, currentSymptoms });

      logger.info('Qalb Rescue session started', { userId, sessionId: sessionData.id });
      return { session: sessionData as EmergencySessionData, initialStepContent };

    } catch (error) {
      logger.error('Error in startQalbRescueSession', { userId, error });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to start Qalb Rescue session', 500);
    }
  }

  async progressQalbRescueSession(
    sessionId: string,
    userId: string, // To verify ownership
    completedStep: QalbRescueStepName,
    timeSpentOnStep?: number
  ): Promise<{ currentStep: QalbRescueStepName; nextStepContent: StepContent } | null> {
    try {
      const session = await prisma.emergencySession.findUnique({
        where: { id: sessionId },
        select: { currentStep: true, status: true, userId: true, log: true },
      });

      if (!session) {
        throw new AppError('Emergency session not found.', 404);
      }
      if (session.userId !== userId) {
        throw new AppError('User not authorized for this session.', 403);
      }
      if (session.status !== 'active') {
        throw new AppError('Session is not active.', 400);
      }
      if (session.currentStep !== completedStep) {
        logger.warn('Completed step mismatch', { sessionId, expected: session.currentStep, actual: completedStep });
        // Potentially allow to proceed or return error - for now, log and proceed based on documented next step
      }

      const currentStepIndex = QALB_RESCUE_STEPS.indexOf(completedStep);
      if (currentStepIndex === -1 || currentStepIndex === QALB_RESCUE_STEPS.length - 1) {
        // Last step completed or invalid step
        await this.endQalbRescueSession(sessionId, userId, 'completed_all_steps');
        return null; // Or indicate completion
      }

      const nextStep = QALB_RESCUE_STEPS[currentStepIndex + 1];

      const currentLog = (session.log as Prisma.JsonArray) || [];
      const logEntry: any = {
        type: 'step_completed',
        step: completedStep,
        timestamp: new Date().toISOString(),
        nextStep: nextStep
      };
      if (timeSpentOnStep) logEntry.durationMs = timeSpentOnStep;
      const updatedLog = [...currentLog, logEntry];

      await prisma.emergencySession.update({
        where: { id: sessionId },
        data: {
            currentStep: nextStep,
            log: updatedLog,
            updatedAt: new Date()
        },
      });

      const nextStepContent = await this.getStepContent(nextStep, userId, sessionId);
      return { currentStep: nextStep, nextStepContent };

    } catch (error) {
      logger.error('Error progressing Qalb Rescue session', { sessionId, error });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to progress Qalb Rescue session', 500);
    }
  }

  async getStepContent(stepName: QalbRescueStepName, userId: string, sessionId: string): Promise<StepContent> {
    // This is where content fetching logic goes.
    // For Phase 1, we might use hardcoded or simple DB lookups.
    // Phase 3 will integrate AI personalization here.
    logger.info('Fetching content for step', { stepName, userId, sessionId });

    // Helper to fetch user profile and session symptoms for AI context
    const getAIContext = async () => {
        const userProfile = await prisma.profile.findUnique({
            where: { id: userId },
            // Select relevant preference fields. Assuming 'preferences' is a JSON field.
            // Adjust select based on actual Profile model structure for preferences.
            select: { preferences: true /* or specific keys like profileData: { select: { preferences: true }} */ }
        });

        const emergencySession = await prisma.emergencySession.findUnique({
            where: { id: sessionId },
            select: { currentSymptoms: true, log: true }
        });

        if (!userProfile || !emergencySession) {
            logger.warn('Could not fetch full context for AI personalization', { userId, sessionId });
        }

        // Assuming preferences might be nested or need default
        let userPrefs: any = {};
        if (userProfile?.preferences) {
            if (typeof userProfile.preferences === 'object' && userProfile.preferences !== null) {
                 // If preferences is a JSON object on Profile model
                userPrefs = userProfile.preferences;
            } else {
                // Attempt to parse if it's a stringified JSON, or handle other structures
                try { userPrefs = JSON.parse(userProfile.preferences as string); } catch { /* ignore */ }
            }
        }


        return {
            user_preferences: userPrefs || {},
            past_interactions: (emergencySession?.log as Prisma.JsonArray) || [],
            crisis_indicators: emergencySession?.currentSymptoms || []
        };
    };

    switch (stepName) {
      case 'grounding':
        return {
          title: "Step 1: Immediate Islamic Grounding",
          description: "Bismillah... breathe with me. Allah (SWT) sees you, knows your pain. You are not alone. Let's find peace.",
          audioUrl: "https://cdn.qalbhealing.com/audio/qalb_rescue/en/grounding_intro.mp3"
        };
      case 'breathing':
        const exercise = await prisma.breathingExercise.findFirst({ // Use findFirst for potentially no match
            where: { name: 'Qalb Rescue Dhikr Breathing' }, // Or a specific ID
            select: { name: true, instructions: true, duration: true, audioUrl: true }
        });

        if (!exercise) {
            logger.warn('Specific breathing exercise not found, using default');
            return {
              title: "Step 2: Spiritual Breathing",
              description: "Inhale (4s): La hawla\nHold (4s): wa la quwwata\nExhale (6s): illa billah",
            };
        }
        return {
            title: exercise.name || "Step 2: Spiritual Breathing",
            description: exercise.instructions?.join('\n') || "Follow the guided breathing pattern.",
            audioUrl: exercise.audioUrl, // Prisma returns audioUrl directly if selected
            items: [{ id: 'fixed_dhikr_lahawla', type: 'dhikr', text: "لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ", translation: "There is no power and no strength except with Allah"}]
        };
      case 'comfort':
        try {
            const aiContext = await getAIContext();
            const aiRequestPayload = {
                user_id: userId,
                user_preferences: aiContext.user_preferences,
                past_interactions: aiContext.past_interactions,
                crisis_indicators: aiContext.crisis_indicators
            };

            logger.info('Calling AI service for Quranic comfort', { url: `${AI_SERVICE_BASE_URL}/content-personalization/recommend`, payload: aiRequestPayload });
            const response = await axios.post(`${AI_SERVICE_BASE_URL}/content-personalization/recommend`, aiRequestPayload, { timeout: 5000 });

            if (response.data && response.data.quran_verses && response.data.quran_verses.length > 0) {
                logger.info('AI service returned Quran verses', { count: response.data.quran_verses.length, reasoning: response.data.reasoning });
                const items = response.data.quran_verses.map((v: any) => ({
                    id: v.id || `${v.surah_name_en}_${v.ayah_number}`,
                    type: 'verse',
                    text: v.arabic_text,
                    translation: v.translation_en,
                    audioUrl: v.audio_url,
                }));
                return {
                    title: "Step 3: Quranic Comfort (Personalized)",
                    description: response.data.reasoning || "Verses selected to bring you peace and reassurance.",
                    items: items
                };
            }
            logger.warn('AI service did not return suitable Quran verses, using fallback.', { aiResponse: response.data });
        } catch (aiError: any) {
            logger.error('AI service call failed for Quranic comfort, using fallback.', { error: aiError.message, code: aiError.code });
        }

        // Fallback logic if AI fails or returns no verses
        const verses = await prisma.ruqyahVerse.findMany({
            where: { category: 'emergency_comfort' },
            select: { id: true, surah: true, ayah: true, arabic: true, translation: true, audioUrl: true },
            take: 2,
        });

        if (!verses || verses.length === 0) {
           logger.warn('Fallback emergency comfort verses not found, using hardcoded default');
           return {
            title: "Step 3: Quranic Comfort",
            description: "Listen to these verses for peace and reassurance.",
            items: [
              { id: 'fallback_65_3', type: 'verse', text: "وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ", translation: "And whoever relies upon Allah - then He is sufficient for him. (65:3)"},
              { id: 'fallback_13_28', type: 'verse', text: "أَلَا بِذِكْرِ اللَّهِ تَطْمَئِنُّ الْقُلُوبُ", translation: "Verily, in the remembrance of Allah do hearts find rest. (13:28)"}
            ]
          };
        }
        return {
          title: "Step 3: Quranic Comfort (General)",
          description: "Listen to these verses from the Quran for peace and reassurance.",
          items: verses.map(v => ({
            id: v.id,
            type: 'verse',
            text: `${v.arabic} (${v.surah}:${v.ayah})`,
            translation: v.translation,
            audioUrl: v.audio_url
          }))
        };
      case 'reflection':
        return {
          title: "Step 4: Grounding & Reflection",
          description: "Engage your senses with the 5-4-3-2-1 technique:\n- 5 things you can see (Allah's creation)\n- 4 things you can touch (Allah's blessings)\n- 3 things you can hear (sounds of Allah's world)\n- 2 things you can smell (Allah's gifts)\n- 1 thing you can taste (gratitude for Allah's provision)",
        };
      case 'connection':
        return {
          title: "Step 5: Community Connection",
          description: "You are not alone. Reach out for support if you need to.",
          interactiveElements: {
            buttons: [
              { id: 'request_dua', label: "Request Du'a from Community" },
              { id: 'connect_peer', label: "Talk to a Peer Supporter" },
              { id: 'professional_help', label: "Find Professional Help"},
            ]
          }
        };
      default:
        throw new AppError("Invalid step name for content fetching.", 500);
    }
  }

  async recordUserFeedback(
    sessionId: string,
    userId: string,
    effectivenessRating?: number,
    feedbackText?: string
  ): Promise<void> {
    try {
      const session = await prisma.emergencySession.findUnique({
        where: { id: sessionId },
        select: { userId: true },
      });

      if (!session) throw new AppError('Session not found', 404);
      if (session.userId !== userId) throw new AppError('Unauthorized', 403);

      const updatePayload: Prisma.EmergencySessionUpdateInput = { updatedAt: new Date() };
      if (effectivenessRating !== undefined) updatePayload.effectivenessRating = effectivenessRating;
      if (feedbackText !== undefined) updatePayload.feedback = feedbackText;

      await prisma.emergencySession.update({
        where: { id: sessionId },
        data: updatePayload,
      });
      await this._logEvent(sessionId, 'feedback_recorded', {effectivenessRating, feedbackText});

    } catch (error: any) {
      logger.error('Error recording user feedback', { sessionId, error: error.message });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to record feedback', 500);
    }
  }

  async endQalbRescueSession(sessionId: string, userId: string, reason: string): Promise<void> {
     try {
      const session = await prisma.emergencySession.findUnique({
        where: { id: sessionId },
        select: { userId: true, status: true },
      });

      if (!session) throw new AppError('Session not found', 404);
      if (session.userId !== userId) throw new AppError('Unauthorized', 403);
      if (session.status !== 'active') {
        logger.warn('Attempting to end an already non-active session', {sessionId, status: session.status});
        return;
      }

      await prisma.emergencySession.update({
        where: { id: sessionId },
        data: {
            status: reason === 'completed_all_steps' ? 'completed' : 'aborted',
            endTime: new Date(),
            updatedAt: new Date()
        },
      });
      await this._logEvent(sessionId, 'session_ended', { reason });
      logger.info('Qalb Rescue session ended', {userId, sessionId, reason});

    } catch (error: any) {
      logger.error('Error ending Qalb Rescue session', { sessionId, error: error.message });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to end session', 500);
    }
  }

// communityService import is removed as we are not calling its requestDua method anymore.
// import { communityService } from './community.service';

  // --- Community Connection Methods (Phase 2) ---
  async requestDuaFromCommunity(sessionId: string, userId: string): Promise<void> {
    logger.info('Processing Du\'a request for Qalb Rescue session', { sessionId, userId });

    // 1. Log to our Prisma-based DuaRequestLog table
    try {
      await prisma.duaRequestLog.create({
        data: {
          sessionId: sessionId,
          userId: userId,
          requestedAt: new Date(),
        }
      });
      logger.info('Du\'a request logged to DuaRequestLog (Prisma)', { sessionId, userId });
    } catch (dbError: any) {
      logger.error('Failed to log dua request to Prisma DuaRequestLog', { sessionId, userId, error: dbError.message });
      // Decide if this should throw an error or just log. For now, logging.
      // If this is critical, an AppError could be thrown.
    }

    // 2. Log this specific event to the EmergencySession log
    await this._logEvent(sessionId, 'dua_requested_from_community', { userId });

    // 3. (Future) Trigger specific notifications.
    // Example: await triggerN8nWorkflow('qalb-rescue-dua-broadcast', { sessionId, userId });
  }

  async findPeerSupporter(sessionId: string, userId: string, criteria?: any): Promise<{
    status: 'supporter_found' | 'unavailable' | 'pending_acceptance' | 'error';
    message: string;
    peerSupportRequestId?: string;
    supporterDetails?: { id: string; name?: string | null };
  }> {
    logger.info('Attempting to find peer supporter', { sessionId, userId, criteria });
    await this._logEvent(sessionId, 'peer_support_request_initiated', { userId, criteria });

    try {
      // Step 1: Query for available supporters
      // Assuming 'profiles' table has 'isPeerSupporter' and 'isAvailableForSupport' (camelCase from Prisma schema)
      const availableSupporters = await prisma.profile.findMany({
        where: {
          isPeerSupporter: true,
          isAvailableForSupport: true,
          id: { not: userId }, // Don't match with the user requesting support
          // TODO: Add criteria filtering here if/when 'criteria' object is defined and Profile model supports it
        },
        select: { id: true, fullName: true, email: true }, // Select fields needed
      });


      if (!availableSupporters || availableSupporters.length === 0) {
        logger.info('No available peer supporters found', { userId, criteria });
        await this._logEvent(sessionId, 'peer_support_unavailable', { reason: 'no_supporters_available' });
        return { status: 'unavailable', message: 'No peer supporters are currently available.' };
      }

      const selectedSupporter = availableSupporters[0]; // Simple matching: pick the first

      // Step 2: Create PeerSupportRequest record
      const peerSupportRequestData = await prisma.peerSupportRequest.create({
        data: {
          sessionId: sessionId,
          userId: userId,
          supporterId: selectedSupporter.id,
          status: 'pending_acceptance',
          // criteria: criteria ? JSON.stringify(criteria) : Prisma.JsonNull, // If criteria is a JSON field
        }
      });

      logger.info('Peer supporter matched. TODO: Implement notification.', {
        supporterId: selectedSupporter.id,
        requestingUserId: userId,
        peerSupportRequestId: peerSupportRequestData.id,
      });
      await this._logEvent(sessionId, 'peer_supporter_matched_pending_notification', {
        supporterId: selectedSupporter.id,
        peerSupportRequestId: peerSupportRequestData.id
      });

      return {
        status: 'pending_acceptance',
        message: 'Peer supporter found and notified. Waiting for acceptance.',
        peerSupportRequestId: peerSupportRequestData.id,
        supporterDetails: {
          id: selectedSupporter.id,
          name: selectedSupporter.fullName,
        },
      };

    } catch (error: any) {
      logger.error('Unexpected error in findPeerSupporter', { sessionId, userId, error: error.message });
      return { status: 'error', message: 'An unexpected error occurred while finding a peer supporter.' };
    }
  }


  // --- Methods from original service, to be reviewed/refactored/removed ---
  // The following methods (escalateToProfessional, getProfessionalContacts) were the only ones
  // previously used by the controller. They use Prisma.
  // For now, they will be adapted to Supabase or their logic moved/re-implemented
  // if they are still needed in this exact form.

  async escalateToProfessional(
    userId: string,
    sessionId: string,
    escalationType: 'mental_health_professional' | 'emergency_services',
    notes?: string
  ): Promise<void> {
    try {
      const session = await prisma.emergencySession.findUnique({
        where: { id: sessionId },
        select: { notes: true, userId: true },
      });

      if (!session) throw new AppError('Session not found for escalation', 404);
      if (session.userId !== userId) throw new AppError('Unauthorized for escalation', 403);

      const existingNotes = session.notes || '';
      const newNotes = notes ? `${existingNotes}\nEscalation Notes: ${notes}`.trim() : existingNotes;

      await prisma.emergencySession.update({
        where: { id: sessionId },
        data: {
          status: 'escalated',
          escalationReason: `Escalated to ${escalationType}`,
          // Assuming an 'escalatedAt' field could be added to schema, otherwise using 'updatedAt'
          updatedAt: new Date(),
          notes: newNotes,
        },
      });

      const workflowName = escalationType === 'mental_health_professional'
        ? 'professional-referral'
        : 'emergency-services-alert';

      await triggerN8nWorkflow(workflowName, {
        userId,
        sessionId,
        notes,
        timestamp: new Date().toISOString(),
      });

      logger.warn(`Session escalated to ${escalationType}`, { userId, sessionId });
      await this._logEvent(sessionId, 'session_escalated', { escalationType, notes });

    } catch (error: any) {
      logger.error('Error escalating to professional', { userId, sessionId, escalationType, error: error.message });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to escalate to professional', 500);
    }
  }

  async getProfessionalContacts(
    userId: string,
    specialization?: string,
    location?: string
  ): Promise<CrisisContactDTO[]> {
    try {
      const whereClause: Prisma.EmergencyHelplineWhereInput = {
        isActive: true,
        // Assuming 'type' field exists on EmergencyHelpline to filter for counselors
        // If not, this part of the query needs adjustment based on how professionals are identified
        type: 'counselor',
      };

      if (specialization) {
        // Prisma text search needs specific setup (e.g., `contains` on a specific text field)
        // For now, assuming 'description' might hold specialization keywords.
        // This might need a more robust tagging or dedicated specialization field.
        whereClause.description = { contains: specialization, mode: 'insensitive' };
      }
      if (location) {
         whereClause.OR = [
          { country: { equals: location, mode: 'insensitive' } },
          { country: { equals: 'global', mode: 'insensitive' } } // Assuming 'global' for non-location specific
        ];
      }

      const contacts = await prisma.emergencyHelpline.findMany({
        where: whereClause,
        orderBy: { priority: 'asc' },
        select: {
            id: true,
            name: true,
            phone: true,
            availability: true,
            description: true, // Used for specialization for now
            isIslamic: true,
            // type: true, // if 'type' field exists
        }
      });

      const professionalContactsDTO: CrisisContactDTO[] = contacts.map((contact: any) => ({
        id: contact.id,
        name: contact.name,
        type: 'counselor', // Assuming we filtered for this type
        phone: contact.phone,
        availability: contact.availability || 'N/A',
        specialization: contact.description ? [contact.description] : [], // Simplified
        isIslamic: contact.isIslamic || false,
      }));

      logger.info('Professional contacts retrieved', { userId, count: professionalContactsDTO.length });
      return professionalContactsDTO;
    } catch (error: any) {
      logger.error('Error getting professional contacts', { userId, error: error.message });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve professional contacts', 500);
    }
  }
}

export const emergencyService = new EmergencyService();
