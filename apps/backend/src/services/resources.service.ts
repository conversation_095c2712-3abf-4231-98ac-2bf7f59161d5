/**
 * Resources Service for Journey Resources
 * Provides healing resources, Islamic content, and educational materials
 */

import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

export interface Resource {
  id: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'audio' | 'practice' | 'dua' | 'hadith';
  category: 'healing' | 'spiritual' | 'educational' | 'crisis' | 'community';
  content?: string;
  url?: string;
  duration?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  layerFocus?: string[];
  isBookmarked?: boolean;
}

export class ResourcesService {
  /**
   * Get recommended resources for a user's journey
   */
  async getJourneyResources(
    userId: string,
    journeyId?: string,
    category?: string,
    type?: string
  ): Promise<Resource[]> {
    try {
      logger.info('Getting journey resources', { userId, journeyId, category, type });

      // Get user's current journey if not provided
      let journey = null;
      if (journeyId) {
        journey = await prisma.journey.findFirst({
          where: { id: journeyId, userId },
        });
      } else {
        journey = await prisma.journey.findFirst({
          where: { 
            userId,
            status: { in: ['created', 'active', 'paused'] }
          },
          orderBy: { createdAt: 'desc' },
        });
      }

      // For now, return curated resources based on journey focus
      // In production, this would query a resources database
      const resources = this.getCuratedResources(journey, category, type);

      logger.info('Journey resources retrieved', { 
        userId, 
        journeyId, 
        resourceCount: resources.length 
      });

      return resources;
    } catch (error) {
      logger.error('Error getting journey resources', { 
        userId, 
        journeyId, 
        error: error.message 
      });
      throw new AppError('Failed to get journey resources', 500);
    }
  }

  /**
   * Get curated resources based on journey and filters
   */
  private getCuratedResources(
    journey: any,
    category?: string,
    type?: string
  ): Resource[] {
    const allResources: Resource[] = [
      // Healing Resources
      {
        id: 'healing-001',
        title: 'Understanding the Five Layers of Healing',
        description: 'Comprehensive guide to the Jism, Nafs, Aql, Qalb, and Ruh layers in Islamic healing methodology.',
        type: 'article',
        category: 'educational',
        duration: '15 min read',
        difficulty: 'beginner',
        tags: ['five-layers', 'healing', 'islamic-psychology', 'foundation'],
        layerFocus: ['jism', 'nafs', 'aql', 'qalb', 'ruh'],
        content: `# Understanding the Five Layers of Healing

## Introduction
Islamic healing recognizes five interconnected layers of human existence...

## The Five Layers
1. **Jism (Physical Body)** - The material vessel
2. **Nafs (Lower Self)** - Desires and ego
3. **Aql (Intellect)** - Reasoning and cognition
4. **Qalb (Heart)** - Spiritual center
5. **Ruh (Soul)** - Divine connection

## Integration in Healing
Each layer affects the others, requiring holistic treatment...`,
      },
      {
        id: 'healing-002',
        title: 'Dhikr for Heart Purification',
        description: 'Guided dhikr practices specifically designed for Qalb (heart) layer healing with authentic supplications.',
        type: 'audio',
        category: 'spiritual',
        duration: '20 min',
        difficulty: 'beginner',
        tags: ['dhikr', 'qalb', 'heart', 'purification', 'remembrance'],
        layerFocus: ['qalb'],
        url: 'https://example.com/audio/dhikr-heart-purification.mp3',
      },
      {
        id: 'healing-003',
        title: 'Managing Anxiety Through Islamic Practices',
        description: 'Practical techniques combining Islamic teachings with modern wellness approaches for anxiety management.',
        type: 'video',
        category: 'healing',
        duration: '25 min',
        difficulty: 'intermediate',
        tags: ['anxiety', 'mental-health', 'islamic-practices', 'coping'],
        layerFocus: ['nafs', 'aql'],
        url: 'https://example.com/video/anxiety-management.mp4',
      },
      {
        id: 'spiritual-001',
        title: 'Du\'a for Emotional Healing',
        description: 'Collection of authentic du\'as from Quran and Sunnah for emotional and spiritual healing.',
        type: 'dua',
        category: 'spiritual',
        duration: '10 min',
        difficulty: 'beginner',
        tags: ['dua', 'emotional-healing', 'spiritual', 'supplication'],
        layerFocus: ['qalb', 'ruh'],
        content: `# Du'a for Emotional Healing

## From the Quran
**Surah Al-Baqarah (2:286)**
رَبَّنَا لَا تُؤَاخِذْنَا إِن نَّسِينَا أَوْ أَخْطَأْنَا

*"Our Lord, do not impose blame upon us if we have forgotten or erred..."*

## From the Sunnah
**For Anxiety and Distress**
اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْهَمِّ وَالْحَزَنِ

*"O Allah, I seek refuge in You from anxiety and sorrow..."*`,
      },
      {
        id: 'educational-001',
        title: 'Prophetic Medicine for Mental Wellness',
        description: 'Hadith and Sunnah practices for maintaining mental and emotional health according to Islamic tradition.',
        type: 'hadith',
        category: 'educational',
        duration: '30 min read',
        difficulty: 'intermediate',
        tags: ['prophetic-medicine', 'mental-health', 'sunnah', 'hadith'],
        layerFocus: ['jism', 'nafs', 'aql'],
        content: `# Prophetic Medicine for Mental Wellness

## Hadith on Mental Health
The Prophet ﷺ said: "No fatigue, nor disease, nor sorrow, nor sadness, nor hurt, nor distress befalls a Muslim, not even if it were the prick he receives from a thorn, but that Allah expiates some of his sins for that." (Bukhari)

## Practical Applications
1. **Regular Prayer** - Establishes routine and connection
2. **Dhikr** - Calms the mind and heart
3. **Community** - Prevents isolation
4. **Gratitude** - Shifts perspective`,
      },
      {
        id: 'crisis-001',
        title: 'Crisis Support: When to Seek Help',
        description: 'Guidelines for recognizing crisis situations and accessing appropriate Islamic and professional support.',
        type: 'article',
        category: 'crisis',
        duration: '10 min read',
        difficulty: 'beginner',
        tags: ['crisis', 'support', 'emergency', 'mental-health'],
        layerFocus: ['nafs', 'aql', 'qalb'],
        content: `# Crisis Support: When to Seek Help

## Warning Signs
- Persistent thoughts of self-harm
- Complete loss of hope
- Inability to function daily
- Severe anxiety or depression

## Islamic Perspective
Islam values life as sacred and encourages seeking help when needed.

## Resources
- National Suicide Prevention Lifeline: 988
- Islamic counselors and scholars
- Local mental health professionals`,
      },
      {
        id: 'community-001',
        title: 'Building Community Connections',
        description: 'How to engage with the healing community and find peer support in your spiritual journey.',
        type: 'practice',
        category: 'community',
        duration: '15 min',
        difficulty: 'beginner',
        tags: ['community', 'peer-support', 'connection', 'social'],
        layerFocus: ['qalb', 'ruh'],
        content: `# Building Community Connections

## Steps to Connect
1. **Join local mosque activities**
2. **Participate in study circles**
3. **Volunteer for community service**
4. **Attend Islamic events and lectures**

## Benefits
- Shared spiritual growth
- Mutual support and encouragement
- Learning from others' experiences
- Building lasting friendships`,
      },
      {
        id: 'practice-001',
        title: 'Mindful Wudu Practice',
        description: 'Transform your ablution into a mindful healing practice following the Sunnah methodology.',
        type: 'practice',
        category: 'spiritual',
        duration: '12 min',
        difficulty: 'beginner',
        tags: ['wudu', 'mindfulness', 'purification', 'sunnah'],
        layerFocus: ['jism', 'qalb'],
        content: `# Mindful Wudu Practice

## Intention (Niyyah)
Begin with the intention to purify yourself for Allah's worship.

## Steps with Mindfulness
1. **Say Bismillah** - Be present with Allah's name
2. **Wash hands** - Feel the water cleansing impurities
3. **Rinse mouth** - Purify your speech
4. **Rinse nose** - Cleanse what you breathe
5. **Wash face** - Present your best self to Allah
6. **Wash arms** - Prepare for good deeds
7. **Wipe head** - Honor your thoughts
8. **Wash feet** - Walk on the righteous path

## Closing Du'a
أَشْهَدُ أَنْ لَا إِلَهَ إِلَّا اللَّهُ وَأَشْهَدُ أَنَّ مُحَمَّدًا رَسُولُ اللَّهِ`,
      },
      {
        id: 'healing-004',
        title: 'Ruqyah for Spiritual Protection',
        description: 'Authentic Quranic verses and supplications for spiritual healing and protection.',
        type: 'audio',
        category: 'healing',
        duration: '35 min',
        difficulty: 'intermediate',
        tags: ['ruqyah', 'protection', 'quran', 'spiritual-healing'],
        layerFocus: ['qalb', 'ruh'],
        url: 'https://example.com/audio/ruqyah-protection.mp3',
      },
      {
        id: 'educational-002',
        title: 'Islamic Psychology: Mind and Soul',
        description: 'Understanding mental health through the lens of Islamic scholarship and modern psychology.',
        type: 'video',
        category: 'educational',
        duration: '45 min',
        difficulty: 'advanced',
        tags: ['islamic-psychology', 'mental-health', 'scholarship', 'integration'],
        layerFocus: ['aql', 'qalb', 'ruh'],
        url: 'https://example.com/video/islamic-psychology.mp4',
      },
    ];

    // Filter by category
    let filtered = allResources;
    if (category && category !== 'all') {
      filtered = filtered.filter(resource => resource.category === category);
    }

    // Filter by type
    if (type && type !== 'all') {
      filtered = filtered.filter(resource => resource.type === type);
    }

    // Prioritize based on journey focus
    if (journey?.primaryLayer) {
      filtered = filtered.sort((a, b) => {
        const aHasFocus = a.layerFocus?.includes(journey.primaryLayer) ? 1 : 0;
        const bHasFocus = b.layerFocus?.includes(journey.primaryLayer) ? 1 : 0;
        return bHasFocus - aHasFocus; // Sort focused resources first
      });
    }

    return filtered;
  }

  /**
   * Get resource by ID
   */
  async getResourceById(resourceId: string, userId: string): Promise<Resource | null> {
    try {
      const resources = await this.getJourneyResources(userId);
      return resources.find(r => r.id === resourceId) || null;
    } catch (error) {
      logger.error('Error getting resource by ID', { resourceId, userId, error: error.message });
      throw new AppError('Failed to get resource', 500);
    }
  }

  /**
   * Search resources
   */
  async searchResources(
    userId: string,
    query: string,
    category?: string,
    type?: string
  ): Promise<Resource[]> {
    try {
      const resources = await this.getJourneyResources(userId, undefined, category, type);
      
      if (!query.trim()) {
        return resources;
      }

      const searchTerm = query.toLowerCase();
      return resources.filter(resource =>
        resource.title.toLowerCase().includes(searchTerm) ||
        resource.description.toLowerCase().includes(searchTerm) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    } catch (error) {
      logger.error('Error searching resources', { userId, query, error: error.message });
      throw new AppError('Failed to search resources', 500);
    }
  }
}

export const resourcesService = new ResourcesService();