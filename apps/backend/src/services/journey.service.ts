/**
 * Journey Service for Feature 2: Personalized Healing Journeys
 * AI-powered journey creation and management
 */

import { prisma, Prisma } from '../config/database';
import { aiService } from './ai.service';
import { assessmentService } from './assessment.service';
import { AppError } from '../middleware/errorHandler';
import {
  Journey,
  JourneyConfig,
  JourneyProgress,
  JourneyAnalytics,
  JourneyType,
  JourneyStatus,
  DailyPractice,
  JourneyDay,
} from '../models/JourneyTypes'; // Changed import path
import { logger } from '../utils/logger';

export class JourneyService {
  /**
   * Create a personalized healing journey based on assessment results
   */
  async createPersonalizedJourney(
    userId: string,
    assessmentId: string,
    preferences?: Partial<JourneyConfig>
  ): Promise<Journey> {
    logger.info('createPersonalizedJourney - ENTRY', {
      userId,
      assessmentId,
      preferences,
    });
    try {
      // Get assessment results
      const assessment = await prisma.spiritualDiagnosis.findFirst({
        where: {
          assessmentId: assessmentId,
          userId: userId,
        },
      });

      if (!assessment) {
        throw new AppError('Assessment not found', 404);
      }

      // Get user profile
      const userProfile = await this.getUserProfile(userId);

      // Generate journey parameters using AI
      const journeyParameters = await aiService.generateJourneyParameters({
        assessment: assessment.diagnosisData,
        userProfile,
        preferences: preferences || {},
      });

      // Create journey configuration
      const config: JourneyConfig = {
        duration: journeyParameters.duration,
        dailyTimeCommitment: journeyParameters.timeCommitment,
        primaryLayer: journeyParameters.primaryLayer,
        secondaryLayers: journeyParameters.secondaryLayers,
        ruqyaIntegrationLevel: journeyParameters.ruqyaLevel,
        communityIntegration: journeyParameters.communitySupport,
        professionalContext: userProfile.profession,
        culturalAdaptations: journeyParameters.culturalAdaptations,
        crisisSupport: assessment.diagnosisData.crisisLevel !== 'none',
      };

      // Generate journey content using AI
      const journeyContent = await aiService.generateJourneyContent({
        config,
        userProfile,
        assessment: assessment.diagnosisData,
      });

      // Save to database using Prisma
      const createdJourney = await prisma.journey.create({
        data: {
          userId,
          assessmentId,
          type: journeyParameters.type as JourneyType,
          status: 'created' as JourneyStatus,
          title: journeyContent.title,
          description: journeyContent.description,
          personalizedWelcome: journeyContent.personalizedWelcome,
          duration: config.duration,
          dailyTimeCommitment: config.dailyTimeCommitment,
          primaryLayer: config.primaryLayer as any,
          secondaryLayers: config.secondaryLayers as any[],
          ruqyaIntegrationLevel: config.ruqyaIntegrationLevel,
          communityIntegration: config.communityIntegration,
          professionalContext: config.professionalContext,
          culturalAdaptations: config.culturalAdaptations,
          crisisSupport: config.crisisSupport,
          currentDay: 1,
          completedDays: [],
          totalProgress: 0,
          userProfile: userProfile as Prisma.JsonObject,
          aiRecommendations: journeyParameters.recommendations,
          adaptiveAdjustments: [],
          crisisFlags: [],
          createdAt: new Date(),
        },
      });

      // Now, create JourneyDay and DailyPracticeInJourney records
      if (journeyContent.days && Array.isArray(journeyContent.days)) {
        for (const aiDay of journeyContent.days) {
          const createdJourneyDay = await prisma.journeyDay.create({
            data: {
              journeyId: createdJourney.id,
              dayNumber: aiDay.dayNumber,
              theme: aiDay.theme,
              learningObjective: aiDay.learningObjective,
              reflectionPrompts: aiDay.reflectionPrompts,
              communityActivity: aiDay.communityActivity,
              progressMilestone: aiDay.progressMilestone,
              adaptiveContent: (aiDay.adaptiveContent ||
                {}) as Prisma.JsonObject,
            },
          });

          if (aiDay.practices && Array.isArray(aiDay.practices)) {
            for (const [index, aiPractice] of aiDay.practices.entries()) {
              // Prepare data for DailyPracticeInJourney, including potential links
              const dailyPracticeData: Prisma.DailyPracticeInJourneyCreateInput =
                {
                  journeyDay: { connect: { id: createdJourneyDay.id } },
                  type: aiPractice.type as any, // Cast to Prisma enum PracticeType
                  title: aiPractice.title,
                  description: aiPractice.description,
                  duration: aiPractice.duration,
                  instructions: aiPractice.instructions || '',
                  arabicText: aiPractice.arabicText,
                  transliteration: aiPractice.transliteration,
                  translation: aiPractice.translation,
                  benefits: aiPractice.benefits,
                  layerFocus: aiPractice.layerFocus as any, // Cast to Prisma enum LayerFocus
                  difficultyLevel: aiPractice.difficultyLevel,
                  ruqyaComponent: aiPractice.ruqyaComponent,
                  professionalContext: aiPractice.professionalContext,
                  culturalNotes: aiPractice.culturalNotes,
                  orderIndex: index,
                  componentData: (aiPractice.componentDetails ||
                    {}) as Prisma.JsonObject,
                };

              // Add links to specific content tables if ID is provided in componentDetails
              const componentDetails = aiPractice.componentDetails || {};
              if (
                aiPractice.type === 'NameOfAllahSpotlight' &&
                componentDetails.originalContentId
              ) {
                const content = await prisma.nameOfAllahContent.findUnique({
                  where: { id: componentDetails.originalContentId },
                });
                if (content) {
                  dailyPracticeData.nameOfAllahContent = {
                    connect: { id: componentDetails.originalContentId },
                  };
                } else {
                  console.warn(
                    `NameOfAllahContent with ID ${componentDetails.originalContentId} not found for linking to practice "${aiPractice.title}".`
                  );
                }
              } else if (
                aiPractice.type === 'QuranicVerseReflection' &&
                componentDetails.originalContentId
              ) {
                const content = await prisma.quranicVerseContent.findUnique({
                  where: { id: componentDetails.originalContentId },
                });
                if (content) {
                  dailyPracticeData.quranicVerseContent = {
                    connect: { id: componentDetails.originalContentId },
                  };
                } else {
                  console.warn(
                    `QuranicVerseContent with ID ${componentDetails.originalContentId} not found for linking to practice "${aiPractice.title}".`
                  );
                }
              } else if (
                aiPractice.type === 'SunnahPractice' &&
                componentDetails.originalContentId
              ) {
                const content = await prisma.sunnahPracticeContent.findUnique({
                  where: { id: componentDetails.originalContentId },
                });
                if (content) {
                  dailyPracticeData.sunnahPracticeContent = {
                    connect: { id: componentDetails.originalContentId },
                  };
                } else {
                  console.warn(
                    `SunnahPracticeContent with ID ${componentDetails.originalContentId} not found for linking to practice "${aiPractice.title}".`
                  );
                }
              }

              await prisma.dailyPracticeInJourney.create({
                data: dailyPracticeData,
              });
            }
          }
        }
      }

      const journey = await prisma.journey.findUnique({
        where: { id: createdJourney.id },
        include: {
          journeyDays: { include: { dailyPracticesInJourney: true } },
        },
      });
      if (!journey)
        throw new AppError(
          'Failed to retrieve newly created journey with details',
          500
        );

      // Initialize community matching if enabled
      // Temporarily disabled to avoid UUID errors
      // if (config.communityIntegration) {
      //   await this.matchCommunitySupport(journey.id, userProfile);
      // }
      logger.info('createPersonalizedJourney - EXIT (Success)', {
        userId,
        journeyId: journey.id,
      });
      return journey;
    } catch (error) {
      logger.error('Error in createPersonalizedJourney - EXIT (Error):', {
        userId,
        assessmentId,
        preferences,
        error: error.message,
        stack: error.stack,
      });
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create personalized journey", 500);
    }
  }

  /**
   * Start a journey for a user
   */
  async startJourney(journeyId: string, userId: string): Promise<Journey> {
    logger.info('startJourney - ENTRY', { journeyId, userId });
    try {
      // First check if journey exists and belongs to user
      const existingJourney = await prisma.journey.findFirst({
        where: {
          id: journeyId,
          userId: userId,
        },
      });

      if (!existingJourney) {
        throw new AppError('Journey not found', 404);
      }

      if (existingJourney.userId !== userId) {
        throw new AppError('Access denied to this journey', 403);
      }

      const journey = await prisma.journey.update({
        where: {
          id: journeyId,
        },
        data: {
          status: 'active',
          startedAt: new Date(),
          lastActiveAt: new Date(),
        },
      });

      // Send welcome notification
      await this.sendJourneyWelcome(journey);
      logger.info('startJourney - EXIT (Success)', { journeyId, userId });
      return journey;
    } catch (error) {
      logger.error('Error in startJourney - EXIT (Error):', {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        throw new AppError('Journey to start not found', 404);
      }
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to start journey', 500);
    }
  }

  /**
   * Get current journey for user (lightweight - basic info only)
   */
  async getCurrentJourney(userId: string): Promise<Journey | null> {
    logger.info('getCurrentJourney - ENTRY', { userId });
    try {
      const journey = await prisma.journey.findFirst({
        where: {
          userId,
          status: {
            in: ['created', 'active', 'paused'],
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        // No includes - lightweight response for dashboard
      });

      logger.info('getCurrentJourney - EXIT (Success)', {
        userId,
        journeyId: journey?.id,
      });
      return (journey as any) || null; // Cast to any to satisfy Journey type temporarily
    } catch (error) {
      logger.error('Error in getCurrentJourney - EXIT (Error):', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw new AppError('Failed to retrieve current journey', 500); // Throw standard error
    }
  }

  /**
   * Get current journey with full details (heavy - includes all days and practices)
   */
  async getCurrentJourneyWithDetails(userId: string): Promise<Journey | null> {
    logger.info('getCurrentJourneyWithDetails - ENTRY', { userId });
    try {
      const journey = await prisma.journey.findFirst({
        where: {
          userId,
          status: {
            in: ['created', 'active', 'paused'],
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          journeyDays: {
            orderBy: { dayNumber: 'asc' },
            include: {
              dailyPracticesInJourney: {
                orderBy: { orderIndex: 'asc' },
                include: {
                  // Include linked content
                  nameOfAllahContent: true,
                  quranicVerseContent: true,
                  sunnahPracticeContent: true,
                },
              },
            },
          },
        },
      });

      logger.info('getCurrentJourneyWithDetails - EXIT (Success)', {
        userId,
        journeyId: journey?.id,
      });
      return (journey as any) || null; // Cast to any to satisfy Journey type temporarily
    } catch (error) {
      logger.error('Error in getCurrentJourneyWithDetails - EXIT (Error):', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw new AppError('Failed to retrieve current journey with details', 500);
    }
  }

  /**
   * Get journey by ID (lightweight - basic journey info only)
   */
  async getJourneyById(
    journeyId: string,
    userId: string
  ): Promise<Journey | null> {
    logger.info('getJourneyById - ENTRY', { journeyId, userId });
    try {
      const journey = await prisma.journey.findFirst({
        where: {
          id: journeyId,
          userId: userId,
        },
        // No includes - lightweight response
      });

      logger.info('getJourneyById - EXIT (Success)', { journeyId, userId });
      return (journey as any) || null; // Cast to any to satisfy Journey type temporarily
    } catch (error) {
      logger.error('Error in getJourneyById - EXIT (Error):', {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        // Or if findUnique returns null
        throw new AppError('Journey not found', 404);
      }
      throw new AppError('Failed to retrieve journey by ID', 500);
    }
  }

  /**
   * Get journey with full details including days and practices
   */
  async getJourneyWithDetails(
    journeyId: string,
    userId: string
  ): Promise<Journey | null> {
    logger.info('getJourneyWithDetails - ENTRY', { journeyId, userId });
    try {
      const journey = await prisma.journey.findFirst({
        where: {
          id: journeyId,
          userId: userId,
        },
        include: {
          journeyDays: {
            orderBy: { dayNumber: 'asc' },
            include: {
              dailyPracticesInJourney: {
                orderBy: { orderIndex: 'asc' },
                include: {
                  nameOfAllahContent: true,
                  quranicVerseContent: true,
                  sunnahPracticeContent: true,
                },
              },
            },
          },
        },
      });

      logger.info('getJourneyWithDetails - EXIT (Success)', { journeyId, userId });
      return (journey as any) || null; // Cast to any to satisfy Journey type temporarily
    } catch (error) {
      logger.error('Error in getJourneyWithDetails - EXIT (Error):', {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw new AppError('Failed to retrieve journey details', 500);
    }
  }

  /**
   * Get journey progress with analytics (heavy endpoint)
   */
  async getJourneyProgress(journeyId: string): Promise<{
    journey: Journey;
    progressHistory: any[];
    summary: {
      totalDays: number;
      averageRating: number;
      completionRate: number;
    };
  }> {
    logger.info('getJourneyProgress - ENTRY', { journeyId });
    try {
      // Get basic journey info
      const journey = await prisma.journey.findUnique({
        where: { id: journeyId },
      });

      if (!journey) {
        throw new AppError('Journey not found', 404);
      }

      // Get progress history
      const progressHistory = await prisma.journeyProgress.findMany({
        where: { journeyId },
        orderBy: { date: 'asc' },
      });

      // Calculate summary
      const totalDays = progressHistory.length;
      const averageRating = totalDays > 0 
        ? progressHistory.reduce((sum, p) => sum + (p.overallRating || 0), 0) / totalDays
        : 0;
      const completionRate = journey.totalProgress ? Number(journey.totalProgress) : 0;

      logger.info('getJourneyProgress - EXIT (Success)', { journeyId, totalDays });
      return {
        journey: journey as any,
        progressHistory,
        summary: {
          totalDays,
          averageRating,
          completionRate,
        },
      };
    } catch (error) {
      logger.error('Error in getJourneyProgress - EXIT (Error):', {
        journeyId,
        error: error.message,
        stack: error.stack,
      });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve journey progress', 500);
    }
  }

  /**
   * Record daily progress
   */
  async recordDailyProgress(
    journeyId: string,
    userId: string,
    progress: Omit<JourneyProgress, 'id' | 'createdAt' | 'updatedAt'> // This type is from models/JourneyTypes.ts (local)
    // Should ideally align with shared-types JourneyProgress
  ): Promise<any> {
    // Return type should be Prisma.JourneyProgress eventually
    logger.info('recordDailyProgress - ENTRY', {
      journeyId,
      userId,
      dayNumber: progress.dayNumber,
    });
    try {
      // Ensure progress data aligns with Prisma schema and shared types
      const dataToSave: Prisma.JourneyProgressCreateInput = {
        journey: { connect: { id: journeyId } },
        user: { connect: { id: userId } },
        dayNumber: progress.dayNumber,
        date: progress.date ? new Date(progress.date) : new Date(), // Ensure date is a Date object
        practicesCompleted: (progress.practicesCompleted ||
          []) as Prisma.JsonArray,
        overallRating: progress.overallRating,
        moodBefore: progress.moodBefore,
        moodAfter: progress.moodAfter,
        energyLevelBefore: progress.energyLevelBefore, // New field
        spiritualStateBefore: progress.spiritualStateBefore, // New field
        dailyIntention: progress.dailyIntention, // New field
        spiritualConnection: progress.spiritualConnection,
        stressLevel: progress.stressLevel,
        dailyReflection: progress.dailyReflection,
        gratitude: progress.gratitude || [],
        challenges: progress.challenges || [],
        insights: progress.insights || [],
        communityParticipation: progress.communityParticipation,
        communityContribution: progress.communityContribution,
        peerSupport: progress.peerSupport, // Ensure this is in the input `progress` type
        contentRelevance: progress.contentRelevance,
        practiceEffectiveness: progress.practiceEffectiveness,
        timeAppropriate: progress.timeAppropriate,
        suggestedAdjustments: progress.suggestedAdjustments,
        // Prisma handles createdAt and updatedAt automatically
      };

      const savedProgress = await prisma.journeyProgress.create({
        data: dataToSave,
      });

      // Update journey aggregate progress (refactored to use Prisma)
      await this._updateJourneyAggregateProgress(journeyId, progress.dayNumber);

      // Check for crisis indicators using the saved Prisma object
      // Assuming JourneyProgress model type is compatible enough for checkCrisisIndicators
      await this.checkCrisisIndicators(journeyId, userId, savedProgress as any);

      // Generate adaptive recommendations using the saved Prisma object
      await this.generateAdaptiveRecommendations(
        journeyId,
        userId,
        savedProgress as any
      );
      logger.info('recordDailyProgress - EXIT (Success)', {
        journeyId,
        userId,
        dayNumber: progress.dayNumber,
        progressId: savedProgress.id,
      });
      return savedProgress;
    } catch (error) {
      logger.error('Error in recordDailyProgress - EXIT (Error):', {
        journeyId,
        userId,
        dayNumber: progress.dayNumber,
        error: error.message,
        stack: error.stack,
      });
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          // Foreign key constraint failed
          throw new AppError(
            'Invalid journeyId or userId for recording progress.',
            400
          );
        }
      }
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to record daily progress', 500);
    }
  }

  /**
   * Internal method to update the main journey's progress fields.
   * This is called after a daily progress record has been made.
   */
  private async _updateJourneyAggregateProgress(
    journeyId: string,
    completedDayNumber: number
  ): Promise<void> {
    try {
      // Get current journey state using Prisma
      const journey = await prisma.journey.findUnique({
        where: { id: journeyId },
        select: {
          completedDays: true,
          duration: true, // Assuming 'duration' field holds total days from 'configuration'
          currentDay: true,
          status: true,
        },
      });

      if (!journey) {
        throw new AppError(
          `Journey ${journeyId} not found for progress update`,
          404
        );
      }

      const currentCompletedDays = (journey.completedDays as number[]) || [];
      const newCompletedDaysSet = new Set(currentCompletedDays);
      newCompletedDaysSet.add(completedDayNumber);
      const updatedCompletedDays = Array.from(newCompletedDaysSet).sort(
        (a, b) => a - b
      );

      const totalDuration = journey.duration || 30; // Default if duration is not set
      const totalProgress = Math.min(
        (updatedCompletedDays.length / totalDuration) * 100,
        100
      );

      let nextDay = journey.currentDay || 1;
      if (updatedCompletedDays.includes(nextDay)) {
        if (updatedCompletedDays.length === totalDuration) {
          nextDay = totalDuration + 1; // Mark as beyond duration if all complete
        } else {
          // Find the smallest day number not in completedDays
          let i = 1;
          while (i <= totalDuration) {
            if (!updatedCompletedDays.includes(i)) {
              nextDay = i;
              break;
            }
            i++;
          }
          if (i > totalDuration) nextDay = totalDuration + 1; // All days somehow completed
        }
      }

      await prisma.journey.update({
        where: { id: journeyId },
        data: {
          completedDays: updatedCompletedDays,
          totalProgress: new Prisma.Decimal(totalProgress.toFixed(2)), // Ensure Decimal type for Prisma
          currentDay: nextDay,
          lastActiveAt: new Date(),
        },
      });

      if (totalProgress >= 100 && journey.status !== 'completed' as JourneyStatus) {
        await this.completeJourney(journeyId);
      }
    } catch (error) {
      logger.error(
        `Error in _updateJourneyAggregateProgress (Prisma) for ${journeyId}:`,
        { error: error.message, stack: error.stack }
      );
      // Avoid re-throwing if it's an AppError from completeJourney or fetch
      if (!(error instanceof AppError)) {
        // This path should ideally not be hit if errors from completeJourney are AppErrors
        throw new AppError(
          'An unexpected error occurred while updating aggregate journey progress',
          500
        );
      } else {
        // Re-throw AppErrors (e.g., from a failed findUnique)
        throw error;
      }
    }
  }

  /**
   * Complete a journey
   */
  private async completeJourney(journeyId: string): Promise<void> {
    try {
      await prisma.journey.update({
        where: {
          id: journeyId,
        },
        data: {
          status: 'completed' as JourneyStatus, // Use enum
          completedAt: new Date(), // Prisma handles ISOString conversion
        },
      });

      // Generate completion analytics
      await this.generateCompletionAnalytics(journeyId);

      // Send completion celebration
      await this.sendCompletionCelebration(journeyId);
    } catch (error) {
      logger.error('Error in completeJourney (Prisma):', {
        journeyId,
        error: error.message,
        stack: error.stack,
      });
      // Do not re-throw here as this is a private method often called from within a try-catch
      // However, ensure AppError is used if this method were to be called from a context expecting it.
      // For now, if prisma.journey.update fails, it will throw and be caught by the caller.
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        logger.warn(
          `Attempted to complete a non-existent journey: ${journeyId}`
        );
        // No need to throw AppError here as the caller might handle it, or it's an internal cleanup.
      }
    }
  }

  /**
   * Check for crisis indicators in progress
   */
  private async checkCrisisIndicators(
    journeyId: string,
    userId: string,
    progress: JourneyProgress
  ): Promise<void> {
    try {
      // Analyze progress for crisis indicators
      const crisisAnalysis = await aiService.analyzeCrisisIndicators({
        userId,
        journeyId,
        progress,
        context: 'journey_progress',
      });

      if (crisisAnalysis.level !== 'none') {
        // Record crisis flag
        const crisisFlag = {
          date: new Date().toISOString(),
          level: crisisAnalysis.level,
          indicators: crisisAnalysis.indicators,
          response: crisisAnalysis.recommendedActions.join(', '),
        };

        await prisma.journey.update({
          where: {
            id: journeyId,
          },
          data: {
            crisisFlags: {
              push: crisisFlag,
            },
          },
        });

        // Trigger crisis response if needed
        if (['high', 'critical'].includes(crisisAnalysis.level)) {
          await this.triggerCrisisResponse(journeyId, userId, crisisAnalysis);
        }
      }
    } catch (error) {
      logger.error('Error in checkCrisisIndicators:', {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
    }
  }

  /**
   * Generate adaptive recommendations
   */
  private async generateAdaptiveRecommendations(
    journeyId: string,
    userId: string,
    progress: JourneyProgress
  ): Promise<void> {
    try {
      const recommendations = await aiService.generateAdaptiveRecommendations({
        userId,
        journeyId,
        progress,
        context: 'daily_progress',
      });

      if (recommendations.length > 0) {
        await prisma.journey.update({
          where: {
            id: journeyId,
          },
          data: {
            // Assuming aiRecommendations from AI are general string recommendations
            aiRecommendations: recommendations.recommendations,
            // Storing structured adjustments from AI
            adaptiveAdjustments: {
              // Prisma expects a JsonValue; if recommendations.adjustments is already JsonArray, direct assignment is fine
              push: recommendations.adjustments, // Use push if you want to append to existing adjustments array
            } as Prisma.JsonArray, // Cast if needed, ensure it's a JsonArray
          },
        });
      }
    } catch (error) {
      console.error(
        'Error generating/storing adaptive recommendations:',
        error
      );
    }
  }

  /**
   * Get user profile for journey creation
   */
  private async getUserProfile(userId: string): Promise<any> {
    try {
      const userProfileDetailed = await prisma.userProfileDetailed.findUnique({
        where: { userId },
        // Select specific fields if needed, or remove select to get all fields
        // select: { profileData: true } // Example: if profile data is in a JSON field
      });

      if (!userProfileDetailed || !userProfileDetailed.profileData) {
        // Return default profile if not found or profileData is null
        // This default structure should align with what the AI service expects
        return {
          userId: userId, // Keep original snake_case if AI service expects that for this specific field
          profession: 'other', // Example field, adjust to actual expected structure
          // Default fields based on the original Supabase version:
          awareness_level: 'beginner',
          ruqya_familiarity: 'none',
          cultural_background: 'other',
          time_availability: '15-30 minutes',
          learning_style: 'visual',
          // Ensure all fields expected by aiService.generateJourneyParameters are present
        };
      }
      // Assuming profileData is a JSON field containing the profile information
      return { userId, ...(userProfileDetailed.profileData as any) };
    } catch (error) {
      console.error('Error getting user profile with Prisma:', error);
      // Fallback to default profile in case of any error to maintain behavior
      return {
        userId: userId,
        profession: 'other',
        awareness_level: 'beginner',
        ruqya_familiarity: 'none',
        cultural_background: 'other',
        time_availability: '15-30 minutes',
        learning_style: 'visual',
      };
    }
  }

  /**
   * Match community support for user
   */
  private async matchCommunitySupport(
    journeyId: string,
    userProfile: any
  ): Promise<void> {
    try {
      const communityMatch = await aiService.matchCommunitySupport({
        userProfile,
        journeyId,
      });

      if (communityMatch.groupId) {
        await prisma.journey.update({
          where: {
            id: journeyId,
          },
          data: {
            communityGroupId: communityMatch.groupId, // Ensure field name matches Prisma schema
            mentorId: communityMatch.mentorId,
            peerConnections: communityMatch.peerConnections,
          },
        });
      }
    } catch (error) {
      console.error('Error matching community support:', error);
    }
  }

  /**
   * Send journey welcome message
   */
  private async sendJourneyWelcome(journey: Journey): Promise<void> {
    // TODO: Implement actual notification service integration (e.g., email, push notification)
    console.log(
      `[JourneyService] INFO: Sending welcome for journey: "${journey.title}" (ID: ${journey.id}) to user ${journey.userId}.`
    );
    // Example: await notificationService.sendWelcomeEmail(journey.userId, journey.title);
  }

  /**
   * Trigger crisis response
   */
  private async triggerCrisisResponse(
    journeyId: string,
    userId: string,
    crisisAnalysis: any // Consider defining a type for crisisAnalysis
  ): Promise<void> {
    // TODO: Implement actual crisis response protocol (e.g., notify admin, provide emergency resources)
    console.warn(
      `[JourneyService] CRITICAL: Triggering crisis response for journey ID: ${journeyId}, User ID: ${userId}. Analysis:`,
      crisisAnalysis
    );
    // Example: await emergencyService.handleCrisis(userId, journeyId, crisisAnalysis);
    // Example: await adminNotificationService.alertAdminCrisis(userId, journeyId, crisisAnalysis.level);
  }

  /**
   * Generate completion analytics
   */
  private async generateCompletionAnalytics(journeyId: string): Promise<void> {
    // TODO: Implement actual analytics generation and storage
    console.log(
      `[JourneyService] INFO: Generating completion analytics for journey ID: ${journeyId}.`
    );
    // Example:
    // const analyticsData = await this.calculateJourneyAnalytics(journeyId);
    // await analyticsStorageService.saveAnalytics(journeyId, analyticsData);
    // For now, we can assume this might be part of getJourneyAnalytics or a separate detailed process.
  }

  /**
   * Send completion celebration
   */
  private async sendCompletionCelebration(journeyId: string): Promise<void> {
    // TODO: Implement actual celebration notification (e.g., in-app message, email)
    console.log(
      `[JourneyService] INFO: Sending completion celebration for journey ID: ${journeyId}.`
    );
    // Example: await notificationService.sendJourneyCompletionCongrats(userId, journeyId);
  }

  /**
   * Update journey progress for a specific day (Refactored to Prisma)
   */
  async updateJourneyProgress(
    // This is the public method
    journeyId: string,
    userId: string, // Added userId for consistency and to create progress record
    dayNumber: number,
    practiceResults: any // Consider defining a type for practiceResults
  ): Promise<any> {
    try {
      const journey = await prisma.journey.findUnique({
        where: { id: journeyId },
        // select: { userId: true, completedDays: true, duration: true }, // Select only necessary fields
      });

      if (!journey) {
        throw new AppError('Journey not found', 404);
      }

      if (!journey.userId) {
        throw new AppError('Journey is missing userId', 500);
      }

      // Create progress record
      // Assuming JourneyProgress model has fields: journeyId, userId, dayNumber, date, practicesCompleted
      const progressRecord = await prisma.journeyProgress.create({
        data: {
          journeyId: journeyId,
          userId: journey.userId, // Get userId from the journey object
          dayNumber: dayNumber,
          date: new Date(), // Store as DateTime
          practicesCompleted: [practiceResults] as any[], // Cast to any[] if type is Json
          // createdAt and updatedAt are usually handled by Prisma @default(now()) and @updatedAt
        },
      });

      // Update journey completed days
      const currentCompletedDays = (journey.completedDays as number[]) || [];
      const newCompletedDays = [...currentCompletedDays];
      if (!newCompletedDays.includes(dayNumber)) {
        newCompletedDays.push(dayNumber);
      }

      const journeyDuration =
        typeof journey.duration === 'number' ? journey.duration : 0;
      const totalProgress =
        journeyDuration > 0
          ? (newCompletedDays.length / journeyDuration) * 100
          : 0;

      await prisma.journey.update({
        where: { id: journeyId },
        data: {
          completedDays: newCompletedDays,
          totalProgress: totalProgress,
          currentDay:
            newCompletedDays.length > 0 ? Math.max(...newCompletedDays) + 1 : 1,
          lastActiveAt: new Date(),
        },
      });

      return {
        dayNumber,
        completed: true,
        progress: totalProgress,
        practiceResults, // Contains the original practiceResults
        progressId: progressRecord.id, // Return ID of the created progress record
      };
    } catch (error) {
      console.error('Error updating journey progress with Prisma:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to update journey progress', 500);
    }
  }

  /**
   * Get all journeys for a user (Refactored to Prisma)
   */
  async getUserJourneys(userId: string): Promise<Journey[]> {
    logger.info('getUserJourneys - ENTRY', { userId });
    try {
      const journeys = await prisma.journey.findMany({
        where: { userId: userId },
        orderBy: { createdAt: 'desc' },
        // Select a subset of fields for list view, or include basic relations if needed
        // For now, fetching main journey fields. Details can be fetched on demand.
        select: {
          id: true,
          title: true,
          type: true,
          status: true,
          duration: true,
          currentDay: true,
          totalProgress: true,
          createdAt: true,
          startedAt: true,
          completedAt: true,
          primaryLayer: true,
        },
      });
      // The return type Journey[] from local models/JourneyTypes.ts might need mapping
      // if Prisma's Journey model structure is different or if shared-types is the target.
      // Casting to 'any' then 'Journey[]' to bypass strict code checking for now.
      logger.info('getUserJourneys - EXIT (Success)', {
        userId,
        count: journeys.length,
      });
      return journeys as any as Journey[];
    } catch (error) {
      logger.error('Error in getUserJourneys - EXIT (Error):', {
        userId,
        error: error.message,
        stack: error.stack,
      });
      throw new AppError('Failed to get user journeys', 500);
    }
  }

  /**
   * Get or generate analytics for a journey.
   */
  async getJourneyAnalytics(
    journeyId: string,
    userId: string
  ): Promise<JourneyAnalytics | null> {
    try {
      // Fetch the journey
      const journey = await prisma.journey.findUnique({
        where: {
          id_userId: {
            id: journeyId,
            userId: userId,
          },
        },
        include: {
          // Include related models if necessary for analytics, e.g., progress records
          journeyProgress: true, // Assuming you have a relation defined in Prisma schema
        },
      });

      if (!journey) {
        throw new AppError('Journey not found for analytics', 404);
      }

      const totalDuration = journey.duration || 30;
      const completedDaysCount = journey.completedDays?.length || 0;
      const completionRate = (completedDaysCount / totalDuration) * 100;

      let averageDailyRating = 0;
      let practiceAdherence = 0; // Placeholder for more complex calculation

      if (journey.journeyProgress && journey.journeyProgress.length > 0) {
        const ratedRecords = journey.journeyProgress.filter(
          (p) => p.overallRating != null
        );
        if (ratedRecords.length > 0) {
          averageDailyRating =
            ratedRecords.reduce((sum, p) => sum + (p.overallRating || 0), 0) /
            ratedRecords.length;
        }
        // Basic practice adherence: % of days with any progress record
        const daysWithProgress = new Set(
          journey.journeyProgress.map((p) => p.dayNumber)
        ).size;
        practiceAdherence = (daysWithProgress / totalDuration) * 100;
      }

      // This is a MOCK implementation for analytics.
      // Real implementation would involve more complex calculations and data aggregation.
      // Ensure calculations use data from 'journey.journeyProgress' which is now included.

      // Calculate streak (basic example: consecutive days with progress)
      let streakDays = 0;
      if (journey.journeyProgress && journey.journeyProgress.length > 0) {
        const progressDates = journey.journeyProgress
          .map((p) => new Date(p.date).toDateString()) // Get date strings
          .filter((value, index, self) => self.indexOf(value) === index) // Unique dates
          .sort((a, b) => new Date(b).getTime() - new Date(a).getTime()); // Sort descending

        if (progressDates.length > 0) {
          streakDays = 1;
          let lastDate = new Date(progressDates[0]);
          for (let i = 1; i < progressDates.length; i++) {
            const currentDate = new Date(progressDates[i]);
            const diffTime = lastDate.getTime() - currentDate.getTime();
            const diffDays = diffTime / (1000 * 60 * 60 * 24);
            if (diffDays === 1) {
              streakDays++;
              lastDate = currentDate;
            } else {
              break; // Streak broken
            }
          }
        }
      }

      return {
        streak: streakDays,
        completedDays: journey.journeyProgress?.length || 0,
        averageRating:
          journey.journeyProgress?.length > 0
            ? journey.journeyProgress.reduce(
                (sum, p) => sum + (p.overallRating || 0),
                0
              ) / journey.journeyProgress.length
            : 0,
      };
    } catch (error) {
      logger.error('Error loading journey stats', { journeyId, userId, error });
      throw error;
    }
  }

  /**
   * Get all progress records for a journey, ordered by date.
   */
  async getJourneyProgressHistory(
    journeyId: string,
    userId: string,
    limit?: number,
    offset?: number
  ): Promise<JourneyProgressType[]> {
    // Using JourneyProgressType from local models for now
    logger.info('getJourneyProgressHistory - ENTRY', {
      journeyId,
      userId,
      limit,
      offset,
    });
    try {
      // First, verify the journey exists and belongs to the user to ensure authorization
      const journey = await prisma.journey.findFirst({
        where: { id: journeyId, userId: userId },
        select: { id: true }, // Select minimal fields just for existence check
      });

      if (!journey) {
        throw new AppError('Journey not found or access denied.', 404);
      }

      const progressRecords = await prisma.journeyProgress.findMany({
        where: {
          journeyId: journeyId,
          // userId: userId, // Implicitly handled by checking journey ownership above
        },
        orderBy: {
          date: 'asc', // Or dayNumber: 'asc'
        },
        take: limit,
        skip: offset,
      });
      // Cast to local JourneyProgressType. This might need more careful mapping if Prisma types diverge significantly.
      logger.info('getJourneyProgressHistory - EXIT (Success)', {
        journeyId,
        userId,
        count: progressRecords.length,
      });
      return progressRecords as any as JourneyProgressType[];
    } catch (error) {
      logger.error(`Error in getJourneyProgressHistory - EXIT (Error):`, {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve journey progress history', 500);
    }
  }

  /**
   * Get all completed milestones for a specific journey.
   */
  async getJourneyCompletedMilestones(
    journeyId: string,
    userId: string
  ): Promise<any[]> {
    // Define a proper return type later
    logger.info('getJourneyCompletedMilestones - ENTRY', { journeyId, userId });
    try {
      // Verify journey ownership first
      const journey = await prisma.journey.findFirst({
        where: { id: journeyId, userId: userId },
        select: { id: true },
      });

      if (!journey) {
        throw new AppError('Journey not found or access denied.', 404);
      }

      const completedMilestones = await prisma.milestoneCompletion.findMany({
        where: {
          userId: userId,
          // We need to filter by journeyId. The MilestoneCompletion model has milestoneId,
          // and JourneyMilestone has journeyId. So we need to use the relation.
          journeyMilestone: {
            journeyId: journeyId,
          },
        },
        include: {
          journeyMilestone: {
            // Include details from JourneyMilestone
            select: {
              id: true,
              name: true,
              description: true,
              milestoneType: true,
              sequenceOrder: true,
            },
          },
        },
        orderBy: {
          completionDate: 'desc', // Show most recent first
        },
      });

      // Format the data for the client
      return completedMilestones.map((mc) => ({
        milestoneCompletionId: mc.id,
        milestoneId: mc.journeyMilestone.id,
        name: mc.journeyMilestone.name,
        description: mc.journeyMilestone.description,
        type: mc.journeyMilestone.milestoneType,
        sequence: mc.journeyMilestone.sequenceOrder,
        completionDate: mc.completionDate.toISOString(),
        reflection: mc.reflection,
      }));
      logger.info('getJourneyCompletedMilestones - EXIT (Success)', {
        journeyId,
        userId,
        count: formattedMilestones.length,
      });
      return completedMilestones.map((mc) => ({
        milestoneCompletionId: mc.id,
        milestoneId: mc.journeyMilestone.id,
        name: mc.journeyMilestone.name,
        description: mc.journeyMilestone.description,
        type: mc.journeyMilestone.milestoneType,
        sequence: mc.journeyMilestone.sequenceOrder,
        completionDate: mc.completionDate.toISOString(),
        reflection: mc.reflection,
      }));
    } catch (error) {
      logger.error(`Error in getJourneyCompletedMilestones - EXIT (Error):`, {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve journey milestones', 500);
    }
  }

  /**
   * Generate comprehensive analytics for a journey
   */
  async generateJourneyAnalytics(
    journeyId: string,
    userId: string
  ): Promise<any> {
    logger.info('generateJourneyAnalytics - ENTRY', { journeyId, userId });
    try {
      // Fetch journey with progress data
      const journey = await prisma.journey.findFirst({
        where: { id: journeyId, userId: userId },
        include: {
          journeyProgress: true,
          journeyDays: {
            include: {
              dailyPracticesInJourney: true,
            },
          },
        },
      });

      if (!journey) {
        throw new AppError('Journey not found or access denied.', 404);
      }

      // Calculate basic metrics
      const totalDays = journey.duration || 0;
      const completedDaysCount = journey.journeyProgress?.length || 0;
      const completionRate =
        totalDays > 0 ? (completedDaysCount / totalDays) * 100 : 0;

      const averageDailyRating =
        journey.journeyProgress?.length > 0
          ? journey.journeyProgress.reduce(
              (sum, p) => sum + (p.overallRating || 0),
              0
            ) / journey.journeyProgress.length
          : 0;

      const totalPractices =
        journey.journeyDays?.reduce(
          (sum, day) => sum + (day.dailyPracticesInJourney?.length || 0),
          0
        ) || 0;
      const completedPractices =
        journey.journeyProgress?.reduce((sum, progress) => {
          const practicesCompleted =
            (progress.practicesCompleted as any[])?.filter((p) => p.completed)
              ?.length || 0;
          return sum + practicesCompleted;
        }, 0) || 0;
      const practiceAdherence =
        totalPractices > 0 ? (completedPractices / totalPractices) * 100 : 0;

      // Calculate streak
      let streakDays = 0;
      if (journey.journeyProgress && journey.journeyProgress.length > 0) {
        const progressDates = journey.journeyProgress
          .map((p) => new Date(p.date).toDateString())
          .filter((value, index, self) => self.indexOf(value) === index)
          .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

        if (progressDates.length > 0) {
          streakDays = 1;
          let lastDate = new Date(progressDates[0]);
          for (let i = 1; i < progressDates.length; i++) {
            const currentDate = new Date(progressDates[i]);
            const diffTime = lastDate.getTime() - currentDate.getTime();
            const diffDays = diffTime / (1000 * 60 * 60 * 24);
            if (diffDays === 1) {
              streakDays++;
              lastDate = currentDate;
            } else {
              break; // Streak broken
            }
          }
        }
      }

      // Helper function to derive color from progress
      const getProgressColor = (progress: number): string => {
        if (progress < 34) return '#F44336'; // Red
        if (progress < 67) return '#FFC107'; // Yellow
        return '#4CAF50'; // Green
      };

      const layerKeys: ('jism' | 'nafs' | 'aql' | 'qalb' | 'ruh')[] = [
        'jism',
        'nafs',
        'aql',
        'qalb',
        'ruh',
      ];
      const calculatedLayerProgress = {} as any;

      // Fetch SpiritualDiagnosis for baseline scores
      let diagnosis = null;
      if (journey.assessmentId) {
        diagnosis = await prisma.spiritualDiagnosis.findUnique({
          where: { id: journey.assessmentId }, // This assumes Journey.assessmentId is the SpiritualDiagnosis.id
          // If Journey.assessmentId refers to AssessmentSession.id, then need another query
          // For now, assuming Journey.assessmentId directly links to SpiritualDiagnosis.id for simplicity
          // Or that SpiritualDiagnosis.assessmentId (FK to AssessmentSession) is the one stored in Journey.assessmentId,
        });
      }

      // Adjusting the assumption: Journey.assessmentId is the ID of the AssessmentSession.
      // We need to find the SpiritualDiagnosis linked to this AssessmentSession.
      let spiritualDiagnosis = null;
      if (journey.assessmentId) {
        spiritualDiagnosis = await prisma.spiritualDiagnosis.findFirst({
          where: { assessmentId: journey.assessmentId, userId: userId }, // Find diagnosis for this user and session
          orderBy: { generatedAt: 'desc' }, // Get the latest if multiple
        });
      }

      const diagnosisData = spiritualDiagnosis?.diagnosisData as any; // Cast for easier access
      
      // Debug logging for diagnosis data
      console.log('🔍 Diagnosis data for layer calculation:', {
        hasSpiritual: !!spiritualDiagnosis,
        primaryLayer: journey.primaryLayer,
        diagnosisData: {
          primaryLayerAffected: diagnosisData?.primaryLayerAffected,
          secondaryLayersAffected: diagnosisData?.secondaryLayersAffected,
          overallSeverityScore: diagnosisData?.overallSeverityScore,
          layerHealthScores: diagnosisData?.layerHealthScores
        }
      });

      for (const layer of layerKeys) {
        // 1. Baseline Score - More realistic starting values
        let baselineScore = 50; // Default baseline
        
        if (
          diagnosisData?.layerHealthScores &&
          typeof diagnosisData.layerHealthScores[layer] === 'number'
        ) {
          baselineScore = diagnosisData.layerHealthScores[layer];
        } else if (
          diagnosisData?.primaryLayerAffected === layer &&
          typeof diagnosisData.overallSeverityScore === 'number'
        ) {
          // Primary affected layer starts lower
          baselineScore = Math.max(20, 70 - diagnosisData.overallSeverityScore * 8);
        } else if (
          diagnosisData?.secondaryLayersAffected?.includes(layer) &&
          typeof diagnosisData.overallSeverityScore === 'number'
        ) {
          // Secondary affected layers start moderately low
          baselineScore = Math.max(30, 80 - diagnosisData.overallSeverityScore * 5);
        } else {
          // Unaffected layers start higher but not perfect
          baselineScore = Math.max(60, 85 - (diagnosisData?.overallSeverityScore || 5) * 2);
        }
        
        // Add some variation based on journey focus
        if (layer === journey.primaryLayer) {
          baselineScore = Math.max(baselineScore - 10, 15); // Primary focus starts lower for more growth potential
        }
        
        baselineScore = Math.max(0, Math.min(100, baselineScore)); // Clamp

        // 2. Max Journey Impact & 3. Earned Journey Impact
        let maxJourneyImpact = 0;
        let earnedJourneyImpact = 0;

        const allPlannedPractices =
          await prisma.dailyPracticeInJourney.findMany({
            where: {
              journeyDay: { journeyId: journeyId, journey: { userId: userId } },
              layerFocus: layer,
            },
            select: { id: true, type: true }, // Only need ID and type for this part
          });
        maxJourneyImpact = allPlannedPractices.length * 10; // Base 10 points per practice
        if (layer === journey.primaryLayer) {
          maxJourneyImpact += allPlannedPractices.length * 5; // Bonus 5 for primary layer
        }
        // Add potential for mood/reflection bonuses to max impact
        maxJourneyImpact += (journey.duration || 0) * 2; // e.g., max 2 points per day from mood/reflection for this layer

        // Iterate through completed days' progress
        for (const dayProgress of journey.journeyProgress) {
          // Practice Completion Impact
          const completedPracticesForLayerThisDay = (
            (dayProgress.practicesCompleted as Array<{
              practiceId: string;
              completed?: boolean;
            }>) || []
          )
            .filter((pc) => pc.completed)
            .map((pc) =>
              allPlannedPractices.find(
                (p) => p.id === pc.practiceId && p.layerFocus === layer
              )
            )
            .filter(Boolean);

          earnedJourneyImpact += completedPracticesForLayerThisDay.length * 10;
          if (layer === journey.primaryLayer) {
            earnedJourneyImpact += completedPracticesForLayerThisDay.length * 5;
          }

          // Mood Impact (Simplified: for Qalb and Nafs)
          if (
            (layer === 'qalb' || layer === 'nafs') &&
            dayProgress.moodBefore != null &&
            dayProgress.moodAfter != null
          ) {
            const moodDiff = dayProgress.moodAfter - dayProgress.moodBefore;
            if (moodDiff > 2) earnedJourneyImpact += 2;
            else if (moodDiff < -1 && layer === 'nafs')
              earnedJourneyImpact -= 1; // Small penalty for Nafs if mood worsens
          }
          // Spiritual Connection (Ruh, Qalb)
          if (
            (layer === 'ruh' || layer === 'qalb') &&
            dayProgress.spiritualConnection != null &&
            dayProgress.spiritualConnection > 7
          ) {
            earnedJourneyImpact += 2;
          }
          // Reflection (Aql)
          if (
            layer === 'aql' &&
            dayProgress.dailyReflection &&
            dayProgress.dailyReflection.trim().length > 10
          ) {
            // Min length for reflection
            earnedJourneyImpact += 1;
          }
        }

        // 4. Normalized Journey Progress
        const progressRatio =
          maxJourneyImpact > 0 ? earnedJourneyImpact / maxJourneyImpact : 0;
        const normalizedJourneyProgress = Math.max(
          0,
          Math.min(100, progressRatio * 100)
        );

        // 5. Final Progress Value
        const improvementTarget = 100 - baselineScore;
        const progressAchievedInJourney =
          (normalizedJourneyProgress / 100) * improvementTarget;
        let finalProgressValue = Math.round(
          baselineScore + progressAchievedInJourney
        );
        finalProgressValue = Math.max(0, Math.min(100, finalProgressValue));

        calculatedLayerProgress[layer] = {
          progressValue: finalProgressValue,
          color: getProgressColor(finalProgressValue),
          // iconName can be set by mobile client or here if we define a mapping
        };
        
        // Debug logging
        console.log(`🔍 Layer ${layer} calculation:`, {
          baselineScore,
          maxJourneyImpact,
          earnedJourneyImpact,
          normalizedJourneyProgress,
          finalProgressValue,
          isPrimaryLayer: layer === journey.primaryLayer
        });
      }

      const analytics: JourneyAnalytics = {
        journeyId,
        userId,
        completionRate: parseFloat(completionRate.toFixed(2)),
        averageDailyRating: parseFloat(averageDailyRating.toFixed(2)) || null,
        practiceAdherence: parseFloat(practiceAdherence.toFixed(2)),
        communityEngagement: 0, // Placeholder
        completedDays: completedDaysCount,
        streakDays: streakDays,
        layerProgress: calculatedLayerProgress, // Use calculated progress

        symptomImprovement: {
          // Placeholder
          primaryLayer: 0,
          secondaryLayers: {},
          overallWellness: 0,
        },
        spiritualDevelopment: {
          // Placeholder
          islamicPracticeIntegration: 0,
          spiritualConnection: 0,
          communityConnection: 0,
        },
        personalizationEffectiveness: {
          // Placeholder
          contentRelevance: 0,
          culturalAdaptation: 0,
          professionalIntegration: 0,
          timeManagement: 0,
        },
        nextStepRecommendations: [
          'Consider a maintenance plan',
          'Explore advanced topics',
        ], // Placeholder
        graduationReadiness: journey.status === 'completed',
        generatedAt: new Date().toISOString(),
      };

      return analytics;
    } catch (error) {
      console.error(
        `Error generating analytics for journey ${journeyId}:`,
        error
      );
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to generate journey analytics', 500);
    }
  }

  async pauseJourney(journeyId: string, userId: string): Promise<Journey> {
    logger.info('pauseJourney - ENTRY', { journeyId, userId });
    try {
      const journey = await prisma.journey.findFirst({
        where: { id: journeyId, userId, status: 'active' as JourneyStatus },
      });
      if (!journey) {
        throw new AppError('Active journey not found or access denied.', 404);
      }
      const updatedJourney = (await prisma.journey.update({
        where: { id: journeyId },
        data: { status: 'paused' as JourneyStatus, lastActiveAt: new Date() },
      })) as any; // Cast to satisfy local Journey type
      logger.info('pauseJourney - EXIT (Success)', { journeyId, userId });
      return updatedJourney;
    } catch (error) {
      logger.error(`Error in pauseJourney - EXIT (Error):`, {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        throw new AppError('Journey to pause not found', 404);
      }
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to pause journey', 500);
    }
  }

  async resumeJourney(journeyId: string, userId: string): Promise<Journey> {
    logger.info('resumeJourney - ENTRY', { journeyId, userId });
    try {
      const journey = await prisma.journey.findFirst({
        where: { id: journeyId, userId, status: 'paused' as JourneyStatus },
      });
      if (!journey) {
        throw new AppError('Paused journey not found or access denied.', 404);
      }
      const updatedJourney = (await prisma.journey.update({
        where: { id: journeyId },
        data: { status: 'active' as JourneyStatus, lastActiveAt: new Date() },
      })) as any; // Cast to satisfy local Journey type
      logger.info('resumeJourney - EXIT (Success)', { journeyId, userId });
      return updatedJourney;
    } catch (error) {
      logger.error(`Error in resumeJourney - EXIT (Error):`, {
        journeyId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        throw new AppError('Journey to resume not found', 404);
      }
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to resume journey', 500);
    }
  }
}

export const journeyService = new JourneyService();
