/**
 * Onboarding Service for Feature 0: Adaptive Onboarding & User Profiling
 * Handles the complete onboarding flow with adaptive questioning and profile generation
 */

import { Prisma } from '@prisma/client';
import { PrismaClientKnownRequestError as PrismaError } from '@prisma/client/runtime/library'; // Import PrismaError
import {
  UserProfile,
  OnboardingSession as OnboardingSessionType, // Renamed to avoid conflict with Prisma model
  OnboardingStep,
  ProfileGenerationResult,
  MentalHealthAwareness,
  RuqyaKnowledge,
  SpiritualOptimizer,
  ProfessionalContext,
  Demographics,
  LifeCircumstances,
  CrisisIndicators,
  PersonalizationPreferences,
  FeatureAccessibility,
  createEmptyProfile,
  validateProfileCompleteness,
} from '../models/UserProfile';
import { prisma } from '../config/database'; // Import Prisma client
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { AIService } from './ai.service';
import { CrisisDetectionService } from './crisis-detection.service';

export class OnboardingService {
  private aiService: AIService;
  private crisisService: CrisisDetectionService;

  constructor() {
    this.aiService = new AIService();
    this.crisisService = new CrisisDetectionService();
  }

  /**
   * Start a new onboarding session or resume existing incomplete session
   */
  async startOnboarding(
    userId: string,
    deviceInfo?: any,
    forceRestart: boolean = false
  ): Promise<{ session: OnboardingSessionType; resumed: boolean }> {
    try {
      // If forceRestart is true, abandon any existing incomplete sessions
      if (forceRestart) {
        await this.resetOnboardingSession(userId);
      } else {
        // First, check if user has already completed onboarding
        const existingProfile = await prisma.userProfileDetailed.findUnique({
          where: { userId },
        });

        if (existingProfile && existingProfile.completionStatus === 'complete') {
          logger.info('User has already completed onboarding', {
            userId,
            completionStatus: existingProfile.completionStatus,
          });
          throw new AppError(
            'Onboarding already completed. Use forceRestart=true to restart.',
            409, // Conflict status code
            {
              code: 'ONBOARDING_ALREADY_COMPLETED',
              completedAt: existingProfile.updatedAt,
              message: 'User has already completed onboarding. To restart, use the restart endpoint.',
            }
          );
        }

        // Check if there's an existing incomplete session for this user
        const existingSession = await prisma.onboardingSession.findFirst({
          where: {
            userId,
            completedAt: null, // Session is not completed
            abandonedAt: null, // Session is not abandoned
          },
          orderBy: {
            startedAt: 'desc', // Get the most recent session
          },
        });

        if (existingSession) {
          logger.info('Resuming existing onboarding session', {
            userId,
            sessionId: existingSession.sessionId,
            currentStep: existingSession.currentStep,
          });
          return {
            session: existingSession as unknown as OnboardingSessionType,
            resumed: true,
          };
        }
      }

      // No existing incomplete session found, create a new one
      const newSessionId = `onb_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;
      const startedAt = new Date();
      const currentStep = 'welcome';

      const createdSession = await prisma.onboardingSession.create({
        data: {
          sessionId: newSessionId,
          userId,
          startedAt,
          currentStep,
          steps: [] as any, // TODO: Use correct Prisma JsonValue type for arrays
          totalTimeSpent: 0,
          deviceInfo: deviceInfo || null, // TODO: Use correct Prisma type for nullable Json
        },
      });

      logger.info('New onboarding session started with Prisma', {
        userId,
        sessionId: createdSession.sessionId,
      });
      // Map Prisma model to OnboardingSessionType if necessary, though they are similar
      return {
        session: createdSession as unknown as OnboardingSessionType,
        resumed: false,
      };
    } catch (error) {
      // If it's already an AppError (like the 409 for already completed), re-throw it as-is
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('Prisma: Failed to start/resume onboarding session', {
        userId,
        error:
          error instanceof Error
            ? { name: error.name, message: error.message, stack: error.stack }
            : error,
      });
      throw new AppError('Failed to start onboarding session', 500, error);
    }
  }

  /**
   * Reset onboarding session for a user (mark existing sessions as abandoned)
   */
  async resetOnboardingSession(userId: string): Promise<void> {
    try {
      await prisma.$transaction(async (tx) => {
        // Mark all incomplete sessions as abandoned
        await tx.onboardingSession.updateMany({
          where: {
            userId,
            completedAt: null,
            abandonedAt: null,
          },
          data: {
            abandonedAt: new Date(),
            abandonmentReason: 'user_reset',
          },
        });

        // Also reset the completion status on the user's profile
        await tx.userProfileDetailed.update({
          where: { userId },
          data: {
            completionStatus: 'incomplete',
          },
        });
      });

      logger.info('Onboarding sessions and profile status reset for user in transaction', { userId });
    } catch (error) {
      if (error instanceof PrismaError && error.code === 'P2025') {
        // This means the UserProfileDetailed record was not found, which is okay if they never finished.
        logger.warn('UserProfileDetailed not found for reset, but proceeding.', { userId });
        return;
      }
      logger.error('Failed to reset onboarding sessions', {
        userId,
        error:
          error instanceof Error
            ? { name: error.name, message: error.message, stack: error.stack }
            : error,
      });
      throw new AppError('Failed to reset onboarding sessions', 500, error);
    }
  }

  /**
   * Abandon current onboarding session
   */
  async abandonSession(sessionId: string, reason?: string): Promise<void> {
    try {
      await prisma.onboardingSession.update({
        where: { sessionId },
        data: {
          abandonedAt: new Date(),
          abandonmentReason: reason || 'user_abandoned',
        },
      });

      logger.info('Onboarding session abandoned', { sessionId, reason });
    } catch (error) {
      logger.error('Failed to abandon onboarding session', {
        sessionId,
        reason,
        error:
          error instanceof Error
            ? { name: error.name, message: error.message, stack: error.stack }
            : error,
      });
      throw new AppError('Failed to abandon session', 500, error);
    }
  }

  /**
   * Get the next question based on current responses
   */
  async getNextQuestion(
    sessionId: string,
    currentResponses: Record<string, any>
  ) {
    const session = await this.getSession(sessionId);

    const nextStep = this.determineNextStep(
      session.currentStep,
      currentResponses
    );

    if (!nextStep) {
      const completionResult = await this.completeOnboarding(sessionId);
      if (!completionResult) {
        logger.error('completeOnboarding returned undefined/null', { sessionId });
        throw new AppError('Failed to complete onboarding', 500);
      }
      return completionResult;
    }

    const question = this.getQuestionForStep(session.currentStep, currentResponses);
    if (!question) {
      logger.error('getQuestionForStep returned undefined/null', {
        sessionId,
        currentStep: session.currentStep,
        nextStep,
        currentResponses: Object.keys(currentResponses)
      });
      throw new AppError(`No question found for step: ${session.currentStep}`, 500);
    }

    // question: this.getQuestionForStep(nextStep, currentResponses)
    return {
      step: nextStep,
      question,
      progress: this.calculateProgress(
        (session.steps as OnboardingStep[]).length, // Cast steps to OnboardingStep[]
        this.estimateTotalSteps(currentResponses)
      ),
    };
  }

  /**
   * Submit response to current question
   */
  async submitResponse(
    sessionId: string,
    stepId: string,
    response: any,
    timeSpent: number
  ): Promise<any> {
    const session = await this.getSession(sessionId);

    const crisisCheck = await this.crisisService.analyzeResponse(
      response,
      stepId
    );
    if (crisisCheck.isCrisis) {
      const crisisResult = await this.handleCrisisDetection(sessionId, crisisCheck);
      // Include the original crisis analysis for logging purposes
      return {
        ...crisisResult,
        originalCrisisAnalysis: crisisCheck
      };
    }

    const newStep: OnboardingStep = {
      stepId,
      stepName: this.getStepName(stepId),
      isCompleted: true,
      responses: response,
      completedAt: new Date(),
      timeSpent,
    };

    const updatedSteps = [...(session.steps as OnboardingStep[]), newStep]; // Cast and append
    const updatedTotalTimeSpent = (session.totalTimeSpent || 0) + timeSpent;

    // Construct the complete set of responses up to this point for determineNextStep
    const tempSessionForNextStepEvaluation = {
      ...session,
      steps: updatedSteps,
    };
    const allCurrentResponses = this.getAllResponses(
      tempSessionForNextStepEvaluation as OnboardingSessionType
    );

    const nextCurrentStep =
      this.determineNextStep(stepId, allCurrentResponses) || 'complete';

    try {
      await prisma.onboardingSession.update({
        where: { sessionId },
        data: {
          steps: updatedSteps as any, // Prisma expects JsonValue, ensure it's compatible
          currentStep: nextCurrentStep,
          totalTimeSpent: updatedTotalTimeSpent,
          // updatedAt will be handled by Prisma @updatedAt
        },
      });
    } catch (error) {
      // If it's already an AppError, re-throw it as-is
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('Prisma: Failed to update onboarding session', {
        sessionId,
        stepId,
        error:
          error instanceof Error
            ? { name: error.name, message: error.message, stack: error.stack }
            : error,
      });
      throw new AppError('Failed to save response', 500, error);
    }

    // Use the up-to-date allCurrentResponses instead of stale session responses
    const nextQuestionResult = await this.getNextQuestion(sessionId, allCurrentResponses);
    
    // Add defensive check
    if (!nextQuestionResult) {
      logger.error('getNextQuestion returned undefined/null', {
        sessionId,
        stepId,
        nextCurrentStep,
        allCurrentResponses: Object.keys(allCurrentResponses)
      });
      throw new AppError('Failed to determine next question', 500);
    }
    
    return nextQuestionResult;
  }

  /**
   * Complete onboarding and generate user profile
   */
  async completeOnboarding(
    sessionId: string
  ): Promise<ProfileGenerationResult> {
    const session = await this.getSession(sessionId);

    const profileGenerationResult = await this.generateUserProfile(
      sessionId,
      session.userId,
      this.getAllResponses(session)
    );

    const { profile, recommendedPathway, featureConfiguration } =
      profileGenerationResult;

    const finalPathway = this.determineRecommendedPathway(
      profile,
      recommendedPathway
    );
    const nextSteps = this.generateNextSteps(profile, finalPathway);

    await this.saveUserProfile(profile);
    await this.markSessionComplete(sessionId);

    const result: ProfileGenerationResult = {
      profile,
      recommendedPathway: finalPathway,
      featureConfiguration,
      nextSteps,
      warnings: this.generateWarnings(profile),
    };

    logger.info('Onboarding completed successfully (Prisma)', {
      userId: session.userId,
      pathway: finalPathway,
      profileCompleteness: this.calculateCompleteness(profile),
    });
    return result;
  }

  private async generateUserProfile(
    sessionId: string,
    userId: string,
    responses: Record<string, any>
  ): Promise<{
    profile: UserProfile;
    recommendedPathway: string;
    featureConfiguration: FeatureAccessibility;
  }> {
    const aiProfileResponse =
      await this.aiService.generateProfileFromOnboarding(
        responses,
        userId,
        sessionId
      );

    if (!aiProfileResponse) {
      throw new AppError('AI profile generation returned no data', 500);
    }

    const baseProfile = createEmptyProfile(userId);
    const profile: UserProfile = {
      ...(baseProfile as UserProfile),
      id: `profile_${userId}_${Date.now()}`, // This ID might need to align with UserProfileDetailed schema if it's not auto-generated
      userId,
      ...(aiProfileResponse.profileData || {}), // Ensure profileData is not undefined
      completionStatus: 'complete',
      updatedAt: new Date(),
      mentalHealthAwareness: aiProfileResponse.profileData
        ?.mentalHealthAwareness ||
        baseProfile.mentalHealthAwareness || { level: 'symptom_aware' },
      ruqyaKnowledge: aiProfileResponse.profileData?.ruqyaKnowledge ||
        baseProfile.ruqyaKnowledge || { level: 'unaware' },
      professionalContext: this.extractProfessionalContext(responses),
      demographics: aiProfileResponse.profileData?.demographics ||
        baseProfile.demographics || {
          ageRange: '26-35',
          gender: 'prefer_not_to_say',
        },
      lifeCircumstances: aiProfileResponse.profileData?.lifeCircumstances ||
        baseProfile.lifeCircumstances || {
          situations: [],
          islamicJourneyStage: 'practicing',
        },
      crisisIndicators: aiProfileResponse.profileData?.crisisIndicators ||
        baseProfile.crisisIndicators || {
          level: 'none',
          indicators: [],
          immediateHelpRequested: false,
        },
      preferences: aiProfileResponse.profileData?.preferences ||
        baseProfile.preferences || {
          contentStyle: 'moderate',
          islamicTerminology: 'basic',
          learningPace: 'moderate',
          communityEngagement: 'low',
          timeAvailability: '10-20',
        },
      featureAccessibility: this.mapPersonalizationToFeatureAccessibility(
        aiProfileResponse.personalizationSettings || {}, // Ensure personalizationSettings is not undefined
        aiProfileResponse.profileData?.crisisIndicators
      ),
      learningHistory:
        aiProfileResponse.profileData?.learningHistory ||
        baseProfile.learningHistory,
      privacySettings:
        aiProfileResponse.profileData?.privacySettings ||
        baseProfile.privacySettings,
      profileVersion:
        aiProfileResponse.profileData?.profileVersion ||
        baseProfile.profileVersion ||
        '1.0.0',
      createdAt: aiProfileResponse.profileData?.createdAt
        ? new Date(aiProfileResponse.profileData.createdAt)
        : baseProfile.createdAt,
    };

    return {
      profile,
      recommendedPathway:
        aiProfileResponse.recommendedPathway || 'standard_healing_journey', // Provide a default
      featureConfiguration: profile.featureAccessibility,
    };
  }

  private mapPersonalizationToFeatureAccessibility(
    personalizationSettings: any,
    crisisIndicators: CrisisIndicators | undefined // Make crisisIndicators optional
  ): FeatureAccessibility {
    const defaults: FeatureAccessibility = {
      feature1Level: 'standard',
      feature2Complexity: 'moderate',
      ruqyaIntegration: 'optional',
      communityAccess: 'participant',
      crisisSupport: 'standard',
    };

    if (!personalizationSettings) return defaults;

    if (personalizationSettings.content_complexity === 'detailed') {
      defaults.feature1Level = 'advanced';
      defaults.feature2Complexity = 'comprehensive';
    } else if (personalizationSettings.content_complexity === 'simple') {
      defaults.feature1Level = 'basic';
      defaults.feature2Complexity = 'simple';
    }

    if (personalizationSettings.islamic_terminology === 'extensive') {
      defaults.ruqyaIntegration = 'advanced';
    } else if (personalizationSettings.islamic_terminology === 'basic') {
      defaults.ruqyaIntegration = 'none';
    }

    if (crisisIndicators) {
      if (
        crisisIndicators.level === 'critical' ||
        crisisIndicators.level === 'high'
      ) {
        defaults.crisisSupport = 'intensive';
      } else if (crisisIndicators.level === 'moderate') {
        defaults.crisisSupport = 'enhanced';
      }
    }
    return defaults;
  }

  private determineRecommendedPathway(
    profile: UserProfile,
    aiRecommendedPathway?: string
  ): string {
    if (aiRecommendedPathway) return aiRecommendedPathway;

    const {
      mentalHealthAwareness,
      ruqyaKnowledge,
      spiritualOptimizer,
      crisisIndicators,
    } = profile;

    if (
      crisisIndicators.level === 'high' ||
      crisisIndicators.level === 'critical'
    ) {
      return 'crisis_support';
    }
    if (spiritualOptimizer?.type === 'clinical_integration')
      return 'clinical_islamic_integration';
    if (spiritualOptimizer?.type === 'traditional_bridge')
      return 'traditional_modern_bridge';
    if (
      mentalHealthAwareness.level === 'clinically_aware' &&
      ruqyaKnowledge.level === 'expert'
    )
      return 'advanced_clinical_ruqya';
    if (
      mentalHealthAwareness.level === 'symptom_aware' &&
      ruqyaKnowledge.level === 'unaware'
    )
      return 'gentle_introduction';

    return 'standard_healing_journey';
  }

  /**
   * Get onboarding session from database using Prisma
   */
  private async getSession(sessionId: string): Promise<OnboardingSessionType> {
    try {
      const session = await prisma.onboardingSession.findUnique({
        where: { sessionId },
      });

      if (!session) {
        logger.warn('Prisma: Onboarding session not found', { sessionId });
        throw new AppError('Onboarding session not found', 404);
      }
      // Cast to OnboardingSessionType. Ensure fields are compatible.
      // Prisma's `steps` and `deviceInfo` are JsonValue.
      // The application type `OnboardingSessionType` expects `steps` as `OnboardingStep[]`
      // and `deviceInfo` as `any`. This casting assumes the JSON structure is compatible.
      return session as unknown as OnboardingSessionType;
    } catch (error) {
      // If it's already an AppError, re-throw it as-is
      if (error instanceof AppError) throw error;
      
      logger.error('Prisma: Failed to get onboarding session', {
        sessionId,
        error:
          error instanceof Error
            ? { name: error.name, message: error.message, stack: error.stack }
            : error,
      });
      throw new AppError('Failed to retrieve session data', 500, error);
    }
  }

  private determineNextStep(
    currentStep: string,
    responses: Record<string, any>
  ): string | null {
    const stepFlow: Record<string, string | null | Function> = {
      welcome: 'mental_health_awareness',
      mental_health_awareness: () => {
        const mhaResponseData = responses['mental_health_awareness'];
        let mhaChoice: string | undefined | null = null;

        if (
          typeof mhaResponseData === 'object' &&
          mhaResponseData !== null &&
          mhaResponseData.hasOwnProperty('mental_health_awareness')
        ) {
          mhaChoice = mhaResponseData.mental_health_awareness;
        } else if (typeof mhaResponseData === 'string') {
          mhaChoice = mhaResponseData;
        }

        if (mhaChoice === 'clinical_aware') return 'mha_conditions';
        if (mhaChoice === 'symptom_aware') return 'mha_experiences';
        if (mhaChoice === 'clinical_integration') return 'spiritual_optimizer_clinical';
        if (mhaChoice === 'traditional_bridge') return 'spiritual_optimizer_traditional';
        
        return 'ruqya_knowledge';
      },
      mha_conditions: 'mha_therapy',
      mha_therapy: 'ruqya_knowledge',
      mha_experiences: 'mha_concepts_familiarity',
      mha_concepts_familiarity: 'ruqya_knowledge',
      spiritual_optimizer_clinical: 'ruqya_knowledge',
      spiritual_optimizer_traditional: 'ruqya_knowledge',

      ruqya_knowledge: () => {
        const rkResponseObject = responses['ruqya_knowledge'];
        const rkChoice = rkResponseObject?.ruqya_knowledge || rkResponseObject;

        if (rkChoice === 'expert') return 'rk_expert_aspects';
        if (rkChoice === 'practitioner') return 'rk_practitioner_duration';
        if (rkChoice === 'unaware') return 'rk_unaware_openness';
        
        return 'professional_context';
      },
      rk_expert_aspects: 'rk_expert_tools',
      rk_expert_tools: 'professional_context',
      rk_practitioner_duration: 'professional_context',
      rk_unaware_openness: 'rk_unaware_comfort',
      rk_unaware_comfort: 'professional_context',

      professional_context: () => {
        const pcResponse = responses['professional_context'];
        const pcChoice = pcResponse?.professional_context || pcResponse;

        if (pcChoice === 'healthcare_pc') return 'pc_healthcare_details';
        if (pcChoice === 'education_pc') return 'pc_education_details';
        if (pcChoice === 'technology_pc') return 'pc_technology_details';
        if (pcChoice === 'business_finance_pc') return 'pc_business_details';
        if (pcChoice === 'creative_arts_pc') return 'pc_creative_details';
        if (pcChoice === 'service_care_pc') return 'pc_service_details';
        if (pcChoice === 'science_research_pc') return 'pc_science_details';
        
        return 'pc_work_challenges';
      },
      pc_healthcare_details: 'pc_work_challenges',
      pc_education_details: 'pc_work_challenges',
      pc_technology_details: 'pc_work_challenges',
      pc_business_details: 'pc_work_challenges',
      pc_creative_details: 'pc_work_challenges',
      pc_service_details: 'pc_work_challenges',
      pc_science_details: 'pc_work_challenges',
      pc_work_challenges: 'demographics',

      demographics: 'life_circumstances',
      life_circumstances: null,
    };

    let resolvedNextStep: string | null | undefined;
    const stepDefinition = stepFlow[currentStep];

    if (typeof stepDefinition === 'function') {
      resolvedNextStep = stepDefinition(responses);
    } else {
      resolvedNextStep = stepDefinition;
    }

    return resolvedNextStep !== undefined ? resolvedNextStep : null;
  }

  private getAllResponses(session: OnboardingSessionType): Record<string, any> {
    const allResponses: Record<string, any> = {};
    (session.steps as OnboardingStep[]).forEach((step) => {
      allResponses[step.stepId] = step.responses;
    });
    return allResponses;
  }

  private async saveUserProfile(profile: UserProfile): Promise<void> {
    try {
      await prisma.userProfileDetailed.upsert({
        where: { userId: profile.userId },
        create: {
          userId: profile.userId,
          profileData: profile as any,
          completionStatus: profile.completionStatus,
          profileVersion: profile.profileVersion,
        },
        update: {
          profileData: profile as any,
          completionStatus: profile.completionStatus,
          profileVersion: profile.profileVersion,
        },
      });
      logger.info('Prisma: User profile saved/updated successfully', {
        userId: profile.userId,
      });
    } catch (error) {
      logger.error('Prisma: Failed to save user profile', {
        userId: profile.userId,
        error:
          error instanceof Error
            ? { name: error.name, message: error.message, stack: error.stack }
            : error,
      });
      throw new AppError('Failed to save profile data', 500, error);
    }
  }

  private async markSessionComplete(sessionId: string): Promise<void> {
    try {
      await prisma.onboardingSession.update({
        where: { sessionId },
        data: {
          completedAt: new Date(),
          currentStep: 'complete',
        },
      });
      logger.info('Prisma: Onboarding session marked as complete', {
        sessionId,
      });
    } catch (error) {
      logger.error('Prisma: Failed to mark session complete in DB', {
        sessionId,
        error:
          error instanceof PrismaError &&
          error.code === 'P2025'
            ? 'Session not found to mark complete.'
            : error instanceof Error
            ? { name: error.name, message: error.message }
            : error,
      });
      if (
        error instanceof PrismaError &&
        error.code === 'P2025'
      ) {
        return;
      }
      throw new AppError('Failed to finalize session completion', 500, error);
    }
  }

  private calculateProgress(currentStep: number, totalSteps: number): number {
    if (totalSteps === 0) return 0;
    return Math.round((currentStep / totalSteps) * 100);
  }

  private estimateTotalSteps(responses: Record<string, any>): number {
    const baseSteps = 5; // welcome, mha, rk, pc, demographics, life_circ
    let conditionalSteps = 0;

    // Mental Health Awareness Path
    const mhaResponse = responses['mental_health_awareness']?.mental_health_awareness || responses['mental_health_awareness'];
    if (mhaResponse === 'clinical_aware') {
      conditionalSteps += 2; // mha_conditions, mha_therapy
    } else if (mhaResponse === 'symptom_aware') {
      conditionalSteps += 2; // mha_experiences, mha_concepts_familiarity
    } else if (mhaResponse === 'clinical_integration' || mhaResponse === 'traditional_bridge') {
      conditionalSteps += 1; // spiritual_optimizer_*
    }

    // Ruqya Knowledge Path
    const rkResponse = responses['ruqya_knowledge']?.ruqya_knowledge || responses['ruqya_knowledge'];
    if (rkResponse === 'expert') {
      conditionalSteps += 2; // rk_expert_aspects, rk_expert_tools
    } else if (rkResponse === 'practitioner') {
      conditionalSteps += 1; // rk_practitioner_duration
    } else if (rkResponse === 'unaware') {
      conditionalSteps += 2; // rk_unaware_openness, rk_unaware_comfort
    }

    // Professional Context Path
    const pcResponse = responses['professional_context']?.professional_context || responses['professional_context'];
    if (pcResponse && pcResponse !== 'other_professional_pc' && pcResponse !== 'student_pc' && pcResponse !== 'homemaker_family_care_pc' && pcResponse !== 'seeking_work_pc' && pcResponse !== 'retired_pc' && pcResponse !== 'prefer_not_to_say_professional_pc') {
        conditionalSteps += 1; // pc_*_details
    }
    conditionalSteps += 1; // pc_work_challenges is always asked after the details or primary question

    return baseSteps + conditionalSteps;
  }

  private generateWarnings(profile: UserProfile): string[] {
    const warnings: string[] = [];
    if (profile.crisisIndicators.level === 'moderate') {
      warnings.push(
        'Moderate stress indicators detected - enhanced support recommended'
      );
    }
    if (profile.crisisIndicators.level === 'high') {
      warnings.push(
        'High stress indicators - professional support strongly recommended'
      );
    }
    const missing = validateProfileCompleteness(profile);
    if (missing.length > 0) {
      warnings.push(`Incomplete profile sections: ${missing.join(', ')}`);
    }
    return warnings;
  }

  private calculateCompleteness(profile: UserProfile): number {
    const requiredSections = 6;
    const completedSections =
      requiredSections - validateProfileCompleteness(profile).length;
    return Math.round((completedSections / requiredSections) * 100);
  }

  private getQuestionForStep(
    stepId: string,
    responses: Record<string, any>
  ): any {
    const questions: Record<string, any> = {
      welcome: {
        id: 'welcome',
        type: 'welcome',
        title: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        subtitle: 'In the name of Allah, the Most Gracious, the Most Merciful',
        content: `As-salamu alaykum, dear brother/sister.

Welcome to Qalb Healing - your personalized Islamic wellness companion.

To serve you best, we'd like to understand your unique journey and needs.
This will take just 2-3 minutes and will help us create an experience
perfectly tailored for you.

Everything you share is private and will only be used to personalize
your healing journey.`,
        actions: [
          { id: 'begin', text: 'Begin Your Journey', primary: true },
          { id: 'emergency', text: 'I need immediate help', emergency: true },
        ],
      },
      mental_health_awareness: {
        id: 'mental_health_awareness',
        type: 'single_choice',
        title: 'How would you describe what brings you to Qalb Healing today?',
        options: [
          { id: 'clinical_aware', text: 'I know I have anxiety/depression and want Islamic help' },
          { id: 'symptom_aware', text: "Something feels wrong but I'm not sure what it is" },
          { id: 'crisis', text: "I'm having a crisis and need immediate support" },
          { id: 'spiritual_growth', text: "I'm spiritually stable but want to grow and prevent future struggles" },
          { id: 'new_muslim', text: "I'm new to Islam and need gentle guidance" },
          { id: 'clinical_integration', text: 'I want to integrate my clinical/professional knowledge with Islamic spirituality' },
          { id: 'traditional_bridge', text: "I'm a religious leader seeking to understand modern mental health through Islamic lens" },
        ],
      },
      spiritual_optimizer_clinical: {
        id: 'spiritual_optimizer_clinical',
        type: 'multi_section',
        title: 'Clinical-Islamic Integration Goals',
        sections: [
          {
            question: "What's your primary goal in integrating clinical and Islamic knowledge?",
            type: 'multiple_choice',
            options: [
              'Develop Islamic approaches to mental wellness',
              'Mentor others using both clinical and Islamic understanding',
              'Contribute to Islamic mental health field research',
              'Enhance my own professional practice with Islamic principles',
            ],
          },
          {
            question: "What's your current level of Islamic psychology knowledge?",
            type: 'single_choice',
            options: [
              { id: 'extensive', text: 'Extensive - I study Islamic psychology regularly' },
              { id: 'moderate', text: 'Moderate - I know some concepts but want to deepen' },
              { id: 'basic', text: "Basic - I'm just beginning to explore this integration" },
              { id: 'minimal', text: 'Minimal - I need foundational education in Islamic psychology' },
            ],
          },
        ],
      },
      spiritual_optimizer_traditional: {
        id: 'spiritual_optimizer_traditional',
        type: 'multi_section',
        title: 'Traditional-Modern Bridge Building',
        sections: [
          {
            question: "What's your primary goal in understanding modern mental health?",
            type: 'multiple_choice',
            options: [
              'Help community members with spiritual-mental struggles',
              'Bridge traditional Islamic knowledge with modern needs',
              'Learn how Islamic spirituality addresses clinical symptoms',
              'Develop community programs for mental wellness',
            ],
          },
          {
            question: 'How comfortable are you with clinical mental health terminology?',
            type: 'single_choice',
            options: [
              { id: 'very', text: 'Very comfortable - I work with mental health concepts regularly' },
              { id: 'somewhat', text: 'Somewhat comfortable - I understand basic concepts' },
              { id: 'limited', text: 'Limited comfort - I prefer Islamic spiritual language' },
              { id: 'uncomfortable', text: 'Uncomfortable - I need gentle introduction to clinical concepts' },
            ],
          },
        ],
      },
      ruqya_knowledge: {
        id: 'ruqya_knowledge',
        type: 'single_choice',
        title: 'How familiar are you with Islamic spiritual healing (Ruqya)?',
        options: [
          { id: 'expert', text: 'I practice ruqya regularly and help others' },
          { id: 'practitioner', text: 'I know about ruqya and have tried it myself' },
          { id: 'aware', text: "I've heard of ruqya but never practiced it" },
          { id: 'skeptical', text: "I'm skeptical but open to learning" },
          { id: 'unaware', text: "I'm not familiar with ruqya at all" },
        ],
      },
      rk_expert_aspects: {
        id: 'rk_expert_aspects',
        type: 'multiple_choice',
        title: 'What aspects of ruqya are you most experienced with? (Select all that apply)',
        options: [
          { id: 'diag_id', text: 'Diagnosis and spiritual ailment identification' },
          { id: 'treat_proto', text: 'Treatment protocols and 7 intentions' },
          { id: 'waswas_mgmt', text: 'Waswas management and recognition' },
          { id: 'net_treat', text: 'Network treatment approaches' },
          { id: 'jet_hijama', text: 'JET Hijama integration' },
          { id: 'comm_ruq_supp', text: 'Community ruqya support' },
          { id: 'other_rk_aspect', text: 'Other aspects not listed' },
        ],
      },
      rk_expert_tools: {
        id: 'rk_expert_tools',
        type: 'multiple_choice',
        title: 'What tools or features here would best help enhance your practice? (Select all that apply)',
        options: [
          { id: 'adv_prog_track', text: 'Advanced progress tracking for those I help' },
          { id: 'detail_analytics_rk', text: 'Detailed analytics and insights on ruqya outcomes' },
          { id: 'comm_mentor_rk', text: 'Community mentorship opportunities (to mentor or be mentored)' },
          { id: 'scholar_content_rk', text: 'Regularly updated, scholar-verified content and protocols' },
          { id: 'other_rk_tool', text: 'Other tools or features not listed' },
        ],
      },
      rk_practitioner_duration: {
        id: 'rk_practitioner_duration',
        type: 'single_choice',
        title: 'How long have you been practicing ruqya for yourself or others?',
        options: [
          { id: 'less_than_1_yr_rk', text: 'Less than 1 year' },
          { id: '1_to_3_yrs_rk', text: '1-3 years' },
          { id: '3_to_5_yrs_rk', text: '3-5 years' },
          { id: 'more_than_5_yrs_rk', text: 'More than 5 years' },
          { id: 'prefer_not_to_say_rk_duration', text: 'Prefer not to say' },
        ],
      },
      rk_unaware_openness: {
        id: 'rk_unaware_openness',
        type: 'single_choice',
        title: 'Are you open to learning about Islamic spiritual healing concepts (Ruqya)?',
        options: [
          { id: 'rk_very_interested', text: 'Yes, very interested' },
          { id: 'rk_gradually', text: 'Yes, but gradually and gently' },
          { id: 'rk_maybe_verified', text: "Maybe, if it's scholarly verified and clearly explained" },
          { id: 'rk_focus_general', text: 'I prefer to focus on general Islamic wellness first' },
          { id: 'rk_prefer_not_to_say_openness', text: 'Prefer not to say' },
        ],
      },
      rk_unaware_comfort: {
        id: 'rk_unaware_comfort',
        type: 'single_choice',
        title: "What's your comfort level with Islamic terminology for these spiritual concepts?",
        options: [
          { id: 'rk_comfort_very', text: 'Very comfortable' },
          { id: 'rk_comfort_somewhat', text: 'Somewhat comfortable, willing to learn' },
          { id: 'rk_comfort_simple', text: 'Need simple explanations without too much jargon' },
          { id: 'rk_comfort_minimal', text: 'Prefer minimal specific Islamic terms initially' },
          { id: 'rk_prefer_not_to_say_comfort', text: 'Prefer not to say' },
        ],
      },
      professional_context: {
        id: 'professional_context',
        type: 'single_choice',
        title: 'Which of these best describes your professional life or primary daily activity?',
        options: [
          { id: 'healthcare_pc', text: 'Healthcare (Doctor, Nurse, Therapist, etc.)' },
          { id: 'education_pc', text: 'Education (Teacher, Professor, Academic Staff, etc.)' },
          { id: 'technology_pc', text: 'Technology (Developer, Engineer, IT, etc.)' },
          { id: 'business_finance_pc', text: 'Business & Finance' },
          { id: 'creative_arts_pc', text: 'Creative & Arts' },
          { id: 'service_care_pc', text: 'Service & Care (Social work, counseling, etc.)' },
          { id: 'science_research_pc', text: 'Science & Research' },
          { id: 'student_pc', text: 'Student' },
          { id: 'religious_islamic_work_pc', text: 'Religious/Islamic work' },
          { id: 'homemaker_family_care_pc', text: 'Homemaker/Family care' },
          { id: 'self_employed_pc', text: 'Self-employed / Entrepreneur' },
          { id: 'seeking_work_pc', text: 'Seeking employment / Unemployed' },
          { id: 'retired_pc', text: 'Retired' },
          { id: 'other_professional_pc', text: 'Other' },
          { id: 'prefer_not_to_say_professional_pc', text: 'Prefer not to say' },
        ],
      },
      pc_healthcare_details: {
        id: 'pc_healthcare_details',
        type: 'single_choice',
        title: 'Which area of healthcare best describes your role?',
        options: [
            { id: 'physician', text: 'Physician (GP, Specialist)' },
            { id: 'nurse', text: 'Nurse (RN, NP, etc.)' },
            { id: 'mental_health_professional', text: 'Mental Health Professional (Therapist, Psychologist)' },
            { id: 'allied_health', text: 'Allied Health (Pharmacist, Technician, etc.)' },
            { id: 'student_resident', text: 'Healthcare Student or Resident' },
            { id: 'other', text: 'Other' },
        ]
      },
      pc_education_details: {
        id: 'pc_education_details',
        type: 'single_choice',
        title: 'Which area of education best describes your role?',
        options: [
            { id: 'teacher', text: 'Teacher (Elementary, Middle, High School)' },
            { id: 'higher_education', text: 'Higher Education (Professor, Instructor)' },
            { id: 'support_staff', text: 'Education Support (Counselor, Admin)' },
            { id: 'islamic_education', text: 'Islamic Education' },
            { id: 'other', text: 'Other' },
        ]
      },
      pc_technology_details: {
        id: 'pc_technology_details',
        type: 'single_choice',
        title: 'Which area of technology best describes your role?',
        options: [
            { id: 'engineering', text: 'Engineering (Software, DevOps, etc.)' },
            { id: 'design_product', text: 'Design & Product' },
            { id: 'data_analytics', text: 'Data Science & Analytics' },
            { id: 'entrepreneurship', text: 'Startup & Entrepreneurship' },
            { id: 'other', text: 'Other' },
        ]
      },
      pc_business_details: {
        id: 'pc_business_details',
        type: 'single_choice',
        title: 'Which area of business best describes your role?',
        options: [
            { id: 'management', text: 'Management' },
            { id: 'entrepreneur', text: 'Entrepreneur' },
            { id: 'finance', text: 'Finance' },
            { id: 'consulting', text: 'Consulting' },
            { id: 'sales', text: 'Sales' },
            { id: 'other', text: 'Other' },
        ]
      },
      pc_creative_details: {
        id: 'pc_creative_details',
        type: 'single_choice',
        title: 'Which area of creative arts best describes your role?',
        options: [
            { id: 'artist', text: 'Artist' },
            { id: 'designer', text: 'Designer' },
            { id: 'writer', text: 'Writer' },
            { id: 'musician', text: 'Musician' },
            { id: 'photographer', text: 'Photographer' },
            { id: 'other', text: 'Other' },
        ]
      },
      pc_service_details: {
        id: 'pc_service_details',
        type: 'single_choice',
        title: 'Which area of service & care best describes your role?',
        options: [
            { id: 'social_work', text: 'Social Work' },
            { id: 'counseling', text: 'Counseling' },
            { id: 'non_profit', text: 'Non-profit' },
            { id: 'volunteer', text: 'Volunteer' },
            { id: 'other', text: 'Other' },
        ]
      },
      pc_science_details: {
        id: 'pc_science_details',
        type: 'single_choice',
        title: 'Which area of science & research best describes your role?',
        options: [
            { id: 'scientist', text: 'Scientist' },
            { id: 'researcher', text: 'Researcher' },
            { id: 'academic', text: 'Academic' },
            { id: 'other', text: 'Other' },
        ]
      },
      pc_work_challenges: {
        id: 'pc_work_challenges',
        type: 'multiple_choice',
        title: 'What work-related challenges, if any, currently cause you the most stress? (Select all that apply)',
        options: [
          { id: 'heavy_workload_pc', text: 'Heavy workload and deadlines' },
          { id: 'difficult_colleagues_pc', text: 'Difficult relationships with colleagues/clients' },
          { id: 'ethical_conflicts_pc', text: 'Ethical conflicts with Islamic values' },
          { id: 'work_life_balance_pc', text: 'Work-life balance struggles' },
          { id: 'financial_pressures_work_pc', text: 'Financial pressures related to work' },
          { id: 'career_uncertainty_pc', text: 'Career uncertainty or lack of growth' },
          { id: 'none_challenges_pc', text: 'None currently / Not applicable' },
          { id: 'other_challenges_pc', text: 'Other work-related challenges' },
        ],
      },
      mha_conditions: {
        id: 'mha_conditions',
        type: 'multiple_choice',
        title: 'Which of these conditions have you been diagnosed with or suspect you may have?',
        options: [
          { id: 'anxiety', text: 'Anxiety' },
          { id: 'depression', text: 'Depression' },
          { id: 'ocd', text: 'Obsessive Compulsive Disorder (OCD)' },
          { id: 'ptsd', text: 'Post-Traumatic Stress Disorder (PTSD)' },
          { id: 'other', text: 'Other' },
          { id: 'prefer_not_to_say', text: 'Prefer not to say' },
        ],
      },
      mha_therapy: {
        id: 'mha_therapy',
        type: 'single_choice',
        title: 'Have you tried therapy or counseling before for these conditions?',
        options: [
          { id: 'yes_secular', text: 'Yes, secular therapy/counseling' },
          { id: 'yes_islamic', text: 'Yes, Islamic counseling/mashura' },
          { id: 'no_open', text: "No, but I'm open to it" },
          { id: 'no_prefer_islamic', text: 'No, I prefer Islamic-only approaches for this' },
          { id: 'no_prefer_not', text: 'No, and I prefer not to pursue it at this time' },
          { id: 'prefer_not_to_say_therapy', text: 'Prefer not to say' },
        ],
      },
      mha_experiences: {
        id: 'mha_experiences',
        type: 'multiple_choice',
        title: 'What physical or emotional experiences concern you most? (Select all that apply)',
        options: [
          { id: 'sleep_problems_fatigue', text: 'Sleep problems and/or persistent fatigue' },
          { id: 'racing_heart_breathing', text: 'Racing heart, palpitations, or breathing difficulties' },
          { id: 'overwhelming_sadness_emptiness', text: 'Overwhelming sadness, emptiness, or hopelessness' },
          { id: 'anger_irritability', text: 'Increased anger, frustration, or irritability' },
          { id: 'spiritual_disconnection_mha', text: 'Spiritual disconnection or doubts related to these feelings' },
          { id: 'difficulty_concentrating', text: 'Difficulty concentrating or making decisions' },
          { id: 'social_withdrawal_mha', text: 'Avoiding social situations or withdrawing from others' },
          { id: 'excessive_worry_fear', text: 'Excessive worry, fear, or a sense of impending doom' },
          { id: 'other_experience', text: 'Other specific experiences' },
          { id: 'none_prefer_not_to_say_experience', text: 'None of these are primary / Prefer not to say' },
        ],
      },
      mha_concepts_familiarity: {
        id: 'mha_concepts_familiarity',
        type: 'single_choice',
        title: 'How familiar are you with looking at these experiences through a mental health lens?',
        options: [
          { id: 'very_familiar_concepts', text: 'Very familiar - I often use mental health terms and concepts' },
          { id: 'somewhat_familiar_concepts', text: 'Somewhat familiar - I understand some basic ideas' },
          { id: 'not_very_familiar_concepts', text: 'Not very familiar - These terms are new or unclear to me' },
          { id: 'prefer_not_to_use_terms', text: 'I prefer not to use these kinds of terms for my experiences' },
        ],
      },
      demographics: {
        id: 'demographics',
        type: 'multi_section',
        title: 'A little more about you.',
        sections: [
          {
            id: 'age_range_section',
            question: 'What is your age range?',
            type: 'single_choice',
            options: [
              { id: 'under_18_age', text: 'Under 18' },
              { id: '18_25_age', text: '18-25' },
              { id: '26_35_age', text: '26-35' },
              { id: '36_45_age', text: '36-45' },
              { id: '46_55_age', text: '46-55' },
              { id: '56_65_age', text: '56-65' },
              { id: 'over_65_age', text: 'Over 65' },
              { id: 'prefer_not_to_say_age', text: 'Prefer not to say' },
            ],
          },
          {
            id: 'gender_section',
            question: 'How do you identify?',
            type: 'single_choice',
            options: [
              { id: 'male_gender', text: 'Male' },
              { id: 'female_gender', text: 'Female' },
              { id: 'prefer_not_to_say_gender', text: 'Prefer not to say' },
            ],
          },
          {
            id: 'family_status_section',
            question: 'What is your family status?',
            type: 'single_choice',
            options: [
              { id: 'single_fs', text: 'Single' },
              { id: 'married_fs', text: 'Married' },
              { id: 'married_children_fs', text: 'Married with children' },
              { id: 'single_parent_fs', text: 'Single parent' },
              { id: 'caregiver_elderly_fs', text: 'Caregiver for elderly parents' },
              { id: 'divorced_separated_fs', text: 'Recently divorced/separated' },
              { id: 'widowed_fs', text: 'Widowed' },
              { id: 'other_fs', text: 'Other' },
              { id: 'prefer_not_to_say_fs', text: 'Prefer not to say' },
            ],
          },
        ],
      },
      life_circumstances: {
        id: 'life_circumstances',
        type: 'multiple_choice',
        title: 'Are any of these current life circumstances particularly relevant to you at the moment? (Select all that apply)',
        options: [
          { id: 'new_muslim_lc', text: 'New to Islam / Revert (e.g., within the last 2 years)' },
          { id: 'major_life_change_lc', text: 'Recent major life event (e.g., marriage, divorce, new job, relocation, bereavement)' },
          { id: 'family_caregiving_lc', text: 'Caring for family members (e.g., children, elderly, or those with health issues)' },
          { id: 'financial_stress_lc', text: 'Experiencing significant financial stress or uncertainty' },
          { id: 'non_muslim_env_lc', text: 'Living in a non-Muslim majority environment or feeling culturally isolated' },
          { id: 'academic_work_pressure_lc', text: 'Facing high academic or work-related pressure' },
          { id: 'pregnancy_new_parent_lc', text: 'Pregnancy or navigating new parenthood' },
          { id: 'social_isolation_lc', text: 'Feeling isolated, lonely, or lacking community connection' },
          { id: 'personal_health_lc', text: 'Managing personal chronic or acute health concerns' },
          { id: 'seeking_islamic_knowledge_lc', text: 'Seeking deeper Islamic knowledge or spiritual development' },
          { id: 'none_apply_lc', text: 'None of these are primary concerns for me right now' },
        ],
      },
    };
    return questions[stepId] || null;
  }

  private getStepName(stepId: string): string {
    const stepNames: Record<string, string> = {
      welcome: 'Welcome & Introduction',
      mental_health_awareness: 'Mental Health Awareness Assessment',
      spiritual_optimizer_clinical: 'Clinical Integration Goals',
      spiritual_optimizer_traditional: 'Traditional Bridge Goals',
      ruqya_knowledge: 'Ruqya Knowledge Assessment',
      professional_context: 'Professional Context',
      pc_healthcare_details: 'Healthcare Details',
      pc_education_details: 'Education Details',
      pc_technology_details: 'Technology Details',
      pc_business_details: 'Business Details',
      pc_creative_details: 'Creative Details',
      pc_service_details: 'Service Details',
      pc_science_details: 'Science Details',
      pc_work_challenges: 'Work Challenges',
      demographics: 'Demographics & Life Situation',
      life_circumstances: 'Life Circumstances',
    };
    return stepNames[stepId] || stepId;
  }

  private async handleCrisisDetection(
    sessionId: string,
    crisisCheck: any
  ): Promise<any> {
    const session = await this.getSession(sessionId);
    logger.warn('Crisis detected during onboarding', {
      sessionId,
      userId: session.userId,
      crisisLevel: crisisCheck.level,
      indicators: crisisCheck.indicators,
    });
    return {
      type: 'crisis_detected',
      level: crisisCheck.level,
      message:
        "SubhanAllah, we hear you and Allah sees your pain. You are not alone. Let's get you immediate support right now.",
      actions: [
        {
          id: 'emergency_sakina',
          text: 'Qalb Rescue',
          primary: true,
        },
        { id: 'crisis_counselor', text: 'Talk to Islamic Counselor' },
        { id: 'crisis_hotline', text: 'Crisis Hotline' },
      ],
      nextStep: 'crisis_support',
    };
  }

  private extractSpiritualOptimizer(
    responses: Record<string, any>
  ): SpiritualOptimizer | undefined {
    const primary = responses.mental_health_primary;
    if (primary === 'clinical_integration') {
      return {
        type: 'clinical_integration',
        goals: responses.clinical_integration_goals || [],
        islamicPsychologyLevel: responses.islamic_psychology_level || 'basic',
      };
    }
    if (primary === 'traditional_bridge') {
      return {
        type: 'traditional_bridge',
        goals: responses.traditional_bridge_goals || [],
        clinicalComfort: responses.clinical_comfort || 'limited',
      };
    }
    return undefined;
  }

  private extractProfessionalContext(
    responses: Record<string, any>
  ): ProfessionalContext {
    const pcResponse = responses['professional_context'];
    const field = pcResponse?.professional_context || pcResponse || 'other';

    const context: ProfessionalContext = {
      field: field,
      workStressors: responses.pc_work_challenges?.challenges || [],
    };

    if (field === 'healthcare_pc') {
      const details = responses['pc_healthcare_details'];
      context.healthcare = {
        role: details?.role || 'other',
        isStudent: details?.role === 'student_resident',
      };
    } else if (field === 'education_pc') {
        const details = responses['pc_education_details'];
        context.education = {
            role: details?.role || 'other',
            level: details?.level || 'other',
        };
    } else if (field === 'technology_pc') {
        const details = responses['pc_technology_details'];
        context.technology = {
            role: details?.role || 'other',
            isFounder: details?.role === 'entrepreneurship',
        };
    } else if (field === 'business_finance_pc') {
        const details = responses['pc_business_details'];
        context.business = {
            role: details?.role || 'other',
        };
    } else if (field === 'creative_arts_pc') {
        const details = responses['pc_creative_details'];
        context.creative = {
            role: details?.role || 'other',
        };
    } else if (field === 'service_care_pc') {
        const details = responses['pc_service_details'];
        context.service = {
            role: details?.role || 'other',
        };
    } else if (field === 'science_research_pc') {
        const details = responses['pc_science_details'];
        context.science = {
            role: details?.role || 'other',
        };
    }

    return context;
  }

  private extractDemographics(responses: Record<string, any>): Demographics {
    return {
      ageRange: responses.age_range || '26-35',
      gender: responses.gender || 'prefer_not_to_specify',
      familyStatus: responses.family_status || 'single',
      location: {
        country: responses.country,
        city: responses.city,
        timezone: responses.timezone,
      },
    };
  }

  private extractLifeCircumstances(
    responses: Record<string, any>
  ): LifeCircumstances {
    return {
      situations: responses.life_situations || [],
      islamicJourneyStage: responses.islamic_journey_stage || 'practicing',
      conversionDate: responses.conversion_date
        ? new Date(responses.conversion_date)
        : undefined,
      culturalBackground: responses.cultural_background,
      languagePreferences: responses.language_preferences || ['English'],
    };
  }

  private async assessCrisisIndicators(
    responses: Record<string, any>
  ): Promise<CrisisIndicators> {
    const crisisAnalysis = await this.aiService.analyzeCrisisIndicators(
      responses
    );
    return {
      level: crisisAnalysis.level || 'none',
      indicators: crisisAnalysis.indicators || [],
      immediateHelpRequested: responses.mental_health_primary === 'crisis',
      previousCrises: responses.previous_crises === 'yes',
      supportSystem: responses.support_system || 'moderate',
    };
  }

  private generatePersonalizationPreferences(
    responses: Record<string, any>
  ): PersonalizationPreferences {
    const mentalHealthLevel = responses.mental_health_primary;
    const ruqyaLevel = responses.ruqya_familiarity;
    let contentStyle: 'simple' | 'moderate' | 'detailed' = 'moderate';
    let islamicTerminology: 'extensive' | 'moderate' | 'basic' | 'minimal' =
      'moderate';

    if (
      mentalHealthLevel === 'clinical_integration' ||
      ruqyaLevel === 'expert'
    ) {
      contentStyle = 'detailed';
      islamicTerminology = 'extensive';
    } else if (mentalHealthLevel === 'new_muslim' || ruqyaLevel === 'unaware') {
      contentStyle = 'simple';
      islamicTerminology = 'basic';
    }
    return {
      contentStyle,
      islamicTerminology,
      learningPace: responses.learning_pace || 'moderate',
      communityEngagement: responses.community_preference || 'moderate',
      timeAvailability: responses.time_availability || '10-20',
    };
  }

  private configureFeatureAccessibility(
    mentalHealthAwareness: MentalHealthAwareness,
    ruqyaKnowledge: RuqyaKnowledge,
    spiritualOptimizer?: SpiritualOptimizer,
    crisisIndicators?: CrisisIndicators
  ): FeatureAccessibility {
    let feature1Level: 'basic' | 'standard' | 'advanced' = 'standard';
    let feature2Complexity: 'simple' | 'moderate' | 'comprehensive' =
      'moderate';
    let ruqyaIntegration: 'none' | 'optional' | 'integrated' | 'advanced' =
      'optional';
    let communityAccess: 'observer' | 'participant' | 'contributor' | 'leader' =
      'participant';
    let crisisSupport: 'standard' | 'enhanced' | 'intensive' = 'standard';

    if (mentalHealthAwareness.level === 'clinically_aware') {
      feature1Level = 'advanced';
      feature2Complexity = 'comprehensive';
    }
    if (ruqyaKnowledge.level === 'expert') {
      ruqyaIntegration = 'advanced';
      communityAccess = 'leader';
    } else if (ruqyaKnowledge.level === 'practitioner') {
      ruqyaIntegration = 'integrated';
      communityAccess = 'contributor';
    } else if (ruqyaKnowledge.level === 'unaware') {
      ruqyaIntegration = 'none';
    }
    if (spiritualOptimizer) {
      feature1Level = 'advanced';
      feature2Complexity = 'comprehensive';
      communityAccess = 'leader';
    }
    if (
      crisisIndicators?.level === 'high' ||
      crisisIndicators?.level === 'critical'
    ) {
      crisisSupport = 'intensive';
    } else if (crisisIndicators?.level === 'moderate') {
      crisisSupport = 'enhanced';
    }
    return {
      feature1Level,
      feature2Complexity,
      ruqyaIntegration,
      communityAccess,
      crisisSupport,
    };
  }

  private configureFeatureAccess(profile: UserProfile): FeatureAccessibility {
    return profile.featureAccessibility;
  }

  private generateNextSteps(profile: UserProfile, pathway: string): string[] {
    const baseSteps = [
      'Complete your personalized spiritual assessment (Feature 1)',
      'Create your healing journey (Feature 2)',
      'Connect with your community',
    ];
    const pathwaySteps: Record<string, string[]> = {
      crisis_support: [
        'Access Qalb Rescue immediately',
        'Connect with crisis counselor',
        'Activate community support network',
      ],
      clinical_islamic_integration: [
        'Access advanced Islamic psychology resources',
        'Join professional development community',
        'Begin research collaboration opportunities',
      ],
      traditional_modern_bridge: [
        'Access traditional-modern bridge content',
        'Join religious leader support network',
        'Begin community program development',
      ],
      advanced_clinical_ruqya: [
        'Access advanced diagnostic tools',
        'Join expert practitioner community',
        'Begin mentorship opportunities',
      ],
      gentle_introduction: [
        'Begin gentle Islamic wellness introduction',
        'Access new Muslim support resources',
        'Join beginner-friendly community',
      ],
    };
    return pathwaySteps[pathway] || baseSteps;
  }

  async getCurrentQuestion(sessionId: string) {
    const session = await this.getSession(sessionId);
    
    const allResponses = this.getAllResponses(session);
    
    const question = this.getQuestionForStep(session.currentStep, allResponses);
    if (!question) {
      logger.error('getQuestionForStep returned undefined/null for current step', {
        sessionId,
        currentStep: session.currentStep,
        allResponses: Object.keys(allResponses)
      });
      throw new AppError(`No question found for current step: ${session.currentStep}`, 500);
    }

    return {
      step: session.currentStep,
      question,
      progress: this.calculateProgress(
        (session.steps as OnboardingStep[]).length,
        this.estimateTotalSteps(allResponses)
      ),
    };
  }
}
