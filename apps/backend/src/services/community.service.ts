import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { DuaRequest } from '@prisma/client';

interface HeartCircle {
  id: string;
  name: string;
  description: string;
  type: 'support' | 'study' | 'dhikr' | 'general';
  memberCount: number;
  isPrivate: boolean;
  guidelines: string[];
  moderators: string[];
  createdAt: Date;
}

interface CircleMessage {
  id: string;
  circleId: string;
  userId: string;
  content: string;
  messageType: 'text' | 'dua' | 'verse' | 'encouragement';
  isAnonymous: boolean;
  createdAt: Date;
  reactions: MessageReaction[];
}

interface MessageReaction {
  userId: string;
  type: 'dua' | 'ameen' | 'barakallahu' | 'support';
  createdAt: Date;
}

interface CommunityMember {
  userId: string;
  displayName: string;
  joinDate: Date;
  role: 'member' | 'moderator' | 'admin';
  isActive: boolean;
  contributionScore: number;
}

/**
 * Community Service - Manages Heart Circles (Islamic support groups)
 */
export class CommunityService {
  /**
   * Get available Heart Circles for user
   * @param userId - User ID
   * @param type - Circle type filter
   * @returns Available Heart Circles
   */
  async getAvailableCircles(
    userId: string,
    type?: string
  ): Promise<HeartCircle[]> {
    try {
      const circles = await prisma.heartCircle.findMany({
        where: {
          isActive: true,
          ...(type ? { type } : {}),
        },
        include: {
          circleMembers: {
            select: {
              _count: true,
            },
          },
          circleModerators: {
            select: {
              userId: true,
            },
          },
        },
      });

      const heartCircles: HeartCircle[] = circles.map((circle) => ({
        id: circle.id,
        name: circle.name,
        description: circle.description,
        type: circle.type,
        memberCount: circle.circleMembers?._count || 0,
        isPrivate: circle.isPrivate,
        guidelines: circle.guidelines || [],
        moderators:
          circle.circleModerators?.map((m) => m.userId) || [],
        createdAt: circle.createdAt,
      }));

      logger.info('Available circles retrieved', {
        userId,
        type,
        count: heartCircles.length,
      });

      return heartCircles;
    } catch (error) {
      logger.error('Error getting available circles', {
        userId,
        type,
        error: error.message,
      });
      throw new AppError('Failed to get available circles', 500);
    }
  }

  /**
   * Join a Heart Circle
   * @param userId - User ID
   * @param circleId - Circle ID
   * @returns Membership confirmation
   */
  async joinCircle(userId: string, circleId: string): Promise<void> {
    try {
      // Check if user is already a member
      const existingMember = await prisma.circleMember.findUnique({
        where: {
          userId_circleId: {
            userId,
            circleId,
          },
        },
      });

      if (existingMember) {
        throw new AppError('Already a member of this circle', 400);
      }

      // Check circle capacity and privacy
      const circle = await prisma.heartCircle.findUnique({
        where: {
          id: circleId,
        },
        select: {
          isPrivate: true,
          maxMembers: true,
          circleMembers: {
            select: {
              _count: true,
            },
          },
        },
      });

      if (!circle) throw new AppError('Circle not found', 404);

      if (circle.isPrivate) {
        throw new AppError(
          'This is a private circle requiring invitation',
          403
        );
      }

      const currentMembers = circle.circleMembers?._count || 0;
      if (circle.maxMembers && currentMembers >= circle.maxMembers) {
        throw new AppError('Circle is at maximum capacity', 400);
      }

      // Add user to circle
      await prisma.circleMember.create({
        data: {
          userId,
          circleId,
          joinedAt: new Date(),
          role: 'member',
          isActive: true,
        },
      });

      // Log activity
      await this.logCommunityActivity(userId, 'joined_circle', { circleId });

      logger.info('User joined circle', { userId, circleId });
    } catch (error) {
      logger.error('Error joining circle', {
        userId,
        circleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to join circle', 500);
    }
  }

  /**
   * Get messages from a Heart Circle
   * @param userId - User ID
   * @param circleId - Circle ID
   * @param limit - Number of messages to retrieve
   * @param offset - Offset for pagination
   * @returns Circle messages
   */
  async getCircleMessages(
    userId: string,
    circleId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<CircleMessage[]> {
    try {
      // Verify user is a member
      await this.verifyCircleMembership(userId, circleId);

      const messages = await prisma.circleMessage.findMany({
        where: {
          circleId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: offset,
        take: limit,
        include: {
          user: {
            select: {
              displayName: true,
              avatarUrl: true,
            },
          },
          messageReactions: {
            select: {
              userId: true,
              reactionType: true,
              createdAt: true,
            },
          },
        },
      });

      const circleMessages: CircleMessage[] = messages.map((msg) => ({
        id: msg.id,
        circleId: msg.circleId,
        userId: msg.isAnonymous ? 'anonymous' : msg.userId,
        content: msg.content,
        messageType: msg.messageType,
        isAnonymous: msg.isAnonymous,
        createdAt: msg.createdAt,
        reactions:
          msg.messageReactions?.map((r) => ({
            userId: r.userId,
            type: r.reactionType,
            createdAt: r.createdAt,
          })) || [],
      }));

      logger.info('Circle messages retrieved', {
        userId,
        circleId,
        count: circleMessages.length,
      });

      return circleMessages;
    } catch (error) {
      logger.error('Error getting circle messages', {
        userId,
        circleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to get circle messages', 500);
    }
  }

  /**
   * Post a message to a Heart Circle
   * @param userId - User ID
   * @param circleId - Circle ID
   * @param content - Message content
   * @param messageType - Type of message
   * @param isAnonymous - Whether to post anonymously
   * @returns Posted message
   */
  async postMessage(
    userId: string,
    circleId: string,
    content: string,
    messageType: string = 'text',
    isAnonymous: boolean = false
  ): Promise<CircleMessage> {
    try {
      // Verify user is a member
      await this.verifyCircleMembership(userId, circleId);

      // Validate content for Islamic guidelines
      await this.validateMessageContent(content, messageType);

      const message = await prisma.circleMessage.create({
        data: {
          circleId,
          userId,
          content,
          messageType,
          isAnonymous,
          createdAt: new Date(),
        },
      });

      // Log activity
      await this.logCommunityActivity(userId, 'posted_message', {
        circleId,
        messageType,
      });

      logger.info('Message posted to circle', {
        userId,
        circleId,
        messageType,
        isAnonymous,
      });

      return {
        id: message.id,
        circleId: message.circleId,
        userId: isAnonymous ? 'anonymous' : message.userId,
        content: message.content,
        messageType: message.messageType,
        isAnonymous: message.isAnonymous,
        createdAt: message.createdAt,
        reactions: [],
      };
    } catch (error) {
      logger.error('Error posting message', {
        userId,
        circleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to post message', 500);
    }
  }

  /**
   * React to a message
   * @param userId - User ID
   * @param messageId - Message ID
   * @param reactionType - Type of reaction
   */
  async reactToMessage(
    userId: string,
    messageId: string,
    reactionType: string
  ): Promise<void> {
    try {
      // Check if user already reacted
      const existingReaction = await prisma.messageReaction.findUnique({
        where: {
          userId_messageId: {
            userId,
            messageId,
          },
        },
      });

      if (existingReaction) {
        // Update existing reaction
        await prisma.messageReaction.update({
          where: {
            id: existingReaction.id,
          },
          data: {
            reactionType,
          },
        });
      } else {
        // Create new reaction
        await prisma.messageReaction.create({
          data: {
            userId,
            messageId,
            reactionType,
            createdAt: new Date(),
          },
        });
      }

      logger.info('Message reaction added', {
        userId,
        messageId,
        reactionType,
      });
    } catch (error) {
      logger.error('Error reacting to message', {
        userId,
        messageId,
        error: error.message,
      });
      throw new AppError('Failed to react to message', 500);
    }
  }

  /**
   * Get user's community activity summary
   * @param userId - User ID
   * @returns Activity summary
   */
  async getUserCommunityActivity(userId: string): Promise<any> {
    try {
      const activity = await prisma.communityActivity.findMany({
        where: {
          userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 100,
      });

      // Calculate activity metrics
      const activitySummary = {
        totalActivities: activity.length,
        recentActivities: activity.slice(0, 10),
        activityTypes: this.groupActivitiesByType(activity),
        contributionScore: this.calculateContributionScore(activity),
      };

      logger.info('Community activity retrieved', {
        userId,
        totalActivities: activity.length,
      });

      return activitySummary;
    } catch (error) {
      logger.error('Error getting community activity', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get community activity', 500);
    }
  }

  /**
   * Verify user is a member of the circle
   * @param userId - User ID
   * @param circleId - Circle ID
   */
  private async verifyCircleMembership(
    userId: string,
    circleId: string
  ): Promise<void> {
    const membership = await prisma.circleMember.findUnique({
      where: {
        userId_circleId: {
          userId,
          circleId,
        },
      },
      select: {
        id: true,
      },
    });

    if (!membership) {
      throw new AppError('You are not a member of this circle', 403);
    }
  }

  /**
   * Validate message content for Islamic guidelines
   * @param content - Message content
   * @param messageType - Type of message
   */
  private async validateMessageContent(
    content: string,
    messageType: string
  ): Promise<void> {
    // Basic validation - in production, this would include more sophisticated checks
    if (!content || content.trim().length === 0) {
      throw new AppError('Message content cannot be empty', 400);
    }

    if (content.length > 1000) {
      throw new AppError('Message content too long', 400);
    }

    // Add Islamic content validation here
    // Check for inappropriate content, validate Quranic verses, etc.
  }

  /**
   * Log community activity
   * @param userId - User ID
   * @param activityType - Type of activity
   * @param metadata - Additional metadata
   */
  private async logCommunityActivity(
    userId: string,
    activityType: string,
    metadata: any
  ): Promise<void> {
    await prisma.communityActivity.create({
      data: {
        userId,
        activityType,
        metadata,
        createdAt: new Date(),
      },
    });
  }

  /**
   * Group activities by type
   * @param activities - Activity list
   * @returns Grouped activities
   */
  private groupActivitiesByType(activities: any[]): Record<string, number> {
    return activities.reduce((acc, activity) => {
      acc[activity.activityType] = (acc[activity.activityType] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Calculate user's contribution score
   * @param activities - Activity list
   * @returns Contribution score
   */
  private calculateContributionScore(activities: any[]): number {
    const weights = {
      posted_message: 5,
      reacted_to_message: 1,
      joined_circle: 3,
      helped_member: 10,
    };

    return activities.reduce((score, activity) => {
      return (
        score + (weights[activity.activityType as keyof typeof weights] || 1)
      );
    }, 0);
  }

  /**
   * Request a new Du'a from the community.
   * @param userId - The ID of the user requesting the Du'a.
   * @param requestText - The text of the Du'a request.
   * @returns The created Du'a request.
   */
  async requestDua(userId: string, requestText: string): Promise<DuaRequest> {
    try {
      const duaRequest = await prisma.duaRequest.create({
        data: {
          userId,
          requestText,
          status: 'pending',
        },
      });
      logger.info('New Du\'a request created', { userId, requestId: duaRequest.id });
      return duaRequest;
    } catch (error) {
      logger.error('Error creating Du\'a request', { userId, error: error.message });
      throw new AppError('Failed to create Du\'a request', 500);
    }
  }

  /**
   * Get all pending Du'a requests.
   * @returns A list of pending Du'a requests.
   */
  async getPendingDuaRequests(): Promise<DuaRequest[]> {
    try {
      const pendingRequests = await prisma.duaRequest.findMany({
        where: { status: 'pending' },
        orderBy: { createdAt: 'desc' },
      });
      logger.info('Retrieved pending Du\'a requests', { count: pendingRequests.length });
      return pendingRequests;
    } catch (error) {
      logger.error('Error retrieving pending Du\'a requests', { error: error.message });
      throw new AppError('Failed to retrieve pending Du\'a requests', 500);
    }
  }

  /**
   * Mark a Du'a request as completed.
   * @param requestId - The ID of the Du'a request to complete.
   * @param completerId - The ID of the user who completed the Du'a.
   * @returns The updated Du'a request.
   */
  async completeDuaRequest(requestId: string, completerId: string): Promise<DuaRequest> {
    try {
      const duaRequest = await prisma.duaRequest.findUnique({
        where: { id: requestId },
      });
      if (!duaRequest) {
        throw new AppError('Du\'a request not found', 404);
      }
      if (duaRequest.status === 'completed') {
        throw new AppError('Du\'a request already completed', 400);
      }

      const updatedDuaRequest = await prisma.duaRequest.update({
        where: { id: requestId },
        data: {
          status: 'completed',
          completedAt: new Date(),
        },
      });

      // Log activity for the completer
      await this.logCommunityActivity(completerId, 'completed_dua_request', { requestId });

      logger.info('Du\'a request completed', { requestId, completerId });
      return updatedDuaRequest;
    } catch (error) {
      logger.error('Error completing Du\'a request', { requestId, completerId, error: error.message });
      throw error instanceof AppError ? error : new AppError('Failed to complete Du\'a request', 500);
    }
  }

  /**
   * Placeholder for matching a peer supporter.
   * In a real implementation, this would involve matching logic based on availability,
   * user preferences, and crisis level.
   * @param userId - The ID of the user needing support.
   * @returns The ID of a matched peer supporter, or null if none found.
   */
  async matchPeerSupporter(userId: string): Promise<string | null> {
    try {
      logger.info('Attempting to match peer supporter', { userId });
      // For now, return a mock supporter ID
      const mockSupporterId = 'mock_peer_supporter_123';
      logger.info('Mock peer supporter matched', { userId, supporterId: mockSupporterId });
      return mockSupporterId;
    } catch (error) {
      logger.error('Error matching peer supporter', { userId, error: error.message });
      throw new AppError('Failed to match peer supporter', 500);
    }
  }
}

export const communityService = new CommunityService();