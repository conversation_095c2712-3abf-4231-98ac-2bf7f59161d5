import axios from 'axios';
import logger from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

interface N8nWorkflowPayload {
  [key: string]: any;
}

interface SymptomAnalysisResponse {
  layers_affected: string[];
  spotlight: string;
  recommended_journey: string;
  severity_level: string;
}

interface FallbackResponse {
  error?: string;
  fallback_action?: string;
  layers_affected?: string[];
  spotlight?: string;
  recommended_journey?: string;
  severity_level?: string;
  contentIds?: string[];
  modules?: any[];
}

export const triggerN8nWorkflow = async (
  workflowType: string,
  payload: N8nWorkflowPayload
): Promise<SymptomAnalysisResponse | FallbackResponse> => {
  try {
    const response = await axios.post(
      `${process.env.N8N_WEBHOOK_BASE_URL}/${workflowType}`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-N8N-API-KEY': process.env.N8N_API_KEY,
        },
        timeout: 30000, // 30 second timeout
      }
    );

    if (response.status !== 200) {
      throw new AppError('N8N workflow failed', 500);
    }

    return response.data;
  } catch (error) {
    logger.error('N8N Workflow Error:', {
      workflowType,
      error: (error as Error).message,
      payload,
    });

    // Return fallback response based on workflow type
    return getFallbackResponse(workflowType);
  }
};

const getFallbackResponse = (workflowType: string): FallbackResponse => {
  const fallbacks: Record<string, FallbackResponse> = {
    'symptom-analysis': {
      layers_affected: ['Qalb'],
      spotlight:
        "We're currently experiencing technical difficulties. Starting you with our foundational healing journey.",
      recommended_journey: '7-day-foundation',
      severity_level: 'moderate',
    },
    'get-content-recommendations': {
      contentIds: [],
      fallback_action: 'show_popular_content',
    },
    'search-content': {
      contentIds: [],
      fallback_action: 'show_recent_content',
    },
    'get-daily-inspiration': {
      contentIds: [],
      fallback_action: 'show_default_inspiration',
    },
    'generate-journey-modules': {
      modules: [
        {
          title: 'Foundation of Islamic Healing',
          type: 'introduction',
          dayNumber: 1,
          focusLayer: 'Qalb',
          estimatedDuration: 15,
        },
      ],
      fallback_action: 'default_journey',
    },
  };

  return (
    fallbacks[workflowType] || {
      error: 'Service temporarily unavailable',
      fallback_action: 'default_response',
    }
  );
};
