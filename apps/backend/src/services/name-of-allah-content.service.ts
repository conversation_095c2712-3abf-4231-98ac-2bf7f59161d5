import { PrismaClient, NameOfAllahContent, Prisma } from '@prisma/client';

class NotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

class InternalServerError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InternalServerError';
  }
}

export class NameOfAllahContentService {
  constructor(private prisma: PrismaClient) {}

  async create(data: Prisma.NameOfAllahContentCreateInput): Promise<NameOfAllahContent> {
    try {
      return await this.prisma.nameOfAllahContent.create({ data });
    } catch (error) {
      throw new InternalServerError('Error creating Name of Allah content');
    }
  }

  async findAll(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.NameOfAllahContentWhereUniqueInput;
    where?: Prisma.NameOfAllahContentWhereInput;
    orderBy?: Prisma.NameOfAllahContentOrderByWithRelationInput;
  }): Promise<NameOfAllahContent[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      return await this.prisma.nameOfAllahContent.findMany({
        skip,
        take,
        cursor,
        where,
        orderBy,
      });
    } catch (error) {
      throw new InternalServerError('Error fetching Name of Allah content');
    }
  }

  async findOne(id: string): Promise<NameOfAllahContent | null> {
    try {
      const content = await this.prisma.nameOfAllahContent.findUnique({ where: { id } });
      if (!content) {
        throw new NotFoundError('Name of Allah content not found');
      }
      return content;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      throw new InternalServerError('Error fetching Name of Allah content');
    }
  }

  async update(id: string, data: Prisma.NameOfAllahContentUpdateInput): Promise<NameOfAllahContent> {
    try {
      return await this.prisma.nameOfAllahContent.update({
        where: { id },
        data,
      });
    } catch (error) {
      // P2025 is Prisma's error code for "Record to update not found".
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundError('Name of Allah content not found for update');
      }
      throw new InternalServerError('Error updating Name of Allah content');
    }
  }

  async remove(id: string): Promise<NameOfAllahContent> {
    try {
      return await this.prisma.nameOfAllahContent.delete({ where: { id } });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundError('Name of Allah content not found for deletion');
      }
      throw new InternalServerError('Error deleting Name of Allah content');
    }
  }
}
