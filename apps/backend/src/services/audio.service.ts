import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { generateSignedUrl, validateContentAccess } from './content.service';
import { logger } from '../utils/logger';

// Assuming Prisma models are named like:
// AudioContent (for audio_content table)
// ContentItem (for content_items table)
// AudioBenefit (for audio_benefits table)
// SoulLayerMapping (for soul_layer_mappings table)
// AudioPlaylist (for audio_playlists table)
// AudioPlaybackSession (for audio_playback_sessions table)

/**
 * Audio content types for Islamic content
 */
export const AUDIO_TYPES = {
  DHIKR: 'dhikr',
  RUQYA: 'ruqya',
  DUA: 'dua',
  QURAN: 'quran',
  LECTURE: 'lecture',
  GUIDED_PRACTICE: 'guided_practice',
} as const;

/**
 * Playlist types
 */
export const PLAYLIST_TYPES = {
  SYSTEM: 'system', // Created by the system
  USER: 'user', // Created by users
  TREATMENT: 'treatment', // Part of treatment plan
  DAILY: 'daily', // Daily recommended
} as const;

// Interface for DTO, might differ from Prisma model structure slightly
interface AudioContentDTO {
  id: string;
  title: string;
  description: string;
  audioType: keyof typeof AUDIO_TYPES;
  duration: number;
  fileUrl: string;
  thumbnailUrl?: string;
  tags: string[];
  soulLayers: string[];
  benefits: string[];
}

interface PlaylistDTO {
  id: string;
  name: string;
  description: string;
  type: keyof typeof PLAYLIST_TYPES;
  audioItems: AudioContentDTO[];
  totalDuration: number;
  isPublic: boolean;
}

interface PlaybackSessionDTO {
  id: string;
  userId: string;
  audioId: string;
  startTime: Date;
  endTime?: Date;
  progress: number;
  completed: boolean;
}

/**
 * Audio Service - Handles Islamic audio content management and streaming
 */
export class AudioService {
  // private supabase: any; // No longer needed

  constructor() {
    // No Supabase initialization needed
  }

  /**
   * Get audio content by ID with streaming URL
   * @param userId - User ID
   * @param audioId - Audio content ID
   * @returns Audio content with streaming URL
   */
  async getAudioContent(
    userId: string,
    audioId: string
  ): Promise<AudioContentDTO> {
    try {
      await validateContentAccess(userId, audioId);

      const audio = await prisma.audioContent.findUnique({
        where: { id: audioId },
        include: {
          contentItem: { // Assuming relation name is 'contentItem' linking to ContentItem model
            select: { title: true, description: true, thumbnailUrl: true, duration: true, tags: true },
          },
          audioBenefits: { select: { benefitDescription: true } }, // Assuming relation name
          soulLayerMappings: { select: { soulLayer: true } }, // Assuming relation name
        },
      });

      if (!audio) throw new AppError('Audio content not found', 404);
      if (!audio.contentItem) throw new AppError('Associated content item not found', 404);


      const streamingUrl = await generateSignedUrl(audio.filePath); // Removed '3600'

      const audioContent: AudioContentDTO = {
        id: audio.id,
        title: audio.contentItem.title,
        description: audio.contentItem.description,
        audioType: audio.audioType as keyof typeof AUDIO_TYPES, // Ensure audio.audioType is compatible
        duration: audio.contentItem.duration || 0,
        fileUrl: streamingUrl,
        thumbnailUrl: audio.contentItem.thumbnailUrl || undefined,
        tags: (audio.contentItem.tags as string[]) || [],
        soulLayers: audio.soulLayerMappings?.map((m) => m.soulLayer) || [],
        benefits: audio.audioBenefits?.map((b) => b.benefitDescription) || [],
      };

      logger.info('Audio content retrieved', {
        userId,
        audioId,
        audioType: audio.audioType,
      });

      return audioContent;
    } catch (error) {
      logger.error('Error getting audio content', {
        userId,
        audioId,
        error: error instanceof Error ? error.message : String(error),
      });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to get audio content', 500);
    }
  }

  /**
   * Get audio content by type and soul layer
   */
  async getAudioByTypeAndLayer(
    userId: string, // userId might be used for personalization later, keeping it for now
    audioType: string,
    soulLayer?: string,
    limit: number = 10
  ): Promise<AudioContentDTO[]> {
    try {
      const whereClause: any = {
        audioType: audioType as keyof typeof AUDIO_TYPES,
        isActive: true,
      };

      if (soulLayer) {
        whereClause.soulLayerMappings = {
          some: { soulLayer: soulLayer },
        };
      }

      const audioList = await prisma.audioContent.findMany({
        where: whereClause,
        include: {
          contentItem: {
            select: { title: true, description: true, thumbnailUrl: true, duration: true, tags: true },
          },
          soulLayerMappings: { select: { soulLayer: true } },
        },
        take: limit,
      });

      const audioContentDTOs = await Promise.all(
        audioList.map(async (audio) => {
          if (!audio.contentItem) return null; // Should not happen if data is consistent
          const streamingUrl = await generateSignedUrl(audio.filePath); // Removed '3600'
          return {
            id: audio.id,
            title: audio.contentItem.title,
            description: audio.contentItem.description,
            audioType: audio.audioType as keyof typeof AUDIO_TYPES,
            duration: audio.contentItem.duration || 0,
            fileUrl: streamingUrl,
            thumbnailUrl: audio.contentItem.thumbnailUrl || undefined,
            tags: (audio.contentItem.tags as string[]) || [],
            soulLayers: audio.soulLayerMappings?.map((m) => m.soulLayer) || [],
            benefits: [], // Benefits were not included in original Supabase query here
          };
        })
      );

      const validAudioContent = audioContentDTOs.filter(item => item !== null) as AudioContentDTO[];

      logger.info('Audio content by type retrieved', {
        userId,
        audioType,
        soulLayer,
        count: validAudioContent.length,
      });

      return validAudioContent;
    } catch (error) {
      logger.error('Error getting audio by type', {
        userId,
        audioType,
        soulLayer,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get audio content by type', 500);
    }
  }

  async createPlaylist(
    userId: string,
    playlistData: Partial<PlaylistDTO>
  ): Promise<PlaylistDTO> {
    try {
      const playlist = await prisma.audioPlaylist.create({
        data: {
          userId,
          name: playlistData.name!,
          description: playlistData.description,
          type: playlistData.type || PLAYLIST_TYPES.USER,
          isPublic: playlistData.isPublic || false,
          // createdAt is handled by Prisma @default(now())
        },
      });

      logger.info('Playlist created', {
        userId,
        playlistId: playlist.id,
        name: playlist.name,
      });

      return {
        id: playlist.id,
        name: playlist.name,
        description: playlist.description || '',
        type: playlist.type as keyof typeof PLAYLIST_TYPES,
        audioItems: [], // New playlist is empty
        totalDuration: 0,
        isPublic: playlist.isPublic,
      };
    } catch (error) {
      logger.error('Error creating playlist', { userId, error: error instanceof Error ? error.message : String(error) });
      throw new AppError('Failed to create playlist', 500);
    }
  }

  async startPlaybackSession(
    userId: string,
    audioId: string
  ): Promise<PlaybackSessionDTO> {
    try {
      const session = await prisma.audioPlaybackSession.create({
        data: {
          userId,
          audioId,
          startTime: new Date(),
          progress: 0,
          completed: false,
        },
      });

      logger.info('Playback session started', {
        userId,
        audioId,
        sessionId: session.id,
      });

      return {
        id: session.id,
        userId: session.userId,
        audioId: session.audioId,
        startTime: session.startTime,
        progress: session.progress,
        completed: session.completed,
        // endTime will be null
      };
    } catch (error) {
      logger.error('Error starting playback session', {
        userId,
        audioId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to start playback session', 500);
    }
  }

  async updatePlaybackProgress(
    sessionId: string,
    progress: number,
    completed: boolean = false
  ): Promise<void> {
    try {
      const updateData: any = {
        progress,
        completed,
        lastUpdated: new Date(), // Assuming schema has lastUpdated
      };

      if (completed) {
        updateData.endTime = new Date();
      }

      await prisma.audioPlaybackSession.update({
        where: { id: sessionId },
        data: updateData,
      });

      logger.info('Playback progress updated', {
        sessionId,
        progress,
        completed,
      });
    } catch (error) {
      logger.error('Error updating playback progress', {
        sessionId,
        progress,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to update playback progress', 500);
    }
  }

  async getListeningHistory(
    userId: string,
    limit: number = 20
  ): Promise<any[]> { // Consider defining a DTO for history items
    try {
      const history = await prisma.audioPlaybackSession.findMany({
        where: { userId },
        include: {
          audioContent: { // Assuming relation name is 'audioContent'
            include: {
              contentItem: { // Nested include
                select: { title: true, thumbnailUrl: true, duration: true },
              },
            },
          },
        },
        orderBy: { startTime: 'desc' },
        take: limit,
      });

      logger.info('Listening history retrieved', {
        userId,
        count: history.length,
      });
      // Map to DTOs if necessary
      return history.map(h => ({
          ...h,
          // Ensure audio structure matches what client expects
          audio: h.audioContent ? {
              ...h.audioContent,
              content: h.audioContent.contentItem
          } : null
      }));
    } catch (error) {
      logger.error('Error getting listening history', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get listening history', 500);
    }
  }

  async getRecommendedAudio(
    userId: string,
    limit: number = 10
  ): Promise<AudioContentDTO[]> {
    try {
      const recentSessions = await prisma.audioPlaybackSession.findMany({
        where: { userId },
        select: {
          audioId: true,
          audioContent: { // Assuming relation name
            select: {
              audioType: true,
              soulLayerMappings: { select: { soulLayer: true } },
            },
          },
        },
        orderBy: { startTime: 'desc' },
        take: 20,
      });

      // Adapt recentSessions for analyzePreferences if its structure changed
      const adaptedSessions = recentSessions.map(s => ({
        audio_id: s.audioId, // Keep original field name if analyzePreferences expects it
        audio: s.audioContent ? { // Nest under 'audio' if analyzePreferences expects it
            audio_type: s.audioContent.audioType, // Keep original field name
            soul_layer_mappings: s.audioContent.soulLayerMappings
        } : null
      }));

      const preferredTypes = this.analyzePreferences(adaptedSessions);
      const defaultType = AUDIO_TYPES.DHIKR;
      let preferredType: string = defaultType;

      if (preferredTypes[0]) {
        preferredType = preferredTypes[0];
      }

      // Re-use getAudioByTypeAndLayer, which is already Prisma-based
      const recommendations = await this.getAudioByTypeAndLayer(
        userId,
        preferredType,
        undefined, // No specific soulLayer for general recommendations here
        limit
      );

      logger.info('Audio recommendations generated', {
        userId,
        count: recommendations.length,
      });

      return recommendations;
    } catch (error) {
      logger.error('Error getting recommended audio', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to get recommended audio', 500);
    }
  }

  private analyzePreferences(sessions: any[]): string[] {
    const typeCount: Record<string, number> = {};
    sessions.forEach((session) => {
      // Adjust access based on the structure of 'sessions' elements
      // after Prisma query (e.g., session.audio?.audioType)
      const audioType = session.audio?.audio_type; // Supabase used audio.audio_type
      if (audioType) {
        typeCount[audioType] = (typeCount[audioType] || 0) + 1;
      }
    });

    return Object.entries(typeCount)
      .sort(([, a], [, b]) => b - a)
      .map(([type]) => type);
  }
}

export const audioService = new AudioService();
