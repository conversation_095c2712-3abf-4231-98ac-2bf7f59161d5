import { PrismaClient, SunnahPracticeContent, Prisma } from '@prisma/client';

class NotFoundError extends <PERSON>rror {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

class InternalServerError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InternalServerError';
  }
}

class ConflictError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConflictError';
  }
}

export class SunnahPracticeContentService {
  constructor(private prisma: PrismaClient) {}

  async create(data: Prisma.SunnahPracticeContentCreateInput): Promise<SunnahPracticeContent> {
    try {
      return await this.prisma.sunnahPracticeContent.create({ data });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        // Unique constraint failed (title)
        throw new ConflictError('Sunnah practice with this title already exists.');
      }
      throw new InternalServerError('Error creating Sunnah practice content');
    }
  }

  async findAll(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.SunnahPracticeContentWhereUniqueInput;
    where?: Prisma.SunnahPracticeContentWhereInput;
    orderBy?: Prisma.SunnahPracticeContentOrderByWithRelationInput;
  }): Promise<SunnahPracticeContent[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      return await this.prisma.sunnahPracticeContent.findMany({
        skip,
        take,
        cursor,
        where,
        orderBy,
      });
    } catch (error) {
      throw new InternalServerError('Error fetching Sunnah practice content');
    }
  }

  async findOne(id: string): Promise<SunnahPracticeContent | null> {
    try {
      const content = await this.prisma.sunnahPracticeContent.findUnique({ where: { id } });
      if (!content) {
        throw new NotFoundError('Sunnah practice content not found');
      }
      return content;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      throw new InternalServerError('Error fetching Sunnah practice content');
    }
  }

  async findByTitle(title: string): Promise<SunnahPracticeContent | null> {
    try {
      return await this.prisma.sunnahPracticeContent.findUnique({ where: { title } });
    } catch (error) {
      throw new InternalServerError('Error fetching Sunnah practice content by title');
    }
  }

  async update(id: string, data: Prisma.SunnahPracticeContentUpdateInput): Promise<SunnahPracticeContent> {
    try {
      return await this.prisma.sunnahPracticeContent.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundError('Sunnah practice content not found for update');
      }
       if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        throw new ConflictError('Update conflicts with an existing Sunnah practice (title may already exist).');
      }
      throw new InternalServerError('Error updating Sunnah practice content');
    }
  }

  async remove(id: string): Promise<SunnahPracticeContent> {
    try {
      return await this.prisma.sunnahPracticeContent.delete({ where: { id } });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundError('Sunnah practice content not found for deletion');
      }
      throw new InternalServerError('Error deleting Sunnah practice content');
    }
  }
}
