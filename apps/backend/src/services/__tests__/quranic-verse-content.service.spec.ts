import { Test, TestingModule } from '@nestjs/testing';
import { QuranicVerseContentService } from '../quranic-verse-content.service';
import { PrismaClient, Prisma, QuranicVerseContent } from '@prisma/client';
import { HttpException, HttpStatus } from '@nestjs/common';

// Mock PrismaClient
const mockPrismaClient = {
  quranicVerseContent: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe('QuranicVerseContentService', () => {
  let service: QuranicVerseContentService;
  let prisma: typeof mockPrismaClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuranicVerseContentService,
        {
          provide: PrismaClient,
          useValue: mockPrismaClient,
        },
      ],
    }).compile();

    service = module.get<QuranicVerseContentService>(QuranicVerseContentService);
    prisma = module.get<typeof mockPrismaClient>(PrismaClient);
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // Test suite for create()
  describe('create', () => {
    const createData: Prisma.QuranicVerseContentCreateInput = {
      surahNumber: 2,
      ayahNumber: 255,
      arabicText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ...',
      translation: 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence...',
      tafsir: 'This is Ayat al-Kursi...',
      audioUrl: 'http://example.com/ayat-al-kursi.mp3',
      sunnahPracticeCategory: 'RECITATION_FOR_PROTECTION',
    };
    const expectedResult = { id: 'verse1', ...createData, createdAt: new Date(), updatedAt: new Date() } as QuranicVerseContent;

    it('should create a new Quranic verse content successfully', async () => {
      prisma.quranicVerseContent.create.mockResolvedValue(expectedResult);
      const result = await service.create(createData);
      expect(result).toEqual(expectedResult);
      expect(prisma.quranicVerseContent.create).toHaveBeenCalledWith({ data: createData });
    });

    it('should throw ConflictError if Prisma create fails due to unique constraint (P2002)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Unique constraint failed', { code: 'P2002', clientVersion: 'x.y.z'});
      prisma.quranicVerseContent.create.mockRejectedValue(prismaError);
      await expect(service.create(createData)).rejects.toThrow(
        new ConflictError('Quranic verse with this Surah and Ayah number already exists.'),
      );
    });

    it('should throw InternalServerError for other Prisma create errors', async () => {
      prisma.quranicVerseContent.create.mockRejectedValue(new Error('DB error'));
      await expect(service.create(createData)).rejects.toThrow(
        new InternalServerError('Error creating Quranic verse content'),
      );
    });
  });

  // Test suite for findAll()
  describe('findAll', () => {
    const findManyArgs = { where: { surahNumber: 2 } };
    const expectedResults: QuranicVerseContent[] = [
      { id: 'verse1', surahNumber: 2, ayahNumber: 255, arabicText: '...', translation: '...', tafsir: '...', audioUrl: 'url1', sunnahPracticeCategory: 'RECITATION_FOR_PROTECTION', createdAt: new Date(), updatedAt: new Date() },
      { id: 'verse2', surahNumber: 2, ayahNumber: 286, arabicText: '...', translation: '...', tafsir: '...', audioUrl: 'url2', sunnahPracticeCategory: 'GENERAL_RECITATION', createdAt: new Date(), updatedAt: new Date() },
    ];

    it('should return an array of Quranic verse content', async () => {
      prisma.quranicVerseContent.findMany.mockResolvedValue(expectedResults);
      const result = await service.findAll(findManyArgs);
      expect(result).toEqual(expectedResults);
      expect(prisma.quranicVerseContent.findMany).toHaveBeenCalledWith(findManyArgs);
    });

    it('should return an empty array if no content found', async () => {
      prisma.quranicVerseContent.findMany.mockResolvedValue([]);
      const result = await service.findAll({});
      expect(result).toEqual([]);
    });

    it('should throw HttpException if Prisma findMany fails', async () => {
      prisma.quranicVerseContent.findMany.mockRejectedValue(new Error('DB error'));
      await expect(service.findAll({})).rejects.toThrow(
        new HttpException('Error fetching Quranic verse content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for findOne()
  describe('findOne', () => {
    const contentId = 'verse1';
    const expectedResult: QuranicVerseContent = { id: contentId, surahNumber: 2, ayahNumber: 255, arabicText: '...', translation: '...', tafsir: '...', audioUrl: 'url1', sunnahPracticeCategory: 'RECITATION_FOR_PROTECTION', createdAt: new Date(), updatedAt: new Date() };

    it('should return a single Quranic verse content if found', async () => {
      prisma.quranicVerseContent.findUnique.mockResolvedValue(expectedResult);
      const result = await service.findOne(contentId);
      expect(result).toEqual(expectedResult);
      expect(prisma.quranicVerseContent.findUnique).toHaveBeenCalledWith({ where: { id: contentId } });
    });

    it('should throw HttpException NOT_FOUND if content not found', async () => {
      prisma.quranicVerseContent.findUnique.mockResolvedValue(null);
      await expect(service.findOne(contentId)).rejects.toThrow(
        new HttpException('Quranic verse content not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR on other Prisma errors', async () => {
      prisma.quranicVerseContent.findUnique.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.findOne(contentId)).rejects.toThrow(
        new HttpException('Error fetching Quranic verse content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for findBySurahAyah()
  describe('findBySurahAyah', () => {
    const surah = 2;
    const ayah = 255;
    const expectedResult: QuranicVerseContent = { id: 'verseX', surahNumber: surah, ayahNumber: ayah, arabicText: 'Ayat Al Kursi', translation: '...', tafsir: '...', audioUrl: 'urlX', sunnahPracticeCategory: 'RECITATION_FOR_PROTECTION', createdAt: new Date(), updatedAt: new Date() };

    it('should return Quranic verse content if found by surah and ayah', async () => {
      prisma.quranicVerseContent.findUnique.mockResolvedValue(expectedResult);
      const result = await service.findBySurahAyah(surah, ayah);
      expect(result).toEqual(expectedResult);
      expect(prisma.quranicVerseContent.findUnique).toHaveBeenCalledWith({ where: { surahNumber_ayahNumber: { surahNumber: surah, ayahNumber: ayah } } });
    });

    it('should return null if content not found by surah and ayah (as per Prisma behavior)', async () => {
      prisma.quranicVerseContent.findUnique.mockResolvedValue(null);
      const result = await service.findBySurahAyah(surah, ayah);
      expect(result).toBeNull();
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR on Prisma errors', async () => {
      prisma.quranicVerseContent.findUnique.mockRejectedValue(new Error('DB error'));
      await expect(service.findBySurahAyah(surah, ayah)).rejects.toThrow(
        new HttpException('Error fetching Quranic verse content by Surah/Ayah', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for update()
  describe('update', () => {
    const contentId = 'verse1';
    const updateData: Prisma.QuranicVerseContentUpdateInput = { tafsir: 'Updated Tafsir.' };
    const expectedResult: QuranicVerseContent = { id: contentId, surahNumber: 2, ayahNumber: 255, arabicText: '...', translation: '...', tafsir: 'Updated Tafsir.', audioUrl: 'url1', sunnahPracticeCategory: 'RECITATION_FOR_PROTECTION', createdAt: new Date(), updatedAt: new Date() };

    it('should update Quranic verse content successfully', async () => {
      prisma.quranicVerseContent.update.mockResolvedValue(expectedResult);
      const result = await service.update(contentId, updateData);
      expect(result).toEqual(expectedResult);
      expect(prisma.quranicVerseContent.update).toHaveBeenCalledWith({ where: { id: contentId }, data: updateData });
    });

    it('should throw HttpException NOT_FOUND if content to update not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Record to update not found', { code: 'P2025', clientVersion: 'x.y.z'});
      prisma.quranicVerseContent.update.mockRejectedValue(prismaError);
      await expect(service.update(contentId, updateData)).rejects.toThrow(
        new HttpException('Quranic verse content not found for update', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException CONFLICT if update causes unique constraint violation (P2002)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Unique constraint failed', { code: 'P2002', clientVersion: 'x.y.z'});
      prisma.quranicVerseContent.update.mockRejectedValue(prismaError);
      await expect(service.update(contentId, { surahNumber: 3, ayahNumber: 10 })).rejects.toThrow( // Example conflicting update
        new HttpException('Update conflicts with an existing Quranic verse (Surah/Ayah may already exist).', HttpStatus.CONFLICT),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR for other Prisma update errors', async () => {
      prisma.quranicVerseContent.update.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.update(contentId, updateData)).rejects.toThrow(
        new HttpException('Error updating Quranic verse content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for remove()
  describe('remove', () => {
    const contentId = 'verse1';
    const expectedResult: QuranicVerseContent = { id: contentId, surahNumber: 2, ayahNumber: 255, arabicText: '...', translation: '...', tafsir: '...', audioUrl: 'url1', sunnahPracticeCategory: 'RECITATION_FOR_PROTECTION', createdAt: new Date(), updatedAt: new Date() };

    it('should remove Quranic verse content successfully', async () => {
      prisma.quranicVerseContent.delete.mockResolvedValue(expectedResult);
      const result = await service.remove(contentId);
      expect(result).toEqual(expectedResult);
      expect(prisma.quranicVerseContent.delete).toHaveBeenCalledWith({ where: { id: contentId } });
    });

    it('should throw HttpException NOT_FOUND if content to delete not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Record to delete not found', { code: 'P2025', clientVersion: 'x.y.z'});
      prisma.quranicVerseContent.delete.mockRejectedValue(prismaError);
      await expect(service.remove(contentId)).rejects.toThrow(
        new HttpException('Quranic verse content not found for deletion', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw InternalServerError for other Prisma delete errors', async () => {
      prisma.quranicVerseContent.delete.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.remove(contentId)).rejects.toThrow(
        new InternalServerError('Error deleting Quranic verse content'),
      );
    });
  });
});