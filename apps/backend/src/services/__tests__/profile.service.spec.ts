import { Test, TestingModule } from '@nestjs/testing';
import { ProfileService, profileService as profileServiceInstance } from '../profile.service';
import { prisma, Profile as PrismaProfile, Prisma } from '../../config/database';
import { AppError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock Prisma
// We need to mock specific methods of prisma.profile that are used by ProfileService
jest.mock('../../config/database', () => ({
  prisma: {
    profile: {
      findUnique: jest.fn(),
      upsert: jest.fn(),
      update: jest.fn(),
      // Add other methods if ProfileService uses them e.g. create, delete etc.
    },
  },
  // Export Prisma types if they are used in tests for casting, though direct use is less common in mocks
  Prisma: {
    PrismaClientKnownRequestError: class PrismaClientKnownRequestError extends Error { // Mock the error class
        code: string;
        meta?: object;
        constructor(message: string, code: string, meta?:object) {
            super(message);
            this.code = code;
            this.meta = meta;
        }
    },
    // Add other Prisma utility types if needed by tests, e.g. JsonNull for Prisma.JsonNull
  },
}));


describe('ProfileService', () => {
  let service: ProfileService;
  let mockPrismaProfile: jest.Mocked<typeof prisma.profile>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ProfileService],
    }).compile();

    service = module.get<ProfileService>(ProfileService);
    // Assign the mocked prisma.profile to a typed variable for easier use in tests
    mockPrismaProfile = prisma.profile as jest.Mocked<typeof prisma.profile>;
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('service instance should be defined', () => {
    expect(profileServiceInstance).toBeDefined();
  });

  describe('getProfileById', () => {
    const userId = 'test-user-id';
    const mockProfileData: PrismaProfile = {
      id: userId,
      email: '<EMAIL>',
      awarenessLevel: 'symptom_aware',
      completedOnboarding: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      name: 'Test User',
      userType: 'standard',
      // Add all other fields from PrismaProfile with appropriate mock values
      bio: null, preferredLanguage: 'en', timezone: 'UTC', country: null, city: null,
      gender: null, dateOfBirth: null, occupation: null, educationLevel: null, maritalStatus: null,
      livingSituation: null, religiousBackground: null, religiousPracticeFrequency: null,
      spiritualGoals: null, preferredContentFormat: null, timeAvailability: null,
      subscribedToNewsletter: false, lastLogin: null, isActive: true, roles: [],
      notificationPreferences: Prisma.JsonNull, userSettings: Prisma.JsonNull,
      profilePictureUrl: null, coverPictureUrl: null,
      // Feature specific fields
      f0MentalHealthAwareness: Prisma.JsonNull, f0RuqyaKnowledge: Prisma.JsonNull,
      f0SpiritualOptimizer: Prisma.JsonNull, f0ProfessionalContext: Prisma.JsonNull,
      f0Demographics: Prisma.JsonNull, f0LifeCircumstances: Prisma.JsonNull,
      f0DiagnosisFeedbackOptOut: false, f0JourneyOptOut: false,
    };

    it('should return a profile if found', async () => {
      mockPrismaProfile.findUnique.mockResolvedValue(mockProfileData);

      const result = await service.getProfileById(userId);

      expect(logger.info).toHaveBeenCalledWith(`Fetching profile for user ID: ${userId}`);
      expect(mockPrismaProfile.findUnique).toHaveBeenCalledWith({ where: { id: userId } });
      expect(result).toEqual(mockProfileData);
    });

    it('should return null if profile not found', async () => {
      mockPrismaProfile.findUnique.mockResolvedValue(null);

      const result = await service.getProfileById(userId);

      expect(logger.warn).toHaveBeenCalledWith(`Profile not found for user ID: ${userId}`);
      expect(result).toBeNull();
    });

    it('should throw AppError if Prisma call fails', async () => {
      const errorMessage = 'Prisma connection failed';
      mockPrismaProfile.findUnique.mockRejectedValue(new Error(errorMessage));

      await expect(service.getProfileById(userId)).rejects.toThrow(AppError);
      await expect(service.getProfileById(userId)).rejects.toThrow('Failed to retrieve profile.');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error fetching profile for user ID ${userId}: ${errorMessage}`),
        expect.any(Object) // For the stack trace
      );
    });
  });

  // TODO: Add describe blocks for getProfileByEmail, upsertProfile, updateProfile
  // For upsertProfile and updateProfile, test success cases, not found errors (for update),
  // and unique constraint violation errors (e.g., Prisma.PrismaClientKnownRequestError with code P2002).

  describe('upsertProfile', () => {
    const userId = 'upsert-user-id';
    const email = '<EMAIL>';
    const profileDetails = { name: 'Upsert User', preferredLanguage: 'es' };
    const fullProfileData: PrismaProfile = { // Construct a complete PrismaProfile object for return value
        id: userId,
        email: email,
        name: 'Upsert User',
        preferredLanguage: 'es',
        awarenessLevel: 'symptom_aware', // Default from service logic if not in profileDetails
        completedOnboarding: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        userType: 'standard',
        bio: null, timezone: 'UTC', country: null, city: null,
        gender: null, dateOfBirth: null, occupation: null, educationLevel: null, maritalStatus: null,
        livingSituation: null, religiousBackground: null, religiousPracticeFrequency: null,
        spiritualGoals: null, preferredContentFormat: null, timeAvailability: null,
        subscribedToNewsletter: false, lastLogin: null, isActive: true, roles: [],
        notificationPreferences: Prisma.JsonNull, userSettings: Prisma.JsonNull,
        profilePictureUrl: null, coverPictureUrl: null,
        f0MentalHealthAwareness: Prisma.JsonNull, f0RuqyaKnowledge: Prisma.JsonNull,
        f0SpiritualOptimizer: Prisma.JsonNull, f0ProfessionalContext: Prisma.JsonNull,
        f0Demographics: Prisma.JsonNull, f0LifeCircumstances: Prisma.JsonNull,
        f0DiagnosisFeedbackOptOut: false, f0JourneyOptOut: false,
    };


    it('should create a new profile with default awarenessLevel if not provided', async () => {
      mockPrismaProfile.upsert.mockResolvedValue(fullProfileData);

      const result = await service.upsertProfile(userId, email, profileDetails);

      expect(mockPrismaProfile.upsert).toHaveBeenCalledWith({
        where: { id: userId },
        create: {
          id: userId,
          email: email,
          ...profileDetails,
          awarenessLevel: 'symptom_aware', // Default
        },
        update: {
          ...profileDetails,
        },
      });
      expect(result).toEqual(fullProfileData);
      expect(logger.info).toHaveBeenCalledWith(`Upserting profile for user ID: ${userId}, email: ${email}`);
    });

    it('should update an existing profile', async () => {
      const updateData = { name: 'Updated Upsert User', awarenessLevel: 'clinically_aware' as const };
      const updatedProfile = { ...fullProfileData, ...updateData };
      mockPrismaProfile.upsert.mockResolvedValue(updatedProfile);

      const result = await service.upsertProfile(userId, email, updateData);

      expect(mockPrismaProfile.upsert).toHaveBeenCalledWith({
        where: { id: userId },
        create: {
          id: userId,
          email: email,
          ...updateData,
          // awarenessLevel is provided in updateData, so no default needed for create path here
        },
        update: {
          ...updateData,
        },
      });
      expect(result).toEqual(updatedProfile);
    });

    it('should throw AppError with 409 status for unique constraint violation (P2002)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        'Unique constraint failed',
        'P2002', // Error code for unique constraint violation
        // { clientVersion: 'x.y.z' } // Removed clientVersion from meta as it's not what service uses
        { target: ['email'] } // Example meta, service appends this to message
      );
      mockPrismaProfile.upsert.mockRejectedValue(prismaError);

      await expect(service.upsertProfile(userId, email, profileDetails)).rejects.toThrow(AppError);
      try {
        await service.upsertProfile(userId, email, profileDetails);
      } catch (error: any) {
        expect(error.statusCode).toBe(409);
        expect(error.message).toContain('Profile creation/update failed: A field (e.g., email) is already in use.');
        expect(error.message).toContain("email"); // Check if meta.target is included
      }
    });

    it('should throw generic AppError for other Prisma errors', async () => {
      const genericPrismaError = new Error('Some other Prisma error');
      mockPrismaProfile.upsert.mockRejectedValue(genericPrismaError);

      await expect(service.upsertProfile(userId, email, profileDetails)).rejects.toThrow(AppError);
      await expect(service.upsertProfile(userId, email, profileDetails)).rejects.toThrow('Failed to create or update profile.');
    });
  });

  // TODO: Add describe blocks for getProfileByEmail, updateProfile
  describe('getProfileByEmail', () => {
    const email = '<EMAIL>';
    // Re-use mockProfileData structure, just change email/id for clarity if needed
    const mockProfileDataFromEmail: PrismaProfile = {
        id: 'user-from-email-id',
        email: email,
        awarenessLevel: 'symptom_aware', completedOnboarding: false, createdAt: new Date(), updatedAt: new Date(),
        name: 'Test User From Email', userType: 'standard', bio: null, preferredLanguage: 'en',
        timezone: 'UTC', country: null, city: null, gender: null, dateOfBirth: null, occupation: null,
        educationLevel: null, maritalStatus: null, livingSituation: null, religiousBackground: null,
        religiousPracticeFrequency: null, spiritualGoals: null, preferredContentFormat: null,
        timeAvailability: null, subscribedToNewsletter: false, lastLogin: null, isActive: true, roles: [],
        notificationPreferences: Prisma.JsonNull, userSettings: Prisma.JsonNull, profilePictureUrl: null,
        coverPictureUrl: null, f0MentalHealthAwareness: Prisma.JsonNull, f0RuqyaKnowledge: Prisma.JsonNull,
        f0SpiritualOptimizer: Prisma.JsonNull, f0ProfessionalContext: Prisma.JsonNull, f0Demographics: Prisma.JsonNull,
        f0LifeCircumstances: Prisma.JsonNull, f0DiagnosisFeedbackOptOut: false, f0JourneyOptOut: false,
      };

    it('should return a profile if found by email', async () => {
      mockPrismaProfile.findUnique.mockResolvedValue(mockProfileDataFromEmail);

      const result = await service.getProfileByEmail(email);

      expect(logger.info).toHaveBeenCalledWith(`Fetching profile for email: ${email}`);
      expect(mockPrismaProfile.findUnique).toHaveBeenCalledWith({ where: { email: email } });
      expect(result).toEqual(mockProfileDataFromEmail);
    });

    it('should return null if profile not found by email', async () => {
      mockPrismaProfile.findUnique.mockResolvedValue(null);

      const result = await service.getProfileByEmail(email);

      expect(logger.warn).toHaveBeenCalledWith(`Profile not found for email: ${email}`);
      expect(result).toBeNull();
    });

    it('should throw AppError if Prisma call fails for getProfileByEmail', async () => {
      const errorMessage = 'Prisma connection failed for email lookup';
      mockPrismaProfile.findUnique.mockRejectedValue(new Error(errorMessage));

      await expect(service.getProfileByEmail(email)).rejects.toThrow(AppError);
      await expect(service.getProfileByEmail(email)).rejects.toThrow('Failed to retrieve profile by email.');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error fetching profile for email ${email}: ${errorMessage}`),
        expect.any(Object)
      );
    });
  });

  describe('updateProfile', () => {
    const userId = 'update-user-id';
    const updateData = { name: 'Updated Name', preferredLanguage: 'fr' };
    const mockExistingProfile: PrismaProfile = { // Base profile before update
        id: userId, email: '<EMAIL>', name: 'Original Name', preferredLanguage: 'en',
        awarenessLevel: 'symptom_aware', completedOnboarding: true, createdAt: new Date(), updatedAt: new Date(),
        userType: 'standard', bio: null, timezone: 'UTC', country: null, city: null, gender: null,
        dateOfBirth: null, occupation: null, educationLevel: null, maritalStatus: null, livingSituation: null,
        religiousBackground: null, religiousPracticeFrequency: null, spiritualGoals: null,
        preferredContentFormat: null, timeAvailability: null, subscribedToNewsletter: false,
        lastLogin: null, isActive: true, roles: [], notificationPreferences: Prisma.JsonNull,
        userSettings: Prisma.JsonNull, profilePictureUrl: null, coverPictureUrl: null,
        f0MentalHealthAwareness: Prisma.JsonNull, f0RuqyaKnowledge: Prisma.JsonNull,
        f0SpiritualOptimizer: Prisma.JsonNull, f0ProfessionalContext: Prisma.JsonNull,
        f0Demographics: Prisma.JsonNull, f0LifeCircumstances: Prisma.JsonNull,
        f0DiagnosisFeedbackOptOut: false, f0JourneyOptOut: false,
    };
    const mockUpdatedProfile: PrismaProfile = { ...mockExistingProfile, ...updateData, updatedAt: new Date() };

    it('should successfully update an existing profile', async () => {
      mockPrismaProfile.update.mockResolvedValue(mockUpdatedProfile);

      const result = await service.updateProfile(userId, updateData);

      expect(mockPrismaProfile.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: updateData,
      });
      expect(result).toEqual(mockUpdatedProfile);
      expect(logger.info).toHaveBeenCalledWith(`Updating profile for user ID: ${userId}`);
    });

    it('should throw AppError with 404 status if profile to update is not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        'Record to update not found',
        'P2025', // Error code for record not found
        // { clientVersion: 'x.y.z' }
        { cause: 'Record not found' } // Example meta
      );
      mockPrismaProfile.update.mockRejectedValue(prismaError);

      await expect(service.updateProfile(userId, updateData)).rejects.toThrow(AppError);
      try {
        await service.updateProfile(userId, updateData);
      } catch (error: any) {
        expect(error.statusCode).toBe(404);
        expect(error.message).toBe('Profile not found for update.');
      }
    });

    it('should throw AppError with 409 status for unique constraint violation (P2002) on update', async () => {
      // This case is less common for update unless updating a unique field like email (if allowed)
      const uniqueConstraintData = { email: '<EMAIL>' }; // Assuming email is updatable and unique
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        'Unique constraint failed',
        'P2002',
        { target: ['email'] }
      );
      mockPrismaProfile.update.mockRejectedValue(prismaError);

      // Cast to any because UpdateProfileDto normally excludes email. For test purposes.
      await expect(service.updateProfile(userId, uniqueConstraintData as any)).rejects.toThrow(AppError);
      try {
        await service.updateProfile(userId, uniqueConstraintData as any);
      } catch (error: any) {
        expect(error.statusCode).toBe(409);
        expect(error.message).toContain('Profile update failed: A field (e.g., email if updatable) is already in use.');
        expect(error.message).toContain("email");
      }
    });

    it('should throw generic AppError for other Prisma errors during update', async () => {
      const genericPrismaError = new Error('Some other Prisma update error');
      mockPrismaProfile.update.mockRejectedValue(genericPrismaError);

      await expect(service.updateProfile(userId, updateData)).rejects.toThrow(AppError);
      await expect(service.updateProfile(userId, updateData)).rejects.toThrow('Failed to update profile.');
    });
  });
});
