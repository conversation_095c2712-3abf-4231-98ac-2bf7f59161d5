import { Test, TestingModule } from '@nestjs/testing';
import { AIService } from '../ai.service';
import { AppError } from '../../middleware/errorHandler';
import axios from 'axios';
import { setupSupabase } from '../../config/supabase';
import { SupabaseClient } from '@supabase/supabase-js';

// Mock setupSupabase and SupabaseClient
jest.mock('../../config/supabase', () => ({
  setupSupabase: jest.fn(),
}));

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('AIService', () => {
  let service: AIService;
  let mockSupabaseClient: Partial<SupabaseClient>;

  beforeEach(async () => {
    // Reset mocks before each test
    jest.clearAllMocks();

    mockSupabaseClient = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      insert: jest.fn().mockResolvedValue({ data: null, error: null }),
    };

    (setupSupabase as jest.Mock).mockReturnValue(mockSupabaseClient);

    const module: TestingModule = await Test.createTestingModule({
      providers: [AIService],
    }).compile();

    service = module.get<AIService>(AIService);

    // Mock environment variables
    process.env.AI_SERVICE_URL = 'http://mock-ai-service.com';
    process.env.AI_SERVICE_TOKEN = 'mock-token';
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('analyzeSymptoms', () => {
    const userId = 'test-user-id';
    const symptomData = {
      jism: ['headache'],
      nafs: ['anxiety'],
      aql: ['difficulty concentrating'],
      qalb: ['sadness'],
      ruh: ['spiritual emptiness'],
      intensity: { headache: 3, anxiety: 4 },
      duration: '1 week',
      additionalNotes: 'Feeling overwhelmed',
    };

    const mockAiResponse = {
      primary_layers_affected: ['nafs'],
      immediate_actions: ['Practice mindfulness', 'Recite Ayat al-Kursi'],
      spotlight: 'Focus on managing anxiety through spiritual practices.',
      severity_level: 'moderate',
    };

    const expectedProcessedResult = {
      primaryLayer: 'nafs',
      affectedLayers: ['nafs'],
      recommendations: [
        { type: 'practice', content: 'Practice mindfulness', frequency: 'Daily', duration: '1 week', priority: 1 },
        { type: 'practice', content: 'Recite Ayat al-Kursi', frequency: 'Daily', duration: '1 week', priority: 2 },
      ],
      insights: ['Focus on managing anxiety through spiritual practices.'],
      severity: 5, // Mapped from 'moderate'
      confidence: 0.8,
    };

    it('should analyze symptoms, call AI service, process results, and store them', async () => {
      // Mock Supabase response for user history
      (mockSupabaseClient.limit as jest.Mock).mockResolvedValue({ data: [], error: null });

      // Mock AI service response
      mockedAxios.post.mockResolvedValue({ data: mockAiResponse });

      const result = await service.analyzeSymptoms(userId, symptomData);

      expect(setupSupabase).toHaveBeenCalledTimes(1); // Called in constructor
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('health_assessments');
      expect(mockSupabaseClient.select).toHaveBeenCalledWith('*');
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('user_id', userId);
      expect(mockSupabaseClient.order).toHaveBeenCalledWith('assessment_date', { ascending: false });
      expect(mockSupabaseClient.limit).toHaveBeenCalledWith(5);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${process.env.AI_SERVICE_URL}/analyze-symptoms`,
        expect.objectContaining({
          user_id: userId,
          symptoms: {
            jism: symptomData.jism,
            nafs: symptomData.nafs,
            aql: symptomData.aql,
            qalb: symptomData.qalb,
            ruh: symptomData.ruh,
          },
        }),
        expect.any(Object)
      );

      expect(mockSupabaseClient.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: userId,
          symptoms_data: symptomData,
          analysis_result: expect.objectContaining({ primaryLayer: 'nafs' }), // Check a key part of the processed result
          primary_layer: 'nafs',
          severity_score: 5,
        })
      );

      expect(result).toEqual(expectedProcessedResult);
    });

    it('should use mock user history if Supabase is null (e.g., in constructor error)', async () => {
      // Simulate Supabase connection failure in constructor by making setupSupabase return null
      (setupSupabase as jest.Mock).mockReturnValueOnce(null);

      // Re-initialize service to simulate constructor failure for Supabase
      const newService = new AIService();

      mockedAxios.post.mockResolvedValue({ data: mockAiResponse });

      const result = await newService.analyzeSymptoms(userId, symptomData);

      // Supabase 'from' should not be called for history if supabase is null
      expect(mockSupabaseClient.from).not.toHaveBeenCalledWith('health_assessments');
      // But insert should still be attempted (and skipped internally with a debug log)
      // However, our mockSupabaseClient is still the one from beforeEach, so insert would still be "called" on the mock.
      // The internal logic `if (this.supabase)` handles the actual skipping.
      // A more accurate test for this would involve checking logger.debug for "Skipping database storage".

      expect(mockedAxios.post).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedProcessedResult);
    });

    // Tests for processAnalysisResult logic (via analyzeSymptoms)
    describe('processAnalysisResult logic', () => {
        beforeEach(() => {
            // Common setup for these specific tests: no user history needed
            (mockSupabaseClient.limit as jest.Mock).mockResolvedValue({ data: [], error: null });
        });

        it('should default primaryLayer and affectedLayers to "nafs" if AI response is missing them', async () => {
            const deficientAiResponse = { /* primary_layers_affected is missing */ spotlight: 'General feeling.', severity_level: 'mild' };
            mockedAxios.post.mockResolvedValue({ data: deficientAiResponse });
            const result = await service.analyzeSymptoms(userId, symptomData);
            expect(result.primaryLayer).toBe('nafs');
            expect(result.affectedLayers).toEqual(['nafs']);
        });

        it('should correctly map immediate_actions to recommendations', async () => {
            const aiResponseWithActions = { ...mockAiResponse, immediate_actions: ['Action 1', 'Action 2'] };
            mockedAxios.post.mockResolvedValue({ data: aiResponseWithActions });
            const result = await service.analyzeSymptoms(userId, symptomData);
            expect(result.recommendations).toEqual([
                { type: 'practice', content: 'Action 1', frequency: 'Daily', duration: '1 week', priority: 1 },
                { type: 'practice', content: 'Action 2', frequency: 'Daily', duration: '1 week', priority: 2 },
            ]);
        });

        it('should map severity_level using mapSeverityToNumber', async () => {
            mockedAxios.post.mockResolvedValue({ data: { ...mockAiResponse, severity_level: 'mild' } });
            let result = await service.analyzeSymptoms(userId, symptomData);
            expect(result.severity).toBe(3); // mild -> 3

            mockedAxios.post.mockResolvedValue({ data: { ...mockAiResponse, severity_level: 'severe' } });
            result = await service.analyzeSymptoms(userId, symptomData);
            expect(result.severity).toBe(8); // severe -> 8

            mockedAxios.post.mockResolvedValue({ data: { ...mockAiResponse, severity_level: 'unknown_level' } });
            result = await service.analyzeSymptoms(userId, symptomData);
            expect(result.severity).toBe(5); // unknown -> default 5
        });
    });

    // Tests for getDefaultRecommendations logic (via analyzeSymptoms)
    describe('getDefaultRecommendations logic (when AI provides no actions)', () => {
        beforeEach(() => {
            (mockSupabaseClient.limit as jest.Mock).mockResolvedValue({ data: [], error: null });
            // Ensure AI provides no immediate_actions to trigger getDefaultRecommendations
            mockedAxios.post.mockImplementation((url, payload) => {
                // The primary_layers_affected in payload is what determines the default recs
                // So, the mock response needs to reflect the layer we want to test.
                const primaryLayerForTest = (payload as any).symptoms?.qalb?.length > 0 ? 'qalb' : // Example logic
                                           (payload as any).symptoms?.jism?.length > 0 ? 'jism' :
                                           (payload as any).symptoms?.aql?.length > 0 ? 'aql' :
                                           (payload as any).symptoms?.ruh?.length > 0 ? 'ruh' : 'nafs';

                return Promise.resolve({ data: {
                    primary_layers_affected: [primaryLayerForTest], // This will be used by processAnalysisResult
                    immediate_actions: [], // CRITICAL: no actions from AI
                    spotlight: 'General spotlight',
                    severity_level: 'mild'
                }});
            });
        });

        it('should provide default "jism" recommendations', async () => {
            // Modify symptomData to suggest 'jism' as primary if processAnalysisResult relies on it,
            // or ensure mockAiResponse sets primary_layers_affected to jism.
            // The mockAxios.post above now sets primary_layers_affected based on input.
            const jismSymptomData = { ...symptomData, jism: ['body ache'], nafs:[], aql:[], qalb:[], ruh:[] };
            const result = await service.analyzeSymptoms(userId, jismSymptomData);
            expect(result.recommendations[0].content).toBe('Allahumma ashfi wa anta ash-shafi');
        });

        it('should provide default "nafs" recommendations', async () => {
            const nafsSymptomData = { ...symptomData, nafs: ['irritability'], jism:[], aql:[], qalb:[], ruh:[] };
            const result = await service.analyzeSymptoms(userId, nafsSymptomData);
            expect(result.recommendations[0].content).toBe('La hawla wa la quwwata illa billah');
        });

        it('should provide default "aql" recommendations', async () => {
            const aqlSymptomData = { ...symptomData, aql: ['brain fog'], jism:[], nafs:[], qalb:[], ruh:[] };
            const result = await service.analyzeSymptoms(userId, aqlSymptomData);
            expect(result.recommendations[0].content).toBe('Surah Al-Fatiha');
        });

        it('should provide default "qalb" recommendations', async () => {
            const qalbSymptomData = { ...symptomData, qalb: ['sadness'], jism:[], nafs:[], aql:[], ruh:[] };
            const result = await service.analyzeSymptoms(userId, qalbSymptomData);
            expect(result.recommendations[0].content).toBe('Astaghfirullah');
        });

        it('should provide default "ruh" recommendations', async () => {
            const ruhSymptomData = { ...symptomData, ruh: ['emptiness'], jism:[], nafs:[], aql:[], qalb:[] };
            const result = await service.analyzeSymptoms(userId, ruhSymptomData);
            expect(result.recommendations[0].content).toBe('Tahajjud prayer');
        });
    });


    it('should throw AppError if AI service call fails', async () => {
      (mockSupabaseClient.limit as jest.Mock).mockResolvedValue({ data: [], error: null });
      mockedAxios.post.mockRejectedValue(new Error('AI service unavailable'));

      await expect(service.analyzeSymptoms(userId, symptomData)).rejects.toThrow(AppError);
      await expect(service.analyzeSymptoms(userId, symptomData)).rejects.toThrow('Failed to analyze symptoms');
    });

    it('should throw AppError if Supabase history fetch fails (and not caught internally)', async () => {
      // This test assumes errors from Supabase calls like select, eq, etc., are not caught and re-thrown as AppError within analyzeSymptoms
      // if they are critical to proceeding. The current implementation uses 'await' which would let them propagate.
      (mockSupabaseClient.limit as jest.Mock).mockRejectedValue(new Error('Supabase down'));

      await expect(service.analyzeSymptoms(userId, symptomData)).rejects.toThrow('Supabase down');
    });
  });

  // TODO: Add describe blocks for other public methods:
  // - generateProfileFromOnboarding
  // - generateHealingJourney
  // - getContentRecommendations
  // - analyzeCrisisIndicators
  // - analyzeSpiritualLandscape
  // - getAIPersonalizedWelcome
  // - generateJourneyParameters (mock method)
  // - generateJourneyContent (mock method)
  // - generateAdaptiveRecommendations (mock method)
  // - matchCommunitySupport (mock method)

  describe('generateProfileFromOnboarding', () => {
    const mockResponses = { q1: 'ans1' };
    const userId = 'user-profile-test';
    const sessionId = 'session-test';
    const mockAiProfileResponse = { recommendedPathway: 'pathA', profileData: { some: 'data' } };

    it('should call AI service and return profile data', async () => {
      mockedAxios.post.mockResolvedValue({ data: mockAiProfileResponse });
      const result = await service.generateProfileFromOnboarding(mockResponses, userId, sessionId);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${process.env.AI_SERVICE_URL}/profiles/generate`,
        { userId, responses: mockResponses, sessionId },
        expect.any(Object)
      );
      expect(result).toEqual(mockAiProfileResponse);
      expect(logger.info).toHaveBeenCalledWith('AI Profile generation completed for user', expect.any(Object));
    });

    it('should throw AppError if AI service call fails', async () => {
      mockedAxios.post.mockRejectedValue(new Error('AI fail'));
      await expect(service.generateProfileFromOnboarding(mockResponses, userId, sessionId))
        .rejects.toThrow(new AppError('Failed to generate profile using AI service', 500));
    });
  });

  describe('generateHealingJourney', () => {
    const userId = 'user-journey-test';
    const mockJourneyResponse = { id: 'journey1', plan: { days: [] } };

    const baseAnalysisResult = {
        userId: userId,
        affectedLayers: ['qalb', 'nafs'],
        recommendations: [{ type: 'practice' as const, content: 'Dhikr daily', frequency: 'Daily', duration: '1 week', priority: 1 }],
        insights: ['Feeling stressed'],
        confidence: 0.8,
    };

    beforeEach(() => {
        // Default mocks for dependencies in this describe block
        mockedAxios.post.mockResolvedValue({ data: mockJourneyResponse });
        (mockSupabaseClient.from as jest.Mock).mockReturnValue(mockSupabaseClient);
        (mockSupabaseClient.insert as jest.Mock).mockResolvedValue({ data: [mockJourneyResponse], error: null });
    });

    it('should map high severity to "intensive" intensity_level in payload', async () => {
        const highSeverityAnalysis = { ...baseAnalysisResult, severity: 8 };
        await service.generateHealingJourney(userId, highSeverityAnalysis);
        expect(mockedAxios.post).toHaveBeenCalledWith(
            expect.any(String), // URL
            expect.objectContaining({ intensity_level: 'intensive' }),
            expect.any(Object)  // Headers/config
        );
    });

    it('should map moderate severity to "moderate" intensity_level in payload', async () => {
        const moderateSeverityAnalysis = { ...baseAnalysisResult, severity: 5 };
        await service.generateHealingJourney(userId, moderateSeverityAnalysis);
        expect(mockedAxios.post).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({ intensity_level: 'moderate' }),
            expect.any(Object)
        );
    });

    it('should map low severity to "light" intensity_level in payload', async () => {
        const lowSeverityAnalysis = { ...baseAnalysisResult, severity: 3 };
        await service.generateHealingJourney(userId, lowSeverityAnalysis);
        expect(mockedAxios.post).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({ intensity_level: 'light' }),
            expect.any(Object)
        );
    });

    it('should correctly map affectedLayers and recommendations to payload', async () => {
        const specificAnalysis = {
            ...baseAnalysisResult,
            severity: 6, // moderate
            affectedLayers: ['aql', 'ruh'],
            recommendations: [
                { type: 'practice' as const, content: 'Read Quran', frequency: 'Daily', duration: '1 week', priority: 1},
                { type: 'dua' as const, content: 'Make specific dua', frequency: '5 times', duration: 'Ongoing', priority: 2}
            ]
        };
        await service.generateHealingJourney(userId, specificAnalysis);
        expect(mockedAxios.post).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({
                user_id: userId,
                focus_layers: ['aql', 'ruh'],
                intensity_level: 'moderate',
                specific_goals: ['Read Quran', 'Make specific dua'],
                duration_days: 21 // Default
            }),
            expect.any(Object)
        );
    });

    it('should call AI service, store journey (if supabase available), and return journey data', async () => {
      // This test re-verifies the general success path including Supabase interaction
      const analysisResult: any = { affectedLayers: ['qalb'], severity: 5, recommendations: [{content: 'rec1'}] };
      const result = await service.generateHealingJourney(userId, analysisResult);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${process.env.AI_SERVICE_URL}/generate-journey`,
        expect.objectContaining({ user_id: userId, intensity_level: 'moderate' }),
        expect.any(Object)
      );
      expect(result).toEqual(mockJourneyResponse);
      expect(logger.info).toHaveBeenCalledWith('Healing journey generated', { userId, journeyId: mockJourneyResponse.id });
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('healing_journeys');
      expect(mockSupabaseClient.insert).toHaveBeenCalledWith(expect.objectContaining({ user_id: userId, journey_data: mockJourneyResponse }));
    });

    it('should skip storing journey if Supabase is null', async () => {
        (setupSupabase as jest.Mock).mockReturnValueOnce(null);
        const newService = new AIService(); // Re-initialize with Supabase as null
        mockedAxios.post.mockResolvedValue({ data: mockJourneyResponse });

        await newService.generateHealingJourney(userId, mockAnalysisResult);
        expect(logger.debug).toHaveBeenCalledWith('Skipping journey storage in development mode');
      });

    it('should throw AppError if AI service call fails', async () => {
      mockedAxios.post.mockRejectedValue(new Error('AI fail'));
      await expect(service.generateHealingJourney(userId, mockAnalysisResult))
        .rejects.toThrow(new AppError('Failed to generate healing journey', 500));
    });
  });

  describe('getContentRecommendations', () => {
    const userId = 'user-content-test';

    it('should call AI service with full context and return recommendations', async () => {
      const fullContext = {
        healingFocus: ['qalb', 'nafs'],
        currentMood: 'anxious',
        timeAvailable: 30,
        contentTypes: ['dua', 'quran']
      };
      const mockApiResponse = [{ id: 'rec1', title: 'Dua for anxiety' }];
      mockedAxios.post.mockResolvedValue({ data: mockApiResponse });

      const result = await service.getContentRecommendations(userId, fullContext);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${process.env.AI_SERVICE_URL}/recommend-content`,
        {
          user_id: userId,
          healing_focus: ['qalb', 'nafs'],
          current_mood: 'anxious',
          time_available: 30,
          content_types: ['dua', 'quran'],
        },
        expect.any(Object)
      );
      expect(result).toEqual(mockApiResponse);
      expect(logger.info).toHaveBeenCalledWith('Content recommendations generated', { userId, count: 1 });
    });

    it('should use default healingFocus if not provided in context', async () => {
      const contextWithoutHealingFocus = { currentMood: 'neutral', timeAvailable: 15, contentTypes: ['dhikr'] };
      mockedAxios.post.mockResolvedValue({ data: [] }); // Return empty array for this test

      await service.getContentRecommendations(userId, contextWithoutHealingFocus);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String), // URL
        expect.objectContaining({ healing_focus: ['general'] }),
        expect.any(Object)
      );
    });

    it('should handle missing optional context fields gracefully in payload', async () => {
      const minimalContext = { currentMood: 'happy' }; // Only currentMood provided
      mockedAxios.post.mockResolvedValue({ data: [{id: 'rec2'}] });

      await service.getContentRecommendations(userId, minimalContext);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          user_id: userId,
          healing_focus: ['general'], // Default
          current_mood: 'happy',
          // timeAvailable and contentTypes should not be in the payload if undefined in context
        }),
        expect.any(Object)
      );
      // Check that undefined fields are not sent, or sent as undefined if AI service expects them
      const payloadSent = mockedAxios.post.mock.calls[0][1];
      expect(payloadSent).not.toHaveProperty('time_available');
      expect(payloadSent).not.toHaveProperty('content_types');
    });

    it('should return an empty array if AI service returns null or no recommendations', async () => {
      mockedAxios.post.mockResolvedValue({ data: null });
      let result = await service.getContentRecommendations(userId, { currentMood: 'ok' });
      expect(result).toEqual([]);

      mockedAxios.post.mockResolvedValue({ data: [] });
      result = await service.getContentRecommendations(userId, { currentMood: 'ok' });
      expect(result).toEqual([]);
    });

    it('should throw AppError if AI service call fails', async () => {
      const mockContext = { healingFocus: ['qalb'], currentMood: 'calm' };
      mockedAxios.post.mockRejectedValue(new Error('AI fail'));
      await expect(service.getContentRecommendations(userId, mockContext))
        .rejects.toThrow(new AppError('Failed to get content recommendations', 500));
    });
  });

  describe('analyzeCrisisIndicators', () => {
    const mockResponses = { userId: 'user-crisis', text: 'I feel hopeless' };
    const defaultFallbackResponse = {
      level: 'moderate', confidence: 0.5, indicators: ['analysis_error'],
      urgency: 'moderate', recommended_actions: ['enhanced_support', 'manual_review'],
    };

    it('should call AI service and return crisis analysis for "high" crisis', async () => {
      const mockAiCrisisResponse = { level: 'high', confidence: 0.9, indicators: ['suicidal ideation'], urgency: 'high', recommended_actions: ['immediate_intervention'] };
      mockedAxios.post.mockResolvedValue({ data: mockAiCrisisResponse });

      const result = await service.analyzeCrisisIndicators(mockResponses);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${process.env.AI_SERVICE_URL}/crisis-analysis/analyze`,
        expect.objectContaining({ response: mockResponses }),
        expect.any(Object)
      );
      expect(result).toEqual(mockAiCrisisResponse);
      expect(logger.info).toHaveBeenCalledWith('Crisis analysis completed', { level: 'high', confidence: 0.9 });
    });

    it('should call AI service and return crisis analysis for "low" crisis', async () => {
        const mockAiCrisisResponse = { level: 'low', confidence: 0.7, indicators: ['feeling down'], urgency: 'low', recommended_actions: ['self_care_routine'] };
        mockedAxios.post.mockResolvedValue({ data: mockAiCrisisResponse });

        const result = await service.analyzeCrisisIndicators(mockResponses);
        expect(result).toEqual(mockAiCrisisResponse);
        expect(logger.info).toHaveBeenCalledWith('Crisis analysis completed', { level: 'low', confidence: 0.7 });
    });

    it('should call AI service and return crisis analysis for "none" crisis', async () => {
        const mockAiCrisisResponse = { level: 'none', confidence: 0.99, indicators: [], urgency: 'none', recommended_actions: [] };
        mockedAxios.post.mockResolvedValue({ data: mockAiCrisisResponse });

        const result = await service.analyzeCrisisIndicators(mockResponses);
        expect(result).toEqual(mockAiCrisisResponse);
        expect(logger.info).toHaveBeenCalledWith('Crisis analysis completed', { level: 'none', confidence: 0.99 });
      });

    it('should return default fallback crisis data if AI call fails (axios error)', async () => {
      mockedAxios.post.mockRejectedValue(new Error('AI service network error'));
      const result = await service.analyzeCrisisIndicators(mockResponses);
      expect(result).toEqual(defaultFallbackResponse);
      expect(logger.error).toHaveBeenCalledWith('Error analyzing crisis indicators', { error: 'AI service network error' });
    });

    it('should return default fallback crisis data if AI call fails (non-axios error)', async () => {
        mockedAxios.post.mockImplementation(() => { throw new Error('Non-axios error'); });
        const result = await service.analyzeCrisisIndicators(mockResponses);
        expect(result).toEqual(defaultFallbackResponse);
        expect(logger.error).toHaveBeenCalledWith('Error analyzing crisis indicators', { error: 'Non-axios error' });
      });
  });

  describe('analyzeSpiritualLandscape', () => {
    const mockUserId = 'user-landscape-test';
    const mockUserProfile: any = { user_id: mockUserId, demographics: { age: 30 } };
    const mockFullAnalysisData: any = {
      user_profile: mockUserProfile,
      physical_experiences: { symptoms: ['fatigue'] },
      emotional_experiences: { symptoms: ['sadness'], intensity: 'moderate' },
      mental_experiences: null, // Test with some null experiences
      spiritual_experiences: { user_reflection: 'Feeling distant' },
      reflections: { main_concern: 'How to reconnect spiritually?' },
      session_metadata: { sessionId: 's123', startedAt: new Date().toISOString(), totalTimeSpent: 600 },
    };
    const mockLandscapeResponse: any = {
        primary_layer: 'qalb',
        layer_insights: { qalb: { insights: ['Heart needs attention'] } },
        crisis_level: 'low',
        // ... other fields
    };

    it('should call AI service with correctly structured payload and return landscape analysis', async () => {
      mockedAxios.post.mockResolvedValue({ data: mockLandscapeResponse });
      const result = await service.analyzeSpiritualLandscape(mockFullAnalysisData);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${process.env.AI_SERVICE_URL}/analyze-spiritual-landscape`,
        expect.objectContaining({
          user_profile: mockUserProfile,
          physical_experiences: mockFullAnalysisData.physical_experiences,
          emotional_experiences: mockFullAnalysisData.emotional_experiences,
          mental_experiences: null, // Ensure nulls are passed
          spiritual_experiences: mockFullAnalysisData.spiritual_experiences,
          reflections: mockFullAnalysisData.reflections,
          session_metadata: expect.objectContaining({ sessionId: 's123' })
        }),
        expect.objectContaining({
            headers: expect.objectContaining({'Content-Type': 'application/json'}),
            timeout: expect.any(Number)
        })
      );
      expect(result).toEqual(mockLandscapeResponse);
      expect(logger.info).toHaveBeenCalledWith('Spiritual landscape analysis received successfully', { userId: mockUserId });
    });

    it('should handle various valid AI responses (e.g., different crisis levels, optional fields)', async () => {
        const criticalResponse = { ...mockLandscapeResponse, crisis_level: 'critical', educational_content: 'Seek immediate help article.' };
        mockedAxios.post.mockResolvedValue({ data: criticalResponse });
        let result = await service.analyzeSpiritualLandscape(mockFullAnalysisData);
        expect(result.crisis_level).toBe('critical');
        expect(result.educational_content).toBe('Seek immediate help article.');

        const noOptionalFieldsResponse = { primary_layer: 'nafs', layer_insights: {}, crisis_level: 'none', crisis_indicators:[], immediate_actions:[], next_steps:[], recommended_journey_type:'general', estimated_healing_duration:14, confidence:0.7, personalized_message:"msg", islamic_insights:[] };
        mockedAxios.post.mockResolvedValue({ data: noOptionalFieldsResponse });
        result = await service.analyzeSpiritualLandscape(mockFullAnalysisData);
        expect(result.primary_layer).toBe('nafs');
        expect(result).not.toHaveProperty('educational_content'); // Check optional field absence
    });

    it('should throw AppError with details from AI response if Axios error has response data', async () => {
      const axiosError: any = new Error('AI service error');
      axiosError.isAxiosError = true;
      axiosError.response = { data: { detail: 'Specific AI validation error' }, status: 422 };
      mockedAxios.post.mockRejectedValue(axiosError);

      await expect(service.analyzeSpiritualLandscape(mockFullAnalysisData))
        .rejects.toThrow(new AppError('Specific AI validation error', 422));
    });

    it('should throw AppError with default message if Axios error has no response data detail', async () => {
        const axiosError: any = new Error('AI service network error');
        axiosError.isAxiosError = true;
        axiosError.response = { status: 503 }; // No data.detail
        mockedAxios.post.mockRejectedValue(axiosError);

        await expect(service.analyzeSpiritualLandscape(mockFullAnalysisData))
          .rejects.toThrow(new AppError('AI service failed to analyze spiritual landscape', 503));
      });

    it('should throw AppError for non-Axios errors', async () => {
      const nonAxiosError = new Error('Some other internal error');
      mockedAxios.post.mockRejectedValue(nonAxiosError);

      await expect(service.analyzeSpiritualLandscape(mockFullAnalysisData))
        .rejects.toThrow(new AppError('An unexpected error occurred while analyzing spiritual landscape', 500));
    });
  });

  describe('getAIPersonalizedWelcome', () => {
    const mockUserId = 'user-welcome-test';
    const mockUserProfileData: any = {
        user_id: mockUserId,
        demographics: { language: 'en' },
        // other profile fields as per AIServiceUserProfileData
    };
    const baseAiWelcomeResponse = {
        user_id: mockUserId,
        user_type: 'returning_user',
        greeting: 'Welcome back!',
        introduction: 'Let us continue your journey.',
        primary_action: {id: 'pa1', text: 'Continue Assessment', description: 'Pick up where you left off.'},
        secondary_actions: [
            {id: 'sa1', text: 'Learn More', description: 'About this assessment.'},
            {id: 'sa2', text: 'Skip for now'} // No description
        ]
    };

    it('should call AI service with correct payload and transform response (snake_case to camelCase)', async () => {
      mockedAxios.post.mockResolvedValue({ data: baseAiWelcomeResponse });

      const result = await service.getAIPersonalizedWelcome(mockUserProfileData);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${process.env.AI_SERVICE_URL}/generate-assessment-welcome`,
        mockUserProfileData, // Ensure the full userProfileData is passed
        expect.any(Object)
      );
      expect(result.user_id).toBe(mockUserId);
      expect(result.greeting).toBe(baseAiWelcomeResponse.greeting);
      expect(result.primaryAction).toEqual(baseAiWelcomeResponse.primary_action);
      expect(result.secondaryActions).toEqual(baseAiWelcomeResponse.secondary_actions);
      expect(result).not.toHaveProperty('primary_action');
      expect(result).not.toHaveProperty('secondary_actions');
      expect(logger.info).toHaveBeenCalledWith('Personalized assessment welcome received from AI service', { userId: mockUserId });
    });

    it('should handle AI response with optional fields (explanation, motivation) present', async () => {
        const welcomeWithOptional = {
            ...baseAiWelcomeResponse,
            explanation: 'This assessment helps understand your state.',
            motivation: 'You are on a path to healing!',
        };
        mockedAxios.post.mockResolvedValue({ data: welcomeWithOptional });
        const result = await service.getAIPersonalizedWelcome(mockUserProfileData);
        expect(result.explanation).toBe(welcomeWithOptional.explanation);
        expect(result.motivation).toBe(welcomeWithOptional.motivation);
    });

    it('should handle AI response with optional fields (explanation, motivation) absent', async () => {
        // baseAiWelcomeResponse already omits these
        mockedAxios.post.mockResolvedValue({ data: baseAiWelcomeResponse });
        const result = await service.getAIPersonalizedWelcome(mockUserProfileData);
        expect(result.explanation).toBeUndefined(); // Or null, depending on how service handles it (current service code implies undefined)
        expect(result.motivation).toBeUndefined();
      });

    it('should throw AppError with details from AI response if Axios error has response data', async () => {
      const axiosError: any = new Error('AI service error');
      axiosError.isAxiosError = true;
      axiosError.response = { data: { detail: 'AI welcome generation issue' }, status: 400 };
      mockedAxios.post.mockRejectedValue(axiosError);

      await expect(service.getAIPersonalizedWelcome(mockUserProfileData))
        .rejects.toThrow(new AppError('AI welcome generation issue', 400));
    });

    it('should throw AppError with default message if Axios error has no response data detail', async () => {
        const axiosError: any = new Error('AI service network error');
        axiosError.isAxiosError = true;
        axiosError.response = { status: 502 }; // No data.detail
        mockedAxios.post.mockRejectedValue(axiosError);

        await expect(service.getAIPersonalizedWelcome(mockUserProfileData))
          .rejects.toThrow(new AppError('AI service failed to generate personalized welcome', 502));
      });

    it('should throw AppError for non-Axios errors', async () => {
      const nonAxiosError = new Error('Some other internal error');
      mockedAxios.post.mockRejectedValue(nonAxiosError);

      await expect(service.getAIPersonalizedWelcome(mockUserProfileData))
        .rejects.toThrow(new AppError('An unexpected error occurred while generating personalized welcome', 500));
    });
  });

  // Tests for Mock AI methods
  describe('Mock AI Methods', () => {
    const testData = { some: 'data' };
    it('generateJourneyParameters should return mock data and log', async () => {
        const result = await service.generateJourneyParameters(testData);
        expect(result).toEqual(expect.objectContaining({ duration: 21 }));
        expect(logger.info).toHaveBeenCalledWith('Mock AI: Generating journey parameters', { data: testData });
    });
    it('generateJourneyContent should return mock data and log', async () => {
        const result = await service.generateJourneyContent(testData);
        expect(result).toEqual(expect.objectContaining({ title: 'Mock Journey Title' }));
        expect(logger.info).toHaveBeenCalledWith('Mock AI: Generating journey content', { data: testData });
    });
    it('generateAdaptiveRecommendations should return mock data and log', async () => {
        const result = await service.generateAdaptiveRecommendations(testData);
        expect(result).toEqual(expect.arrayContaining([{ recommendation: expect.any(String) }]));
        expect(logger.info).toHaveBeenCalledWith('Mock AI: Generating adaptive recommendations', { data: testData });
    });
    it('matchCommunitySupport should return mock data and log', async () => {
        const result = await service.matchCommunitySupport(testData);
        expect(result).toEqual(expect.objectContaining({ groupId: 'mockGroupId' }));
        expect(logger.info).toHaveBeenCalledWith('Mock AI: Matching community support', { data: testData });
    });
  });


  describe('constructor', () => {
    it('should initialize with Supabase if setupSupabase succeeds', () => {
      // This is implicitly tested by beforeEach, but an explicit test is fine.
      (setupSupabase as jest.Mock).mockReturnValue(mockSupabaseClient);
      const newService = new AIService();
      // @ts-ignore // Access private member for test
      expect(newService.supabase).toBe(mockSupabaseClient);
    });

    it('should initialize with Supabase as null if setupSupabase throws', () => {
      (setupSupabase as jest.Mock).mockImplementation(() => {
        throw new Error('Supabase init failed');
      });
      const newService = new AIService();
      // @ts-ignore // Access private member for test
      expect(newService.supabase).toBeNull();
    });
  });

});
