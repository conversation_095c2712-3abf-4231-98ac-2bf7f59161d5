import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsService, analyticsService as analyticsServiceInstance } from '../analytics.service';
import { prisma } from '../../config/database';
import { triggerN8nWorkflow } from '../n8n.service';
import { AppError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock Prisma
jest.mock('../../config/database', () => ({
  prisma: {
    moduleCompletion: {
      findMany: jest.fn(),
    },
    journeyModule: {
      findMany: jest.fn(),
    },
    userActivity: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock n8n Service
jest.mock('../n8n.service', () => ({
  triggerN8nWorkflow: jest.fn(),
}));

describe('AnalyticsService', () => {
  let service: AnalyticsService;
  let mockPrismaModuleCompletion: jest.Mocked<typeof prisma.moduleCompletion>;
  let mockPrismaJourneyModule: jest.Mocked<typeof prisma.journeyModule>;
  let mockPrismaUserActivity: jest.Mocked<typeof prisma.userActivity>;
  let mockTriggerN8nWorkflow: jest.Mocked<typeof triggerN8nWorkflow>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AnalyticsService],
    }).compile();

    service = module.get<AnalyticsService>(AnalyticsService);

    mockPrismaModuleCompletion = prisma.moduleCompletion as jest.Mocked<typeof prisma.moduleCompletion>;
    mockPrismaJourneyModule = prisma.journeyModule as jest.Mocked<typeof prisma.journeyModule>;
    mockPrismaUserActivity = prisma.userActivity as jest.Mocked<typeof prisma.userActivity>;
    mockTriggerN8nWorkflow = triggerN8nWorkflow as jest.Mocked<typeof triggerN8nWorkflow>;

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('service instance should be defined', () => {
    expect(analyticsServiceInstance).toBeDefined();
  });

  describe('trackActivity', () => {
    const userId = 'user-123';
    const activityType = 'module_completed';
    const activityData = { moduleId: 'mod-abc', score: 90 };

    it('should successfully track an activity', async () => {
      mockPrismaUserActivity.create.mockResolvedValue({} as any); // Mock successful creation

      await service.trackActivity(userId, activityType, activityData);

      expect(mockPrismaUserActivity.create).toHaveBeenCalledWith({
        data: {
          userId,
          activityType,
          activityData,
          createdAt: expect.any(Date), // Check that createdAt is some Date
        },
      });
      expect(logger.info).toHaveBeenCalledWith('Activity tracked', { userId, activityType });
    });

    it('should throw AppError if Prisma call fails', async () => {
      const errorMessage = 'Prisma create failed';
      mockPrismaUserActivity.create.mockRejectedValue(new Error(errorMessage));

      await expect(service.trackActivity(userId, activityType, activityData)).rejects.toThrow(AppError);
      await expect(service.trackActivity(userId, activityType, activityData)).rejects.toThrow('Failed to track activity');
      expect(logger.error).toHaveBeenCalledWith(
        'Error tracking activity',
        expect.objectContaining({
          userId,
          activityType,
          error: errorMessage,
        })
      );
    });
  });

  // TODO: Add describe blocks for getUserProgress, generateInsights, getDashboardAnalytics
  // and private methods if they need to be tested indirectly or by refactoring for testability.

  describe('getUserProgress', () => {
    const userId = 'user-progress-123';
    const mockCompletions = [
      { score: 80, soulLayer: 'qalb' }, { score: 90, soulLayer: 'nafs' }, { score: 70, soulLayer: 'qalb' }
    ];
    const mockTotalModules = [ {}, {}, {}, {} ]; // 4 total modules

    let calculateStreakDaysSpy: jest.SpyInstance;
    let calculateLayerProgressSpy: jest.SpyInstance;

    beforeEach(() => {
      // Spy on private methods and mock their implementation for these tests
      calculateStreakDaysSpy = jest.spyOn(AnalyticsService.prototype as any, 'calculateStreakDays')
        .mockResolvedValue(5); // Mock streak days
      calculateLayerProgressSpy = jest.spyOn(AnalyticsService.prototype as any, 'calculateLayerProgress')
        .mockResolvedValue({ qalb: 75, nafs: 100 }); // Mock layer progress

      mockPrismaModuleCompletion.findMany.mockResolvedValue(mockCompletions as any);
      mockPrismaJourneyModule.findMany.mockResolvedValue(mockTotalModules as any);
    });

    afterEach(() => {
      // Restore original implementations of spied methods
      if (calculateStreakDaysSpy) calculateStreakDaysSpy.mockRestore(); // Ensure spy exists before restoring
      if (calculateLayerProgressSpy) calculateLayerProgressSpy.mockRestore(); // Ensure spy exists before restoring
    });

    // This test remains as a general check, specific streak tests follow
    it('should calculate progress metrics correctly (general case, with mocked streak/layer)', async () => {
      // For this general test, explicitly mock calculateStreakDays as it was before
      const generalStreakSpy = jest.spyOn(AnalyticsService.prototype as any, 'calculateStreakDays').mockResolvedValue(5);
      // Ensure calculateLayerProgressSpy is the one from the outer scope for this general test
      const outerCalculateLayerProgressSpy = jest.spyOn(AnalyticsService.prototype as any, 'calculateLayerProgress')
        .mockResolvedValue({ qalb: 75, nafs: 100 });

      const result = await service.getUserProgress(userId);

      expect(mockPrismaModuleCompletion.findMany).toHaveBeenCalledWith({ where: { userId } });
      expect(mockPrismaJourneyModule.findMany).toHaveBeenCalledWith({ where: { userId } });
      expect(generalStreakSpy).toHaveBeenCalledWith(userId);
      expect(outerCalculateLayerProgressSpy).toHaveBeenCalledWith(userId, undefined);

      expect(result.totalModules).toBe(4);
      expect(result.completedModules).toBe(3);
      expect(result.completionRate).toBe(75);
      expect(result.averageScore).toBe(80);
      expect(result.streakDays).toBe(5); // From generalStreakSpy mock for this test
      expect(result.layerProgress).toEqual({ qalb: 75, nafs: 100 });

      expect(logger.info).toHaveBeenCalledWith('User progress calculated', expect.any(Object));
      generalStreakSpy.mockRestore(); // Clean up spy for this specific test
    });

    describe('calculateStreakDays logic (tested via getUserProgress)', () => {
        // Minimal mocks for other parts of getUserProgress
        const minimalCompletions: any[] = [];
        const minimalTotalModules: any[] = [];

        beforeEach(() => {
            mockPrismaModuleCompletion.findMany.mockResolvedValue(minimalCompletions);
            mockPrismaJourneyModule.findMany.mockResolvedValue(minimalTotalModules);
            // calculateLayerProgressSpy is already restored and re-mocked in outer beforeEach if needed,
            // or we can mock it to simple {} for these specific streak tests.
            // The outer beforeEach already mocks calculateLayerProgressSpy to return { qalb: 75, nafs: 100 }
            // which is fine, as we are not asserting layerProgress here.
        });

        it('should return streakDays = 0 if no activities', async () => {
            mockPrismaUserActivity.findMany.mockResolvedValue([]);
            const result = await service.getUserProgress(userId);
            expect(result.streakDays).toBe(0);
        });

        const today = new Date();
        const yesterday = new Date(today); yesterday.setDate(today.getDate() - 1);
        const twoDaysAgo = new Date(today); twoDaysAgo.setDate(today.getDate() - 2);
        const threeDaysAgo = new Date(today); threeDaysAgo.setDate(today.getDate() - 3);
        const fiveDaysAgo = new Date(today); fiveDaysAgo.setDate(today.getDate() - 5);

        it('should calculate a continuous streak of 3 days', async () => {
            mockPrismaUserActivity.findMany.mockResolvedValue([
                { createdAt: today }, { createdAt: yesterday }, { createdAt: twoDaysAgo },
            ]as any);
            const result = await service.getUserProgress(userId);
            expect(result.streakDays).toBe(3);
        });

        it('should calculate streak correctly with a break', async () => {
            mockPrismaUserActivity.findMany.mockResolvedValue([
                { createdAt: today }, { createdAt: yesterday },
                // Break here (twoDaysAgo is missing)
                { createdAt: threeDaysAgo }, // This should not count towards current streak from today
            ]as any);
            const result = await service.getUserProgress(userId);
            expect(result.streakDays).toBe(2); // Only today and yesterday
        });

        it('should calculate streak correctly with multiple activities on the same day', async () => {
            mockPrismaUserActivity.findMany.mockResolvedValue([
                { createdAt: new Date(today.setHours(10,0,0,0)) },
                { createdAt: new Date(today.setHours(15,0,0,0)) },
                { createdAt: yesterday },
            ]as any);
            // Reset today for next tests if needed, or use a fresh `new Date()`
            const result = await service.getUserProgress(userId);
            expect(result.streakDays).toBe(2); // Today (multiple) and yesterday
        });

        it('should calculate streak correctly if only today has activity', async () => {
            mockPrismaUserActivity.findMany.mockResolvedValue([ { createdAt: new Date() } ]as any);
            const result = await service.getUserProgress(userId);
            expect(result.streakDays).toBe(1);
        });

        it('should handle activities older than the potential streak correctly', async () => {
            mockPrismaUserActivity.findMany.mockResolvedValue([
                { createdAt: today }, { createdAt: yesterday }, { createdAt: fiveDaysAgo }, // fiveDaysAgo breaks the streak from today
            ]as any);
            const result = await service.getUserProgress(userId);
            expect(result.streakDays).toBe(2);
        });
    });

    describe('calculateLayerProgress logic (tested via getUserProgress)', () => {
        const defaultLayers = ['jism', 'nafs', 'aql', 'qalb', 'ruh'];
        let streakSpy: jest.SpyInstance;

        beforeEach(() => {
            // Mock streak days to a fixed value as it's not the focus here
            streakSpy = jest.spyOn(AnalyticsService.prototype as any, 'calculateStreakDays').mockResolvedValue(3);
            // Minimal mocks for overall module/completion counts, not relevant to layerProgress assertion
            mockPrismaModuleCompletion.findMany.mockResolvedValue([]);
            mockPrismaJourneyModule.findMany.mockResolvedValue([]);
        });

        afterEach(() => {
            streakSpy.mockRestore();
        });

        it('should calculate progress for default layers when no specific layers are passed', async () => {
            mockPrismaJourneyModule.findMany
                .mockResolvedValueOnce([{ soulLayer: 'qalb' }, { soulLayer: 'qalb' }] as any) // For qalb modules
                .mockResolvedValueOnce([{ soulLayer: 'nafs' }] as any) // For nafs modules
                .mockResolvedValueOnce([]) // For aql modules
                .mockResolvedValueOnce([]) // For jism modules
                .mockResolvedValueOnce([]); // For ruh modules (order matters for mockResolvedValueOnce)

            // To ensure correct mapping in the test, we need to align mock calls with the loop in calculateLayerProgress
            // The loop is: jism, nafs, aql, qalb, ruh
            (prisma.journeyModule.findMany as jest.Mock)
                .mockImplementation(async ({ where }) => {
                    if (where.soulLayer === 'jism') return [];
                    if (where.soulLayer === 'nafs') return [{ soulLayer: 'nafs' }];
                    if (where.soulLayer === 'aql') return [];
                    if (where.soulLayer === 'qalb') return [{ soulLayer: 'qalb' }, { soulLayer: 'qalb' }];
                    if (where.soulLayer === 'ruh') return [];
                    return [];
                });

            (prisma.moduleCompletion.findMany as jest.Mock)
                .mockImplementation(async ({ where }) => {
                    if (where.soulLayer === 'jism') return [];
                    if (where.soulLayer === 'nafs') return [{ soulLayer: 'nafs' }]; // 1 completion for nafs
                    if (where.soulLayer === 'aql') return [];
                    if (where.soulLayer === 'qalb') return [{ soulLayer: 'qalb' }];    // 1 completion for qalb
                    if (where.soulLayer === 'ruh') return [];
                    return [];
                });

            const result = await service.getUserProgress(userId);
            expect(result.layerProgress).toEqual({
                jism: 0,
                nafs: 100, // 1 completed / 1 total
                aql: 0,
                qalb: 50,  // 1 completed / 2 total
                ruh: 0,
            });
        });

        it('should calculate progress for a specific subset of layers', async () => {
            (prisma.journeyModule.findMany as jest.Mock)
                .mockImplementation(async ({ where }) => {
                    if (where.soulLayer === 'qalb') return [{id: 'm1'}, {id: 'm2'}]; // 2 total qalb modules
                    return [];
                });
            (prisma.moduleCompletion.findMany as jest.Mock)
                .mockImplementation(async ({ where }) => {
                    if (where.soulLayer === 'qalb') return [{id: 'c1'}]; // 1 completed qalb module
                    return [];
                });

            const result = await service.getUserProgress(userId, 'month', ['qalb']);
            expect(result.layerProgress).toEqual({ qalb: 50 });
        });

        it('should return 0 progress for a layer with no assigned modules', async () => {
            (prisma.journeyModule.findMany as jest.Mock).mockImplementation(async ({where}) => {
                if(where.soulLayer === 'jism') return []; // No jism modules
                return [];
            });
            (prisma.moduleCompletion.findMany as jest.Mock).mockImplementation(async () => []);


            const result = await service.getUserProgress(userId, 'month', ['jism']);
            expect(result.layerProgress).toEqual({ jism: 0 });
        });

        it('should calculate partial completion for a layer', async () => {
            (prisma.journeyModule.findMany as jest.Mock).mockImplementation(async ({where}) => {
                if(where.soulLayer === 'nafs') return [{}, {}, {}, {}]; // 4 nafs modules
                return [];
            });
            (prisma.moduleCompletion.findMany as jest.Mock).mockImplementation(async ({where}) => {
                if(where.soulLayer === 'nafs') return [{}]; // 1 completed nafs module
                return [];
            });

            const result = await service.getUserProgress(userId, 'month', ['nafs']);
            expect(result.layerProgress).toEqual({ nafs: 25 }); // 1/4 = 25%
        });

        it('should calculate full completion for a layer', async () => {
            (prisma.journeyModule.findMany as jest.Mock).mockImplementation(async ({where}) => {
                if(where.soulLayer === 'ruh') return [{}, {}]; // 2 ruh modules
                return [];
            });
             (prisma.moduleCompletion.findMany as jest.Mock).mockImplementation(async ({where}) => {
                if(where.soulLayer === 'ruh') return [{}, {}]; // 2 completed ruh modules
                return [];
            });

            const result = await service.getUserProgress(userId, 'month', ['ruh']);
            expect(result.layerProgress).toEqual({ ruh: 100 });
        });
    });

    it('should handle zero total modules correctly (to avoid division by zero)', async () => {
      mockPrismaJourneyModule.findMany.mockResolvedValue([]); // No modules assigned
      mockPrismaModuleCompletion.findMany.mockResolvedValue([]); // No completions

      // Recalculate mocks for private methods based on zero modules if their logic depends on it,
      // but for this test, fixed mocks are fine to isolate getUserProgress logic.
      calculateLayerProgressSpy.mockResolvedValue({});


      const result = await service.getUserProgress(userId);

      expect(result.totalModules).toBe(0);
      expect(result.completedModules).toBe(0);
      expect(result.completionRate).toBe(0);
      expect(result.averageScore).toBe(0);
      expect(result.layerProgress).toEqual({});
    });

    it('should handle zero completions correctly for averageScore', async () => {
        mockPrismaJourneyModule.findMany.mockResolvedValue(mockTotalModules as any); // Has modules
        mockPrismaModuleCompletion.findMany.mockResolvedValue([]); // No completions

        const result = await service.getUserProgress(userId);

        expect(result.totalModules).toBe(4);
        expect(result.completedModules).toBe(0);
        expect(result.completionRate).toBe(0);
        expect(result.averageScore).toBe(0); // Important: average score should be 0
      });

    it('should throw AppError if a Prisma call fails', async () => {
      const errorMessage = 'Prisma findMany failed';
      mockPrismaModuleCompletion.findMany.mockRejectedValue(new Error(errorMessage));

      await expect(service.getUserProgress(userId)).rejects.toThrow(AppError);
      await expect(service.getUserProgress(userId)).rejects.toThrow('Failed to get user progress');
      expect(logger.error).toHaveBeenCalledWith(
        'Error getting user progress',
        expect.objectContaining({ userId, error: errorMessage })
      );
    });
  });

  describe('generateInsights', () => {
    const userId = 'user-insights-123';
    const mockProgressData: any = { // Using 'any' for brevity, should match ProgressMetrics
      completionRate: 75, totalModules: 4, completedModules: 3, averageScore: 80,
      streakDays: 5, layerProgress: { qalb: 75, nafs: 100 }
    };
    const mockRecentActivities = [{ activityType: 'module_completed', createdAt: new Date() }];
    const mockAiInsights = {
      strengths: ['Good consistency'],
      improvements: ['Focus more on Aql layer'],
      recommendations: ['Read Surah Yasin'],
      spiritualGrowth: 0.6,
      consistencyScore: 0.8,
    };
    const mockIslamicRecommendations = ['Perform extra Sunnah prayers'];

    let getIslamicRecommendationsSpy: jest.SpyInstance;

    beforeEach(() => {
      mockPrismaUserActivity.findMany.mockResolvedValue(mockRecentActivities as any);
      mockTriggerN8nWorkflow.mockResolvedValue(mockAiInsights);
      getIslamicRecommendationsSpy = jest.spyOn(AnalyticsService.prototype as any, 'getIslamicRecommendations')
        .mockResolvedValue(mockIslamicRecommendations);
    });

    afterEach(() => {
      getIslamicRecommendationsSpy.mockRestore();
    });

    it('should generate insights successfully', async () => {
      const result = await service.generateInsights(userId, mockProgressData);

      expect(mockPrismaUserActivity.findMany).toHaveBeenCalledWith({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 50,
      });
      expect(mockTriggerN8nWorkflow).toHaveBeenCalledWith(
        'generate-insights',
        expect.objectContaining({
          userId,
          progressData: mockProgressData,
          recentActivities: mockRecentActivities,
        })
      );
      expect(getIslamicRecommendationsSpy).toHaveBeenCalledWith(mockProgressData);

      expect(result.strengths).toEqual(mockAiInsights.strengths);
      expect(result.improvements).toEqual(mockAiInsights.improvements);
      expect(result.recommendations).toEqual([...mockAiInsights.recommendations, ...mockIslamicRecommendations]);
      expect(result.spiritualGrowth).toBe(mockAiInsights.spiritualGrowth);
      expect(result.consistencyScore).toBe(mockAiInsights.consistencyScore);
      expect(logger.info).toHaveBeenCalledWith('User insights generated', expect.any(Object));
    });

    it('should handle empty AI insights gracefully', async () => {
        mockTriggerN8nWorkflow.mockResolvedValue({}); // Empty AI response
        const result = await service.generateInsights(userId, mockProgressData);

        expect(result.strengths).toEqual([]);
        expect(result.improvements).toEqual([]);
        // Recommendations will still have Islamic ones if progressData leads to them
        expect(result.recommendations).toEqual(mockIslamicRecommendations);
        expect(result.spiritualGrowth).toBe(0);
        expect(result.consistencyScore).toBe(0);
      });

    it('should throw AppError if triggerN8nWorkflow fails', async () => {
      const errorMessage = 'N8N workflow failed';
      mockTriggerN8nWorkflow.mockRejectedValue(new Error(errorMessage));

      await expect(service.generateInsights(userId, mockProgressData)).rejects.toThrow(AppError);
      await expect(service.generateInsights(userId, mockProgressData)).rejects.toThrow('Failed to generate insights');
      expect(logger.error).toHaveBeenCalledWith(
        'Error generating insights',
        expect.objectContaining({ userId, error: errorMessage })
      );
    });

    it('should throw AppError if prisma.userActivity.findMany fails', async () => {
        const errorMessage = 'Prisma findMany for activities failed';
        mockPrismaUserActivity.findMany.mockRejectedValue(new Error(errorMessage));

        await expect(service.generateInsights(userId, mockProgressData)).rejects.toThrow(AppError);
        await expect(service.generateInsights(userId, mockProgressData)).rejects.toThrow('Failed to generate insights');
      });

    describe('getIslamicRecommendations logic (tested via generateInsights)', () => {
      beforeEach(() => {
        // Isolate Islamic recommendations by returning minimal AI insights
        mockTriggerN8nWorkflow.mockResolvedValue({
            recommendations: [], // No AI recommendations
            strengths: [], improvements: [], spiritualGrowth: 0, consistencyScore: 0
        });
        // User activities not relevant for this specific logic test part
        mockPrismaUserActivity.findMany.mockResolvedValue([]);
      });

      it('should include dhikr routine recommendation if streakDays < 7', async () => {
        const lowStreakProgress: any = { ...mockProgressData, streakDays: 3, layerProgress: {} };
        const result = await service.generateInsights(userId, lowStreakProgress);
        expect(result.recommendations).toContain('Establish daily dhikr routine for spiritual consistency');
      });

      it('should NOT include dhikr routine recommendation if streakDays >= 7', async () => {
        const highStreakProgress: any = { ...mockProgressData, streakDays: 10, layerProgress: {} };
        const result = await service.generateInsights(userId, highStreakProgress);
        expect(result.recommendations).not.toContain('Establish daily dhikr routine for spiritual consistency');
      });

      it('should include jism recommendation if jism progress < 50', async () => {
        const lowJismProgress: any = { ...mockProgressData, layerProgress: { jism: 30 } };
        const result = await service.generateInsights(userId, lowJismProgress);
        expect(result.recommendations).toContain('Focus on Prophetic medicine and physical wellness');
      });

      it('should include nafs recommendation if nafs progress < 50', async () => {
        const lowNafsProgress: any = { ...mockProgressData, layerProgress: { nafs: 20 } };
        const result = await service.generateInsights(userId, lowNafsProgress);
        expect(result.recommendations).toContain('Increase istighfar and nafs purification practices');
      });

      it('should include aql recommendation if aql progress < 50', async () => {
        const lowAqlProgress: any = { ...mockProgressData, layerProgress: { aql: 40 } };
        const result = await service.generateInsights(userId, lowAqlProgress);
        expect(result.recommendations).toContain('Engage in Quranic reflection and Islamic learning');
      });

      it('should include qalb recommendation if qalb progress < 50', async () => {
        const lowQalbProgress: any = { ...mockProgressData, layerProgress: { qalb: 10 } };
        const result = await service.generateInsights(userId, lowQalbProgress);
        expect(result.recommendations).toContain('Practice heart-centered dhikr and tawbah');
      });

      it('should include ruh recommendation if ruh progress < 50', async () => {
        const lowRuhProgress: any = { ...mockProgressData, layerProgress: { ruh: 45 } };
        const result = await service.generateInsights(userId, lowRuhProgress);
        expect(result.recommendations).toContain('Strengthen connection with Allah through worship');
      });

      it('should include multiple layer recommendations if applicable', async () => {
        const lowMultiLayerProgress: any = { ...mockProgressData, layerProgress: { jism: 30, nafs: 25, qalb: 60 } };
        const result = await service.generateInsights(userId, lowMultiLayerProgress);
        expect(result.recommendations).toContain('Focus on Prophetic medicine and physical wellness');
        expect(result.recommendations).toContain('Increase istighfar and nafs purification practices');
        expect(result.recommendations).not.toContain('Practice heart-centered dhikr and tawbah');
      });

      it('should not include layer recommendations if progress is >= 50', async () => {
        const highLayerProgress: any = { ...mockProgressData, layerProgress: { jism: 70, nafs: 50, aql: 90, qalb: 80, ruh: 60 } };
        const result = await service.generateInsights(userId, highLayerProgress);
        // Assuming streakDays might still be low for this test, so only check layer recs
        const nonDhikrRecommendations = result.recommendations.filter(rec => rec !== 'Establish daily dhikr routine for spiritual consistency');
        expect(nonDhikrRecommendations).toEqual([]);
      });

      it('should return only AI recommendations if all Islamic conditions are met (high streak, high layer progress)', async () => {
        mockTriggerN8nWorkflow.mockResolvedValue({ recommendations: ['AI Rec 1'], strengths: [], improvements: [], spiritualGrowth: 0, consistencyScore: 0 });
        const allGoodProgress: any = {
            ...mockProgressData,
            streakDays: 10,
            layerProgress: { jism: 80, nafs: 80, aql: 80, qalb: 80, ruh: 80 }
        };
        const result = await service.generateInsights(userId, allGoodProgress);
        expect(result.recommendations).toEqual(['AI Rec 1']);
      });

      it('should combine AI recommendations with multiple Islamic recommendations', async () => {
        mockTriggerN8nWorkflow.mockResolvedValue({
            recommendations: ['AI Recommendation Alpha'],
            strengths: [], improvements: [], spiritualGrowth: 0, consistencyScore: 0
        });
        const progressWithMultipleNeeds: any = {
          ...mockProgressData,
          streakDays: 2, // Low streak
          layerProgress: { jism: 30, nafs: 40, aql: 80, qalb: 20, ruh: 90 } // Low jism, nafs, qalb
        };
        const result = await service.generateInsights(userId, progressWithMultipleNeeds);
        expect(result.recommendations).toEqual(expect.arrayContaining([
          'AI Recommendation Alpha',
          'Establish daily dhikr routine for spiritual consistency',
          'Focus on Prophetic medicine and physical wellness',
          'Increase istighfar and nafs purification practices',
          'Practice heart-centered dhikr and tawbah'
        ]));
        expect(result.recommendations.length).toBe(5); // AI (1) + Streak (1) + 3 Layers (3)
      });

      it('should only include relevant Islamic recommendations', async () => {
        mockTriggerN8nWorkflow.mockResolvedValue({
            recommendations: ['Another AI Rec'],
            strengths: [], improvements: [], spiritualGrowth: 0, consistencyScore: 0
        });
        const progressWithSpecificNeeds: any = {
          ...mockProgressData,
          streakDays: 10, // High streak (no streak rec)
          layerProgress: { jism: 80, nafs: 80, aql: 30, qalb: 80, ruh: 25 } // Low aql, ruh
        };
        const result = await service.generateInsights(userId, progressWithSpecificNeeds);
        expect(result.recommendations).toEqual(expect.arrayContaining([
          'Another AI Rec',
          'Engage in Quranic reflection and Islamic learning', // Aql
          'Strengthen connection with Allah through worship'  // Ruh
        ]));
        expect(result.recommendations.length).toBe(3); // AI (1) + 2 Layers (2)
        expect(result.recommendations).not.toContain('Establish daily dhikr routine for spiritual consistency');
      });
    });
  });

  describe('getDashboardAnalytics', () => {
    const userId = 'user-dashboard-123';
    const timeframe = 'week';
    const mockProgress: any = { completionRate: 50, totalModules: 10, completedModules: 5, averageScore: 75, streakDays: 3, layerProgress: {} };
    const mockInsights: any = { strengths: ['Resilience'], improvements: [], recommendations: ['More Salah'], spiritualGrowth: 0.5, consistencyScore: 0.7 };
    const mockTrends = [{ date: '2023-01-01', mood: 7 }];
    const mockMilestones = [{ id: 'milestone1', name: 'First Week Complete', achievedAt: new Date() }];

    let getUserProgressSpy: jest.SpyInstance;
    let generateInsightsSpy: jest.SpyInstance;
    let getTrendsSpy: jest.SpyInstance;
    let getMilestonesSpy: jest.SpyInstance;

    beforeEach(() => {
      getUserProgressSpy = jest.spyOn(service, 'getUserProgress').mockResolvedValue(mockProgress);
      generateInsightsSpy = jest.spyOn(service, 'generateInsights').mockResolvedValue(mockInsights);
      getTrendsSpy = jest.spyOn(AnalyticsService.prototype as any, 'getTrends').mockResolvedValue(mockTrends);
      getMilestonesSpy = jest.spyOn(AnalyticsService.prototype as any, 'getMilestones').mockResolvedValue(mockMilestones);
    });

    afterEach(() => {
      getUserProgressSpy.mockRestore();
      generateInsightsSpy.mockRestore();
      getTrendsSpy.mockRestore();
      getMilestonesSpy.mockRestore();
    });

    it('should retrieve and assemble all analytics components successfully', async () => {
      const result = await service.getDashboardAnalytics(userId, timeframe);

      expect(getUserProgressSpy).toHaveBeenCalledWith(userId, timeframe);
      expect(generateInsightsSpy).toHaveBeenCalledWith(userId, mockProgress);
      expect(getTrendsSpy).toHaveBeenCalledWith(userId, timeframe);
      expect(getMilestonesSpy).toHaveBeenCalledWith(userId);

      expect(result).toEqual({
        progress: mockProgress,
        insights: mockInsights,
        trends: mockTrends,
        milestones: mockMilestones,
      });
      expect(logger.info).toHaveBeenCalledWith('getDashboardAnalytics - ENTRY', { userId, timeframe });
      expect(logger.info).toHaveBeenCalledWith('getDashboardAnalytics - EXIT (Success)', { userId, timeframe });
    });

    it('should throw AppError if getUserProgress fails', async () => {
      const errorMessage = 'getUserProgress failed';
      getUserProgressSpy.mockRejectedValue(new Error(errorMessage));

      await expect(service.getDashboardAnalytics(userId, timeframe)).rejects.toThrow(AppError);
      await expect(service.getDashboardAnalytics(userId, timeframe)).rejects.toThrow('Failed to get dashboard analytics');
      expect(logger.error).toHaveBeenCalledWith(
        'Error in getDashboardAnalytics - EXIT (Error)',
        expect.objectContaining({ userId, timeframe, error: errorMessage })
      );
    });

    it('should throw AppError if generateInsights fails', async () => {
      const errorMessage = 'generateInsights failed';
      generateInsightsSpy.mockRejectedValue(new Error(errorMessage));

      await expect(service.getDashboardAnalytics(userId, timeframe)).rejects.toThrow(AppError);
      await expect(service.getDashboardAnalytics(userId, timeframe)).rejects.toThrow('Failed to get dashboard analytics');
    });

    // Similar tests can be added if getTrends or getMilestones were to throw errors,
    // though they are currently simple private methods returning empty arrays.
  });
});
