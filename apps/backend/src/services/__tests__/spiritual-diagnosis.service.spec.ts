import { Test, TestingModule } from '@nestjs/testing';
import { SpiritualDiagnosisService, spiritualDiagnosisService as spiritualDiagnosisServiceInstance } from '../spiritual-diagnosis.service';
import { logger } from '../../utils/logger';

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock dependencies if any were used in the actual service constructor or methods
// For example, if prisma or aiService were used:
import { prisma, SpiritualDiagnosis as PrismaSpiritualDiagnosis, Prisma } from '../../config/database'; // Import type for mocking and Prisma namespace
import { AppError } from '../../middleware/errorHandler';

jest.mock('../../config/database', () => ({
  prisma: {
    spiritualDiagnosis: {
      findUnique: jest.fn(),
      upsert: jest.fn(),
      findMany: jest.fn(),
      delete: jest.fn(), // Added delete to mock
    },
  },
  // Mock Prisma namespace for error types
  Prisma: {
    PrismaClientKnownRequestError: class PrismaClientKnownRequestError extends Error {
        code: string;
        meta?: object;
        constructor(message: string, code: string, meta?:object) {
            super(message);
            this.code = code;
            this.meta = meta;
        }
    },
  }
}));
// jest.mock('../ai.service', () => ({ aiService: {} })); // Mock aiService if used

describe('SpiritualDiagnosisService', () => {
  let service: SpiritualDiagnosisService;
  let mockPrismaSpiritualDiagnosis: jest.Mocked<typeof prisma.spiritualDiagnosis>;


  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SpiritualDiagnosisService],
    }).compile();

    service = module.get<SpiritualDiagnosisService>(SpiritualDiagnosisService);
    mockPrismaSpiritualDiagnosis = prisma.spiritualDiagnosis as jest.Mocked<typeof prisma.spiritualDiagnosis>;
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('service instance should be defined', () => {
    expect(spiritualDiagnosisServiceInstance).toBeDefined();
  });

  describe('getDiagnosisById', () => {
    const diagnosisId = 'test-diag-id';
    const mockDiagnosisData: PrismaSpiritualDiagnosis = {
      id: diagnosisId,
      userId: 'user-123',
      assessmentSessionId: 'session-123',
      primaryLayer: 'qalb',
      secondaryLayers: ['nafs'],
      overallSeverity: 'moderate',
      crisisLevel: 'low',
      confidence: 0.85,
      recommendedJourneyType: 'heart_purification',
      estimatedHealingDuration: '21 days',
      diagnosisData: { summary: 'Test summary' } as any, // Prisma.JsonValue
      layerAnalysesData: { qalb_analysis: {} } as any, // Prisma.JsonValue
      nextSteps: ['Dhikr', 'Quran'],
      generatedAt: new Date(),
      // Add any other required fields from your PrismaSpiritualDiagnosis model
      // For example, if you have createdAt, updatedAt, etc.
      // createdAt: new Date(),
      // updatedAt: new Date(),
      // isCurrent: true,
    };

    it('should return a diagnosis if found', async () => {
      mockPrismaSpiritualDiagnosis.findUnique.mockResolvedValue(mockDiagnosisData);

      const result = await service.getDiagnosisById(diagnosisId);

      expect(logger.info).toHaveBeenCalledWith(`Fetching diagnosis with ID: ${diagnosisId}`);
      expect(mockPrismaSpiritualDiagnosis.findUnique).toHaveBeenCalledWith({ where: { id: diagnosisId } });
      expect(result).toEqual(mockDiagnosisData);
    });

    it('should return null if diagnosis is not found', async () => {
      mockPrismaSpiritualDiagnosis.findUnique.mockResolvedValue(null);

      const result = await service.getDiagnosisById(diagnosisId);

      expect(logger.warn).toHaveBeenCalledWith(`Spiritual diagnosis not found for ID: ${diagnosisId}`);
      expect(result).toBeNull();
    });

    it('should throw AppError if Prisma call fails', async () => {
      const errorMessage = 'Prisma findUnique failed';
      mockPrismaSpiritualDiagnosis.findUnique.mockRejectedValue(new Error(errorMessage));

      await expect(service.getDiagnosisById(diagnosisId)).rejects.toThrow(AppError);
      await expect(service.getDiagnosisById(diagnosisId)).rejects.toThrow('Failed to retrieve spiritual diagnosis.');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error fetching spiritual diagnosis for ID ${diagnosisId}: ${errorMessage}`),
        expect.any(Object) // For the stack trace
      );
    });
  });

  describe('deleteDiagnosis', () => {
    const diagnosisId = 'diag-to-delete-123';
    const mockDeletedDiagnosis: PrismaSpiritualDiagnosis = { // Using a structure similar to mockUpsertedDiagnosis
      id: diagnosisId,
      userId: 'user-for-deleted-diag',
      assessmentSessionId: 'session-for-deleted-diag',
      primaryLayer: 'qalb',
      secondaryLayers: [],
      overallSeverity: 'low',
      crisisLevel: 'none',
      confidence: 0.99,
      recommendedJourneyType: 'maintenance',
      estimatedHealingDuration: null,
      diagnosisData: {} as any,
      layerAnalysesData: {} as any,
      nextSteps: [],
      generatedAt: new Date(),
    };

    it('should successfully delete a diagnosis and return it', async () => {
      mockPrismaSpiritualDiagnosis.delete.mockResolvedValue(mockDeletedDiagnosis);

      const result = await service.deleteDiagnosis(diagnosisId);

      expect(mockPrismaSpiritualDiagnosis.delete).toHaveBeenCalledWith({
        where: { id: diagnosisId },
      });
      expect(result).toEqual(mockDeletedDiagnosis);
      expect(logger.info).toHaveBeenCalledWith(`Attempting to delete spiritual diagnosis with ID: ${diagnosisId}`);
      expect(logger.info).toHaveBeenCalledWith(`Successfully deleted spiritual diagnosis with ID: ${diagnosisId}`);
    });

    it('should throw AppError with 404 if diagnosis to delete is not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        'Record to delete not found', 'P2025', { modelName: 'SpiritualDiagnosis' }
      );
      mockPrismaSpiritualDiagnosis.delete.mockRejectedValue(prismaError);

      await expect(service.deleteDiagnosis(diagnosisId)).rejects.toThrow(AppError);
      try {
        await service.deleteDiagnosis(diagnosisId);
      } catch (e: any) {
        expect(e.statusCode).toBe(404);
        expect(e.message).toBe('Spiritual diagnosis not found for deletion.');
      }
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error deleting spiritual diagnosis ID ${diagnosisId}`),
        expect.any(Object)
      );
    });

    it('should throw generic AppError for other Prisma errors during delete', async () => {
      const errorMessage = 'Prisma delete failed for other reason';
      mockPrismaSpiritualDiagnosis.delete.mockRejectedValue(new Error(errorMessage));

      await expect(service.deleteDiagnosis(diagnosisId)).rejects.toThrow(AppError);
      await expect(service.deleteDiagnosis(diagnosisId)).rejects.toThrow('Failed to delete spiritual diagnosis.');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error deleting spiritual diagnosis ID ${diagnosisId}: ${errorMessage}`),
        expect.any(Object)
      );
    });
  });

  describe('getDiagnosesForUser', () => {
    const userId = 'user-get-diagnoses-123';
    const mockDiagnosesArray: PrismaSpiritualDiagnosis[] = [
      { ...mockUpsertedDiagnosis, id: 'diag1', assessmentSessionId: 'session1' }, // Using mockUpsertedDiagnosis structure
      { ...mockUpsertedDiagnosis, id: 'diag2', assessmentSessionId: 'session2', primaryLayer: 'nafs' },
    ];

    it('should return an array of diagnoses if found for the user', async () => {
      mockPrismaSpiritualDiagnosis.findMany.mockResolvedValue(mockDiagnosesArray);

      const result = await service.getDiagnosesForUser(userId);

      expect(logger.info).toHaveBeenCalledWith(`Fetching all diagnoses for user ID: ${userId}`);
      expect(mockPrismaSpiritualDiagnosis.findMany).toHaveBeenCalledWith({
        where: { userId },
        orderBy: { generatedAt: 'desc' },
      });
      expect(result).toEqual(mockDiagnosesArray);
      expect(result.length).toBe(2);
    });

    it('should return an empty array if no diagnoses are found for the user', async () => {
      mockPrismaSpiritualDiagnosis.findMany.mockResolvedValue([]);

      const result = await service.getDiagnosesForUser(userId);

      expect(mockPrismaSpiritualDiagnosis.findMany).toHaveBeenCalledWith({
        where: { userId },
        orderBy: { generatedAt: 'desc' },
      });
      expect(result).toEqual([]);
    });

    it('should throw AppError if Prisma call fails', async () => {
      const errorMessage = 'Prisma findMany failed for diagnoses';
      mockPrismaSpiritualDiagnosis.findMany.mockRejectedValue(new Error(errorMessage));

      await expect(service.getDiagnosesForUser(userId)).rejects.toThrow(AppError);
      await expect(service.getDiagnosesForUser(userId)).rejects.toThrow('Failed to retrieve diagnoses for user.');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error fetching diagnoses for user ${userId}: ${errorMessage}`),
        expect.any(Object)
      );
    });
  });

  // Add describe blocks for other methods as they are implemented in the service.

  describe('createOrUpdateDiagnosis', () => {
    const userId = 'user-create-diag-123';
    const assessmentSessionId = 'session-create-diag-456';

    const fullDiagnosisInput: Partial<PrismaSpiritualDiagnosis> = {
      primaryLayer: 'qalb',
      secondaryLayers: ['nafs'],
      overallSeverity: 'high',
      crisisLevel: 'moderate',
      confidence: 0.9,
      recommendedJourneyType: 'intensive_qalb_purification',
      estimatedHealingDuration: '40 days',
      diagnosisData: { detail: "Comprehensive AI output" } as any,
      layerAnalysesData: { qalb: "Qalb analysis details" } as any,
      nextSteps: ['Follow journey plan', 'Engage with community'],
      generatedAt: new Date('2023-10-01T10:00:00.000Z'), // Specific date for consistent testing
    };

    const mockBaseDiagnosis: Omit<PrismaSpiritualDiagnosis, 'id'> = { // Base for constructing expected values
        userId,
        assessmentSessionId,
        primaryLayer: 'unknown',
        secondaryLayers: [],
        overallSeverity: 'unknown',
        crisisLevel: 'low',
        confidence: 0,
        recommendedJourneyType: 'general',
        estimatedHealingDuration: null,
        diagnosisData: Prisma.JsonNull,
        layerAnalysesData: Prisma.JsonNull,
        nextSteps: [],
        generatedAt: expect.any(Date), // Will be overridden if provided in input
    };

    const mockResolvedDiagnosis: PrismaSpiritualDiagnosis = {
        id: 'diag-xyz-789', // Usually generated by DB
        ...mockBaseDiagnosis,
        // Overwrite with fullDiagnosisInput fields for a complete mock return
        ...fullDiagnosisInput,
        generatedAt: fullDiagnosisInput.generatedAt || new Date(), // Ensure generatedAt is a Date
    };


    it('should successfully create a diagnosis with all fields provided in input', async () => {
      mockPrismaSpiritualDiagnosis.upsert.mockResolvedValue(mockResolvedDiagnosis);

      const result = await service.createOrUpdateDiagnosis(userId, assessmentSessionId, fullDiagnosisInput);

      expect(mockPrismaSpiritualDiagnosis.upsert).toHaveBeenCalledWith({
        where: { assessmentSessionId_userId: { assessmentSessionId, userId } },
        create: {
          userId,
          assessmentSessionId,
          ...fullDiagnosisInput, // All fields from input should be used
        },
        update: {
          ...fullDiagnosisInput, // All fields from input for update
        },
      });
      expect(result).toEqual(mockResolvedDiagnosis);
      expect(logger.info).toHaveBeenCalledWith(`Creating or updating spiritual diagnosis for user: ${userId}, session: ${assessmentSessionId}`);
    });

    it('should successfully create a diagnosis using defaults for sparse input', async () => {
        const sparseInput: Partial<PrismaSpiritualDiagnosis> = {
          primaryLayer: 'nafs',
          // Many fields missing, expecting defaults to be applied
        };
        const expectedCreateObject = {
            userId,
            assessmentSessionId,
            primaryLayer: 'nafs', // From sparseInput
            secondaryLayers: [],   // Default
            overallSeverity: 'unknown', // Default
            crisisLevel: 'low',       // Default
            confidence: 0,            // Default
            recommendedJourneyType: 'general', // Default
            estimatedHealingDuration: null,    // Default
            diagnosisData: Prisma.JsonNull,  // Default
            layerAnalysesData: Prisma.JsonNull, // Default
            nextSteps: [],             // Default
            generatedAt: expect.any(Date), // Default (or from sparseInput if provided)
            ...sparseInput, // Spread sparseInput again to ensure its values take precedence if they overlap with defaults
        };
        const mockReturnedDiagnosisFromSparse = { ...mockBaseDiagnosis, ...expectedCreateObject, id: 'diag-sparse-123', generatedAt: expectedCreateObject.generatedAt };
        mockPrismaSpiritualDiagnosis.upsert.mockResolvedValue(mockReturnedDiagnosisFromSparse);

        const result = await service.createOrUpdateDiagnosis(userId, assessmentSessionId, sparseInput);

        expect(mockPrismaSpiritualDiagnosis.upsert).toHaveBeenCalledWith({
            where: { assessmentSessionId_userId: { assessmentSessionId, userId } },
            create: expectedCreateObject,
            update: sparseInput,
        });
        expect(result).toEqual(mockReturnedDiagnosisFromSparse);
    });


    it('should throw AppError with 409 for unique constraint violation (P2002) during upsert', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        'Unique constraint failed', 'P2002', { target: ['some_field'] }
      );
      mockPrismaSpiritualDiagnosis.upsert.mockRejectedValue(prismaError);

      await expect(service.createOrUpdateDiagnosis(userId, assessmentSessionId, diagnosisInput))
        .rejects.toThrow(AppError);
      try {
        await service.createOrUpdateDiagnosis(userId, assessmentSessionId, diagnosisInput);
      } catch (e: any) {
        expect(e.statusCode).toBe(409);
        expect(e.message).toContain('Diagnosis creation/update failed due to a unique constraint. Fields: some_field');
      }
    });

    it('should throw generic AppError for other Prisma errors during upsert', async () => {
      const errorMessage = 'Prisma upsert failed for other reason';
      mockPrismaSpiritualDiagnosis.upsert.mockRejectedValue(new Error(errorMessage));

      await expect(service.createOrUpdateDiagnosis(userId, assessmentSessionId, diagnosisInput))
        .rejects.toThrow(AppError);
      await expect(service.createOrUpdateDiagnosis(userId, assessmentSessionId, diagnosisInput))
        .rejects.toThrow('Failed to create or update spiritual diagnosis.');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error creating/updating spiritual diagnosis for user ${userId}: ${errorMessage}`),
        expect.any(Object)
      );
    });
  });
});
