import { prisma } from '../../config/database';
import { assessmentService, AssessmentService, DiagnosisFeedbackData } from '../assessment.service';
import { aiService } from '../ai.service';
import { crisisDetectionService } from '../crisis-detection.service';
import { AppError } from '../../middleware/errorHandler';
import { ASSESSMENT_STEPS, PersonalizedWelcome } from '../../models/Assessment';

// Mock Prisma
jest.mock('../../config/database', () => ({
  prisma: {
    profile: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    assessmentSession: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
    },
    spiritualDiagnosis: {
      create: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
    diagnosisFeedback: {
      create: jest.fn(),
    },
    assessmentQuestion: {
      findMany: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

// Mock AI Service
jest.mock('../ai.service', () => ({
  aiService: {
    getAIPersonalizedWelcome: jest.fn(),
    analyzeSpiritualLandscape: jest.fn(),
  },
}));

// Mock Crisis Detection Service
jest.mock('../crisis-detection.service', () => ({
  crisisDetectionService: {
    analyzeResponse: jest.fn(),
  },
}));

// Mock User Profile Helper (though some of its functions are tested separately)
// We mock it here if the service uses it directly and we want to control its output in service tests
jest.mock('../../utils/assessmentUserProfile.helper', () => ({
  prepareProfileForAI: jest.fn((profile, userId) => ({ user_id: userId, ...profile })),
  determineUserTypeFromProfile: jest.fn().mockReturnValue('symptom_aware'),
  getDeliveryStyleForUserType: jest.fn().mockReturnValue('gentle'),
}));

// Mock Mappers and Presenters
jest.mock('../../mappers/aiDiagnosis.mapper', () => ({
  mapAIDiagnosisToPrismaCreateInput: jest.fn(data => ({
      diagnosisCreateData: { primaryLayer: data.primary_layer }, // Simplified mock
      layerAnalysesCreateInput: [],
  })),
}));
jest.mock('../../presenters/diagnosisDelivery.presenter', () => ({
  formatDiagnosisForDelivery: jest.fn().mockReturnValue({}), // Simplified mock
}));


describe('AssessmentService', () => {
  let serviceInstance: AssessmentService;

  beforeEach(() => {
    jest.clearAllMocks();
    serviceInstance = new AssessmentService(); // Use new AssessmentService() instead of assessmentService singleton for cleaner tests if state is involved, though here it's likely okay.
  });

  describe('startAssessment', () => {
    const userId = 'user1';
    const userEmail = '<EMAIL>';
    const userProfileJson = { type: 'testProfile' } as any;
    const mockWelcome: PersonalizedWelcome = {
      userType: 'symptom_aware',
      greeting: 'As-salamu alaykum',
      introduction: 'Let us begin.',
      assessmentFocus: 'general',
      buttons: [{ text: 'Begin', action: 'start_assessment'}]
    };

    beforeEach(() => {
      // Mock getPersonalizedWelcome directly on the service instance for these tests
      // Or ensure aiService.getAIPersonalizedWelcome is mocked appropriately
      (aiService.getAIPersonalizedWelcome as jest.Mock).mockResolvedValue(mockWelcome);
    });

    it('should create a new session if no active one exists', async () => {
      (prisma.profile.findUnique as jest.Mock).mockResolvedValue({ id: userId, email: userEmail }); // Profile exists
      (prisma.assessmentSession.findFirst as jest.Mock).mockResolvedValue(null); // No existing session
      const mockCreatedSession = { id: 'session1', userId, userProfile: userProfileJson, startedAt: new Date(), currentStep: 'welcome', totalSteps: ASSESSMENT_STEPS.length };
      (prisma.assessmentSession.create as jest.Mock).mockResolvedValue(mockCreatedSession);

      const result = await serviceInstance.startAssessment(userId, userEmail, userProfileJson);

      expect(prisma.assessmentSession.create).toHaveBeenCalledWith(expect.objectContaining({
        userId,
        userProfile: userProfileJson,
        currentStep: 'welcome',
      }));
      expect(result.session).toEqual(mockCreatedSession);
      expect(result.welcome).toEqual(mockWelcome);
      expect(aiService.getAIPersonalizedWelcome).toHaveBeenCalled();
    });

    it('should resume an existing session if an active one is found', async () => {
      (prisma.profile.findUnique as jest.Mock).mockResolvedValue({ id: userId, email: userEmail });
      const existingSession = { id: 'existingSession1', userId, userProfile: userProfileJson, completedAt: null, abandonedAt: null };
      (prisma.assessmentSession.findFirst as jest.Mock).mockResolvedValue(existingSession);

      const result = await serviceInstance.startAssessment(userId, userEmail, userProfileJson);

      expect(prisma.assessmentSession.create).not.toHaveBeenCalled();
      expect(result.session).toEqual(existingSession);
      expect(result.welcome).toEqual(mockWelcome);
      expect(aiService.getAIPersonalizedWelcome).toHaveBeenCalled();
    });

    it('should create a user profile if one does not exist by ID or email', async () => {
      (prisma.profile.findUnique as jest.Mock)
        .mockResolvedValueOnce(null) // No profile by ID
        .mockResolvedValueOnce(null); // No profile by email
      (prisma.profile.create as jest.Mock).mockResolvedValue({ id: userId, email: userEmail });
      (prisma.assessmentSession.findFirst as jest.Mock).mockResolvedValue(null);
      const mockCreatedSession = { id: 'session1', userId, userProfile: userProfileJson };
      (prisma.assessmentSession.create as jest.Mock).mockResolvedValue(mockCreatedSession);

      await serviceInstance.startAssessment(userId, userEmail, userProfileJson);

      expect(prisma.profile.create).toHaveBeenCalledWith({ data: { id: userId, email: userEmail } });
    });

    it('should use profile from DB if ID matches but email token differs (warning case)', async () => {
        const dbProfile = { id: userId, email: '<EMAIL>' };
        (prisma.profile.findUnique as jest.Mock).mockResolvedValue(dbProfile); // Profile exists by ID
        // No need to mock findByEmail as findById is sufficient
        (prisma.assessmentSession.findFirst as jest.Mock).mockResolvedValue(null);
        const mockCreatedSession = { id: 'session1', userId, userProfile: userProfileJson };
        (prisma.assessmentSession.create as jest.Mock).mockResolvedValue(mockCreatedSession);

        // spy on logger.warn
        const loggerWarnSpy = jest.spyOn(require('../../utils/logger').logger, 'warn');

        await serviceInstance.startAssessment(userId, userEmail, userProfileJson); // userEmail is '<EMAIL>'

        // This scenario should not trigger the warning because the profile ID from token matches DB.
        // The warning is for when ID in token does NOT match DB, but email does.
        // The current code: if (!userProfileInDB) { ... check by email ... }
        // If userProfileInDB IS found (our case), the email check branch is skipped.
        // So, the warning about ID mismatch won't be hit here.
        // A different test would be needed for the warning:
        // Token ID: user1, DB has no user1. Token email: emailA. DB has profile with emailA but ID user2.
        expect(loggerWarnSpy).not.toHaveBeenCalledWith(expect.stringContaining('User profile ID mismatch'));
        loggerWarnSpy.mockRestore();
    });
  });

  describe('getAssessmentQuestions', () => {
    it('should fetch and transform questions for a step', async () => {
      const mockPrismaQuestions = [
        { id: 'q1', step: 'physical', category: 'jism', title: 'Q1', assessment_symptoms: [], reflectionRequired: false, allowMultipleSelection: false, customInputAllowed: false, questionType: 'multiple_choice', isActive: true, displayOrder: 1 },
      ];
      (prisma.assessmentQuestion.findMany as jest.Mock).mockResolvedValue(mockPrismaQuestions);

      const questions = await serviceInstance.getAssessmentQuestions('session1', 'physical');

      expect(prisma.assessmentQuestion.findMany).toHaveBeenCalledWith({
        where: { step: 'physical', isActive: true },
        include: { assessment_symptoms: { where: { isActive: true }, orderBy: { displayOrder: 'asc' } } },
        orderBy: { displayOrder: 'asc' },
      });
      expect(questions).toHaveLength(1);
      expect(questions[0].id).toBe('q1');
      expect(questions[0].layer).toBe('jism'); // From _transformPrismaQuestionToClientType logic
    });
  });

  describe('submitAssessmentResponse', () => {
    const sessionId = 'session1';
    const step = 'physical_experiences';
    const responses = { q1: 'ans1' } as any;
    const timeSpent = 120;
    const mockSession = { id: sessionId, userId: 'user1', currentStep: step, totalTimeSpent: 0, timeSpentPerStep: {} } as any;

    beforeEach(() => {
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue(mockSession);
      (prisma.assessmentSession.update as jest.Mock).mockResolvedValue({}); // Default mock for updates
      (crisisDetectionService.analyzeResponse as jest.Mock).mockResolvedValue({ isCrisis: false });
      // Mock generateDiagnosis to prevent its actual execution during these tests
      jest.spyOn(serviceInstance, 'generateDiagnosis').mockResolvedValue({} as any);
    });

    it('should update session, not call generateDiagnosis if not last step', async () => {
      // Mock _getNextStepLogic to return a next step
      jest.spyOn(serviceInstance as any, '_getNextStepLogic').mockReturnValue('emotional_experiences');

      const result = await serviceInstance.submitAssessmentResponse(sessionId, step, responses, timeSpent);

      expect(prisma.assessmentSession.update).toHaveBeenCalledTimes(2); // Once for responses, once for currentStep
      expect(serviceInstance.generateDiagnosis).not.toHaveBeenCalled();
      expect(result.nextStep).toBe('emotional_experiences');
      expect(result.crisisDetected).toBe(false);
    });

    it('should call generateDiagnosis if it is the last step', async () => {
      jest.spyOn(serviceInstance as any, '_getNextStepLogic').mockReturnValue(null); // Last step

      await serviceInstance.submitAssessmentResponse(sessionId, step, responses, timeSpent);

      expect(serviceInstance.generateDiagnosis).toHaveBeenCalledWith(sessionId);
    });

    it('should handle crisis detection', async () => {
      (crisisDetectionService.analyzeResponse as jest.Mock).mockResolvedValue({ isCrisis: true, level: 'high', message: 'Crisis!' });
      jest.spyOn(serviceInstance as any, '_calculateProgress').mockReturnValue(50);


      const result = await serviceInstance.submitAssessmentResponse(sessionId, step, responses, timeSpent);

      expect(result.crisisDetected).toBe(true);
      expect(result.crisisData).toEqual(expect.objectContaining({ level: 'high' }));
      expect(serviceInstance.generateDiagnosis).not.toHaveBeenCalled();
      expect(result.nextStep).toBeNull();
    });
  });

  describe('generateDiagnosis', () => {
    const sessionId = 'diagSession1';
    const userId = 'userForDiag';
    const mockSession = {
        id: sessionId,
        userId,
        userProfile: { type: 'test' },
        completedAt: null,
        // other fields needed by _prepareAIAnalysisData
        physicalExperiences: {}, emotionalExperiences: {}, mentalExperiences: {}, spiritualExperiences: {}, reflections: {}

    } as any;
    const mockAiAnalysisResponse = { id: 'aiDiag1', primary_layer: 'qalb', layer_insights: {} } as any;
    const mockSavedDiagnosis = { id: 'dbDiag1', userId, assessmentSessionId: sessionId, primaryLayer: 'qalb' } as any;

    beforeEach(() => {
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue(mockSession);
      (aiService.analyzeSpiritualLandscape as jest.Mock).mockResolvedValue(mockAiAnalysisResponse);
      // Mock the mapper directly for this unit test
      (require('../../mappers/aiDiagnosis.mapper').mapAIDiagnosisToPrismaCreateInput as jest.Mock).mockReturnValue({
        diagnosisCreateData: { userId, assessmentSessionId: sessionId, primaryLayer: 'qalb' },
        layerAnalysesCreateInput: []
      });
      (prisma.spiritualDiagnosis.create as jest.Mock).mockResolvedValue(mockSavedDiagnosis);
      (prisma.assessmentSession.update as jest.Mock).mockResolvedValue({});

      // Mock the internal mapper from Prisma to Client model
      jest.spyOn(serviceInstance as any, 'mapPrismaDiagnosisToClient').mockReturnValue(mockSavedDiagnosis);
    });

    it('should successfully generate and save a diagnosis', async () => {
      const diagnosis = await serviceInstance.generateDiagnosis(sessionId);

      expect(aiService.analyzeSpiritualLandscape).toHaveBeenCalled();
      expect(prisma.spiritualDiagnosis.create).toHaveBeenCalled();
      expect(prisma.assessmentSession.update).toHaveBeenCalledWith(expect.objectContaining({
        where: { id: sessionId },
        data: { completedAt: expect.any(Date), currentStep: 'complete' },
      }));
      expect(diagnosis).toEqual(mockSavedDiagnosis);
    });

    it('should return existing diagnosis if session.diagnosis exists', async () => {
        const existingDiag = { ...mockSavedDiagnosis, id: 'existingDbDiag' };
        (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue({
            ...mockSession,
            diagnosis: existingDiag // Attach existing diagnosis to session mock
        });
        jest.spyOn(serviceInstance as any, 'mapPrismaDiagnosisToClient').mockReturnValue(existingDiag);


        const diagnosis = await serviceInstance.generateDiagnosis(sessionId);
        expect(aiService.analyzeSpiritualLandscape).not.toHaveBeenCalled();
        expect(prisma.spiritualDiagnosis.create).not.toHaveBeenCalled();
        expect(diagnosis).toEqual(existingDiag);
    });

    it('should throw AppError if session is missing userId', async () => {
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue({ ...mockSession, userId: null });
      await expect(serviceInstance.generateDiagnosis(sessionId)).rejects.toThrow(AppError);
    });

    it('should throw AppError if AI service fails', async () => {
      (aiService.analyzeSpiritualLandscape as jest.Mock).mockRejectedValue(new Error('AI failed'));
      await expect(serviceInstance.generateDiagnosis(sessionId)).rejects.toThrow(AppError);
    });
  });

  describe('forceGenerateDiagnosis', () => {
    const sessionId = 'forceDiagSession1';
    const userId = 'userForForceDiag';
    const mockSession = { id: sessionId, userId, userProfile: {type: 'test'}, completedAt: new Date(), physicalExperiences: {}, emotionalExperiences: {}, mentalExperiences: {}, spiritualExperiences: {}, reflections: {} } as any;
    const mockAiAnalysisResponse = { id: 'aiForceDiag1', primary_layer: 'aql', layer_insights: {} } as any;
    const mockSavedDiagnosis = { id: 'dbForceDiag1', userId, assessmentSessionId: sessionId, primaryLayer: 'aql' } as any;

    beforeEach(() => {
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue(mockSession);
      (aiService.analyzeSpiritualLandscape as jest.Mock).mockResolvedValue(mockAiAnalysisResponse);
      (require('../../mappers/aiDiagnosis.mapper').mapAIDiagnosisToPrismaCreateInput as jest.Mock).mockReturnValue({
        diagnosisCreateData: { userId, assessmentSessionId: sessionId, primaryLayer: 'aql' },
        layerAnalysesCreateInput: []
      });
      (prisma.spiritualDiagnosis.create as jest.Mock).mockResolvedValue(mockSavedDiagnosis);
      (prisma.assessmentSession.update as jest.Mock).mockResolvedValue({});
      jest.spyOn(serviceInstance as any, 'mapPrismaDiagnosisToClient').mockReturnValue(mockSavedDiagnosis);
    });

    it('should generate a new diagnosis even if one exists or session was complete', async () => {
      const diagnosis = await serviceInstance.forceGenerateDiagnosis(sessionId);
      expect(aiService.analyzeSpiritualLandscape).toHaveBeenCalled();
      expect(prisma.spiritualDiagnosis.create).toHaveBeenCalled();
      expect(diagnosis).toEqual(mockSavedDiagnosis);
    });

    it('should mark session as complete if it was not', async () => {
        (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue({...mockSession, completedAt: null, currentStep: 'some_step'});
        await serviceInstance.forceGenerateDiagnosis(sessionId);
        expect(prisma.assessmentSession.update).toHaveBeenCalledWith(expect.objectContaining({
            where: { id: sessionId },
            data: { completedAt: expect.any(Date), currentStep: 'complete' },
        }));
    });
  });

  describe('getDiagnosisDelivery', () => {
    const diagnosisId = 'diagDelivery1';
    const mockDiagnosisRecord = {
        id: diagnosisId,
        assessmentSession: { userId: 'user1', userProfile: {} },
        diagnosisData: { ai_response: { id: 'ai1' } },
        generatedAt: new Date(),
    } as any;
    const mockDelivery = { userId: 'user1', diagnosisId } as any;

    it('should return formatted diagnosis delivery', async () => {
      (prisma.spiritualDiagnosis.findUnique as jest.Mock).mockResolvedValue(mockDiagnosisRecord);
      (require('../../presenters/diagnosisDelivery.presenter').formatDiagnosisForDelivery as jest.Mock).mockReturnValue(mockDelivery);

      const result = await serviceInstance.getDiagnosisDelivery(diagnosisId);

      expect(prisma.spiritualDiagnosis.findUnique).toHaveBeenCalledWith(expect.objectContaining({ where: { id: diagnosisId } }));
      expect(require('../../presenters/diagnosisDelivery.presenter').formatDiagnosisForDelivery).toHaveBeenCalled();
      expect(result).toEqual(mockDelivery);
    });

    it('should throw AppError if diagnosis not found', async () => {
      (prisma.spiritualDiagnosis.findUnique as jest.Mock).mockResolvedValue(null);
      await expect(serviceInstance.getDiagnosisDelivery(diagnosisId)).rejects.toThrow(new AppError('Diagnosis or associated session not found', 404));
    });

    it('should throw AppError if AI response data is missing from diagnosis', async () => {
      (prisma.spiritualDiagnosis.findUnique as jest.Mock).mockResolvedValue({ ...mockDiagnosisRecord, diagnosisData: { } }); // No ai_response
      await expect(serviceInstance.getDiagnosisDelivery(diagnosisId)).rejects.toThrow(new AppError('AI response data missing from diagnosis record.', 500));
    });
  });

  describe('getAssessmentHistory', () => {
    const userId = 'historyUser1';
    const mockSessionsData = [{ id: 's1', userId, completedAt: new Date(), spiritualDiagnoses: [{id: 'd1', primaryLayer: 'qalb'}] }] as any[];
    const mockClientSession = { id: 's1', diagnosis: {id: 'd1'} } as any;

    beforeEach(() => {
        (prisma.$transaction as jest.Mock).mockResolvedValue([mockSessionsData, 1]);
        jest.spyOn(serviceInstance as any, 'mapPrismaDiagnosisToClient')
            .mockImplementation(diag => ({ ...diag, mapped: true })); // simple mock for mapPrismaDiagnosisToClient
    });

    it('should fetch and map assessment history', async () => {
      const result = await serviceInstance.getAssessmentHistory(userId, 1, 10);
      expect(prisma.$transaction).toHaveBeenCalled();
      expect(result.sessions).toHaveLength(1);
      expect(result.sessions[0].id).toBe('s1');
      expect(result.sessions[0].diagnosis).toEqual(expect.objectContaining({ id: 'd1', mapped: true }));
      expect(result.pagination.total).toBe(1);
    });
  });

  describe('submitDiagnosisFeedback', () => {
    const diagnosisId = 'feedbackDiag1';
    const userId = 'feedbackUser1';
    const feedbackData: DiagnosisFeedbackData = { userId, diagnosisId, accuracy: 5, helpfulness: 4, comments: 'Good!' };
    const mockDiagnosis = { id: diagnosisId, userId };

    it('should successfully submit feedback', async () => {
      (prisma.spiritualDiagnosis.findUnique as jest.Mock).mockResolvedValue(mockDiagnosis);
      (prisma.diagnosisFeedback.create as jest.Mock).mockResolvedValue({});

      await serviceInstance.submitDiagnosisFeedback(diagnosisId, feedbackData);

      expect(prisma.diagnosisFeedback.create).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining(feedbackData),
      }));
    });

    it('should throw AppError if diagnosis not found', async () => {
      (prisma.spiritualDiagnosis.findUnique as jest.Mock).mockResolvedValue(null);
      await expect(serviceInstance.submitDiagnosisFeedback(diagnosisId, feedbackData)).rejects.toThrow(new AppError('Diagnosis not found for feedback submission.', 404));
    });

    it('should throw AppError if user is not authorized', async () => {
      (prisma.spiritualDiagnosis.findUnique as jest.Mock).mockResolvedValue({ ...mockDiagnosis, userId: 'anotherUser' });
      await expect(serviceInstance.submitDiagnosisFeedback(diagnosisId, feedbackData)).rejects.toThrow(new AppError('User not authorized to submit feedback for this diagnosis.', 403));
    });
  });

  // Tests for getSession, updateSession, abandonAssessment, resetSession

  describe('getSession', () => {
    const sessionId = 's1';
    it('should return session with diagnosis if found', async () => {
      const mockDiag = { id: 'd1' };
      const mockSessionResult = { id: sessionId, spiritualDiagnoses: [mockDiag] };
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue(mockSessionResult);
      const session = await serviceInstance.getSession(sessionId);
      expect(session.id).toBe(sessionId);
      expect(session.diagnosis).toEqual(mockDiag);
    });
    it('should return session with null diagnosis if no diagnosis found', async () => {
      const mockSessionResult = { id: sessionId, spiritualDiagnoses: [] };
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue(mockSessionResult);
      const session = await serviceInstance.getSession(sessionId);
      expect(session.diagnosis).toBeNull();
    });
    it('should throw AppError if session not found', async () => {
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue(null);
      await expect(serviceInstance.getSession(sessionId)).rejects.toThrow(new AppError('Assessment session not found', 404));
    });
  });

  describe('updateSession', () => {
    const sessionId = 's1';
    const userId = 'user1';
    const updateData = { currentStep: 'new_step' } as any;
    const mockSession = { id: sessionId, userId };

    it('should update session successfully', async () => {
      jest.spyOn(serviceInstance, 'getSession').mockResolvedValue(mockSession as any);
      (prisma.assessmentSession.update as jest.Mock).mockResolvedValue({ ...mockSession, ...updateData });

      const result = await serviceInstance.updateSession(sessionId, userId, updateData);

      expect(prisma.assessmentSession.update).toHaveBeenCalledWith({
        where: { id: sessionId },
        data: updateData,
      });
      expect(result.currentStep).toBe('new_step');
    });

    it('should throw AppError if user does not own session', async () => {
      jest.spyOn(serviceInstance, 'getSession').mockResolvedValue({ ...mockSession, userId: 'anotherUser' } as any);
      await expect(serviceInstance.updateSession(sessionId, userId, updateData)).rejects.toThrow(new AppError('Access denied. User does not own this session.', 403));
    });
  });

  describe('abandonAssessment', () => {
    const sessionId = 's1';
    const userId = 'user1';
    const mockSession = { id: sessionId, userId };

    it('should mark session as abandoned', async () => {
      jest.spyOn(serviceInstance, 'getSession').mockResolvedValue(mockSession as any);
      (prisma.assessmentSession.update as jest.Mock).mockResolvedValue({});

      await serviceInstance.abandonAssessment(sessionId, userId, 'test reason');

      expect(prisma.assessmentSession.update).toHaveBeenCalledWith({
        where: { id: sessionId },
        data: { abandonedAt: expect.any(Date), abandonmentReason: 'test reason' },
      });
    });
  });

  describe('resetSession', () => {
    const sessionId = 's1';
    it('should reset session fields and delete diagnoses', async () => {
      (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue({ id: sessionId });
      (prisma.spiritualDiagnosis.deleteMany as jest.Mock).mockResolvedValue({ count: 1 });
      (prisma.assessmentSession.update as jest.Mock).mockResolvedValue({});

      await serviceInstance.resetSession(sessionId);

      expect(prisma.spiritualDiagnosis.deleteMany).toHaveBeenCalledWith({ where: { assessmentSessionId: sessionId } });
      expect(prisma.assessmentSession.update).toHaveBeenCalledWith(expect.objectContaining({
        where: { id: sessionId },
        data: expect.objectContaining({ completedAt: null, currentStep: 'welcome' }),
      }));
    });
    it('should throw AppError if session not found for reset', async () => {
        (prisma.assessmentSession.findUnique as jest.Mock).mockResolvedValue(null);
        await expect(serviceInstance.resetSession(sessionId)).rejects.toThrow(new AppError('Session not found for reset', 404));
    });
  });
});
