/**
 * Journey Service Unit Tests
 * Tests journey service with mocked dependencies
 */
import {
  JourneyService,
  journeyService as journeyServiceInstance,
} from '../journey.service'; // Import instance for spy
import { prisma } from '../../config/database'; // Import the actual instance to be mocked
import { aiService } from '../ai.service';
// import { assessmentService } from '../assessment.service'; // Not directly used by methods under test for now
import { AppError } from '../../middleware/errorHandler';
import {
  JourneyStatus,
  JourneyType,
  LayerFocus,
  PracticeType,
} from '@prisma/client'; // Import enums

// Mock the prisma client completely
jest.mock('../../config/database', () => ({
  prisma: {
    profile: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    spiritualDiagnoses: {
      findUnique: jest.fn(),
    },
    journey: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
    journeyDay: {
      create: jest.fn(),
    },
    dailyPracticeInJourney: {
      create: jest.fn(),
      findMany: jest.fn(), // Added for getJourneyAnalytics
    },
    nameOfAllahContent: {
      findUnique: jest.fn(),
    },
    quranicVerseContent: {
      findUnique: jest.fn(),
    },
    sunnahPracticeContent: {
      findUnique: jest.fn(),
    },
    journeyProgress: {
      create: jest.fn(),
      findMany: jest.fn(), // Added for getJourneyProgressHistory & getJourneyAnalytics
    },
    milestoneCompletion: {
      findMany: jest.fn(),
    },
    // Add other models and methods as needed by JourneyService
  },
}));

// Mock ai.service
jest.mock('../ai.service', () => ({
  aiService: {
    generateJourneyParameters: jest.fn(),
    generateJourneyContent: jest.fn(),
    analyzeCrisisIndicators: jest.fn(),
    generateAdaptiveRecommendations: jest.fn(),
    matchCommunitySupport: jest.fn(),
  },
}));

describe('Journey Service Unit Tests', () => {
  let service: JourneyService;

  beforeEach(() => {
    service = new JourneyService(); // Use 'service' to avoid conflict with imported 'journeyServiceInstance'
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Mock crypto.randomUUID if it's used directly by the service for ID generation
    // For Prisma, IDs are usually auto-generated by the DB or using @default(uuid())
    // If JourneyProgress ID is set by crypto.randomUUID() in the service:
    // global.crypto = {
    //   randomUUID: jest.fn(() => 'mock-uuid-for-progress'),
    // } as any;
  });

  // Helper to cast prisma mock for easier use in tests
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;

  describe('createPersonalizedJourney', () => {
    const userId = 'test-user-123';
    const assessmentId = 'assessment-456';
    const mockSpiritualDiagnosis = {
      id: assessmentId, // Assuming assessmentId passed to service is SpiritualDiagnosis.id
      userId: userId,
      assessmentId: 'session-id-123', // This would be the AssessmentSession id
      diagnosis_data: {
        // Renamed from 'results' to match Prisma schema better (diagnosisData is Json)
        overallSeverityScore: 5, // Example: 0-10
        primaryLayerAffected: 'nafs',
        crisisLevel: 'none',
        // layerHealthScores: { nafs: 40, qalb: 60 } // Example for refined layer progress
      },
      primaryLayer: 'nafs',
      overallSeverity: 'moderate',
      generatedAt: new Date(),
      // ... other SpiritualDiagnosis fields
    };
    const mockUserProfile = {
      // Data that getUserProfile would return (subset of UserProfileDetailed.profileData)
      userId: userId,
      name: 'Test User',
      profession: 'teacher',
      // ... other relevant profile fields for AI
    };
    const mockAiJourneyParameters = {
      duration: 7, // Shorter for easier testing
      timeCommitment: 20,
      primaryLayer: 'nafs' as LayerFocus,
      secondaryLayers: ['qalb'] as LayerFocus[],
      ruqyaLevel: 'basic',
      communitySupport: true,
      culturalAdaptations: ['urdu_terminology'],
      type: 'heart_purification' as JourneyType, // Example type
      recommendations: ['Focus on self-reflection'],
    };
    const mockAiPracticeComponentDetails = {
      originalContentId: 'noa-arrahman-id',
      name: 'Ar-Rahman',
      meaning: 'The Most Merciful',
      // ... other details for Ar-Rahman
    };
    const mockAiJourneyContent = {
      title: 'Test Nafs Healing Journey',
      description: 'A 7-day journey for nafs.',
      personalizedWelcome: 'Welcome, Test User!',
      days: [
        {
          // Day 1
          dayNumber: 1,
          theme: 'Understanding the Nafs',
          learningObjective: 'To identify aspects of the nafs.',
          reflectionPrompts: [
            'What is one aspect of your nafs you noticed today?',
          ],
          practices: [
            {
              id: 'd1p1',
              type: 'MorningCheckIn' as PracticeType,
              title: 'Morning Intentions',
              duration: 5,
              componentDetails: { prompt: 'What is your intention?' },
            },
            {
              id: 'd1p2',
              type: 'NameOfAllahSpotlight' as PracticeType,
              title: 'Ar-Rahman',
              duration: 10,
              layerFocus: 'qalb' as LayerFocus,
              componentDetails: mockAiPracticeComponentDetails,
            },
            {
              id: 'd1p3',
              type: 'QuranicVerseReflection' as PracticeType,
              title: 'Verse on Patience',
              duration: 10,
              componentDetails: {
                originalContentId: 'quran-verse-patience-id',
                arabicText: '...',
              },
            },
            {
              id: 'd1p4',
              type: 'PersonalReflectionJournaling' as PracticeType,
              title: 'Daily Journal',
              duration: 10,
              componentDetails: { prompts: ['My key takeaway...'] },
            },
            {
              id: 'd1p5',
              type: 'SunnahPractice' as PracticeType,
              title: 'Smiling',
              duration: 5,
              componentDetails: {
                originalContentId: 'sunnah-smile-id',
                category: 'Social',
              },
            },
          ],
        },
        // Can add Day 2 for more complex scenarios if needed
      ],
    };

    beforeEach(() => {
      // Mock Prisma calls used by createPersonalizedJourney and its private _getUserProfile
      mockPrisma.spiritualDiagnoses.findUnique.mockResolvedValue(
        mockSpiritualDiagnosis as any
      );
      mockPrisma.profile.findUnique.mockResolvedValue({
        // For _getUserProfile if it uses this
        id: userId,
        email: '<EMAIL>',
        fullName: 'Test User',
        // ... other profile fields
      });
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValue({
        // For _getUserProfile
        userId: userId,
        profileData: mockUserProfile as any, // Prisma.JsonObject,
        // ... other UserProfileDetailed fields
      });

      // Mock AI service responses
      (aiService.generateJourneyParameters as jest.Mock).mockResolvedValue(
        mockAiJourneyParameters
      );
      (aiService.generateJourneyContent as jest.Mock).mockResolvedValue(
        mockAiJourneyContent
      );
      (aiService.matchCommunitySupport as jest.Mock).mockResolvedValue({
        groupId: 'group-123',
      }); // For community matching part

      // Mock successful creation calls for Journey, JourneyDay, DailyPracticeInJourney
      mockPrisma.journey.create.mockImplementation(async (args: any) => ({
        id: 'mock-journey-id-123',
        ...args.data, // Return what was passed in, plus ID
        journeyDays: [], // Will be populated by subsequent creates
        journeyProgress: [],
        // ... other relations
      }));
      mockPrisma.journeyDay.create.mockImplementation(async (args: any) => ({
        id: `mock-day-id-${args.data.dayNumber}`,
        ...args.data,
        dailyPracticesInJourney: [],
      }));
      mockPrisma.dailyPracticeInJourney.create.mockImplementation(
        async (args: any) => ({
          id: `mock-practice-id-${args.data.orderIndex}`,
          ...args.data,
        })
      );

      // Mock content lookups for linking (return found content)
      mockPrisma.nameOfAllahContent.findUnique.mockResolvedValue({
        id: 'noa-arrahman-id',
        name: 'Ar-Rahman',
      } as any);
      mockPrisma.quranicVerseContent.findUnique.mockResolvedValue({
        id: 'quran-verse-patience-id',
        arabicText: '...',
      } as any);
      mockPrisma.sunnahPracticeContent.findUnique.mockResolvedValue({
        id: 'sunnah-smile-id',
        title: 'Smiling',
      } as any);

      // Mock the final findUnique call that re-fetches the journey
      mockPrisma.journey.findUnique.mockImplementation(async (args: any) => {
        if (args.where.id === 'mock-journey-id-123') {
          // Simulate the fully created journey with nested relations for the return value
          return {
            id: 'mock-journey-id-123',
            userId: userId,
            assessmentId: mockSpiritualDiagnosis.id,
            type: mockAiJourneyParameters.type,
            status: 'created',
            title: mockAiJourneyContent.title,
            description: mockAiJourneyContent.description,
            personalizedWelcome: mockAiJourneyContent.personalizedWelcome,
            duration: mockAiJourneyParameters.duration,
            dailyTimeCommitment: mockAiJourneyParameters.timeCommitment,
            primaryLayer: mockAiJourneyParameters.primaryLayer,
            // ... other journey fields based on mockAiJourneyParameters & mockAiJourneyContent
            journeyDays: mockAiJourneyContent.days.map((day) => ({
              id: `mock-day-id-${day.dayNumber}`,
              journeyId: 'mock-journey-id-123',
              dayNumber: day.dayNumber,
              theme: day.theme,
              learningObjective: day.learningObjective,
              dailyPracticesInJourney: day.practices.map((practice, index) => ({
                id: `mock-practice-id-${index}`,
                journeyDayId: `mock-day-id-${day.dayNumber}`,
                type: practice.type,
                title: practice.title,
                componentData: practice.componentDetails,
                nameOfAllahContentId:
                  practice.type === 'NameOfAllahSpotlight'
                    ? (practice.componentDetails as any).originalContentId
                    : null,
                quranicVerseContentId:
                  practice.type === 'QuranicVerseReflection'
                    ? (practice.componentDetails as any).originalContentId
                    : null,
                sunnahPracticeContentId:
                  practice.type === 'SunnahPractice'
                    ? (practice.componentDetails as any).originalContentId
                    : null,
                // ... other practice fields
              })),
            })),
          } as any; // Cast to any to satisfy Prisma return type in mock
        }
        return null;
      });
    });

    it('should create personalized journey successfully with correct data mapping and linking', async () => {
      const result = await service.createPersonalizedJourney(
        userId,
        mockSpiritualDiagnosis.id
      );

      // Assert Journey creation
      expect(mockPrisma.journey.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            userId: userId,
            assessmentId: mockSpiritualDiagnosis.id,
            type: mockAiJourneyParameters.type,
            status: 'created',
            title: mockAiJourneyContent.title,
            duration: mockAiJourneyParameters.duration,
            primaryLayer: mockAiJourneyParameters.primaryLayer,
            userProfile: expect.objectContaining(mockUserProfile),
          }),
        })
      );

      // Assert JourneyDay creation (check for Day 1)
      expect(mockPrisma.journeyDay.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            journeyId: 'mock-journey-id-123', // from the mocked journey.create
            dayNumber: 1,
            theme: 'Understanding the Nafs',
          }),
        })
      );

      // Assert DailyPracticeInJourney creation for Day 1, Practice 2 (NameOfAllahSpotlight)
      const nameOfAllahPracticeData = mockAiJourneyContent.days[0].practices[1];
      expect(mockPrisma.dailyPracticeInJourney.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            // journeyDayId will be a connect object, harder to assert directly without more complex mock
            type: nameOfAllahPracticeData.type,
            title: nameOfAllahPracticeData.title,
            componentData: nameOfAllahPracticeData.componentDetails,
            nameOfAllahContent: {
              connect: {
                id: (nameOfAllahPracticeData.componentDetails as any)
                  .originalContentId,
              },
            }, // Crucial check for linking
          }),
        })
      );

      // Assert AI service calls
      expect(aiService.generateJourneyParameters).toHaveBeenCalledWith({
        assessment: mockSpiritualDiagnosis.diagnosis_data,
        userProfile: expect.objectContaining(mockUserProfile),
        preferences: {},
      });
      expect(aiService.generateJourneyContent).toHaveBeenCalled();

      // Assert structure of returned result (simplified)
      expect(result.id).toBe('mock-journey-id-123');
      expect(result.journeyDays).toHaveLength(mockAiJourneyContent.days.length);
      expect(result.journeyDays[0].dailyPracticesInJourney).toHaveLength(
        mockAiJourneyContent.days[0].practices.length
      );
      const returnedPractice =
        result.journeyDays[0].dailyPracticesInJourney.find(
          (p) => p.type === 'NameOfAllahSpotlight'
        );
      expect(returnedPractice?.nameOfAllahContentId).toEqual(
        (nameOfAllahPracticeData.componentDetails as any).originalContentId
      );
    });

    it('should apply user preferences to journey configuration', async () => {
      const preferences = {
        duration: 14,
        dailyTimeCommitment: 20,
        ruqyaIntegrationLevel: 'advanced',
        communityIntegration: false,
      };

      await journeyService.createPersonalizedJourney(
        userId,
        assessmentId,
        preferences
      );

      expect(
        global.mockAiService.generateJourneyParameters
      ).toHaveBeenCalledWith({
        assessment: mockAssessment.results,
        userProfile: mockUserProfile,
        preferences,
      });
    });

    it('should enable crisis support for high-risk assessments', async () => {
      const crisisAssessment = {
        ...mockAssessment,
        results: {
          ...mockAssessment.results,
          crisisLevel: 'high',
        },
      };

      global.mockAssessmentService.getAssessmentById.mockResolvedValueOnce(
        crisisAssessment
      );

      const result = await journeyService.createPersonalizedJourney(
        userId,
        assessmentId
      );

      expect(result.configuration.crisisSupport).toBe(true);
    });

    it('should use diagnosis data for AI parameters when assessment is found', async () => {
      // This test implicitly covers the "assessment found" path by not mocking findUnique to return null.
      // It ensures that the diagnosis_data from the mockSpiritualDiagnosis is passed to the AI service.
      const preferences = { duration: 7 };
      const getUserProfileSpy = jest
        .spyOn(service as any, 'getUserProfile')
        .mockResolvedValue(mockUserProfile);

      await service.createPersonalizedJourney(
        userId,
        mockSpiritualDiagnosis.id,
        preferences
      );

      expect(aiService.generateJourneyParameters).toHaveBeenCalledWith(
        expect.objectContaining({
          assessment: mockSpiritualDiagnosis.diagnosis_data, // Key assertion
          userProfile: mockUserProfile,
          preferences,
        })
      );
      getUserProfileSpy.mockRestore();
    });

    it('should enable crisis support in journey config if assessment indicates crisis', async () => {
      const crisisDiagnosis = {
        ...mockSpiritualDiagnosis,
        diagnosis_data: {
          ...mockSpiritualDiagnosis.diagnosis_data,
          crisisLevel: 'high',
        },
      };
      mockPrisma.spiritualDiagnoses.findUnique.mockResolvedValueOnce(
        crisisDiagnosis as any
      );
      const getUserProfileSpy = jest
        .spyOn(service as any, 'getUserProfile')
        .mockResolvedValue(mockUserProfile);

      await service.createPersonalizedJourney(userId, crisisDiagnosis.id);

      expect(mockPrisma.journey.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            crisisSupport: true, // This should be true based on updated config logic
          }),
        })
      );
      getUserProfileSpy.mockRestore();
    });

    it('should throw AppError if assessment not found', async () => {
      mockPrisma.spiritualDiagnoses.findUnique.mockResolvedValueOnce(null);
      const getUserProfileSpy = jest
        .spyOn(service as any, 'getUserProfile')
        .mockResolvedValue(mockUserProfile);

      await expect(
        service.createPersonalizedJourney(userId, 'invalid-assessment-id')
      ).rejects.toThrow(new AppError('Assessment not found', 404));
      getUserProfileSpy.mockRestore();
    });

    it('should use default user profile if UserProfileDetailed not found (and not throw)', async () => {
      // _getUserProfile is designed to return a default if UserProfileDetailed is not found.
      // So, createPersonalizedJourney should proceed with a default profile.
      mockPrisma.userProfileDetailed.findUnique.mockResolvedValueOnce(null);
      // We expect generateJourneyParameters to be called with a default profile

      await service.createPersonalizedJourney(
        userId,
        mockSpiritualDiagnosis.id
      );

      expect(aiService.generateJourneyParameters).toHaveBeenCalledWith(
        expect.objectContaining({
          userProfile: expect.objectContaining({
            // Check for default structure
            userId: userId,
            profession: 'other',
            awareness_level: 'beginner',
          }),
        })
      );
    });

    it('should throw AppError if aiService.generateJourneyParameters fails', async () => {
      (aiService.generateJourneyParameters as jest.Mock).mockRejectedValueOnce(
        new Error('AI Parameters Service Error')
      );
      const getUserProfileSpy = jest
        .spyOn(service as any, 'getUserProfile')
        .mockResolvedValue(mockUserProfile);

      await expect(
        service.createPersonalizedJourney(userId, mockSpiritualDiagnosis.id)
      ).rejects.toThrow('AI Parameters Service Error');
      getUserProfileSpy.mockRestore();
    });

    it('should throw AppError if prisma.journey.create fails', async () => {
      mockPrisma.journey.create.mockRejectedValueOnce(
        new Error('DB Journey Create Error')
      );
      const getUserProfileSpy = jest
        .spyOn(service as any, 'getUserProfile')
        .mockResolvedValue(mockUserProfile);

      await expect(
        service.createPersonalizedJourney(userId, mockSpiritualDiagnosis.id)
      ).rejects.toThrow('DB Journey Create Error');
      getUserProfileSpy.mockRestore();
    });

    it('should warn if linkable content ID from AI is not found in DB', async () => {
      const consoleWarnSpy = jest
        .spyOn(console, 'warn')
        .mockImplementation(() => {});
      mockPrisma.nameOfAllahContent.findUnique.mockResolvedValueOnce(null); // Simulate content not found

      const getUserProfileSpy = jest
        .spyOn(service as any, 'getUserProfile')
        .mockResolvedValue(mockUserProfile);

      await service.createPersonalizedJourney(
        userId,
        mockSpiritualDiagnosis.id
      );

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `NameOfAllahContent with ID ${mockAiPracticeComponentDetails.originalContentId} not found for linking`
        )
      );

      consoleWarnSpy.mockRestore();
      getUserProfileSpy.mockRestore();
    });
  });

  // --- Tests for getJourneyAnalytics with layerProgress focus ---
  describe('getJourneyAnalytics - Layer Progress', () => {
    const journeyId = 'journey-analytics-123';
    const userId = 'test-user-123'; // Ensure this matches userId in mockDiagnosisData and mockJourneyWithPractices
    const mockDiagnosisData = {
      id: 'diag-1',
      userId, // Match this
      assessmentId: 'assess-1', // This is assessment_session_id
      // spiritualDiagnosisId: 'diag-1', // This field does not exist on SpiritualDiagnoses model
      overallSeverityScore: 7, // from assessment results
      primaryLayerAffected: LayerFocus.Qalb, // from assessment results
      secondaryLayerAffected: LayerFocus.Nafs, // from assessment results
      layerHealthScores: {
        // This is from diagnosis_data.layerHealthScores typically
        [LayerFocus.Qalb]: 30,
        [LayerFocus.Nafs]: 40,
        [LayerFocus.Aql]: 60,
        [LayerFocus.Ruh]: 50,
        [LayerFocus.Sirr]: 70,
      },
      diagnosis_data: {
        // This is the JSON field
        overallSeverityScore: 7,
        primaryLayerAffected: 'Qalb',
        secondaryLayerAffected: 'Nafs',
        layerHealthScores: { Qalb: 30, Nafs: 40, Aql: 60, Ruh: 50, Sirr: 70 },
        crisisLevel: 'low',
      },
      // ... other diagnosis fields
    };

    const mockJourneyWithPracticesAndProgress = {
      id: journeyId,
      userId, // Match this
      spiritualDiagnosisId: 'diag-1',
      status: 'active', // Correct enum
      totalDays: 3, // total days in this mock journey
      // Configuration might be relevant if it holds weights or targets not on practices
      configuration: {
        primaryLayerWeight: 1.5,
        secondaryLayerWeight: 1.2,
        moodBonusThreshold: 7,
        moodBonusPoints: 1, // Simplified bonus
        spiritualStateBonusThreshold: 8,
        spiritualStateBonusPoints: 1, // Simplified bonus
        reflectionCompletionBonus: 0.5, // Simplified bonus
      },
      journeyDays: [
        {
          // Day 1
          id: 'day-1-id',
          dayNumber: 1,
          status: 'completed', // JourneyDayStatus enum would be better if available at test level
          dailyPractices: [
            // This should be dailyPracticesInJourney from prisma schema perspective
            // For layerProgress, we need layerTarget on DailyPracticeInJourney
            {
              id: 'dp-1-1',
              practiceType: PracticeType.QuranicVerseReflection,
              layerTarget: LayerFocus.Qalb,
              effortRating: 3,
              componentData: { originalContentId: 'qv1' },
            },
            {
              id: 'dp-1-2',
              practiceType: PracticeType.SunnahPractice,
              layerTarget: LayerFocus.Nafs,
              effortRating: 2,
              componentData: { originalContentId: 'sp1' },
            },
          ],
          // JourneyProgress should be an array, and we'd typically fetch the latest or relevant one.
          // For simplicity, assuming one progress record per completed day for this test.
          journeyProgress: [
            {
              // This is JourneyProgress model data
              id: 'jp-1',
              moodAfter: 7, // Meets moodBonusThreshold
              spiritualStateAfter: 8, // Meets spiritualStateBonusThreshold
              reflections: 'Completed Day 1 reflections.', // Triggers reflectionCompletionBonus
              overallRating: 8,
            },
          ],
        },
        {
          // Day 2
          id: 'day-2-id',
          dayNumber: 2,
          status: 'completed',
          dailyPractices: [
            {
              id: 'dp-2-1',
              practiceType: PracticeType.NameOfAllahSpotlight,
              layerTarget: LayerFocus.Ruh,
              effortRating: 4,
              componentData: { originalContentId: 'noa1' },
            },
            {
              id: 'dp-2-2',
              practiceType: PracticeType.PersonalReflectionJournaling,
              layerTarget: LayerFocus.Aql,
              effortRating: 2,
              componentData: { originalContentId: 'prj1' },
            },
          ],
          journeyProgress: [
            {
              id: 'jp-2',
              moodAfter: 6, // Below moodBonusThreshold
              spiritualStateAfter: 7, // Below spiritualStateBonusThreshold
              reflections: 'Day 2 reflections done.', // Triggers reflectionCompletionBonus
              overallRating: 7,
            },
          ],
        },
        {
          // Day 3 (Planned, not completed)
          id: 'day-3-id',
          dayNumber: 3,
          status: 'pending',
          dailyPractices: [
            {
              id: 'dp-3-1',
              practiceType: PracticeType.MorningCheckIn,
              layerTarget: LayerFocus.Sirr,
              effortRating: 1,
              componentData: { originalContentId: 'mc1' },
            },
          ],
          journeyProgress: [], // No progress yet
        },
      ],
      // other journey fields
    };

    beforeEach(() => {
      // Mock the main journey fetch for getJourneyAnalytics
      mockPrisma.journey.findUnique.mockImplementation(async (args) => {
        if (args.where.id === journeyId && args.where.userId === userId) {
          // This is the complex part: the include structure needs to be right
          // The service expects journeyDays with their dailyPractices (as dailyPracticesInJourney)
          // AND journeyProgress records (which are at the Journey level, filtered by dayNumber internally by the service)
          return {
            ...mockJourneyWithPracticesAndProgress,
            // Simulate the includes:
            spiritualDiagnosis: mockDiagnosisData, // Nested include
            journeyDays: mockJourneyWithPracticesAndProgress.journeyDays.map(
              (jd) => ({
                ...jd,
                // dailyPractices is what the service expects, map from schema's dailyPracticesInJourney
                dailyPracticesInJourney: jd.dailyPractices,
              })
            ),
            // JourneyProgress records are fetched separately by the service or via a relation
            // For this mock, let's assume the service fetches them and they are available.
            // The service code itself will iterate journeyDays and find matching JourneyProgress.
          } as any;
        }
        return null;
      });

      // Mock the SpiritualDiagnoses fetch (if service fetches it separately, though include is better)
      mockPrisma.spiritualDiagnoses.findUnique.mockImplementation(
        async (args) => {
          if (
            args.where.id ===
            mockJourneyWithPracticesAndProgress.spiritualDiagnosisId
          ) {
            return mockDiagnosisData as any;
          }
          return null;
        }
      );

      // Mock JourneyProgress.findMany if the service uses it directly.
      // The current JourneyService._getBaselineAndProgressData seems to get progress from journey.journeyDays[x].journeyProgress
      // So, the journey.findUnique mock above should provide it nested.
      // If it were a direct call:
      // mockPrisma.journeyProgress.findMany.mockResolvedValue([...mockJourneyWithPracticesAndProgress.journeyDays[0].journeyProgress, ...mockJourneyWithPracticesAndProgress.journeyDays[1].journeyProgress] as any);
    });

    it('should calculate layerProgress correctly based on diagnosis, completed practices, and progress feedback', async () => {
      const analytics = await service.getJourneyAnalytics(journeyId, userId);
      const { layerProgress } = analytics;

      expect(layerProgress).toBeDefined();
      expect(layerProgress.length).toBe(5);

      const qalbProgress = layerProgress.find(
        (lp) => lp.layer === LayerFocus.Qalb
      );
      const nafsProgress = layerProgress.find(
        (lp) => lp.layer === LayerFocus.Nafs
      );
      const aqlProgress = layerProgress.find(
        (lp) => lp.layer === LayerFocus.Aql
      );
      const ruhProgress = layerProgress.find(
        (lp) => lp.layer === LayerFocus.Ruh
      );
      const sirrProgress = layerProgress.find(
        (lp) => lp.layer === LayerFocus.Sirr
      );

      // Baseline scores from mockDiagnosisData.layerHealthScores
      // Qalb: 30, Nafs: 40, Aql: 60, Ruh: 50, Sirr: 70

      // --- Expected Earned Impact Calculation (Day 1) ---
      // Qalb (Primary): Practice (3 * 1.5) + Mood Bonus (1) + Spiritual State Bonus (1) + Reflection Bonus (0.5) = 4.5 + 1 + 1 + 0.5 = 7
      // Nafs (Secondary): Practice (2 * 1.2) + Mood Bonus (1) + Reflection Bonus (0.5) = 2.4 + 1 + 0.5 = 3.9
      // (Aql, Ruh, Sirr get reflection bonus if applicable from Day 1, but no practices)
      // Aql Day 1: Reflection Bonus (0.5)

      // --- Expected Earned Impact Calculation (Day 2) ---
      // Ruh: Practice (4 * 1.0) + Reflection Bonus (0.5) = 4.5
      // Aql: Practice (2 * 1.0) + Reflection Bonus (0.5) = 2.5
      // (Qalb, Nafs get reflection bonus from Day 2)
      // Qalb Day 2: Reflection Bonus (0.5)
      // Nafs Day 2: Reflection Bonus (0.5)

      // Total Earned:
      // Qalb: 7 (D1) + 0.5 (D2 Ref) = 7.5
      // Nafs: 3.9 (D1) + 0.5 (D2 Ref) = 4.4
      // Aql: 0.5 (D1 Ref) + 2.5 (D2) = 3.0
      // Ruh: 4.5 (D2)
      // Sirr: 0 (No completed practices or direct bonuses)

      // Max Impact calculation is also important (based on ALL planned practices + max potential bonuses)
      // Max Qalb: Practice (3*1.5) + MaxBonusesPerDay*2 = 4.5 + (1+1+0.5)*2 = 4.5 + 5 = 9.5
      // Max Nafs: Practice (2*1.2) + MaxBonusesPerDay*2 = 2.4 + 5 = 7.4
      // Max Aql: Practice (2*1.0) + MaxBonusesPerDay*2 = 2.0 + 5 = 7.0
      // Max Ruh: Practice (4*1.0) + MaxBonusesPerDay*2 = 4.0 + 5 = 9.0
      // Max Sirr: Practice (1*1.0) + MaxBonusesPerDay*1 (only 1 day planned for it if only day 3 counts for its bonuses)
      //      Let's assume bonuses apply per completed day with reflection for Sirr: MaxBonusesPerDay*1 = 2.5
      //      So, Max Sirr = 1.0 + 2.5 = 3.5 (if Day 3 was completed with full bonuses)
      //      More accurately, max impact for sirr from practices is 1*1.0 = 1. Max bonuses from day 3 would be 1+1+0.5=2.5. Total = 3.5
      //      The _calculateMaxJourneyImpact sums all practice efforts and potential bonuses across ALL planned days.

      // Final Value = baseline + (earned / max) * (100 - baseline)
      // Example for Sirr: Baseline 70. Earned 0. Max (let's say 1 from practice + 2.5 potential bonus for 1 day = 3.5)
      // Sirr Value = 70 + (0 / 3.5) * (100 - 70) = 70. This seems correct.
      expect(sirrProgress?.currentValue).toBeCloseTo(
        mockDiagnosisData.layerHealthScores.Sirr
      );

      // Qalb: Baseline 30. Earned 7.5. Max Impact (Qalb practice 3*1.5=4.5. Total 3 days. Max bonuses per day = 1+1+0.5 = 2.5. So, 4.5 + 2.5*3 = 4.5 + 7.5 = 12)
      // Qalb Value = 30 + (7.5 / 12) * (100 - 30) = 30 + 0.625 * 70 = 30 + 43.75 = 73.75
      expect(qalbProgress?.currentValue).toBeGreaterThan(
        mockDiagnosisData.layerHealthScores.Qalb
      ); // Should be significantly higher
      // expect(qalbProgress?.currentValue).toBeCloseTo(73.75); // This requires exact replication of maxImpact logic

      // Nafs: Baseline 40. Earned 4.4. Max (Nafs practice 2*1.2=2.4. Max bonuses 7.5. Total = 9.9)
      // Nafs Value = 40 + (4.4 / 9.9) * (100 - 40) = 40 + 0.444 * 60 = 40 + 26.64 = 66.64
      expect(nafsProgress?.currentValue).toBeGreaterThan(
        mockDiagnosisData.layerHealthScores.Nafs
      );
      // expect(nafsProgress?.currentValue).toBeCloseTo(66.64);

      [
        qalbProgress,
        nafsProgress,
        aqlProgress,
        ruhProgress,
        sirrProgress,
      ].forEach((lp) => {
        expect(lp).toBeDefined();
        expect(lp?.currentValue).toBeGreaterThanOrEqual(0);
        expect(lp?.currentValue).toBeLessThanOrEqual(100);
        expect(typeof lp?.color).toBe('string');
      });
    });

    it('should use default layer health scores if not present in diagnosis, but primary/secondary are', async () => {
      const diagnosisWithoutLayerScores = {
        ...mockDiagnosisData,
        // layerHealthScores: undefined, // This field is on diagnosis_data
        diagnosis_data: {
          ...mockDiagnosisData.diagnosis_data,
          layerHealthScores: undefined, // Scores are missing from the JSON blob
          overallSeverityScore: 8,
          primaryLayerAffected: LayerFocus.Qalb,
          secondaryLayerAffected: LayerFocus.Nafs,
        },
      };
      // Adjust the journey mock to point to this modified diagnosis
      const journeyWithModifiedDiagnosis = {
        ...mockJourneyWithPracticesAndProgress,
        spiritualDiagnosis: diagnosisWithoutLayerScores, // Link to the one without scores
      };
      mockPrisma.journey.findUnique.mockResolvedValue(
        journeyWithModifiedDiagnosis as any
      ); // Ensure this mock is used
      mockPrisma.spiritualDiagnoses.findUnique.mockResolvedValue(
        diagnosisWithoutLayerScores as any
      );

      const analytics = await service.getJourneyAnalytics(journeyId, userId);
      const { layerProgress } = analytics;

      // From constants: DEFAULT_LOW_LAYER_HEALTH = 20, DEFAULT_MID_LAYER_HEALTH = 40, DEFAULT_HIGH_LAYER_HEALTH = 60
      const qalbProg = layerProgress.find((l) => l.layer === LayerFocus.Qalb); // Primary, severity 8 -> low (20)
      expect(qalbProg?.currentValue).toBeGreaterThanOrEqual(20);

      const nafsProg = layerProgress.find((l) => l.layer === LayerFocus.Nafs); // Secondary, severity 8 -> mid (40)
      expect(nafsProg?.currentValue).toBeGreaterThanOrEqual(40);

      const aqlProg = layerProgress.find((l) => l.layer === LayerFocus.Aql); // Other -> high (60)
      expect(aqlProg?.currentValue).toBeGreaterThanOrEqual(60);
    });

    it('should handle cases with no completed practices (progress should reflect baselines)', async () => {
      const journeyNoCompletedPractices = {
        ...mockJourneyWithPracticesAndProgress,
        journeyDays: mockJourneyWithPracticesAndProgress.journeyDays.map(
          (day) => ({
            ...day,
            status: 'pending',
            journeyProgress: [],
          })
        ),
      };
      mockPrisma.journey.findUnique.mockResolvedValueOnce(
        journeyNoCompletedPractices as any
      );

      const analytics = await service.getJourneyAnalytics(journeyId, userId);
      const { layerProgress } = analytics;

      layerProgress.forEach((lp) => {
        expect(lp.currentValue).toBeCloseTo(
          mockDiagnosisData.diagnosis_data.layerHealthScores[lp.layer]
        );
      });
    });

    it('should handle journey or diagnosis not found errors', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce(null); // Journey not found
      await expect(
        service.getJourneyAnalytics(journeyId, userId)
      ).rejects.toThrow(
        new AppError('Journey not found or not authorized.', 404)
      );

      // Reset journey mock, make diagnosis not found
      mockPrisma.journey.findUnique.mockResolvedValueOnce({
        ...mockJourneyWithPracticesAndProgress,
        spiritualDiagnosisId: 'non-existent-diag-id', // Point to a non-existent diagnosis
        spiritualDiagnosis: null, // Simulate include returning null
      } as any);
      mockPrisma.spiritualDiagnoses.findUnique.mockResolvedValueOnce(null); // Explicitly make diagnosis fetch fail

      await expect(
        service.getJourneyAnalytics(journeyId, userId)
      ).rejects.toThrow(
        new AppError('Spiritual diagnosis not found for this journey.', 404)
      );
    });

    it('should assign colors correctly based on progress value thresholds', async () => {
      const analytics = await service.getJourneyAnalytics(journeyId, userId);
      analytics.layerProgress.forEach((lp) => {
        expect(typeof lp.color).toBe('string');
        expect(lp.color.startsWith('#')).toBe(true);
      });
    });
  }); // End of getJourneyAnalytics describe block

  describe('getJourneyProgressHistory', () => {
    const journeyId = 'journey-hist-123';
    const userId = 'test-user-123';
    const mockProgressData = [
      {
        journeyId,
        userId,
        dayNumber: 1,
        moodAfter: 7,
        overallRating: 8,
        createdAt: new Date(),
      },
      {
        journeyId,
        userId,
        dayNumber: 2,
        moodAfter: 6,
        overallRating: 7,
        createdAt: new Date(),
      },
    ];

    it('should retrieve progress history for a journey successfully', async () => {
      // Mock that the journey exists and belongs to the user
      mockPrisma.journey.findUnique.mockResolvedValueOnce({
        id: journeyId,
        userId,
      } as any);
      // Mock the progress data fetch
      mockPrisma.journeyProgress.findMany.mockResolvedValueOnce(
        mockProgressData as any
      );

      const result = await service.getJourneyProgressHistory(journeyId, userId);

      expect(mockPrisma.journey.findUnique).toHaveBeenCalledWith({
        where: { id: journeyId, userId },
        select: { id: true }, // Service checks for journey existence and ownership
      });
      expect(mockPrisma.journeyProgress.findMany).toHaveBeenCalledWith({
        where: { journeyId: journeyId }, // Filter by journeyId
        orderBy: { dayNumber: 'asc' },
        // Select all relevant fields as defined in the service method
        select: {
          dayNumber: true,
          moodBefore: true,
          moodAfter: true,
          energyLevelBefore: true,
          energyLevelAfter: true,
          sleepQuality: true,
          overallRating: true,
          completedPractices: true,
          reflections: true,
          feedback: true,
          spiritualStateBefore: true,
          spiritualStateAfter: true,
          dailyIntention: true,
          challengesFaced: true,
          successesAchieved: true,
          practiceSpecificFeedback: true,
          createdAt: true,
        },
      });
      expect(result).toEqual(mockProgressData);
    });

    it('should return an empty array if no progress history found', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce({
        id: journeyId,
        userId,
      } as any);
      mockPrisma.journeyProgress.findMany.mockResolvedValueOnce([]); // No progress records

      const result = await service.getJourneyProgressHistory(journeyId, userId);
      expect(result).toEqual([]);
    });

    it('should throw AppError if journey not found or not authorized', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce(null); // Journey does not exist or not owned by user

      await expect(
        service.getJourneyProgressHistory(journeyId, userId)
      ).rejects.toThrow(
        new AppError(
          'Journey not found or not authorized to view progress.',
          404
        )
      );
      expect(mockPrisma.journeyProgress.findMany).not.toHaveBeenCalled();
    });

    it('should throw error if database query for progress fails', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce({
        id: journeyId,
        userId,
      } as any);
      const dbError = new Error('Database query failed');
      mockPrisma.journeyProgress.findMany.mockRejectedValueOnce(dbError);

      await expect(
        service.getJourneyProgressHistory(journeyId, userId)
      ).rejects.toThrow(dbError);
    });
  }); // End of getJourneyProgressHistory describe block

  describe('getJourneyCompletedMilestones', () => {
    const journeyId = 'journey-milestone-123';
    const userId = 'test-user-123';
    const mockJourneyConfigWithMilestones = {
      id: journeyId,
      userId,
      totalDays: 40,
      configuration: {
        milestoneDays: [7, 14, 21, 40], // Defined milestones
        // ... other config
      },
      // ... other journey fields
    };
    const mockJourneyConfigNoMilestones = {
      id: journeyId,
      userId,
      totalDays: 10,
      configuration: {
        /* No milestoneDays defined */
      },
    };

    const mockCompletedJourneyDaysForMilestones = [
      {
        dayNumber: 7,
        status: 'completed',
        completedAt: new Date(),
        reflections: 'Week 1 done!',
      },
      {
        dayNumber: 14,
        status: 'completed',
        completedAt: new Date(),
        reflections: 'Halfway (for a 28 day variant if it existed)',
      },
      // Day 21 is missing, Day 40 is missing for this mock result
    ];

    it('should retrieve completed milestones based on journey configuration and completed days', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce(
        mockJourneyConfigWithMilestones as any
      );
      mockPrisma.journeyDay.findMany.mockResolvedValueOnce(
        mockCompletedJourneyDaysForMilestones as any
      );

      const result = await service.getJourneyCompletedMilestones(
        journeyId,
        userId
      );

      expect(mockPrisma.journey.findUnique).toHaveBeenCalledWith({
        where: { id: journeyId, userId },
        select: { id: true, configuration: true, totalDays: true },
      });
      expect(mockPrisma.journeyDay.findMany).toHaveBeenCalledWith({
        where: {
          journeyId: journeyId,
          dayNumber: {
            in: mockJourneyConfigWithMilestones.configuration.milestoneDays,
          },
          status: 'completed', // Assuming JourneyDayStatus.COMPLETED maps to 'completed' string if not using enum directly in test
        },
        select: { dayNumber: true, completedAt: true, reflections: true },
        orderBy: { dayNumber: 'asc' },
      });

      expect(result).toEqual([
        {
          dayNumber: 7,
          completedAt: mockCompletedJourneyDaysForMilestones[0].completedAt,
          description: 'Completed Day 7 milestone.',
          reflections: 'Week 1 done!',
        },
        {
          dayNumber: 14,
          completedAt: mockCompletedJourneyDaysForMilestones[1].completedAt,
          description: 'Completed Day 14 milestone.',
          reflections: 'Halfway (for a 28 day variant if it existed)',
        },
      ]);
    });

    it('should return an empty array if no milestone days are configured in the journey', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce(
        mockJourneyConfigNoMilestones as any
      );

      const result = await service.getJourneyCompletedMilestones(
        journeyId,
        userId
      );

      expect(mockPrisma.journey.findUnique).toHaveBeenCalledWith({
        where: { id: journeyId, userId },
        select: { id: true, configuration: true, totalDays: true },
      });
      // journeyDay.findMany should not be called if there are no milestoneDays
      expect(mockPrisma.journeyDay.findMany).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it('should return an empty array if configured milestone days are not completed', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce(
        mockJourneyConfigWithMilestones as any
      );
      mockPrisma.journeyDay.findMany.mockResolvedValueOnce([]); // No matching completed days found

      const result = await service.getJourneyCompletedMilestones(
        journeyId,
        userId
      );
      expect(result).toEqual([]);
      expect(mockPrisma.journeyDay.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            dayNumber: { in: [7, 14, 21, 40] },
          }),
        })
      );
    });

    it('should throw AppError if journey not found or not authorized', async () => {
      mockPrisma.journey.findUnique.mockResolvedValueOnce(null);

      await expect(
        service.getJourneyCompletedMilestones(journeyId, userId)
      ).rejects.toThrow(
        new AppError(
          'Journey not found or not authorized to view milestones.',
          404
        )
      );
      expect(mockPrisma.journeyDay.findMany).not.toHaveBeenCalled();
    });

    it('should handle empty configuration object gracefully', async () => {
      const journeyWithEmptyConfig = {
        ...mockJourneyConfigNoMilestones,
        configuration: {},
      };
      mockPrisma.journey.findUnique.mockResolvedValueOnce(
        journeyWithEmptyConfig as any
      );

      const result = await service.getJourneyCompletedMilestones(
        journeyId,
        userId
      );
      expect(result).toEqual([]);
      expect(mockPrisma.journeyDay.findMany).not.toHaveBeenCalled();
    });

    it('should handle configuration.milestoneDays being an empty array', async () => {
      const journeyWithEmptyMilestoneDays = {
        ...mockJourneyConfigNoMilestones,
        configuration: { milestoneDays: [] },
      };
      mockPrisma.journey.findUnique.mockResolvedValueOnce(
        journeyWithEmptyMilestoneDays as any
      );

      const result = await service.getJourneyCompletedMilestones(
        journeyId,
        userId
      );
      expect(result).toEqual([]);
      expect(mockPrisma.journeyDay.findMany).not.toHaveBeenCalled();
    });
  }); // End of getJourneyCompletedMilestones describe block
}); // End of main describe block
