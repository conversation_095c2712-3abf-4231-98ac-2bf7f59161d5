import { Test, TestingModule } from '@nestjs/testing';
import { NameOfAllahContentService } from '../name-of-allah-content.service';
import { PrismaClient, Prisma } from '@prisma/client';
import { HttpException, HttpStatus } from '@nestjs/common';

// Mock PrismaClient
const mockPrismaClient = {
  nameOfAllahContent: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe('NameOfAllahContentService', () => {
  let service: NameOfAllahContentService;
  let prisma: typeof mockPrismaClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NameOfAllahContentService,
        {
          provide: PrismaClient,
          useValue: mockPrismaClient,
        },
      ],
    }).compile();

    service = module.get<NameOfAllahContentService>(NameOfAllahContentService);
    prisma = module.get<typeof mockPrismaClient>(PrismaClient);
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // Test suite for create()
  describe('create', () => {
    const createData: Prisma.NameOfAllahContentCreateInput = {
      name: 'Ar-Rahman',
      transliteration: 'Ar-Rahman',
      meaning: 'The Most Gracious',
      description: 'One of the names of Allah.',
      audioUrl: 'http://example.com/ar-rahman.mp3',
    };
    const expectedResult = { id: '1', ...createData, createdAt: new Date(), updatedAt: new Date() };

    it('should create a new Name of Allah content successfully', async () => {
      prisma.nameOfAllahContent.create.mockResolvedValue(expectedResult);
      const result = await service.create(createData);
      expect(result).toEqual(expectedResult);
      expect(prisma.nameOfAllahContent.create).toHaveBeenCalledWith({ data: createData });
    });

    it('should throw HttpException if Prisma create fails', async () => {
      prisma.nameOfAllahContent.create.mockRejectedValue(new Error('DB error'));
      await expect(service.create(createData)).rejects.toThrow(
        new HttpException('Error creating Name of Allah content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for findAll()
  describe('findAll', () => {
    const findManyArgs = { where: { meaning: { contains: 'Gracious' } } };
    const expectedResults = [
      { id: '1', name: 'Ar-Rahman', transliteration: 'Ar-Rahman', meaning: 'The Most Gracious', description: 'Desc 1', audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() },
      { id: '2', name: 'Ar-Raheem', transliteration: 'Ar-Raheem', meaning: 'The Most Merciful and Gracious', description: 'Desc 2', audioUrl: 'url2', createdAt: new Date(), updatedAt: new Date() },
    ];

    it('should return an array of Name of Allah content', async () => {
      prisma.nameOfAllahContent.findMany.mockResolvedValue(expectedResults);
      const result = await service.findAll(findManyArgs);
      expect(result).toEqual(expectedResults);
      expect(prisma.nameOfAllahContent.findMany).toHaveBeenCalledWith(findManyArgs);
    });

    it('should return an empty array if no content found', async () => {
      prisma.nameOfAllahContent.findMany.mockResolvedValue([]);
      const result = await service.findAll({});
      expect(result).toEqual([]);
      expect(prisma.nameOfAllahContent.findMany).toHaveBeenCalledWith({});
    });

    it('should throw HttpException if Prisma findMany fails', async () => {
      prisma.nameOfAllahContent.findMany.mockRejectedValue(new Error('DB error'));
      await expect(service.findAll({})).rejects.toThrow(
        new HttpException('Error fetching Name of Allah content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for findOne()
  describe('findOne', () => {
    const contentId = '1';
    const expectedResult = { id: contentId, name: 'Ar-Rahman', transliteration: 'Ar-Rahman', meaning: 'The Most Gracious', description: 'Desc 1', audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() };

    it('should return a single Name of Allah content if found', async () => {
      prisma.nameOfAllahContent.findUnique.mockResolvedValue(expectedResult);
      const result = await service.findOne(contentId);
      expect(result).toEqual(expectedResult);
      expect(prisma.nameOfAllahContent.findUnique).toHaveBeenCalledWith({ where: { id: contentId } });
    });

    it('should throw HttpException NOT_FOUND if content not found', async () => {
      prisma.nameOfAllahContent.findUnique.mockResolvedValue(null);
      await expect(service.findOne(contentId)).rejects.toThrow(
        new HttpException('Name of Allah content not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR on other Prisma errors', async () => {
      prisma.nameOfAllahContent.findUnique.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.findOne(contentId)).rejects.toThrow(
        new HttpException('Error fetching Name of Allah content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for update()
  describe('update', () => {
    const contentId = '1';
    const updateData: Prisma.NameOfAllahContentUpdateInput = { meaning: 'The Exceedingly Gracious' };
    const expectedResult = { id: contentId, name: 'Ar-Rahman', transliteration: 'Ar-Rahman', meaning: 'The Exceedingly Gracious', description: 'Desc 1', audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() };

    it('should update Name of Allah content successfully', async () => {
      prisma.nameOfAllahContent.update.mockResolvedValue(expectedResult);
      const result = await service.update(contentId, updateData);
      expect(result).toEqual(expectedResult);
      expect(prisma.nameOfAllahContent.update).toHaveBeenCalledWith({ where: { id: contentId }, data: updateData });
    });

    it('should throw HttpException NOT_FOUND if content to update not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Record to update not found', { code: 'P2025', clientVersion: 'x.y.z'});
      prisma.nameOfAllahContent.update.mockRejectedValue(prismaError);
      await expect(service.update(contentId, updateData)).rejects.toThrow(
        new HttpException('Name of Allah content not found for update', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR for other Prisma update errors', async () => {
      prisma.nameOfAllahContent.update.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.update(contentId, updateData)).rejects.toThrow(
        new HttpException('Error updating Name of Allah content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for remove()
  describe('remove', () => {
    const contentId = '1';
    const expectedResult = { id: contentId, name: 'Ar-Rahman', transliteration: 'Ar-Rahman', meaning: 'The Most Gracious', description: 'Desc 1', audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() };

    it('should remove Name of Allah content successfully', async () => {
      prisma.nameOfAllahContent.delete.mockResolvedValue(expectedResult);
      const result = await service.remove(contentId);
      expect(result).toEqual(expectedResult);
      expect(prisma.nameOfAllahContent.delete).toHaveBeenCalledWith({ where: { id: contentId } });
    });

    it('should throw HttpException NOT_FOUND if content to delete not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Record to delete not found', { code: 'P2025', clientVersion: 'x.y.z'});
      prisma.nameOfAllahContent.delete.mockRejectedValue(prismaError);
      await expect(service.remove(contentId)).rejects.toThrow(
        new HttpException('Name of Allah content not found for deletion', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR for other Prisma delete errors', async () => {
      prisma.nameOfAllahContent.delete.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.remove(contentId)).rejects.toThrow(
        new HttpException('Error deleting Name of Allah content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });
});