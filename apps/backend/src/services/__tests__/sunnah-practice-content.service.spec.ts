import { Test, TestingModule } from '@nestjs/testing';
import { SunnahPracticeContentService } from '../sunnah-practice-content.service';
import { PrismaClient, Prisma, SunnahPracticeContent, SunnahPracticeCategory } from '@prisma/client';
import { HttpException, HttpStatus } from '@nestjs/common';

// Mock PrismaClient
const mockPrismaClient = {
  sunnahPracticeContent: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe('SunnahPracticeContentService', () => {
  let service: SunnahPracticeContentService;
  let prisma: typeof mockPrismaClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SunnahPracticeContentService,
        {
          provide: PrismaClient,
          useValue: mockPrismaClient,
        },
      ],
    }).compile();

    service = module.get<SunnahPracticeContentService>(SunnahPracticeContentService);
    prisma = module.get<typeof mockPrismaClient>(PrismaClient);
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // Test suite for create()
  describe('create', () => {
    const createData: Prisma.SunnahPracticeContentCreateInput = {
      title: 'Using Miswak',
      description: 'Cleaning teeth with a miswak is a Sunnah.',
      benefits: ['Oral hygiene', 'Pleasing to Allah'],
      steps: ['Use miswak before prayer.'],
      category: SunnahPracticeCategory.PURIFICATION,
      hadithReferences: ['Sahih Bukhari XX:YY'],
      audioUrl: 'http://example.com/miswak.mp3',
    };
    const expectedResult = { id: 'sunnah1', ...createData, createdAt: new Date(), updatedAt: new Date() } as SunnahPracticeContent;

    it('should create a new Sunnah practice content successfully', async () => {
      prisma.sunnahPracticeContent.create.mockResolvedValue(expectedResult);
      const result = await service.create(createData);
      expect(result).toEqual(expectedResult);
      expect(prisma.sunnahPracticeContent.create).toHaveBeenCalledWith({ data: createData });
    });

    it('should throw HttpException CONFLICT if Prisma create fails due to unique constraint (P2002 on title)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Unique constraint failed', { code: 'P2002', clientVersion: 'x.y.z', meta: { target: ['title'] } });
      prisma.sunnahPracticeContent.create.mockRejectedValue(prismaError);
      await expect(service.create(createData)).rejects.toThrow(
        new HttpException('Sunnah practice with this title already exists.', HttpStatus.CONFLICT),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR for other Prisma create errors', async () => {
      prisma.sunnahPracticeContent.create.mockRejectedValue(new Error('DB error'));
      await expect(service.create(createData)).rejects.toThrow(
        new HttpException('Error creating Sunnah practice content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for findAll()
  describe('findAll', () => {
    const findManyArgs = { where: { category: SunnahPracticeCategory.PURIFICATION } };
    const expectedResults: SunnahPracticeContent[] = [
      { id: 'sunnah1', title: 'Using Miswak', description: '...', benefits: [], steps: [], category: SunnahPracticeCategory.PURIFICATION, hadithReferences: [], audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() },
      { id: 'sunnah2', title: 'Wudu Steps', description: '...', benefits: [], steps: [], category: SunnahPracticeCategory.PURIFICATION, hadithReferences: [], audioUrl: 'url2', createdAt: new Date(), updatedAt: new Date() },
    ];

    it('should return an array of Sunnah practice content', async () => {
      prisma.sunnahPracticeContent.findMany.mockResolvedValue(expectedResults);
      const result = await service.findAll(findManyArgs);
      expect(result).toEqual(expectedResults);
      expect(prisma.sunnahPracticeContent.findMany).toHaveBeenCalledWith(findManyArgs);
    });

    it('should return an empty array if no content found', async () => {
      prisma.sunnahPracticeContent.findMany.mockResolvedValue([]);
      const result = await service.findAll({});
      expect(result).toEqual([]);
    });

    it('should throw HttpException if Prisma findMany fails', async () => {
      prisma.sunnahPracticeContent.findMany.mockRejectedValue(new Error('DB error'));
      await expect(service.findAll({})).rejects.toThrow(
        new HttpException('Error fetching Sunnah practice content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for findOne()
  describe('findOne', () => {
    const contentId = 'sunnah1';
    const expectedResult: SunnahPracticeContent = { id: contentId, title: 'Using Miswak', description: '...', benefits: [], steps: [], category: SunnahPracticeCategory.PURIFICATION, hadithReferences: [], audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() };

    it('should return a single Sunnah practice content if found', async () => {
      prisma.sunnahPracticeContent.findUnique.mockResolvedValue(expectedResult);
      const result = await service.findOne(contentId);
      expect(result).toEqual(expectedResult);
      expect(prisma.sunnahPracticeContent.findUnique).toHaveBeenCalledWith({ where: { id: contentId } });
    });

    it('should throw HttpException NOT_FOUND if content not found', async () => {
      prisma.sunnahPracticeContent.findUnique.mockResolvedValue(null);
      await expect(service.findOne(contentId)).rejects.toThrow(
        new HttpException('Sunnah practice content not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR on other Prisma errors', async () => {
      prisma.sunnahPracticeContent.findUnique.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.findOne(contentId)).rejects.toThrow(
        new HttpException('Error fetching Sunnah practice content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for findByTitle()
  describe('findByTitle', () => {
    const title = 'Using Miswak';
    const expectedResult: SunnahPracticeContent = { id: 'sunnahX', title: title, description: '...', benefits: [], steps: [], category: SunnahPracticeCategory.PURIFICATION, hadithReferences: [], audioUrl: 'urlX', createdAt: new Date(), updatedAt: new Date() };

    it('should return Sunnah practice content if found by title', async () => {
      prisma.sunnahPracticeContent.findUnique.mockResolvedValue(expectedResult);
      const result = await service.findByTitle(title);
      expect(result).toEqual(expectedResult);
      expect(prisma.sunnahPracticeContent.findUnique).toHaveBeenCalledWith({ where: { title: title } });
    });

    it('should return null if content not found by title (as per Prisma behavior)', async () => {
      prisma.sunnahPracticeContent.findUnique.mockResolvedValue(null);
      const result = await service.findByTitle(title);
      expect(result).toBeNull();
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR on Prisma errors', async () => {
      prisma.sunnahPracticeContent.findUnique.mockRejectedValue(new Error('DB error'));
      await expect(service.findByTitle(title)).rejects.toThrow(
        new HttpException('Error fetching Sunnah practice content by title', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for update()
  describe('update', () => {
    const contentId = 'sunnah1';
    const updateData: Prisma.SunnahPracticeContentUpdateInput = { description: 'Updated description.' };
    const expectedResult: SunnahPracticeContent = { id: contentId, title: 'Using Miswak', description: 'Updated description.', benefits: [], steps: [], category: SunnahPracticeCategory.PURIFICATION, hadithReferences: [], audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() };

    it('should update Sunnah practice content successfully', async () => {
      prisma.sunnahPracticeContent.update.mockResolvedValue(expectedResult);
      const result = await service.update(contentId, updateData);
      expect(result).toEqual(expectedResult);
      expect(prisma.sunnahPracticeContent.update).toHaveBeenCalledWith({ where: { id: contentId }, data: updateData });
    });

    it('should throw HttpException NOT_FOUND if content to update not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Record to update not found', { code: 'P2025', clientVersion: 'x.y.z'});
      prisma.sunnahPracticeContent.update.mockRejectedValue(prismaError);
      await expect(service.update(contentId, updateData)).rejects.toThrow(
        new HttpException('Sunnah practice content not found for update', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException CONFLICT if update causes unique constraint violation (P2002 on title)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Unique constraint failed', { code: 'P2002', clientVersion: 'x.y.z', meta: { target: ['title'] }});
      prisma.sunnahPracticeContent.update.mockRejectedValue(prismaError);
      await expect(service.update(contentId, { title: 'Existing Title' })).rejects.toThrow(
        new HttpException('Update conflicts with an existing Sunnah practice (title may already exist).', HttpStatus.CONFLICT),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR for other Prisma update errors', async () => {
      prisma.sunnahPracticeContent.update.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.update(contentId, updateData)).rejects.toThrow(
        new HttpException('Error updating Sunnah practice content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });

  // Test suite for remove()
  describe('remove', () => {
    const contentId = 'sunnah1';
    const expectedResult: SunnahPracticeContent = { id: contentId, title: 'Using Miswak', description: '...', benefits: [], steps: [], category: SunnahPracticeCategory.PURIFICATION, hadithReferences: [], audioUrl: 'url1', createdAt: new Date(), updatedAt: new Date() };

    it('should remove Sunnah practice content successfully', async () => {
      prisma.sunnahPracticeContent.delete.mockResolvedValue(expectedResult);
      const result = await service.remove(contentId);
      expect(result).toEqual(expectedResult);
      expect(prisma.sunnahPracticeContent.delete).toHaveBeenCalledWith({ where: { id: contentId } });
    });

    it('should throw HttpException NOT_FOUND if content to delete not found (P2025)', async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError('Record to delete not found', { code: 'P2025', clientVersion: 'x.y.z'});
      prisma.sunnahPracticeContent.delete.mockRejectedValue(prismaError);
      await expect(service.remove(contentId)).rejects.toThrow(
        new HttpException('Sunnah practice content not found for deletion', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException INTERNAL_SERVER_ERROR for other Prisma delete errors', async () => {
      prisma.sunnahPracticeContent.delete.mockRejectedValue(new Error('Some other DB error'));
      await expect(service.remove(contentId)).rejects.toThrow(
        new HttpException('Error deleting Sunnah practice content', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });
  });
});
