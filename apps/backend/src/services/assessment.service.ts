/**
 * Assessment Service for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Handles spiritual assessment sessions, AI analysis, and diagnosis generation
 */

import {
  AssessmentSession as ClientAssessmentSession, // Renamed to avoid clash with Prisma type
  SpiritualDiagnosis as ClientSpiritualDiagnosis, // Renamed to avoid clash with Prisma type
  PersonalizedWelcome,
  DiagnosisDelivery,
  AssessmentQuestion as AssessmentQuestionClientType,
  ASSESSMENT_STEPS,
} from '../models/Assessment';
import {
  prisma,
  AssessmentSession as PrismaAssessmentSession,
  SpiritualDiagnosis as PrismaSpiritualDiagnosis,
  LayerAnalysis as PrismaLayerAnalysis,
  Prisma,
} from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import {
  aiService,
  AIComprehensiveAssessmentDataType,
  AISpiritualLandscapeResponse,
  AILayerAnalysisOutput,
  AIServiceSymptomExperienceData,
  // AIServiceUserProfileData, // No longer needed directly here
} from './ai.service';
import {
  crisisDetectionService,
  CrisisCheckResult,
} from './crisis-detection.service';
import {
  prepareProfileForAI,
  determineUserTypeFromProfile, // Now imported for use in assessment service
  // getDeliveryStyleForUserType, // Will be used by presenter
} from '../utils/assessmentUserProfile.helper';
import { mapAIDiagnosisToPrismaCreateInput } from '../mappers/aiDiagnosis.mapper';
import { formatDiagnosisForDelivery } from '../presenters/diagnosisDelivery.presenter';

// Define a more specific type for session with diagnosis
type PrismaAssessmentSessionWithDiagnosis = PrismaAssessmentSession & {
  spiritualDiagnoses: PrismaSpiritualDiagnosis[];
  diagnosis?: PrismaSpiritualDiagnosis | null; // Added this for direct access
};

// Define a more specific type for diagnosis with relations
type PrismaSpiritualDiagnosisWithRelations = PrismaSpiritualDiagnosis & {
  assessmentSession: { userId: string; userProfile: Prisma.JsonValue } | null;
  layerAnalyses: PrismaLayerAnalysis[];
};

// Define a type for Diagnosis Feedback data
export interface DiagnosisFeedbackData {
  accuracy: number;
  helpfulness: number;
  comments?: string;
  userId: string; // To link feedback to the user
  diagnosisId: string; // To link feedback to the diagnosis
}

export class AssessmentService {
  /**
   * Start a new assessment session with personalized welcome
   */
  async startAssessment(
    userId: string,
    userEmail: string,
    initialUserProfileJson: Prisma.JsonValue // Renamed for clarity
  ): Promise<{
    session: PrismaAssessmentSession;
    welcome: PersonalizedWelcome;
  }> {
    let effectiveUserProfileJson = initialUserProfileJson;
    let profileDataSource = 'initial argument';

    const userProfileInDBById = await prisma.profile.findUnique({
      where: { id: userId },
    });

    if (userProfileInDBById) {
      // If profile exists by ID, this is the most authoritative version.
      // We need to convert it to Prisma.JsonValue if it's not already (though findUnique returns the model type).
      // Assuming the userProfileInDBById is compatible or can be mapped to the structure expected in AssessmentSession.userProfile
      effectiveUserProfileJson = userProfileInDBById as any; // Adjust casting as necessary
      profileDataSource = 'DB by ID';
    } else {
      const userProfileInDBByEmail = await prisma.profile.findUnique({
        where: { email: userEmail },
      });
      if (userProfileInDBByEmail) {
        logger.warn(
          `User profile ID mismatch in startAssessment for email ${userEmail}. Token ID: ${userId}, DB ID found by email: ${userProfileInDBByEmail.id}. Using profile found by email.`
        );
        // This scenario implies the userId in token might be new or different from the one linked to the email.
        // For consistency, we might still want to create/update the profile for `userId` or handle this as an error/merge.
        // For now, let's prioritize the profile found by email if primary ID lookup fails.
        effectiveUserProfileJson = userProfileInDBByEmail as any; // Adjust casting
        profileDataSource = 'DB by Email';
        // Optionally, ensure a profile for `userId` also gets created or linked here if that's desired.
        // For example, create profile for userId if it doesn't exist, possibly copying data from profileByEmail.
        // await prisma.profile.upsert({ where: { id: userId }, update: { email: userEmail, ... }, create: {id: userId, email: userEmail, ...}})
      } else {
        logger.info(
          `No profile found for user ${userId} or email ${userEmail} in startAssessment. Creating new profile for ID ${userId}.`
        );
        const newProfile = await prisma.profile.create({
          data: {
            id: userId,
            email:
              userEmail /* any defaults from initialUserProfileJson if applicable and valid */,
          },
        });
        effectiveUserProfileJson = newProfile as any; // Use the newly created profile
        profileDataSource = 'New DB Profile';
      }
    }
    logger.info(
      `Using profile data from: ${profileDataSource} for session processing.`
    );

    const existingSession = await prisma.assessmentSession.findFirst({
      where: { userId, completedAt: null, abandonedAt: null },
      orderBy: { startedAt: 'desc' },
    });

    if (existingSession) {
      logger.info('Resuming existing assessment session', {
        userId,
        sessionId: existingSession.id,
      });
      // For resuming, use the profile data from the existing session if it's considered more relevant,
      // or the newly determined effectiveUserProfileJson if updates should propagate.
      // Let's use the effectiveUserProfileJson to ensure latest profile is used for welcome.
      const welcome = await this.getPersonalizedWelcome(
        userId,
        existingSession.userProfile || effectiveUserProfileJson
      ); // Prefer existing session's profile for welcome if available
      return { session: existingSession, welcome };
    }

    const session = await prisma.assessmentSession.create({
      data: {
        userId,
        userProfile: effectiveUserProfileJson, // Store the determined authoritative profile
        startedAt: new Date(),
        currentStep: 'welcome',
        totalSteps: ASSESSMENT_STEPS.length, // Ensure ASSESSMENT_STEPS is accurate
        physicalExperiences: Prisma.JsonNull,
        emotionalExperiences: Prisma.JsonNull,
        mentalExperiences: Prisma.JsonNull,
        spiritualExperiences: Prisma.JsonNull,
        reflections: Prisma.JsonNull,
        timeSpentPerStep: Prisma.JsonNull,
        totalTimeSpent: 0,
      },
    });
    const welcome = await this.getPersonalizedWelcome(
      userId,
      effectiveUserProfileJson
    );
    logger.info('Assessment session started', {
      userId,
      sessionId: session.id,
    });
    return { session, welcome };
  }

  /**
   * Get the latest completed assessment for a user
   */
  async getLatestCompletedAssessment(
    userId: string
  ): Promise<AssessmentSession | null> {
    logger.info('Checking for completed assessments', { userId });

    const completedAssessment = await prisma.assessmentSession.findFirst({
      where: {
        userId,
        completedAt: { not: null },
        abandonedAt: null,
      },
      orderBy: { completedAt: 'desc' },
    });

    logger.info('Completed assessment query result', {
      userId,
      found: !!completedAssessment,
      sessionId: completedAssessment?.id,
      completedAt: completedAssessment?.completedAt,
    });

    return completedAssessment;
  }

  /**
   * Get personalized welcome content
   */
  async getPersonalizedWelcome(
    userId: string,
    userProfileJson: Prisma.JsonValue,
    providedName?: string
  ): Promise<PersonalizedWelcome> {
    try {
      const preparedProfile = prepareProfileForAI(userProfileJson, userId); // Using helper

      // Extract user's name from profile for personalization
      const rawProfile = (userProfileJson as Prisma.JsonObject) || {};
      let profile = rawProfile;
      if (
        rawProfile.userProfile &&
        typeof rawProfile.userProfile === 'object'
      ) {
        profile = rawProfile.userProfile as Prisma.JsonObject;
      }
      let userName = '';

      // Try to get name from various possible fields in the profile
      if (profile.fullName && typeof profile.fullName === 'string') {
        userName = profile.fullName.split(' ')[0]; // Get first name
      } else if (profile.firstName && typeof profile.firstName === 'string') {
        userName = profile.firstName;
      } else if (profile.name && typeof profile.name === 'string') {
        userName = profile.name;
      } else if (
        profile.demographics &&
        typeof profile.demographics === 'object'
      ) {
        const demographics = profile.demographics as Prisma.JsonObject;
        if (demographics.name && typeof demographics.name === 'string') {
          userName = demographics.name;
        }
      }

      // If no name found in profile, try to fetch from database using userId
      if (!userName) {
        try {
          const userProfile = await prisma.profile.findUnique({
            where: { id: userId },
            select: { firstName: true, fullName: true },
          });

          if (userProfile?.firstName) {
            userName = userProfile.firstName;
          } else if (userProfile?.fullName) {
            userName = userProfile.fullName.split(' ')[0]; // Get first name
          }
        } catch (error) {
          logger.warn('Could not fetch user name from database:', {
            userId,
            error: error.message,
          });
        }
      }

      // Determine user type from profile
      // logger.info('DEBUG: Profile data before user type determination:', {
      // userId,
      // originalProfile: JSON.stringify(userProfileJson),
      // preparedProfile: JSON.stringify(preparedProfile)
      // });
      const userType = determineUserTypeFromProfile(preparedProfile);
      logger.info('DEBUG: User type determination result:', {
        userId,
        userType,
      });

      // Generate personalized content based on profile
      let greeting = 'As-salamu alaykum, dear brother/sister.';
      let introduction = '';
      let explanation = '';
      let motivation = '';

      // Customize based on mental health awareness level
      const mentalHealthAwareness = (profile.mentalHealthAwareness ||
        profile.mental_health_awareness) as Prisma.JsonObject;
      let awarenessLevel = '';
      if (mentalHealthAwareness?.level?.mental_health_awareness) {
        awarenessLevel = mentalHealthAwareness.level.mental_health_awareness;
      } else if (mentalHealthAwareness?.level) {
        awarenessLevel = mentalHealthAwareness.level;
      } else if (typeof mentalHealthAwareness === 'string') {
        awarenessLevel = mentalHealthAwareness;
      }

      if (awarenessLevel === 'clinical_aware') {
        introduction =
          "You mentioned having clinical awareness of anxiety/depression and seeking Islamic help. That's a brave and wise step.";
        explanation =
          "We'll explore how your experiences connect across physical, emotional, mental, and spiritual dimensions, offering you Islamic-informed insights alongside your existing knowledge.";
        motivation =
          'Your clinical awareness combined with Islamic wisdom can be powerful for healing.';
      } else if (awarenessLevel === 'symptom_aware') {
        introduction =
          "You mentioned feeling overwhelmed but aren't sure exactly what's happening. That's completely normal - sometimes our hearts know something is wrong before our minds can name it.";
        explanation =
          "Let's explore what you're experiencing together. We'll look at your physical, emotional, mental, and spiritual experiences to understand what Allah might be teaching you through this time.";
        motivation =
          "There's no pressure to use any specific terms - just share what feels true.";
      } else if (awarenessLevel === 'crisis') {
        introduction =
          "You've indicated you're in crisis and need immediate support. You are not alone, and help is available.";
        explanation =
          'While we prepare personalized support, please know that immediate help is just a click away.';
        motivation =
          "Your courage in reaching out shows strength. Let's get you the support you need right now.";
      } else if (awarenessLevel === 'spiritual_growth') {
        introduction =
          "MashaAllah, you're seeking spiritual growth and prevention of future struggles. This proactive approach shows wisdom.";
        explanation =
          "We'll help you strengthen your spiritual foundation and develop tools for continued growth and resilience.";
        motivation =
          "Prevention and growth are beautiful intentions. Let's build on your existing strength.";
      } else if (awarenessLevel === 'new_muslim') {
        introduction =
          "Welcome to Islam, dear brother/sister. What a beautiful journey you're beginning.";
        explanation =
          "We'll gently guide you through understanding your inner landscape from an Islamic perspective, with patience and care for your new path.";
        motivation =
          'Take this at your own pace. Allah is Most Merciful and Patient with His servants.';
      } else {
        introduction =
          'Welcome to your personalized spiritual wellness assessment.';
        explanation =
          'This assessment will help us understand your unique inner landscape and provide personalized guidance for your healing journey.';
        motivation =
          'Take your time and answer honestly. Your responses will help us create a personalized healing plan just for you.';
      }

      // Add user name to greeting if available
      if (userName) {
        greeting = `As-salamu alaykum, dear ${userName}.`;
      }

      // Determine primary action based on crisis level
      let primaryAction = {
        id: 'begin_assessment',
        text: 'Begin Assessment',
        description: null,
      };

      let secondaryActions = [
        {
          id: 'emergency_help',
          text: 'I Need Immediate Help',
          description: null,
        },
      ];

      if (awarenessLevel === 'crisis') {
        primaryAction = {
          id: 'emergency_help',
          text: 'Get Immediate Help',
          description: null,
        };
        secondaryActions = [
          {
            id: 'begin_assessment',
            text: 'Continue to Assessment',
            description: null,
          },
        ];
      }

      return {
        userId: userId,
        userType: userType,
        userName: userName || undefined,
        greeting: greeting,
        introduction: introduction,
        explanation: explanation,
        motivation: motivation,
        primaryAction: primaryAction,
        secondaryActions: secondaryActions,
      } as PersonalizedWelcome;
    } catch (error) {
      logger.error(
        'Error getting personalized welcome, using fallback:',
        error
      );

      // Fallback welcome content
      const rawProfile = (userProfileJson as Prisma.JsonObject) || {};
      let profile = rawProfile;
      if (
        rawProfile.userProfile &&
        typeof rawProfile.userProfile === 'object'
      ) {
        profile = rawProfile.userProfile as Prisma.JsonObject;
      }
      let userName = '';

      // Try to get name from various possible fields for fallback too
      if (profile.fullName && typeof profile.fullName === 'string') {
        userName = profile.fullName.split(' ')[0]; // Get first name
      } else if (profile.firstName && typeof profile.firstName === 'string') {
        userName = profile.firstName;
      } else if (profile.name && typeof profile.name === 'string') {
        userName = profile.name;
      }

      return {
        userId: userId,
        userType: 'general',
        userName: userName || undefined,
        greeting: userName
          ? `As-salamu alaykum, dear ${userName}.`
          : 'As-salamu alaykum, dear brother/sister.',
        introduction: 'Welcome to your spiritual wellness assessment.',
        explanation:
          'This assessment will help us understand your inner landscape and provide personalized guidance for your healing journey.',
        motivation: 'Take your time and answer honestly.',
        primaryAction: {
          id: 'begin_assessment',
          text: 'Begin Assessment',
          description: null,
        },
        secondaryActions: [
          {
            id: 'emergency_help',
            text: 'I Need Immediate Help',
            description: null,
          },
        ],
      } as PersonalizedWelcome;
    }
  }

  /**
   * Get assessment questions for a given step
   */
  async getAssessmentQuestions(
    _sessionId: string, // Not used if questions aren't session-specific beyond userProfile context
    step: string
  ): Promise<AssessmentQuestionClientType[]> {
    return this._getQuestionsForStepDB(step);
  }

  /**
   * Helper to create the update data for session responses.
   */
  private _prepareSessionUpdateData(
    session: PrismaAssessmentSession,
    step: string,
    responses: Prisma.JsonValue,
    timeSpent: number
  ): Prisma.AssessmentSessionUpdateInput {
    const updatedTimeSpentPerStep = {
      ...((session.timeSpentPerStep as Prisma.JsonObject) || {}),
      [step]: timeSpent,
    };

    const updateData: Prisma.AssessmentSessionUpdateInput = {
      timeSpentPerStep: updatedTimeSpentPerStep,
      totalTimeSpent: (session.totalTimeSpent || 0) + timeSpent,
    };

    switch (step) {
      case 'physical_experiences':
        updateData.physicalExperiences = responses;
        break;
      case 'emotional_experiences':
        updateData.emotionalExperiences = responses;
        break;
      case 'mental_experiences':
        updateData.mentalExperiences = responses;
        break;
      case 'spiritual_experiences':
        updateData.spiritualExperiences = responses;
        break;
      case 'reflections':
        updateData.reflections = responses;
        break;
      default:
        logger.warn(
          `Unknown assessment step: ${step}. Responses not saved to specific field.`
        );
        // If responses should be stored generically for unknown steps:
        // const currentGenericResponses = (session.responses as Prisma.JsonObject) || {};
        // updateData.responses = { ...currentGenericResponses, [step]: responses };
        break;
    }
    return updateData;
  }

  /**
   * Submit assessment response with crisis detection
   */
  async submitAssessmentResponse(
    sessionId: string,
    step: string,
    responses: Prisma.JsonValue,
    timeSpent: number
  ): Promise<{
    nextStep: string | null;
    progress: number;
    crisisDetected: boolean;
    crisisData?: CrisisCheckResult;
  }> {
    const session = await this.getSession(sessionId); // Fetches current session state

    const updateData = this._prepareSessionUpdateData(
      session,
      step,
      responses,
      timeSpent
    );

    await prisma.assessmentSession.update({
      where: { id: sessionId },
      data: updateData,
    });

    // Update local session object with new data for subsequent logic
    const updatedSession = {
      ...session,
      ...updateData,
    } as PrismaAssessmentSessionWithDiagnosis;

    const crisisCheck = await crisisDetectionService.analyzeResponse(
      responses,
      step
    );
    if (crisisCheck.isCrisis) {
      return this.handleCrisisDetection(
        sessionId,
        crisisCheck,
        updatedSession.currentStep
      );
    }

    const nextStep = this._getNextStepLogic(step);

    await prisma.assessmentSession.update({
      where: { id: sessionId },
      data: { currentStep: nextStep || 'complete' },
    });

    if (!nextStep) {
      // Assessment is complete
      await this.generateDiagnosis(sessionId);
    }

    const progress = this._calculateProgress(nextStep || 'complete');
    return { nextStep, progress, crisisDetected: false };
  }

  private async _prepareAIAnalysisData(
    session: PrismaAssessmentSession
  ): Promise<AIComprehensiveAssessmentDataType> {
    if (!session.userId) {
      logger.error(
        'Session is missing userId, cannot prepare AI analysis data.',
        { sessionId: session.id }
      );
      throw new AppError(
        'Session is missing userId, critical for AI analysis.',
        500
      );
    }

    const preparedProfile = prepareProfileForAI(
      session.userProfile,
      session.userId
    ); // Using helper

    // Helper function to convert symptom IDs to text
    const convertSymptomsToText = async (
      experienceData: Prisma.JsonObject | null
    ): Promise<AIServiceSymptomExperienceData | null> => {
      if (
        !experienceData ||
        !experienceData.symptoms ||
        !Array.isArray(experienceData.symptoms)
      ) {
        return null;
      }

      const symptomIds = experienceData.symptoms as string[];
      if (symptomIds.length === 0) {
        return {
          symptoms: [],
          intensity: (experienceData.intensity as string) || null,
          user_reflection: (experienceData.userReflection as string) || null,
        };
      }

      // Fetch symptom texts from database
      const symptoms = await prisma.assessmentSymptom.findMany({
        where: { id: { in: symptomIds } },
        select: { id: true, text: true },
      });

      const symptomTexts = symptomIds.map((id) => {
        const symptom = symptoms.find((s) => s.id === id);
        return symptom ? symptom.text : `Unknown symptom (${id})`;
      });

      return {
        symptoms: symptomTexts,
        intensity: (experienceData.intensity as string) || null,
        user_reflection: (experienceData.userReflection as string) || null,
      };
    };

    // Convert all experience data
    const physicalExperiences = await convertSymptomsToText(
      session.physicalExperiences as Prisma.JsonObject
    );
    const emotionalExperiences = await convertSymptomsToText(
      session.emotionalExperiences as Prisma.JsonObject
    );
    const mentalExperiences = await convertSymptomsToText(
      session.mentalExperiences as Prisma.JsonObject
    );
    const spiritualExperiences = await convertSymptomsToText(
      session.spiritualExperiences as Prisma.JsonObject
    );

    // Handle reflections
    let reflectionsData = (session.reflections as Prisma.JsonObject) || {};
    if (reflectionsData?.symptoms && Array.isArray(reflectionsData.symptoms)) {
      const reflectionSymptomIds = reflectionsData.symptoms as string[];
      if (reflectionSymptomIds.length > 0) {
        const reflectionSymptoms = await prisma.assessmentSymptom.findMany({
          where: { id: { in: reflectionSymptomIds } },
          select: { id: true, text: true },
        });

        const reflectionSymptomTexts = reflectionSymptomIds.map((id) => {
          const symptom = reflectionSymptoms.find((s) => s.id === id);
          return symptom ? symptom.text : `Unknown symptom (${id})`;
        });

        reflectionsData = {
          ...reflectionsData,
          symptoms: reflectionSymptomTexts.join(', '),
        };
      } else {
        reflectionsData = {
          ...reflectionsData,
          symptoms: '',
        };
      }
    }

    return {
      user_profile: preparedProfile,
      physical_experiences: physicalExperiences,
      emotional_experiences: emotionalExperiences,
      mental_experiences: mentalExperiences,
      spiritual_experiences: spiritualExperiences,
      reflections: reflectionsData,
      session_metadata: {
        totalTimeSpent: session.totalTimeSpent ?? 0,
        timeSpentPerStep: (session.timeSpentPerStep as Prisma.JsonObject) || {},
        sessionId: session.id,
        startedAt: session.startedAt.toISOString(),
      },
    };
  }

  private async _saveAIDiagnosisToDB(
    sessionId: string,
    userId: string,
    aiAnalysis: AISpiritualLandscapeResponse
  ): Promise<PrismaSpiritualDiagnosisWithRelations> {
    // Using the mapper
    const { diagnosisCreateData, layerAnalysesCreateInput } =
      mapAIDiagnosisToPrismaCreateInput(aiAnalysis, userId, sessionId);

    logger.info('Creating spiritual diagnosis record in DB...', {
      sessionId,
      userId,
    });
    const savedDiagnosis = await prisma.spiritualDiagnosis.create({
      data: {
        ...diagnosisCreateData, // Spread the mapped diagnosis data
        layerAnalyses: { create: layerAnalysesCreateInput }, // Create related layer analyses
      },
      include: {
        layerAnalyses: true,
        assessmentSession: { select: { userId: true, userProfile: true } },
      },
    });
    logger.info('Spiritual diagnosis record created in DB.', {
      diagnosisId: savedDiagnosis.id,
      sessionId,
    });
    return savedDiagnosis as PrismaSpiritualDiagnosisWithRelations;
  }

  /**
   * Generate AI-powered spiritual diagnosis.
   * If a diagnosis already exists for the session, it returns the existing one.
   * Otherwise, it generates a new one.
   */
  async generateDiagnosis(
    sessionId: string
  ): Promise<ClientSpiritualDiagnosis> {
    const session = await this.getSession(sessionId);
    if (!session.userId) {
      throw new AppError(
        'Session is missing userId, cannot generate diagnosis.',
        500
      );
    }

    // Check if a diagnosis already exists and is considered valid
    if (session.diagnosis) {
      logger.info(
        'Diagnosis already exists for session, returning existing one.',
        { sessionId, diagnosisId: session.diagnosis.id }
      );
      return this.mapPrismaDiagnosisToClient(
        session.diagnosis as PrismaSpiritualDiagnosisWithRelations
      );
    }

    try {
      const analysisData = await this._prepareAIAnalysisData(session);

      logger.info('Calling AI service for analysis...', { sessionId });
      const aiAnalysis = await aiService.analyzeSpiritualLandscape(
        analysisData
      );
      logger.info('AI analysis received.', {
        sessionId,
        aiAnalysisId: aiAnalysis.id,
      });

      const savedDiagnosis = await this._saveAIDiagnosisToDB(
        sessionId,
        session.userId,
        aiAnalysis
      );

      logger.info('Updating assessment session as complete...', { sessionId });
      await prisma.assessmentSession.update({
        where: { id: sessionId },
        data: { completedAt: new Date(), currentStep: 'complete' },
      });
      logger.info('Assessment session marked as complete.', { sessionId });

      logger.info('Spiritual diagnosis generated', {
        diagnosisId: savedDiagnosis.id,
        sessionId,
      });
      return this.mapPrismaDiagnosisToClient(savedDiagnosis);
    } catch (error: any) {
      logger.error('Error during diagnosis generation:', {
        sessionId,
        error: error.message,
        stack: error.stack,
      });
      throw new AppError(`Failed to generate diagnosis: ${error.message}`, 500);
    }
  }

  /**
   * Force generates a new diagnosis, potentially overwriting or adding to existing ones.
   * This is useful for re-analysis or admin-triggered updates.
   */
  async forceGenerateDiagnosis(
    sessionId: string
  ): Promise<ClientSpiritualDiagnosis> {
    logger.info('Force generating diagnosis for session:', { sessionId });
    const session = await this.getSession(sessionId); // Get current session state
    if (!session.userId) {
      throw new AppError(
        'Session is missing userId, cannot force generate diagnosis.',
        500
      );
    }

    // Optional: Add logic here if existing diagnoses need to be marked as outdated or deleted
    // For example:
    // await prisma.spiritualDiagnosis.updateMany({
    //   where: { assessmentSessionId: sessionId, isCurrent: true },
    //   data: { isCurrent: false }, // Assuming an 'isCurrent' field
    // });

    try {
      const analysisData = await this._prepareAIAnalysisData(session);
      logger.info('Calling AI service for forced analysis...', { sessionId });
      const aiAnalysis = await aiService.analyzeSpiritualLandscape(
        analysisData
      );
      logger.info('Forced AI analysis received.', {
        sessionId,
        aiAnalysisId: aiAnalysis.id,
      });

      // _saveAIDiagnosisToDB will create a new diagnosis record.
      // If your schema implies only one diagnosis per session, ensure old one is handled.
      const savedDiagnosis = await this._saveAIDiagnosisToDB(
        sessionId,
        session.userId,
        aiAnalysis
      );

      // Ensure session is marked complete if it wasn't already
      if (!session.completedAt || session.currentStep !== 'complete') {
        await prisma.assessmentSession.update({
          where: { id: sessionId },
          data: { completedAt: new Date(), currentStep: 'complete' },
        });
      }
      logger.info('Spiritual diagnosis force-generated', {
        diagnosisId: savedDiagnosis.id,
        sessionId,
      });
      return this.mapPrismaDiagnosisToClient(savedDiagnosis);
    } catch (error: any) {
      logger.error('Error during forced diagnosis generation:', {
        sessionId,
        error: error.message,
        stack: error.stack,
      });
      throw new AppError(
        `Failed to force-generate diagnosis: ${error.message}`,
        500
      );
    }
  }

  // Helper to map Prisma diagnosis to client model
  private mapPrismaDiagnosisToClient(
    prismaDiagnosis: PrismaSpiritualDiagnosisWithRelations
  ): ClientSpiritualDiagnosis {
    return {
      id: prismaDiagnosis.id,
      userId: prismaDiagnosis.userId,
      assessmentSessionId: prismaDiagnosis.assessmentSessionId,
      primaryLayer: prismaDiagnosis.primaryLayer || 'N/A', // Handle null case
      secondaryLayers:
        (prismaDiagnosis.secondaryLayers as string[] | null) || [], // Handle null case
      overallSeverity: prismaDiagnosis.overallSeverity || 'N/A', // Handle null case
      crisisLevel: prismaDiagnosis.crisisLevel || 'low', // Handle null case
      confidence: prismaDiagnosis.confidence || 0, // Handle null case
      recommendedJourneyType:
        prismaDiagnosis.recommendedJourneyType || 'general', // Handle null case
      estimatedHealingDuration: prismaDiagnosis.estimatedHealingDuration || 21, // Handle null case with default 21 days
      nextSteps: (prismaDiagnosis.nextSteps as string[] | null) || [], // Handle null case
      generatedAt: prismaDiagnosis.generatedAt,
      // Include full diagnosisData (AI response) if needed by client, or selected parts
      // diagnosisData: prismaDiagnosis.diagnosisData ? (prismaDiagnosis.diagnosisData as any).ai_response : null,
      // Map layerAnalyses if they are part of ClientSpiritualDiagnosis model
      // layerAnalyses: prismaDiagnosis.layerAnalyses?.map(la => ({
      //   ...la,
      //   affectedSymptoms: la.affectedSymptoms as any, // Ensure proper typing
      //   insights: la.insights as string[],
      //   recommendations: la.recommendations as string[],
      //   islamicContext: la.islamicContext as string[],
      // })) || [],
    };
  }

  async getDiagnosisDelivery(diagnosisId: string): Promise<DiagnosisDelivery> {
    const diagnosisRecord = await prisma.spiritualDiagnosis.findUnique({
      where: { id: diagnosisId },
      include: {
        assessmentSession: { select: { userId: true, userProfile: true } },
        // layerAnalyses: true, // layerAnalyses are part of ai_response in diagnosisData
      },
    });

    if (!diagnosisRecord || !diagnosisRecord.assessmentSession) {
      throw new AppError('Diagnosis or associated session not found', 404);
    }

    const aiResponse = (diagnosisRecord.diagnosisData as Prisma.JsonObject)
      ?.ai_response as AISpiritualLandscapeResponse | undefined;

    if (!aiResponse) {
      // This could happen if diagnosisData or ai_response field is missing/null
      logger.error(
        'AI response data missing or malformed in diagnosis record',
        { diagnosisId }
      );
      throw new AppError(
        'AI response data missing from diagnosis record.',
        500
      );
    }

    // Using the presenter
    return formatDiagnosisForDelivery(
      { id: diagnosisRecord.id, generatedAt: diagnosisRecord.generatedAt },
      aiResponse,
      diagnosisRecord.assessmentSession.userProfile,
      diagnosisRecord.assessmentSession.userId
    );
  }

  async getAssessmentHistory(
    userId: string,
    page: number,
    limit: number
  ): Promise<{ sessions: ClientAssessmentSession[]; pagination: any }> {
    const skip = (page - 1) * limit;
    const [sessionsData, total] = await prisma.$transaction([
      prisma.assessmentSession.findMany({
        where: { userId, completedAt: { not: null }, abandonedAt: null }, // Exclude abandoned sessions
        orderBy: { completedAt: 'desc' },
        skip,
        take: limit,
        // Include the latest diagnosis with its layer analyses for each session
        include: {
          spiritualDiagnoses: {
            take: 1,
            orderBy: { generatedAt: 'desc' },
            include: { layerAnalyses: true },
          },
        },
      }),
      prisma.assessmentSession.count({
        where: { userId, completedAt: { not: null }, abandonedAt: null },
      }),
    ]);

    const sessions = sessionsData.map((s) => {
      const { spiritualDiagnoses, ...sessionFields } = s;
      const clientDiagnosis = spiritualDiagnoses[0]
        ? this.mapPrismaDiagnosisToClient(
            spiritualDiagnoses[0] as PrismaSpiritualDiagnosisWithRelations
          )
        : null;

      // TODO: Robustly map PrismaAssessmentSession fields to ClientAssessmentSession fields.
      // This requires a clear definition of ClientAssessmentSession.
      // For now, direct assignment with 'as any' for JSON fields.
      return {
        id: sessionFields.id,
        userId: sessionFields.userId,
        userProfile: sessionFields.userProfile as any,
        startedAt: sessionFields.startedAt,
        completedAt: sessionFields.completedAt,
        abandonedAt: sessionFields.abandonedAt, // Make sure this is part of ClientAssessmentSession
        currentStep: sessionFields.currentStep,
        totalSteps: sessionFields.totalSteps,
        // Example: map specific fields if ClientAssessmentSession is more structured
        // physicalExperiencesSummary: (sessionFields.physicalExperiences as any)?.summary,
        // emotionalExperiencesSummary: (sessionFields.emotionalExperiences as any)?.summary,
        // ... other fields
        // For now, assume direct mapping or that client handles raw JSON
        physicalExperiences: sessionFields.physicalExperiences as any,
        emotionalExperiences: sessionFields.emotionalExperiences as any,
        mentalExperiences: sessionFields.mentalExperiences as any,
        spiritualExperiences: sessionFields.spiritualExperiences as any,
        reflections: sessionFields.reflections as any,
        timeSpentPerStep: sessionFields.timeSpentPerStep as any,
        totalTimeSpent: sessionFields.totalTimeSpent,
        diagnosis: clientDiagnosis,
      } as ClientAssessmentSession;
    });

    return {
      sessions,
      pagination: { page, limit, total, totalPages: Math.ceil(total / limit) },
    };
  }

  async getSession(
    sessionId: string
  ): Promise<PrismaAssessmentSessionWithDiagnosis> {
    const session = await prisma.assessmentSession.findUnique({
      where: { id: sessionId },
      include: {
        // Include the latest diagnosis with its layer analyses
        spiritualDiagnoses: {
          orderBy: { generatedAt: 'desc' },
          take: 1,
          include: { layerAnalyses: true },
        },
      },
    });
    if (!session) throw new AppError('Assessment session not found', 404);

    const sessionWithDiagnosis =
      session as PrismaAssessmentSessionWithDiagnosis;
    // Ensure the 'diagnosis' field on the session object itself is populated for easy access
    sessionWithDiagnosis.diagnosis =
      sessionWithDiagnosis.spiritualDiagnoses[0] || null;
    return sessionWithDiagnosis;
  }

  async updateSession(
    sessionId: string,
    userId: string,
    updateData: Partial<ClientAssessmentSession>
  ): Promise<PrismaAssessmentSession> {
    const session = await this.getSession(sessionId);
    if (session.userId !== userId) {
      throw new AppError('Access denied. User does not own this session.', 403);
    }

    // Map ClientAssessmentSession fields to Prisma.AssessmentSessionUpdateInput
    // Be careful with JSON fields and ensure they are correctly typed for Prisma.
    const prismaUpdateData: Prisma.AssessmentSessionUpdateInput = {};
    if (updateData.userProfile !== undefined)
      prismaUpdateData.userProfile = updateData.userProfile as Prisma.JsonValue;
    if (updateData.physicalExperiences !== undefined)
      prismaUpdateData.physicalExperiences =
        updateData.physicalExperiences as Prisma.JsonValue;
    if (updateData.emotionalExperiences !== undefined)
      prismaUpdateData.emotionalExperiences =
        updateData.emotionalExperiences as Prisma.JsonValue;
    if (updateData.mentalExperiences !== undefined)
      prismaUpdateData.mentalExperiences =
        updateData.mentalExperiences as Prisma.JsonValue;
    if (updateData.spiritualExperiences !== undefined)
      prismaUpdateData.spiritualExperiences =
        updateData.spiritualExperiences as Prisma.JsonValue;
    if (updateData.reflections !== undefined)
      prismaUpdateData.reflections = updateData.reflections as Prisma.JsonValue;
    if (updateData.timeSpentPerStep !== undefined)
      prismaUpdateData.timeSpentPerStep =
        updateData.timeSpentPerStep as Prisma.JsonValue;
    if (updateData.currentStep !== undefined)
      prismaUpdateData.currentStep = updateData.currentStep;
    if (updateData.totalTimeSpent !== undefined)
      prismaUpdateData.totalTimeSpent = updateData.totalTimeSpent;
    // Add other updatable fields from ClientAssessmentSession to prismaUpdateData as needed

    // Fields like id, spiritualDiagnoses, diagnosis should not be updated this way.
    // delete (prismaUpdateData as any).id; // Not needed if explicitly constructing prismaUpdateData

    const updatedSession = await prisma.assessmentSession.update({
      where: { id: sessionId },
      data: prismaUpdateData,
    });
    return updatedSession;
  }

  // PRIVATE HELPERS that were not moved to external modules

  private async _getQuestionsForStepDB(
    step: string
  ): Promise<AssessmentQuestionClientType[]> {
    logger.debug(`Fetching questions for step: ${step}`);
    const prismaQuestions = await prisma.assessmentQuestion.findMany({
      where: { step, isActive: true },
      include: {
        assessment_symptoms: {
          where: { isActive: true },
          orderBy: { displayOrder: 'asc' },
        },
      },
      orderBy: { displayOrder: 'asc' },
    });
    logger.debug(
      `Found ${prismaQuestions.length} questions for step: ${step}.`
    );
    // TODO: Consider moving _transformPrismaQuestionToClientType to a mapper if it grows complex
    return prismaQuestions.map((pq) =>
      this._transformPrismaQuestionToClientType(pq)
    );
  }

  private _transformPrismaQuestionToClientType(
    pq: Prisma.AssessmentQuestionGetPayload<{
      include: { assessment_symptoms: true };
    }>
  ): AssessmentQuestionClientType {
    // TODO: Define explicit enum types for category and questionType in models/Assessment.ts
    // and use them here for stronger typing, e.g., category: pq.category as AssessmentCategory,
    let layer: string = 'general'; // Default layer
    // Map category to layer if applicable, otherwise use a default or specific logic
    if (
      ['jism', 'nafs', 'aql', 'qalb', 'ruh'].includes(pq.category.toLowerCase())
    ) {
      layer = pq.category.toLowerCase();
    }

    return {
      id: pq.id,
      category: pq.category as any, // Should map to a defined enum/type
      layer: layer,
      title: pq.title,
      description: pq.description ?? undefined,
      questionType: pq.questionType as any, // Should map to a defined enum/type
      symptoms: (pq.assessment_symptoms || []).map((symptom) => ({
        id: symptom.id,
        text: symptom.text,
        description: symptom.description ?? undefined,
        // Ensure these fields exist on Prisma AssessmentSymptom model and map them
        severity: symptom.severity as number | undefined, // Example, adjust if not present
        primaryLayer: symptom.primaryLayer as string | undefined,
        secondaryLayers: (symptom.secondaryLayers as
          | string[]
          | Prisma.JsonArray
          | null)
          ? Array.isArray(symptom.secondaryLayers)
            ? symptom.secondaryLayers
            : []
          : [],
        clinicalTerms: (symptom.clinicalTerms as
          | string[]
          | Prisma.JsonArray
          | null)
          ? Array.isArray(symptom.clinicalTerms)
            ? symptom.clinicalTerms
            : []
          : [],
        dsmMapping: (symptom.dsmMapping as string[] | Prisma.JsonArray | null)
          ? Array.isArray(symptom.dsmMapping)
            ? symptom.dsmMapping
            : []
          : [],
        spiritualAilmentIndicators: (symptom.spiritualAilmentIndicators as
          | string[]
          | Prisma.JsonArray
          | null)
          ? Array.isArray(symptom.spiritualAilmentIndicators)
            ? symptom.spiritualAilmentIndicators
            : []
          : [],
        ruqyaRelevance: symptom.ruqyaRelevance as number | undefined,
        islamicContext: symptom.islamicContext ?? undefined,
        displayOrder: symptom.displayOrder,
        isActive: symptom.isActive, // This might not be needed by client if already filtered by isActive:true
        createdAt: symptom.createdAt,
      })),
      reflectionPrompt: pq.reflectionPrompt ?? undefined,
      reflectionRequired: pq.reflectionRequired,
      allowMultipleSelection: pq.allowMultipleSelection,
      intensityScale: pq.intensityScale as any, // Should map to a defined type/interface
      customInputAllowed: pq.customInputAllowed,
    };
  }

  private _getNextStepLogic(currentStep: string): string | null {
    const currentIndex = ASSESSMENT_STEPS.indexOf(currentStep as any);
    if (currentIndex === -1 || currentIndex >= ASSESSMENT_STEPS.length - 1) {
      return null; // End of assessment or invalid step
    }
    return ASSESSMENT_STEPS[currentIndex + 1] as string;
  }

  private _calculateProgress(currentStepOrCompleted: string): number {
    if (currentStepOrCompleted === 'complete') return 100;
    const currentIndex = ASSESSMENT_STEPS.indexOf(
      currentStepOrCompleted as any
    );
    if (currentIndex === -1) return 0; // Invalid step means 0 progress
    // Progress is based on the number of steps *completed*
    return Math.round((currentIndex / ASSESSMENT_STEPS.length) * 100);
  }

  private async handleCrisisDetection(
    sessionId: string,
    crisisCheck: CrisisCheckResult,
    currentSessionStep: string
  ): Promise<{
    nextStep: null;
    progress: number;
    crisisDetected: true;
    crisisData: CrisisCheckResult;
  }> {
    logger.warn('Crisis detected during assessment', {
      sessionId,
      crisisData: crisisCheck,
      step: currentSessionStep,
    });

    // Optionally, update the session status to indicate crisis
    // await prisma.assessmentSession.update({
    //   where: { id: sessionId },
    //   data: { status: 'CRISIS_DETECTED', currentStep: 'crisis_intervention' }, // Example
    // });

    return {
      nextStep: null, // Stop assessment flow
      progress: this._calculateProgress(currentSessionStep), // Progress up to the point of crisis
      crisisDetected: true,
      crisisData: crisisCheck,
    };
  }

  // Removed _determineOverallSeverityFromAIScore, _getLayerSummary, _determineUserTypeFromProfile,
  // _getDeliveryStyleForUserType, and formatLayerName as they are now in helpers/mappers/presenters.
  // Removed _prepareProfileForAI as it's imported.

  async submitDiagnosisFeedback(
    diagnosisId: string,
    feedbackData: DiagnosisFeedbackData
  ): Promise<void> {
    // Ensure diagnosis exists
    const diagnosis = await prisma.spiritualDiagnosis.findUnique({
      where: { id: diagnosisId },
    });
    if (!diagnosis) {
      throw new AppError('Diagnosis not found for feedback submission.', 404);
    }
    // Ensure user submitting feedback is the one who owns the diagnosis (or admin)
    if (diagnosis.userId !== feedbackData.userId) {
      // Add role-based check here if admins can submit feedback too
      throw new AppError(
        'User not authorized to submit feedback for this diagnosis.',
        403
      );
    }

    await prisma.diagnosisFeedback.create({
      data: {
        diagnosisId: diagnosisId,
        userId: feedbackData.userId,
        accuracy: feedbackData.accuracy,
        helpfulness: feedbackData.helpfulness,
        comments: feedbackData.comments,
        submittedAt: new Date(),
      },
    });
    logger.info('Diagnosis feedback submitted', {
      diagnosisId,
      userId: feedbackData.userId,
    });
  }

  async abandonAssessment(
    sessionId: string,
    userId: string,
    reason?: string
  ): Promise<void> {
    const session = await this.getSession(sessionId); // Ensures session exists
    if (session.userId !== userId) {
      throw new AppError('Access denied. User does not own this session.', 403);
    }
    await prisma.assessmentSession.update({
      where: { id: sessionId },
      data: { abandonedAt: new Date(), abandonmentReason: reason },
    });
    logger.info('Assessment session abandoned', { sessionId, userId, reason });
  }

  async resetSession(sessionId: string): Promise<void> {
    const session = await prisma.assessmentSession.findUnique({
      where: { id: sessionId },
    });
    if (!session) {
      throw new AppError('Session not found for reset', 404);
    }

    // Delete related spiritual diagnoses first to avoid foreign key constraints if any
    await prisma.spiritualDiagnosis.deleteMany({
      where: { assessmentSessionId: sessionId },
    });

    await prisma.assessmentSession.update({
      where: { id: sessionId },
      data: {
        completedAt: null,
        currentStep: 'welcome',
        physicalExperiences: Prisma.JsonNull,
        emotionalExperiences: Prisma.JsonNull,
        mentalExperiences: Prisma.JsonNull,
        spiritualExperiences: Prisma.JsonNull,
        reflections: Prisma.JsonNull,
        timeSpentPerStep: Prisma.JsonNull,
        totalTimeSpent: 0,
        abandonedAt: null,
        abandonmentReason: null,
        // spiritualDiagnoses: { deleteMany: {} }, // This is another way if relations allow cascade
      },
    });
    logger.info(`Assessment session ${sessionId} reset successfully.`);
  }

  // Placeholders below are removed as their logic is integrated or part of other methods.
  // private getQuestionsForStep(...) - Covered by getAssessmentQuestions -> _getQuestionsForStepDB
  // private updateSessionWithResponses(...) - Covered by _prepareSessionUpdateData and Prisma update
  // private getNextStep(...) - Covered by _getNextStepLogic
  // private calculateProgress(...) - Covered by _calculateProgress
  // private async saveSession(...) - Prisma calls are inherently saving.
}

export const assessmentService = new AssessmentService();
