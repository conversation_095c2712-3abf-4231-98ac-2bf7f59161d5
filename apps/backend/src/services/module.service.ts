import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

interface HealingModule {
  id: string;
  title: string;
  description: string;
  soulLayer: string;
  moduleType: 'assessment' | 'practice' | 'education' | 'reflection';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number;
  prerequisites: string[];
  content: ModuleContent[];
  assessments: ModuleAssessment[];
  isCompleted: boolean;
  progress: number;
}

interface ModuleContent {
  id: string;
  type: 'text' | 'audio' | 'video' | 'interactive';
  title: string;
  content: string;
  duration: number;
  order: number;
}

interface ModuleAssessment {
  id: string;
  type: 'quiz' | 'reflection' | 'practice' | 'self_evaluation';
  questions: AssessmentQuestion[];
  passingScore: number;
}

interface AssessmentQuestion {
  id: string;
  question: string;
  type: 'multiple_choice' | 'text' | 'scale' | 'boolean';
  options?: string[];
  correctAnswer?: string;
  points: number;
}

interface ModuleCompletion {
  id: string;
  userId: string;
  moduleId: string;
  score: number;
  completedAt: Date;
  timeSpent: number;
  feedback?: string;
}

/**
 * Module Service - Manages healing journey modules and progress
 */
export class ModuleService {
  /**
   * Get available modules for user based on their journey
   * @param userId - User ID
   * @param soulLayer - Filter by soul layer
   * @param difficulty - Filter by difficulty
   * @returns Available modules
   */
  async getAvailableModules(
    userId: string,
    soulLayer?: string,
    difficulty?: string
  ): Promise<HealingModule[]> {
    try {
      // Get user's current journey and progress
      const userJourney = await prisma.userJourney.findFirst({
        where: {
          userId,
          status: 'active',
        },
      });

      let query = prisma.healingModule.findMany({
        where: {
          isActive: true,
          ...(soulLayer ? { soulLayer } : {}),
          ...(difficulty ? { difficulty } : {}),
        },
        orderBy: { orderIndex: 'asc' },
      });

      // Filter based on user's journey if exists
      if (userJourney) {
        query = query.journeyTypes({
          some: {
            journeyType: userJourney.journeyType,
          },
        });
      }

      const modules = await query;

      const healingModules: HealingModule[] = modules.map((module: any) => {
        const completion = module.moduleCompletions?.find(
          (c: any) => c.userId === userId
        );

        return {
          id: module.id,
          title: module.title,
          description: module.description,
          soulLayer: module.soulLayer,
          moduleType: module.moduleType,
          difficulty: module.difficulty,
          estimatedDuration: module.estimatedDuration,
          prerequisites: module.prerequisites || [],
          content: module.moduleContent || [],
          assessments: module.moduleAssessments || [],
          isCompleted: !!completion,
          progress: completion ? 100 : 0,
        };
      });

      logger.info('Available modules retrieved', {
        userId,
        soulLayer,
        difficulty,
        count: healingModules.length,
      });

      return healingModules;
    } catch (error) {
      logger.error('Error getting available modules', {
        userId,
        soulLayer,
        error: error.message,
      });
      throw new AppError('Failed to get available modules', 500);
    }
  }

  /**
   * Get specific module details
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Module details
   */
  async getModuleDetails(
    userId: string,
    moduleId: string
  ): Promise<HealingModule> {
    try {
      const module = await prisma.healingModule.findUnique({
        where: { id: moduleId },
        include: {
          moduleContent: true,
          moduleAssessments: {
            include: {
              assessmentQuestions: true,
            },
          },
          moduleCompletions: {
            where: { userId },
          },
        },
      });

      if (!module) throw new AppError('Module not found', 404);

      // Check if user has access to this module
      const hasAccess = await this.checkModuleAccess(userId, moduleId);
      if (!hasAccess) {
        throw new AppError('Access denied to this module', 403);
      }

      const completion = module.moduleCompletions?.find(
        (c: any) => c.userId === userId
      );

      const healingModule: HealingModule = {
        id: module.id,
        title: module.title,
        description: module.description,
        soulLayer: module.soulLayer,
        moduleType: module.moduleType,
        difficulty: module.difficulty,
        estimatedDuration: module.estimatedDuration,
        prerequisites: module.prerequisites || [],
        content:
          module.moduleContent?.sort((a: any, b: any) => a.order - b.order) ||
          [],
        assessments:
          module.moduleAssessments?.map((assessment: any) => ({
            ...assessment,
            questions: assessment.assessmentQuestions || [],
          })) || [],
        isCompleted: !!completion,
        progress: completion ? 100 : 0,
      };

      logger.info('Module details retrieved', {
        userId,
        moduleId,
        isCompleted: healingModule.isCompleted,
      });

      return healingModule;
    } catch (error) {
      logger.error('Error getting module details', {
        userId,
        moduleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to get module details', 500);
    }
  }

  /**
   * Start a module session
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Session information
   */
  async startModuleSession(userId: string, moduleId: string): Promise<any> {
    try {
      // Check prerequisites
      const hasPrerequisites = await this.checkPrerequisites(userId, moduleId);
      if (!hasPrerequisites) {
        throw new AppError('Prerequisites not met for this module', 400);
      }

      // Create module session
      const session = await prisma.moduleSession.create({
        data: {
          userId,
          moduleId,
          startedAt: new Date(),
          status: 'in_progress',
        },
      });

      logger.info('Module session started', {
        userId,
        moduleId,
        sessionId: session.id,
      });

      return {
        sessionId: session.id,
        startedAt: session.startedAt,
        status: session.status,
      };
    } catch (error) {
      logger.error('Error starting module session', {
        userId,
        moduleId,
        error: error.message,
      });
      throw error instanceof AppError
        ? error
        : new AppError('Failed to start module session', 500);
    }
  }

  /**
   * Complete a module
   * @param userId - User ID
   * @param moduleId - Module ID
   * @param sessionId - Session ID
   * @param assessmentResults - Assessment results
   * @param timeSpent - Time spent in minutes
   * @returns Completion result
   */
  async completeModule(
    userId: string,
    moduleId: string,
    sessionId: string,
    assessmentResults: any[],
    timeSpent: number
  ): Promise<ModuleCompletion> {
    try {
      // Calculate total score
      const totalScore = this.calculateModuleScore(assessmentResults);

      // Get module passing score
      const module = await prisma.healingModule.findUnique({
        where: { id: moduleId },
        select: { passingScore: true },
      });

      const passed = totalScore >= (module?.passingScore || 70);

      // Create completion record
      const completion = await prisma.moduleCompletion.create({
        data: {
          userId,
          moduleId,
          sessionId,
          score: totalScore,
          passed,
          timeSpent,
          assessmentResults,
          completedAt: new Date(),
        },
      });

      // Update session status
      await prisma.moduleSession.update({
        where: { id: sessionId },
        data: {
          status: 'completed',
          completedAt: new Date(),
        },
      });

      // Update user's journey progress
      await this.updateJourneyProgress(userId, moduleId);

      logger.info('Module completed', {
        userId,
        moduleId,
        sessionId,
        score: totalScore,
        passed,
      });

      return {
        id: completion.id,
        userId: completion.userId,
        moduleId: completion.moduleId,
        score: completion.score,
        completedAt: completion.completedAt,
        timeSpent: completion.timeSpent,
        feedback: passed
          ? 'Congratulations! You have successfully completed this module.'
          : 'Please review the material and try again.',
      };
    } catch (error) {
      logger.error('Error completing module', {
        userId,
        moduleId,
        sessionId,
        error: error.message,
      });
      throw new AppError('Failed to complete module', 500);
    }
  }

  /**
   * Get user's module progress
   * @param userId - User ID
   * @param journeyId - Journey ID (optional)
   * @returns Module progress
   */
  async getUserModuleProgress(
    userId: string,
    journeyId?: string
  ): Promise<any> {
    try {
      let query = prisma.moduleCompletion.findMany({
        where: {
          userId,
        },
        orderBy: { completedAt: 'desc' },
      });

      if (journeyId) {
        query = query.where({ journeyId });
      }

      const completions = await query;

      // Calculate progress statistics
      const progressStats = {
        totalCompleted: completions.length,
        averageScore:
          completions.length > 0
            ? completions.reduce((sum: number, c: any) => sum + c.score, 0) /
              completions.length
            : 0,
        completionsByLayer: this.groupCompletionsByLayer(completions),
        recentCompletions: completions.slice(0, 5),
        totalTimeSpent: completions.reduce(
          (sum: number, c: any) => sum + (c.timeSpent || 0),
          0
        ),
      };

      logger.info('Module progress retrieved', {
        userId,
        journeyId,
        totalCompleted: completions.length,
      });

      return progressStats;
    } catch (error) {
      logger.error('Error getting module progress', {
        userId,
        journeyId,
        error: error.message,
      });
      throw new AppError('Failed to get module progress', 500);
    }
  }

  /**
   * Check if user has access to module
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Whether user has access
   */
  private async checkModuleAccess(
    userId: string,
    moduleId: string
  ): Promise<boolean> {
    // Check if module is part of user's active journey
    const userJourney = await prisma.userJourney.findFirst({
      where: {
        userId,
        status: 'active',
      },
      select: { journeyType: true },
    });

    if (!userJourney) return true; // Allow access if no active journey

    const module = await prisma.healingModule.findUnique({
      where: { id: moduleId },
      select: { journeyTypes: true },
    });

    return module?.journeyTypes?.some(
      (jt) => jt.journeyType === userJourney.journeyType
    );
  }

  /**
   * Check if user meets module prerequisites
   * @param userId - User ID
   * @param moduleId - Module ID
   * @returns Whether prerequisites are met
   */
  private async checkPrerequisites(
    userId: string,
    moduleId: string
  ): Promise<boolean> {
    const module = await prisma.healingModule.findUnique({
      where: { id: moduleId },
      select: { prerequisites: true },
    });

    if (!module?.prerequisites || module.prerequisites.length === 0) {
      return true;
    }

    // Check if user has completed all prerequisite modules
    const completions = await prisma.moduleCompletion.findMany({
      where: {
        userId,
        passed: true,
        moduleId: {
          in: module.prerequisites,
        },
      },
      select: { moduleId: true },
    });

    return completions.length === module.prerequisites.length;
  }

  /**
   * Calculate module score from assessment results
   * @param assessmentResults - Assessment results
   * @returns Total score
   */
  private calculateModuleScore(assessmentResults: any[]): number {
    if (!assessmentResults || assessmentResults.length === 0) return 0;

    const totalPoints = assessmentResults.reduce(
      (sum, result) => sum + (result.points || 0),
      0
    );
    const maxPoints = assessmentResults.reduce(
      (sum, result) => sum + (result.maxPoints || 0),
      0
    );

    return maxPoints > 0 ? Math.round((totalPoints / maxPoints) * 100) : 0;
  }

  /**
   * Update user's journey progress
   * @param userId - User ID
   * @param moduleId - Completed module ID
   */
  private async updateJourneyProgress(
    userId: string,
    moduleId: string
  ): Promise<void> {
    // Get user's active journey
    const journey = await prisma.userJourney.findFirst({
      where: {
        userId,
        status: 'active',
      },
      select: {
        id: true,
        totalModules: true,
        completedModules: true,
      },
    });

    if (journey) {
      const newCompletedModules = (journey.completedModules || 0) + 1;
      const progress = (newCompletedModules / journey.totalModules) * 100;

      await prisma.userJourney.update({
        where: { id: journey.id },
        data: {
          completedModules: newCompletedModules,
          progress,
          lastActivity: new Date(),
        },
      });
    }
  }

  /**
   * Group completions by soul layer
   * @param completions - Module completions
   * @returns Grouped completions
   */
  private groupCompletionsByLayer(completions: any[]): Record<string, number> {
    return completions.reduce((acc, completion) => {
      const layer = completion.module?.soulLayer || 'unknown';
      acc[layer] = (acc[layer] || 0) + 1;
      return acc;
    }, {});
  }
}

export const moduleService = new ModuleService();
