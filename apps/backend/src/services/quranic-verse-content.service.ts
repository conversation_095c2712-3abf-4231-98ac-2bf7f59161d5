import { PrismaClient, QuranicVerseContent, Prisma } from '@prisma/client';

class NotFoundError extends <PERSON>rror {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

class InternalServerError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InternalServerError';
  }
}

class ConflictError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConflictError';
  }
}

export class QuranicVerseContentService {
  constructor(private prisma: PrismaClient) {}

  async create(data: Prisma.QuranicVerseContentCreateInput): Promise<QuranicVerseContent> {
    try {
      return await this.prisma.quranicVerseContent.create({ data });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        // Unique constraint failed (surahNumber, ayahNumber)
        throw new ConflictError('Quranic verse with this Surah and Ayah number already exists.');
      }
      throw new InternalServerError('Error creating Quranic verse content');
    }
  }

  async findAll(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.QuranicVerseContentWhereUniqueInput;
    where?: Prisma.QuranicVerseContentWhereInput;
    orderBy?: Prisma.QuranicVerseContentOrderByWithRelationInput;
  }): Promise<QuranicVerseContent[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      return await this.prisma.quranicVerseContent.findMany({
        skip,
        take,
        cursor,
        where,
        orderBy,
      });
    } catch (error) {
      throw new InternalServerError('Error fetching Quranic verse content');
    }
  }

  async findOne(id: string): Promise<QuranicVerseContent | null> {
    try {
      const content = await this.prisma.quranicVerseContent.findUnique({ where: { id } });
      if (!content) {
        throw new NotFoundError('Quranic verse content not found');
      }
      return content;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      throw new InternalServerError('Error fetching Quranic verse content');
    }
  }

  async findBySurahAyah(surahNumber: number, ayahNumber: number): Promise<QuranicVerseContent | null> {
    try {
      return await this.prisma.quranicVerseContent.findUnique({
        where: {
          surahNumber_ayahNumber: {
            surahNumber,
            ayahNumber
          }
        }
      });
    } catch (error) {
      throw new InternalServerError('Error fetching Quranic verse content by Surah/Ayah');
    }
  }

  async update(id: string, data: Prisma.QuranicVerseContentUpdateInput): Promise<QuranicVerseContent> {
    try {
      return await this.prisma.quranicVerseContent.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundError('Quranic verse content not found for update');
      }
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
         throw new ConflictError('Update conflicts with an existing Quranic verse (Surah/Ayah may already exist).');
      }
      throw new InternalServerError('Error updating Quranic verse content');
    }
  }

  async remove(id: string): Promise<QuranicVerseContent> {
    try {
      return await this.prisma.quranicVerseContent.delete({ where: { id } });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundError('Quranic verse content not found for deletion');
      }
      throw new InternalServerError('Error deleting Quranic verse content');
    }
  }
}
