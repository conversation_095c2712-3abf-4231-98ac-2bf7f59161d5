import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { triggerN8nWorkflow } from './n8n.service';
import { logger } from '../utils/logger';

interface ProgressMetrics {
  completionRate: number;
  totalModules: number;
  completedModules: number;
  averageScore: number;
  streakDays: number;
  layerProgress: Record<string, number>;
}

interface UserInsights {
  strengths: string[];
  improvements: string[];
  recommendations: string[];
  spiritualGrowth: number;
  consistencyScore: number;
}

interface AnalyticsData {
  progress: ProgressMetrics;
  insights: UserInsights;
  trends: any[];
  milestones: any[];
}

interface CrisisEffectivenessMetrics {
  timeToRegulation?: number; // in seconds
  techniqueEffectiveness?: number; // 1-5 rating
  userSatisfaction?: number; // 1-5 rating
  followUpEngagement?: boolean;
  crisisRecurrence?: boolean;
}

/**
 * Analytics Service - Handles user progress tracking and insights
 */
export class AnalyticsService {
  /**
   * Get user's overall progress analytics
   * @param userId - User ID
   * @param timeframe - Analysis timeframe (week, month, year)
   * @param layers - Specific soul layers to analyze
   * @returns Progress analytics data
   */
  async getUserProgress(
    userId: string,
    timeframe: string = 'month',
    layers?: string[]
  ): Promise<ProgressMetrics> {
    try {
      // Get module completion data
      const completions = await prisma.moduleCompletion.findMany({
        where: { userId },
      });

      // Get total modules assigned
      const totalModules = await prisma.journeyModule.findMany({
        where: { userId },
      });

      // Calculate completion metrics
      const completionRate =
        totalModules.length > 0
          ? (completions.length / totalModules.length) * 100
          : 0;

      // Calculate average score
      const averageScore =
        completions.length > 0
          ? completions.reduce(
              (sum: number, comp: any) => sum + (comp.score || 0),
              0
            ) / completions.length
          : 0;

      // Calculate streak days
      const streakDays = await this.calculateStreakDays(userId);

      // Calculate layer-specific progress
      const layerProgress = await this.calculateLayerProgress(
        userId,
        layers
      );

      const progressMetrics: ProgressMetrics = {
        completionRate,
        totalModules: totalModules.length,
        completedModules: completions.length,
        averageScore,
        streakDays,
        layerProgress,
      };

      logger.info('User progress calculated', {
        userId,
        timeframe,
        completionRate,
      });

      return progressMetrics;
    } catch (error) {
      logger.error('Error getting user progress', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get user progress', 500);
    }
  }

  /**
   * Generate personalized insights for user
   * @param userId - User ID
   * @param progressData - User's progress data
   * @returns Personalized insights
   */
  async generateInsights(
    userId: string,
    progressData: ProgressMetrics
  ): Promise<UserInsights> {
    try {
      // Get user's recent activities
      const recentActivities = await prisma.userActivity.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 50,
      });

      // Trigger AI insights generation
      const insightsPayload = {
        userId,
        progressData,
        recentActivities,
        timestamp: new Date().toISOString(),
      };

      const aiInsights = await triggerN8nWorkflow(
        'generate-insights',
        insightsPayload
      );

      // Process and validate insights
      const insights: UserInsights = {
        strengths: (aiInsights as any).strengths || [],
        improvements: (aiInsights as any).improvements || [],
        recommendations: (aiInsights as any).recommendations || [],
        spiritualGrowth: (aiInsights as any).spiritualGrowth || 0,
        consistencyScore: (aiInsights as any).consistencyScore || 0,
      };

      // Add Islamic-specific insights
      insights.recommendations.push(
        ...(await this.getIslamicRecommendations(progressData))
      );

      logger.info('User insights generated', {
        userId,
        insightsCount: insights.recommendations.length,
      });

      return insights;
    } catch (error) {
      logger.error('Error generating insights', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to generate insights', 500);
    }
  }

  /**
   * Get comprehensive analytics dashboard data
   * @param userId - User ID
   * @param timeframe - Analysis timeframe
   * @returns Complete analytics data
   */
  async getDashboardAnalytics(
    userId: string,
    timeframe: string = 'month'
  ): Promise<AnalyticsData> {
    logger.info("getDashboardAnalytics - ENTRY", { userId, timeframe });
    try {
      const progress = await this.getUserProgress(userId, timeframe);
      const insights = await this.generateInsights(userId, progress);
      const trends = await this.getTrends(userId, timeframe);
      const milestones = await this.getMilestones(userId);

      const analyticsData: AnalyticsData = {
        progress,
        insights,
        trends,
        milestones,
      };

      logger.info('getDashboardAnalytics - EXIT (Success)', { userId, timeframe });

      return analyticsData;
    } catch (error) {
      logger.error('Error in getDashboardAnalytics - EXIT (Error)', {
        userId,
        timeframe,
        error: error.message,
        stack: error.stack
      });
      throw new AppError('Failed to get dashboard analytics', 500);
    }
  }

  /**
   * Track user activity
   * @param userId - User ID
   * @param activityType - Type of activity
   * @param activityData - Activity data
   */
  async trackActivity(
    userId: string,
    activityType: string,
    activityData: any
  ): Promise<void> {
    try {
      await prisma.userActivity.create({
        data: {
          userId,
          activityType,
          activityData,
          createdAt: new Date(),
        },
      });

      logger.info('Activity tracked', { userId, activityType });
    } catch (error) {
      logger.error('Error tracking activity', {
        userId,
        activityType,
        error: error.message,
      });
      throw new AppError('Failed to track activity', 500);
    }
  }

  /**
   * Calculate user's streak days
   * @param userId - User ID
   * @returns Number of consecutive days
   */
  private async calculateStreakDays(userId: string): Promise<number> {
    const activities = await prisma.userActivity.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 30,
    });

    if (!activities || activities.length === 0) return 0;

    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    for (const activity of activities) {
      const activityDate = new Date(activity.createdAt);
      activityDate.setHours(0, 0, 0, 0);

      const daysDiff = Math.floor(
        (currentDate.getTime() - activityDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDiff === streak) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (daysDiff > streak) {
        break;
      }
    }

    return streak;
  }

  /**
   * Calculate progress for each soul layer
   * @param userId - User ID
   * @param layers - Specific layers to analyze
   * @returns Layer progress data
   */
  private async calculateLayerProgress(
    userId: string,
    layers?: string[]
  ): Promise<Record<string, number>> {
    const soulLayers = layers || ['jism', 'nafs', 'aql', 'qalb', 'ruh'];
    const layerProgress: Record<string, number> = {};

    for (const layer of soulLayers) {
      const layerCompletions = await prisma.moduleCompletion.findMany({
        where: { userId, soulLayer: layer },
      });
      const layerModules = await prisma.journeyModule.findMany({
        where: { userId, soulLayer: layer },
      });

      const progress =
        layerModules && layerModules.length > 0
          ? ((layerCompletions?.length || 0) / layerModules.length) * 100
          : 0;

      layerProgress[layer] = Math.round(progress);
    }

    return layerProgress;
  }

  /**
   * Get Islamic-specific recommendations based on progress
   * @param progressData - User's progress data
   * @returns Islamic recommendations
   */
  private async getIslamicRecommendations(
    progressData: ProgressMetrics
  ): Promise<string[]> {
    const recommendations: string[] = [];

    // Consistency recommendations
    if (progressData.streakDays < 7) {
      recommendations.push(
        'Establish daily dhikr routine for spiritual consistency'
      );
    }

    // Layer-specific recommendations
    Object.entries(progressData.layerProgress).forEach(([layer, progress]) => {
      if (progress < 50) {
        switch (layer) {
          case 'jism':
            recommendations.push(
              'Focus on Prophetic medicine and physical wellness'
            );
            break;
          case 'nafs':
            recommendations.push(
              'Increase istighfar and nafs purification practices'
            );
            break;
          case 'aql':
            recommendations.push(
              'Engage in Quranic reflection and Islamic learning'
            );
            break;
          case 'qalb':
            recommendations.push('Practice heart-centered dhikr and tawbah');
            break;
          case 'ruh':
            recommendations.push(
              'Strengthen connection with Allah through worship'
            );
            break;
        }
      }
    });

    return recommendations;
  }

  /**
   * Get user trends data
   * @param userId - User ID
   * @param timeframe - Analysis timeframe
   * @returns Trends data
   */
  private async getTrends(userId: string, timeframe: string): Promise<any[]> {
    // Implementation for trends calculation
    return [];
  }

  /**
   * Get user milestones
   * @param userId - User ID
   * @returns Milestones data
   */
  private async getMilestones(userId: string): Promise<any[]> {
    // Implementation for milestones calculation
    return [];
  }

  /**
   * Tracks the effectiveness of a crisis intervention session.
   * @param sessionId - The ID of the crisis session.
   * @param metrics - The effectiveness metrics for the session.
   */
  async trackCrisisInterventionEffectiveness(
    sessionId: string,
    metrics: CrisisEffectivenessMetrics
  ): Promise<void> {
    try {
      // Log the metrics to the database
      await prisma.crisisInterventionLog.create({
        data: {
          sessionId,
          timeToRegulation: metrics.timeToRegulation,
          techniqueEffectiveness: metrics.techniqueEffectiveness,
          userSatisfaction: metrics.userSatisfaction,
          followUpEngagement: metrics.followUpEngagement,
          crisisRecurrence: metrics.crisisRecurrence,
          loggedAt: new Date(),
        },
      });

      // Optionally trigger an n8n workflow for deeper analysis or reporting
      await triggerN8nWorkflow('crisis-effectiveness-analysis', {
        sessionId,
        metrics,
        timestamp: new Date().toISOString(),
      });

      logger.info('Crisis intervention effectiveness tracked', { sessionId, metrics });
    } catch (error) {
      logger.error('Error tracking crisis intervention effectiveness', {
        sessionId,
        error: error.message,
      });
      throw new AppError('Failed to track crisis intervention effectiveness', 500);
    }
  }
}

export const analyticsService = new AnalyticsService();
