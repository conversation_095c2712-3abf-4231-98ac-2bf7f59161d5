import { setupSupabase } from '../config/supabase';
import type { SupabaseClient } from '@supabase/supabase-js'; // Import SupabaseClient type
import { logger } from '../utils/logger';
import { AppError } from '../middleware/errorHandler';
import axios from 'axios';

interface ModelConfig {
  temperature: number;
  maxTokens: number;
  presencePenalty: number;
  frequencyPenalty: number;
}

interface AIServiceConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
}

interface SymptomData {
  jism: string[];
  nafs: string[];
  aql: string[];
  qalb: string[];
  ruh: string[];
  intensity: Record<string, number>;
  duration: string;
  additionalNotes?: string;
}

interface AnalysisResult {
  primaryLayer: string;
  affectedLayers: string[];
  recommendations: Recommendation[];
  insights: string[];
  severity: number;
  confidence: number;
}

interface Recommendation {
  type: 'dhikr' | 'quran' | 'dua' | 'practice' | 'ruqyah';
  content: string;
  frequency: string;
  duration: string;
  priority: number;
}

// Types for Feature 1: Spiritual Landscape Analysis
// Matches SpiritualLandscapeRequest in ai-service
export interface AIServiceUserProfileData { // Added export
  user_id: string;
  mental_health_awareness?: Record<string, any>;
  ruqya_knowledge?: Record<string, any>;
  spiritual_optimizer?: Record<string, any>;
  professional_context?: Record<string, any>;
  demographics?: Record<string, any>;
  life_circumstances?: Record<string, any>;
  // Ensure this matches the structure expected by the AI service,
  // which should be derived from the backend's UserProfile model/type.
}

export interface AIServiceSymptomExperienceData {
  symptoms?: string[];
  intensity?: string | null; // "mild", "moderate", "severe"
  user_reflection?: string | null;
}

export interface AIComprehensiveAssessmentDataType {
  user_profile: AIServiceUserProfileData;
  physical_experiences?: AIServiceSymptomExperienceData | null;
  emotional_experiences?: AIServiceSymptomExperienceData | null;
  mental_experiences?: AIServiceSymptomExperienceData | null;
  spiritual_experiences?: AIServiceSymptomExperienceData | null;
  reflections?: Record<string, string> | null;
  session_metadata?: Record<string, any> | null;
}

// Matches LayerAnalysisOutput in ai-service
export interface AILayerAnalysisOutput {
  layer: string;
  layer_name?: string;
  insights: string[];
  recommendations: string[];
  islamic_context: string | string[];
  severity_score: number;
  affected_symptoms?: string[] | any[];
}

// Matches SpiritualLandscapeResponse in ai-service
export interface AISpiritualLandscapeResponse {
  id?: string;
  primary_layer: string;
  layer_insights: Record<string, AILayerAnalysisOutput>;
  personalized_message: string;
  islamic_insights: string[];
  educational_content: string;
  crisis_level: string; // none, low, moderate, high, critical
  crisis_indicators: string[];
  immediate_actions: string[]; // For crisis
  next_steps: string[]; // Post-diagnosis next steps
  recommended_journey_type: string;
  estimated_healing_duration: number; // in days
  confidence: number;
}

// Types for Personalized Assessment Welcome
// Matches WelcomeAction Pydantic model in ai-service
export interface AIWelcomeAction {
  id: string;
  text: string;
  description?: string | null;
}

// Matches PersonalizedWelcomeResponse Pydantic model in ai-service
export interface AIPersonalizedWelcomeResponse {
  user_id: string;
  user_type: string;
  greeting: string;
  introduction: string;
  explanation?: string | null;
  motivation?: string | null;
  primary_action: AIWelcomeAction;
  secondary_actions: AIWelcomeAction[];
}

/**
 * AI Service Class - Handles all AI-related operations for Islamic mental wellness
 */
export class AIService {
  private supabase: SupabaseClient | null; // Typed supabase
  private modelConfigs: Record<string, ModelConfig>;
  private aiServiceConfig: AIServiceConfig;

  constructor() {
    try {
      this.supabase = setupSupabase(); // Assuming setupSupabase() returns a SupabaseClient
      logger.info('AI service initialized with Supabase connection');
    } catch (error: any) { // Explicitly type error
      logger.warn('AI service initialized without Supabase connection', {
        error: error.message,
        note: 'Using mock mode for development',
      });
      this.supabase = null; // For testing purposes, ensures supabase is always defined
    }

    // Configure AI service connection
    this.aiServiceConfig = {
      baseUrl: process.env.AI_SERVICE_URL || 'http://localhost:8000',
      apiKey: process.env.AI_SERVICE_TOKEN || 'mock-token',
      timeout: 30000, // 30 seconds
    };

    this.modelConfigs = {
      symptomAnalysis: {
        temperature: 0.3,
        maxTokens: 1000,
        presencePenalty: 0.1,
        frequencyPenalty: 0.1,
      },
      journeyPlanning: {
        temperature: 0.4,
        maxTokens: 1500,
        presencePenalty: 0.2,
        frequencyPenalty: 0.2,
      },
      contentRecommendation: {
        temperature: 0.5,
        maxTokens: 800,
        presencePenalty: 0.1,
        frequencyPenalty: 0.1,
      },
    };
  }

  /**
   * Analyze user symptoms and generate Islamic healing insights
   * @param userId - User ID
   * @param symptoms - Symptom data across 5 soul layers
   * @returns Analysis results with Islamic recommendations
   */
  async analyzeSymptoms(
    userId: string,
    symptoms: SymptomData
  ): Promise<AnalysisResult> {
    try {
      // Get user's history and context
      let userHistory = [];
      if (this.supabase !== null) { // Explicitly check for null
        const { data } = await this.supabase
          .from('health_assessments')
          .select('*')
          .eq('user_id', userId)
          .order('assessment_date', { ascending: false })
          .limit(5);
        userHistory = data || [];
      } else {
        // Mock data for development
        userHistory = [];
        logger.debug('Using mock user history for development');
      }

      // Call AI service for symptom analysis
      const analysisPayload = {
        user_id: userId,
        symptoms: {
          jism: symptoms.jism,
          nafs: symptoms.nafs,
          aql: symptoms.aql,
          qalb: symptoms.qalb,
          ruh: symptoms.ruh,
        },
        intensity: symptoms.intensity,
        duration: symptoms.duration,
        additional_notes: symptoms.additionalNotes,
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/analyze-symptoms`,
        analysisPayload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      console.log('Maaiz123 in getNex');

      const analysisResult = response.data;

      // Process and validate results
      const processedResult = await this.processAnalysisResult(
        analysisResult,
        symptoms
      );

      // Store analysis in database
      if (this.supabase) {
        await this.storeAnalysisResult(userId, symptoms, processedResult);
      } else {
        logger.debug('Skipping database storage in development mode');
      }

      logger.info('Symptom analysis completed', {
        userId,
        primaryLayer: processedResult.primaryLayer,
      });

      return processedResult;
    } catch (error) {
      logger.error('Error in symptom analysis', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to analyze symptoms', 500);
    }
  }

  /**
   * Generate user profile from onboarding responses via AI service
   * @param responses - Collected onboarding responses
   * @param userId - User ID
   * @param sessionId - Onboarding Session ID
   * @returns Profile data, recommended pathway, and personalization settings from AI
   */
  async generateProfileFromOnboarding(
    responses: Record<string, any>,
    userId: string,
    sessionId: string
  ): Promise<any> {
    try {
      const payload = {
        userId,
        responses,
        sessionId,
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/profiles/generate`, // Updated endpoint
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const result = response.data;

      logger.info('AI Profile generation completed for user', {
        userId,
        sessionId,
        recommendedPathway: result.recommendedPathway,
      });

      if (!result) {
        throw new AppError('AI service returned empty profile data', 500);
      }
      return result; // This should match OnboardingProfileResponse from AI service
    } catch (error) {
      logger.error('Error generating profile from onboarding via AI', {
        userId,
        sessionId,
        error: error.message,
      });
      // Consider if a more specific error or a default profile structure should be returned
      throw new AppError('Failed to generate profile using AI service', 500);
    }
  }

  /**
   * Generate personalized healing journey based on analysis
   * @param userId - User ID
   * @param analysisResult - Previous symptom analysis
   * @returns Personalized journey plan
   */
  async generateHealingJourney(
    userId: string,
    analysisResult: AnalysisResult
  ): Promise<any> {
    try {
      const journeyPayload = {
        user_id: userId,
        focus_layers: analysisResult.affectedLayers,
        duration_days: 21, // Default 21-day journey
        intensity_level:
          analysisResult.severity > 7
            ? 'intensive'
            : analysisResult.severity > 4
            ? 'moderate'
            : 'light',
        specific_goals: analysisResult.recommendations.map((r) => r.content),
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/generate-journey`,
        journeyPayload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const journey = response.data;

      // Store journey in database
      if (this.supabase) {
        await this.storeJourneyPlan(userId, journey);
      } else {
        logger.debug('Skipping journey storage in development mode');
      }

      logger.info('Healing journey generated', {
        userId,
        journeyId: (journey as any).id,
      });

      return journey;
    } catch (error) {
      logger.error('Error generating healing journey', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to generate healing journey', 500);
    }
  }

  /**
   * Get personalized Islamic content recommendations
   * @param userId - User ID
   * @param context - Current context (symptoms, mood, etc.)
   * @returns Recommended content
   */
  async getContentRecommendations(
    userId: string,
    context: any
  ): Promise<any[]> {
    try {
      const recommendationPayload = {
        user_id: userId,
        healing_focus: context.healingFocus || ['general'],
        current_mood: context.currentMood,
        time_available: context.timeAvailable,
        content_types: context.contentTypes,
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/recommend-content`,
        recommendationPayload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const recommendations = response.data;

      logger.info('Content recommendations generated', {
        userId,
        count: (recommendations as any).length || 0,
      });

      return (recommendations as any) || [];
    } catch (error) {
      logger.error('Error getting content recommendations', {
        userId,
        error: error.message,
      });
      throw new AppError('Failed to get content recommendations', 500);
    }
  }

  /**
   * Analyze crisis indicators in user responses
   * @param responses - User responses to analyze
   * @returns Crisis analysis result
   */
  async analyzeCrisisIndicators(responses: Record<string, any>): Promise<any> {
    try {
      const payload = {
        response: responses,
        stepId: 'onboarding_analysis',
        context: 'onboarding',
        userId: responses.userId,
      };

      const response = await axios.post(
        `${this.aiServiceConfig.baseUrl}/crisis-analysis/analyze`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      const result = response.data;

      logger.info('Crisis analysis completed', {
        level: result.level,
        confidence: result.confidence,
      });

      return result;
    } catch (error) {
      logger.error('Error analyzing crisis indicators', {
        error: error.message,
      });

      // Return safe default for critical systems
      return {
        level: 'moderate',
        confidence: 0.5,
        indicators: ['analysis_error'],
        urgency: 'moderate',
        recommended_actions: ['enhanced_support', 'manual_review'],
      };
    }
  }

  /**
   * Process and validate AI analysis results
   * @param rawResult - Raw AI analysis result
   * @param originalSymptoms - Original symptom data
   * @returns Processed and validated result
   */
  private async processAnalysisResult(
    rawResult: any,
    originalSymptoms: SymptomData
  ): Promise<AnalysisResult> {
    // Map AI service response to our internal format
    const processedResult: AnalysisResult = {
      primaryLayer: rawResult.primary_layers_affected?.[0] || 'nafs',
      affectedLayers: rawResult.primary_layers_affected || ['nafs'],
      recommendations:
        rawResult.immediate_actions?.map((action: string, index: number) => ({
          type: 'practice' as const,
          content: action,
          frequency: 'Daily',
          duration: '1 week',
          priority: index + 1,
        })) || [],
      insights: [rawResult.spotlight] || [],
      severity: this.mapSeverityToNumber(rawResult.severity_level),
      confidence: 0.8, // Default confidence
    };

    // Add Islamic validation and fallbacks
    if (!processedResult.recommendations.length) {
      processedResult.recommendations = await this.getDefaultRecommendations(
        processedResult.primaryLayer
      );
    }

    return processedResult;
  }

  private mapSeverityToNumber(severityLevel: string): number {
    const severityMap: Record<string, number> = {
      mild: 3,
      moderate: 5,
      severe: 8,
    };
    return severityMap[severityLevel] || 5;
  }

  /**
   * Get default Islamic recommendations for a soul layer
   * @param layer - Soul layer (jism, nafs, aql, qalb, ruh)
   * @returns Default recommendations
   */
  private async getDefaultRecommendations(
    layer: string
  ): Promise<Recommendation[]> {
    const defaultRecommendations: Record<string, Recommendation[]> = {
      jism: [
        {
          type: 'dua',
          content: 'Allahumma ashfi wa anta ash-shafi',
          frequency: '3 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      nafs: [
        {
          type: 'dhikr',
          content: 'La hawla wa la quwwata illa billah',
          frequency: '100 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      aql: [
        {
          type: 'quran',
          content: 'Surah Al-Fatiha',
          frequency: '7 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      qalb: [
        {
          type: 'dhikr',
          content: 'Astaghfirullah',
          frequency: '100 times daily',
          duration: '1 week',
          priority: 1,
        },
      ],
      ruh: [
        {
          type: 'practice',
          content: 'Tahajjud prayer',
          frequency: 'Daily',
          duration: '1 week',
          priority: 1,
        },
      ],
    };

    return defaultRecommendations[layer] || defaultRecommendations.nafs;
  }

  /**
   * Store analysis result in database
   * @param userId - User ID
   * @param symptoms - Original symptoms
   * @param result - Analysis result
   */
  private async storeAnalysisResult(
    userId: string,
    symptoms: SymptomData,
    result: AnalysisResult
  ): Promise<void> {
    await this.supabase.from('health_assessments').insert({
      user_id: userId,
      symptoms_data: symptoms,
      analysis_result: result,
      assessment_date: new Date().toISOString(),
      primary_layer: result.primaryLayer,
      severity_score: result.severity,
    });
  }

  /**
   * Store journey plan in database
   * @param userId - User ID
   * @param journey - Journey plan
   */
  private async storeJourneyPlan(userId: string, journey: any): Promise<void> {
    await this.supabase.from('healing_journeys').insert({
      user_id: userId,
      journey_data: journey,
      created_at: new Date().toISOString(),
      status: 'active',
    });
  }

  /**
   * Perform comprehensive spiritual landscape analysis using AI service
   * @param analysisData - Comprehensive data from user's assessment session
   * @returns Detailed spiritual diagnosis from AI
   */
  async analyzeSpiritualLandscape(
    analysisData: AIComprehensiveAssessmentDataType
  ): Promise<AISpiritualLandscapeResponse> {
    try {
      logger.info('Sending data for spiritual landscape analysis', {
        userId: analysisData.user_profile.user_id,
      });
      const response = await axios.post<AISpiritualLandscapeResponse>(
        `${this.aiServiceConfig.baseUrl}/analyze-spiritual-landscape`,
        analysisData,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout * 2, // Potentially longer timeout for this complex call
        }
      );

      logger.info('Spiritual landscape analysis received successfully', {
        userId: analysisData.user_profile.user_id,
      });
      return response.data;
    } catch (error) {
      const userId = analysisData?.user_profile?.user_id || 'unknown';
      if (axios.isAxiosError(error)) {
        logger.error(
          `Axios error in spiritual landscape analysis for user ${userId}: ${error.message}`,
          {
            status: error.response?.status,
            data: error.response?.data,
            url: `${this.aiServiceConfig.baseUrl}/analyze-spiritual-landscape`,
          }
        );
        throw new AppError(
          error.response?.data?.detail ||
            'AI service failed to analyze spiritual landscape',
          error.response?.status || 500
        );
      } else {
        logger.error(
          `Unexpected error in spiritual landscape analysis for user ${userId}: ${error.message}`,
          { error }
        );
        throw new AppError(
          'An unexpected error occurred while analyzing spiritual landscape',
          500
        );
      }
    }
  }

  /**
   * Get personalized welcome message for assessment from AI service
   * @param userProfileData - User's profile data from Feature 0
   * @returns Personalized welcome content
   */
  async getAIPersonalizedWelcome(
    userProfileData: AIServiceUserProfileData // Reusing existing interface for user profile
  ): Promise<AIPersonalizedWelcomeResponse> {
    try {
      logger.info(
        'Requesting personalized assessment welcome from AI service',
        { userId: userProfileData.user_id }
      );
      const response = await axios.post<AIPersonalizedWelcomeResponse>(
        `${this.aiServiceConfig.baseUrl}/generate-assessment-welcome`,
        userProfileData, // Send the whole user profile
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.aiServiceConfig.apiKey}`,
          },
          timeout: this.aiServiceConfig.timeout,
        }
      );

      logger.info('Personalized assessment welcome received from AI service', {
        userId: userProfileData.user_id,
      });
      // Transform snake_case to camelCase for frontend compatibility
      const transformedData = {
        ...response.data,
        primaryAction: response.data.primary_action,
        secondaryActions: response.data.secondary_actions,
      };
      // Remove original snake_case keys if desired, or let frontend handle it
      delete transformedData.primary_action;
      delete transformedData.secondary_actions;
      return transformedData;
    } catch (error) {
      const userId = userProfileData?.user_id || 'unknown';
      if (axios.isAxiosError(error)) {
        logger.error(
          `Axios error fetching personalized welcome for user ${userId}: ${error.message}`,
          {
            status: error.response?.status,
            data: error.response?.data,
            url: `${this.aiServiceConfig.baseUrl}/generate-assessment-welcome`,
          }
        );
        throw new AppError(
          error.response?.data?.detail ||
            'AI service failed to generate personalized welcome',
          error.response?.status || 500
        );
      } else {
        logger.error(
          `Unexpected error fetching personalized welcome for user ${userId}: ${error.message}`,
          { error }
        );
        throw new AppError(
          'An unexpected error occurred while generating personalized welcome',
          500
        );
      }
    }
  }

  async generateJourneyParameters(data: any): Promise<any> {
    logger.info('Mock AI: Generating journey parameters', { data });
    
    // Use preferences if provided, otherwise use defaults
    const preferences = data.preferences || {};
    const assessment = data.assessment || {};
    
    // Map to valid Prisma JourneyType enum values based on primary layer
    let journeyType = 'heart_purification'; // default
    const primaryLayer = assessment.primary_layer || 'jism';
    
    if (primaryLayer === 'jism') {
      journeyType = 'heart_purification'; // Physical wellness through heart purification
    } else if (primaryLayer === 'nafs') {
      journeyType = 'ego_purification'; // Ego/self purification
    } else if (primaryLayer === 'aql') {
      journeyType = 'tranquil_mind'; // Mental clarity and peace
    } else if (primaryLayer === 'qalb') {
      journeyType = 'heart_purification'; // Heart purification
    } else if (primaryLayer === 'ruh') {
      journeyType = 'spiritual_optimization'; // Spiritual growth and optimization
    }
    
    // Check for crisis indicators and override if needed
    if (assessment.crisis_level && assessment.crisis_level !== 'none') {
      journeyType = 'crisis_recovery';
    }
    
    return {
      duration: preferences.duration || 40,
      timeCommitment: preferences.dailyTimeCommitment || 30,
      primaryLayer: primaryLayer,
      secondaryLayers: assessment.secondary_layers || ['nafs', 'aql'],
      ruqyaLevel: preferences.ruqyaIntegrationLevel || 'basic',
      communitySupport: preferences.communityIntegration !== false,
      culturalAdaptations: preferences.culturalAdaptations || [],
      type: journeyType, // Now uses valid Prisma enum value
      recommendations: assessment.next_steps || ['Begin with daily dhikr', 'Focus on physical wellness'],
    };
  }

  async generateJourneyContent(data: any): Promise<any> {
    logger.info('Mock AI: Generating journey content', { data });
    
    const config = data.config || {};
    const duration = config.duration || 40;
    const primaryLayer = config.primaryLayer || 'jism';
    
    return {
      title: `${duration}-Day Sunnah-Based Physical Well-being (Jism) Journey`,
      description: `A comprehensive ${duration}-day healing journey focusing on ${primaryLayer} layer purification through Islamic practices.`,
      personalizedWelcome: `Assalamu Alaikum! Welcome to your personalized ${duration}-day healing journey. This journey has been specially designed based on your assessment to focus on your ${primaryLayer} layer.`,
      days: Array.from({ length: duration }, (_, i) => ({
        dayNumber: i + 1,
        theme: `Day ${i + 1}: ${primaryLayer} Healing Focus`,
        learningObjective: `Strengthen your ${primaryLayer} through Islamic practices`,
        reflectionPrompts: [
          `How did you feel during today's practices?`,
          `What insights did you gain about your ${primaryLayer}?`
        ],
        communityActivity: i % 7 === 0 ? `Week ${Math.floor(i/7) + 1} reflection sharing` : null,
        progressMilestone: i === 6 ? 'First week completed' : i === 20 ? 'Three weeks of consistent practice' : null,
        adaptiveContent: {},
        practices: [
          {
            type: 'MorningCheckIn',
            title: 'Morning Spiritual Check-In',
            description: 'Begin your day with intention and spiritual awareness. Set your niyyah and assess your current state.',
            duration: 2,
            instructions: 'Take a moment to center yourself and honestly assess your spiritual and emotional state.',
            layerFocus: 'qalb',
            difficultyLevel: 'beginner',
            ruqyaComponent: false,
            benefits: ['Establishes daily spiritual baseline', 'Creates mindful intention setting', 'Builds self-awareness', 'Connects you with Allah at day\'s start'],
            componentDetails: {
              moodScaleMin: 1,
              moodScaleMax: 10,
              moodDescriptorLow: 'Heavy',
              moodDescriptorHigh: 'Light',
              energyPrompt: 'How is your energy level today?',
              spiritualStatePrompt: 'Describe your spiritual state in a few words:',
              intentionPrompt: 'What is your niyyah (intention) for today?'
            }
          },
          {
            type: 'NameOfAllahSpotlight',
            title: 'Ar-Rahman (The Most Merciful)',
            description: 'Reflect deeply on Allah\'s infinite mercy and how it manifests in your life.',
            duration: 8,
            instructions: 'Contemplate the meaning of Ar-Rahman and recite it with presence and devotion.',
            arabicText: 'الرَّحْمَن',
            transliteration: 'Ar-Rahman',
            translation: 'The Most Merciful, The Beneficent',
            layerFocus: 'qalb',
            difficultyLevel: 'beginner',
            ruqyaComponent: false,
            benefits: ['Deepens connection with Allah\'s mercy', 'Softens the heart', 'Increases gratitude', 'Promotes self-compassion'],
            componentDetails: {
              name: 'Ar-Rahman',
              arabicScript: 'الرَّحْمَن',
              meaning: 'Ar-Rahman refers to Allah\'s all-encompassing mercy that extends to all creation, believers and non-believers alike.',
              significance: 'This beautiful name reminds us that Allah\'s mercy encompasses everything and everyone.',
              reflectionPrompt: 'How have you experienced Allah\'s mercy in your life today?',
              practicalApplication: 'Practice showing mercy and compassion to others as a reflection of Allah\'s mercy.',
              dhikrCount: 33,
              audioUrl: '/audio/ar-rahman-recitation.mp3'
            }
          },
          {
            type: 'QuranicVerseReflection',
            title: 'Verse of Comfort and Hope',
            description: 'Find peace and guidance in Allah\'s words, specifically chosen for your healing journey.',
            duration: 10,
            instructions: 'Read, listen, and reflect on this verse. Consider how it applies to your current situation.',
            arabicText: 'وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ ۚ إِنَّ اللَّهَ بَالِغُ أَمْرِهِ ۚ قَدْ جَعَلَ اللَّهُ لِكُلِّ شَيْءٍ قَدْرًا',
            transliteration: 'Wa man yatawakkal \'ala Allahi fa-huwa hasbuhu. Inna Allaha baligu amrihi. Qad ja\'ala Allahu li-kulli shay\'in qadra.',
            translation: 'And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose. Allah has already set for everything a [decreed] extent.',
            layerFocus: 'qalb',
            difficultyLevel: 'beginner',
            ruqyaComponent: false,
            benefits: ['Builds trust in Allah (Tawakkul)', 'Reduces anxiety and worry', 'Strengthens faith', 'Provides divine perspective'],
            componentDetails: {
              arabicText: 'وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ ۚ إِنَّ اللَّهَ بَالِغُ أَمْرِهِ ۚ قَدْ جَعَلَ اللَّهُ لِكُلِّ شَيْءٍ قَدْرًا',
              translationEn: 'And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose. Allah has already set for everything a [decreed] extent.',
              surahName: 'At-Talaq',
              verseNumber: 3,
              contextualExplanation: 'This verse reminds us that Allah is sufficient for those who trust in Him completely.',
              reflectionPrompts: ['What areas of your life need more tawakkul (trust in Allah)?', 'How can you strengthen your reliance on Allah today?'],
              practicalApplication: 'When facing challenges, remember to place your trust in Allah and make du\'a.',
              audioUrl: '/audio/quran-65-3.mp3'
            }
          },
          {
            type: 'PersonalReflectionJournaling',
            title: 'Daily Spiritual Reflection',
            description: 'Process your spiritual insights and track your growth through mindful journaling.',
            duration: 10,
            instructions: 'Write honestly about your spiritual journey. Use the prompts to guide your reflection.',
            layerFocus: 'aql',
            difficultyLevel: 'beginner',
            ruqyaComponent: false,
            benefits: ['Processes spiritual insights', 'Tracks personal growth', 'Increases self-awareness', 'Strengthens connection with Allah'],
            componentDetails: {
              prompts: [
                'What did I learn about myself today?',
                'How did I see Allah\'s guidance in my day?',
                'What am I grateful for?',
                'What challenges did I face and how did I handle them?'
              ],
              gratitudeCount: 3,
              voiceNoteOption: true
            }
          },
          {
            type: 'SunnahPractice',
            title: 'Mindful Wudu for Heart Purification',
            description: 'Transform your ablution into a mindful spiritual practice following the Sunnah.',
            duration: 8,
            instructions: 'Perform wudu with complete presence and intention, using it as a means of spiritual purification.',
            layerFocus: 'jism',
            difficultyLevel: 'beginner',
            ruqyaComponent: false,
            benefits: ['Combines physical and spiritual cleansing', 'Follows prophetic example', 'Increases mindfulness', 'Prepares heart for prayer'],
            componentDetails: {
              steps: [
                {
                  action: 'Intention (Niyyah)',
                  dua: 'Bismillah',
                  mindfulness: 'Set intention for spiritual purification'
                },
                {
                  action: 'Wash hands three times',
                  dua: 'Allahumma ighfir li dhanbi wa wassi\' li fi dari wa barik li fi rizqi',
                  mindfulness: 'Feel the water cleansing away impurities'
                },
                {
                  action: 'Rinse mouth three times',
                  dua: 'Allahumma a\'inni \'ala dhikrika wa shukrika wa husni \'ibadatika',
                  mindfulness: 'Purify your speech and intentions'
                }
              ],
              finalDua: 'Ash-hadu an la ilaha illa Allah wahdahu la sharika lahu, wa ash-hadu anna Muhammadan \'abduhu wa rasuluhu.'
            }
          }
        ]
      })),
    };
  }

  async generateAdaptiveRecommendations(data: any): Promise<any[]> {
    logger.info('Mock AI: Generating adaptive recommendations', { data });
    return [{ recommendation: 'Consider reflecting on Surah Al-Asr.' }];
  }

  async matchCommunitySupport(data: any): Promise<any> {
    logger.info('Mock AI: Matching community support', { data });
    // Return null values for now since we don't have real community groups set up
    return {
      groupId: null,
      mentorId: null,
      peerConnections: [],
    };
  }
}

export const aiService = new AIService();
