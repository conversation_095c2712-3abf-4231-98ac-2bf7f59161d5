import { Prisma } from '@prisma/client'; // Import Prisma types if needed
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

// Assuming Prisma Models: Ru<PERSON>yaSession, R<PERSON><PERSON>ya<PERSON>erse, RuqyaDua, RuqyaProgram, RuqyaProgramSession, RuqyaPractitioner, RuqyaActivity

interface RuqyaSessionDTO {
  id: string;
  userId: string;
  sessionType: 'self_ruqya' | 'guided_ruqya' | 'protection' | 'healing';
  purpose: string;
  duration: number; // This might be calculated or stored differently
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'paused';
  verses: RuqyaVerseDTO[];
  duas: RuqyaDuaDTO[];
  progress: number;
  programId?: string; // Added from insert
}

interface RuqyaVerseDTO {
  id: string;
  surah: string;
  ayahNumber: number;
  arabic: string;
  transliteration: string | null; // Prisma model might have these as nullable
  translation: string | null;
  purpose: string[];
  recitationUrl?: string | null;
  order: number;
}

interface RuqyaDuaDTO {
  id: string;
  title: string;
  arabic: string;
  transliteration: string | null;
  translation: string | null;
  purpose: string[];
  source: string | null;
  recitationUrl?: string | null;
  order: number;
}

interface RuqyaProgramDTO {
  id: string;
  name: string;
  description: string;
  type: 'protection' | 'healing' | 'general' | 'specific_ailment';
  duration: number; // Assuming this maps to estimatedDuration in Prisma
  sessions: RuqyaSessionDTO[]; // This will be complex if it's a deep relation
  targetAilments: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

interface RuqyaPractitionerDTO {
  id: string;
  name: string;
  qualifications: string[];
  specializations: string[];
  location: string;
  contactInfo: string | null;
  isVerified: boolean;
  rating: number | null;
  availability: string | null;
}

/**
 * Ruqya Service - Manages Islamic spiritual healing and protection
 */
export class RuqyaService {
  constructor() {
    // No Supabase initialization
  }

  async startRuqyaSession(
    userId: string,
    sessionType: string,
    purpose: string,
    programId?: string
  ): Promise<RuqyaSessionDTO> {
    try {
      const verses = await this.getRuqyaVerses(sessionType, purpose);
      const duas = await this.getRuqyaDuas(sessionType, purpose);

      const session = await prisma.ruqyaSession.create({
        data: {
          userId,
          sessionType,
          purpose,
          programId, // Optional: ensure Prisma schema handles this (e.g., program: { connect: { id: programId } })
          startTime: new Date(),
          status: 'active',
          // TODO: Check Prisma version. For newer versions, direct assignment might work if the field is Json.
          versesData: verses as any, // TODO: Use correct Prisma JsonValue type
          duasData: duas as any,     // TODO: Use correct Prisma JsonValue type
          progress: 0, // Initial progress
          // duration might be calculated or set later
        },
      });

      await this.logRuqyaActivity(userId, 'session_started', {
        sessionId: session.id,
        sessionType,
        purpose,
      } as any); // TODO: Use correct Prisma JsonObject type

      logger.info('Ruqya session started', {
        userId,
        sessionId: session.id,
        sessionType,
        purpose,
      });

      return {
        id: session.id,
        userId: session.userId,
        sessionType: session.sessionType as RuqyaSessionDTO['sessionType'],
        purpose: session.purpose,
        duration: session.duration || 0, // Default if not set, or calculate
        startTime: session.startTime,
        status: session.status as RuqyaSessionDTO['status'],
        verses, // These are already DTOs from getRuqyaVerses
        duas,   // These are already DTOs from getRuqyaDuas
        progress: session.progress || 0,
        programId: session.programId || undefined,
      };
    } catch (error) {
      logger.error('Error starting Ruqya session', {
        userId,
        sessionType,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to start Ruqya session', 500);
    }
  }

  async getRuqyaVerses(
    sessionType: string,
    purpose: string
  ): Promise<RuqyaVerseDTO[]> {
    try {
      const verses = await prisma.ruqyaVerse.findMany({
        where: {
          sessionTypes: { has: sessionType }, // Assuming sessionTypes is an array in Prisma model
          purposes: { has: purpose },       // Assuming purposes is an array
          isActive: true,
        },
        orderBy: { orderIndex: 'asc' }, // Assuming field is orderIndex
      });

      const ruqyaVersesDTO: RuqyaVerseDTO[] = verses.map((verse) => ({
        id: verse.id,
        surah: verse.surah,
        ayahNumber: verse.ayahNumber,
        arabic: verse.arabicText, // Assuming field name mapping
        transliteration: verse.transliteration,
        translation: verse.translation,
        purpose: verse.purposes || [],
        recitationUrl: verse.recitationUrl,
        order: verse.orderIndex,
      }));

      logger.info('Ruqya verses retrieved', {
        sessionType,
        purpose,
        count: ruqyaVersesDTO.length,
      });

      return ruqyaVersesDTO;
    } catch (error) {
      logger.error('Error getting Ruqya verses', {
        sessionType,
        purpose,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get Ruqya verses', 500);
    }
  }

  async getRuqyaDuas(
    sessionType: string,
    purpose: string
  ): Promise<RuqyaDuaDTO[]> {
    try {
      const duas = await prisma.ruqyaDua.findMany({
        where: {
          sessionTypes: { has: sessionType },
          purposes: { has: purpose },
          isActive: true,
        },
        orderBy: { orderIndex: 'asc' },
      });

      const ruqyaDuasDTO: RuqyaDuaDTO[] = duas.map((dua) => ({
        id: dua.id,
        title: dua.title,
        arabic: dua.arabicText,
        transliteration: dua.transliteration,
        translation: dua.translation,
        purpose: dua.purposes || [],
        source: dua.source,
        recitationUrl: dua.recitationUrl,
        order: dua.orderIndex,
      }));

      logger.info('Ruqya duas retrieved', {
        sessionType,
        purpose,
        count: ruqyaDuasDTO.length,
      });

      return ruqyaDuasDTO;
    } catch (error) {
      logger.error('Error getting Ruqya duas', {
        sessionType,
        purpose,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get Ruqya duas', 500);
    }
  }

  async updateSessionProgress(
    sessionId: string,
    progress: number,
    currentVerse?: string // Assuming currentVerseId might be more appropriate if it's a relation
  ): Promise<void> {
    try {
      const updateData: any = {
        progress,
        lastUpdated: new Date(), // Assuming Prisma model has lastUpdated
      };

      if (currentVerse) { // If storing current verse text or ID
        updateData.currentVerse = currentVerse; // Ensure field exists in Prisma model
      }

      await prisma.ruqyaSession.update({
        where: { id: sessionId },
        data: updateData,
      });

      logger.info('Ruqya session progress updated', {
        sessionId,
        progress,
        currentVerse,
      });
    } catch (error) {
      logger.error('Error updating session progress', {
        sessionId,
        progress,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to update session progress', 500);
    }
  }

  async completeRuqyaSession(
    sessionId: string,
    userId: string,
    feedback?: string,
    effectiveness?: number
  ): Promise<void> {
    try {
      await prisma.ruqyaSession.update({
        where: { id: sessionId, userId: userId }, // Ensure user owns the session
        data: {
          status: 'completed',
          endTime: new Date(),
          feedback,
          effectiveness,
          progress: 100,
        },
      });

      await this.logRuqyaActivity(userId, 'session_completed', {
        sessionId,
        effectiveness,
        feedback: feedback ? 'provided' : 'none',
      });

      logger.info('Ruqya session completed', {
        sessionId,
        userId,
        effectiveness,
      });
    } catch (error) {
      logger.error('Error completing Ruqya session', {
        sessionId,
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to complete Ruqya session', 500);
    }
  }

  async getRuqyaPrograms(
    userId: string, // May not be needed if programs are generic, or for filtering user-specific programs
    type?: string
  ): Promise<RuqyaProgramDTO[]> {
    try {
      const whereClause: any = { isActive: true };
      if (type) {
        whereClause.type = type;
      }

      const programs = await prisma.ruqyaProgram.findMany({
        where: whereClause,
        include: {
          sessions: true, // Assuming a relation 'sessions' to RuqyaProgramSession or RuqyaSession
        },
        orderBy: { createdAt: 'desc' }, // Assuming createdAt field
      });

      const ruqyaProgramsDTO: RuqyaProgramDTO[] = programs.map((program) => ({
        id: program.id,
        name: program.name,
        description: program.description,
        type: program.type as RuqyaProgramDTO['type'],
        duration: program.estimatedDuration || 0, // Assuming estimatedDuration field
        sessions: (program.sessions?.map(s => ({ /* map to RuqyaSessionDTO */ })) || []) as RuqyaSessionDTO[], // Requires mapping
        targetAilments: program.targetAilments || [],
        difficulty: program.difficulty as RuqyaProgramDTO['difficulty'],
      }));

      logger.info('Ruqya programs retrieved', {
        userId,
        type,
        count: ruqyaProgramsDTO.length,
      });

      return ruqyaProgramsDTO;
    } catch (error) {
      logger.error('Error getting Ruqya programs', {
        userId,
        type,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get Ruqya programs', 500);
    }
  }

  async getRuqyaPractitioners(
    location?: string,
    specialization?: string
  ): Promise<RuqyaPractitionerDTO[]> {
    try {
      const whereClause: any = {
        isVerified: true,
        isActive: true,
      };
      if (location) {
        whereClause.OR = [{ location: location }, { location: 'online' }];
      }
      if (specialization) {
        whereClause.specializations = { has: specialization }; // Assuming specializations is an array
      }

      const practitioners = await prisma.ruqyaPractitioner.findMany({
        where: whereClause,
        orderBy: { rating: 'desc' }, // Prisma uses 'desc' for descending
      });

      const ruqyaPractitionersDTO: RuqyaPractitionerDTO[] = practitioners.map(
        (practitioner) => ({
          id: practitioner.id,
          name: practitioner.name,
          qualifications: practitioner.qualifications || [],
          specializations: practitioner.specializations || [],
          location: practitioner.location,
          contactInfo: practitioner.contactInfo,
          isVerified: practitioner.isVerified,
          rating: practitioner.rating,
          availability: practitioner.availability,
        })
      );

      logger.info('Ruqya practitioners retrieved', {
        location,
        specialization,
        count: ruqyaPractitionersDTO.length,
      });

      return ruqyaPractitionersDTO;
    } catch (error) {
      logger.error('Error getting Ruqya practitioners', {
        location,
        specialization,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get Ruqya practitioners', 500);
    }
  }

  async getRuqyaHistory(
    userId: string,
    limit: number = 20
  ): Promise<RuqyaSessionDTO[]> {
    try {
      const sessions = await prisma.ruqyaSession.findMany({
        where: { userId },
        orderBy: { startTime: 'desc' },
        take: limit,
        // Include verses and duas if they are relations and not just JSON data
        // include: { verses: true, duas: true }
      });

      const ruqyaHistoryDTO: RuqyaSessionDTO[] = sessions.map((session) => ({ // Removed PrismaRuqyaSession type annotation
        id: session.id,
        userId: session.userId,
        sessionType: session.sessionType as RuqyaSessionDTO['sessionType'],
        purpose: session.purpose,
        duration: session.duration || 0,
        startTime: session.startTime,
        endTime: session.endTime || undefined,
        status: session.status as RuqyaSessionDTO['status'],
        // If versesData and duasData are JSON fields in Prisma:
        verses: (session.versesData as unknown as RuqyaVerseDTO[]) || [],
        duas: (session.duasData as unknown as RuqyaDuaDTO[]) || [],
        progress: session.progress || 0,
        programId: session.programId || undefined,
      }));

      logger.info('Ruqya history retrieved', {
        userId,
        count: ruqyaHistoryDTO.length,
      });

      return ruqyaHistoryDTO;
    } catch (error) {
      logger.error('Error getting Ruqya history', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get Ruqya history', 500);
    }
  }

  async getDailyProtectionVerses(): Promise<RuqyaVerseDTO[]> {
    try {
      const verses = await prisma.ruqyaVerse.findMany({
        where: {
          isDailyProtection: true, // Assuming boolean field name
          isActive: true,
        },
        orderBy: { orderIndex: 'asc' },
      });

      const protectionVersesDTO: RuqyaVerseDTO[] = verses.map((verse) => ({
        id: verse.id,
        surah: verse.surah,
        ayahNumber: verse.ayahNumber,
        arabic: verse.arabicText,
        transliteration: verse.transliteration,
        translation: verse.translation,
        purpose: verse.purposes || [],
        recitationUrl: verse.recitationUrl,
        order: verse.orderIndex,
      }));

      logger.info('Daily protection verses retrieved', {
        count: protectionVersesDTO.length,
      });

      return protectionVersesDTO;
    } catch (error) {
      logger.error('Error getting daily protection verses', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw new AppError('Failed to get daily protection verses', 500);
    }
  }

  private async logRuqyaActivity(
    userId: string,
    activityType: string,
    metadata: any // Prisma expects JsonObject type for JSON fields
  ): Promise<void> {
    await prisma.ruqyaActivity.create({ // Assuming RuqyaActivity model
      data: {
        userId,
        activityType,
        metadata: metadata as any, // TODO: Use correct Prisma JsonValue type
        // createdAt is handled by Prisma
      },
    });
  }
}

export const ruqyaService = new RuqyaService();
