import { prisma, Profile as PrismaProfile, Prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

// Define a type for what the client might send to update a profile
// This should be a subset of PrismaProfile and exclude fields like id, createdAt, updatedAt
export type UpdateProfileDto = Omit<Partial<PrismaProfile>, 'id' | 'createdAt' | 'updatedAt' | 'email'>;


export class ProfileService {
  constructor() {
    logger.info('ProfileService initialized');
  }

  /**
   * Get user profile by ID.
   * @param userId - The ID of the user.
   */
  async getProfileById(userId: string): Promise<PrismaProfile | null> {
    logger.info(`Fetching profile for user ID: ${userId}`);
    try {
      const profile = await prisma.profile.findUnique({
        where: { id: userId },
      });
      if (!profile) {
        // Depending on requirements, could throw AppError or return null
        logger.warn(`Profile not found for user ID: ${userId}`);
        return null;
      }
      return profile;
    } catch (error: any) {
      logger.error(`Error fetching profile for user ID ${userId}: ${error.message}`, { stack: error.stack });
      throw new AppError('Failed to retrieve profile.', 500);
    }
  }

  /**
   * Get user profile by Email.
   * @param email - The email of the user.
   */
  async getProfileByEmail(email: string): Promise<PrismaProfile | null> {
    logger.info(`Fetching profile for email: ${email}`);
    try {
      const profile = await prisma.profile.findUnique({
        where: { email: email },
      });
      if (!profile) {
        logger.warn(`Profile not found for email: ${email}`);
        return null;
      }
      return profile;
    } catch (error: any) {
      logger.error(`Error fetching profile for email ${email}: ${error.message}`, { stack: error.stack });
      throw new AppError('Failed to retrieve profile by email.', 500);
    }
  }

  /**
   * Create or update a user profile.
   * If profile exists, it's updated. Otherwise, it's created.
   * Email is used for lookup if ID doesn't match, but ID is primary for upsert.
   * @param userId - The ID of the user.
   * @param email - The email of the user.
   * @param data - Data for creating/updating the profile.
   */
  async upsertProfile(userId: string, email: string, data: UpdateProfileDto): Promise<PrismaProfile> {
    logger.info(`Upserting profile for user ID: ${userId}, email: ${email}`);
    try {
      const profileData: Prisma.ProfileUpsertArgs['create'] = {
        id: userId,
        email: email,
        ...data, // Spread other DTO fields
        // Ensure mandatory fields not in DTO have defaults or are handled
        awarenessLevel: data.awarenessLevel || 'symptom_aware', // Example default
      };

      const profileUpdateData: Prisma.ProfileUpsertArgs['update'] = {
        ...data, // Spread DTO fields
        // email: email, // Typically email is not updated this way or only if verified
      };

      const profile = await prisma.profile.upsert({
        where: { id: userId },
        create: profileData,
        update: profileUpdateData,
      });
      return profile;
    } catch (error: any) {
      logger.error(`Error upserting profile for user ID ${userId}: ${error.message}`, { stack: error.stack });
      // Consider specific error codes for unique constraint violations (e.g., email already exists for another user)
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') { // Unique constraint failed
          throw new AppError(`Profile creation/update failed: A field (e.g., email) is already in use. ${error.meta?.target}`, 409);
        }
      }
      throw new AppError('Failed to create or update profile.', 500);
    }
  }

   /**
   * Partially updates a user profile.
   * Only fields provided in data will be updated.
   * @param userId - The ID of the user whose profile is to be updated.
   * @param data - An object containing the fields to update.
   */
  async updateProfile(userId: string, data: UpdateProfileDto): Promise<PrismaProfile> {
    logger.info(`Updating profile for user ID: ${userId}`);
    try {
      const profile = await prisma.profile.update({
        where: { id: userId },
        data,
      });
      return profile;
    } catch (error: any) {
      logger.error(`Error updating profile for user ID ${userId}: ${error.message}`, { stack: error.stack });
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // P2025: Record to update not found
        if (error.code === 'P2025') {
          throw new AppError('Profile not found for update.', 404);
        }
        // P2002: Unique constraint failed (e.g. if trying to update email to one that already exists)
        if (error.code === 'P2002') {
           throw new AppError(`Profile update failed: A field (e.g., email if updatable) is already in use. ${error.meta?.target}`, 409);
        }
      }
      throw new AppError('Failed to update profile.', 500);
    }
  }

  // Potentially add methods for:
  // - Deleting a profile (handle with care, GDPR implications)
  // - Fetching profiles with specific criteria (e.g., for admin dashboards)
}

export const profileService = new ProfileService();
