/**
 * Database Configuration
 * Prisma client configuration and database utilities
 */

import { PrismaClient, Prisma } from '@prisma/client';

// Create Prisma client
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    // 👇 This disables prepared statements (required for pgbouncer pooled connection)
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    // __internal removed for compatibility
  });

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Database utility functions
export class DatabaseService {
  private client: PrismaClient;

  constructor() {
    this.client = prisma;
  }

  /**
   * Get Prisma client instance
   */
  getClient(): PrismaClient {
    return this.client;
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.client.assessmentSession.findFirst();
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  /**
   * Execute raw SQL query (use with caution)
   */
  async executeQuery(query: string, params?: any[]): Promise<any> {
    try {
      // Use $executeRawUnsafe for dynamic SQL
      const result = await this.client.$executeRawUnsafe(query, ...(params || []));

      return result;
    } catch (error) {
      console.error('Query execution failed:', error);
      throw error;
    }
  }

  /**
   * Get table row count
   */
  async getTableCount(tableName: string): Promise<number> {
    try {
      const count = await this.client[tableName].count();

      return count;
    } catch (error) {
      console.error(`Failed to get count for table ${tableName}:`, error);
      return 0;
    }
  }

  /**
   * Check if table exists
   */
  async tableExists(tableName: string): Promise<boolean> {
    try {
      const result = await this.client
        .$queryRaw`SELECT to_regclass('public.${tableName}')`;
      const rows = result as any[];
      return rows.length > 0 && rows[0] !== null;
    } catch (error) {
      console.error(`Failed to check if table ${tableName} exists:`, error);
      return false;
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    connected: boolean;
    tablesExist: boolean;
    userCount: number;
    lastChecked: string;
  }> {
    const connected = await this.testConnection();
    const tablesExist = await this.tableExists('user_profiles');
    const userCount = connected ? await this.getTableCount('user_profiles') : 0;

    return {
      connected,
      tablesExist,
      userCount,
      lastChecked: new Date().toISOString(),
    };
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();

// Export Prisma namespace for type definitions and utilities
export { Prisma };

// Export Prisma model types for use in services
export type AssessmentSession = Prisma.AssessmentSessionGetPayload<{}>;
export type SpiritualDiagnosis = Prisma.SpiritualDiagnosisGetPayload<{}>;
export type LayerAnalysis = Prisma.LayerAnalysisGetPayload<{}>;
export type Profile = Prisma.ProfileGetPayload<{}>;
export type AssessmentQuestion = Prisma.AssessmentQuestionGetPayload<{}>;
export type AssessmentSymptom = Prisma.AssessmentSymptomGetPayload<{}>;
export type DiagnosisFeedback = Prisma.DiagnosisFeedbackGetPayload<{}>;
