/**
 * Assessment Models for Feature 1: Understanding Your Inner Landscape
 * Comprehensive spiritual assessment and diagnosis system
 */

export interface SymptomCategory {
  id: string;
  name: string;
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  symptoms: string[];
  intensity: 'mild' | 'moderate' | 'severe';
  userReflection?: string;
}

export interface LayerAnalysis {
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  layerName: string;
  impactScore: number; // 0-100
  affectedSymptoms: string[];
  insights: string[];
  recommendations: string[];
  islamicContext: string;
  priority: 'primary' | 'secondary' | 'tertiary';
}

export interface SpiritualDiagnosis {
  id: string;
  userId: string;
  assessmentId: string;

  // Core Analysis
  primaryLayer: LayerAnalysis;
  secondaryLayers: LayerAnalysis[];
  overallSeverity: 'mild' | 'moderate' | 'severe' | 'critical';

  // Personalized Insights
  personalizedMessage: string;
  islamicInsights: string[];
  educationalContent: string;

  // Crisis Assessment
  crisisLevel: 'none' | 'low' | 'moderate' | 'high' | 'critical';
  crisisIndicators: string[];
  immediateActions: string[];

  // Recommendations
  nextSteps: string[];
  recommendedJourneyType: string;
  estimatedHealingDuration: number; // days

  // Metadata
  confidence: number; // 0-1
  generatedAt: Date;
  userFeedback?: {
    accuracy: number; // 1-5
    helpfulness: number; // 1-5
    comments?: string;
  };
}

export interface AssessmentSession {
  id: string;
  userId: string;
  userProfile: any; // From Feature 0

  // Session Data
  startedAt: Date;
  completedAt?: Date;
  currentStep: string;
  totalSteps: number;

  // Assessment Responses
  physicalExperiences: SymptomCategory;
  emotionalExperiences: SymptomCategory;
  mentalExperiences: SymptomCategory;
  spiritualExperiences: SymptomCategory;

  // Reflections
  reflections: {
    overwhelmingEmotions?: string;
    mindOccupation?: string;
    lastConnectionWithAllah?: string;
    additionalThoughts?: string;
  };

  // Progress Tracking
  timeSpentPerStep: Record<string, number>; // seconds
  totalTimeSpent: number;

  // Results
  diagnosis?: SpiritualDiagnosis;

  // Metadata
  deviceInfo?: any;
  abandonedAt?: Date;
  abandonmentReason?: string;
  responses?: any; // Added to store dynamic responses from steps
}

export interface AssessmentQuestion {
  id: string;
  category: 'physical' | 'emotional' | 'mental' | 'spiritual';
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';

  // Question Content
  title: string;
  description?: string;
  questionType: string; // Added missing field
  symptoms: AssessmentSymptom[];

  // Reflection Prompts
  reflectionPrompt?: string;
  reflectionRequired: boolean;

  // Adaptive Logic
  showConditions?: {
    userType?: string[];
    previousResponses?: Record<string, any>;
    minSymptomCount?: number;
  };

  // UI Configuration
  allowMultipleSelection: boolean;
  intensityScale: boolean;
  customInputAllowed: boolean;
}

export interface AssessmentSymptom {
  id: string;
  text: string;
  description?: string;
  severity: 'mild' | 'moderate' | 'severe';
  islamicContext?: string;

  // Clinical Mapping
  clinicalTerms?: string[];
  dsmMapping?: string[];

  // Ruqya Mapping
  spiritualAilmentIndicators?: string[];
  ruqyaRelevance?: 'none' | 'low' | 'moderate' | 'high';

  // Layer Impact
  primaryLayer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  secondaryLayers?: ('jism' | 'nafs' | 'aql' | 'qalb' | 'ruh')[];
}

export interface PersonalizedWelcome {
  userId: string;
  userType: string;
  userName?: string; // Added to include user's name in welcome

  // Welcome Content
  greeting: string;
  introduction: string;
  explanation: string;
  motivation: string;

  // Action Options
  primaryAction: {
    id: string;
    text: string;
    description?: string;
  };
  secondaryActions: {
    id: string;
    text: string;
    description?: string;
  }[];

  // Educational Content
  layerEducation?: {
    showUpfront: boolean;
    content: string;
    interactive: boolean;
  };
}

export interface DiagnosisDelivery {
  userId: string;
  userType: string;
  deliveryStyle:
    | 'gentle'
    | 'clinical'
    | 'advanced'
    | 'traditional'
    | 'compassionate';
  generatedAt: Date;

  // Core diagnosis data from AI, augmented with DB ID
  diagnosis: any; // This will be the AISpiritualLandscapeResponse structure from AI, plus 'id' field for the diagnosis DB record

  // Derived and formatted content for direct display
  layerIntroduction?: string;
  educationalContent?: string;
  islamicInsights?: string;
  nextStepsGuidance?: string;
  journeyRecommendation?: string;

  // Summaries
  primaryLayerAnalysisSummary?: string;
  secondaryLayersSummary?: string;

  additionalResources?: string[];
}

// Utility Types
export interface AssessmentProgress {
  currentStep: number;
  totalSteps: number;
  percentage: number;
  estimatedTimeRemaining: number; // minutes
  canSkip: boolean;
  canGoBack: boolean;
}

export interface AssessmentValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  requiredFields: string[];
  completionPercentage: number;
}

// Constants
export const SOUL_LAYERS = {
  jism: {
    name: 'Jism',
    englishName: 'Physical Body',
    description: 'Your body is an amanah (trust) from Allah',
    color: '#8B4513',
    icon: '🤲',
  },
  nafs: {
    name: 'Nafs',
    englishName: 'Ego/Lower Self',
    description: 'The part that needs purification and discipline',
    color: '#DC143C',
    icon: '😤',
  },
  aql: {
    name: 'Aql',
    englishName: 'Rational Mind',
    description: 'The mind that finds peace through dhikr and knowledge',
    color: '#4169E1',
    icon: '🧠',
  },
  qalb: {
    name: 'Qalb',
    englishName: 'Spiritual Heart',
    description: 'The center of your relationship with Allah',
    color: '#228B22',
    icon: '💖',
  },
  ruh: {
    name: 'Ruh',
    englishName: 'Soul',
    description: 'The soul that yearns for its Creator and eternal home',
    color: '#9370DB',
    icon: '✨',
  },
} as const;

export const ASSESSMENT_STEPS = [
  'welcome',
  'layer_education', // Optional based on user
  'physical_experiences',
  'emotional_experiences',
  'mental_experiences',
  'spiritual_experiences',
  'reflections',
] as const;

export const CRISIS_THRESHOLDS = {
  none: 0,
  low: 25,
  moderate: 50,
  high: 75,
  critical: 90,
} as const;

// Helper Functions
export const calculateLayerImpact = (
  symptoms: string[],
  intensity: string
): number => {
  const baseScore = symptoms.length * 10;
  const intensityMultiplier = {
    mild: 1,
    moderate: 1.5,
    severe: 2,
  };

  return Math.min(100, baseScore * intensityMultiplier[intensity]);
};

export const determineOverallSeverity = (
  layerAnalyses: LayerAnalysis[]
): 'mild' | 'moderate' | 'severe' | 'critical' => {
  const maxImpact = Math.max(...layerAnalyses.map((l) => l.impactScore));

  if (maxImpact >= 80) return 'critical';
  if (maxImpact >= 60) return 'severe';
  if (maxImpact >= 40) return 'moderate';
  return 'mild';
};

export const createEmptyAssessmentSession = (
  userId: string,
  userProfile: any
): Partial<AssessmentSession> => ({
  userId,
  userProfile,
  startedAt: new Date(),
  currentStep: 'welcome',
  totalSteps: ASSESSMENT_STEPS.length,
  physicalExperiences: {
    id: 'physical',
    name: 'Physical Experiences',
    layer: 'jism',
    symptoms: [],
    intensity: 'mild',
  },
  emotionalExperiences: {
    id: 'emotional',
    name: 'Emotional Experiences',
    layer: 'nafs',
    symptoms: [],
    intensity: 'mild',
  },
  mentalExperiences: {
    id: 'mental',
    name: 'Mental Experiences',
    layer: 'aql',
    symptoms: [],
    intensity: 'mild',
  },
  spiritualExperiences: {
    id: 'spiritual',
    name: 'Spiritual Experiences',
    layer: 'qalb',
    symptoms: [],
    intensity: 'mild',
  },
  reflections: {},
  timeSpentPerStep: {},
  totalTimeSpent: 0,
});
