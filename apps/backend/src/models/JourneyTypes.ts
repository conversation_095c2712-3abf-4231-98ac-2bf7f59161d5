// Placeholder types for @qalb-healing/shared-types

export type JourneyType = 'tranquil_mind' | 'heart_purification' | 'ego_purification' | 'spiritual_optimization' | 'crisis_recovery' | 'maintenance_program';
export type JourneyStatus = 'created' | 'active' | 'paused' | 'completed' | 'abandoned';

export interface JourneyConfig {
  duration: number;
  dailyTimeCommitment: number;
  primaryLayer: string;
  secondaryLayers: string[];
  ruqyaIntegrationLevel: string;
  communityIntegration: boolean;
  professionalContext?: string;
  culturalAdaptations?: string[];
  crisisSupport: boolean;
}

export interface DailyPractice {
  id: string;
  title: string;
  description: string;
  type: 'meditation' | 'dhikr' | 'quran' | 'dua' | 'exercise' | 'reflection';
  estimatedDuration: number; // minutes
  audioUrl?: string;
  videoUrl?: string;
  instructions?: string;
}

export interface JourneyDay {
  dayNumber: number;
  theme: string;
  focus: string;
  practices: DailyPractice[];
  reflectionPrompt?: string;
  educationalContent?: string; // Link or ID
}

export interface Journey {
  id: string;
  userId: string;
  assessmentId?: string;
  type: JourneyType;
  status: JourneyStatus;
  configuration: JourneyConfig;
  title: string;
  description: string;
  personalizedWelcome?: string;
  days: JourneyDay[];
  currentDay: number;
  completedDays: number[];
  totalProgress: number; // Percentage
  userProfile?: any; // Consider defining a UserProfile type if shared
  aiRecommendations?: any[];
  adaptiveAdjustments?: any[];
  crisisFlags?: any[];
  startedAt?: Date;
  completedAt?: Date;
  lastActiveAt?: Date;
  createdAt: Date;
  updatedAt?: Date;
  communityGroupId?: string;
  mentorId?: string;
  peerConnections?: string[];
  duration?: number; // Added to satisfy usage in journey.service.ts
}

export interface JourneyProgress {
  id: string;
  journeyId: string;
  userId: string;
  dayNumber: number;
  date: string; // ISO Date string
  practicesCompleted: Array<{ practiceId: string; status: 'completed' | 'skipped' | 'partially_completed'; timeSpent?: number; rating?: number; notes?: string }>;
  mood?: string;
  reflections?: string;
  challenges?: string;
  timeSpentOnDay?: number; // total time in minutes
  createdAt: string; // ISO Date string
  updatedAt: string; // ISO Date string
}

export interface JourneyAnalytics {
  journeyId: string;
  userId: string;
  completionRate: number; // Percentage
  averageSessionTime: number; // minutes
  streakDays: number;
  layerProgress: Record<string, number>; // Layer name to percentage
  mostEngagedPractices: string[];
  leastEngagedPractices: string[];
  moodTrends: Record<string, number>; // Date to mood score
  crisisEvents: number;
}
