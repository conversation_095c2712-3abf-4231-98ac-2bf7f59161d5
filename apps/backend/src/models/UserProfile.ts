/**
 * User Profile Model for Feature 0: Adaptive Onboarding
 * Comprehensive user profiling system for personalized Islamic wellness
 */

export interface MentalHealthAwareness {
  level: 'clinically_aware' | 'symptom_aware';
  conditions?: string[];
  previousTherapy?: boolean;
  comfortWithTerminology?: 'very' | 'somewhat' | 'limited' | 'prefer_not';
}

export interface RuqyaKnowledge {
  level: 'expert' | 'practitioner' | 'aware' | 'skeptical' | 'unaware';
  experience?: string[];
  practiceYears?: number;
  helpingOthers?: boolean;
  openToLearning?: 'very' | 'gradually' | 'maybe' | 'prefer_general';
}

export interface SpiritualOptimizer {
  type?: 'clinical_integration' | 'traditional_bridge';
  goals?: string[];
  islamicPsychologyLevel?: 'extensive' | 'moderate' | 'basic' | 'minimal';
  clinicalComfort?: 'very' | 'somewhat' | 'limited' | 'uncomfortable';
}

export interface HealthcareProfessionalContext {
  role: 'physician' | 'nurse' | 'mental_health_professional' | 'allied_health' | 'student_resident' | 'other';
  specialization?: string;
  isStudent?: boolean;
}

export interface EducationProfessionalContext {
  role: 'teacher' | 'higher_education' | 'support_staff' | 'islamic_education' | 'other';
  level?: 'elementary' | 'middle_school' | 'high_school' | 'university' | 'other';
  subject?: string;
}

export interface TechnologyProfessionalContext {
  role: 'engineering' | 'design_product' | 'data_analytics' | 'entrepreneurship' | 'other';
  specialization?: string;
  isFounder?: boolean;
}

export interface BusinessProfessionalContext {
    role: 'management' | 'entrepreneur' | 'finance' | 'consulting' | 'sales' | 'other';
    industry?: string;
}

export interface CreativeProfessionalContext {
    role: 'artist' | 'designer' | 'writer' | 'musician' | 'photographer' | 'other';
    medium?: string;
}

export interface ServiceProfessionalContext {
    role: 'social_work' | 'counseling' | 'non_profit' | 'volunteer' | 'other';
    focusArea?: string;
}

export interface ScienceProfessionalContext {
    role: 'scientist' | 'researcher' | 'academic' | 'other';
    fieldOfStudy?: string;
}

export interface ProfessionalContext {
  field: 'healthcare' | 'education' | 'technology' | 'business' | 'creative' | 'service' | 'science' | 'religious' | 'homemaker' | 'student' | 'retired' | 'other';
  specificRole?: string;
  experienceYears?: number;
  workStressors?: string[];
  faithIntegrationGoals?: string[];
  healthcare?: HealthcareProfessionalContext;
  education?: EducationProfessionalContext;
  technology?: TechnologyProfessionalContext;
  business?: BusinessProfessionalContext;
  creative?: CreativeProfessionalContext;
  service?: ServiceProfessionalContext;
  science?: ScienceProfessionalContext;
}

export interface Demographics {
  ageRange: '13-18' | '19-25' | '26-35' | '36-50' | '51-65' | '65+';
  gender: 'brother' | 'sister' | 'prefer_not_to_specify';
  familyStatus: 'single' | 'married' | 'married_with_children' | 'single_parent' | 'caregiver' | 'divorced' | 'widowed' | 'other';
  location?: {
    country: string;
    city?: string;
    timezone?: string;
  };
}

export interface LifeCircumstances {
  situations: string[];
  islamicJourneyStage: 'new_muslim' | 'practicing' | 'seeking' | 'advanced';
  conversionDate?: Date;
  culturalBackground?: string;
  languagePreferences?: string[];
}

export interface CrisisIndicators {
  level: 'none' | 'low' | 'moderate' | 'high' | 'critical';
  indicators: string[];
  immediateHelpRequested: boolean;
  previousCrises?: boolean;
  supportSystem?: 'strong' | 'moderate' | 'limited' | 'none';
}

export interface PersonalizationPreferences {
  contentStyle: 'simple' | 'moderate' | 'detailed';
  islamicTerminology: 'extensive' | 'moderate' | 'basic' | 'minimal';
  learningPace: 'fast' | 'moderate' | 'slow' | 'self_paced';
  communityEngagement: 'high' | 'moderate' | 'low' | 'private';
  timeAvailability: '5-10' | '10-20' | '20-30' | '30+'; // minutes per day
}

export interface FeatureAccessibility {
  feature1Level: 'basic' | 'standard' | 'advanced';
  feature2Complexity: 'simple' | 'moderate' | 'comprehensive';
  ruqyaIntegration: 'none' | 'optional' | 'integrated' | 'advanced';
  communityAccess: 'observer' | 'participant' | 'contributor' | 'leader';
  crisisSupport: 'standard' | 'enhanced' | 'intensive';
}

export interface UserProfile {
  // Core Identity
  id: string;
  userId: string;
  
  // Assessment Results
  mentalHealthAwareness: MentalHealthAwareness;
  ruqyaKnowledge: RuqyaKnowledge;
  spiritualOptimizer?: SpiritualOptimizer;
  professionalContext: ProfessionalContext;
  demographics: Demographics;
  lifeCircumstances: LifeCircumstances;
  
  // Risk Assessment
  crisisIndicators: CrisisIndicators;
  
  // Personalization
  preferences: PersonalizationPreferences;
  featureAccessibility: FeatureAccessibility;
  
  // Adaptive Learning
  learningHistory: {
    completedAssessments: string[];
    behaviorPatterns: Record<string, any>;
    progressMetrics: Record<string, number>;
    lastUpdated: Date;
  };
  
  // Privacy & Consent
  privacySettings: {
    dataSharing: boolean;
    communityVisibility: 'public' | 'limited' | 'private';
    analyticsConsent: boolean;
    crisisInterventionConsent: boolean;
  };
  
  // Metadata
  profileVersion: string;
  createdAt: Date;
  updatedAt: Date;
  completionStatus: 'incomplete' | 'complete' | 'needs_update';
}

export interface OnboardingStep {
  stepId: string;
  stepName: string;
  isCompleted: boolean;
  responses: Record<string, any>;
  completedAt?: Date;
  timeSpent?: number; // seconds
}

export interface OnboardingSession {
  sessionId: string;
  userId: string;
  startedAt: Date;
  completedAt?: Date;
  currentStep: string;
  steps: OnboardingStep[];
  totalTimeSpent: number;
  abandonedAt?: Date;
  abandonmentReason?: string;
  deviceInfo?: {
    platform: string;
    browser?: string;
    screenSize?: string;
  };
}

// Utility types for API responses
export interface ProfileGenerationResult {
  profile: UserProfile;
  recommendedPathway: string;
  featureConfiguration: FeatureAccessibility;
  nextSteps: string[];
  warnings?: string[];
}

export interface PersonalizationUpdate {
  profileId: string;
  updates: Partial<UserProfile>;
  reason: string;
  confidence: number;
  appliedAt: Date;
}

// Validation schemas
export const VALID_MENTAL_HEALTH_LEVELS = ['clinically_aware', 'symptom_aware'] as const;
export const VALID_RUQYA_LEVELS = ['expert', 'practitioner', 'aware', 'skeptical', 'unaware'] as const;
export const VALID_PROFESSIONAL_FIELDS = [
  'healthcare', 'education', 'technology', 'business', 'creative', 
  'service', 'science', 'religious', 'homemaker', 'student', 'retired', 'other'
] as const;
export const VALID_AGE_RANGES = ['13-18', '19-25', '26-35', '36-50', '51-65', '65+'] as const;
export const VALID_CRISIS_LEVELS = ['none', 'low', 'moderate', 'high', 'critical'] as const;

// Helper functions
export const createEmptyProfile = (userId: string): Partial<UserProfile> => ({
  userId,
  profileVersion: '1.0.0',
  createdAt: new Date(),
  updatedAt: new Date(),
  completionStatus: 'incomplete',
  privacySettings: {
    dataSharing: false,
    communityVisibility: 'private',
    analyticsConsent: false,
    crisisInterventionConsent: true
  },
  learningHistory: {
    completedAssessments: [],
    behaviorPatterns: {},
    progressMetrics: {},
    lastUpdated: new Date()
  }
});

export const validateProfileCompleteness = (profile: Partial<UserProfile>): string[] => {
  const missing: string[] = [];
  
  if (!profile.mentalHealthAwareness) missing.push('Mental Health Awareness');
  if (!profile.ruqyaKnowledge) missing.push('Ruqya Knowledge');
  if (!profile.professionalContext) missing.push('Professional Context');
  if (!profile.demographics) missing.push('Demographics');
  if (!profile.lifeCircumstances) missing.push('Life Circumstances');
  if (!profile.preferences) missing.push('Personalization Preferences');
  
  return missing;
};

export const calculateProfileCompleteness = (profile: Partial<UserProfile>): number => {
  const requiredFields = 6; // Core sections required
  const completedFields = 6 - validateProfileCompleteness(profile).length;
  return Math.round((completedFields / requiredFields) * 100);
};
