import { Prisma } from '../config/database';
import { AISpiritualLandscapeResponse, AILayerAnalysisOutput } from '../services/ai.service';

// Helper function to determine overall severity, can be co-located or imported if used elsewhere
function determineOverallSeverityFromAIScore(
  layerInsights: Record<string, AILayerAnalysisOutput>,
  primaryLayerKey: string | null | undefined
): string {
  if (!primaryLayerKey || primaryLayerKey === 'none' || !layerInsights[primaryLayerKey]) {
    // Default to mild if no primary layer or insights for it.
    const scores = Object.values(layerInsights).map(l => l.severity_score);
    if (scores.length === 0) return 'mild'; // No layer insights at all
    const maxScore = Math.max(...scores);
    if (maxScore >= 75) return 'severe';
    if (maxScore >= 50) return 'moderate';
    return 'mild';
  }
  const score = layerInsights[primaryLayerKey].severity_score;
  if (score >= 75) return 'severe';
  if (score >= 50) return 'moderate';
  return 'mild';
}

export function mapAIDiagnosisToPrismaCreateInput(
  aiAnalysis: AISpiritualLandscapeResponse,
  userId: string,
  sessionId: string
): {
  diagnosisCreateData: Prisma.SpiritualDiagnosisCreateInput;
  layerAnalysesCreateInput: Prisma.LayerAnalysisCreateWithoutSpiritualDiagnosisInput[];
} {
  const diagnosisDataForPrisma: Prisma.JsonObject = {
    ai_response: aiAnalysis as unknown as Prisma.InputJsonValue,
    primary_layer_name: aiAnalysis.primary_layer,
    // Add any other parts of aiAnalysis you want to store directly in diagnosisData
  };

  const layerAnalysesCreateInput: Prisma.LayerAnalysisCreateWithoutSpiritualDiagnosisInput[] =
    Object.entries(aiAnalysis.layer_insights).map(([layerKey, insight]) => ({
      layer: layerKey, // The key of the layer insight (e.g., 'qalb', 'nafs')
      layerName: insight.layer_name || layerKey, // Use specific layer_name from AI if available, else key
      impactScore: insight.severity_score,
      priority: insight.layer === aiAnalysis.primary_layer ? 'primary' :
                (insight.severity_score >= 50 ? 'secondary' : 'tertiary'), // Example priority logic
      affectedSymptoms: Array.isArray(insight.affected_symptoms) 
        ? insight.affected_symptoms.map(s => String(s))
        : [],
      insights: insight.insights || [], // Ensure it's an array
      recommendations: insight.recommendations || [], // Ensure it's an array
      islamicContext: Array.isArray(insight.islamic_context) 
        ? insight.islamic_context.join('; ')
        : (typeof insight.islamic_context === 'string' ? insight.islamic_context : null),
      // Add other fields if your Prisma model for LayerAnalysis has them
    }));

  const diagnosisCreateData: Prisma.SpiritualDiagnosisCreateInput = {
    user: { connect: { id: userId } },
    assessmentSession: { connect: { id: sessionId } },
    diagnosisData: diagnosisDataForPrisma,
    primaryLayer: aiAnalysis.primary_layer && aiAnalysis.primary_layer !== 'none' ? aiAnalysis.primary_layer : 'unknown',
    secondaryLayers: Object.keys(aiAnalysis.layer_insights).filter(
      (k) => k !== aiAnalysis.primary_layer && aiAnalysis.layer_insights[k].severity_score >= 40 // Example: only include if score is significant
    ),
    overallSeverity: determineOverallSeverityFromAIScore(
      aiAnalysis.layer_insights,
      aiAnalysis.primary_layer
    ),
    crisisLevel: aiAnalysis.crisis_level || 'low', // Default if not provided
    confidence: aiAnalysis.confidence || 0.0, // Default if not provided
    recommendedJourneyType: aiAnalysis.recommended_journey_type || 'general',
    estimatedHealingDuration: typeof aiAnalysis.estimated_healing_duration === 'number' 
      ? aiAnalysis.estimated_healing_duration 
      : null,
    nextSteps: aiAnalysis.next_steps || [],
    generatedAt: new Date(),
    // layerAnalyses will be connected via the nested create in the service
  };

  return { diagnosisCreateData, layerAnalysesCreateInput };
}
