import { Prisma } from '@prisma/client'; // For Prisma.InputJsonValue if needed
import { AISpiritualLandscapeResponse, AILayerAnalysisOutput } from '../../services/ai.service'; // Adjust path
import { mapAIDiagnosisToPrismaCreateInput } from '../aiDiagnosis.mapper'; // Adjust path

describe('aiDiagnosis.mapper', () => {
  const testUserId = 'user-test-123';
  const testSessionId = 'session-test-456';

  const minimalAIResponse: AISpiritualLandscapeResponse = {
    id: 'ai-resp-1',
    primary_layer: 'qalb',
    layer_insights: {
      qalb: {
        layer: 'qalb',
        layer_name: 'Qalb (Heart)',
        severity_score: 60,
        affected_symptoms: ['difficulty in khushu'],
        insights: ['The heart needs softening.'],
        recommendations: ['Increase remembrance.'],
        islamic_context: 'Quranic verse about hearts.',
      },
    },
    personalized_message: 'A message for you.',
    educational_content: 'Learn about the Qalb.',
    islamic_insights: ['Insight 1'],
    next_steps: ['Step 1'],
    recommended_journey_type: 'qalb_focus',
    estimated_healing_duration: 90, // 3 months in days
    confidence: 0.75,
    crisis_level: 'low',
    five_layers_explanation: 'Explanation of 5 layers.',
    additional_resources: [{ title: 'Resource 1', url: 'http://example.com/res1' }],
  };

  const comprehensiveAIResponse: AISpiritualLandscapeResponse = {
    id: 'ai-resp-2',
    primary_layer: 'nafs',
    layer_insights: {
      nafs: {
        layer: 'nafs',
        layer_name: 'Nafs (Ego)',
        severity_score: 80,
        affected_symptoms: ['anger', 'pride'],
        insights: ['Nafs requires discipline.'],
        recommendations: ['Fasting, self-reflection.'],
        islamic_context: 'Hadith about controlling ego.',
      },
      aql: {
        layer: 'aql',
        layer_name: 'Aql (Intellect)',
        severity_score: 70,
        affected_symptoms: ['overthinking', 'doubts'],
        insights: ['Aql needs clarity and knowledge.'],
        recommendations: ['Seek beneficial knowledge.'],
        islamic_context: 'Importance of knowledge in Islam.',
      },
      jism: {
        layer: 'jism',
        layer_name: 'Jism (Body)',
        severity_score: 30, // Tertiary
        affected_symptoms: ['lethargy'],
        insights: ['Body needs care.'],
        recommendations: ['Healthy diet, exercise.'],
        islamic_context: 'Body is an amanah.',
      }
    },
    personalized_message: 'Detailed message for comprehensive user.',
    educational_content: 'In-depth education module.',
    islamic_insights: ['Deep insight 1', 'Deep insight 2'],
    next_steps: ['Actionable step A', 'Actionable step B'],
    recommended_journey_type: 'nafs_purification_advanced',
    estimated_healing_duration: 240, // 8 months average in days
    confidence: 0.90,
    crisis_level: 'medium',
    five_layers_explanation: 'A very detailed explanation of the five layers.',
    additional_resources: [
        { title: 'Book on Nafs', url: 'http://example.com/nafs-book' },
        { title: 'Lecture on Aql', url: 'http://example.com/aql-lecture' }
    ],
  };

  describe('mapAIDiagnosisToPrismaCreateInput', () => {
    it('should map a minimal AI response correctly', () => {
      const { diagnosisCreateData, layerAnalysesCreateInput } = mapAIDiagnosisToPrismaCreateInput(
        minimalAIResponse,
        testUserId,
        testSessionId
      );

      // Diagnosis Create Data
      expect(diagnosisCreateData.user).toEqual({ connect: { id: testUserId } });
      expect(diagnosisCreateData.assessmentSession).toEqual({ connect: { id: testSessionId } });
      expect((diagnosisCreateData.diagnosisData as any).ai_response).toEqual(minimalAIResponse);
      expect((diagnosisCreateData.diagnosisData as any).primary_layer_name).toBe('qalb');
      expect(diagnosisCreateData.primaryLayer).toBe('qalb');
      expect(diagnosisCreateData.secondaryLayers).toEqual([]); // No other layers with score >=40
      expect(diagnosisCreateData.overallSeverity).toBe('moderate'); // Qalb score 60
      expect(diagnosisCreateData.crisisLevel).toBe('low');
      expect(diagnosisCreateData.confidence).toBe(0.75);
      expect(diagnosisCreateData.recommendedJourneyType).toBe('qalb_focus');
      expect(diagnosisCreateData.estimatedHealingDuration).toBe(90);
      expect(diagnosisCreateData.nextSteps).toEqual(['Step 1']);
      expect(diagnosisCreateData.generatedAt).toBeInstanceOf(Date);

      // Layer Analyses Create Input
      expect(layerAnalysesCreateInput).toHaveLength(1);
      const qalbLayer = layerAnalysesCreateInput[0];
      expect(qalbLayer.layer).toBe('qalb');
      expect(qalbLayer.layerName).toBe('Qalb (Heart)');
      expect(qalbLayer.impactScore).toBe(60);
      expect(qalbLayer.priority).toBe('primary');
      expect(qalbLayer.affectedSymptoms).toEqual(['difficulty in khushu']);
      expect(qalbLayer.insights).toEqual(['The heart needs softening.']);
      expect(qalbLayer.recommendations).toEqual(['Increase remembrance.']);
      expect(qalbLayer.islamicContext).toBe('Quranic verse about hearts.');
    });

    it('should map a comprehensive AI response correctly', () => {
      const { diagnosisCreateData, layerAnalysesCreateInput } = mapAIDiagnosisToPrismaCreateInput(
        comprehensiveAIResponse,
        testUserId,
        testSessionId
      );

      // Diagnosis Create Data
      expect(diagnosisCreateData.primaryLayer).toBe('nafs');
      expect(diagnosisCreateData.secondaryLayers).toEqual(['aql']); // Aql score 70 >= 40
      expect(diagnosisCreateData.overallSeverity).toBe('severe'); // Nafs score 80
      expect(diagnosisCreateData.crisisLevel).toBe('medium');
      expect(diagnosisCreateData.confidence).toBe(0.90);
      expect(diagnosisCreateData.recommendedJourneyType).toBe('nafs_purification_advanced');

      // Layer Analyses Create Input
      expect(layerAnalysesCreateInput).toHaveLength(3);

      const nafsLayer = layerAnalysesCreateInput.find(l => l.layer === 'nafs');
      expect(nafsLayer?.priority).toBe('primary');
      expect(nafsLayer?.impactScore).toBe(80);

      const aqlLayer = layerAnalysesCreateInput.find(l => l.layer === 'aql');
      expect(aqlLayer?.priority).toBe('secondary'); // Score 70 >= 50
      expect(aqlLayer?.impactScore).toBe(70);

      const jismLayer = layerAnalysesCreateInput.find(l => l.layer === 'jism');
      expect(jismLayer?.priority).toBe('tertiary'); // Score 30 < 50
      expect(jismLayer?.impactScore).toBe(30);
    });

    it('should handle null or undefined optional fields in AI response', () => {
      const partialResponse: AISpiritualLandscapeResponse = {
        id: 'ai-resp-3',
        primary_layer: 'qalb',
        layer_insights: {
          qalb: {
            layer: 'qalb',
            layer_name: 'Qalb',
            severity_score: 50,
            affected_symptoms: null, // Test null
            insights: undefined, // Test undefined
            recommendations: [], // Test empty array
            islamic_context: null,
          } as unknown as AILayerAnalysisOutput, // Cast to bypass strict null checks for test
        },
        // Most other fields are missing
        confidence: null,
        crisis_level: undefined,
      } as AISpiritualLandscapeResponse;

      const { diagnosisCreateData, layerAnalysesCreateInput } = mapAIDiagnosisToPrismaCreateInput(
        partialResponse,
        testUserId,
        testSessionId
      );

      expect(diagnosisCreateData.primaryLayer).toBe('qalb');
      expect(diagnosisCreateData.overallSeverity).toBe('moderate');
      expect(diagnosisCreateData.crisisLevel).toBe('low'); // Default
      expect(diagnosisCreateData.confidence).toBe(0.0); // Default
      expect(diagnosisCreateData.recommendedJourneyType).toBe('general'); // Default
      expect(diagnosisCreateData.estimatedHealingDuration).toBe(null); // Default
      expect(diagnosisCreateData.nextSteps).toEqual([]); // Default

      expect(layerAnalysesCreateInput[0].affectedSymptoms).toEqual([]);
      expect(layerAnalysesCreateInput[0].insights).toEqual([]);
      expect(layerAnalysesCreateInput[0].recommendations).toEqual([]);
      expect(layerAnalysesCreateInput[0].islamicContext).toBe(null);
    });

    it('should determine overallSeverity based on highest score if primary_layer is null or none', () => {
        const noPrimaryLayerResponse: AISpiritualLandscapeResponse = {
            ...minimalAIResponse,
            primary_layer: null, // or 'none'
            layer_insights: {
                aql: { ...minimalAIResponse.layer_insights.qalb, layer: 'aql', severity_score: 75 }, // severe
                nafs: { ...minimalAIResponse.layer_insights.qalb, layer: 'nafs', severity_score: 60 } // moderate
            }
        };
        const { diagnosisCreateData: data1 } = mapAIDiagnosisToPrismaCreateInput(noPrimaryLayerResponse, testUserId, testSessionId);
        expect(data1.overallSeverity).toBe('severe');

        const nonePrimaryLayerResponse: AISpiritualLandscapeResponse = {...noPrimaryLayerResponse, primary_layer: 'none'};
        const { diagnosisCreateData: data2 } = mapAIDiagnosisToPrismaCreateInput(nonePrimaryLayerResponse, testUserId, testSessionId);
        expect(data2.overallSeverity).toBe('severe');
    });

    it('should default overallSeverity to mild if no layer insights are present', () => {
        const noInsightsResponse: AISpiritualLandscapeResponse = {
            ...minimalAIResponse,
            primary_layer: 'qalb',
            layer_insights: {} // No insights
        };
        const { diagnosisCreateData } = mapAIDiagnosisToPrismaCreateInput(noInsightsResponse, testUserId, testSessionId);
        expect(diagnosisCreateData.overallSeverity).toBe('mild');
    });

    it('should default primaryLayer to "unknown" if AI provides null or "none"', () => {
        let response: AISpiritualLandscapeResponse = { ...minimalAIResponse, primary_layer: null };
        let { diagnosisCreateData } = mapAIDiagnosisToPrismaCreateInput(response, testUserId, testSessionId);
        expect(diagnosisCreateData.primaryLayer).toBe('unknown');

        response = { ...minimalAIResponse, primary_layer: 'none' };
        ({ diagnosisCreateData } = mapAIDiagnosisToPrismaCreateInput(response, testUserId, testSessionId));
        expect(diagnosisCreateData.primaryLayer).toBe('unknown');
    });
  });
});
