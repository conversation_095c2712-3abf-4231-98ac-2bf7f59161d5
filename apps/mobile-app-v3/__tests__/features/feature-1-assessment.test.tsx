/**
 * Feature 1: Understanding Your Inner Landscape Tests
 * Tests the 5-layer Islamic assessment and diagnosis system
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { jest } from '@jest/globals';
import '../setup';

// Import components to test
import AssessmentWelcome from '../../src/app/assessment/welcome';
import AssessmentFlow from '../../src/app/assessment/flow';
import { SymptomItem } from '../../src/components/SymptomItem';
import { SoulLayerBadge } from '../../src/components/SoulLayerBadge';

// Import services
import { assessmentService } from '../../src/services/assessment.service';

// Import test utilities
import {
  mockSymptoms,
  mockUserProfile,
  createMockNavigation,
  validateIslamicContent,
} from '../setup';

// Mock services
jest.mock('../../src/services/assessment.service');

const mockAssessmentService = assessmentService as jest.Mocked<typeof assessmentService>;

describe('Feature 1: Understanding Your Inner Landscape', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Assessment Welcome Screen', () => {
    it('should display personalized welcome based on user profile', () => {
      render(<AssessmentWelcome userProfile={mockUserProfile} />);
      
      expect(screen.getByText(new RegExp(mockUserProfile.name, 'i'))).toBeTruthy();
      expect(screen.getByText(/Assalamu Alaikum/i)).toBeTruthy();
      expect(screen.getByText(/inner landscape/i)).toBeTruthy();
    });

    it('should explain the 5-layer Islamic framework', () => {
      render(<AssessmentWelcome userProfile={mockUserProfile} />);
      
      const layers = ['Jism', 'Nafs', 'Aql', 'Qalb', 'Ruh'];
      layers.forEach(layer => {
        expect(screen.getByText(new RegExp(layer, 'i'))).toBeTruthy();
      });
    });

    it('should provide culturally appropriate introduction', () => {
      const traditionalUser = { ...mockUserProfile, islamicBackground: 'traditional' };
      render(<AssessmentWelcome userProfile={traditionalUser} />);
      
      expect(screen.getByText(/gentle approach/i)).toBeTruthy();
      expect(screen.getByText(/Islamic wisdom/i)).toBeTruthy();
    });

    it('should show professional context for healthcare workers', () => {
      const professionalUser = { ...mockUserProfile, profession: 'Healthcare Professional' };
      render(<AssessmentWelcome userProfile={professionalUser} />);
      
      expect(screen.getByText(/clinical integration/i)).toBeTruthy();
      expect(screen.getByText(/evidence-based/i)).toBeTruthy();
    });
  });

  describe('5-Layer Islamic Soul Framework', () => {
    const soulLayers = [
      { name: 'Jism', description: 'Physical body and health', color: '#E8F5E8' },
      { name: 'Nafs', description: 'Emotional self and desires', color: '#FFF3E0' },
      { name: 'Aql', description: 'Rational mind and thoughts', color: '#E3F2FD' },
      { name: 'Qalb', description: 'Spiritual heart and faith', color: '#F3E5F5' },
      { name: 'Ruh', description: 'Divine soul and connection', color: '#E8F5E8' },
    ];

    it('should render all 5 soul layers correctly', () => {
      soulLayers.forEach(layer => {
        render(<SoulLayerBadge layer={layer.name} />);
        expect(screen.getByText(layer.name)).toBeTruthy();
      });
    });

    it('should categorize symptoms by appropriate soul layer', async () => {
      const symptoms = [
        { name: 'Headaches', expectedLayer: 'Jism' },
        { name: 'Feeling anxious', expectedLayer: 'Nafs' },
        { name: 'Difficulty concentrating', expectedLayer: 'Aql' },
        { name: 'Feeling distant from Allah', expectedLayer: 'Qalb' },
        { name: 'Loss of spiritual purpose', expectedLayer: 'Ruh' },
      ];

      for (const symptom of symptoms) {
        mockAssessmentService.categorizeSymptom.mockResolvedValue(symptom.expectedLayer);
        
        const result = await assessmentService.categorizeSymptom(symptom.name);
        expect(result).toBe(symptom.expectedLayer);
      }
    });

    it('should provide layer-specific Islamic guidance', () => {
      const layerGuidance = {
        Jism: 'Take care of your body as an amanah (trust) from Allah',
        Nafs: 'Practice self-discipline and seek Allah\'s guidance',
        Aql: 'Use your intellect to remember and reflect upon Allah',
        Qalb: 'Purify your heart through dhikr and prayer',
        Ruh: 'Strengthen your connection with Allah through worship',
      };

      Object.entries(layerGuidance).forEach(([layer, guidance]) => {
        render(<SoulLayerBadge layer={layer} showGuidance={true} />);
        expect(screen.getByText(new RegExp(guidance.substring(0, 20), 'i'))).toBeTruthy();
      });
    });
  });

  describe('Experience-First Symptom Selection', () => {
    it('should present symptoms in natural language', () => {
      const naturalSymptoms = [
        'I feel overwhelmed by daily tasks',
        'I have trouble sleeping at night',
        'I feel disconnected from my prayers',
        'I worry constantly about the future',
      ];

      naturalSymptoms.forEach(symptom => {
        render(<SymptomItem symptom={{ name: symptom, id: '1' }} onSelect={jest.fn()} />);
        expect(screen.getByText(symptom)).toBeTruthy();
      });
    });

    it('should avoid clinical terminology initially', () => {
      const clinicalTerms = ['depression', 'anxiety disorder', 'PTSD', 'bipolar'];
      
      render(<AssessmentFlow />);
      
      clinicalTerms.forEach(term => {
        expect(screen.queryByText(new RegExp(term, 'i'))).toBeFalsy();
      });
    });

    it('should allow multiple symptom selection', () => {
      const onSelect = jest.fn();
      const symptoms = mockSymptoms;
      
      symptoms.forEach((symptom, index) => {
        render(<SymptomItem symptom={symptom} onSelect={onSelect} />);
        
        const symptomElement = screen.getByText(symptom.name);
        fireEvent.press(symptomElement);
        
        expect(onSelect).toHaveBeenCalledWith(symptom.id);
      });
    });

    it('should show all symptoms to reduce cognitive load', () => {
      render(<AssessmentFlow />);
      
      // Should display comprehensive symptom list
      expect(screen.getByText(/all symptoms/i)).toBeTruthy();
      expect(screen.getByText(/choose any that apply/i)).toBeTruthy();
    });
  });

  describe('Assessment Flow and Progress', () => {
    it('should track assessment progress', async () => {
      const progress = {
        currentStep: 2,
        totalSteps: 5,
        selectedSymptoms: ['1', '2'],
        completedReflections: 1,
      };

      mockAssessmentService.saveProgress.mockResolvedValue(true);
      
      await assessmentService.saveProgress(progress);
      expect(mockAssessmentService.saveProgress).toHaveBeenCalledWith(progress);
    });

    it('should provide reflection prompts for deeper understanding', async () => {
      const reflectionPrompts = [
        'When did you first notice these feelings?',
        'How do these experiences affect your relationship with Allah?',
        'What brings you comfort during difficult times?',
      ];

      mockAssessmentService.getReflectionPrompts.mockResolvedValue(reflectionPrompts);
      
      const prompts = await assessmentService.getReflectionPrompts(['1', '2']);
      expect(prompts).toEqual(reflectionPrompts);
    });

    it('should handle assessment interruption gracefully', async () => {
      mockAssessmentService.pauseAssessment.mockResolvedValue(true);
      
      const result = await assessmentService.pauseAssessment('user_request');
      expect(result).toBe(true);
    });
  });

  describe('Crisis Detection During Assessment', () => {
    it('should detect crisis indicators in symptom selection', async () => {
      const crisisSymptoms = [
        'thoughts of self-harm',
        'feeling hopeless',
        'wanting to end my life',
      ];

      for (const symptom of crisisSymptoms) {
        mockAssessmentService.detectCrisisDuringAssessment.mockResolvedValue(true);
        
        const result = await assessmentService.detectCrisisDuringAssessment([symptom]);
        expect(result).toBe(true);
      }
    });

    it('should pause assessment when crisis detected', async () => {
      mockAssessmentService.pauseForCrisis.mockResolvedValue({
        paused: true,
        crisisLevel: 'high',
        nextAction: 'emergency_support',
      });
      
      const result = await assessmentService.pauseForCrisis(['crisis_symptom']);
      expect(result.paused).toBe(true);
      expect(result.nextAction).toBe('emergency_support');
    });

    it('should preserve assessment data during crisis intervention', async () => {
      const assessmentData = {
        selectedSymptoms: ['1', '2'],
        reflections: ['reflection1'],
        progress: 50,
      };

      mockAssessmentService.preserveAssessmentData.mockResolvedValue(true);
      
      const result = await assessmentService.preserveAssessmentData(assessmentData);
      expect(result).toBe(true);
    });
  });

  describe('AI Analysis and Diagnosis', () => {
    it('should generate comprehensive 5-layer analysis', async () => {
      const analysisResult = {
        layers: {
          Jism: { symptoms: ['fatigue'], recommendations: ['rest', 'exercise'] },
          Nafs: { symptoms: ['anxiety'], recommendations: ['dhikr', 'meditation'] },
          Aql: { symptoms: ['confusion'], recommendations: ['reflection', 'study'] },
          Qalb: { symptoms: ['spiritual emptiness'], recommendations: ['prayer', 'Quran'] },
          Ruh: { symptoms: ['disconnection'], recommendations: ['worship', 'community'] },
        },
        overallAssessment: 'Moderate spiritual and emotional challenges',
        islamicGuidance: 'Focus on strengthening your connection with Allah',
      };

      mockAssessmentService.generateAnalysis.mockResolvedValue(analysisResult);
      
      const result = await assessmentService.generateAnalysis(['1', '2']);
      expect(result.layers).toBeDefined();
      expect(Object.keys(result.layers)).toHaveLength(5);
    });

    it('should provide Islamic context in diagnosis', async () => {
      const diagnosis = await assessmentService.generateAnalysis(['1']);
      
      expect(diagnosis.islamicGuidance).toBeDefined();
      expect(diagnosis.islamicGuidance).toMatch(/Allah|Islamic|spiritual/i);
    });

    it('should adapt language based on user persona', async () => {
      const professionalUser = { ...mockUserProfile, profession: 'Healthcare Professional' };
      const traditionalUser = { ...mockUserProfile, islamicBackground: 'traditional' };

      // Professional language
      mockAssessmentService.generatePersonalizedAnalysis.mockResolvedValue({
        language: 'clinical_islamic',
        content: 'Evidence-based Islamic therapeutic approach recommended',
      });

      let result = await assessmentService.generatePersonalizedAnalysis(['1'], professionalUser);
      expect(result.language).toBe('clinical_islamic');

      // Traditional language
      mockAssessmentService.generatePersonalizedAnalysis.mockResolvedValue({
        language: 'gentle_traditional',
        content: 'May Allah grant you ease and healing',
      });

      result = await assessmentService.generatePersonalizedAnalysis(['1'], traditionalUser);
      expect(result.language).toBe('gentle_traditional');
    });
  });

  describe('Integration Tests', () => {
    it('should complete full assessment flow', async () => {
      const navigation = createMockNavigation();
      
      mockAssessmentService.completeAssessment.mockResolvedValue({
        analysis: { layers: {}, overallAssessment: 'Complete' },
        nextStep: 'journeys',
        recommendations: ['journey1', 'journey2'],
      });
      
      render(<AssessmentFlow />);
      
      await waitFor(() => {
        expect(mockAssessmentService.completeAssessment).toHaveBeenCalled();
      });
    });

    it('should handle assessment errors gracefully', async () => {
      mockAssessmentService.generateAnalysis.mockRejectedValue(new Error('AI service unavailable'));
      
      render(<AssessmentFlow />);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeTruthy();
        expect(screen.getByText(/try again/i)).toBeTruthy();
      });
    });
  });

  describe('Islamic Authenticity in Assessment', () => {
    it('should validate Islamic content in all assessment materials', () => {
      const islamicContent = [
        {
          arabic: 'وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ',
          reference: 'Quran 17:82',
          translation: 'And We send down of the Quran that which is healing and mercy for the believers',
        },
      ];

      islamicContent.forEach(content => {
        expect(validateIslamicContent(content)).toBe(true);
      });
    });

    it('should ensure cultural sensitivity throughout assessment', () => {
      render(<AssessmentFlow />);
      
      // Check for appropriate Islamic language
      expect(screen.queryByText(/insha\'Allah/i)).toBeTruthy();
      expect(screen.queryByText(/barakAllahu feeki/i)).toBeTruthy();
    });
  });
});
