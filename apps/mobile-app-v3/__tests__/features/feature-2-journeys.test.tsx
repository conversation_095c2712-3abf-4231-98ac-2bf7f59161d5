/**
 * Feature 2: Personalized Healing Journeys Tests
 * Tests AI-powered journey generation, daily practices, and community integration
 */

import React from 'react';
import {
  render,
  fireEvent,
  waitFor,
  screen,
} from '@testing-library/react-native';
import { jest } from '@jest/globals';
import '../setup';

// Import components to test
import JourneysScreen from '../../src/app/journeys';
import JourneyDashboard from '../../src/screens/JourneyDashboardScreen';
import { JourneyCard } from '../../src/components/JourneyCard';
import { DailyPracticeCard } from '../../src/components/DailyPracticeCard';
import { JourneyStatsCard } from '../../src/components/JourneyStatsCard';

// Import services
// import { JourneyService } from '../../src/services/api/JourneyService';

// Import test utilities
import {
  mockJourney,
  mockUserProfile,
  createMockNavigation,
  validateIslamicContent,
  validateArabicText,
} from '../setup';

// Mock services
jest.mock('../../src/services/journey.service');

const mockJourneyService = journeyService as jest.Mocked<typeof journeyService>;

describe('Feature 2: Personalized Healing Journeys', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock for getJourneyAnalytics used by JourneyDashboard
    mockJourneyService.getCurrentJourney.mockResolvedValue(mockJourney as any); // Ensure dashboard has a journey
    mockJourneyService.getJourneyAnalytics.mockResolvedValue({
      journeyId: mockJourney.id,
      completionRate: mockJourney.totalProgress || 0,
      averageDailyRating: 4.5, // Example value
      practiceAdherence: 80,
      communityEngagement: 0,
      symptomImprovement: { primaryLayer: 0, secondaryLayers: {}, overallWellness: 0 },
      spiritualDevelopment: { islamicPracticeIntegration: 0, spiritualConnection: 0, communityConnection: 0 },
      personalizationEffectiveness: { contentRelevance: 0, culturalAdaptation: 0, professionalIntegration: 0, timeManagement: 0 },
      nextStepRecommendations: [],
      graduationReadiness: false,
      // Fields used by dashboard:
      streakDays: 5,
      completedDays: mockJourney.completedDays?.length || 10,
      averageRating: 4.5, // Can also be used by dashboard as fallback
    });
  });

  describe('Journey Generation and Personalization', () => {
    it('should generate personalized journey based on assessment results', async () => {
      const assessmentResults = {
        layers: {
          Nafs: { symptoms: ['anxiety'], severity: 'moderate' },
          Qalb: { symptoms: ['spiritual emptiness'], severity: 'mild' },
        },
        userProfile: mockUserProfile,
      };

      const expectedJourney = {
        ...mockJourney,
        title: 'Calming the Anxious Heart',
        practices: [
          {
            type: 'dhikr',
            content: 'La hawla wa la quwwata illa billah',
            duration: 10,
          },
          { type: 'dua', content: 'Dua for anxiety relief', duration: 5 },
        ],
      };

      mockJourneyService.generatePersonalizedJourney.mockResolvedValue(
        expectedJourney
      );

      const result = await journeyService.generatePersonalizedJourney(
        assessmentResults
      );

      expect(result.title).toBe('Calming the Anxious Heart');
      expect(result.practices).toHaveLength(2);
      expect(
        mockJourneyService.generatePersonalizedJourney
      ).toHaveBeenCalledWith(assessmentResults);
    });

    it('should adapt journey duration based on user availability', async () => {
      const userPreferences = {
        ...mockUserProfile,
        timeCommitment: 'low', // 5-10 minutes daily
      };

      mockJourneyService.adaptJourneyDuration.mockResolvedValue({
        ...mockJourney,
        duration: 7,
        dailyTimeCommitment: 8,
      });

      const result = await journeyService.adaptJourneyDuration(
        mockJourney,
        userPreferences
      );

      expect(result.dailyTimeCommitment).toBeLessThanOrEqual(10);
    });

    it('should consider cultural and professional context', async () => {
      const professionalUser = {
        ...mockUserProfile,
        profession: 'Healthcare Professional',
      };
      const traditionalUser = {
        ...mockUserProfile,
        islamicBackground: 'traditional',
      };

      // Professional journey
      mockJourneyService.generatePersonalizedJourney.mockResolvedValue({
        ...mockJourney,
        title: 'Clinical Islamic Integration',
        practices: [
          { type: 'reflection', content: 'Evidence-based Islamic practices' },
        ],
      });

      let result = await journeyService.generatePersonalizedJourney({
        userProfile: professionalUser,
      });
      expect(result.title).toContain('Clinical');

      // Traditional journey
      mockJourneyService.generatePersonalizedJourney.mockResolvedValue({
        ...mockJourney,
        title: 'Traditional Islamic Healing',
        practices: [
          { type: 'dhikr', content: 'Traditional remembrance practices' },
        ],
      });

      result = await journeyService.generatePersonalizedJourney({
        userProfile: traditionalUser,
      });
      expect(result.title).toContain('Traditional');
    });
  });

  describe('Daily Practice Flow', () => {
    const mockDailyPractice = {
      id: '1',
      type: 'dhikr',
      title: 'Morning Remembrance',
      arabic: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
      transliteration: 'Subhan Allahi wa bihamdihi',
      translation: 'Glory is to Allah and praise is to Him',
      duration: 5,
      repetitions: 33,
    };

    it('should render daily practice with Islamic content', () => {
      render(
        <DailyPracticeCard
          practice={mockDailyPractice}
          onComplete={jest.fn()}
        />
      );

      expect(screen.getByText(mockDailyPractice.title)).toBeTruthy();
      expect(screen.getByText(mockDailyPractice.arabic)).toBeTruthy();
      expect(screen.getByText(mockDailyPractice.transliteration)).toBeTruthy();
      expect(screen.getByText(mockDailyPractice.translation)).toBeTruthy();
    });

    it('should validate Arabic text in dhikr practices', () => {
      render(
        <DailyPracticeCard
          practice={mockDailyPractice}
          onComplete={jest.fn()}
        />
      );

      expect(validateArabicText(mockDailyPractice.arabic)).toBe(true);
    });

    it('should track practice completion and progress', async () => {
      const onComplete = jest.fn();
      render(
        <DailyPracticeCard
          practice={mockDailyPractice}
          onComplete={onComplete}
        />
      );

      const completeButton = screen.getByText(/complete/i);
      fireEvent.press(completeButton);

      expect(onComplete).toHaveBeenCalledWith(mockDailyPractice.id);
    });

    it('should provide prayer guidance and reflection prompts', async () => {
      const prayerPractice = {
        ...mockDailyPractice,
        type: 'prayer',
        guidance: 'Focus on your connection with Allah during this prayer',
        reflectionPrompt: 'How did this prayer make you feel?',
      };

      render(
        <DailyPracticeCard practice={prayerPractice} onComplete={jest.fn()} />
      );

      expect(screen.getByText(prayerPractice.guidance)).toBeTruthy();
      expect(screen.getByText(prayerPractice.reflectionPrompt)).toBeTruthy();
    });

    it('should integrate Quranic study with context and application', async () => {
      const quranPractice = {
        ...mockDailyPractice,
        type: 'quran_study',
        verse: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
        reference: 'Quran 65:2',
        context: 'This verse reminds us that Allah always provides a way out',
        application: 'Reflect on how this applies to your current challenges',
      };

      render(
        <DailyPracticeCard practice={quranPractice} onComplete={jest.fn()} />
      );

      expect(screen.getByText(quranPractice.context)).toBeTruthy();
      expect(screen.getByText(quranPractice.application)).toBeTruthy();
    });
  });

  describe('Progress Tracking and Analytics', () => {
    it('should track mood and spiritual metrics', async () => {
      const progressData = {
        date: '2024-12-01',
        mood: 7,
        spiritualConnection: 8,
        practicesCompleted: 3,
        totalPractices: 4,
        reflectionNotes: 'Feeling more connected to Allah today',
      };

      mockJourneyService.trackProgress.mockResolvedValue(true);

      const result = await journeyService.trackProgress(progressData);
      expect(result).toBe(true);
      expect(mockJourneyService.trackProgress).toHaveBeenCalledWith(
        progressData
      );
    });

    it('should display journey statistics', () => {
      const stats = {
        totalDays: 7,
        completedDays: 5,
        averageMood: 7.2,
        averageSpiritualConnection: 8.1,
        practicesCompleted: 15,
        totalPractices: 20,
      };

      render(<JourneyStatsCard stats={stats} />);

      expect(screen.getByText('5/7')).toBeTruthy(); // Completed days
      expect(screen.getByText('7.2')).toBeTruthy(); // Average mood
      expect(screen.getByText('8.1')).toBeTruthy(); // Spiritual connection
    });

    it('should provide insights based on progress patterns', async () => {
      const progressHistory = [
        { date: '2024-12-01', mood: 6, spiritualConnection: 7 },
        { date: '2024-12-02', mood: 7, spiritualConnection: 8 },
        { date: '2024-12-03', mood: 8, spiritualConnection: 9 },
      ];

      mockJourneyService.generateInsights.mockResolvedValue({
        trend: 'improving',
        insights: [
          'Your mood is steadily improving',
          'Spiritual connection is strengthening',
        ],
        recommendations: [
          'Continue with dhikr practices',
          'Add more Quran reflection',
        ],
      });

      const result = await journeyService.generateInsights(progressHistory);

      expect(result.trend).toBe('improving');
      expect(result.insights).toHaveLength(2);
      expect(result.recommendations).toHaveLength(2);
    });
  });

  describe('Community Integration', () => {
    it('should match users with appropriate peer support groups', async () => {
      const userCriteria = {
        age: 25,
        gender: 'female',
        islamicBackground: 'practicing',
        challenges: ['anxiety', 'spiritual_growth'],
      };

      const expectedGroup = {
        id: 'group1',
        name: 'Sisters Supporting Sisters',
        members: 8,
        focus: 'anxiety_support',
        islamicApproach: true,
      };

      mockJourneyService.findPeerSupportGroup.mockResolvedValue(expectedGroup);

      const result = await journeyService.findPeerSupportGroup(userCriteria);

      expect(result.name).toBe('Sisters Supporting Sisters');
      expect(result.islamicApproach).toBe(true);
    });

    it('should assign appropriate mentors', async () => {
      const mentorCriteria = {
        userProfile: mockUserProfile,
        preferredGender: 'same',
        experienceLevel: 'intermediate',
      };

      const expectedMentor = {
        id: 'mentor1',
        name: 'Sister Aisha',
        experience: '5 years',
        specialization: 'anxiety_islamic_healing',
        availability: 'weekends',
      };

      mockJourneyService.assignMentor.mockResolvedValue(expectedMentor);

      const result = await journeyService.assignMentor(mentorCriteria);

      expect(result.name).toBe('Sister Aisha');
      expect(result.specialization).toContain('islamic_healing');
    });

    it('should provide community activities and discussions', async () => {
      const communityActivities = [
        {
          id: 'activity1',
          type: 'group_dhikr',
          title: 'Weekly Dhikr Circle',
          participants: 12,
          nextSession: '2024-12-15T19:00:00Z',
        },
        {
          id: 'activity2',
          type: 'quran_study',
          title: 'Surah Al-Fatiha Reflection',
          participants: 8,
          nextSession: '2024-12-16T20:00:00Z',
        },
      ];

      mockJourneyService.getCommunityActivities.mockResolvedValue(
        communityActivities
      );

      const result = await journeyService.getCommunityActivities();

      expect(result).toHaveLength(2);
      expect(result[0].type).toBe('group_dhikr');
      expect(result[1].type).toBe('quran_study');
    });

    it('should ensure cultural group alignment', async () => {
      const culturalPreferences = {
        language: 'arabic',
        culturalBackground: 'middle_eastern',
        practiceLevel: 'traditional',
      };

      mockJourneyService.findCulturallyAlignedGroup.mockResolvedValue({
        id: 'cultural_group1',
        name: 'Arabic Speaking Sisters',
        culturalAlignment: 'middle_eastern',
        language: 'arabic',
      });

      const result = await journeyService.findCulturallyAlignedGroup(
        culturalPreferences
      );

      expect(result.language).toBe('arabic');
      expect(result.culturalAlignment).toBe('middle_eastern');
    });
  });

  describe('Emergency Mode Integration', () => {
    it('should provide access to emergency mode from journey dashboard', () => {
      render(<JourneyDashboard />);

      const emergencyButton = screen.getByText(/emergency/i);
      expect(emergencyButton).toBeTruthy();

      fireEvent.press(emergencyButton);
      // Should navigate to emergency mode
    });

    it('should detect crisis during journey practices', async () => {
      const crisisIndicators = [
        'feeling hopeless during practice',
        'thoughts of self-harm',
        'unable to continue',
      ];

      for (const indicator of crisisIndicators) {
        mockJourneyService.detectCrisisDuringJourney.mockResolvedValue(true);

        const result = await journeyService.detectCrisisDuringJourney(
          indicator
        );
        expect(result).toBe(true);
      }
    });

    it('should preserve journey context during emergency support', async () => {
      const journeyContext = {
        currentJourney: mockJourney,
        currentDay: 3,
        todaysPractices: ['dhikr', 'reflection'],
        progress: { mood: 4, spiritualConnection: 3 },
      };

      mockJourneyService.preserveJourneyContext.mockResolvedValue(true);

      const result = await journeyService.preserveJourneyContext(
        journeyContext
      );
      expect(result).toBe(true);
    });

    it('should provide seamless return to journey after crisis support', async () => {
      mockJourneyService.resumeJourneyAfterCrisis.mockResolvedValue({
        resumePoint: 'day_3_practice_2',
        adjustedPractices: ['gentle_dhikr', 'comfort_dua'],
        supportMessage: 'Welcome back, take it gently today',
      });

      const result = await journeyService.resumeJourneyAfterCrisis('journey1');

      expect(result.resumePoint).toBe('day_3_practice_2');
      expect(result.adjustedPractices).toContain('gentle_dhikr');
    });
  });

  describe('Integration Tests', () => {
    it('should complete full journey creation and start flow', async () => {
      const navigation = createMockNavigation();

      mockJourneyService.createAndStartJourney.mockResolvedValue({
        journey: mockJourney,
        firstPractice: mockJourney.practices[0],
        communityGroup: { id: 'group1', name: 'Support Group' },
      });

      render(<JourneysScreen />);

      await waitFor(() => {
        expect(mockJourneyService.createAndStartJourney).toHaveBeenCalled();
      });
    });

    it('should handle journey service errors gracefully', async () => {
      mockJourneyService.generatePersonalizedJourney.mockRejectedValue(
        new Error('AI service unavailable')
      );

      render(<JourneysScreen />);

      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeTruthy();
        expect(screen.getByText(/try again/i)).toBeTruthy();
      });
    });

    it('should sync journey progress across devices', async () => {
      const progressData = {
        journeyId: 'journey1',
        progress: { completed: 3, total: 7 },
        lastSync: new Date().toISOString(),
      };

      mockJourneyService.syncProgress.mockResolvedValue(true);

      const result = await journeyService.syncProgress(progressData);
      expect(result).toBe(true);
    });
  });

  describe('Islamic Authenticity in Journeys', () => {
    it('should validate all Islamic practices for authenticity', () => {
      const islamicPractices = [
        {
          type: 'dhikr',
          arabic: 'لَا إِلَٰهَ إِلَّا اللَّهُ',
          transliteration: 'La ilaha illa Allah',
          translation: 'There is no god but Allah',
        },
        {
          type: 'dua',
          arabic: 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً',
          reference: 'Quran 2:201',
        },
      ];

      islamicPractices.forEach((practice) => {
        expect(validateIslamicContent(practice)).toBe(true);
      });
    });

    it('should ensure community activities align with Islamic values', async () => {
      const activities = await journeyService.getCommunityActivities();

      activities.forEach((activity) => {
        expect(['group_dhikr', 'quran_study', 'islamic_discussion']).toContain(
          activity.type
        );
      });
    });
  });
});
