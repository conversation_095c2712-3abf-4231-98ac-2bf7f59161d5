import React from 'react';
import { render, fireEvent, waitFor, screen, act } from '@testing-library/react-native';
import { Alert, Linking } from 'react-native';
import { router } from 'expo-router';
import OnboardingScreen from '../../src/app/onboarding/index';
import { onboardingService } from '../../src/services/onboarding.service';
import { authService } from '../../src/services/auth.service';
import '../setup'; // For global mocks like Dimensions, Platform

// Mock services
jest.mock('../../src/services/onboarding.service');
jest.mock('../../src/services/auth.service');

// Mock expo-router
jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
  usePathname: jest.fn(() => '/onboarding'), // Mock pathname
}));

// Mock Linking
jest.mock('react-native/Libraries/Linking/Linking', () => ({
  canOpenURL: jest.fn(() => Promise.resolve(true)),
  openURL: jest.fn(() => Promise.resolve()),
}));

// Mock child components to isolate OnboardingScreen logic
jest.mock('../../src/components/OnboardingQuestion', () => {
  const { View, Button } = require('react-native');
  return {
    OnboardingQuestion: jest.fn(({ onResponse, question }) => ( // Make sure it can receive onResponse
      <View testID="mock-onboarding-question">
        <Button title="Mock Submit Response" onPress={() => onResponse({ mockAnswer: 'test' })} />
         <Button title={`Mock Welcome Action ${question?.actions?.[0]?.id}`} onPress={() => onResponse({ action: question?.actions?.[0]?.id })} />
      </View>
    )),
  };
});
jest.mock('../../src/components/WelcomeScreen', () => {
  const { View, Button } = require('react-native');
  return {
    WelcomeScreen: jest.fn(({ onResponse, question }) => ( // Make sure it can receive onResponse
      <View testID="mock-welcome-screen">
        <Button title={`Mock Welcome Action ${question?.actions?.[0]?.id}`} onPress={() => onResponse({ action: question?.actions?.[0]?.id })} />
      </View>
    )),
  };
});
jest.mock('../../src/components/CrisisModal', () => {
    const { View, Button } = require('react-native');
    return {
        CrisisModal: jest.fn(({ visible, onAction, crisisData }) =>
            visible ? (
                <View testID="mock-crisis-modal">
                    <Button title="Mock Crisis Action" onPress={() => onAction(crisisData?.actions?.[0]?.id || 'emergency_sakina')} />
                </View>
            ) : null
        ),
    };
});
jest.mock('../../src/components/GoogleSignInButton', () => {
  const { View } = require('react-native');
  return {
    GoogleSignInButton: jest.fn(() => <View testID="mock-google-signin" />),
  };
});


const mockOnboardingService = onboardingService as jest.Mocked<typeof onboardingService>;
const mockAuthService = authService as jest.Mocked<typeof authService>;
const mockRouterReplace = router.replace as jest.Mock;
const mockLinkingOpenURL = Linking.openURL as jest.Mock;

// Mock Alert
jest.spyOn(Alert, 'alert');


describe('OnboardingScreen', () => {
  const initialQuestion = { id: 'welcome_q', type: 'welcome', title: 'Welcome!', actions: [{id: 'begin', text: 'Begin'}] };
  const firstQuestion = { id: 'q1', type: 'single_choice', title: 'First Question', options: [{id: 'opt1', text: 'Option 1'}]};

  beforeEach(() => {
    jest.clearAllMocks();
    mockOnboardingService.startOnboarding.mockResolvedValue({
        session: { sessionId: 'test-session-id', currentStep: 'welcome', startedAt: new Date().toISOString() },
        question: { question: initialQuestion, progress: 0 } as any, // Ensure question is nested under 'question'
    });
    mockOnboardingService.submitResponse.mockResolvedValue({
        question: firstQuestion, // submitResponse returns question directly
        progress: 10,
    } as any);
    mockOnboardingService.skipOnboarding.mockResolvedValue({
        data: {
            profile: { userId: 'skipped-user' },
            recommendedPathway: 'gentle_introduction',
        }
    } as any);
    mockAuthService.updateUserProfile.mockResolvedValue(undefined);
  });

  it('initializes onboarding on mount and shows loading indicator', async () => {
    render(<OnboardingScreen />);
    expect(screen.getByText('Preparing your journey...')).toBeTruthy();
    await waitFor(() => expect(mockOnboardingService.startOnboarding).toHaveBeenCalledTimes(1));
    await waitFor(() => expect(screen.queryByText('Preparing your journey...')).toBeNull()); // Loading done
    await waitFor(() => expect(screen.getByTestId('mock-welcome-screen')).toBeTruthy());
  });

  it('handles error during initialization and shows retry/skip alert', async () => {
    mockOnboardingService.startOnboarding.mockRejectedValueOnce(new Error('Network Error'));
    render(<OnboardingScreen />);
    await waitFor(() => expect(Alert.alert).toHaveBeenCalledWith(
      'Connection Error',
      'Unable to start onboarding. Please check your connection and try again.',
      expect.arrayContaining([
        expect.objectContaining({ text: 'Retry' }),
        expect.objectContaining({ text: 'Skip' }),
      ])
    ));
  });

  it('submits response and navigates to the next question', async () => {
    render(<OnboardingScreen />);
    await waitFor(() => expect(screen.getByTestId('mock-welcome-screen')).toBeTruthy()); // Wait for init

    // Simulate response from WelcomeScreen (or OnboardingQuestion)
    // The mock OnboardingQuestion/WelcomeScreen has a button that calls onResponse
    fireEvent.press(screen.getByText(`Mock Welcome Action ${initialQuestion.actions[0].id}`));

    await waitFor(() => expect(mockOnboardingService.submitResponse).toHaveBeenCalledTimes(1));
    // Check if the state updated to show the next question (mocked by OnboardingQuestion)
    // This implicitly tests that the screen updated its currentQuestion state
    // If OnboardingQuestion mock was more detailed, we could check its props.
  });

  it('handles onboarding completion and navigates', async () => {
    mockOnboardingService.submitResponse.mockResolvedValueOnce({
      profile: { userId: 'user123', name: 'Test User' },
      recommendedPathway: 'crisis_support', // Test a specific pathway
      featureConfiguration: {},
      nextSteps: [],
    } as any);

    render(<OnboardingScreen />);
    await waitFor(() => expect(screen.getByTestId('mock-welcome-screen')).toBeTruthy());
    
    fireEvent.press(screen.getByText(`Mock Welcome Action ${initialQuestion.actions[0].id}`)); // Trigger submit

    await waitFor(() => {
      expect(mockAuthService.updateUserProfile).toHaveBeenCalledWith({ userId: 'user123', name: 'Test User' });
      expect(mockRouterReplace).toHaveBeenCalledWith('/emergency');
    });
  });
  
   it('navigates to default tabs if pathway is not crisis_support or specific', async () => {
    mockOnboardingService.submitResponse.mockResolvedValueOnce({
      profile: { userId: 'user123' },
      recommendedPathway: 'standard_journey', // A generic pathway
    } as any);

    render(<OnboardingScreen />);
    await waitFor(() => screen.getByTestId('mock-welcome-screen'));
    fireEvent.press(screen.getByText(`Mock Welcome Action ${initialQuestion.actions[0].id}`));

    await waitFor(() => expect(mockRouterReplace).toHaveBeenCalledWith('/(tabs)'));
  });


  it('handles crisis detection response', async () => {
    const crisisData = { level: 'high', message: 'Crisis detected!', actions: [{id: 'emergency_sakina', text:'Sakina'}] };
    mockOnboardingService.submitResponse.mockResolvedValueOnce(crisisData as any);
    
    render(<OnboardingScreen />);
    await waitFor(() => screen.getByTestId('mock-welcome-screen'));
    fireEvent.press(screen.getByText(`Mock Welcome Action ${initialQuestion.actions[0].id}`));

    await waitFor(() => expect(screen.getByTestId('mock-crisis-modal')).toBeTruthy());
  });

  it('handles crisis modal actions (e.g., emergency_sakina)', async () => {
    const crisisData = { level: 'high', message: 'Crisis detected!', actions: [{id: 'emergency_sakina', text:'Sakina Mode'}] };
    mockOnboardingService.submitResponse.mockResolvedValueOnce(crisisData as any);

    render(<OnboardingScreen />);
    await waitFor(() => screen.getByTestId('mock-welcome-screen'));
    fireEvent.press(screen.getByText(`Mock Welcome Action ${initialQuestion.actions[0].id}`)); // Trigger submit that leads to crisis
    
    await waitFor(() => screen.getByTestId('mock-crisis-modal'));
    fireEvent.press(screen.getByText('Mock Crisis Action')); // Action from CrisisModal mock

    expect(mockRouterReplace).toHaveBeenCalledWith('/emergency');
  });
  
  it('handles crisis modal action for hotline call', async () => {
    const crisisData = { level: 'high', message: 'Crisis detected!', actions: [{id: 'crisis_hotline', text:'Call Hotline'}] };
     mockOnboardingService.submitResponse.mockResolvedValueOnce(crisisData as any);

    render(<OnboardingScreen />);
    await waitFor(() => screen.getByTestId('mock-welcome-screen'));
    fireEvent.press(screen.getByText(`Mock Welcome Action ${initialQuestion.actions[0].id}`));
    
    await waitFor(() => screen.getByTestId('mock-crisis-modal'));
    // Simulate the modal calling onAction with 'crisis_hotline'
    // This depends on how the mock CrisisModal is set up to pass the actionId.
    // For this test, assume the mock CrisisModal's button press calls onAction with the first action's ID.
    fireEvent.press(screen.getByText('Mock Crisis Action')); 

    expect(mockLinkingOpenURL).toHaveBeenCalledWith('tel:988');
  });


  it('handles skip onboarding', async () => {
    render(<OnboardingScreen />);
    await waitFor(() => screen.getByTestId('mock-welcome-screen')); // Wait for init to complete

    fireEvent.press(screen.getByText('Skip')); // Press the actual skip button
    // Alert.alert is called, first confirm
    expect(Alert.alert).toHaveBeenCalled();
    
    // Simulate user pressing "Skip" on the Alert
    // @ts-ignore
    const alertArgs = Alert.alert.mock.calls[0];
    const skipButtonAction = alertArgs[2].find((b: any) => b.text === 'Skip');
    
    await act(async () => {
      if (skipButtonAction && skipButtonAction.onPress) {
        skipButtonAction.onPress();
      }
    });

    await waitFor(() => expect(mockOnboardingService.skipOnboarding).toHaveBeenCalledWith('time_constraint'));
    await waitFor(() => expect(mockAuthService.updateUserProfile).toHaveBeenCalled());
    await waitFor(() => expect(mockRouterReplace).toHaveBeenCalledWith('/(tabs)')); // Default for gentle_introduction
  });
  
  it('shows loading indicator during response submission', async () => {
    mockOnboardingService.submitResponse.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ question: firstQuestion, progress: 10 } as any), 100))
    );
    render(<OnboardingScreen />);
    await waitFor(() => screen.getByTestId('mock-welcome-screen'));
    
    fireEvent.press(screen.getByText(`Mock Welcome Action ${initialQuestion.actions[0].id}`));
    
    // Check for ActivityIndicator or a loading state text if OnboardingQuestion mock shows it
    // This depends on how isLoading is propagated and handled by the mock.
    // For now, we check if the service was called, implying interaction started.
    await waitFor(() => expect(mockOnboardingService.submitResponse).toHaveBeenCalled());
    // A more robust test would check for a visible loading indicator if the mock component rendered one based on `isLoading` prop.
  });

});
