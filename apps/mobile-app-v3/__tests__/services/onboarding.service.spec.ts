import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Application from 'expo-application';
import * as Localization from 'expo-localization';
import { onboardingService, OnboardingResponse, SubmitResponseRequest, DeviceInfo } from '../../src/services/onboarding.service';
import { API_BASE_URL } from '../../src/constants/Config';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

// Mock expo-application and expo-localization
jest.mock('expo-application', () => ({
  nativeApplicationVersion: '1.0.0',
  nativeBuildVersion: '1',
}));
jest.mock('expo-localization', () => ({
  locale: 'en-US',
}));

// Global fetch mock
global.fetch = jest.fn();

const mockFetch = global.fetch as jest.Mock;
const mockAsyncStorageGetItem = AsyncStorage.getItem as jest.Mock;
const mockAsyncStorageSetItem = AsyncStorage.setItem as jest.Mock;
const mockAsyncStorageRemoveItem = AsyncStorage.removeItem as jest.Mock;

describe('OnboardingServiceClass', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset internal state of the singleton for each test
    onboardingService['currentSessionId'] = null;
    onboardingService['sessionStartTime'] = 0;
    mockAsyncStorageGetItem.mockResolvedValue(null); // Default to no stored token or session
  });

  const mockApiUrl = (path: string) => `${API_BASE_URL}/api/onboarding${path}`;

  describe('startOnboarding', () => {
    const mockDeviceInfo: Partial<DeviceInfo> = { screenSize: '1024x768' };
    const mockFullDeviceInfo: DeviceInfo = {
        platform: 'mobile',
        version: '1.0.0',
        locale: 'en-US',
        screenSize: '1024x768'
    };
    const mockSuccessResponse: OnboardingResponse = {
      status: 'success',
      data: {
        session: { sessionId: 'session123', startedAt: new Date().toISOString(), currentStep: 'welcome' },
        question: { id: 'q1', type: 'welcome', title: 'Welcome' },
        progress: 0,
      },
    };

    it('should start an onboarding session successfully', async () => {
      mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token'); // For getAuthHeaders
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockSuccessResponse), // fetch returns the full structure
      });

      const result = await onboardingService.startOnboarding(mockDeviceInfo);
      
      expect(mockFetch).toHaveBeenCalledWith(
        mockApiUrl('/start'),
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json', Authorization: 'Bearer fake-auth-token' },
          body: JSON.stringify({ deviceInfo: mockFullDeviceInfo }),
        })
      );
      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('onboarding_session_id', 'session123');
      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('onboarding_start_time', expect.any(String));
      expect(onboardingService.getCurrentSessionId()).toBe('session123');
      expect(result).toEqual(mockSuccessResponse.data); // Service maps response.data
    });

    it('should throw an error if API call fails', async () => {
      mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Server Error',
        json: jest.fn().mockResolvedValueOnce({ message: 'Internal Server Error' }),
      });

      await expect(onboardingService.startOnboarding(mockDeviceInfo)).rejects.toThrow('Internal Server Error');
    });
    
    it('should use default device info if none provided', async () => {
      mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockSuccessResponse),
      });
      await onboardingService.startOnboarding(); // No device info
      expect(mockFetch).toHaveBeenCalledWith(
        mockApiUrl('/start'),
        expect.objectContaining({
          body: JSON.stringify({ deviceInfo: { platform: 'mobile', version: '1.0.0', locale: 'en-US'} }),
        })
      );
    });
  });

  describe('submitResponse', () => {
    const mockSubmitRequest: SubmitResponseRequest = {
      sessionId: 'session123',
      stepId: 'q1',
      response: { answer: 'yes' },
      timeSpent: 30,
    };
    const mockContinueResponse: OnboardingResponse = {
      status: 'continue',
      data: {
        question: { id: 'q2', type: 'single_choice', title: 'Next Question' },
        progress: 50,
        step: 'q2',
      },
    };

    it('should submit a response and handle "continue" status', async () => {
      mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockContinueResponse),
      });

      const result = await onboardingService.submitResponse(mockSubmitRequest);

      expect(mockFetch).toHaveBeenCalledWith(
        mockApiUrl('/respond'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(mockSubmitRequest),
        })
      );
      expect(result).toEqual(mockContinueResponse.data); // Service maps response.data
    });

    it('should handle "completed" status and call handleOnboardingCompletion', async () => {
      const mockCompletedResponse: OnboardingResponse = {
        status: 'completed',
        data: {
          profile: { userId: 'user1', name: 'Test User' },
          recommendedPathway: 'standard',
        },
      };
      mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockCompletedResponse),
      });
      const handleCompletionSpy = jest.spyOn(onboardingService as any, 'handleOnboardingCompletion');

      const result = await onboardingService.submitResponse(mockSubmitRequest);

      expect(result).toEqual(mockCompletedResponse.data);
      expect(handleCompletionSpy).toHaveBeenCalledWith(mockCompletedResponse.data);
    });
    
    it('should handle "crisis_detected" status', async () => {
      const mockCrisisResponse: OnboardingResponse = {
        status: 'crisis_detected',
        data: {
          level: 'high',
          message: 'Seek help',
          actions: [{id: 'call', text: 'Call Help'}],
        },
      };
       mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
       mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockCrisisResponse),
      });
      const result = await onboardingService.submitResponse(mockSubmitRequest);
      expect(result).toEqual(mockCrisisResponse.data);
    });


    it('should throw an error if API call fails', async () => {
      mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
      mockFetch.mockResolvedValueOnce({ ok: false, status: 500, json: jest.fn().mockResolvedValueOnce({message: 'Submit error'}) });
      await expect(onboardingService.submitResponse(mockSubmitRequest)).rejects.toThrow('Submit error');
    });
  });
  
  describe('handleOnboardingCompletion (private method through public callers)', () => {
    it('should store profile, pathway, config, nextSteps, and mark as complete in AsyncStorage', async () => {
      const completionData = {
        profile: { id: 'user1', name: 'Test User' },
        recommendedPathway: 'test_pathway',
        featureConfiguration: { darkMode: true },
        nextSteps: ['step1', 'step2'],
      };
      // This method is private, so we test its effects via a public method that calls it, e.g., submitResponse when status is 'completed'
      const mockCompletedResponse: OnboardingResponse = { status: 'completed', data: completionData };
      mockAsyncStorageGetItem.mockResolvedValue('fake-auth-token'); // For auth header
      mockFetch.mockResolvedValue({ ok: true, json: jest.fn().mockResolvedValue(mockCompletedResponse) });

      await onboardingService.submitResponse({ sessionId: 's1', stepId: 'q_final', response: {} });

      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('userProfile', JSON.stringify(completionData.profile));
      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('user_pathway', completionData.recommendedPathway);
      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('feature_config', JSON.stringify(completionData.featureConfiguration));
      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('onboarding_next_steps', JSON.stringify(completionData.nextSteps));
      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('onboarding_completed', 'true');
      expect(mockAsyncStorageSetItem).toHaveBeenCalledWith('onboarding_completed_at', expect.any(String));
      expect(mockAsyncStorageRemoveItem).toHaveBeenCalledWith('onboarding_session_id');
      expect(mockAsyncStorageRemoveItem).toHaveBeenCalledWith('onboarding_start_time');
    });
  });

  describe('getOnboardingStatus', () => {
    it('should fetch onboarding status for a given session ID', async () => {
        mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
        const mockStatusResponse: OnboardingResponse = { status: 'continue', data: { currentStep: 'q3', progress: 75 } };
        mockFetch.mockResolvedValueOnce({ ok: true, json: jest.fn().mockResolvedValueOnce(mockStatusResponse) });
        
        const result = await onboardingService.getOnboardingStatus('session123');
        expect(mockFetch).toHaveBeenCalledWith(mockApiUrl('/session-status/session123'), expect.any(Object));
        expect(result).toEqual(mockStatusResponse.data);
    });

     it('should use currentSessionId if no sessionId provided', async () => {
        onboardingService['currentSessionId'] = 'currentSession456';
        mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
        mockFetch.mockResolvedValueOnce({ ok: true, json: jest.fn().mockResolvedValueOnce({}) });
        await onboardingService.getOnboardingStatus();
        expect(mockFetch).toHaveBeenCalledWith(mockApiUrl('/session-status/currentSession456'), expect.any(Object));
    });
    
    it('should use AsyncStorage session ID if no other ID available', async () => {
        mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token'); // Auth
        mockAsyncStorageGetItem.mockResolvedValueOnce('storedSession789'); // onboarding_session_id
        mockFetch.mockResolvedValueOnce({ ok: true, json: jest.fn().mockResolvedValueOnce({}) });
        await onboardingService.getOnboardingStatus();
        expect(mockFetch).toHaveBeenCalledWith(mockApiUrl('/session-status/storedSession789'), expect.any(Object));
    });

    it('should throw error if no session ID is available', async () => {
        mockAsyncStorageGetItem.mockResolvedValue(null); // No token, no stored session
        await expect(onboardingService.getOnboardingStatus()).rejects.toThrow('No active onboarding session');
    });
  });

  describe('resumeOnboarding', () => {
     it('should resume an onboarding session successfully', async () => {
        onboardingService['currentSessionId'] = 'sessionToResume';
        mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
        const mockResumeResponse = { status: 'continue', data: { question: {id: 'q5', title: 'Resumed Q'}}};
        mockFetch.mockResolvedValueOnce({ ok: true, json: jest.fn().mockResolvedValueOnce(mockResumeResponse) });
        
        const result = await onboardingService.resumeOnboarding();
        expect(mockFetch).toHaveBeenCalledWith(mockApiUrl('/resume'), expect.objectContaining({
            body: JSON.stringify({sessionId: 'sessionToResume'})
        }));
        expect(result).toEqual(mockResumeResponse.data);
        expect(onboardingService.getCurrentSessionId()).toBe('sessionToResume');
     });
  });

  describe('skipOnboarding', () => {
    it('should call /skip endpoint and handle completion', async () => {
        mockAsyncStorageGetItem.mockResolvedValueOnce('fake-auth-token');
        const mockSkipResponse: OnboardingResponse = { status: 'success', data: { profile: { id: 'skippedUser'}, recommendedPathway: 'gentle'}};
        mockFetch.mockResolvedValueOnce({ ok: true, json: jest.fn().mockResolvedValueOnce(mockSkipResponse) });
        const handleCompletionSpy = jest.spyOn(onboardingService as any, 'handleOnboardingCompletion');

        const result = await onboardingService.skipOnboarding('crisis');
        expect(mockFetch).toHaveBeenCalledWith(mockApiUrl('/skip'), expect.objectContaining({
            body: JSON.stringify({reason: 'crisis'})
        }));
        expect(result).toEqual(mockSkipResponse.data);
        expect(handleCompletionSpy).toHaveBeenCalledWith(mockSkipResponse.data);
    });
  });
  
  describe('hasCompletedOnboarding', () => {
    it('should return true if profile status is complete', async () => {
        mockAsyncStorageGetItem.mockResolvedValueOnce(JSON.stringify({ completionStatus: 'complete' }));
        expect(await onboardingService.hasCompletedOnboarding()).toBe(true);
    });
    it('should return false if profile status is not complete or profile missing', async () => {
        mockAsyncStorageGetItem.mockResolvedValueOnce(JSON.stringify({ completionStatus: 'incomplete' }));
        expect(await onboardingService.hasCompletedOnboarding()).toBe(false);
        mockAsyncStorageGetItem.mockResolvedValueOnce(null);
        expect(await onboardingService.hasCompletedOnboarding()).toBe(false);
    });
  });

  describe('clearSession', () => {
    it('should remove session items from AsyncStorage and reset internal state', async () => {
        onboardingService['currentSessionId'] = 'test';
        onboardingService['sessionStartTime'] = 123;
        await onboardingService.clearSession();
        expect(mockAsyncStorageRemoveItem).toHaveBeenCalledWith('onboarding_session_id');
        expect(mockAsyncStorageRemoveItem).toHaveBeenCalledWith('onboarding_start_time');
        expect(onboardingService.getCurrentSessionId()).toBeNull();
        expect(onboardingService['sessionStartTime']).toBe(0);
    });
  });
});
