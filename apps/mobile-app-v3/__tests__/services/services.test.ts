/**
 * Service Layer Tests
 * Tests backend integration, API calls, and data persistence
 */

import { jest } from '@jest/globals';
import '../setup';

// Import services
import { onboardingService } from '../../src/services/onboarding.service';
import { assessmentService } from '../../src/services/assessment.service';
// import { JourneyService } from '../../src/services/api/JourneyService';
import { authService } from '../../src/services/auth.service';
import { storageService } from '../../src/services/storage.service';

// Import test utilities
import { mockUserProfile, mockSymptoms, mockJourney, waitFor } from '../setup';

// Mock AsyncStorage
import AsyncStorage from '@react-native-async-storage/async-storage';

describe('Service Layer Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Onboarding Service', () => {
    it('should create user profile with proper validation', async () => {
      const profileData = {
        name: 'Test User',
        profession: 'Healthcare Professional',
        islamicBackground: 'practicing',
        mentalHealthExperience: 'some',
        preferredLanguage: 'en',
      };

      // Mock API response
      (global.fetch as jest.Mock) = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ success: true, user: mockUserProfile }),
      });

      const result = await onboardingService.createUserProfile(profileData);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/onboarding/profile'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(profileData),
        })
      );

      expect(result).toEqual({
        success: true,
        user: mockUserProfile,
      });
    });

    it('should detect crisis keywords accurately', async () => {
      const crisisTexts = [
        'I want to harm myself',
        'feeling hopeless and suicidal',
        'no point in living anymore',
        'want to end my life',
      ];

      const safeTexts = [
        'feeling a bit sad today',
        'having some difficulties',
        'need support with anxiety',
      ];

      global.fetch = jest.fn();

      // Test crisis detection
      for (const text of crisisTexts) {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: async () => ({ crisisDetected: true, severity: 'high' }),
        });

        const result = await onboardingService.detectCrisis(text);
        expect(result).toBe(true);
      }

      // Test safe text
      for (const text of safeTexts) {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: async () => ({ crisisDetected: false, severity: 'low' }),
        });

        const result = await onboardingService.detectCrisis(text);
        expect(result).toBe(false);
      }
    });

    it('should determine appropriate pathways', async () => {
      const pathwayTests = [
        {
          profile: { islamicBackground: 'practicing' },
          expected: 'advanced_practices',
        },
        {
          profile: { islamicBackground: 'new_muslim' },
          expected: 'gentle_introduction',
        },
        {
          profile: { islamicBackground: 'cultural' },
          expected: 'cultural_bridge',
        },
        {
          profile: { islamicBackground: 'other' },
          expected: 'general_wellness',
        },
      ];

      global.fetch = jest.fn();

      for (const test of pathwayTests) {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: async () => ({ pathway: test.expected }),
        });

        const result = await onboardingService.determinePathway(test.profile);
        expect(result).toBe(test.expected);
      }
    });

    it('should handle API errors gracefully', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      await expect(onboardingService.createUserProfile({})).rejects.toThrow(
        'Network error'
      );
    });

    it('should save progress locally', async () => {
      const progress = {
        currentStep: 3,
        answers: { profession: 'Student' },
        timestamp: new Date().toISOString(),
      };

      await onboardingService.saveProgress(progress);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'onboarding_progress',
        JSON.stringify(progress)
      );
    });
  });

  describe('Assessment Service', () => {
    it('should categorize symptoms by Islamic soul layers', async () => {
      const symptomTests = [
        { symptom: 'physical pain', expectedLayer: 'Jism' },
        { symptom: 'emotional sadness', expectedLayer: 'Nafs' },
        { symptom: 'mental concentration issues', expectedLayer: 'Aql' },
        { symptom: 'spiritual prayer difficulties', expectedLayer: 'Qalb' },
        { symptom: 'transcendent divine connection', expectedLayer: 'Ruh' },
      ];

      global.fetch = jest.fn();

      for (const test of symptomTests) {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: async () => ({ layer: test.expectedLayer }),
        });

        const result = await assessmentService.categorizeSymptom(test.symptom);
        expect(result).toBe(test.expectedLayer);
      }
    });

    it('should generate comprehensive 5-layer analysis', async () => {
      const selectedSymptoms = ['anxiety', 'spiritual_emptiness', 'fatigue'];

      const expectedAnalysis = {
        layers: {
          Jism: {
            symptoms: ['fatigue'],
            recommendations: ['rest', 'exercise'],
          },
          Nafs: {
            symptoms: ['anxiety'],
            recommendations: ['dhikr', 'breathing'],
          },
          Aql: { symptoms: [], recommendations: [] },
          Qalb: {
            symptoms: ['spiritual_emptiness'],
            recommendations: ['prayer', 'Quran'],
          },
          Ruh: { symptoms: [], recommendations: [] },
        },
        overallAssessment:
          'Moderate challenges across physical and spiritual layers',
        islamicGuidance:
          'Focus on strengthening your connection with Allah through prayer and remembrance',
        recommendations: ['spiritual_renewal_journey', 'stress_management'],
      };

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => expectedAnalysis,
      });

      const result = await assessmentService.generateAnalysis(selectedSymptoms);

      expect(result.layers).toBeDefined();
      expect(Object.keys(result.layers)).toHaveLength(5);
      expect(result.islamicGuidance).toContain('Allah');
    });

    it('should adapt analysis language based on user persona', async () => {
      const professionalUser = {
        ...mockUserProfile,
        profession: 'Healthcare Professional',
      };
      const traditionalUser = {
        ...mockUserProfile,
        islamicBackground: 'traditional',
      };

      global.fetch = jest.fn();

      // Professional analysis
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          language: 'clinical_islamic',
          analysis: 'Evidence-based Islamic therapeutic approach recommended',
        }),
      });

      let result = await assessmentService.generatePersonalizedAnalysis(
        ['anxiety'],
        professionalUser
      );
      expect(result.language).toBe('clinical_islamic');

      // Traditional analysis
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          language: 'gentle_traditional',
          analysis:
            'May Allah grant you ease and healing through traditional practices',
        }),
      });

      result = await assessmentService.generatePersonalizedAnalysis(
        ['anxiety'],
        traditionalUser
      );
      expect(result.language).toBe('gentle_traditional');
    });

    it('should handle crisis detection during assessment', async () => {
      const crisisSymptoms = ['thoughts of self-harm', 'feeling hopeless'];

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ crisisDetected: true, severity: 'high' }),
      });

      const result = await assessmentService.detectCrisisDuringAssessment(
        crisisSymptoms
      );
      expect(result).toBe(true);
    });

    it('should preserve assessment data during crisis', async () => {
      const assessmentData = {
        selectedSymptoms: ['anxiety', 'depression'],
        reflections: ['feeling overwhelmed'],
        progress: 60,
        timestamp: new Date().toISOString(),
      };

      await assessmentService.preserveAssessmentData(assessmentData);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'assessment_crisis_backup',
        JSON.stringify(assessmentData)
      );
    });
  });

  describe('Journey Service', () => {
    it('should generate personalized journey based on assessment', async () => {
      const assessmentResults = {
        layers: {
          Nafs: { symptoms: ['anxiety'], severity: 'moderate' },
          Qalb: { symptoms: ['spiritual_emptiness'], severity: 'mild' },
        },
        userProfile: mockUserProfile,
      };

      const expectedJourney = {
        ...mockJourney,
        title: 'Anxiety Relief through Islamic Practices',
        practices: [
          { type: 'dhikr', content: 'Calming dhikr', duration: 10 },
          { type: 'dua', content: "Du'a for anxiety", duration: 5 },
        ],
      };

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => expectedJourney,
      });

      const result = await journeyService.generatePersonalizedJourney(
        assessmentResults
      );

      expect(result.title).toContain('Anxiety Relief');
      expect(result.practices).toHaveLength(2);
    });

    it('should track progress with mood and spiritual metrics', async () => {
      const progressData = {
        journeyId: 'journey1',
        date: '2024-12-01',
        mood: 7,
        spiritualConnection: 8,
        practicesCompleted: 3,
        totalPractices: 4,
        reflectionNotes: 'Feeling more connected today',
      };

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ success: true }),
      });

      const result = await journeyService.trackProgress(progressData);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/journey/progress'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(progressData),
        })
      );

      expect(result).toBe(true);
    });

    it('should find appropriate peer support groups', async () => {
      const userCriteria = {
        age: 25,
        gender: 'female',
        islamicBackground: 'practicing',
        challenges: ['anxiety'],
      };

      const expectedGroup = {
        id: 'group1',
        name: 'Sisters Anxiety Support',
        members: 8,
        culturalAlignment: 'practicing_muslim',
      };

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => expectedGroup,
      });

      const result = await journeyService.findPeerSupportGroup(userCriteria);

      expect(result.name).toBe('Sisters Anxiety Support');
      expect(result.culturalAlignment).toBe('practicing_muslim');
    });

    it('should handle journey context preservation during crisis', async () => {
      const journeyContext = {
        currentJourney: mockJourney,
        currentDay: 3,
        todaysPractices: ['dhikr', 'reflection'],
        progress: { mood: 4, spiritualConnection: 3 },
      };

      await journeyService.preserveJourneyContext(journeyContext);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'journey_crisis_context',
        JSON.stringify(journeyContext)
      );
    });
  });

  describe('Authentication Service', () => {
    it('should handle user authentication', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({
          token: 'jwt_token_123',
          user: mockUserProfile,
        }),
      });

      const result = await authService.login(credentials);

      expect(result.token).toBe('jwt_token_123');
      expect(result.user).toEqual(mockUserProfile);
    });

    it('should store authentication token securely', async () => {
      const token = 'jwt_token_123';

      await authService.storeToken(token);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith('auth_token', token);
    });

    it('should handle token refresh', async () => {
      const refreshToken = 'refresh_token_123';

      // Mock AsyncStorage to return the refresh token
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(refreshToken);

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({
          token: 'new_jwt_token_456',
          refreshToken: 'new_refresh_token_456',
        }),
      });

      const result = await authService.refreshToken();

      expect(result.token).toBe('new_jwt_token_456');
    });
  });

  describe('Storage Service', () => {
    it('should store and retrieve user data', async () => {
      const userData = { ...mockUserProfile };

      await storageService.storeUserData(userData);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'qalb_healing_user_data',
        JSON.stringify(userData)
      );

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
        JSON.stringify(userData)
      );

      const retrievedData = await storageService.getUserData();
      expect(retrievedData).toEqual(userData);
    });

    it('should handle offline data storage', async () => {
      const offlineData = {
        assessmentProgress: { currentStep: 2 },
        journeyProgress: { currentDay: 3 },
        timestamp: new Date().toISOString(),
      };

      await storageService.storeOfflineData('assessment', offlineData);

      // The actual implementation stores with a specific structure
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        expect.stringMatching(/^qalb_healing_offline_assessment_\d+$/),
        expect.stringContaining('"type":"assessment"')
      );
    });

    it('should sync offline data when connection restored', async () => {
      const offlineData = {
        assessmentProgress: { currentStep: 2 },
        journeyProgress: { currentDay: 3 },
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
        JSON.stringify(offlineData)
      );

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ success: true }),
      });

      const result = await storageService.syncOfflineData();

      expect(result).toBe(true);
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(
        'qalb_healing_offline_data'
      );
    });

    it('should clear all data on logout', async () => {
      await storageService.clearAllData();

      expect(AsyncStorage.clear).toHaveBeenCalled();
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle network timeouts', async () => {
      global.fetch = jest
        .fn()
        .mockImplementation(
          () =>
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Request timeout')), 100)
            )
        );

      await expect(onboardingService.createUserProfile({})).rejects.toThrow(
        'Request timeout'
      );
    });

    it('should retry failed requests', async () => {
      let callCount = 0;
      global.fetch = jest.fn().mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          return Promise.reject(new Error('Network error'));
        }
        return Promise.resolve({
          ok: true,
          json: async () => ({ success: true }),
        });
      });

      // Should succeed after retries
      const result = await onboardingService.createUserProfileWithRetry({});
      expect(result.success).toBe(true);
      expect(callCount).toBe(3);
    });

    it('should handle malformed API responses', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      });

      await expect(assessmentService.generateAnalysis([])).rejects.toThrow(
        'Invalid JSON'
      );
    });
  });
});
