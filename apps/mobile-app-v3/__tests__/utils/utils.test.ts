/**
 * Utility Functions Unit Tests
 * Tests helper functions and utilities
 */

import {
  validateArabicText,
  validateQuranReference,
  validateIslamicContent,
} from '../setup';

// Mock utility functions for testing
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

const calculateProgress = (completed: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
};

const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} min`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const categorizeSymptomByLayer = (symptom: string): string => {
  const layerKeywords = {
    Jism: ['headache', 'fatigue', 'pain', 'tired', 'physical', 'body'],
    Nafs: ['angry', 'anxious', 'sad', 'emotional', 'mood'],
    Aql: [
      'concentration',
      'memory',
      'thinking',
      'focus',
      'mental',
      'cognitive',
    ],
    Qalb: [
      'spiritual',
      'prayer',
      'Allah',
      'faith',
      'heart',
      'soul',
      'feeling distant',
      'difficulties',
    ],
    Ruh: ['purpose', 'meaning', 'divine', 'connection', 'transcendent'],
  };

  const lowerSymptom = symptom.toLowerCase();

  for (const [layer, keywords] of Object.entries(layerKeywords)) {
    if (keywords.some((keyword) => lowerSymptom.includes(keyword))) {
      return layer;
    }
  }

  return 'Nafs'; // Default to emotional layer
};

const formatArabicText = (text: string): string => {
  // Add proper RTL markers and formatting
  return `\u202E${text}\u202C`;
};

const getIslamicGreeting = (
  timeOfDay: 'morning' | 'afternoon' | 'evening'
): string => {
  const greetings = {
    morning: 'صباح الخير - Good morning',
    afternoon: 'مساء الخير - Good afternoon',
    evening: 'مساء الخير - Good evening',
  };
  return greetings[timeOfDay];
};

describe('Utility Functions Unit Tests', () => {
  describe('Date and Time Utilities', () => {
    it('should format date correctly', () => {
      const testDate = new Date('2024-12-01T10:30:00Z');
      const formatted = formatDate(testDate);

      expect(formatted).toContain('December');
      expect(formatted).toContain('1');
      expect(formatted).toContain('2024');
    });

    it('should format time correctly', () => {
      const testDate = new Date('2024-12-01T10:30:00Z');
      const formatted = formatTime(testDate);

      expect(formatted).toMatch(/\d{1,2}:\d{2}/);
    });

    it('should handle different time zones', () => {
      const testDate = new Date('2024-12-01T10:30:00Z');
      const formatted = formatTime(testDate);

      expect(typeof formatted).toBe('string');
      expect(formatted.length).toBeGreaterThan(0);
    });
  });

  describe('Progress Calculations', () => {
    it('should calculate progress percentage correctly', () => {
      expect(calculateProgress(3, 10)).toBe(30);
      expect(calculateProgress(7, 14)).toBe(50);
      expect(calculateProgress(10, 10)).toBe(100);
    });

    it('should handle edge cases', () => {
      expect(calculateProgress(0, 10)).toBe(0);
      expect(calculateProgress(5, 0)).toBe(0);
      expect(calculateProgress(0, 0)).toBe(0);
    });

    it('should round to nearest integer', () => {
      expect(calculateProgress(1, 3)).toBe(33); // 33.33... rounded
      expect(calculateProgress(2, 3)).toBe(67); // 66.66... rounded
    });
  });

  describe('Duration Formatting', () => {
    it('should format minutes correctly', () => {
      expect(formatDuration(30)).toBe('30 min');
      expect(formatDuration(45)).toBe('45 min');
      expect(formatDuration(59)).toBe('59 min');
    });

    it('should format hours correctly', () => {
      expect(formatDuration(60)).toBe('1h');
      expect(formatDuration(120)).toBe('2h');
      expect(formatDuration(90)).toBe('1h 30m');
      expect(formatDuration(135)).toBe('2h 15m');
    });

    it('should handle zero and negative values', () => {
      expect(formatDuration(0)).toBe('0 min');
      expect(formatDuration(-10)).toBe('-10 min');
    });
  });

  describe('ID Generation', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();

      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });

    it('should generate IDs of consistent length', () => {
      const ids = Array.from({ length: 10 }, () => generateId());

      ids.forEach((id) => {
        expect(id.length).toBe(9);
        expect(/^[a-z0-9]+$/.test(id)).toBe(true);
      });
    });
  });

  describe('Input Sanitization', () => {
    it('should remove dangerous characters', () => {
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe(
        'scriptalert("xss")/script'
      );
      expect(sanitizeInput('Hello <world>')).toBe('Hello world');
      expect(sanitizeInput('Normal text')).toBe('Normal text');
    });

    it('should trim whitespace', () => {
      expect(sanitizeInput('  hello world  ')).toBe('hello world');
      expect(sanitizeInput('\n\ttest\n\t')).toBe('test');
    });

    it('should handle empty and null inputs', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput('   ')).toBe('');
    });
  });

  describe('Email Validation', () => {
    it('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach((email) => {
        expect(isValidEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        'user@domain',
        'user <EMAIL>',
        '',
      ];

      invalidEmails.forEach((email) => {
        expect(isValidEmail(email)).toBe(false);
      });
    });
  });

  describe('Islamic Soul Layer Categorization', () => {
    it('should categorize physical symptoms to Jism', () => {
      const physicalSymptoms = [
        'headache',
        'feeling tired',
        'body pain',
        'physical fatigue',
      ];

      physicalSymptoms.forEach((symptom) => {
        expect(categorizeSymptomByLayer(symptom)).toBe('Jism');
      });
    });

    it('should categorize emotional symptoms to Nafs', () => {
      const emotionalSymptoms = [
        'feeling anxious',
        'angry mood',
        'sad emotions',
        'emotional distress',
      ];

      emotionalSymptoms.forEach((symptom) => {
        expect(categorizeSymptomByLayer(symptom)).toBe('Nafs');
      });
    });

    it('should categorize mental symptoms to Aql', () => {
      const mentalSymptoms = [
        'concentration problems',
        'memory issues',
        'difficulty thinking',
        'mental fog',
      ];

      mentalSymptoms.forEach((symptom) => {
        expect(categorizeSymptomByLayer(symptom)).toBe('Aql');
      });
    });

    it('should categorize spiritual symptoms to Qalb', () => {
      const spiritualSymptoms = [
        'feeling distant from Allah',
        'prayer difficulties',
        'spiritual emptiness',
        'heart problems',
      ];

      spiritualSymptoms.forEach((symptom) => {
        expect(categorizeSymptomByLayer(symptom)).toBe('Qalb');
      });
    });

    it('should categorize transcendent symptoms to Ruh', () => {
      const transcendentSymptoms = [
        'loss of purpose',
        'meaninglessness',
        'divine disconnection',
        'transcendent emptiness',
      ];

      transcendentSymptoms.forEach((symptom) => {
        expect(categorizeSymptomByLayer(symptom)).toBe('Ruh');
      });
    });

    it('should default to Nafs for unknown symptoms', () => {
      const unknownSymptoms = [
        'random symptom',
        'unknown issue',
        'undefined problem',
      ];

      unknownSymptoms.forEach((symptom) => {
        expect(categorizeSymptomByLayer(symptom)).toBe('Nafs');
      });
    });
  });

  describe('Arabic Text Formatting', () => {
    it('should format Arabic text with RTL markers', () => {
      const arabicText = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
      const formatted = formatArabicText(arabicText);

      expect(formatted).toContain(arabicText);
      expect(formatted).toMatch(/\u202E.*\u202C/); // RTL markers
    });

    it('should handle empty Arabic text', () => {
      const formatted = formatArabicText('');
      expect(formatted).toBe('\u202E\u202C');
    });
  });

  describe('Islamic Greetings', () => {
    it('should return appropriate greetings for different times', () => {
      const morningGreeting = getIslamicGreeting('morning');
      const afternoonGreeting = getIslamicGreeting('afternoon');
      const eveningGreeting = getIslamicGreeting('evening');

      expect(morningGreeting).toContain('صباح الخير');
      expect(morningGreeting).toContain('Good morning');

      expect(afternoonGreeting).toContain('مساء الخير');
      expect(afternoonGreeting).toContain('Good afternoon');

      expect(eveningGreeting).toContain('مساء الخير');
      expect(eveningGreeting).toContain('Good evening');
    });

    it('should include both Arabic and English', () => {
      const greeting = getIslamicGreeting('morning');

      // Should contain Arabic characters
      expect(/[\u0600-\u06FF]/.test(greeting)).toBe(true);

      // Should contain English text
      expect(/[a-zA-Z]/.test(greeting)).toBe(true);
    });
  });

  describe('Islamic Content Validation', () => {
    it('should validate Arabic text correctly', () => {
      const validArabic = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
      const invalidArabic = 'Hello World';

      expect(validateArabicText(validArabic)).toBe(true);
      expect(validateArabicText(invalidArabic)).toBe(false);
    });

    it('should validate Quran references correctly', () => {
      const validReferences = ['Quran 2:255', 'Quran 65:2', 'Quran 113:1'];
      const invalidReferences = ['Quran 2', 'Verse 2:255', 'Quran 2:'];

      validReferences.forEach((ref) => {
        expect(validateQuranReference(ref)).toBe(true);
      });

      invalidReferences.forEach((ref) => {
        expect(validateQuranReference(ref)).toBe(false);
      });
    });

    it('should validate complete Islamic content', () => {
      const validContent = {
        arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
        reference: 'Quran 65:2',
        translation: 'And whoever fears Allah - He will make for him a way out',
      };

      const invalidContent = {
        arabic: 'Invalid Arabic',
        reference: 'Invalid Reference',
      };

      expect(validateIslamicContent(validContent)).toBe(true);
      expect(validateIslamicContent(invalidContent)).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle null and undefined inputs', () => {
      expect(() => formatDate(null as any)).toThrow();
      expect(() => calculateProgress(null as any, 10)).not.toThrow();
      expect(() => sanitizeInput(null as any)).toThrow();
    });

    it('should handle edge cases gracefully', () => {
      expect(calculateProgress(Infinity, 10)).toBe(Infinity);
      expect(formatDuration(-1)).toBe('-1 min');
      expect(isValidEmail(null as any)).toBe(false);
    });
  });
});
