import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import { OnboardingQuestion } from '../../src/components/OnboardingQuestion'; // Adjust path as necessary
import { Ionicons } from '@expo/vector-icons'; // Ensure this is mocked or handled if it causes issues

// Mock Ionicons if they are not critical for the logic test or cause rendering issues in Jest
jest.mock('@expo/vector-icons', () => {
  const { View } = require('react-native');
  return {
    Ionicons: (props: any) => <View testID="mock-icon" {...props} />, // Simple mock
  };
});

describe('OnboardingQuestion Component', () => {
  const mockOnResponse = jest.fn();
  const baseQuestionProps = {
    onResponse: mockOnResponse,
    isLoading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Single Choice Question', () => {
    const singleChoiceQuestion = {
      id: 'q_single',
      type: 'single_choice' as const,
      title: 'What is your favorite color?',
      options: [
        { id: 'red', text: 'Red' },
        { id: 'blue', text: 'Blue' },
        { id: 'green', text: 'Green', description: 'The color of nature' },
      ],
    };

    it('renders title and options correctly', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={singleChoiceQuestion} />);
      expect(screen.getByText(singleChoiceQuestion.title)).toBeTruthy();
      singleChoiceQuestion.options.forEach(opt => {
        expect(screen.getByText(opt.text)).toBeTruthy();
        if (opt.description) {
          expect(screen.getByText(opt.description)).toBeTruthy();
        }
      });
    });

    it('calls onResponse with correct data when an option is pressed', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={singleChoiceQuestion} />);
      fireEvent.press(screen.getByText('Blue'));
      expect(mockOnResponse).toHaveBeenCalledWith({
        [singleChoiceQuestion.id]: 'blue',
        questionType: 'single_choice',
      });
    });

    it('disables option buttons when isLoading is true', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={singleChoiceQuestion} isLoading={true} />);
      const optionButton = screen.getByText('Red'); // Get one of the option buttons
      // Check TouchableOpacity disabled prop (more direct if possible, or check behavior)
      // For now, we assume pressing it won't call onResponse if disabled
      fireEvent.press(optionButton);
      expect(mockOnResponse).not.toHaveBeenCalled();
    });
  });

  describe('Multiple Choice Question', () => {
    const multipleChoiceQuestion = {
      id: 'q_multi',
      type: 'multiple_choice' as const,
      title: 'Which fruits do you like?',
      options: [
        { id: 'apple', text: 'Apple' },
        { id: 'banana', text: 'Banana' },
        { id: 'orange', text: 'Orange' },
      ],
    };

    it('renders title and options with checkboxes', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={multipleChoiceQuestion} />);
      expect(screen.getByText(multipleChoiceQuestion.title)).toBeTruthy();
      multipleChoiceQuestion.options.forEach(opt => {
        expect(screen.getByText(opt.text)).toBeTruthy();
      });
      // Check for checkbox visual cues if possible/needed, or rely on selection logic
    });

    it('allows selecting and deselecting multiple options', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={multipleChoiceQuestion} />);
      fireEvent.press(screen.getByText('Apple'));
      fireEvent.press(screen.getByText('Orange'));
      // Check if 'Continue' button becomes active
      expect(screen.getByText('Continue')).toBeTruthy();

      fireEvent.press(screen.getByText('Apple')); // Deselect
      // 'Continue' should still be there if Orange is selected
      expect(screen.getByText('Continue')).toBeTruthy(); 
    });

    it('calls onResponse with selected options when "Continue" is pressed', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={multipleChoiceQuestion} />);
      fireEvent.press(screen.getByText('Apple'));
      fireEvent.press(screen.getByText('Orange'));
      fireEvent.press(screen.getByText('Continue'));

      expect(mockOnResponse).toHaveBeenCalledWith({
        [multipleChoiceQuestion.id]: ['apple', 'orange'],
        questionType: 'multiple_choice',
      });
    });
    
    it('does not call onResponse if no option selected and continue pressed', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={multipleChoiceQuestion} />);
      // "Continue" button might not be rendered or should be disabled.
      // If it's rendered and active by default, this test needs adjustment.
      // Assuming it's only active/visible when options are selected.
      const continueButton = screen.queryByText('Continue');
      if (continueButton) {
         fireEvent.press(continueButton);
         expect(mockOnResponse).not.toHaveBeenCalled();
      } else {
          expect(true).toBe(true); // Pass if button isn't there as expected
      }
    });
  });

  describe('Text Input Question', () => {
    const textInputQuestion = {
      id: 'q_text',
      type: 'text_input' as const,
      title: 'Any other comments?',
    };

    it('renders title and text input field', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={textInputQuestion} />);
      expect(screen.getByText(textInputQuestion.title)).toBeTruthy();
      expect(screen.getByPlaceholderText('Share your thoughts...')).toBeTruthy();
    });

    it('updates text input value and calls onResponse on continue', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={textInputQuestion} />);
      fireEvent.changeText(screen.getByPlaceholderText('Share your thoughts...'), 'This is a test comment.');
      fireEvent.press(screen.getByText('Continue'));

      expect(mockOnResponse).toHaveBeenCalledWith({
        [textInputQuestion.id]: 'This is a test comment.',
        questionType: 'text_input',
      });
    });
    
    it('does not call onResponse if text input is empty/whitespace and continue pressed', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={textInputQuestion} />);
      fireEvent.changeText(screen.getByPlaceholderText('Share your thoughts...'), '   ');
      // "Continue" button might not be rendered or should be disabled.
      const continueButton = screen.queryByText('Continue');
      if (continueButton) {
         fireEvent.press(continueButton);
         expect(mockOnResponse).not.toHaveBeenCalled();
      } else {
         expect(true).toBe(true);
      }
    });
  });

  describe('Multi-Section Question', () => {
    const multiSectionQuestion = {
      id: 'q_multi_section',
      type: 'multi_section' as const,
      title: 'Tell us more about yourself',
      sections: [
        { id: 'ms_gender', question: 'Your Gender?', type: 'single_choice', options: [{ id: 'male', text: 'Male' }, { id: 'female', text: 'Female' }] },
        { id: 'ms_age', question: 'Your Age Range?', type: 'single_choice', options: [{ id: 'u18', text: 'Under 18' }, { id: '18-25', text: '18-25' }] },
      ],
    };

    it('renders the first section initially', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={multiSectionQuestion} />);
      expect(screen.getByText(multiSectionQuestion.sections[0].question)).toBeTruthy();
      expect(screen.getByText('Male')).toBeTruthy();
      expect(screen.getByText('1 of 2')).toBeTruthy(); // Progress
    });

    it('navigates to the next section when an option is chosen', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={multiSectionQuestion} />);
      fireEvent.press(screen.getByText('Female')); // Answer first section
      expect(screen.getByText(multiSectionQuestion.sections[1].question)).toBeTruthy(); // Now shows second section
      expect(screen.getByText('Under 18')).toBeTruthy();
      expect(screen.getByText('2 of 2')).toBeTruthy(); // Progress updated
      expect(mockOnResponse).not.toHaveBeenCalled(); // Not called until all sections are done
    });

    it('calls onResponse with all section responses when the last section is answered', () => {
      render(<OnboardingQuestion {...baseQuestionProps} question={multiSectionQuestion} />);
      fireEvent.press(screen.getByText('Female')); // Section 1
      fireEvent.press(screen.getByText('18-25'));    // Section 2 (last)

      expect(mockOnResponse).toHaveBeenCalledWith({
        [multiSectionQuestion.id]: {
          'ms_gender': 'female',
          'ms_age': '18-25',
        },
        questionType: 'multi_section',
      });
    });
    
    it('resets section progress when question.id changes', () => {
      const { rerender } = render(<OnboardingQuestion {...baseQuestionProps} question={multiSectionQuestion} />);
      fireEvent.press(screen.getByText('Female')); // Answer first section, currentSection becomes 1
      expect(screen.getByText(multiSectionQuestion.sections[1].question)).toBeTruthy();
      
      const newQuestion = { ...multiSectionQuestion, id: 'q_multi_section_new' };
      rerender(<OnboardingQuestion {...baseQuestionProps} question={newQuestion} />);
      
      // Should show the first section of the new question
      expect(screen.getByText(newQuestion.sections[0].question)).toBeTruthy();
      expect(screen.getByText('1 of 2')).toBeTruthy();
    });
  });
  
  it('resets local state (selectedOptions, textInput, section state) when question.id changes', () => {
    // Test for multiple choice reset
    const multiChoice = { id: 'mc1', type: 'multiple_choice' as const, title: 'MC1', options: [{id:'a', text:'A'}]};
    const { rerender } = render(<OnboardingQuestion {...baseQuestionProps} question={multiChoice} />);
    fireEvent.press(screen.getByText('A')); // Select an option
    expect(screen.getByText('Continue')).toBeTruthy(); // Continue button appears

    const singleChoice = { id: 'sc1', type: 'single_choice' as const, title: 'SC1', options: [{id:'b', text:'B'}]};
    rerender(<OnboardingQuestion {...baseQuestionProps} question={singleChoice} />);
    // If state didn't reset, the "Continue" button from multiple choice might still be there or affect logic.
    // Check that it's now rendering the single choice correctly without interference.
    expect(screen.getByText('SC1')).toBeTruthy();
    expect(screen.queryByText('Continue')).toBeNull(); // No continue button for single choice by default

    // Test for text input reset
    const textInput = { id: 'ti1', type: 'text_input' as const, title: 'TI1'};
    rerender(<OnboardingQuestion {...baseQuestionProps} question={textInput} />);
    fireEvent.changeText(screen.getByPlaceholderText('Share your thoughts...'), 'some text');
    expect(screen.getByText('Continue')).toBeTruthy();

    rerender(<OnboardingQuestion {...baseQuestionProps} question={singleChoice} />); // Switch back to single choice
    expect(screen.queryByText('Continue')).toBeNull();
    
     // Test for multi-section reset (already covered by a specific multi-section test, but good to be explicit)
    const multiSection = { id: 'ms1', type: 'multi_section' as const, title: 'MS1', sections: [{id:'s1', question:'S1Q', type:'single_choice', options:[{id:'o1',text:'O1'}]}]};
    rerender(<OnboardingQuestion {...baseQuestionProps} question={multiSection} />);
    expect(screen.getByText('S1Q')).toBeTruthy();
    expect(screen.getByText('1 of 1')).toBeTruthy();

    rerender(<OnboardingQuestion {...baseQuestionProps} question={singleChoice} />);
    expect(screen.getByText('SC1')).toBeTruthy();

  });
});
