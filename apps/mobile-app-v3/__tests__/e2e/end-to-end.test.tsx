/**
 * End-to-End Integration Tests
 * Tests complete user journeys across mobile-app, backend, and AI layer
 */

import React from 'react';
import {
  render,
  fireEvent,
  waitFor,
  screen,
} from '@testing-library/react-native';
import { jest } from '@jest/globals';
import '../setup';

// Import all main components
import OnboardingScreen from '../../src/app/onboarding/index';
import AssessmentWelcome from '../../src/app/assessment/welcome';
import AssessmentFlow from '../../src/app/assessment/flow';
import JourneysScreen from '../../src/app/journeys';
import EmergencyScreen from '../../src/app/emergency';

// Import services
import { onboardingService } from '../../src/services/onboarding.service';
import { assessmentService } from '../../src/services/assessment.service';
// import { JourneyService } from '../../src/services/api/JourneyService';

// Import test utilities
import {
  mockUserProfile,
  mockJourney,
  createMockNavigation,
  waitFor as customWaitFor,
} from '../setup';

// Mock all services
jest.mock('../../src/services/onboarding.service');
jest.mock('../../src/services/assessment.service');
jest.mock('../../src/services/journey.service');

const mockOnboardingService = onboardingService as jest.Mocked<
  typeof onboardingService
>;
const mockAssessmentService = assessmentService as jest.Mocked<
  typeof assessmentService
>;
const mockJourneyService = journeyService as jest.Mocked<typeof journeyService>;

describe('End-to-End Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete User Journey: Dr. Ahmed (Clinical Professional)', () => {
    const drAhmedProfile = {
      ...mockUserProfile,
      name: 'Dr. Ahmed',
      profession: 'Healthcare Professional',
      islamicBackground: 'practicing',
      pathway: 'clinical_islamic_integration',
    };

    it('should complete full journey: Onboarding → Assessment → Journeys', async () => {
      // Step 1: Complete Onboarding
      mockOnboardingService.completeOnboarding.mockResolvedValue({
        user: drAhmedProfile,
        pathway: 'clinical_islamic_integration',
        nextScreen: 'assessment/welcome',
      });

      render(<OnboardingScreen />);

      // Simulate onboarding completion
      await waitFor(() => {
        expect(mockOnboardingService.completeOnboarding).toHaveBeenCalled();
      });

      // Step 2: Assessment Welcome
      render(<AssessmentWelcome userProfile={drAhmedProfile} />);

      expect(screen.getByText(/Dr. Ahmed/i)).toBeTruthy();
      expect(screen.getByText(/clinical integration/i)).toBeTruthy();

      // Step 3: Complete Assessment
      const assessmentResults = {
        layers: {
          Nafs: { symptoms: ['work_stress'], severity: 'moderate' },
          Qalb: { symptoms: ['spiritual_balance'], severity: 'mild' },
        },
        overallAssessment: 'Professional burnout with spiritual needs',
        recommendations: ['stress_management_journey', 'spiritual_renewal'],
      };

      mockAssessmentService.completeAssessment.mockResolvedValue({
        analysis: assessmentResults,
        nextStep: 'journeys',
        recommendations: ['Professional Stress Relief Journey'],
      });

      render(<AssessmentFlow />);

      await waitFor(() => {
        expect(mockAssessmentService.completeAssessment).toHaveBeenCalled();
      });

      // Step 4: Journey Creation
      const professionalJourney = {
        ...mockJourney,
        title: 'Professional Stress Relief with Islamic Practices',
        practices: [
          { type: 'dhikr', content: 'Stress relief dhikr', duration: 10 },
          {
            type: 'reflection',
            content: 'Professional-spiritual balance',
            duration: 15,
          },
        ],
      };

      mockJourneyService.generatePersonalizedJourney.mockResolvedValue(
        professionalJourney
      );

      render(<JourneysScreen />);

      await waitFor(() => {
        expect(
          mockJourneyService.generatePersonalizedJourney
        ).toHaveBeenCalledWith(
          expect.objectContaining({
            userProfile: expect.objectContaining({
              profession: 'Healthcare Professional',
            }),
          })
        );
      });

      expect(screen.getByText(/Professional Stress Relief/i)).toBeTruthy();
    });

    it('should handle professional-level crisis intervention', async () => {
      // Simulate crisis during assessment
      const crisisSymptoms = [
        'burnout',
        'thoughts of leaving profession',
        'feeling hopeless',
      ];

      mockAssessmentService.detectCrisisDuringAssessment.mockResolvedValue(
        true
      );
      mockAssessmentService.pauseForCrisis.mockResolvedValue({
        paused: true,
        crisisLevel: 'moderate',
        nextAction: 'professional_support',
      });

      render(<AssessmentFlow />);

      // Crisis should be detected and handled appropriately for professional
      await waitFor(() => {
        expect(
          mockAssessmentService.detectCrisisDuringAssessment
        ).toHaveBeenCalled();
      });

      render(<EmergencyScreen />);

      // Should show professional-appropriate crisis resources
      expect(screen.getByText(/Professional Support/i)).toBeTruthy();
      expect(screen.getByText(/Physician Support Line/i)).toBeTruthy();
    });
  });

  describe('Complete User Journey: Fatima (Traditional Muslim)', () => {
    const fatimaProfile = {
      ...mockUserProfile,
      name: 'Fatima',
      profession: 'Homemaker',
      islamicBackground: 'traditional',
      pathway: 'gentle_introduction',
    };

    it('should complete gentle introduction journey', async () => {
      // Step 1: Gentle Onboarding
      mockOnboardingService.completeOnboarding.mockResolvedValue({
        user: fatimaProfile,
        pathway: 'gentle_introduction',
        nextScreen: 'assessment/welcome',
      });

      render(<OnboardingScreen />);

      await waitFor(() => {
        expect(mockOnboardingService.completeOnboarding).toHaveBeenCalled();
      });

      // Step 2: Culturally Sensitive Assessment
      render(<AssessmentWelcome userProfile={fatimaProfile} />);

      expect(screen.getByText(/Sister Fatima/i)).toBeTruthy();
      expect(screen.getByText(/gentle approach/i)).toBeTruthy();
      expect(screen.getByText(/Islamic wisdom/i)).toBeTruthy();

      // Step 3: Traditional Islamic Journey
      const traditionalJourney = {
        ...mockJourney,
        title: 'Traditional Islamic Healing Practices',
        practices: [
          { type: 'dhikr', content: 'Traditional morning dhikr', duration: 15 },
          {
            type: 'quran_recitation',
            content: 'Surah Al-Fatiha reflection',
            duration: 10,
          },
          { type: 'dua', content: 'Traditional healing duas', duration: 5 },
        ],
      };

      mockJourneyService.generatePersonalizedJourney.mockResolvedValue(
        traditionalJourney
      );

      render(<JourneysScreen />);

      await waitFor(() => {
        expect(screen.getByText(/Traditional Islamic Healing/i)).toBeTruthy();
      });
    });

    it('should provide culturally appropriate community matching', async () => {
      const traditionalCommunity = {
        id: 'traditional_sisters',
        name: 'Traditional Sisters Support Circle',
        culturalAlignment: 'traditional_islamic',
        language: 'arabic_english',
        practiceLevel: 'traditional',
      };

      mockJourneyService.findCulturallyAlignedGroup.mockResolvedValue(
        traditionalCommunity
      );

      const result = await journeyService.findCulturallyAlignedGroup({
        culturalBackground: 'traditional',
        language: 'arabic_english',
        practiceLevel: 'traditional',
      });

      expect(result.name).toBe('Traditional Sisters Support Circle');
      expect(result.culturalAlignment).toBe('traditional_islamic');
    });
  });

  describe('Complete User Journey: Omar (Crisis State)', () => {
    const omarProfile = {
      ...mockUserProfile,
      name: 'Omar',
      profession: 'Student',
      islamicBackground: 'practicing',
      pathway: 'crisis_support',
      emergencySkip: true,
    };

    it('should handle crisis journey: Emergency → Support → Later Assessment', async () => {
      // Step 1: Crisis Detection in Onboarding
      mockOnboardingService.detectCrisis.mockResolvedValue(true);
      mockOnboardingService.handleEmergencySkip.mockResolvedValue({
        pathway: 'crisis_support',
        profile: omarProfile,
      });

      render(<OnboardingScreen />);

      // Should immediately redirect to emergency support
      await waitFor(() => {
        expect(mockOnboardingService.detectCrisis).toHaveBeenCalled();
      });

      // Step 2: Emergency Support
      render(<EmergencyScreen />);

      expect(screen.getByText(/Crisis Support/i)).toBeTruthy();
      expect(screen.getByText(/You are not alone/i)).toBeTruthy();
      expect(screen.getByText(/Allah is with you/i)).toBeTruthy();

      // Step 3: Later Assessment (when ready)
      const gentleAssessment = {
        layers: {
          Nafs: { symptoms: ['anxiety', 'depression'], severity: 'high' },
          Qalb: { symptoms: ['spiritual_crisis'], severity: 'high' },
        },
        overallAssessment: 'Crisis state requiring immediate support',
        recommendations: ['crisis_healing_journey', 'professional_counseling'],
      };

      mockAssessmentService.completeAssessment.mockResolvedValue({
        analysis: gentleAssessment,
        nextStep: 'crisis_journey',
        recommendations: ['Crisis Recovery Journey'],
      });

      // Step 4: Crisis Recovery Journey
      const crisisJourney = {
        ...mockJourney,
        title: 'Crisis Recovery with Islamic Support',
        practices: [
          { type: 'gentle_dhikr', content: 'Calming remembrance', duration: 5 },
          { type: 'comfort_dua', content: "Du'as for relief", duration: 5 },
          {
            type: 'breathing',
            content: 'Islamic breathing exercises',
            duration: 10,
          },
        ],
      };

      mockJourneyService.generatePersonalizedJourney.mockResolvedValue(
        crisisJourney
      );

      render(<JourneysScreen />);

      await waitFor(() => {
        expect(screen.getByText(/Crisis Recovery/i)).toBeTruthy();
      });
    });

    it('should provide intensive crisis monitoring', async () => {
      const crisisMonitoring = {
        checkInFrequency: 'daily',
        crisisLevel: 'high',
        supportContacts: ['crisis_counselor', 'imam', 'family'],
        emergencyPlan: 'immediate_intervention',
      };

      mockJourneyService.setupCrisisMonitoring.mockResolvedValue(
        crisisMonitoring
      );

      const result = await journeyService.setupCrisisMonitoring(omarProfile);

      expect(result.checkInFrequency).toBe('daily');
      expect(result.crisisLevel).toBe('high');
    });
  });

  describe('Backend and AI Layer Integration', () => {
    it('should handle API failures gracefully', async () => {
      // Simulate backend API failure
      mockOnboardingService.createUserProfile.mockRejectedValue(
        new Error('Backend unavailable')
      );

      render(<OnboardingScreen />);

      await waitFor(() => {
        expect(screen.getByText(/connection error/i)).toBeTruthy();
        expect(screen.getByText(/try again/i)).toBeTruthy();
      });
    });

    it('should handle AI service failures with fallback', async () => {
      // Simulate AI service failure
      mockAssessmentService.generateAnalysis.mockRejectedValue(
        new Error('AI service unavailable')
      );

      // Should provide fallback analysis
      mockAssessmentService.getFallbackAnalysis.mockResolvedValue({
        layers: { Nafs: { symptoms: [], recommendations: ['basic_support'] } },
        overallAssessment: 'Basic assessment completed',
        islamicGuidance: 'Trust in Allah and seek support',
      });

      render(<AssessmentFlow />);

      await waitFor(() => {
        expect(mockAssessmentService.getFallbackAnalysis).toHaveBeenCalled();
        expect(screen.getByText(/Basic assessment completed/i)).toBeTruthy();
      });
    });

    it('should sync data across all layers', async () => {
      const userData = {
        profile: drAhmedProfile,
        assessmentResults: { layers: {}, overallAssessment: 'Complete' },
        journeyProgress: { completed: 3, total: 7 },
      };

      // Mock successful sync across all services
      mockOnboardingService.syncUserData.mockResolvedValue(true);
      mockAssessmentService.syncAssessmentData.mockResolvedValue(true);
      mockJourneyService.syncJourneyData.mockResolvedValue(true);

      // Simulate data sync
      await Promise.all([
        onboardingService.syncUserData(userData.profile),
        assessmentService.syncAssessmentData(userData.assessmentResults),
        journeyService.syncJourneyData(userData.journeyProgress),
      ]);

      expect(mockOnboardingService.syncUserData).toHaveBeenCalled();
      expect(mockAssessmentService.syncAssessmentData).toHaveBeenCalled();
      expect(mockJourneyService.syncJourneyData).toHaveBeenCalled();
    });
  });

  describe('Cross-Feature Crisis Detection', () => {
    it('should detect crisis across all features and respond appropriately', async () => {
      const crisisScenarios = [
        {
          feature: 'onboarding',
          trigger: 'I want to harm myself',
          expectedResponse: 'immediate_crisis_modal',
        },
        {
          feature: 'assessment',
          trigger: ['thoughts of self-harm', 'feeling hopeless'],
          expectedResponse: 'pause_assessment_crisis_support',
        },
        {
          feature: 'journeys',
          trigger: 'I cannot continue, want to end everything',
          expectedResponse: 'pause_journey_emergency_mode',
        },
      ];

      for (const scenario of crisisScenarios) {
        // Mock crisis detection for each feature
        if (scenario.feature === 'onboarding') {
          mockOnboardingService.detectCrisis.mockResolvedValue(true);
        } else if (scenario.feature === 'assessment') {
          mockAssessmentService.detectCrisisDuringAssessment.mockResolvedValue(
            true
          );
        } else if (scenario.feature === 'journeys') {
          mockJourneyService.detectCrisisDuringJourney.mockResolvedValue(true);
        }

        // Should always redirect to emergency support
        render(<EmergencyScreen />);

        expect(screen.getByText(/Crisis Support/i)).toBeTruthy();
        expect(screen.getByText(/Emergency Resources/i)).toBeTruthy();
      }
    });
  });

  describe('Performance and Reliability', () => {
    it('should handle concurrent users and load', async () => {
      const concurrentUsers = Array.from({ length: 10 }, (_, i) => ({
        id: `user_${i}`,
        profile: { ...mockUserProfile, id: `user_${i}` },
      }));

      // Simulate concurrent onboarding
      const onboardingPromises = concurrentUsers.map((user) =>
        onboardingService.createUserProfile(user.profile)
      );

      mockOnboardingService.createUserProfile.mockResolvedValue(
        mockUserProfile
      );

      await Promise.all(onboardingPromises);

      expect(mockOnboardingService.createUserProfile).toHaveBeenCalledTimes(10);
    });

    it('should handle offline functionality', async () => {
      // Simulate offline mode
      const offlineData = {
        userProfile: mockUserProfile,
        assessmentProgress: { currentStep: 2, selectedSymptoms: ['anxiety'] },
        journeyProgress: { currentDay: 3, completedPractices: 2 },
      };

      // Should store data locally when offline
      mockOnboardingService.storeOfflineData.mockResolvedValue(true);

      const result = await onboardingService.storeOfflineData(offlineData);
      expect(result).toBe(true);

      // Should sync when back online
      mockOnboardingService.syncOfflineData.mockResolvedValue(true);

      const syncResult = await onboardingService.syncOfflineData();
      expect(syncResult).toBe(true);
    });
  });

  describe('Islamic Authenticity Across All Features', () => {
    it('should maintain Islamic authenticity throughout entire user journey', async () => {
      // Test Islamic content consistency across all features
      const features = [
        {
          component: OnboardingScreen,
          expectedContent: ['Bismillah', 'Assalamu Alaikum'],
        },
        {
          component: AssessmentWelcome,
          expectedContent: ['Islamic wisdom', 'soul layers'],
        },
        {
          component: JourneysScreen,
          expectedContent: ['dhikr', 'Islamic practices'],
        },
        {
          component: EmergencyScreen,
          expectedContent: ['Sakīna', 'Quranic comfort'],
        },
      ];

      for (const feature of features) {
        render(<feature.component />);

        feature.expectedContent.forEach((content) => {
          expect(screen.queryByText(new RegExp(content, 'i'))).toBeTruthy();
        });
      }
    });

    it('should ensure all Arabic text is properly validated', () => {
      const arabicTexts = [
        'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
        'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
      ];

      arabicTexts.forEach((text) => {
        const arabicRegex = /[\u0600-\u06FF]/;
        expect(arabicRegex.test(text)).toBe(true);
      });
    });
  });
});
