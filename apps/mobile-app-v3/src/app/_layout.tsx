import React from 'react';
// Import polyfill first to fix React.use hook issue
import '../polyfills/react-use';
import { RootProvider } from '../state/context';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { StyleSheet, useColorScheme } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useOAuthDeepLinkHandler } from '../hooks/useOAuthDeepLinkHandler';
import { useRouteProtection } from '../hooks/useRouteProtection';
import Toast from 'react-native-toast-message';

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: '(tabs)',
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useOAuthDeepLinkHandler(); // Listen for OAuth deep links globally
  useRouteProtection(); // Enforce route protection (toggle with env var)

  const [loaded, error] = useFonts({
    ...FontAwesome.font,
    ...FontAwesome.font,
    // Actual font files would be added to /assets/fonts/
    // For example, NotoNaskhArabic-Regular.ttf and NotoNastaliqUrdu-Regular.ttf
    ArabicFont: require('../../assets/fonts/NotoNaskhArabic-Regular.ttf'),
    UrduFont: require('../../assets/fonts/NotoNastaliqUrdu-Regular.ttf'),
    // Fallback/existing fonts if any (e.g., SpaceMono if it was intentionally used elsewhere beyond FontAwesome)
    // 'SpaceMono': require('../../assets/fonts/SpaceMono-Regular.ttf'), // Assuming SpaceMono might still be needed directly
  });

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

function RootLayoutNav() {
  const colorScheme = useColorScheme();

  return (
    <GestureHandlerRootView style={styles.container}>
      <RootProvider>
        {/* Toast component needs to be rendered at the root */}
        <ThemeProvider
          value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}
        >
          <Stack>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen
              name="emergency"
              options={{
                presentation: 'modal',
                title: 'Sakīna Mode',
              }}
            />
            <Stack.Screen name="journal" options={{ title: 'Journal' }} />
            <Stack.Screen
              name="knowledge"
              options={{ title: 'Knowledge Hub' }}
            />
            <Stack.Screen name="community" options={{ title: 'Community' }} />
            <Stack.Screen
              name="journeys"
              options={{ title: 'Healing Journeys' }}
            />
            <Stack.Screen
              name="practices"
              options={{ title: 'Daily Practices' }}
            />
            <Stack.Screen name="dhikr" options={{ title: 'Dhikr Counter' }} />
            <Stack.Screen
              name="symptoms"
              options={{ title: 'Symptom Assessment' }}
            />
            <Stack.Screen
              name="login"
              options={{ title: 'Login', headerShown: false }}
            />
            <Stack.Screen
              name="signup"
              options={{ title: 'Sign Up', headerShown: false }}
            />
          </Stack>
          <StatusBar style="auto" />
        </ThemeProvider>
        {/* Render Toast globally here */}
        <Toast />
      </RootProvider>
    </GestureHandlerRootView>
  );
}
