import React, { useState } from 'react';
import { StyleSheet, ScrollView, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';

import { View } from '../components/ui/View';
import { Text } from '../components/ui/Text';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import SymptomSelectorContent from '../components/symptoms/SymptomSelectorContent';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { SymptomSubmission } from '../services/api/types';

export default function SymptomsScreen() {
  const router = useRouter();

  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleSymptomSubmit = async (
    symptoms: SymptomSubmission,
    diagnosis: any
  ) => {
    try {
      setIsAnalyzing(true);

      // Simulate AI analysis
      setTimeout(() => {
        setIsAnalyzing(false);

        // Navigate to journeys with the diagnosis
        router.push({
          pathname: '/journeys',
          params: {
            fromSymptoms: 'true',
            diagnosis: JSON.stringify(diagnosis),
            symptoms: JSON.stringify(symptoms),
          },
        });
      }, 2000);
    } catch (error) {
      setIsAnalyzing(false);
      Alert.alert('Error', 'Failed to analyze symptoms. Please try again.');
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (isAnalyzing) {
    return (
      <View style={styles.container}>
        <View style={styles.analyzingContainer}>
          <View style={styles.analyzingIcon}></View>
          <Text variant="heading3" style={styles.analyzingTitle}>
            Analyzing Your Symptoms
          </Text>
          <Text
            variant="body"
            color="textSecondary"
            style={styles.analyzingText}
          >
            Our AI is mapping your symptoms to the five layers of the soul and
            preparing personalized healing recommendations...
          </Text>
          <View style={styles.loadingDots}>
            <View style={[styles.dot, { backgroundColor: colors.primary }]} />
            <View style={[styles.dot, { backgroundColor: colors.primary }]} />
            <View style={[styles.dot, { backgroundColor: colors.primary }]} />
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.header}>
          <Text variant="heading2" style={styles.title}>
            What are you experiencing?
          </Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Select symptoms across the five layers of your being. Our AI will
            analyze them and recommend a personalized healing journey.
          </Text>
        </View>

        <Card variant="elevated" style={styles.layerExplanation}>
          <Text variant="subtitle" style={styles.layerTitle}>
            The Five Layers of Healing
          </Text>
          <View style={styles.layerGrid}>
            <View style={styles.layerItem}>
              <View
                style={[
                  styles.layerIcon,
                  { backgroundColor: colors.error + '20' },
                ]}
              ></View>
              <Text variant="caption" style={styles.layerName}>
                Jism (Body)
              </Text>
            </View>
            <View style={styles.layerItem}>
              <View
                style={[
                  styles.layerIcon,
                  { backgroundColor: colors.secondary + '20' },
                ]}
              ></View>
              <Text variant="caption" style={styles.layerName}>
                Nafs (Emotions)
              </Text>
            </View>
            <View style={styles.layerItem}>
              <View
                style={[
                  styles.layerIcon,
                  { backgroundColor: colors.aqlYellow + '20' },
                ]}
              ></View>
              <Text variant="caption" style={styles.layerName}>
                Aql (Mind)
              </Text>
            </View>
            <View style={styles.layerItem}>
              <View
                style={[
                  styles.layerIcon,
                  { backgroundColor: colors.primary + '20' },
                ]}
              ></View>
              <Text variant="caption" style={styles.layerName}>
                Qalb (Heart)
              </Text>
            </View>
            <View style={styles.layerItem}>
              <View
                style={[
                  styles.layerIcon,
                  { backgroundColor: colors.accent + '20' },
                ]}
              ></View>
              <Text variant="caption" style={styles.layerName}>
                Ruh (Soul)
              </Text>
            </View>
          </View>
        </Card>

        <SymptomSelectorContent
          onSubmit={handleSymptomSubmit}
          onCancel={handleCancel}
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  title: {
    marginBottom: Theme.spacing.s,
  },
  subtitle: {
    lineHeight: 22,
  },
  layerExplanation: {
    marginBottom: Theme.spacing.l,
    padding: Theme.spacing.m,
  },
  layerTitle: {
    marginBottom: Theme.spacing.m,
    textAlign: 'center',
  },
  layerGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  layerItem: {
    alignItems: 'center',
    width: '18%',
    marginBottom: Theme.spacing.s,
  },
  layerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Theme.spacing.xs,
  },
  layerName: {
    textAlign: 'center',
    fontSize: 10,
  },
  analyzingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Theme.spacing.xl,
  },
  analyzingIcon: {
    marginBottom: Theme.spacing.l,
  },
  analyzingTitle: {
    marginBottom: Theme.spacing.m,
    textAlign: 'center',
  },
  analyzingText: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Theme.spacing.xl,
  },
  loadingDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});
