/**
 * Onboarding Screen for Feature 0: Adaptive Onboarding & User Profiling
 * Main onboarding flow component for React Native
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
  Linking, // Import Linking
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Import custom components
import { OnboardingQuestion } from '../../components/OnboardingQuestion';
import { ProgressBar } from '../../components/ui/ProgressBar';
import { CrisisModal } from '../../components/CrisisModal';
import { WelcomeScreen } from '../../components/WelcomeScreen';
import { ConfirmationModal } from '../../components/ui/ConfirmationModal';
import { NotificationModal } from '../../components/ui/NotificationModal';

// Import services
import { onboardingService } from '../../services/onboarding.service';
import { authService } from '../../services/auth.service';

// Types
interface OnboardingState {
  sessionId: string | null;
  currentQuestion: any;
  progress: number;
  isLoading: boolean;
  isComplete: boolean;
  crisisDetected: boolean;
  crisisData: any;
  alreadyCompleted: boolean;
  completionData: any;
  isRestarting: boolean;
}

interface ErrorState {
  visible: boolean;
  title: string;
  message: string;
  onRetry: () => void;
}

interface NotificationState {
  visible: boolean;
  title: string;
  message: string;
  iconName?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
}

const { width, height } = Dimensions.get('window');

export default function OnboardingScreen() {
  const [state, setState] = useState<OnboardingState>({
    sessionId: null,
    currentQuestion: null,
    progress: 0,
    isLoading: true,
    isComplete: false,
    crisisDetected: false,
    crisisData: null,
    alreadyCompleted: false,
    completionData: null,
    isRestarting: false,
  });

  const [responses, setResponses] = useState<Record<string, any>>({});
  const [stepStartTime, setStepStartTime] = useState<number>(Date.now());
  const [showRestartModal, setShowRestartModal] = useState<boolean>(false);
  const [showSkipModal, setShowSkipToModal] = useState<boolean>(false);

  const [error, setError] = useState<ErrorState>({
    visible: false,
    title: '',
    message: '',
    onRetry: () => {},
  });

  const [notification, setNotification] = useState<NotificationState>({
    visible: false,
    title: '',
    message: '',
  });

  
  useEffect(() => {
    initializeOnboarding();
  }, []);

  useEffect(() => {
    // Track time spent on each step
    setStepStartTime(Date.now());
  }, [state.currentQuestion]);

  const initializeOnboarding = async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      const deviceInfo = {
        platform: 'mobile',
        screenSize: `${width}x${height}`,
      };

      let response;
      const storedSessionId = await onboardingService.getCurrentSessionId();
      const storedProgress = await onboardingService.loadProgress();

      if (storedSessionId && storedProgress && storedProgress.currentQuestion) {
        // Attempt to resume existing session
        try {
          response = await onboardingService.resumeOnboarding(storedSessionId);
          // If resume successful, use stored progress for current question and responses
          setResponses(storedProgress.responses || {});
          setStepStartTime(Date.now()); // Reset timer for the resumed step
          setState((prev) => ({
            ...prev,
            sessionId: storedSessionId,
            currentQuestion: storedProgress.currentQuestion,
            progress: storedProgress.progress || 0,
            isLoading: false,
          }));
          return; // Exit early if resumed
        } catch (resumeError) {
          console.warn('Failed to resume onboarding session, starting new one:', resumeError);
          // Fall through to start a new session if resume fails
        }
      }

      // Start a new onboarding session if no session or resume failed
      response = await onboardingService.startOnboarding(deviceInfo);

      // Check if onboarding is already completed
      if (response?.status === 'already_completed') {
        setState((prev) => ({
          ...prev,
          alreadyCompleted: true,
          completionData: response.data,
          isLoading: false,
          currentQuestion: null, // Explicitly set to null
          sessionId: null, // Clear session ID
        }));
        return;
      }

      const actualQuestion = response?.question?.question;
      const actualProgress = response?.question?.progress;
      const sessionIdFromResponse = response?.session?.sessionId;

      // Only set question data if we have a valid question
      if (actualQuestion && actualQuestion.id) {
        setState((prev) => ({
          ...prev,
          sessionId: sessionIdFromResponse || null,
          currentQuestion: actualQuestion,
          progress: typeof actualProgress === 'number' ? actualProgress : 0,
          isLoading: false,
        }));
      } else {
        // No valid question received, show error
        setState((prev) => ({ ...prev, isLoading: false }));
        setError({
          visible: true,
          title: 'Error',
          message: 'Unable to load onboarding questions. Please try again.',
          onRetry: initializeOnboarding,
        });
      }
    } catch (error: any) {
      console.error('Failed to initialize onboarding:', error);
      
      setState((prev) => ({ ...prev, isLoading: false }));
      setError({
        visible: true,
        title: 'Connection Error',
        message: 'Unable to start onboarding. Please check your connection and try again.',
        onRetry: initializeOnboarding,
      });
    }
  };

  const handleResponse = async (response: any) => {
    if (!state.sessionId || !state.currentQuestion) return;

    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      // Calculate time spent on this step
      const currentTimeSpent = Math.round((Date.now() - stepStartTime) / 1000);

      // Submit response
      const result = await onboardingService.submitResponse({
        sessionId: state.sessionId,
        stepId: state.currentQuestion.id,
        response,
        timeSpent: currentTimeSpent,
      });

      // Update responses state with the user's selection
      setResponses((prev) => {
        const newResponses = {
          ...prev,
          [state.currentQuestion.id]: response,
        };
        // Save progress locally
        onboardingService.saveProgress({
          sessionId: state.sessionId,
          currentQuestion: state.currentQuestion,
          progress: result.progress || 0,
          responses: newResponses,
        });
        return newResponses;
      });

      // --- BEGIN DIAGNOSTIC LOGGING FOR CONDITION ---
      console.log('OnboardingScreen: Checking conditions with result:', JSON.stringify(result, null, 2));
      if (result && result.question) {
        console.log('OnboardingScreen: typeof result.question.id is:', typeof result.question.id);
        console.log('OnboardingScreen: result.question.id value is:', result.question.id);
      } else {
        console.log('OnboardingScreen: result.question is falsy or result is falsy when checking for .id');
      }
      // --- END DIAGNOSTIC LOGGING FOR CONDITION ---

      // 'result' holds the data payload from the service call
      // e.g., { question: ... } or { profile: ... } or { level: ..., message: ... }
      if (result && result.level && result.actions) { // Crisis
        setState((prev) => ({
          ...prev,
          crisisDetected: true,
          crisisData: result,
          isLoading: false,
        }));
      } else if (result && result.profile) { // Onboarding complete
        setState((prev) => ({
          ...prev,
          isComplete: true,
          isLoading: false,
        }));
        handleOnboardingComplete(result);
      } else if (result && result.question && typeof result.question.id === 'string') { // Check for question.id
        setState((prev) => ({
          ...prev,
          currentQuestion: result.question, // result.question is the actual question object
          progress: result.progress || 0,   // result.progress is a sibling
          isLoading: false,
        }));
      } else {
        // Unknown response structure, log it and stop loading to be safe
        console.error("OnboardingScreen: Unknown/incomplete response structure from submitResponse:", result);
        setState((prev) => ({ ...prev, isLoading: false }));
        setNotification({
          visible: true,
          title: 'Error',
          message: 'Received an unexpected response. Please try again.',
          iconName: 'alert-circle',
          iconColor: '#ef4444',
        });
      }
    } catch (error) {
      console.error('Failed to submit response:', error);
      setState((prev) => ({ ...prev, isLoading: false })); // Ensure isLoading is false on error
      setNotification({
        visible: true,
        title: 'Error',
        message: 'Failed to submit response. Please try again.',
        iconName: 'alert-circle',
        iconColor: '#ef4444',
      });
    }
  };

  const handleOnboardingComplete = async (completionData: any) => {
    try {
      // Store profile data and wait for it to complete
      await authService.updateUserProfile(completionData.profile);

      // Navigate to appropriate next screen based on pathway
      const pathway = completionData.recommendedPathway;

      if (pathway === 'crisis_support') {
        router.replace('/emergency');
      } else if (pathway === 'clinical_islamic_integration') {
        // TODO: Create a dedicated '/dashboard/advanced' screen or verify if '/journey/dashboard' is more appropriate.
        router.replace('/(tabs)');
      } else if (pathway === 'gentle_introduction') {
        // TODO: Create a dedicated '/intro' screen for gentle introduction pathway.
        router.replace('/(tabs)');
      } else if (pathway === 'traditional_modern_bridge') {
        // TODO: Create a dedicated screen for 'traditional_modern_bridge' pathway.
        router.replace('/(tabs)');
      } else if (pathway === 'advanced_development') {
        // TODO: Create a dedicated screen for 'advanced_development' pathway.
        router.replace('/(tabs)');
      } else if (pathway === 'maintenance_program') {
        // TODO: Create a dedicated screen for 'maintenance_program' pathway.
        router.replace('/(tabs)');
      } else {
        // Default fallback to main dashboard
        router.replace('/(tabs)');
      }
    } catch (error) {
      console.error("Failed to update profile after onboarding:", error);
      // Optionally, show a notification to the user
      setNotification({
        visible: true,
        title: 'Update Failed',
        message: 'Your profile could not be updated. Please try again from the settings menu.',
        iconName: 'alert-circle',
        iconColor: '#ef4444',
      });
      // Still navigate the user away from onboarding
      router.replace('/(tabs)');
    }
  };

  const handleSkipOnboarding = async () => {
    setShowSkipToModal(true);
  };

  const handleConfirmSkip = async () => {
    setShowSkipToModal(false);
    try {
      const result = await onboardingService.skipOnboarding(
        'time_constraint'
      );
      handleOnboardingComplete(result.data);
    } catch (error) {
      console.error('Failed to skip onboarding:', error);
    }
  };

  const handleCrisisAction = (actionId: string) => {
    switch (actionId) {
      case 'emergency_sakina':
        router.replace('/emergency');
        break;
      case 'crisis_counselor':
        router.replace('/crisis-counselor' as any); // Assuming this route exists
        break;
      case 'crisis_hotline':
        // TODO: Consider making the hotline number configurable
        const phoneNumber = 'tel:988'; // Example: US crisis line
        Linking.canOpenURL(phoneNumber)
          .then((supported) => {
            if (!supported) {
              setNotification({
                visible: true,
                title: 'Hotline Error',
                message: 'Unable to open phone dialer.',
                iconName: 'call',
                iconColor: '#ef4444',
              });
              console.warn('Phone call not supported');
            } else {
              return Linking.openURL(phoneNumber);
            }
          })
          .catch((err) => {
            setNotification({
              visible: true,
              title: 'Hotline Error',
              message: 'An error occurred trying to call the hotline.',
              iconName: 'call',
              iconColor: '#ef4444',
            });
            console.error('An error occurred', err)
          });
        // Keep the modal open or close? For now, let's assume we keep it open
        // or the user navigates away via the call.
        break;
      default:
        setState((prev) => ({ ...prev, crisisDetected: false }));
    }
  };

  const handleCompletionAction = async (actionId: string) => {
    switch (actionId) {
      case 'go_to_dashboard':
        router.replace('/(tabs)');
        break;
      case 'restart_onboarding':
        // Show custom confirmation modal instead of Alert.alert
        setShowRestartModal(true);
        break;
      default:
        setState((prev) => ({ ...prev, alreadyCompleted: false }));
    }
  };

  const handleRestartConfirm = async () => {
    try {
      setShowRestartModal(false);
      setState((prev) => ({ ...prev, isLoading: true, alreadyCompleted: false, isRestarting: true }));
      
      // Clear any stored progress
      await onboardingService.clearSession();
      setResponses({});
      
      const deviceInfo = {
        platform: 'mobile',
        screenSize: `${width}x${height}`,
      };
      
      // Force restart by calling the restart endpoint
      const response = await onboardingService.restartOnboarding(deviceInfo);
      
      const actualQuestion = response?.question?.question;
      const actualProgress = response?.question?.progress;
      const sessionIdFromResponse = response?.session?.sessionId;

      setState((prev) => ({
        ...prev,
        sessionId: sessionIdFromResponse || null,
        currentQuestion: actualQuestion || null,
        progress: typeof actualProgress === 'number' ? actualProgress : 0,
        isLoading: false,
        alreadyCompleted: false,
        completionData: null,
        isRestarting: false,
      }));
      
      // Reset step timer
      setStepStartTime(Date.now());
      
      setNotification({
        visible: true,
        title: 'Onboarding Restarted',
        message: 'Your onboarding has been successfully restarted. Let\'s begin your journey again!',
        iconName: 'checkmark-circle',
        iconColor: '#4ade80',
      });
    } catch (error) {
      console.error('Failed to restart onboarding:', error);
      setState((prev) => ({ ...prev, isLoading: false, isRestarting: false }));
      setNotification({
        visible: true,
        title: 'Restart Failed',
        message: 'Unable to restart onboarding. Please try again or contact support if the problem persists.',
        iconName: 'alert-circle',
        iconColor: '#ef4444',
      });
    }
  };

  const handleRestartCancel = () => {
    setShowRestartModal(false);
  };

  if (state.isLoading && !state.currentQuestion) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a365d', '#2d5a87', '#4a90a4']}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Preparing your journey...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  // Show completion screen if onboarding is already completed
  if (state.alreadyCompleted) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a365d', '#2d5a87', '#4a90a4']}
          style={styles.gradient}
        >
          <ScrollView
            style={styles.content}
            contentContainerStyle={[styles.contentContainer, styles.completionContainer]}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.completionContent}>
              <Ionicons name="checkmark-circle" size={80} color="#4ade80" />
              <Text style={styles.completionTitle}>Onboarding Complete!</Text>
              <Text style={styles.completionMessage}>
                {state.completionData?.message || 'You have already completed your onboarding journey.'}
              </Text>
              
              <View style={styles.infoBox}>
                <Ionicons name="information-circle" size={20} color="#60a5fa" />
                <Text style={styles.infoText}>
                  Your personalized healing journey is ready. You can continue to your dashboard or restart if you'd like to update your preferences.
                </Text>
              </View>
              
              {state.completionData?.completedAt && (
                <Text style={styles.completionDate}>
                  Completed on {new Date(state.completionData.completedAt).toLocaleDateString()}
                </Text>
              )}
              
              <View style={styles.completionActions}>
                {state.completionData?.actions?.map((action: any) => (
                  <TouchableOpacity
                    key={action.id}
                    style={[
                      styles.completionButton,
                      action.primary ? styles.primaryButton : styles.secondaryButton,
                    ]}
                    onPress={() => handleCompletionAction(action.id)}
                    disabled={action.id === 'restart_onboarding' && state.isRestarting}
                  >
                    {action.id === 'restart_onboarding' && state.isRestarting ? (
                      <ActivityIndicator size="small" color={action.primary ? "#1a365d" : "#ffffff"} />
                    ) : (
                      <Text
                        style={[
                          styles.completionButtonText,
                          action.primary ? styles.primaryButtonText : styles.secondaryButtonText,
                        ]}
                      >
                        {action.text}
                      </Text>
                    )}
                  </TouchableOpacity>
                )) || [
                  // Default actions if none provided
                  <TouchableOpacity
                    key="go_to_dashboard"
                    style={[styles.completionButton, styles.primaryButton]}
                    onPress={() => handleCompletionAction('go_to_dashboard')}
                  >
                    <Text style={[styles.completionButtonText, styles.primaryButtonText]}>
                      Go to Dashboard
                    </Text>
                  </TouchableOpacity>,
                  <TouchableOpacity
                    key="restart_onboarding"
                    style={[
                      styles.completionButton, 
                      styles.secondaryButton,
                      state.isRestarting && styles.disabledButton
                    ]}
                    onPress={() => handleCompletionAction('restart_onboarding')}
                    disabled={state.isRestarting}
                  >
                    {state.isRestarting ? (
                      <ActivityIndicator size="small" color="#ffffff" />
                    ) : (
                      <Text style={[styles.completionButtonText, styles.secondaryButtonText]}>
                        Restart Onboarding
                      </Text>
                    )}
                  </TouchableOpacity>
                ]}
              </View>
            </View>
          </ScrollView>
          
          {/* Restart Confirmation Modal - Positioned inside completion screen */}
          <ConfirmationModal
            visible={showRestartModal}
            title="Restart Onboarding"
            message="This will start your onboarding journey from the beginning. Your previous responses will be cleared. Are you sure?"
            confirmText="Restart"
            cancelText="Cancel"
            onConfirm={handleRestartConfirm}
            onCancel={handleRestartCancel}
            isDestructive={true}
            iconName="warning"
            iconColor="#f59e0b"
          />
        </LinearGradient>
      </SafeAreaView>
    );
  }

  console.log('OnboardingScreen render - state.progress:', state.progress, 'isLoading:', state.isLoading, 'currentQuestion exists:', !!state.currentQuestion);
  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#1a365d', '#2d5a87', '#4a90a4']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <View style={styles.headerLeft} />
            <TouchableOpacity
              style={styles.skipButton}
              onPress={handleSkipOnboarding}
            >
              <Text style={styles.skipText}>Skip</Text>
            </TouchableOpacity>
          </View>
          {state.progress > 0 && (
            <View style={styles.progressBarContainer}>
              <ProgressBar progress={state.progress} />
            </View>
          )}
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {state.currentQuestion?.type === 'welcome' ? (
            <WelcomeScreen
              question={state.currentQuestion}
              onResponse={handleResponse}
              isLoading={state.isLoading}
            />
          ) : state.currentQuestion && state.currentQuestion.id ? (
            <OnboardingQuestion
              question={state.currentQuestion}
              onResponse={handleResponse}
              isLoading={state.isLoading}
            />
          ) : !state.alreadyCompleted && !state.isComplete ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#ffffff" />
              <Text style={styles.loadingText}>Loading question...</Text>
            </View>
          ) : null}
        </ScrollView>

        {/* Crisis Modal */}
        <CrisisModal
          visible={state.crisisDetected}
          crisisData={state.crisisData}
          onAction={handleCrisisAction}
          onClose={() =>
            setState((prev) => ({ ...prev, crisisDetected: false }))
          }
        />

        {/* Error Modal */}
        <ConfirmationModal
          visible={error.visible}
          title={error.title}
          message={error.message}
          confirmText="Retry"
          cancelText="Skip"
          onConfirm={() => {
            setError({ ...error, visible: false });
            error.onRetry();
          }}
          onCancel={() => {
            setError({ ...error, visible: false });
            handleSkipOnboarding();
          }}
          iconName="alert-circle"
          iconColor="#ef4444"
        />

        {/* Skip Confirmation Modal */}
        <ConfirmationModal
          visible={showSkipModal}
          title="Skip Onboarding?"
          message="Skipping will limit personalization. You can complete it later in settings."
          confirmText="Skip"
          cancelText="Cancel"
          onConfirm={handleConfirmSkip}
          onCancel={() => setShowSkipToModal(false)}
          isDestructive={true}
          iconName="warning"
          iconColor="#f59e0b"
        />

        {/* Notification Modal */}
        <NotificationModal
          visible={notification.visible}
          title={notification.title}
          message={notification.message}
          iconName={notification.iconName}
          iconColor={notification.iconColor}
          onDismiss={() => setNotification({ ...notification, visible: false })}
        />

      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Poppins-Regular',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10, // Reduced padding
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15, // Space between skip button and progress bar
    minHeight: 30, // Ensure consistent height
  },
  headerLeft: {
    flex: 1,
  },
  skipButton: {
    paddingVertical: 6, // Smaller padding
    paddingHorizontal: 14, // Smaller padding
    borderRadius: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.15)', // More subtle background
  },
  skipText: {
    color: '#ffffff',
    fontSize: 13, // Smaller font size
    fontFamily: 'Poppins-Medium',
  },
  progressBarContainer: {
    width: '100%',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  // Completion screen styles
  completionContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: height * 0.8,
  },
  completionContent: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  completionTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 16,
    fontFamily: 'Poppins-Bold',
  },
  completionMessage: {
    fontSize: 16,
    color: '#e2e8f0',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
    fontFamily: 'Poppins-Regular',
  },
  completionDate: {
    fontSize: 14,
    color: '#94a3b8',
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'Poppins-Regular',
    fontStyle: 'italic',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(96, 165, 250, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: 'rgba(96, 165, 250, 0.2)',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#cbd5e1',
    lineHeight: 20,
    marginLeft: 12,
    fontFamily: 'Poppins-Regular',
  },
  completionActions: {
    width: '100%',
    gap: 16,
  },
  completionButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  primaryButton: {
    backgroundColor: '#4ade80',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  completionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  primaryButtonText: {
    color: '#1a365d',
  },
  secondaryButtonText: {
    color: '#ffffff',
  },
  disabledButton: {
    opacity: 0.6,
  },
});