import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native'; // Removed Alert
import { LinearGradient } from 'expo-linear-gradient';
import { GoogleSignInButton } from '../components/GoogleSignInButton';
import Toast from 'react-native-toast-message'; // Added Toast
import { authService } from '../services/auth.service';
import { router } from 'expo-router';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendingConfirmation, setResendingConfirmation] = useState(false);

  const handleLogin = async () => {
    setLoading(true);
    try {
      // Assuming authService.signIn was correctly updated to point to /api/auth/login
      // or consider using authService.login if that's more appropriate.
      await authService.signIn(email, password);
      Toast.show({
        type: 'success',
        text1: 'Login Successful',
        text2: 'Welcome back!',
        visibilityTime: 3000,
      });
      router.replace('/(tabs)');
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: error.message || 'An unexpected error occurred. Please try again.',
        visibilityTime: 4000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendConfirmation = async () => {
    if (!email) {
      Toast.show({
        type: 'error',
        text1: 'Email Required',
        text2: 'Please enter your email address to resend confirmation.',
        visibilityTime: 3000,
      });
      return;
    }
    setResendingConfirmation(true);
    try {
      await authService.requestResendConfirmation(email);
      Toast.show({
        type: 'success',
        text1: 'Confirmation Sent',
        text2: 'If an account exists for this email, a new confirmation link has been sent.',
        visibilityTime: 4000,
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Resend Failed',
        text2: error.message || 'Could not resend confirmation. Please try again.',
        visibilityTime: 4000,
      });
    } finally {
      setResendingConfirmation(false);
    }
  };

  return (
    <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.container}>
      <View style={styles.card}>
        <Text style={styles.title}>Welcome Back</Text>
        <Text style={styles.subtitle}>Sign in to continue your journey</Text>
        <TextInput
          style={styles.input}
          placeholder="Email"
          placeholderTextColor="#b0b8c1"
          autoCapitalize="none"
          keyboardType="email-address"
          value={email}
          onChangeText={setEmail}
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor="#b0b8c1"
          secureTextEntry
          value={password}
          onChangeText={setPassword}
        />
        <TouchableOpacity style={styles.loginButton} onPress={handleLogin} disabled={loading}>
          <Text style={styles.loginButtonText}>{loading ? 'Signing in...' : 'Sign In'}</Text>
        </TouchableOpacity>
        <Text style={styles.orText}>or</Text>
        <GoogleSignInButton />
        <TouchableOpacity onPress={() => router.replace('/signup')}>
          <Text style={styles.linkText}>Don’t have an account? Sign up</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleResendConfirmation} disabled={resendingConfirmation || !email} style={{ marginTop: 8 }}>
          <Text style={styles.linkText}>
            {resendingConfirmation ? 'Resending...' : 'Resend confirmation email?'}
          </Text>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    width: '90%',
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a365d',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#4a90a4',
    marginBottom: 24,
  },
  input: {
    width: '100%',
    height: 48,
    borderRadius: 8,
    backgroundColor: '#f0f4f8',
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 16,
    color: '#1a365d',
  },
  loginButton: {
    width: '100%',
    backgroundColor: '#2d5a87',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 12,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  orText: {
    color: '#4a90a4',
    marginVertical: 8,
    fontSize: 14,
  },
  linkText: {
    color: '#2d5a87',
    marginTop: 16,
    fontSize: 15,
    textDecorationLine: 'underline',
  },
});
