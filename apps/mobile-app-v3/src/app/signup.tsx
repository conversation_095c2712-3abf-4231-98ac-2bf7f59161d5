import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native'; // Removed Alert
import { LinearGradient } from 'expo-linear-gradient';
import { GoogleSignInButton } from '../components/GoogleSignInButton';
import Toast from 'react-native-toast-message'; // Added Toast
import { authService } from '../services/auth.service';
import { router } from 'expo-router';

export default function SignupScreen() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSignup = async () => {
    if (!firstName) {
      Toast.show({
        type: 'error',
        text1: 'Signup Error',
        text2: 'First name is required.',
        visibilityTime: 3000,
      });
      return;
    }
    if (password !== confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Signup Error',
        text2: 'Passwords do not match.',
        visibilityTime: 3000,
      });
      return;
    }
    setLoading(true);
    try {
      await authService.signUp(email, password, firstName, lastName);
      Toast.show({
        type: 'success',
        text1: 'Signup Successful',
        text2: 'Account created! Please log in.',
        visibilityTime: 4000,
      });
      router.replace('/login');
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Signup Failed',
        text2: error.message || 'An unexpected error occurred. Please try again.',
        visibilityTime: 4000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.container}>
      <View style={styles.card}>
        <Text style={styles.title}>Create Account</Text>
        <Text style={styles.subtitle}>Sign up to begin your journey</Text>
        <TextInput
          style={styles.input}
          placeholder="First Name*"
          placeholderTextColor="#b0b8c1"
          value={firstName}
          onChangeText={setFirstName}
        />
        <TextInput
          style={styles.input}
          placeholder="Last Name"
          placeholderTextColor="#b0b8c1"
          value={lastName}
          onChangeText={setLastName}
        />
        <TextInput
          style={styles.input}
          placeholder="Email"
          placeholderTextColor="#b0b8c1"
          autoCapitalize="none"
          keyboardType="email-address"
          value={email}
          onChangeText={setEmail}
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor="#b0b8c1"
          secureTextEntry
          value={password}
          onChangeText={setPassword}
        />
        <TextInput
          style={styles.input}
          placeholder="Confirm Password"
          placeholderTextColor="#b0b8c1"
          secureTextEntry
          value={confirmPassword}
          onChangeText={setConfirmPassword}
        />
        <TouchableOpacity style={styles.signupButton} onPress={handleSignup} disabled={loading}>
          <Text style={styles.signupButtonText}>{loading ? 'Signing up...' : 'Sign Up'}</Text>
        </TouchableOpacity>
        <Text style={styles.orText}>or</Text>
        <GoogleSignInButton />
        <TouchableOpacity onPress={() => router.replace('/login')}>
          <Text style={styles.linkText}>Already have an account? Log in</Text>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    width: '90%',
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a365d',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#4a90a4',
    marginBottom: 24,
  },
  input: {
    width: '100%',
    height: 48,
    borderRadius: 8,
    backgroundColor: '#f0f4f8',
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 16,
    color: '#1a365d',
  },
  signupButton: {
    width: '100%',
    backgroundColor: '#2d5a87',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 12,
  },
  signupButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  orText: {
    color: '#4a90a4',
    marginVertical: 8,
    fontSize: 14,
  },
  linkText: {
    color: '#2d5a87',
    marginTop: 16,
    fontSize: 15,
    textDecorationLine: 'underline',
  },
});
