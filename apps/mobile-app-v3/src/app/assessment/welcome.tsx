import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { assessmentService } from '../../services/assessment.service';
import { colors } from '../../constants/Colors';
import Theme from '../../constants/Theme';
import { IslamicIcons } from '../../constants/IslamicIcons';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const AssessmentWelcomeScreen = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [assessmentData, setAssessmentData] = useState<any | null>(null);

  useEffect(() => {
    const fetchAssessmentStatus = async () => {
      try {
        const response = await assessmentService.getPersonalizedWelcome();
        setAssessmentData(response);
      } catch (error) {
        console.error(error);
        setAssessmentData({
          assessmentStatus: 'not_started',
          message: 'Welcome to Your Spiritual Healing Journey',
          actions: [
            {
              id: 'start_assessment',
              text: 'Start Assessment',
              description: 'Begin your path to self-discovery.',
            },
          ],
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAssessmentStatus();
  }, []);

  const handleAction = async (actionId: string) => {
    if (actionLoading) return;

    setActionLoading(true);
    try {
      switch (actionId) {
        case 'start_assessment':
          router.push('/assessment/flow');
          break;
        case 'retake_assessment':
          // This now properly calls the backend to start a new session
          await assessmentService.startAssessment();
          router.push('/assessment/flow');
          break;
        case 'view_results':
          if (assessmentData?.sessionId) {
            router.push({ pathname: '/assessment/results', params: { sessionId: assessmentData.sessionId } });
          } else {
            Alert.alert("Error", "Could not find the session ID to view results.");
          }
          break;
        case 'start_journey':
          router.push('/journey');
          break;
        default:
          break;
      }
    } catch (error) {
      console.error(`Failed to handle action ${actionId}:`, error);
      Alert.alert("Error", "An unexpected error occurred. Please try again.");
    } finally {
      setActionLoading(false);
    }
  };

  const getIconForAction = (actionId: string) => {
    switch (actionId) {
      case 'view_results':
        return IslamicIcons.analysisStep;
      case 'start_journey':
        return IslamicIcons.growth;
      case 'retake_assessment':
        return 'refresh';
      case 'start_assessment':
        return 'play-circle-outline';
      default:
        return 'arrow-right';
    }
  };

  const renderLoading = () => (
    <LinearGradient colors={[colors.lightBlue, colors.sandBeige]} style={styles.container}>
      <ActivityIndicator size="large" color={colors.emerald} />
    </LinearGradient>
  );

  const renderContent = () => {
    if (!assessmentData) return null;

    const isCompleted = assessmentData.assessmentStatus === 'completed';
    const message = isCompleted ? assessmentData.message : assessmentData.greeting;
    const actions = isCompleted ? assessmentData.actions : [assessmentData.primaryAction, ...assessmentData.secondaryActions].filter(Boolean);
    const introduction = isCompleted ? null : assessmentData.introduction;

    return (
      <LinearGradient colors={[colors.lightBlue, colors.sandBeige]} style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Text style={styles.title}>{message}</Text>
          {introduction && <Text style={styles.introduction}>{introduction}</Text>}
          <View style={styles.actionsContainer}>
            {actions.map((action: any) => (
              <TouchableOpacity
                key={action.id}
                style={[styles.actionButton, actionLoading && styles.actionButtonDisabled]}
                onPress={() => handleAction(action.id)}
                disabled={actionLoading}
              >
                {actionLoading ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <>
                    <MaterialCommunityIcons
                      name={getIconForAction(action.id) as any}
                      size={24}
                      color="white"
                      style={styles.actionIcon}
                    />
                    <View style={styles.actionTextContainer}>
                      <Text style={styles.actionButtonText}>{action.text}</Text>
                      {action.description && <Text style={styles.actionButtonDescription}>{action.description}</Text>}
                    </View>
                  </>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </LinearGradient>
    );
  };

  return loading ? renderLoading() : renderContent();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  title: {
    fontSize: Theme.typography.fontSize.xxxl,
    fontWeight: Theme.typography.fontWeight.bold,
    color: colors.forestGreen,
    textAlign: 'center',
    marginBottom: 16,
  },
  introduction: {
    fontSize: Theme.typography.fontSize.l,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 48,
    paddingHorizontal: 16,
  },
  actionsContainer: {
    width: '100%',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.islamicGold,
    borderRadius: Theme.borderRadius.large,
    paddingVertical: 18,
    paddingHorizontal: 24,
    marginBottom: 16,
    ...Theme.shadows.medium,
  },
  actionButtonDisabled: {
    backgroundColor: colors.disabled,
  },
  actionIcon: {
    marginRight: 16,
  },
  actionTextContainer: {
    flex: 1,
  },
  actionButtonText: {
    color: 'white',
    fontSize: Theme.typography.fontSize.l,
    fontWeight: Theme.typography.fontWeight.semibold,
  },
  actionButtonDescription: {
    color: 'white',
    fontSize: Theme.typography.fontSize.s,
    opacity: 0.9,
    marginTop: 4,
  },
});

export default AssessmentWelcomeScreen;
