/**
 * Enhanced Assessment Flow Screen - Visually Stunning Islamic Design
 * Implements comprehensive UI enhancements from Feature 1 checklist
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Animated,
  TextInput,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons, Feather } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { assessmentService } from '../../services/assessment.service';
import { colors } from '../../constants/Colors';
import Theme from '../../constants/Theme';
import CrisisModal, { CrisisData } from '../../components/CrisisModal';
import { IslamicIcons } from '../../constants/IslamicIcons';

const { width, height } = Dimensions.get('window');

// Enhanced interfaces
interface AssessmentQuestion {
  id: string;
  category: 'physical' | 'emotional' | 'mental' | 'spiritual';
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  title: string;
  description?: string;
  questionType: string;
  symptoms: AssessmentSymptom[];
  reflectionPrompt?: string;
  reflectionRequired: boolean;
  allowMultipleSelection: boolean;
  intensityScale: boolean;
  customInputAllowed: boolean;
}

interface AssessmentSymptom {
  id: string;
  text: string;
  description?: string;
  severity: 'mild' | 'moderate' | 'severe';
  islamicContext?: string;
  primaryLayer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
}

interface AssessmentSession {
  id: string;
  userId: string;
  currentStep: string;
  diagnosis?: any;
}

interface StepResponseData {
  selectedSymptomValues: string[];
  intensity: 'mild' | 'moderate' | 'severe';
  reflectionTexts: Record<string, string>;
}

// Enhanced step configuration with Islamic theming
const ASSESSMENT_STEPS = [
  {
    id: 'physical_experiences',
    title: 'Physical Experiences',
    subtitle: 'Jism - Your Body\'s Voice',
    icon: 'account-heart-outline',
    color: colors.jism,
    gradient: [colors.jism, colors.jism + '80'],
    description: 'Let\'s start with what your body is telling you',
    islamicContext: 'The body is an amanah (trust) from Allah',
  },
  {
    id: 'emotional_experiences',
    title: 'Emotional Experiences',
    subtitle: 'Nafs - Your Inner Emotions',
    icon: 'fire',
    color: colors.nafs,
    gradient: [colors.nafs, colors.nafs + '80'],
    description: 'Now, let\'s explore your emotional landscape',
    islamicContext: 'Managing the nafs is part of spiritual growth',
  },
  {
    id: 'mental_experiences',
    title: 'Mental Experiences',
    subtitle: 'Aql - Your Mind\'s Patterns',
    icon: 'brain',
    color: colors.aql,
    gradient: [colors.aql, colors.aql + '80'],
    description: 'Let\'s look at what\'s happening in your mind',
    islamicContext: 'The mind is a gift to contemplate Allah\'s creation',
  },
  {
    id: 'spiritual_experiences',
    title: 'Spiritual Experiences',
    subtitle: 'Qalb & Ruh - Your Soul\'s Journey',
    icon: 'heart-pulse',
    color: colors.qalb,
    gradient: [colors.qalb, colors.qalb + '80'],
    description: 'Finally, let\'s explore your spiritual experiences',
    islamicContext: 'The heart is the seat of faith and spiritual connection',
  },
  {
    id: 'reflections',
    title: 'Personal Reflections',
    subtitle: 'Your Inner Wisdom',
    icon: 'star-four-points-outline',
    color: colors.ruh,
    gradient: [colors.ruh, colors.ruh + '80'],
    description: 'Take a moment to reflect on your experiences',
    islamicContext: 'Self-reflection is a path to spiritual awareness',
  },
];

export default function EnhancedAssessmentFlowScreen() {
  const { sessionId, currentStep: initialStepParam } = useLocalSearchParams();

  // State management
  const [currentStep, setCurrentStep] = useState(initialStepParam as string || 'physical_experiences');
  const [questions, setQuestions] = useState<AssessmentQuestion[]>([]);
  const [stepHistory, setStepHistory] = useState<string[]>([]);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [session, setSession] = useState<AssessmentSession | null>(null);
  const [stepResponses, setStepResponses] = useState<Record<string, StepResponseData>>({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [crisisModalData, setCrisisModalData] = useState<CrisisData | null>(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const stepStartTime = useRef(Date.now());

  // Get current step configuration
  const getCurrentStepConfig = () => {
    return ASSESSMENT_STEPS.find(step => step.id === currentStep) || ASSESSMENT_STEPS[0];
  };

  // Enhanced progress calculation
  const getProgressPercentage = () => {
    const currentIndex = ASSESSMENT_STEPS.findIndex(step => step.id === currentStep);
    return ((currentIndex + 1) / ASSESSMENT_STEPS.length) * 100;
  };

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, [sessionId]);

  // Load step data when step changes
  useEffect(() => {
    if (!loading && session) {
      loadStepData();
    }
  }, [currentStep, session]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      if (!sessionId) {
        Alert.alert('Error', 'Session ID is missing.');
        router.replace('/assessment/welcome');
        return;
      }

      const sessionData = await assessmentService.getSession(sessionId as string);
      setSession(sessionData);

      if (sessionData.currentStep === 'complete') {
        router.replace({ pathname: '/assessment/results', params: { sessionId } });
        return;
      }

      if (sessionData.currentStep === 'welcome') {
        router.replace({ pathname: '/assessment/welcome', params: { sessionId } });
        return;
      }

      setCurrentStep(sessionData.currentStep);

      // Load local progress
      const localProgress = await assessmentService.loadProgressLocally(sessionId as string);
      if (localProgress) {
        setStepResponses(localProgress.stepResponses || {});
        setCompletedSteps(localProgress.completedSteps || []);
        setProgress(localProgress.progress || 0);
      }

      await loadStepData();
      startAnimations();
    } catch (err) {
      console.error('Error loading initial data:', err);
      Alert.alert('Error', 'Failed to load assessment data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadStepData = async () => {
    try {
      const stepQuestionsFromService = await assessmentService.getAssessmentQuestions(
        sessionId as string,
        currentStep
      );
      
      setQuestions(stepQuestionsFromService.map(q => ({
        ...q,
        questionType: typeof q.questionType === 'string' ? q.questionType : ''
      })));

      setStepResponses((prev) => ({
        ...prev,
        [currentStep]: prev[currentStep] || {
          selectedSymptomValues: [],
          intensity: 'mild',
          reflectionTexts: {},
        },
      }));

      startTimer();
    } catch (err) {
      console.error('Error loading step data:', err);
      Alert.alert('Error', 'Failed to load assessment questions. Please try again.');
    }
  };

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: getProgressPercentage(),
        duration: 1000,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const startTimer = () => {
    stepStartTime.current = Date.now();
  };

  // Enhanced progress ring component
  const renderProgressRing = () => {
    const stepConfig = getCurrentStepConfig();
    const progressPercentage = getProgressPercentage();

    return (
      <View style={styles.progressRingContainer}>
        <Animated.View style={[styles.progressRing, { opacity: fadeAnim }]}>
          <LinearGradient
            colors={stepConfig.gradient}
            style={styles.progressRingGradient}
          >
            <View style={styles.progressRingInner}>
              <MaterialCommunityIcons
                name={stepConfig.icon as any}
                size={32}
                color="white"
              />
              <Text style={styles.progressRingText}>{Math.round(progressPercentage)}%</Text>
            </View>
          </LinearGradient>
        </Animated.View>
        <Text style={styles.progressRingLabel}>Assessment Progress</Text>
      </View>
    );
  };

  // Enhanced step indicator
  const renderStepIndicator = () => {
    return (
      <View style={styles.stepIndicatorContainer}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.stepIndicatorContent}
        >
          {ASSESSMENT_STEPS.map((step, index) => {
            const isCompleted = completedSteps.includes(step.id);
            const isCurrent = step.id === currentStep;
            const isAccessible = index <= ASSESSMENT_STEPS.findIndex(s => s.id === currentStep);

            return (
              <TouchableOpacity
                key={step.id}
                style={[
                  styles.stepIndicatorItem,
                  isCurrent && styles.stepIndicatorItemCurrent,
                  isCompleted && styles.stepIndicatorItemCompleted,
                ]}
                onPress={() => isAccessible && handleJumpToStep(step.id)}
                disabled={!isAccessible}
                activeOpacity={0.7}
              >
                <LinearGradient
                  colors={isCurrent ? step.gradient : isCompleted ? [colors.success, colors.success] : [colors.surface, colors.surface]}
                  style={styles.stepIndicatorGradient}
                >
                  <MaterialCommunityIcons
                    name={isCompleted ? 'check' : step.icon as any}
                    size={20}
                    color={isCurrent || isCompleted ? 'white' : colors.textSecondary}
                  />
                </LinearGradient>
                <Text style={[
                  styles.stepIndicatorText,
                  isCurrent && styles.stepIndicatorTextCurrent,
                  isCompleted && styles.stepIndicatorTextCompleted,
                ]}>
                  {step.title.split(' ')[0]}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  // Enhanced question card component
  const renderQuestionCard = (question: AssessmentQuestion) => {
    const stepConfig = getCurrentStepConfig();

    return (
      <Animated.View
        key={question.id}
        style={[
          styles.questionCard,
          { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }
        ]}
      >
        <LinearGradient
          colors={['white', stepConfig.color + '05']}
          style={styles.questionCardGradient}
        >
          <View style={styles.questionCardHeader}>
            <MaterialCommunityIcons
              name={stepConfig.icon as any}
              size={24}
              color={stepConfig.color}
              style={styles.questionCardIcon}
            />
            <Text style={styles.questionCardTitle}>{question.title}</Text>
          </View>

          {question.description && (
            <Text style={styles.questionCardDescription}>{question.description}</Text>
          )}

          {/* Symptom Selection */}
          {question.questionType?.startsWith('symptom_') && (
            <View style={styles.symptomsContainer}>
              {question.symptoms.map((symptom) => (
                <TouchableOpacity
                  key={symptom.id}
                  style={[
                    styles.symptomCard,
                    stepResponses[currentStep]?.selectedSymptomValues?.includes(symptom.id) && 
                    [styles.symptomCardSelected, { borderColor: stepConfig.color }],
                  ]}
                  onPress={() => handleSymptomSelection(question, symptom.id)}
                  activeOpacity={0.8}
                >
                  <View style={styles.symptomCardContent}>
                    <View style={styles.symptomCardLeft}>
                      <MaterialCommunityIcons
                        name={
                          stepResponses[currentStep]?.selectedSymptomValues?.includes(symptom.id)
                            ? question.questionType === 'symptom_single_choice'
                              ? 'radiobox-marked'
                              : 'checkbox-marked'
                            : question.questionType === 'symptom_single_choice'
                            ? 'radiobox-blank'
                            : 'checkbox-blank-outline'
                        }
                        size={24}
                        color={
                          stepResponses[currentStep]?.selectedSymptomValues?.includes(symptom.id)
                            ? stepConfig.color
                            : colors.textSecondary
                        }
                      />
                      <View style={styles.symptomCardText}>
                        <Text style={[
                          styles.symptomCardTitle,
                          stepResponses[currentStep]?.selectedSymptomValues?.includes(symptom.id) && 
                          { color: stepConfig.color }
                        ]}>
                          {symptom.text}
                        </Text>
                        {symptom.description && (
                          <Text style={styles.symptomCardDescription}>{symptom.description}</Text>
                        )}
                      </View>
                    </View>
                    <View style={[
                      styles.severityBadge,
                      { backgroundColor: getSeverityColor(symptom.severity) + '20' }
                    ]}>
                      <Text style={[
                        styles.severityBadgeText,
                        { color: getSeverityColor(symptom.severity) }
                      ]}>
                        {symptom.severity}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Reflection Input */}
          {question.questionType?.startsWith('reflection_') && (
            <View style={styles.reflectionContainer}>
              <View style={styles.reflectionInputContainer}>
                <MaterialCommunityIcons
                  name="pencil-outline"
                  size={20}
                  color={stepConfig.color}
                  style={styles.reflectionIcon}
                />
                <TextInput
                  style={styles.reflectionInput}
                  value={stepResponses[currentStep]?.reflectionTexts?.[question.id] || ''}
                  onChangeText={(text) => handleReflectionChange(question.id, text)}
                  placeholder={question.reflectionPrompt || "Share your thoughts and feelings here..."}
                  placeholderTextColor={colors.textSecondary + '80'}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
              <Text style={styles.reflectionHelper}>
                Take your time to reflect deeply. Your insights are valuable.
              </Text>
            </View>
          )}
        </LinearGradient>
      </Animated.View>
    );
  };

  // Enhanced intensity selector
  const renderIntensitySelector = () => {
    const stepConfig = getCurrentStepConfig();
    const isSymptomStep = questions.some((q) => q.questionType?.startsWith('symptom_'));

    if (!isSymptomStep) return null;

    return (
      <Animated.View style={[styles.intensityContainer, { opacity: fadeAnim }]}>
        <View style={styles.intensityHeader}>
          <MaterialCommunityIcons
            name="gauge"
            size={24}
            color={stepConfig.color}
          />
          <Text style={styles.intensityTitle}>
            How much do these experiences affect your daily life?
          </Text>
        </View>

        <View style={styles.intensityOptions}>
          {(['mild', 'moderate', 'severe'] as const).map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.intensityOption,
                stepResponses[currentStep]?.intensity === level && [
                  styles.intensityOptionSelected,
                  { backgroundColor: stepConfig.color }
                ],
              ]}
              onPress={() => handleIntensityChange(level)}
              activeOpacity={0.8}
            >
              <MaterialCommunityIcons
                name={
                  level === 'mild' ? 'emoticon-happy-outline' :
                  level === 'moderate' ? 'emoticon-neutral-outline' :
                  'emoticon-sad-outline'
                }
                size={24}
                color={
                  stepResponses[currentStep]?.intensity === level 
                    ? 'white' 
                    : getSeverityColor(level)
                }
              />
              <Text style={[
                styles.intensityOptionText,
                stepResponses[currentStep]?.intensity === level && styles.intensityOptionTextSelected,
              ]}>
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Animated.View>
    );
  };

  // Enhanced Islamic comfort section
  const renderIslamicComfort = () => {
    const stepConfig = getCurrentStepConfig();
    
    const islamicQuotes = [
      "And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose. - Quran 65:3",
      "And it is He who created the heavens and earth in truth. And the day He says, 'Be,' and it is, His word is the truth. - Quran 6:73",
      "And Allah would not punish them while they seek forgiveness. - Quran 8:33",
      "So remember Me; I will remember you. And be grateful to Me and do not deny Me. - Quran 2:152",
      "And whoever fears Allah - He will make for him a way out. - Quran 65:2",
    ];

    const randomQuote = islamicQuotes[Math.floor(Math.random() * islamicQuotes.length)];

    return (
      <Animated.View style={[styles.islamicComfortContainer, { opacity: fadeAnim }]}>
        <LinearGradient
          colors={[colors.lightGold + '20', colors.lightGold + '10']}
          style={styles.islamicComfortGradient}
        >
          <View style={styles.islamicComfortContent}>
            <MaterialCommunityIcons
              name="heart"
              size={24}
              color={colors.islamicGold}
              style={styles.islamicComfortIcon}
            />
            <View style={styles.islamicComfortText}>
              <Text style={styles.islamicComfortQuote}>{randomQuote}</Text>
              <Text style={styles.islamicComfortContext}>{stepConfig.islamicContext}</Text>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  // Event handlers
  const handleSymptomSelection = (question: AssessmentQuestion, symptomId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    const currentSelected = stepResponses[currentStep]?.selectedSymptomValues || [];
    let newSelected: string[];

    if (question.questionType === 'symptom_single_choice') {
      newSelected = currentSelected.includes(symptomId) ? [] : [symptomId];
    } else {
      newSelected = currentSelected.includes(symptomId)
        ? currentSelected.filter(val => val !== symptomId)
        : [...currentSelected, symptomId];
    }

    setStepResponses((prev) => ({
      ...prev,
      [currentStep]: {
        ...(prev[currentStep] || { selectedSymptomValues: [], intensity: 'mild', reflectionTexts: {} }),
        selectedSymptomValues: newSelected,
      },
    }));
  };

  const handleIntensityChange = (intensity: 'mild' | 'moderate' | 'severe') => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    setStepResponses((prev) => ({
      ...prev,
      [currentStep]: {
        ...(prev[currentStep] || { selectedSymptomValues: [], intensity: 'mild', reflectionTexts: {} }),
        intensity,
      },
    }));
  };

  const handleReflectionChange = (questionId: string, text: string) => {
    setStepResponses((prev) => ({
      ...prev,
      [currentStep]: {
        ...(prev[currentStep] || { selectedSymptomValues: [], intensity: 'mild', reflectionTexts: {} }),
        reflectionTexts: { ...(prev[currentStep]?.reflectionTexts || {}), [questionId]: text },
      },
    }));
  };

  const handleNext = async () => {
    try {
      setSubmitting(true);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      const stepTime = Math.floor((Date.now() - stepStartTime.current) / 1000);

      let consolidatedReflection = '';
      const reflectionQuestionIds = questions
        .filter((q) => q.questionType?.startsWith('reflection_'))
        .map((q) => q.id);

      if (reflectionQuestionIds.length > 0) {
        consolidatedReflection = reflectionQuestionIds
          .map((id) => stepResponses[currentStep]?.reflectionTexts?.[id] || '')
          .join('\n\n---\n\n')
          .trim();
      }

      const submissionDataForStep = {
        symptoms: stepResponses[currentStep]?.selectedSymptomValues || [],
        intensity: stepResponses[currentStep]?.intensity || 'mild',
        userReflection: consolidatedReflection,
      };

      const result = await assessmentService.submitAssessmentResponse(
        sessionId as string,
        currentStep,
        submissionDataForStep,
        stepTime
      );

      if (result.crisisDetected) {
        const backendActions = result.emergencyActions || [];
        const componentActions = backendActions.map((action: any) => ({
          id: action.id,
          text: action.text,
          primary: action.primary || false,
          phone: action.phone,
          url: action.url,
        }));
        setCrisisModalData({
          level: result.crisisLevel || 'moderate',
          message: result.message || "It seems you're going through a difficult time. We're here to support you.",
          actions: componentActions,
          urgency: result.urgency || (result.crisisLevel === 'critical' || result.crisisLevel === 'high' ? 'urgent' : 'moderate'),
          indicators: result.crisisIndicators || [],
        });
        setSubmitting(false);
        return;
      }

      setProgress(result.progress);

      await assessmentService.saveProgressLocally(sessionId as string, {
        stepResponses,
        completedSteps: [...new Set([...completedSteps, currentStep])],
        progress: result.progress,
        currentStep: result.nextStep || 'complete',
      });

      if (result.nextStep) {
        setCompletedSteps((prev) => [...new Set([...prev, currentStep])]);
        setStepHistory((prev) => [...prev, currentStep]);
        setCurrentStep(result.nextStep);
        
        // Animate step transition
        Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        await assessmentService.clearProgressLocally(sessionId as string);
        router.push({ pathname: '/assessment/results', params: { sessionId } });
      }
    } catch (err) {
      console.error('Error submitting response:', err);
      Alert.alert('Error', 'Failed to submit response. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleBack = () => {
    if (stepHistory.length > 0) {
      const previousStep = stepHistory[stepHistory.length - 1];
      setStepHistory((prev) => prev.slice(0, -1));
      setCurrentStep(previousStep);
    } else {
      router.back();
    }
  };

  const handleJumpToStep = (stepId: string) => {
    if (stepId === currentStep) return;

    const orderedSteps = ASSESSMENT_STEPS.map(step => step.id);
    const targetIndex = orderedSteps.indexOf(stepId);
    const currentIndex = orderedSteps.indexOf(currentStep);

    if (targetIndex === -1) return;

    if (targetIndex > currentIndex) {
      setStepHistory((prev) => {
        const newHistory = [...prev];
        while (newHistory.length > 0 && orderedSteps.indexOf(newHistory[newHistory.length - 1]) >= currentIndex) {
          newHistory.pop();
        }
        return [...newHistory, currentStep];
      });
    } else {
      setStepHistory((prev) => {
        const newHistory = [...prev];
        while (newHistory.length > 0 && orderedSteps.indexOf(newHistory[newHistory.length - 1]) >= targetIndex) {
          newHistory.pop();
        }
        return newHistory;
      });
    }

    setCurrentStep(stepId);
    setCompletedSteps((prev) => [...new Set([...prev, currentStep])]);
  };

  const handleCrisisAction = (actionId: string) => {
    setCrisisModalData(null);
    switch (actionId) {
      case 'emergency_sakina':
        router.push('/emergency');
        break;
      case 'crisis_counselor':
        router.push('/crisis-counselor' as any);
        break;
      default:
        console.log(`Crisis action chosen: ${actionId}`);
        break;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'mild': return colors.mild;
      case 'moderate': return colors.moderate;
      case 'severe': return colors.severe;
      default: return colors.textSecondary;
    }
  };

  const canProceed = () => {
    const symptomQuestion = questions.find(q => q.questionType?.startsWith('symptom_'));
    if (symptomQuestion && 
        (symptomQuestion.questionType === 'symptom_single_choice' || symptomQuestion.questionType === 'symptom_multi_choice') &&
        (!stepResponses[currentStep]?.selectedSymptomValues || stepResponses[currentStep]?.selectedSymptomValues.length === 0)) {
      return false;
    }

    const reflectionQuestion = questions.find(q => q.questionType?.startsWith('reflection_') && q.reflectionRequired);
    if (reflectionQuestion &&
        (!stepResponses[currentStep]?.reflectionTexts?.[reflectionQuestion.id] ||
         stepResponses[currentStep]?.reflectionTexts[reflectionQuestion.id].trim().length === 0)) {
      return false;
    }

    return true;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={[colors.emerald + '10', colors.lightGold + '10', 'white']}
          style={styles.loadingContainer}
        >
          <View style={styles.loadingContent}>
            <MaterialCommunityIcons 
              name={IslamicIcons.qalb as any} 
              size={64} 
              color={colors.primary} 
            />
            <ActivityIndicator size="large" color={colors.primary} style={styles.loadingSpinner} />
            <Text style={styles.loadingText}>Loading your spiritual assessment...</Text>
            <Text style={styles.loadingSubtext}>
              "And Allah is the best of healers" - Quran 26:80
            </Text>
          </View>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  const stepConfig = getCurrentStepConfig();

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[stepConfig.color + '05', stepConfig.color + '02', 'white']}
        style={styles.gradient}
      >
        {/* Enhanced Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>{stepConfig.title}</Text>
            <Text style={styles.headerSubtitle}>{stepConfig.subtitle}</Text>
          </View>
          <View style={styles.headerRight}>
            <Text style={styles.headerProgress}>{Math.round(getProgressPercentage())}%</Text>
          </View>
        </View>

        {/* Progress Ring */}
        {renderProgressRing()}

        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Main Content */}
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Step Description */}
            <View style={styles.stepDescriptionContainer}>
              <Text style={styles.stepDescription}>{stepConfig.description}</Text>
            </View>

            {/* Questions */}
            {questions.map(renderQuestionCard)}

            {/* Intensity Selector */}
            {renderIntensitySelector()}

            {/* Islamic Comfort */}
            {renderIslamicComfort()}
          </ScrollView>
        </Animated.View>

        {/* Enhanced Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[
              styles.nextButton,
              !canProceed() && styles.nextButtonDisabled,
            ]}
            onPress={handleNext}
            disabled={!canProceed() || submitting}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={canProceed() ? stepConfig.gradient : [colors.disabled, colors.disabled]}
              style={styles.nextButtonGradient}
            >
              {submitting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <>
                  <MaterialCommunityIcons
                    name={currentStep === 'reflections' ? 'check-circle' : 'arrow-right-circle'}
                    size={24}
                    color="white"
                  />
                  <Text style={styles.nextButtonText}>
                    {currentStep === 'reflections' ? 'Complete Assessment' : 'Continue'}
                  </Text>
                </>
              )}
            </LinearGradient>
          </TouchableOpacity>

          {questions.some((q) => !q.reflectionRequired) && currentStep !== 'reflections' && (
            <TouchableOpacity style={styles.skipButton} onPress={handleNext}>
              <Text style={styles.skipButtonText}>Skip this section</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Crisis Modal */}
        <CrisisModal
          visible={!!crisisModalData}
          crisisData={crisisModalData}
          onAction={handleCrisisAction}
          onClose={() => setCrisisModalData(null)}
        />
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    flex: 1,
  },
  
  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    padding: 32,
  },
  loadingSpinner: {
    marginVertical: 20,
  },
  loadingText: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Header styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderBottomWidth: 1,
    borderBottomColor: colors.border + '30',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    ...Theme.shadows.small,
  },
  headerContent: {
    flex: 1,
    marginLeft: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 2,
  },
  headerRight: {
    alignItems: 'center',
  },
  headerProgress: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
  },

  // Progress ring
  progressRingContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  progressRing: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
    ...Theme.shadows.medium,
  },
  progressRingGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressRingInner: {
    alignItems: 'center',
  },
  progressRingText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
    marginTop: 4,
  },
  progressRingLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginTop: 8,
  },

  // Step indicator
  stepIndicatorContainer: {
    marginBottom: 20,
  },
  stepIndicatorContent: {
    paddingHorizontal: 20,
  },
  stepIndicatorItem: {
    alignItems: 'center',
    marginRight: 16,
  },
  stepIndicatorItemCurrent: {},
  stepIndicatorItemCompleted: {},
  stepIndicatorGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    ...Theme.shadows.small,
  },
  stepIndicatorText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginTop: 4,
    textAlign: 'center',
  },
  stepIndicatorTextCurrent: {
    color: colors.primary,
    fontWeight: '600',
  },
  stepIndicatorTextCompleted: {
    color: colors.success,
    fontWeight: '600',
  },

  // Content
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 120,
  },

  // Step description
  stepDescriptionContainer: {
    marginBottom: 24,
  },
  stepDescription: {
    fontSize: 18,
    fontWeight: '500',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 26,
  },

  // Question cards
  questionCard: {
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    ...Theme.shadows.medium,
  },
  questionCardGradient: {
    padding: 20,
  },
  questionCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionCardIcon: {
    marginRight: 12,
  },
  questionCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
  },
  questionCardDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
    lineHeight: 20,
  },

  // Symptoms
  symptomsContainer: {
    gap: 12,
  },
  symptomCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: colors.border,
    ...Theme.shadows.small,
  },
  symptomCardSelected: {
    backgroundColor: colors.primary + '10',
    borderWidth: 2,
  },
  symptomCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  symptomCardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  symptomCardText: {
    marginLeft: 12,
    flex: 1,
  },
  symptomCardTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  symptomCardDescription: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 2,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  severityBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },

  // Reflection
  reflectionContainer: {
    marginTop: 16,
  },
  reflectionInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
    ...Theme.shadows.small,
  },
  reflectionIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  reflectionInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  reflectionHelper: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 8,
    fontStyle: 'italic',
  },

  // Intensity selector
  intensityContainer: {
    marginTop: 24,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    ...Theme.shadows.medium,
  },
  intensityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  intensityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 12,
    flex: 1,
  },
  intensityOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  intensityOption: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: colors.surface,
    borderWidth: 2,
    borderColor: colors.border,
  },
  intensityOptionSelected: {
    borderColor: 'transparent',
  },
  intensityOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginTop: 8,
  },
  intensityOptionTextSelected: {
    color: 'white',
  },

  // Islamic comfort
  islamicComfortContainer: {
    marginTop: 24,
    borderRadius: 16,
    overflow: 'hidden',
    ...Theme.shadows.medium,
  },
  islamicComfortGradient: {
    padding: 20,
  },
  islamicComfortContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  islamicComfortIcon: {
    marginRight: 16,
    marginTop: 2,
  },
  islamicComfortText: {
    flex: 1,
  },
  islamicComfortQuote: {
    fontSize: 16,
    color: colors.text,
    fontStyle: 'italic',
    lineHeight: 24,
    marginBottom: 8,
  },
  islamicComfortContext: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },

  // Actions
  actionsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderTopWidth: 1,
    borderTopColor: colors.border + '30',
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
  },
  nextButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 12,
    ...Theme.shadows.large,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
  },
  nextButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginLeft: 12,
  },
  skipButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  skipButtonText: {
    fontSize: 14,
    color: colors.textSecondary,
    textDecorationLine: 'underline',
  },
});