/**
 * Enhanced Diagnosis Results Screen - Visually Stunning Islamic Design
 * Combines comprehensive functionality with beautiful UI enhancements
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Animated,
  Share,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons, Feather } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { assessmentService } from '../../services/assessment.service';
import { colors } from '../../constants/Colors';
import Theme from '../../constants/Theme';
import InsightCard from '../../components/ui/InsightCard';
import CustomRadarChart from '../../components/charts/RadarChart';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import { ConfirmationModal } from '../../components/ui/ConfirmationModal';
import { mockDiagnosis } from '../../services/mockSymptomsService';
import { IslamicIcons } from '../../constants/IslamicIcons';

const { width, height } = Dimensions.get('window');

// Enhanced interfaces with visual data
interface AILayerAnalysisOutput {
  layer: string;
  insights: string[];
  recommendations: string[];
  islamic_context: string;
  severity_score: number;
}

interface AISpiritualLandscapeResponse {
  id?: string;
  primary_layer: string;
  layer_insights: Record<string, AILayerAnalysisOutput>;
  personalized_message: string;
  islamic_insights: string[];
  educational_content: string;
  crisis_level: string;
  crisis_indicators: string[];
  immediate_actions: string[];
  next_steps: string[];
  recommended_journey_type: string;
  estimated_healing_duration: number;
  confidence: number;
  overallSeverity?: 'mild' | 'moderate' | 'severe' | 'critical';
}

interface DiagnosisDelivery {
  userId: string;
  userType: string;
  diagnosis: AISpiritualLandscapeResponse;
  deliveryStyle: 'gentle' | 'clinical' | 'advanced' | 'traditional';
  layerIntroduction: string;
}

// Enhanced layer visualization data using centralized colors
const LAYER_VISUAL_CONFIG = {
  jism: {
    name: 'Jism (Body)',
    icon: 'account-heart-outline',
    color: colors.jism,
    gradient: [colors.jism, colors.jism + '80'],
    description: 'Physical manifestations and bodily experiences',
  },
  nafs: {
    name: 'Nafs (Emotions)',
    icon: 'fire',
    color: colors.nafs,
    gradient: [colors.nafs, colors.nafs + '80'],
    description: 'Emotional states and desires of the self',
  },
  aql: {
    name: 'Aql (Mind)',
    icon: 'brain',
    color: colors.aql,
    gradient: [colors.aql, colors.aql + '80'],
    description: 'Mental processes and intellectual understanding',
  },
  qalb: {
    name: 'Qalb (Heart)',
    icon: 'heart-pulse',
    color: colors.qalb,
    gradient: [colors.qalb, colors.qalb + '80'],
    description: 'Spiritual heart and divine connection',
  },
  ruh: {
    name: 'Ruh (Soul)',
    icon: 'star-four-points-outline',
    color: colors.ruh,
    gradient: [colors.ruh, colors.ruh + '80'],
    description: 'Divine essence and spiritual reality',
  },
};

export default function EnhancedDiagnosisResultsScreen() {
  const { sessionId, diagnosis: diagnosisParam } = useLocalSearchParams();

  // State management
  const [deliveryData, setDeliveryData] = useState<DiagnosisDelivery | null>(null);
  const [mockData, setMockData] = useState<any>(null);
  const [useMockData, setUseMockData] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showRetakeModal, setShowRetakeModal] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['hero', 'primary', 'insights'])
  );

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadDiagnosisData();
  }, [sessionId, diagnosisParam]);

  const loadDiagnosisData = async () => {
    try {
      setLoading(true);

      // Check if diagnosis was passed as parameter
      if (diagnosisParam) {
        try {
          const parsedDiagnosis = JSON.parse(diagnosisParam as string);
          setMockData(parsedDiagnosis);
          setUseMockData(true);
          startAnimations();
          return;
        } catch (parseError) {
          console.warn('Failed to parse diagnosis parameter');
        }
      }

      // Try to load from session or use mock data
      if (!sessionId) {
        setMockData(mockDiagnosis);
        setUseMockData(true);
        startAnimations();
        return;
      }

      try {
        const session = await assessmentService.getSession(sessionId as string);
        if (session.currentStep !== 'complete') {
          if (Platform.OS === 'web') {
            alert('Assessment Incomplete. Please complete the assessment first.');
          } else {
            Alert.alert('Assessment Incomplete', 'Please complete the assessment first.');
          }
          return;
        }

        const result = await assessmentService.getDiagnosis(sessionId as string);
        setDeliveryData(result.delivery);
        setUseMockData(false);
      } catch (error) {
        console.warn('Service failed, using mock data:', error);
        setMockData(mockDiagnosis);
        setUseMockData(true);
      }

      startAnimations();
    } catch (err) {
      console.error('Error loading diagnosis:', err);
      setMockData(mockDiagnosis);
      setUseMockData(true);
      startAnimations();
    } finally {
      setLoading(false);
    }
  };

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 1200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const toggleSection = (sectionId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) newSet.delete(sectionId);
      else newSet.add(sectionId);
      return newSet;
    });
  };

  const handleShare = async () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }

    const shareContent = useMockData && mockData
      ? `My Spiritual Diagnosis from Qalb Healing:\n${mockData.diagnosisData.ai_response.personalized_message}\n\nDownload Qalb Healing to start your own spiritual assessment.`
      : deliveryData?.diagnosis
      ? `My Spiritual Diagnosis from Qalb Healing:\nPrimary Layer: ${deliveryData.diagnosis.primary_layer}\nKey Insights: ${deliveryData.diagnosis.islamic_insights.slice(0, 2).join('\n')}\n\nDownload Qalb Healing to start your own spiritual assessment.`
      : 'Check out Qalb Healing for Islamic mental wellness assessment.';

    try {
      await Share.share({ message: shareContent, title: 'My Spiritual Diagnosis' });
    } catch (err) {
      console.error('Error sharing:', err);
    }
  };

  const handleStartJourney = () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }

    const params = useMockData
      ? {
          assessmentId: 'mock-assessment',
          aiRecommendedDuration: '21',
          aiRecommendedJourneyType: 'heart_purification',
          aiRecommendationReason: 'Based on your spiritual assessment results.',
        }
      : {
          assessmentId: Array.isArray(sessionId) ? sessionId[0] : sessionId,
          aiRecommendedDuration: deliveryData?.diagnosis.estimated_healing_duration?.toString() || '21',
          aiRecommendedJourneyType: deliveryData?.diagnosis.recommended_journey_type || 'heart_purification',
          aiRecommendationReason: deliveryData?.diagnosis.personalized_message || 'Based on your assessment.',
        };

    router.push({ pathname: '/journey/select', params });
  };

  const handleRetakeAssessment = () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    setShowRetakeModal(true);
  };

  const handleConfirmRetake = async () => {
    setShowRetakeModal(false);
    try {
      setLoading(true);
      
      // Clear any existing local progress if we have a sessionId
      if (sessionId && !useMockData) {
        await assessmentService.clearProgressLocally(sessionId as string);
      }
      
      // Start a new assessment session
      const result = await assessmentService.startAssessment();
      
      // Navigate to the welcome screen with the new session
      router.replace({
        pathname: '/assessment/welcome',
        params: { sessionId: result.session.id }
      });
    } catch (error) {
      console.error('Error starting new assessment:', error);
      // For web compatibility, we'll need to handle this differently
      if (Platform.OS === 'web') {
        alert('Failed to start new assessment. Please try again.');
      } else {
        Alert.alert(
          'Error',
          'Failed to start new assessment. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancelRetake = () => {
    setShowRetakeModal(false);
  };

  const getSeverityColor = (severity?: string) => {
    switch (severity?.toLowerCase()) {
      case 'mild': return colors.mild;
      case 'moderate': return colors.moderate;
      case 'severe': return colors.severe;
      case 'critical': return colors.critical;
      default: return colors.textSecondary;
    }
  };

  const getSeverityIcon = (severity?: string) => {
    switch (severity?.toLowerCase()) {
      case 'mild': return 'checkmark-circle-outline';
      case 'moderate': return 'information-circle-outline';
      case 'severe': return 'alert-circle-outline';
      case 'critical': return 'warning-outline';
      default: return 'help-circle-outline';
    }
  };

  const formatLayerName = (layerKey: string) => 
    layerKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Enhanced Hero Section Component
  const renderHeroSection = () => {
    const message = useMockData && mockData
      ? mockData.diagnosisData.ai_response.personalized_message
      : deliveryData?.diagnosis.personalized_message || 'Your spiritual journey begins with understanding.';

    return (
      <Animated.View 
        style={[
          styles.heroSection,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
          }
        ]}
      >
        <LinearGradient
          colors={[colors.emerald + '20', colors.lightGold + '20', 'white']}
          style={styles.heroGradient}
        >
          <View style={styles.heroContent}>
            <View style={styles.heroIcon}>
              <MaterialCommunityIcons 
                name={IslamicIcons.qalb as any} 
                size={48} 
                color={colors.primary} 
              />
            </View>
            <Text style={styles.heroTitle}>Your Spiritual Diagnosis</Text>
            <Text style={styles.heroSubtitle}>
              "And whoever relies upon Allah - then He is sufficient for him"
            </Text>
            <View style={styles.heroMessage}>
              <Text style={styles.heroMessageText}>{message}</Text>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  // Enhanced Layer Visualization Component
  const renderLayerVisualization = () => {
    const layerData = useMockData && mockData
      ? mockData.layerAnalyses.map((l: any) => ({
          value: l.impactScore,
          label: l.layerName.charAt(0).toUpperCase() + l.layerName.slice(1),
        }))
      : deliveryData?.diagnosis.layer_insights
      ? Object.entries(deliveryData.diagnosis.layer_insights).map(([key, value]) => ({
          value: value.severity_score,
          label: formatLayerName(value.layer),
        }))
      : [];

    return (
      <View style={styles.visualizationSection}>
        <TouchableOpacity 
          style={styles.sectionHeader}
          onPress={() => toggleSection('visualization')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitleContainer}>
            <MaterialCommunityIcons 
              name="chart-donut" 
              size={24} 
              color={colors.primary} 
              style={styles.sectionIcon}
            />
            <Text style={styles.sectionTitle}>Five-Layer Analysis</Text>
          </View>
          <Ionicons 
            name={expandedSections.has('visualization') ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={colors.textSecondary}
          />
        </TouchableOpacity>

        {expandedSections.has('visualization') && (
          <Animated.View style={[styles.sectionContent, { opacity: fadeAnim }]}>
            <View style={styles.chartContainer}>
              <CustomRadarChart data={layerData} />
            </View>
            <Text style={styles.chartDescription}>
              This visualization shows the impact levels across the five Islamic layers of human existence.
            </Text>
          </Animated.View>
        )}
      </View>
    );
  };

  // Enhanced Layer Cards Component
  const renderLayerCards = () => {
    const layers = useMockData && mockData
      ? mockData.layerAnalyses
      : deliveryData?.diagnosis.layer_insights
      ? Object.entries(deliveryData.diagnosis.layer_insights)
          .sort(([, a], [, b]) => b.severity_score - a.severity_score)
          .map(([key, value]) => ({
            id: key,
            layerName: value.layer,
            impactScore: value.severity_score,
            priority: key === deliveryData.diagnosis.primary_layer ? 'primary' : 'secondary',
            insights: value.insights,
            recommendations: value.recommendations,
            islamicContext: value.islamic_context,
          }))
      : [];

    return (
      <View style={styles.layerCardsSection}>
        <Text style={styles.sectionMainTitle}>Detailed Layer Analysis</Text>
        {layers.map((layer: any, index: number) => (
          <Animated.View
            key={layer.id || index}
            style={[
              styles.layerCard,
              {
                opacity: fadeAnim,
                transform: [{
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 50],
                    outputRange: [0, 50 + (index * 20)],
                  })
                }]
              }
            ]}
          >
            <TouchableOpacity
              onPress={() => toggleSection(`layer-${layer.id || index}`)}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={LAYER_VISUAL_CONFIG[layer.layerName as keyof typeof LAYER_VISUAL_CONFIG]?.gradient || [colors.primary, colors.primaryLight]}
                style={styles.layerCardHeader}
              >
                <View style={styles.layerCardHeaderContent}>
                  <MaterialCommunityIcons
                    name={LAYER_VISUAL_CONFIG[layer.layerName as keyof typeof LAYER_VISUAL_CONFIG]?.icon as any || 'circle'}
                    size={32}
                    color="white"
                  />
                  <View style={styles.layerCardHeaderText}>
                    <Text style={styles.layerCardTitle}>
                      {LAYER_VISUAL_CONFIG[layer.layerName as keyof typeof LAYER_VISUAL_CONFIG]?.name || layer.layerName}
                    </Text>
                    <Text style={styles.layerCardSubtitle}>
                      {layer.priority === 'primary' ? 'Primary Focus' : 'Secondary Focus'} • Score: {layer.impactScore}
                    </Text>
                  </View>
                  <View style={styles.layerCardBadge}>
                    <Text style={styles.layerCardBadgeText}>{layer.impactScore}</Text>
                  </View>
                </View>
              </LinearGradient>

              {expandedSections.has(`layer-${layer.id || index}`) && (
                <View style={styles.layerCardContent}>
                  <View style={styles.layerSection}>
                    <Text style={styles.layerSectionTitle}>Key Insights</Text>
                    {layer.insights.map((insight: string, idx: number) => (
                      <View key={idx} style={styles.insightItem}>
                        <MaterialCommunityIcons 
                          name="lightbulb-on-outline" 
                          size={16} 
                          color={colors.primary} 
                        />
                        <Text style={styles.insightText}>{insight}</Text>
                      </View>
                    ))}
                  </View>

                  <View style={styles.layerSection}>
                    <Text style={styles.layerSectionTitle}>Recommendations</Text>
                    {layer.recommendations.map((rec: string, idx: number) => (
                      <View key={idx} style={styles.recommendationItem}>
                        <MaterialCommunityIcons 
                          name="arrow-right-circle-outline" 
                          size={16} 
                          color={colors.secondary} 
                        />
                        <Text style={styles.recommendationText}>{rec}</Text>
                      </View>
                    ))}
                  </View>

                  <View style={styles.islamicContextSection}>
                    <Text style={styles.layerSectionTitle}>Islamic Context</Text>
                    <View style={styles.islamicContextCard}>
                      <MaterialCommunityIcons 
                        name="book-open-page-variant-outline" 
                        size={20} 
                        color={colors.islamicGold} 
                      />
                      <Text style={styles.islamicContextText}>{layer.islamicContext}</Text>
                    </View>
                  </View>
                </View>
              )}
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>
    );
  };

  // Enhanced Islamic Insights Component
  const renderIslamicInsights = () => {
    const insights = useMockData && mockData
      ? mockData.diagnosisData.ai_response.islamic_insights
      : deliveryData?.diagnosis.islamic_insights || [];

    if (!insights.length) return null;

    return (
      <View style={styles.islamicInsightsSection}>
        <TouchableOpacity 
          style={styles.sectionHeader}
          onPress={() => toggleSection('insights')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitleContainer}>
            <MaterialCommunityIcons 
              name={IslamicIcons.quran as any} 
              size={24} 
              color={colors.islamicGold} 
              style={styles.sectionIcon}
            />
            <Text style={styles.sectionTitle}>Islamic Wisdom & Guidance</Text>
          </View>
          <Ionicons 
            name={expandedSections.has('insights') ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={colors.textSecondary}
          />
        </TouchableOpacity>

        {expandedSections.has('insights') && (
          <Animated.View style={[styles.sectionContent, { opacity: fadeAnim }]}>
            {insights.map((insight: string, index: number) => (
              <InsightCard
                key={index}
                icon="quran"
                insight={insight}
                islamicContext="Guidance from Islamic teachings"
                actionable="Reflect and implement in your daily life"
              />
            ))}
          </Animated.View>
        )}
      </View>
    );
  };

  // Enhanced Next Steps Component
  const renderNextSteps = () => {
    const steps = useMockData && mockData
      ? mockData.nextSteps
      : deliveryData?.diagnosis.next_steps || [];

    if (!steps.length) return null;

    return (
      <View style={styles.nextStepsSection}>
        <TouchableOpacity 
          style={styles.sectionHeader}
          onPress={() => toggleSection('nextsteps')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitleContainer}>
            <MaterialCommunityIcons 
              name="map-marker-path" 
              size={24} 
              color={colors.primary} 
              style={styles.sectionIcon}
            />
            <Text style={styles.sectionTitle}>Your Healing Roadmap</Text>
          </View>
          <Ionicons 
            name={expandedSections.has('nextsteps') ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={colors.textSecondary}
          />
        </TouchableOpacity>

        {expandedSections.has('nextsteps') && (
          <Animated.View style={[styles.sectionContent, { opacity: fadeAnim }]}>
            {steps.map((step: string, index: number) => (
              <View key={index} style={styles.nextStepItem}>
                <View style={styles.stepNumberContainer}>
                  <LinearGradient
                    colors={[colors.primary, colors.primaryLight]}
                    style={styles.stepNumber}
                  >
                    <Text style={styles.stepNumberText}>{index + 1}</Text>
                  </LinearGradient>
                </View>
                <View style={styles.stepContent}>
                  <Text style={styles.stepText}>{step}</Text>
                </View>
              </View>
            ))}
          </Animated.View>
        )}
      </View>
    );
  };

  // Enhanced Action Buttons Component
  const renderActionButtons = () => (
    <Animated.View 
      style={[
        styles.actionsContainer,
        { opacity: fadeAnim, transform: [{ scale: scaleAnim }] }
      ]}
    >
      <TouchableOpacity 
        style={styles.primaryActionButton} 
        onPress={handleStartJourney}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={[colors.primary, colors.primaryLight]}
          style={styles.primaryActionGradient}
        >
          <MaterialCommunityIcons name="compass-outline" size={24} color="white" />
          <Text style={styles.primaryActionText}>Begin Your Healing Journey</Text>
          <Ionicons name="arrow-forward" size={20} color="white" />
        </LinearGradient>
      </TouchableOpacity>

      <View style={styles.secondaryActions}>
        <TouchableOpacity style={styles.secondaryActionButton} onPress={handleShare}>
          <Feather name="share-2" size={20} color={colors.primary} />
          <Text style={styles.secondaryActionText}>Share Results</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.secondaryActionButton} 
          onPress={handleRetakeAssessment}
        >
          <Feather name="refresh-cw" size={20} color={colors.primary} />
          <Text style={styles.secondaryActionText}>Retake Assessment</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );

  // Loading Component
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={[colors.emerald + '10', colors.lightGold + '10', 'white']}
          style={styles.loadingContainer}
        >
          <View style={styles.loadingContent}>
            <MaterialCommunityIcons 
              name={IslamicIcons.qalb as any} 
              size={64} 
              color={colors.primary} 
            />
            <ActivityIndicator size="large" color={colors.primary} style={styles.loadingSpinner} />
            <Text style={styles.loadingText}>Preparing Your Spiritual Diagnosis...</Text>
            <Text style={styles.loadingSubtext}>
              "And Allah is the best of healers" - Quran 26:80
            </Text>
          </View>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <ErrorBoundary>
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={[colors.emerald + '05', colors.lightGold + '05', 'white']}
          style={styles.gradient}
        >
          {/* Enhanced Header */}
          <View style={styles.header}>
            <TouchableOpacity 
              style={styles.headerButton} 
              onPress={() => router.back()}
              activeOpacity={0.7}
            >
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Spiritual Diagnosis</Text>
            <TouchableOpacity 
              style={styles.headerButton} 
              onPress={handleShare}
              activeOpacity={0.7}
            >
              <Feather name="share-2" size={22} color={colors.primary} />
            </TouchableOpacity>
          </View>

          <ScrollView 
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {renderHeroSection()}
            {renderLayerVisualization()}
            {renderLayerCards()}
            {renderIslamicInsights()}
            {renderNextSteps()}
          </ScrollView>

          {renderActionButtons()}

          {/* Retake Assessment Confirmation Modal */}
          <ConfirmationModal
            visible={showRetakeModal}
            title="Retake Assessment"
            message="This will start a new assessment and your current results will remain saved. Are you sure you want to continue?"
            confirmText="Start New Assessment"
            cancelText="Cancel"
            onConfirm={handleConfirmRetake}
            onCancel={handleCancelRetake}
            isDestructive={false}
          />
        </LinearGradient>
      </SafeAreaView>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    flex: 1,
  },
  
  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    padding: 32,
  },
  loadingSpinner: {
    marginVertical: 20,
  },
  loadingText: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Header styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderBottomWidth: 1,
    borderBottomColor: colors.border + '30',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    ...Theme.shadows.small,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
  },

  // Scroll view
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },

  // Hero section
  heroSection: {
    margin: 20,
    borderRadius: 20,
    overflow: 'hidden',
    ...Theme.shadows.large,
  },
  heroGradient: {
    padding: 32,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    ...Theme.shadows.medium,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: 20,
    textAlign: 'center',
  },
  heroMessage: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 20,
    ...Theme.shadows.small,
  },
  heroMessageText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
    textAlign: 'center',
  },

  // Section styles
  visualizationSection: {
    margin: 20,
    marginTop: 0,
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    ...Theme.shadows.medium,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: colors.surface,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionIcon: {
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
  },
  sectionMainTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: colors.text,
    marginHorizontal: 20,
    marginBottom: 16,
  },
  sectionContent: {
    padding: 20,
    paddingTop: 0,
  },

  // Chart container
  chartContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  chartDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 20,
  },

  // Layer cards
  layerCardsSection: {
    marginTop: 20,
  },
  layerCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: 'white',
    ...Theme.shadows.medium,
  },
  layerCardHeader: {
    padding: 20,
  },
  layerCardHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  layerCardHeaderText: {
    flex: 1,
    marginLeft: 16,
  },
  layerCardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginBottom: 4,
  },
  layerCardSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  layerCardBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  layerCardBadgeText: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
  },
  layerCardContent: {
    padding: 20,
  },
  layerSection: {
    marginBottom: 20,
  },
  layerSectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 12,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  insightText: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
    marginLeft: 8,
    flex: 1,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
    marginLeft: 8,
    flex: 1,
  },
  islamicContextSection: {
    marginTop: 8,
  },
  islamicContextCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.lightGold + '20',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: colors.islamicGold,
  },
  islamicContextText: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
    marginLeft: 12,
    flex: 1,
    fontStyle: 'italic',
  },

  // Islamic insights
  islamicInsightsSection: {
    margin: 20,
    marginTop: 0,
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    ...Theme.shadows.medium,
  },

  // Next steps
  nextStepsSection: {
    margin: 20,
    marginTop: 0,
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    ...Theme.shadows.medium,
  },
  nextStepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  stepNumberContainer: {
    marginRight: 16,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepNumberText: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
  },
  stepContent: {
    flex: 1,
    paddingTop: 4,
  },
  stepText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },

  // Actions
  actionsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderTopWidth: 1,
    borderTopColor: colors.border + '30',
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
  },
  primaryActionButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 12,
    ...Theme.shadows.large,
  },
  primaryActionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
  },
  primaryActionText: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginHorizontal: 12,
  },
  secondaryActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  secondaryActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
    marginHorizontal: 4,
    ...Theme.shadows.small,
  },
  secondaryActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 8,
  },
});