/**
 * Journey Reflection Screen
 * Daily reflection and journaling interface for spiritual growth tracking
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import journeyService from '../../services/journey.service';
import { Journey, JourneyProgress } from '../../../libs/shared-types/src/lib/journey.types';

const { width } = Dimensions.get('window');

interface ReflectionData {
  dailyReflection: string;
  gratitude: string[];
  challenges: string[];
  insights: string[];
  moodAfter: number;
  spiritualConnection: number;
  stressLevel: number;
  overallRating: number;
}

interface ReflectionPrompt {
  id: string;
  question: string;
  placeholder: string;
  type: 'text' | 'list' | 'rating';
  category: 'reflection' | 'gratitude' | 'challenges' | 'insights' | 'mood';
}

const JourneyReflectionScreen = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const journeyId = params.journeyId as string;
  const dayNumber = parseInt(params.dayNumber as string) || 1;

  const [journey, setJourney] = useState<Journey | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [reflectionData, setReflectionData] = useState<ReflectionData>({
    dailyReflection: '',
    gratitude: [''],
    challenges: [''],
    insights: [''],
    moodAfter: 5,
    spiritualConnection: 5,
    stressLevel: 5,
    overallRating: 5,
  });

  const reflectionPrompts: ReflectionPrompt[] = [
    {
      id: 'daily_reflection',
      question: 'How did today\'s practices affect your spiritual state?',
      placeholder: 'Reflect on your spiritual journey today. What did you learn about yourself? How did the practices make you feel?',
      type: 'text',
      category: 'reflection',
    },
    {
      id: 'gratitude',
      question: 'What are you grateful for today? (Alhamdulillah)',
      placeholder: 'List things you\'re thankful to Allah for today',
      type: 'list',
      category: 'gratitude',
    },
    {
      id: 'challenges',
      question: 'What challenges did you face in your spiritual practices?',
      placeholder: 'Describe any difficulties or obstacles you encountered',
      type: 'list',
      category: 'challenges',
    },
    {
      id: 'insights',
      question: 'What insights or realizations did you gain?',
      placeholder: 'Share any spiritual insights or personal discoveries',
      type: 'list',
      category: 'insights',
    },
  ];

  useEffect(() => {
    if (!journeyId) {
      Alert.alert("Error", "Journey ID is missing.");
      router.back();
      return;
    }
    loadJourneyData();
  }, [journeyId]);

  const loadJourneyData = async () => {
    try {
      setIsLoading(true);
      // Use proper REST endpoint for journey details
      const journeyData = await journeyService.getJourneyById(journeyId);
      
      if (!journeyData) {
        Alert.alert("Error", "Journey not found. Please ensure you have an active journey.");
        router.back();
        return;
      }

      setJourney(journeyData);

      // Try to load existing reflection for today
      try {
        const progressData = await journeyService.getJourneyProgress(journeyId);
        const todayProgress = progressData.progressHistory?.find(
          p => p.dayNumber === dayNumber
        );

        if (todayProgress) {
          setReflectionData({
            dailyReflection: todayProgress.dailyReflection || '',
            gratitude: todayProgress.gratitude || [''],
            challenges: todayProgress.challenges || [''],
            insights: todayProgress.insights || [''],
            moodAfter: todayProgress.moodAfter || 5,
            spiritualConnection: todayProgress.spiritualConnection || 5,
            stressLevel: todayProgress.stressLevel || 5,
            overallRating: todayProgress.overallRating || 5,
          });
        }
      } catch (progressError) {
        console.log('No existing progress found, starting fresh');
      }
    } catch (error) {
      console.error('Error loading journey data:', error);
      Alert.alert('Error', 'Failed to load journey data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const updateReflectionData = (field: keyof ReflectionData, value: any) => {
    setReflectionData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const updateListItem = (category: 'gratitude' | 'challenges' | 'insights', index: number, value: string) => {
    setReflectionData(prev => ({
      ...prev,
      [category]: prev[category].map((item, i) => i === index ? value : item),
    }));
  };

  const addListItem = (category: 'gratitude' | 'challenges' | 'insights') => {
    setReflectionData(prev => ({
      ...prev,
      [category]: [...prev[category], ''],
    }));
  };

  const removeListItem = (category: 'gratitude' | 'challenges' | 'insights', index: number) => {
    if (reflectionData[category].length > 1) {
      setReflectionData(prev => ({
        ...prev,
        [category]: prev[category].filter((_, i) => i !== index),
      }));
    }
  };

  const handleSaveReflection = async () => {
    if (!journey) return;

    // Validate required fields
    if (!reflectionData.dailyReflection.trim()) {
      Alert.alert('Incomplete', 'Please write your daily reflection before saving.');
      return;
    }

    try {
      setIsSaving(true);

      // Filter out empty items from lists
      const cleanedData = {
        ...reflectionData,
        gratitude: reflectionData.gratitude.filter(item => item.trim() !== ''),
        challenges: reflectionData.challenges.filter(item => item.trim() !== ''),
        insights: reflectionData.insights.filter(item => item.trim() !== ''),
      };

      // Create progress data
      const progressData = {
        journeyId: journey.id,
        userId: journey.userId,
        dayNumber,
        date: new Date().toISOString(),
        ...cleanedData,
        practicesCompleted: [], // This would be filled from daily practices
      };

      await journeyService.recordDailyProgress(progressData);
      
      Alert.alert(
        'Reflection Saved',
        'Your daily reflection has been saved successfully. May Allah bless your spiritual journey.',
        [
          {
            text: 'Continue',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error('Error saving reflection:', error);
      Alert.alert('Error', 'Failed to save your reflection. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const renderRatingSelector = (
    title: string,
    value: number,
    onValueChange: (value: number) => void,
    lowLabel: string,
    highLabel: string
  ) => (
    <View style={styles.ratingContainer}>
      <Text style={styles.ratingTitle}>{title}</Text>
      <View style={styles.ratingScale}>
        <Text style={styles.ratingLabel}>{lowLabel}</Text>
        <View style={styles.ratingButtons}>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(rating => (
            <TouchableOpacity
              key={rating}
              style={[
                styles.ratingButton,
                value === rating && styles.ratingButtonActive,
              ]}
              onPress={() => onValueChange(rating)}
            >
              <Text
                style={[
                  styles.ratingButtonText,
                  value === rating && styles.ratingButtonTextActive,
                ]}
              >
                {rating}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        <Text style={styles.ratingLabel}>{highLabel}</Text>
      </View>
    </View>
  );

  const renderListInput = (
    category: 'gratitude' | 'challenges' | 'insights',
    prompt: ReflectionPrompt
  ) => (
    <View style={styles.inputSection}>
      <Text style={styles.promptQuestion}>{prompt.question}</Text>
      {reflectionData[category].map((item, index) => (
        <View key={index} style={styles.listItemContainer}>
          <TextInput
            style={styles.listInput}
            value={item}
            onChangeText={(text) => updateListItem(category, index, text)}
            placeholder={`${prompt.placeholder} ${index + 1}`}
            placeholderTextColor="rgba(255, 255, 255, 0.5)"
            multiline
          />
          {reflectionData[category].length > 1 && (
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeListItem(category, index)}
            >
              <Ionicons name="close-circle" size={20} color="#FF6B6B" />
            </TouchableOpacity>
          )}
        </View>
      ))}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => addListItem(category)}
      >
        <Ionicons name="add-circle" size={20} color="#4a90a4" />
        <Text style={styles.addButtonText}>Add another</Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading reflection...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (!journey) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color="#ffffff" />
          <Text style={styles.errorTitle}>Journey Not Found</Text>
          <Text style={styles.errorText}>
            We couldn't find your journey. Please try again.
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: 'Daily Reflection', headerShown: false }} />
      <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.gradient}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Daily Reflection</Text>
            <Text style={styles.headerSubtitle}>Day {dayNumber} • {journey.title}</Text>
          </View>
          <TouchableOpacity
            style={[styles.saveButton, isSaving && styles.saveButtonDisabled]}
            onPress={handleSaveReflection}
            disabled={isSaving}
          >
            {isSaving ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <Ionicons name="checkmark" size={24} color="#ffffff" />
            )}
          </TouchableOpacity>
        </View>

        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            style={styles.content}
            contentContainerStyle={styles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            {/* Islamic Greeting */}
            <View style={styles.greetingSection}>
              <Text style={styles.greetingText}>بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيم</Text>
              <Text style={styles.greetingTranslation}>
                "In the name of Allah, the Most Gracious, the Most Merciful"
              </Text>
              <Text style={styles.reflectionIntro}>
                Take a moment to reflect on your spiritual journey today. 
                Your honest reflection helps track your growth and strengthens your connection with Allah.
              </Text>
            </View>

            {/* Daily Reflection */}
            <View style={styles.inputSection}>
              <Text style={styles.promptQuestion}>
                {reflectionPrompts[0].question}
              </Text>
              <TextInput
                style={styles.textInput}
                value={reflectionData.dailyReflection}
                onChangeText={(text) => updateReflectionData('dailyReflection', text)}
                placeholder={reflectionPrompts[0].placeholder}
                placeholderTextColor="rgba(255, 255, 255, 0.5)"
                multiline
                numberOfLines={6}
                textAlignVertical="top"
              />
            </View>

            {/* Gratitude List */}
            {renderListInput('gratitude', reflectionPrompts[1])}

            {/* Challenges List */}
            {renderListInput('challenges', reflectionPrompts[2])}

            {/* Insights List */}
            {renderListInput('insights', reflectionPrompts[3])}

            {/* Mood and Spiritual Ratings */}
            <View style={styles.ratingsSection}>
              <Text style={styles.sectionTitle}>How are you feeling?</Text>
              
              {renderRatingSelector(
                'Mood After Practices',
                reflectionData.moodAfter,
                (value) => updateReflectionData('moodAfter', value),
                'Heavy',
                'Light'
              )}

              {renderRatingSelector(
                'Spiritual Connection',
                reflectionData.spiritualConnection,
                (value) => updateReflectionData('spiritualConnection', value),
                'Distant',
                'Close'
              )}

              {renderRatingSelector(
                'Stress Level',
                reflectionData.stressLevel,
                (value) => updateReflectionData('stressLevel', value),
                'Very Stressed',
                'Very Calm'
              )}

              {renderRatingSelector(
                'Overall Day Rating',
                reflectionData.overallRating,
                (value) => updateReflectionData('overallRating', value),
                'Difficult',
                'Excellent'
              )}
            </View>

            {/* Save Button */}
            <TouchableOpacity
              style={[styles.saveReflectionButton, isSaving && styles.saveReflectionButtonDisabled]}
              onPress={handleSaveReflection}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <Ionicons name="checkmark-circle" size={24} color="#ffffff" />
              )}
              <Text style={styles.saveReflectionButtonText}>
                {isSaving ? 'Saving...' : 'Save Reflection'}
              </Text>
            </TouchableOpacity>

            {/* Islamic Closing */}
            <View style={styles.closingSection}>
              <Text style={styles.closingText}>
                "And it is He who sends down rain after [people] have despaired and spreads His mercy. 
                And He is the Protector, the Praiseworthy." (Quran 42:28)
              </Text>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Poppins-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  errorText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    fontFamily: 'Poppins-Regular',
  },
  retryButton: {
    backgroundColor: '#4a90a4',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  saveButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4a90a4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: 'rgba(74, 144, 164, 0.5)',
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  greetingSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    alignItems: 'center',
  },
  greetingText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  greetingTranslation: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: 16,
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
  reflectionIntro: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
  inputSection: {
    marginBottom: 24,
  },
  promptQuestion: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    fontFamily: 'Poppins-SemiBold',
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    color: '#ffffff',
    fontSize: 16,
    minHeight: 120,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    fontFamily: 'Poppins-Regular',
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  listInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    color: '#ffffff',
    fontSize: 16,
    minHeight: 60,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    fontFamily: 'Poppins-Regular',
  },
  removeButton: {
    marginLeft: 12,
    marginTop: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(74, 144, 164, 0.5)',
    borderStyle: 'dashed',
  },
  addButtonText: {
    color: '#4a90a4',
    fontSize: 14,
    marginLeft: 8,
    fontFamily: 'Poppins-Medium',
  },
  ratingsSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
    fontFamily: 'Poppins-SemiBold',
  },
  ratingContainer: {
    marginBottom: 24,
  },
  ratingTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    fontFamily: 'Poppins-Medium',
  },
  ratingScale: {
    alignItems: 'center',
  },
  ratingButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 12,
    paddingHorizontal: 8,
  },
  ratingButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  ratingButtonActive: {
    backgroundColor: '#4a90a4',
  },
  ratingButtonText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Poppins-Medium',
  },
  ratingButtonTextActive: {
    color: '#ffffff',
    fontWeight: '600',
  },
  ratingLabel: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
  },
  saveReflectionButton: {
    backgroundColor: '#4a90a4',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  saveReflectionButtonDisabled: {
    backgroundColor: 'rgba(74, 144, 164, 0.5)',
  },
  saveReflectionButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  closingSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  closingText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: 'Poppins-Regular',
  },
});

export default JourneyReflectionScreen;