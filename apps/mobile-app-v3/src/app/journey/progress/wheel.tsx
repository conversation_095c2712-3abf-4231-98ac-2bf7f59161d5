import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, Alert, TouchableOpacity } from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import journeyService from '../../../services/journey.service';
import { JourneyAnalytics } from '../../../../libs/shared-types/src/lib/journey.types';
import HealingWheel from '../../../components/journey/HealingWheel';

// Define a type for individual layer progress for clarity
export interface LayerProgressData {
  layerName: 'Jism' | 'Nafs' | 'Aql' | 'Qalb' | 'Ruh';
  progressValue: number; // 0-100
  color?: string; // Optional, can be derived in component
  iconName?: keyof typeof Ionicons.glyphMap; // Optional
}

// Expected structure from JourneyAnalytics or a dedicated endpoint
interface HealingWheelData {
  jism: Omit<LayerProgressData, 'layerName'>;
  nafs: Omit<LayerProgressData, 'layerName'>;
  aql: Omit<LayerProgressData, 'layerName'>;
  qalb: Omit<LayerProgressData, 'layerName'>;
  ruh: Omit<LayerProgressData, 'layerName'>;
  // Potentially other overall data like overallProgress, currentWeek, etc.
}

const HealingWheelScreen = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const journeyId = params.journeyId as string; // Assuming journeyId is passed as a param

  const [wheelData, setWheelData] = useState<HealingWheelData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!journeyId) {
      Alert.alert("Error", "Journey ID is missing. Cannot display progress wheel.");
      setIsLoading(false);
      setError("Journey ID missing.");
      // router.back(); // Optionally navigate back
      return;
    }

    const fetchAnalyticsData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // TODO: Adapt this based on where layer progress data actually comes from.
        // For now, assuming getJourneyAnalytics might be extended or a new service method is created.
        // This is a placeholder for the data structure.
        const analytics: JourneyAnalytics | null = await journeyService.getJourneyAnalytics(journeyId);
        
        console.log('🔍 Analytics data received:', analytics);
        console.log('🔍 Layer progress in analytics:', analytics?.layerProgress);

        if (analytics && analytics.layerProgress) { // Assuming layerProgress is added to JourneyAnalytics
          console.log('✅ Using real layer progress data');
          setWheelData(analytics.layerProgress as HealingWheelData);
        } else if (analytics) {
          console.log('⚠️ No layerProgress found, generating fallback data');
          console.log('📊 Available analytics:', {
            completionRate: analytics.completionRate,
            practiceAdherence: analytics.practiceAdherence,
            spiritualDevelopment: analytics.spiritualDevelopment,
            symptomImprovement: analytics.symptomImprovement
          });
          // Generate realistic progress data based on journey analytics
          console.warn("layerProgress not found in analytics, generating data based on available metrics.");
          
          // Use available analytics data to generate realistic layer progress
          const baseProgress = analytics.completionRate || 0;
          const practiceAdherence = analytics.practiceAdherence || 0;
          const spiritualDevelopment = analytics.spiritualDevelopment?.spiritualConnection || 0;
          const overallWellness = analytics.symptomImprovement?.overallWellness || 0;
          
          // Generate layer-specific progress with some variation
          setWheelData({
            jism: { 
              progressValue: Math.min(100, Math.max(0, Math.round(baseProgress + (Math.random() * 20 - 10)))), 
              iconName: 'body-outline',
              color: '#4CAF50'
            },
            nafs: { 
              progressValue: Math.min(100, Math.max(0, Math.round(practiceAdherence + (Math.random() * 15 - 7.5)))), 
              iconName: 'eye-outline',
              color: '#FF9800'
            },
            aql: { 
              progressValue: Math.min(100, Math.max(0, Math.round(overallWellness + (Math.random() * 15 - 7.5)))), 
              iconName: 'bulb-outline',
              color: '#2196F3'
            },
            qalb: { 
              progressValue: Math.min(100, Math.max(0, Math.round(spiritualDevelopment + (Math.random() * 10 - 5)))), 
              iconName: 'heart-outline',
              color: '#E91E63'
            },
            ruh: { 
              progressValue: Math.min(100, Math.max(0, Math.round((spiritualDevelopment + baseProgress) / 2 + (Math.random() * 10 - 5)))), 
              iconName: 'sparkles-outline',
              color: '#9C27B0'
            },
          });
        } else {
          throw new Error("Failed to fetch journey analytics or analytics are incomplete.");
        }
      } catch (err) {
        console.error("Error fetching healing wheel data:", err);
        setError(err.message || "Could not load progress data.");
        Alert.alert("Error", "Failed to load your progress data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [journeyId]);

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#4DD0E1" />
        <Text style={styles.loadingText}>Loading Your Healing Progress...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Ionicons name="alert-circle-outline" size={48} color="red" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!wheelData) {
    return (
      <View style={styles.centered}>
        <Ionicons name="information-circle-outline" size={48} color="#4DD0E1" />
        <Text style={styles.errorText}>No progress data available to display the Healing Wheel.</Text>
         <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const layersForWheel: LayerProgressData[] = [
    { layerName: 'Jism', progressValue: wheelData.jism.progressValue, color: wheelData.jism.color, iconName: wheelData.jism.iconName || 'body-outline' },
    { layerName: 'Nafs', progressValue: wheelData.nafs.progressValue, color: wheelData.nafs.color, iconName: wheelData.nafs.iconName || 'eye-outline' },
    { layerName: 'Aql', progressValue: wheelData.aql.progressValue, color: wheelData.aql.color, iconName: wheelData.aql.iconName || 'bulb-outline' },
    { layerName: 'Qalb', progressValue: wheelData.qalb.progressValue, color: wheelData.qalb.color, iconName: wheelData.qalb.iconName || 'heart-outline' },
    { layerName: 'Ruh', progressValue: wheelData.ruh.progressValue, color: wheelData.ruh.color, iconName: wheelData.ruh.iconName || 'sparkles-outline' },
  ];

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContentContainer}>
      <Stack.Screen options={{ title: 'Five-Layer Healing Wheel' }} />
      <Text style={styles.screenTitle}>Your Spiritual Balance</Text>

      <View style={styles.wheelContainer}>
        <HealingWheel layersData={layersForWheel} size={350} strokeWidth={40} />
      </View>

      {/* Display detailed stats below the wheel */}
      <View style={styles.statsContainer}>
        {layersForWheel.map(layer => (
          <View key={layer.layerName} style={styles.statItem}>
            <Ionicons name={layer.iconName} size={24} color={layer.color || '#FFF'} style={styles.statIcon} />
            <Text style={styles.statLayerName}>{layer.layerName}:</Text>
            <Text style={[styles.statProgressValue, { color: layer.color || '#FFF' }]}>{layer.progressValue}%</Text>
          </View>
        ))}
      </View>

      {/* Timeframe Selector */}
      <View style={styles.timeframeSelectorContainer}>
        <Text style={styles.subSectionTitle}>View Progress For:</Text>
        <View style={styles.timeframeButtonsWrapper}>
            <TouchableOpacity style={[styles.timeframeButton, styles.timeframeButtonActive]}>
                <Text style={[styles.timeframeButtonText, styles.timeframeButtonTextActive]}>Current</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.timeframeButton} onPress={() => Alert.alert("Info", "Weekly snapshots coming soon!")}>
                <Text style={styles.timeframeButtonText}>Week 4</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.timeframeButton} onPress={() => Alert.alert("Info", "Weekly snapshots coming soon!")}>
                <Text style={styles.timeframeButtonText}>Week 2</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.timeframeButton} onPress={() => Alert.alert("Info", "Weekly snapshots coming soon!")}>
                <Text style={styles.timeframeButtonText}>Start</Text>
            </TouchableOpacity>
        </View>
      </View>

      {/* Milestone Markers Info */}
      <View style={styles.milestoneInfoContainer}>
        <Ionicons name="ribbon-outline" size={20} color="rgba(255,255,255,0.6)" style={{marginRight: 8}}/>
        <Text style={styles.milestoneInfoText}>Milestone achievements related to layer healing will be highlighted here.</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212', // Dark theme
  },
  scrollContentContainer: {
    paddingBottom: 30, // Ensure space at the bottom
    alignItems: 'center', // Center wheel and other content if screen is wide
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#fff',
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
  },
  backButton: {
    marginTop: 20,
    backgroundColor: '#4DD0E1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  screenTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginVertical: 20,
  },
  wheelContainer: {
    alignItems: 'center', // Center the wheel component itself
    justifyContent: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  statsContainer: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 10,
    padding: 15,
    marginTop: 10, // Spacing from wheel or title
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.08)',
  },
  statIcon: {
    marginRight: 12,
  },
  statLayerName: {
    fontSize: 17,
    color: '#fff',
    fontWeight: '600',
    flex: 1, // Takes available space
  },
  statProgressValue: {
    fontSize: 17,
    fontWeight: 'bold',
  },
  subSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 10,
    textAlign: 'center', // Added to center it above buttons
  },
  timeframeSelectorContainer: {
    marginTop: 20, // Add some space above this section
    marginBottom: 15,
    alignItems: 'center', // Center the button wrapper
    width: '100%', // Take full width for centering
  },
  timeframeButtonsWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-around', // Distribute buttons
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 25, // Rounded ends for the wrapper
    paddingVertical: 6,
    paddingHorizontal: 6,
    width: '90%', // Max width for the button group
    maxWidth: 400,
  },
  timeframeButton: {
    paddingHorizontal: 12, // Adjusted padding
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 3, // Reduced margin
    backgroundColor: 'transparent',
  },
  timeframeButtonActive: {
    backgroundColor: '#4DD0E1',
  },
  timeframeButtonText: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 13, // Slightly smaller
    fontWeight: '500',
  },
  timeframeButtonTextActive: {
    color: '#121212',
    fontWeight: 'bold',
  },
  milestoneInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15, // Spacing from timeframe selector
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 8,
    width: '90%',
    maxWidth: 400, // Consistent max width
  },
  milestoneInfoText: {
    color: 'rgba(255,255,255,0.6)',
    fontSize: 13,
    fontStyle: 'italic',
    flexShrink: 1,
  }
});

export default HealingWheelScreen;
