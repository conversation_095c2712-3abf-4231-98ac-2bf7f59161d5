import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, Alert, TouchableOpacity } from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import journeyService from '../../../services/journey.service'; // Adjusted path
import { JourneyProgress as JourneyProgressType, Journey, MilestoneCompletion } from '../../../../libs/shared-types/src/lib/journey.types'; // Adjusted path

// Define interfaces for processed data for the dashboard
interface MoodTrendDataPoint {
  date: string; // Or Date object
  moodBefore?: number | null;
  moodAfter?: number | null;
}

interface DailyCompletionStatus {
  date: string; // Or Date object
  completedPractices: number; // Count of 5
  totalPractices: number; // Usually 5
}

interface ReflectionSnippet {
  date: string; // Or Date object
  excerpt: string;
  fullReflection?: string; // For future navigation
  journeyProgressId?: string; // To link back
}

interface DisplayMilestone {
  id: string;
  name: string;
  description?: string;
  completionDate: string; // Formatted date
}

const DailyMetricsDashboardScreen = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const journeyId = params.journeyId as string;
  const currentJourneyTitle = params.journeyTitle as string || "Your Journey"; // Passed from previous screen

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for processed metrics
  const [moodTrends, setMoodTrends] = useState<MoodTrendDataPoint[]>([]);
  const [practiceCompletionRate, setPracticeCompletionRate] = useState<number>(0);
  const [dailyCompletions, setDailyCompletions] = useState<DailyCompletionStatus[]>([]);
  const [reflectionSnippets, setReflectionSnippets] = useState<ReflectionSnippet[]>([]);
  const [recentMilestones, setRecentMilestones] = useState<DisplayMilestone[]>([]);

  useEffect(() => {
    if (!journeyId) {
      Alert.alert("Error", "Journey ID is missing. Cannot display metrics dashboard.");
      setIsLoading(false);
      setError("Journey ID missing.");
      return;
    }

    const fetchAndProcessDashboardData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch detailed journey progress history (e.g., last 30 records for robust trend calculation)
        const progressHistoryList = await journeyService.getJourneyProgressHistoryList(journeyId, 30); // Fetch last 30

        // Sort by date ascending to process trends correctly
        const sortedProgressRecords = progressHistoryList.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

        const displayRecordsForTrend = sortedProgressRecords.slice(-14); // Use last 14 for local processing, chart shows 7

        // Process Mood Trends
        setMoodTrends(displayRecordsForTrend.map(p => ({
          date: p.date,
          moodBefore: p.moodBefore,
          moodAfter: p.moodAfter,
        })));

        // Process Practice Completion (for the last 7 unique days in the fetched history)
        const uniqueDaysProgressMap = sortedProgressRecords.reduceRight((acc, p) => {
            const dateStr = new Date(p.date).toDateString();
            if (!acc[dateStr]) { // Take the latest record for any given day if multiple exist
                acc[dateStr] = p;
            }
            return acc;
        }, {} as Record<string, JourneyProgressType>);

        const lastSevenUniqueDaysData = Object.values(uniqueDaysProgressMap)
            .sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()) // Sort most recent first
            .slice(0,7) // Take the latest 7 unique days
            .reverse(); // Reverse again to have oldest of the 7 first for display

        let totalCompletedPractices = 0;
        let totalPossiblePractices = 0;
        const dailyCompletionsData: DailyCompletionStatus[] = lastSevenUniqueDaysData.map(dayProgress => {
            const completedCount = (dayProgress.practicesCompleted || []).filter(pc => pc.completed).length;
            const dayTotalPractices = 5; // Assuming 5 core practices per day

            totalCompletedPractices += completedCount;
            totalPossiblePractices += dayTotalPractices;
            return { date: dayProgress.date, completedPractices: completedCount, totalPractices: dayTotalPractices };
        });

        setDailyCompletions(dailyCompletionsData);
        setPracticeCompletionRate(totalPossiblePractices > 0 ? (totalCompletedPractices / totalPossiblePractices) * 100 : 0);

        // Process Reflection Snippets
        setReflectionSnippets(
            sortedProgressRecords
                .filter(p => p.dailyReflection && p.dailyReflection.trim() !== '')
                .slice(-3)
                .map(p => ({
                    date: p.date,
                    excerpt: p.dailyReflection!.substring(0, 100) + (p.dailyReflection!.length > 100 ? '...' : ''),
                    fullReflection: p.dailyReflection!,
                    journeyProgressId: p.id
                }))
                .reverse()
        );

        // Fetch Actual Milestones
        const completedMilestones = await journeyService.getJourneyCompletedMilestones(journeyId);
        setRecentMilestones(
            // Take last 3, or fewer if not available
            completedMilestones.slice(-3).map((m: any) => ({ // Cast m to any if its type is not fully defined from service yet
                id: m.milestoneId || m.id, // Adjust based on actual returned structure
                name: m.name,
                description: m.description,
                completionDate: new Date(m.completionDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric'}),
            })).reverse() // Show newest first
        );

      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError(err.message || "Could not load your dashboard data.");
        Alert.alert("Error", "Failed to load your dashboard. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAndProcessDashboardData();
  }, [journeyId]);


  if (isLoading) {
    return <View style={styles.centered}><ActivityIndicator size="large" color="#4DD0E1" /><Text style={styles.loadingText}>Loading Dashboard...</Text></View>;
  }

  if (error) {
    return <View style={styles.centered}><Ionicons name="alert-circle-outline" size={48} color="red" /><Text style={styles.errorText}>{error}</Text></View>;
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContentContainer}>
      <Stack.Screen options={{ title: 'Metrics Dashboard' }} />
      <View style={styles.headerContainer}>
        <Ionicons name="stats-chart-outline" size={32} color="#4DD0E1" style={styles.headerIcon} />
        <View>
            <Text style={styles.screenSubtitle}>{currentJourneyTitle}</Text>
            <Text style={styles.screenTitle}>Daily Progress Insights</Text>
        </View>
      </View>

      {/* Mood Trend Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Mood Journey (Last 7 Days)</Text>
        <View style={styles.chartPlaceholder}>
            {/* Basic Bar Chart Simulation */}
            {moodTrends.length > 0 ? (
                <View style={styles.barChartContainer}>
                    {moodTrends.slice(-7).map((mt, index) => (
                        <View key={mt.date || index} style={styles.barGroup}>
                            <View style={[styles.bar, { height: (mt.moodAfter || 0) * 8, backgroundColor: '#81C784' }]} />
                            <View style={[styles.bar, { height: (mt.moodBefore || 0) * 8, backgroundColor: '#FFB74D' }]} />
                            <Text style={styles.barLabel}>{new Date(mt.date).toLocaleDateString('en-US', { day: 'numeric', month: 'short'})}</Text>
                        </View>
                    ))}
                </View>
            ) : (
                <Text style={styles.placeholderText}>No mood data tracked yet.</Text>
            )}
        </View>
        <View style={styles.legendContainer}>
            <View style={styles.legendItem}><View style={[styles.legendColorBox, {backgroundColor: '#FFB74D'}]} />_
                <Text style={styles.legendText}>Mood Before</Text>
            </View>
            <View style={styles.legendItem}><View style={[styles.legendColorBox, {backgroundColor: '#81C784'}]} />_
                <Text style={styles.legendText}>Mood After</Text>
            </View>
        </View>
        <Text style={styles.cardFooter}>"Verily, with hardship comes ease." (Quran 94:6)</Text>
      </View>

      {/* Practice Completion Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Daily Practice Consistency</Text>
        <View style={styles.completionContent}>
            <Text style={styles.largeMetric}>{Math.round(practiceCompletionRate)}%</Text>
            <Text style={styles.metricContext}>Activities completed this week</Text>
        </View>
        <View style={styles.dailyDotsContainer}>
            {dailyCompletions.slice(-7).map((day, index) => {
                let dotColor = styles.dailyDotMissed.backgroundColor;
                if (day.completedPractices === day.totalPractices && day.totalPractices > 0) {
                    dotColor = styles.dailyDotCompleted.backgroundColor;
                } else if (day.completedPractices > 0) {
                    dotColor = styles.dailyDotPartial.backgroundColor;
                }
                return (
                    <View key={day.date || index} style={[styles.dailyDot, { backgroundColor: dotColor }]} >
                        <Text style={styles.dailyDotText}>{new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' }).substring(0,1)}</Text>
                    </View>
                );
            })}
        </View>
      </View>

      {/* Recent Reflections Card */}
      {reflectionSnippets.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Recent Reflections</Text>
          {reflectionSnippets.map(snippet => (
            <TouchableOpacity key={snippet.journeyProgressId || snippet.date} style={styles.snippetItem} onPress={() => Alert.alert("Reflection", snippet.fullReflection || "Full reflection unavailable.")}>
              <Text style={styles.snippetDate}>{new Date(snippet.date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric'})}</Text>
              <Text style={styles.snippetText} numberOfLines={2}>{snippet.excerpt}</Text>
              <Ionicons name="chevron-forward-outline" size={18} color="rgba(255,255,255,0.5)" style={styles.snippetArrow} />
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Milestones Card */}
      {recentMilestones.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Recent Milestones</Text>
          {recentMilestones.map(milestone => (
            <View key={milestone.id} style={styles.milestoneItem}>
                <Ionicons name="ribbon" size={22} color="#FFD700" style={styles.milestoneIcon}/>
                <View>
                    <Text style={styles.milestoneName}>{milestone.name}</Text>
                    <Text style={styles.milestoneDate}>Achieved: {new Date(milestone.completionDate).toLocaleDateString()}</Text>
                </View>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0A0A0A', // Slightly different dark
  },
  scrollContentContainer: {
    padding: 15,
    paddingBottom: 30, // More space at bottom
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#0A0A0A',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 17,
    color: 'rgba(255,255,255,0.8)',
  },
  errorText: {
    marginTop: 12,
    fontSize: 17,
    color: '#FF6B6B', // Softer red
    textAlign: 'center',
  },
  headerContainer: { // Changed from header
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 25, // More space
    paddingHorizontal: 5, // Align with card padding
  },
  headerIcon: {
    marginRight: 12,
  },
  screenTitle: {
    fontSize: 24, // Main title
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  screenSubtitle: { // For Journey Title
    fontSize: 16,
    color: 'rgba(255,255,255,0.7)',
    marginBottom: 2,
  },
  card: {
    backgroundColor: '#1C1C1E', // Darker card
    borderRadius: 16, // More rounded
    padding: 20, // More padding
    marginBottom: 25, // More space between cards
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20, // Larger title
    fontWeight: 'bold',
    color: 'rgba(255,255,255,0.95)',
    marginBottom: 15, // More space
  },
  chartPlaceholder: { // For actual chart or basic bars
    height: 180, // Increased height
    justifyContent: 'flex-end', // For bars from bottom
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
    borderRadius: 10,
    marginBottom: 10,
    paddingHorizontal: 10,
  },
  barChartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: '100%',
    width: '100%',
  },
  barGroup: {
    alignItems: 'center',
    marginHorizontal: 4,
  },
  bar: {
    width: 12, // Thicker bars
    borderRadius: 4,
    marginBottom: 2, // Space between stacked bars if any, or bar and label
  },
  barLabel: {
    fontSize: 10,
    color: 'rgba(255,255,255,0.6)',
    marginTop: 4,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 5,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  legendColorBox: {
    width: 10,
    height: 10,
    borderRadius: 2,
    marginRight: 5,
  },
  legendText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.7)',
  },
  placeholderText: {
    color: 'rgba(255,255,255,0.6)', // Brighter placeholder
    fontSize: 15,
  },
  cardFooter: {
    fontSize: 13, // Slightly larger
    fontStyle: 'italic',
    color: 'rgba(255,255,255,0.65)',
    textAlign: 'center',
    marginTop: 15, // More space
  },
  completionContent: {
    alignItems: 'center', // Center the metric and context
    marginBottom: 10,
  },
  largeMetric: {
    fontSize: 48, // Even larger
    fontWeight: 'bold',
    color: '#4DD0E1',
    textAlign: 'center',
  },
  metricContext: {
    fontSize: 15, // Slightly larger
    color: 'rgba(255,255,255,0.75)',
    textAlign: 'center',
    marginBottom: 15,
  },
  dailyDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between', // Space them out
    paddingVertical: 10,
    paddingHorizontal: 5, // Add some horizontal padding
  },
  dailyDot: {
    width: 28, // Larger dots
    height: 28, // Larger dots
    borderRadius: 14, // Circular
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  dailyDotText: {
    color: '#000', // Black text for contrast on colored dots
    fontSize: 10,
    fontWeight: 'bold',
  },
  dailyDotCompleted: { backgroundColor: '#4CAF50' }, // Green
  dailyDotPartial: { backgroundColor: '#FFC107' },  // Yellow
  dailyDotMissed: { backgroundColor: '#555759' },   // Darker Grey
  snippetItem: {
    paddingVertical: 12, // More padding
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)', // Slightly more visible
    flexDirection: 'row', // For arrow
    alignItems: 'center',
  },
  snippetDate: {
    fontSize: 13, // Slightly larger
    color: 'rgba(255,255,255,0.6)',
    marginBottom: 4,
  },
  snippetText: {
    fontSize: 15, // Slightly larger
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 20,
    flex: 1, // Allow text to take space
  },
  snippetArrow: {
    marginLeft: 10,
  },
  milestoneItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10, // More padding
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  milestoneIcon: {
    marginRight: 12,
  },
  milestoneName: {
    fontSize: 16, // Slightly larger
    color: '#FFD700',
    fontWeight: '600',
  },
  milestoneDate: { // Added style for date
    fontSize: 13,
    color: 'rgba(255,255,255,0.6)',
    marginTop: 2,
  },
});

export default DailyMetricsDashboardScreen;
