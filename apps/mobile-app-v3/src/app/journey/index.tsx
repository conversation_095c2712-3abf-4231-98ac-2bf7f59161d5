import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { colors, ColorTheme } from '../../constants/Colors';
import HealingJourneyRoadmap from '../../components/journeys/HealingJourneyRoadmap';

const milestones = [
  { title: 'Assessment', icon: 'symptomsStep', status: 'completed' },
  { title: 'Diagnosis', icon: 'analysisStep', status: 'completed' },
  { title: 'First Steps', icon: 'growth', status: 'current' },
  { title: 'Deeper Understanding', icon: 'light', status: 'locked' },
  { title: 'Spiritual Growth', icon: 'star', status: 'locked' },
];

const HealingJourneyScreen = () => {
  const styles = createStyles(colors);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Your Healing Journey</Text>
      <HealingJourneyRoadmap milestones={milestones} />
    </ScrollView>
  );
};

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
  });

export default HealingJourneyScreen;
