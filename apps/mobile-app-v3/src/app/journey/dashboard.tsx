/**
 * Journey Dashboard Screen for Feature 2: Personalized Healing Journeys
 * Main dashboard for active healing journey
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Import services
import journeyService from '../../services/journey.service'; // Corrected path
// import { authService } from '../../services/auth.service'; // Not used in this file currently
import { Journey, DailyPractice, PracticeType, JourneyDay, JourneyProgress } from '../../../../libs/shared-types/src/lib/journey.types'; // Corrected import

// Import components
import { ProgressCircle } from '../../components/ProgressCircle';
// import { DailyPracticeCard } from '../../components/DailyPracticeCard'; // Replaced by DailyModuleComponentWrapper for this section
import DailyModuleComponentWrapper from '../../components/journey/DailyModuleComponentWrapper'; // Import new wrapper
import { JourneyStatsCard } from '../../components/JourneyStatsCard';
import { CommunityCard } from '../../components/CommunityCard';
import mockTodaysPractices from '../../data/mockTodaysPractices';

// Extended Journey interface for UI-specific properties
interface JourneyWithUI extends Journey {
  totalProgress?: number;
  configuration?: {
    duration: number;
    dailyTimeCommitment: number;
    primaryLayer: string; // This should be LayerFocus type from shared-types if strongly typed
    communityIntegration: boolean;
  };
  days?: Array<JourneyDay>; // Use JourneyDay from shared-types, which includes DailyPractice[] for practices
}

const { width, height } = Dimensions.get('window');

// Helper function to format layer names for display
const formatLayerName = (layerName: string): string => {
  const layerDisplayNames: Record<string, string> = {
    'jism': 'Jism (Body)',
    'nafs': 'Nafs (Soul)',
    'aql': 'Aql (Mind)',
    'qalb': 'Qalb (Heart)',
    'ruh': 'Ruh (Spirit)',
  };
  
  return layerDisplayNames[layerName.toLowerCase()] || layerName.charAt(0).toUpperCase() + layerName.slice(1);
};

export default function JourneyDashboard() {
  const [journey, setJourney] = useState<Journey | null>(null); // Use Journey from shared-types
  const [isLoading, setIsLoading] = useState(true);
  const [todaysPractices, setTodaysPractices] = useState<DailyPractice[]>([]);
  const [dailyProgressData, setDailyProgressData] = useState<Partial<JourneyProgress>>({});
  const [stats, setStats] = useState({
    streak: 0,
    completedDays: 0,
    averageRating: 0,
  });

  useEffect(() => {
    loadJourneyData();
  }, []);

  const loadJourneyData = async () => {
    try {
      setIsLoading(true);

      console.log('🔍 Dashboard: Loading journey data...');

      // Get current journey (lightweight for basic info)
      const currentJourney = await journeyService.getCurrentJourney();

      console.log('🔍 Dashboard: Current journey result:', currentJourney);
      console.log('🔍 Dashboard: Journey ID:', currentJourney?.id);
      console.log('🔍 Dashboard: Journey status:', currentJourney?.status);
      console.log('🔍 Dashboard: Journey title:', currentJourney?.title);

      if (!currentJourney) {
        console.log('❌ Dashboard: No current journey found, redirecting to create');
        // No active journey, redirect to journey creation
        router.replace('/journey/create' as any);
        return;
      }

      // If journey is in 'created' status, start it automatically
      if (currentJourney.status === 'created') {
        console.log('🔄 Dashboard: Journey in created status, starting it...');
        try {
          await journeyService.startJourney(currentJourney.id);
          console.log('✅ Dashboard: Journey started successfully');
          // Reload journey data to get updated status
          const updatedJourney = await journeyService.getCurrentJourney();
          if (updatedJourney) {
            setJourney(updatedJourney);
          }
        } catch (error) {
          console.error('❌ Dashboard: Failed to start journey:', error);
          Alert.alert('Error', 'Failed to start your journey. Please try again.');
        }
      }

      console.log('✅ Dashboard: Journey found, setting up dashboard');

      // Set basic journey info
      setJourney(currentJourney);

      // For practices, we need the detailed version - but only fetch if we need it
      try {
        const detailedJourney = await journeyService.getCurrentJourneyWithDetails();
        console.log('Detailed journey loaded:', !!detailedJourney);
        console.log('Journey days:', detailedJourney?.journeyDays?.length);
        console.log('Current day:', detailedJourney?.currentDay);
        
        if (detailedJourney && detailedJourney.journeyDays && detailedJourney.currentDay > 0) {
          // Backend returns journeyDays, not days
          const currentDayData = detailedJourney.journeyDays.find(day => day.dayNumber === detailedJourney.currentDay);
          console.log('Current day data found:', !!currentDayData);
          console.log('Practices in current day:', currentDayData?.dailyPracticesInJourney?.length);
          
          if (currentDayData && currentDayData.dailyPracticesInJourney && currentDayData.dailyPracticesInJourney.length > 0) {
            // Backend returns dailyPracticesInJourney, not practices
            console.log('Using real journey practices from backend');
            console.log('Real practices:', currentDayData.dailyPracticesInJourney.map(p => ({ id: p.id, type: p.type, title: p.title })));
            setTodaysPractices(currentDayData.dailyPracticesInJourney);
          } else {
            console.log('No practices in current day, using mock data as fallback');
            setTodaysPractices(mockTodaysPractices);
          }
        } else {
          // Use mock data if no practices found
          console.log('No journey days found, using mock practices for development');
          setTodaysPractices(mockTodaysPractices);
        }
      } catch (practicesError) {
        console.log('Could not load detailed practices, using mock data for development');
        setTodaysPractices(mockTodaysPractices);
      }

      // Load journey stats using the fetched journey's ID
      if (currentJourney?.id) {
        await loadJourneyStats(currentJourney.id);
      } else {
        // Handle case where currentJourney or its ID is null/undefined
        // For example, set default stats or show an error
         setStats({ streak: 0, completedDays: 0, averageRating: 0 });
      }

    } catch (error) {
      console.error('Error loading journey data:', error);
      Alert.alert('Error', 'Failed to load journey data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadJourneyStats = async (journeyId: string) => {
    try {
      const progressData = await journeyService.getJourneyProgress(journeyId);
      
      // Calculate streak from progress history
      const streak = calculateCurrentStreak(progressData.progressHistory || []);
      
      setStats({
        streak,
        completedDays: progressData.summary?.totalDays || 0,
        averageRating: progressData.summary?.averageRating || 0,
      });
    } catch (error) {
      console.error('Error loading journey stats:', error);
      // Set default stats on error
      setStats({ streak: 0, completedDays: 0, averageRating: 0 });
    }
  };

  // Helper function to calculate current streak
  const calculateCurrentStreak = (progressHistory: any[]): number => {
    if (!progressHistory || progressHistory.length === 0) return 0;
    
    // Sort by date descending (most recent first)
    const sortedHistory = progressHistory
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    let streak = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (const record of sortedHistory) {
      const recordDate = new Date(record.date);
      recordDate.setHours(0, 0, 0, 0);
      
      const daysDiff = Math.floor((today.getTime() - recordDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === streak) {
        streak++;
      } else {
        break;
      }
    }
    
    return streak;
  };

  const handleComponentProgressSubmit = useCallback((practiceId: string, componentProgress: any) => {
    setDailyProgressData(prevData => {
      // Find existing practice or add new one
      const existingPracticeIndex = (prevData.practicesCompleted || []).findIndex(p => p.practiceId === practiceId);
      let updatedPracticesCompleted = [...(prevData.practicesCompleted || [])];

      if (existingPracticeIndex > -1) {
        updatedPracticesCompleted[existingPracticeIndex] = {
          ...updatedPracticesCompleted[existingPracticeIndex],
          ...componentProgress,
          completedAt: new Date().toISOString(), // Ensure completion time is updated
        };
      } else {
        updatedPracticesCompleted.push({
          practiceId,
          ...componentProgress,
          completedAt: new Date().toISOString(),
        });
      }

      return {
        ...prevData,
        ...componentProgress, // Merge top-level progress like moodBefore, dailyIntention
        practicesCompleted: updatedPracticesCompleted,
      };
    });
    // Optionally, provide feedback to the user that this component's progress is noted
    Alert.alert("Progress Noted", `${componentProgress.title || 'Component'} progress has been noted. Complete all components and then 'Complete Day'.`);
  }, []);

  const handleCompleteDay = async () => {
    if (!journey || !journey.id || journey.currentDay === undefined) {
      Alert.alert("Error", "Journey data is not available.");
      return;
    }

    // Basic check: ensure all 5 components have submitted some data
    // This can be made more robust by checking specific flags from each component
    const allComponentsReported = todaysPractices.every(tp =>
        (dailyProgressData.practicesCompleted || []).some(pc => pc.practiceId === tp.id && pc.completed)
    );

    if (!allComponentsReported && (dailyProgressData.practicesCompleted || []).length < todaysPractices.length) {
        Alert.alert("Incomplete", `Please complete all ${todaysPractices.length} sections for today before marking the day as complete.`);
        return;
    }

    setIsLoading(true);
    try {
      const submissionData: Omit<JourneyProgress, 'id' | 'createdAt' | 'updatedAt' | 'userId' | 'journeyId'> = {
        dayNumber: journey.currentDay,
        date: new Date().toISOString(),
        practicesCompleted: dailyProgressData.practicesCompleted || [],
        overallRating: dailyProgressData.overallRating, // This would need a separate UI element
        moodBefore: dailyProgressData.moodBefore,
        moodAfter: dailyProgressData.moodAfter, // This might be collected at end of day
        energyLevelBefore: dailyProgressData.energyLevelBefore,
        spiritualStateBefore: dailyProgressData.spiritualStateBefore,
        dailyIntention: dailyProgressData.dailyIntention,
        spiritualConnection: dailyProgressData.spiritualConnection, // Needs UI
        stressLevel: dailyProgressData.stressLevel, // Needs UI
        dailyReflection: dailyProgressData.dailyReflection,
        gratitude: dailyProgressData.gratitude || [], // Needs UI or from journaling
        challenges: dailyProgressData.challenges || [], // Needs UI or from journaling
        insights: dailyProgressData.insights || [], // Needs UI or from journaling
        communityParticipation: dailyProgressData.communityParticipation || false, // Needs UI
        communityContribution: dailyProgressData.communityContribution, // Needs UI
        peerSupport: dailyProgressData.peerSupport || false, // Needs UI
        contentRelevance: dailyProgressData.contentRelevance, // Needs UI
        practiceEffectiveness: dailyProgressData.practiceEffectiveness, // Needs UI
        timeAppropriate: dailyProgressData.timeAppropriate, // Needs UI
        suggestedAdjustments: dailyProgressData.suggestedAdjustments, // Needs UI
      };

      await journeyService.recordDailyProgress({
        journeyId: journey.id,
        userId: journey.userId, // Assuming journey object has userId
        ...submissionData
      });
      Alert.alert("Day Complete!", "Your progress for today has been saved.");
      // Refresh journey data to reflect new day or completion
      loadJourneyData();
      setDailyProgressData({}); // Reset for next day
    } catch (error) {
      console.error("Failed to complete day:", error);
      Alert.alert("Error", "Could not save your daily progress. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };


  const handlePracticePress = useCallback((practice: any) => {
    // This navigation is for older practice card types if they exist.
    // The new 5 components are handled inline.
    router.push({
      pathname: '/journey/practice' as any, // This route might need to be generic or removed if all is inline
      params: {
        practiceId: practice.id,
        journeyId: journey?.id,
        dayNumber: journey?.current_day,
      },
    } as any);
  }, [journey?.id, journey?.current_day]);

  const handleProgressPress = () => {
    router.push({
      pathname: '/journey/progress' as any,
      params: { journeyId: journey?.id },
    } as any);
  };

  const handleCommunityPress = () => {
    router.push({
      pathname: '/journey/community' as any,
      params: { journeyId: journey?.id },
    } as any);
  };

  const handleReflectionPress = () => {
    router.push({
      pathname: '/journey/reflection' as any,
      params: {
        journeyId: journey?.id,
        dayNumber: journey?.current_day,
      },
    });
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a365d', '#2d5a87', '#4a90a4']}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading your journey...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (!journey) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a365d', '#2d5a87', '#4a90a4']}
          style={styles.errorContainer}
        >
          <Ionicons name="alert-circle" size={64} color="#ffffff" />
          <Text style={styles.errorTitle}>No Active Journey</Text>
          <Text style={styles.errorText}>
            You don't have an active healing journey. Let's create one for you.
          </Text>
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => router.replace('/journey/create' as any)}
          >
            <Text style={styles.createButtonText}>Create Journey</Text>
          </TouchableOpacity>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#1a365d', '#2d5a87', '#4a90a4']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.greeting}>Assalamu Alaikum</Text>
            <Text style={styles.journeyTitle}>
              {journey.title || 'Your Healing Journey'}
            </Text>
            <Text style={styles.journeySubtitle}>
              Day {journey.current_day} of{' '}
              {journey.configuration?.duration || journey.total_days}
            </Text>
          </View>

          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => router.push('/journey/settings' as any)}
          >
            <Ionicons name="settings-outline" size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          {/* Progress Overview */}
          <View style={styles.progressSection}>
            <TouchableOpacity
              style={styles.progressCard}
              onPress={handleProgressPress}
            >
              <View style={styles.progressContent}>
                <ProgressCircle
                  progress={journey.totalProgress || journey.progress || 0}
                  size={80}
                  strokeWidth={8}
                  color="#4a90a4"
                />
                <View style={styles.progressInfo}>
                  <Text style={styles.progressTitle}>Journey Progress</Text>
                  <Text style={styles.progressPercentage}>
                    {Math.round(journey.totalProgress || journey.progress || 0)}
                    %
                  </Text>
                  <Text style={styles.progressSubtext}>
                    {stats.completedDays} days completed
                  </Text>
                </View>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color="rgba(255, 255, 255, 0.6)"
              />
            </TouchableOpacity>
          </View>

          {/* Journey Stats */}
          <View style={styles.statsSection}>
            <Text style={styles.sectionTitle}>Your Progress</Text>
            <View style={styles.statsGrid}>
              <JourneyStatsCard
                icon="flame"
                title="Current Streak"
                value={`${stats.streak} days`}
                color="#ff6b35"
              />
              <JourneyStatsCard
                icon="star"
                title="Average Rating"
                value={stats.averageRating.toFixed(1)}
                color="#ffd700"
              />
              <JourneyStatsCard
                icon="heart"
                title="Focus Layer"
                value={
                  formatLayerName(
                    journey.primaryLayer ||
                    journey.configuration?.primaryLayer ||
                    journey.focus_layers?.[0] ||
                    'qalb'
                  )
                }
                color="#e74c3c"
              />
            </View>
          </View>

          {/* Today's Practices */}
          <View style={styles.practicesSection}>
            <Text style={styles.sectionTitle}>Today's Practices</Text>
            {todaysPractices.length > 0 ? (
              (() => {
                console.log('Rendering practices. Total count:', todaysPractices.length);
                const validPractices = todaysPractices.filter(practice => practice && practice.id);
                console.log('Valid practices count:', validPractices.length);
                console.log('Valid practices:', validPractices.map(p => ({ id: p.id, type: p.type, title: p.title })));
                
                return validPractices.map((practice, index) => {
                  console.log(`Rendering practice ${index + 1}:`, practice.type, practice.title);
                  return (
                    <DailyModuleComponentWrapper
                      key={practice.id || `practice-${index}`}
                      practice={practice}
                      onProgressSubmit={handleComponentProgressSubmit} // Pass the actual handler
                      onPress={() => handlePracticePress(practice)} // For fallback navigation if practice is an older type
                    />
                  );
                });
              })()
            ) : (
              <View style={styles.noPracticesCard}>
                <Ionicons name="checkmark-circle" size={48} color="#4a90a4" />
                <Text style={styles.noPracticesTitle}>
                  {journey?.currentDay === 1 ? 'Ready to Start!' : 'All Done!'}
                </Text>
                <Text style={styles.noPracticesText}>
                  {journey?.currentDay === 1 
                    ? 'Your daily practices will appear here once your journey begins.'
                    : 'You\'ve completed all practices for today. Great work!'
                  }
                </Text>
              </View>
            )}
          </View>

          {/* Complete Day Button */}
          {todaysPractices.length > 0 && (
            <View style={styles.completeDaySection}>
              <TouchableOpacity style={styles.completeDayButton} onPress={handleCompleteDay}>
                <Ionicons name="checkmark-circle" size={24} color="#ffffff" style={styles.completeDayIcon} />
                <Text style={styles.completeDayButtonText}>Complete Day {journey?.current_day || journey?.currentDay || 1}</Text>
              </TouchableOpacity>
              <Text style={styles.completeDayHint}>
                Complete all practices above to mark this day as finished
              </Text>
            </View>
          )}

          {/* Community Integration */}
          {journey?.configuration?.communityIntegration && (
            <View style={styles.communitySection}>
              <Text style={styles.sectionTitle}>Community</Text>
              <CommunityCard
                onPress={handleCommunityPress}
                memberCount={12}
                recentActivity="3 new reflections shared"
              />
            </View>
          )}

          {/* Quick Actions */}
          <View style={styles.actionsSection}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleReflectionPress}
              >
                <Ionicons name="book-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Daily Reflection</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => {
                  if (journey?.id) {
                    router.push({ pathname: '/journey/progress/dashboard', params: { journeyId: journey.id, journeyTitle: journey.title } });
                  } else {
                    Alert.alert("Error", "No active journey to view metrics for.");
                  }
                }}
              >
                <Ionicons name="podium-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Metrics Dashboard</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push('/journey/resources' as any)}
              >
                <Ionicons name="library-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Resources</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => {
                  if (journey?.id) {
                    router.push({ pathname: '/journey/progress/wheel', params: { journeyId: journey.id } });
                  } else {
                    Alert.alert("Error", "No active journey to view progress for.");
                  }
                }}
              >
                <Ionicons name="aperture-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Healing Wheel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push('/emergency')}
              >
                <Ionicons name="medical-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Support</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Poppins-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  errorText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    fontFamily: 'Poppins-Regular',
  },
  createButton: {
    backgroundColor: '#4a90a4',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  createButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 24,
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    marginBottom: 4,
    fontFamily: 'Poppins-Regular',
  },
  journeyTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  journeySubtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  progressSection: {
    marginBottom: 32,
  },
  progressCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  progressInfo: {
    marginLeft: 20,
    flex: 1,
  },
  progressTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  progressPercentage: {
    color: '#4a90a4',
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 2,
    fontFamily: 'Poppins-Bold',
  },
  progressSubtext: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  statsSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    fontFamily: 'Poppins-SemiBold',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  practicesSection: {
    marginBottom: 32,
  },
  noPracticesCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  noPracticesTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  noPracticesText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
  communitySection: {
    marginBottom: 32,
  },
  actionsSection: {
    marginBottom: 32,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: (width - 60) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
    textAlign: 'center',
    fontFamily: 'Poppins-Medium',
  },
  completeDaySection: {
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  completeDayButton: {
    backgroundColor: '#4a90a4',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    marginBottom: 8,
  },
  completeDayIcon: {
    marginRight: 8,
  },
  completeDayButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  completeDayHint: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 12,
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
});
