/**
 * Journey Settings Screen
 * Manage journey preferences, notifications, and account settings
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import journeyService from '../../services/journey.service';

const JourneySettingsScreen = () => {
  const router = useRouter();
  const [journey, setJourney] = useState<any>(null);
  const [settings, setSettings] = useState({
    notifications: true,
    dailyReminders: true,
    communityUpdates: false,
    progressSharing: false,
    soundEnabled: true,
    vibrationEnabled: true,
  });

  useEffect(() => {
    loadJourneyData();
  }, []);

  const loadJourneyData = async () => {
    try {
      const currentJourney = await journeyService.getCurrentJourney();
      setJourney(currentJourney);
    } catch (error) {
      console.error('Error loading journey:', error);
    }
  };

  const handleSettingChange = (key: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handlePauseJourney = () => {
    Alert.alert(
      'Pause Journey',
      'Are you sure you want to pause your healing journey? You can resume it anytime.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Pause',
          style: 'destructive',
          onPress: async () => {
            try {
              if (journey?.id) {
                await journeyService.pauseJourney(journey.id);
                Alert.alert('Journey Paused', 'Your journey has been paused. You can resume it from the dashboard.');
                router.back();
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to pause journey. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleResetJourney = () => {
    Alert.alert(
      'Reset Journey',
      'This will reset your journey progress. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Feature Coming Soon', 'Journey reset functionality will be available in a future update.');
          },
        },
      ]
    );
  };

  const SettingItem = ({ 
    title, 
    description, 
    value, 
    onValueChange, 
    icon 
  }: {
    title: string;
    description: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    icon: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Ionicons name={icon as any} size={24} color="#4a90a4" style={styles.settingIcon} />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingDescription}>{description}</Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#767577', true: '#4a90a4' }}
        thumbColor={value ? '#ffffff' : '#f4f3f4'}
      />
    </View>
  );

  const ActionItem = ({ 
    title, 
    description, 
    onPress, 
    icon, 
    color = '#4a90a4',
    destructive = false 
  }: {
    title: string;
    description: string;
    onPress: () => void;
    icon: string;
    color?: string;
    destructive?: boolean;
  }) => (
    <TouchableOpacity style={styles.actionItem} onPress={onPress}>
      <View style={styles.settingContent}>
        <Ionicons 
          name={icon as any} 
          size={24} 
          color={destructive ? '#FF6B6B' : color} 
          style={styles.settingIcon} 
        />
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, destructive && styles.destructiveText]}>{title}</Text>
          <Text style={styles.settingDescription}>{description}</Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.6)" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: 'Journey Settings', headerShown: false }} />
      <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.gradient}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Journey Settings</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Journey Info */}
          {journey && (
            <View style={styles.journeyInfo}>
              <Text style={styles.journeyTitle}>{journey.title}</Text>
              <Text style={styles.journeySubtitle}>
                Day {journey.currentDay || 1} of {journey.duration || journey.total_days || 21}
              </Text>
            </View>
          )}

          {/* Notifications */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notifications</Text>
            <SettingItem
              title="Push Notifications"
              description="Receive daily reminders and updates"
              value={settings.notifications}
              onValueChange={(value) => handleSettingChange('notifications', value)}
              icon="notifications"
            />
            <SettingItem
              title="Daily Reminders"
              description="Get reminded about your daily practices"
              value={settings.dailyReminders}
              onValueChange={(value) => handleSettingChange('dailyReminders', value)}
              icon="alarm"
            />
            <SettingItem
              title="Community Updates"
              description="Notifications from your healing community"
              value={settings.communityUpdates}
              onValueChange={(value) => handleSettingChange('communityUpdates', value)}
              icon="people"
            />
          </View>

          {/* Privacy */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Privacy</Text>
            <SettingItem
              title="Progress Sharing"
              description="Allow community to see your progress"
              value={settings.progressSharing}
              onValueChange={(value) => handleSettingChange('progressSharing', value)}
              icon="share"
            />
          </View>

          {/* Audio & Haptics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Audio & Haptics</Text>
            <SettingItem
              title="Sound Effects"
              description="Play sounds during practices"
              value={settings.soundEnabled}
              onValueChange={(value) => handleSettingChange('soundEnabled', value)}
              icon="volume-high"
            />
            <SettingItem
              title="Vibration"
              description="Haptic feedback for interactions"
              value={settings.vibrationEnabled}
              onValueChange={(value) => handleSettingChange('vibrationEnabled', value)}
              icon="phone-portrait"
            />
          </View>

          {/* Journey Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Journey Management</Text>
            <ActionItem
              title="Export Progress"
              description="Download your journey data"
              onPress={() => Alert.alert('Coming Soon', 'Export functionality will be available soon.')}
              icon="download"
            />
            <ActionItem
              title="Pause Journey"
              description="Temporarily pause your healing journey"
              onPress={handlePauseJourney}
              icon="pause"
              color="#FF9800"
            />
            <ActionItem
              title="Reset Journey"
              description="Start your journey from the beginning"
              onPress={handleResetJourney}
              icon="refresh"
              destructive
            />
          </View>

          {/* Support */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Support</Text>
            <ActionItem
              title="Help & FAQ"
              description="Get help with your journey"
              onPress={() => Alert.alert('Coming Soon', 'Help section will be available soon.')}
              icon="help-circle"
            />
            <ActionItem
              title="Contact Support"
              description="Reach out to our support team"
              onPress={() => Alert.alert('Coming Soon', 'Contact support will be available soon.')}
              icon="mail"
            />
            <ActionItem
              title="Emergency Support"
              description="Access crisis support resources"
              onPress={() => router.push('/emergency')}
              icon="medical"
              color="#FF6B6B"
            />
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  journeyInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    alignItems: 'center',
  },
  journeyTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  journeySubtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    fontFamily: 'Poppins-SemiBold',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
    fontFamily: 'Poppins-Medium',
  },
  settingDescription: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
  },
  destructiveText: {
    color: '#FF6B6B',
  },
});

export default JourneySettingsScreen;