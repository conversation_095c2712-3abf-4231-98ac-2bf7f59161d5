import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, ActivityIndicator } from 'react-native'; // Added ActivityIndicator
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import journeyService from '../../services/journey.service'; // Import journeyService
import { JourneyConfig } from '../../../libs/shared-types/src/lib/journey.types'; // Import JourneyConfig for preferences

// Placeholder for AI Recommendation and Journey Option types
interface AIRecommendation {
  duration: number;
  type: string; // e.g., 'heart_purification'
  reason?: string;
}

interface JourneyOption {
  duration: number;
  title: string;
  description: string;
}

const JourneySelectionScreen = () => {
  const router = useRouter();
  const params = useLocalSearchParams(); // To get assessmentId, aiRecommendedDuration etc.

  const [aiRecommendation, setAiRecommendation] = useState<AIRecommendation | null>(null);
  const [selectedDuration, setSelectedDuration] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const journeyOptions: JourneyOption[] = [
    { duration: 7, title: '7-Day Gentle Healing', description: 'A gentle introduction to spiritual renewal and mindfulness.' },
    { duration: 14, title: '14-Day Standard Healing', description: 'Focused growth on your primary layer with balanced practices.' },
    { duration: 21, title: '21-Day Moderate Healing', description: 'Deeper transformation and establishing spiritual habits.' },
    { duration: 40, title: '40-Day Intensive Healing', description: 'A comprehensive spiritual overhaul for profound change.' },
  ];

  useEffect(() => {
    const assessmentIdFromParams = params.assessmentId as string; // Keep for the submit function
    const recommendedDurationFromParams = params.aiRecommendedDuration ? parseInt(params.aiRecommendedDuration as string, 10) : null;
    const recommendedTypeFromParams = params.aiRecommendedJourneyType as string || 'heart_purification'; // Default type if not passed
    const recommendedReasonFromParams = params.aiRecommendationReason as string || `This journey is tailored to address the insights from your recent assessment, focusing on enhancing your connection with the ${recommendedTypeFromParams.replace('_', ' ')} layer.`;

    let initialSelectedDuration = null;
    let recommendationToSet: AIRecommendation | null = null;

    if (recommendedDurationFromParams && journeyOptions.some(jo => jo.duration === recommendedDurationFromParams)) {
      recommendationToSet = {
        duration: recommendedDurationFromParams,
        type: recommendedTypeFromParams,
        reason: recommendedReasonFromParams,
      };
      initialSelectedDuration = recommendedDurationFromParams;
    } else {
      // If AI rec duration isn't one of the standard options, or not provided, pick a sensible default (e.g., 14 or 21 days)
      // And make the AI recommendation section reflect this default or a general statement.
      const defaultOption = journeyOptions.find(jo => jo.duration === 21) || journeyOptions[1]; // Default to 21 or 14 days
      recommendationToSet = {
        duration: defaultOption.duration,
        type: recommendedTypeFromParams, // Still use the AI type if available
        reason: recommendedReasonFromParams, // The reason might still be generally applicable
      };
      initialSelectedDuration = defaultOption.duration;
      console.log(`AI recommended duration ${recommendedDurationFromParams} not standard or not provided, defaulting to ${initialSelectedDuration} days.`);
    }

    setAiRecommendation(recommendationToSet);
    setSelectedDuration(initialSelectedDuration);

    // Store assessmentId if needed for submission, though it's already in params
    // setAssessmentId(assessmentIdFromParams);

  }, [params.assessmentId, params.aiRecommendedDuration, params.aiRecommendedJourneyType, params.aiRecommendationReason]);

  const handleSelectOption = (duration: number) => {
    setSelectedDuration(duration);
  };

  const handleStartJourney = async () => {
    if (!selectedDuration || !aiRecommendation) {
      Alert.alert("Selection Error", "Please select a journey duration.");
      return;
    }

    const assessmentId = params.assessmentId as string;
    if (!assessmentId) {
        Alert.alert("Error", "Assessment ID is missing. Cannot start journey.");
        return;
    }

    setIsLoading(true);
    try {
      const journeyPreferences: Partial<JourneyConfig> = { // Use JourneyConfig for type safety
        duration: selectedDuration,
        // Other preferences like ruqyaIntegrationLevel, communityIntegration could be set here if UI exists
      };

      // The AI recommended journey type (e.g. heart_purification) is implicitly handled by the backend
      // when it uses the assessmentId to fetch diagnosis details that inform AI journey generation.
      // We are primarily passing the user's chosen DURATION preference here.
      console.log('🔄 Creating journey with preferences:', journeyPreferences);
      const newJourney = await journeyService.createPersonalizedJourney(assessmentId, journeyPreferences);
      console.log('✅ Journey created:', { id: newJourney?.id, status: newJourney?.status, title: newJourney?.title });

      if (newJourney && newJourney.id) {
         // The createPersonalizedJourney on the backend sets the journey to 'created' status.
         // We need to explicitly start the journey to make it 'active' so the dashboard can find it.
         console.log('🔄 Journey created with status:', newJourney.status);
         
         if (newJourney.status === 'created') {
           console.log('🔄 Starting journey...');
           const startedJourney = await journeyService.startJourney(newJourney.id);
           console.log('✅ Journey started successfully:', { id: startedJourney?.id, status: startedJourney?.status });
         }

         // Add a small delay to ensure the journey is properly saved
         await new Promise(resolve => setTimeout(resolve, 500));

         Alert.alert("Journey Created!", `Your ${selectedDuration}-day ${aiRecommendation.type.replace('_', ' ')} journey is ready.`);
         console.log('🔄 Navigating to dashboard with journeyId:', newJourney.id);
         router.replace({ pathname: '/journey/dashboard', params: { journeyId: newJourney.id } }); // Navigate to dashboard
      } else {
        throw new Error("Journey creation failed or did not return a valid journey.");
      }
    } catch (error) {
      console.error("Failed to start journey:", error);
      Alert.alert(
        "Error Starting Journey",
        (error instanceof Error ? error.message : "Could not start your healing journey. Please try again.")
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Stack.Screen options={{ title: 'Choose Your Path' }} />
      <Text style={styles.screenTitle}>Select Your Healing Journey</Text>

      {aiRecommendation && (
        <View style={styles.aiRecommendationSection}>
          <Text style={styles.sectionTitle}><Ionicons name="sparkles-outline" size={22} color="#4DD0E1"/> AI Recommendation</Text>
          <TouchableOpacity
            style={[
              styles.optionCard,
              selectedDuration === aiRecommendation.duration && styles.selectedOptionCard,
            ]}
            onPress={() => handleSelectOption(aiRecommendation.duration)}
          >
            <Text style={styles.optionTitle}>{aiRecommendation.duration}-Day {aiRecommendation.type.replace('_', ' ')} Journey</Text>
            {aiRecommendation.reason && <Text style={styles.optionDescription}>{aiRecommendation.reason}</Text>}
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.optionsSection}>
        <Text style={styles.sectionTitle}>Or Choose a Different Path:</Text>
        {journeyOptions.map((option) => (
          <TouchableOpacity
            key={option.duration}
            style={[
              styles.optionCard,
              selectedDuration === option.duration && styles.selectedOptionCard,
            ]}
            onPress={() => handleSelectOption(option.duration)}
            disabled={isLoading}
          >
            <Text style={styles.optionTitle}>{option.title}</Text>
            <Text style={styles.optionDescription}>{option.description}</Text>
            {selectedDuration === option.duration && <Ionicons name="checkmark-circle" size={24} color="#4CAF50" style={styles.checkmarkIcon}/>}
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity
        style={[styles.startButton, (!selectedDuration || isLoading) && styles.startButtonDisabled]}
        onPress={handleStartJourney}
        disabled={!selectedDuration || isLoading}
      >
        {isLoading ? (
            <ActivityIndicator color="#fff" />
        ) : (
            <Text style={styles.startButtonText}>Start My Healing Journey</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212', // Dark theme
    paddingVertical: 20,
  },
  screenTitle: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 25,
    paddingHorizontal: 15,
  },
  aiRecommendationSection: {
    marginBottom: 30,
    paddingHorizontal: 15,
  },
  optionsSection: {
    marginBottom: 30,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.9)',
    marginBottom: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionCard: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  selectedOptionCard: {
    borderColor: '#4DD0E1', // Accent color for selection
    backgroundColor: 'rgba(77, 208, 225, 0.1)',
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  optionDescription: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.7)',
    lineHeight: 20,
  },
  checkmarkIcon: {
    position: 'absolute',
    right: 15,
    top: 15,
  },
  startButton: {
    backgroundColor: '#5cb85c', // Green color
    paddingVertical: 18,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 15,
    marginBottom: 40, // Ensure it's above any tab bar
  },
  startButtonDisabled: {
    backgroundColor: '#777',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default JourneySelectionScreen;
