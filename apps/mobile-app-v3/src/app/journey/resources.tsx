/**
 * Journey Resources Screen
 * Provides access to healing resources, Islamic content, and educational materials
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Dimensions,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import journeyService from '../../services/journey.service';

const { width } = Dimensions.get('window');

interface Resource {
  id: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'audio' | 'practice' | 'dua' | 'hadith';
  category: 'healing' | 'spiritual' | 'educational' | 'crisis' | 'community';
  content?: string;
  url?: string;
  duration?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  isBookmarked?: boolean;
}

const JourneyResourcesScreen = () => {
  const router = useRouter();
  const [resources, setResources] = useState<Resource[]>([]);
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  const categories = [
    { id: 'all', label: 'All', icon: 'apps' },
    { id: 'healing', label: 'Healing', icon: 'heart' },
    { id: 'spiritual', label: 'Spiritual', icon: 'sparkles' },
    { id: 'educational', label: 'Educational', icon: 'book' },
    { id: 'crisis', label: 'Crisis Support', icon: 'medical' },
    { id: 'community', label: 'Community', icon: 'people' },
  ];

  const types = [
    { id: 'all', label: 'All Types', icon: 'grid' },
    { id: 'article', label: 'Articles', icon: 'document-text' },
    { id: 'video', label: 'Videos', icon: 'videocam' },
    { id: 'audio', label: 'Audio', icon: 'headset' },
    { id: 'practice', label: 'Practices', icon: 'fitness' },
    { id: 'dua', label: 'Du\'a', icon: 'hand-left' },
    { id: 'hadith', label: 'Hadith', icon: 'library' },
  ];

  useEffect(() => {
    loadResources();
  }, [selectedCategory, selectedType, searchQuery]);

  useEffect(() => {
    filterResources();
  }, [resources, searchQuery, selectedCategory, selectedType]);

  const loadResources = async () => {
    try {
      setIsLoading(true);
      
      // Build query parameters
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }
      if (selectedType !== 'all') {
        params.append('type', selectedType);
      }
      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }

      // Call the real API
      const response = await journeyService.getJourneyResources(params.toString());
      
      if (response.status === 'success' && response.data?.resources) {
        setResources(response.data.resources);
        console.log('✅ Loaded resources from API:', response.data.resources.length);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error loading resources:', error);
      Alert.alert('Error', 'Failed to load resources. Please try again.');
      
      // Fallback to empty array instead of mock data
      setResources([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filterResources = () => {
    let filtered = resources;

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(resource =>
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(resource => resource.category === selectedCategory);
    }

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(resource => resource.type === selectedType);
    }

    setFilteredResources(filtered);
  };

  const handleResourcePress = async (resource: Resource) => {
    try {
      // Get full resource details from API
      const response = await journeyService.getResourceById(resource.id);
      const fullResource = response.data?.resource || resource;

      if (fullResource.type === 'article' || fullResource.type === 'dua' || fullResource.type === 'hadith') {
        // Show content in a scrollable modal
        Alert.alert(
          fullResource.title,
          fullResource.content || fullResource.description,
          [
            { text: 'Close', style: 'cancel' },
            { text: 'Bookmark', onPress: () => toggleBookmark(fullResource.id) }
          ],
          { 
            cancelable: true,
          }
        );
      } else if (fullResource.type === 'video') {
        // For videos, show description and option to open external link
        Alert.alert(
          '🎥 Video Resource',
          `${fullResource.description}\n\nDuration: ${fullResource.duration || 'Unknown'}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Watch Video', 
              onPress: () => {
                if (fullResource.url) {
                  // In a real app, you'd open the video URL or navigate to a video player
                  Alert.alert('Video Player', `Opening: ${fullResource.title}\n\nNote: Video player integration coming soon!`);
                } else {
                  Alert.alert('Video Unavailable', 'This video is not yet available.');
                }
              }
            }
          ]
        );
      } else if (fullResource.type === 'audio') {
        // For audio, show description and option to play
        Alert.alert(
          '🎧 Audio Resource',
          `${fullResource.description}\n\nDuration: ${fullResource.duration || 'Unknown'}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Play Audio', 
              onPress: () => {
                if (fullResource.url) {
                  // In a real app, you'd integrate with an audio player
                  Alert.alert('Audio Player', `Playing: ${fullResource.title}\n\nNote: Audio player integration coming soon!`);
                } else {
                  Alert.alert('Audio Unavailable', 'This audio is not yet available.');
                }
              }
            }
          ]
        );
      } else if (fullResource.type === 'practice') {
        // Show practice guide with detailed instructions
        Alert.alert(
          '🤲 Practice Guide',
          fullResource.content || fullResource.description,
          [
            { text: 'Close', style: 'cancel' },
            { 
              text: 'Start Practice', 
              onPress: () => Alert.alert(
                'Practice Started', 
                `You are now following the ${fullResource.title} practice.\n\nTake your time and follow the instructions mindfully.`
              ) 
            }
          ]
        );
      }

      // Log resource interaction for analytics
      console.log('📊 Resource accessed:', {
        resourceId: fullResource.id,
        type: fullResource.type,
        title: fullResource.title
      });

    } catch (error) {
      console.error('❌ Error loading resource details:', error);
      Alert.alert(
        'Error',
        'Unable to load resource details. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const toggleBookmark = (resourceId: string) => {
    setResources(prev =>
      prev.map(resource =>
        resource.id === resourceId
          ? { ...resource, isBookmarked: !resource.isBookmarked }
          : resource
      )
    );
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = types.find(t => t.id === type);
    return typeConfig?.icon || 'document';
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'beginner': return '#4CAF50';
      case 'intermediate': return '#FF9800';
      case 'advanced': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading resources...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: 'Journey Resources', headerShown: false }} />
      <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.gradient}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Journey Resources</Text>
          <TouchableOpacity style={styles.searchButton}>
            <Ionicons name="search" size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="rgba(255, 255, 255, 0.6)" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search resources..."
              placeholderTextColor="rgba(255, 255, 255, 0.6)"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        {/* Simplified Filter Tabs */}
        <View style={styles.filterContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filterTabs}
          >
            {categories.slice(0, 4).map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.filterTab,
                  selectedCategory === category.id && styles.filterTabActive
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <Text
                  style={[
                    styles.filterTabText,
                    selectedCategory === category.id && styles.filterTabTextActive
                  ]}
                >
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Resources List */}
        <ScrollView
          style={styles.resourcesList}
          contentContainerStyle={styles.resourcesContainer}
          showsVerticalScrollIndicator={false}
        >
          {filteredResources.length > 0 ? (
            filteredResources.map(resource => (
              <TouchableOpacity
                key={resource.id}
                style={styles.resourceCard}
                onPress={() => handleResourcePress(resource)}
              >
                <View style={styles.resourceHeader}>
                  <View style={styles.resourceTypeContainer}>
                    <Ionicons
                      name={getTypeIcon(resource.type) as any}
                      size={20}
                      color="#4a90a4"
                    />
                    <Text style={styles.resourceType}>{resource.type.toUpperCase()}</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.bookmarkButton}
                    onPress={() => toggleBookmark(resource.id)}
                  >
                    <Ionicons
                      name={resource.isBookmarked ? 'bookmark' : 'bookmark-outline'}
                      size={20}
                      color={resource.isBookmarked ? '#FFD700' : 'rgba(255, 255, 255, 0.6)'}
                    />
                  </TouchableOpacity>
                </View>

                <Text style={styles.resourceTitle}>{resource.title}</Text>
                <Text style={styles.resourceDescription}>{resource.description}</Text>

                <View style={styles.resourceMeta}>
                  {resource.duration && (
                    <View style={styles.metaItem}>
                      <Ionicons name="time-outline" size={14} color="rgba(255, 255, 255, 0.6)" />
                      <Text style={styles.metaText}>{resource.duration}</Text>
                    </View>
                  )}
                  {resource.difficulty && (
                    <View style={styles.metaItem}>
                      <View
                        style={[
                          styles.difficultyDot,
                          { backgroundColor: getDifficultyColor(resource.difficulty) }
                        ]}
                      />
                      <Text style={styles.metaText}>{resource.difficulty}</Text>
                    </View>
                  )}
                </View>

                <View style={styles.resourceTags}>
                  {resource.tags.slice(0, 3).map(tag => (
                    <View key={tag} style={styles.tag}>
                      <Text style={styles.tagText}>{tag}</Text>
                    </View>
                  ))}
                  {resource.tags.length > 3 && (
                    <Text style={styles.moreTagsText}>+{resource.tags.length - 3} more</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="search" size={64} color="rgba(255, 255, 255, 0.3)" />
              <Text style={styles.emptyStateTitle}>No Resources Found</Text>
              <Text style={styles.emptyStateText}>
                Try adjusting your search or filter criteria
              </Text>
            </View>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Poppins-Regular',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    color: '#ffffff',
    fontSize: 16,
    marginLeft: 12,
    fontFamily: 'Poppins-Regular',
  },
  filterContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  filterTabs: {
    paddingVertical: 8,
  },
  filterTab: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginRight: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  filterTabActive: {
    backgroundColor: '#4a90a4',
    borderColor: '#4a90a4',
  },
  filterTabText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Poppins-Medium',
  },
  filterTabTextActive: {
    color: '#ffffff',
    fontWeight: '600',
  },
  resourcesList: {
    flex: 1,
  },
  resourcesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  resourceCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  resourceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  resourceTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resourceType: {
    color: '#4a90a4',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
    fontFamily: 'Poppins-SemiBold',
  },
  bookmarkButton: {
    padding: 4,
  },
  resourceTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  resourceDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
    fontFamily: 'Poppins-Regular',
  },
  resourceMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  metaText: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 12,
    marginLeft: 4,
    fontFamily: 'Poppins-Regular',
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  resourceTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  tag: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  tagText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 10,
    fontFamily: 'Poppins-Regular',
  },
  moreTagsText: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 10,
    fontStyle: 'italic',
    fontFamily: 'Poppins-Regular',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  emptyStateText: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
});

export default JourneyResourcesScreen;