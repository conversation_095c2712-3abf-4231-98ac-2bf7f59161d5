/**
 * Journey Progress Overview Screen
 * Provides comprehensive view of journey progress and analytics
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import journeyService from '../../services/journey.service';
import { JourneyAnalytics, Journey } from '../../../libs/shared-types/src/lib/journey.types';
import { ProgressCircle } from '../../components/ProgressCircle';

const { width } = Dimensions.get('window');

interface ProgressMetric {
  id: string;
  title: string;
  value: number;
  maxValue: number;
  unit: string;
  icon: string;
  color: string;
  description: string;
}

const JourneyProgressScreen = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const journeyId = params.journeyId as string;

  const [journey, setJourney] = useState<Journey | null>(null);
  const [analytics, setAnalytics] = useState<JourneyAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'all'>('week');

  useEffect(() => {
    if (!journeyId) {
      Alert.alert("Error", "Journey ID is missing.");
      router.back();
      return;
    }
    loadProgressData();
  }, [journeyId]);

  const loadProgressData = async () => {
    try {
      setIsLoading(true);
      
      // Load journey details and analytics using proper REST endpoints
      const [journeyData, analyticsData] = await Promise.all([
        journeyService.getJourneyById(journeyId),
        journeyService.getJourneyAnalytics(journeyId)
      ]);

      setJourney(journeyData);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading progress data:', error);
      Alert.alert('Error', 'Failed to load progress data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getProgressMetrics = (): ProgressMetric[] => {
    if (!analytics) return [];

    return [
      {
        id: 'completion',
        title: 'Journey Completion',
        value: analytics.completionRate,
        maxValue: 100,
        unit: '%',
        icon: 'checkmark-circle',
        color: '#4CAF50',
        description: 'Overall progress through your healing journey'
      },
      {
        id: 'practice',
        title: 'Practice Adherence',
        value: analytics.practiceAdherence,
        maxValue: 100,
        unit: '%',
        icon: 'fitness',
        color: '#2196F3',
        description: 'Consistency in completing daily practices'
      },
      {
        id: 'community',
        title: 'Community Engagement',
        value: analytics.communityEngagement,
        maxValue: 100,
        unit: '%',
        icon: 'people',
        color: '#FF9800',
        description: 'Participation in community activities'
      },
      {
        id: 'spiritual',
        title: 'Spiritual Development',
        value: analytics.spiritualDevelopment.spiritualConnection,
        maxValue: 100,
        unit: '%',
        icon: 'sparkles',
        color: '#9C27B0',
        description: 'Growth in spiritual connection and practices'
      },
      {
        id: 'wellness',
        title: 'Overall Wellness',
        value: analytics.symptomImprovement.overallWellness,
        maxValue: 100,
        unit: '%',
        icon: 'heart',
        color: '#E91E63',
        description: 'Improvement in overall well-being'
      },
      {
        id: 'islamic',
        title: 'Islamic Practice Integration',
        value: analytics.spiritualDevelopment.islamicPracticeIntegration,
        maxValue: 100,
        unit: '%',
        icon: 'moon',
        color: '#607D8B',
        description: 'Integration of Islamic practices into daily life'
      },
    ];
  };

  const formatLayerName = (layerName: string): string => {
    const layerDisplayNames: Record<string, string> = {
      'jism': 'Jism (Body)',
      'nafs': 'Nafs (Soul)',
      'aql': 'Aql (Mind)',
      'qalb': 'Qalb (Heart)',
      'ruh': 'Ruh (Spirit)',
    };
    
    return layerDisplayNames[layerName.toLowerCase()] || layerName.charAt(0).toUpperCase() + layerName.slice(1);
  };

  const getTimeframeLabel = (timeframe: string): string => {
    switch (timeframe) {
      case 'week': return 'Last 7 Days';
      case 'month': return 'Last 30 Days';
      case 'all': return 'All Time';
      default: return 'Last 7 Days';
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading progress data...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (!journey || !analytics) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color="#ffffff" />
          <Text style={styles.errorTitle}>Unable to Load Progress</Text>
          <Text style={styles.errorText}>
            We couldn't load your progress data. Please try again.
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadProgressData}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  const progressMetrics = getProgressMetrics();

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: 'Journey Progress', headerShown: false }} />
      <LinearGradient colors={['#1a365d', '#2d5a87', '#4a90a4']} style={styles.gradient}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Journey Progress</Text>
            <Text style={styles.headerSubtitle}>{journey.title}</Text>
          </View>
          <TouchableOpacity style={styles.shareButton}>
            <Ionicons name="share-outline" size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          {/* Overall Progress */}
          <View style={styles.overallProgressSection}>
            <Text style={styles.sectionTitle}>Overall Progress</Text>
            <View style={styles.overallProgressCard}>
              <View style={styles.progressCircleContainer}>
                <ProgressCircle
                  progress={analytics.completionRate}
                  size={120}
                  strokeWidth={12}
                  color="#4CAF50"
                />
                <View style={styles.progressInfo}>
                  <Text style={styles.progressPercentage}>
                    {Math.round(analytics.completionRate)}%
                  </Text>
                  <Text style={styles.progressLabel}>Complete</Text>
                </View>
              </View>
              <View style={styles.journeyStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    Day {journey.current_day || journey.currentDay || 1}
                  </Text>
                  <Text style={styles.statLabel}>Current Day</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {journey.total_days || journey.configuration?.duration || 21}
                  </Text>
                  <Text style={styles.statLabel}>Total Days</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {formatLayerName(analytics.symptomImprovement.primaryLayer || 'qalb')}
                  </Text>
                  <Text style={styles.statLabel}>Focus Layer</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Timeframe Selector */}
          <View style={styles.timeframeSection}>
            <Text style={styles.sectionTitle}>View Progress For</Text>
            <View style={styles.timeframeButtons}>
              {(['week', 'month', 'all'] as const).map(timeframe => (
                <TouchableOpacity
                  key={timeframe}
                  style={[
                    styles.timeframeButton,
                    selectedTimeframe === timeframe && styles.timeframeButtonActive
                  ]}
                  onPress={() => setSelectedTimeframe(timeframe)}
                >
                  <Text
                    style={[
                      styles.timeframeButtonText,
                      selectedTimeframe === timeframe && styles.timeframeButtonTextActive
                    ]}
                  >
                    {getTimeframeLabel(timeframe)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Progress Metrics Grid */}
          <View style={styles.metricsSection}>
            <Text style={styles.sectionTitle}>Progress Metrics</Text>
            <View style={styles.metricsGrid}>
              {progressMetrics.map(metric => (
                <View key={metric.id} style={styles.metricCard}>
                  <View style={styles.metricHeader}>
                    <View style={[styles.metricIcon, { backgroundColor: metric.color }]}>
                      <Ionicons name={metric.icon as any} size={20} color="#ffffff" />
                    </View>
                    <Text style={styles.metricValue}>
                      {Math.round(metric.value)}{metric.unit}
                    </Text>
                  </View>
                  <Text style={styles.metricTitle}>{metric.title}</Text>
                  <Text style={styles.metricDescription}>{metric.description}</Text>
                  <View style={styles.progressBar}>
                    <View
                      style={[
                        styles.progressBarFill,
                        {
                          width: `${(metric.value / metric.maxValue) * 100}%`,
                          backgroundColor: metric.color,
                        },
                      ]}
                    />
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.actionsSection}>
            <Text style={styles.sectionTitle}>Detailed Views</Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={styles.actionCard}
                onPress={() => router.push({
                  pathname: '/journey/progress/wheel',
                  params: { journeyId }
                })}
              >
                <Ionicons name="aperture-outline" size={32} color="#4a90a4" />
                <Text style={styles.actionTitle}>Healing Wheel</Text>
                <Text style={styles.actionDescription}>
                  View your five-layer progress visualization
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={() => router.push({
                  pathname: '/journey/progress/dashboard',
                  params: { journeyId, journeyTitle: journey.title }
                })}
              >
                <Ionicons name="analytics-outline" size={32} color="#4a90a4" />
                <Text style={styles.actionTitle}>Metrics Dashboard</Text>
                <Text style={styles.actionDescription}>
                  Detailed analytics and trends
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Recommendations */}
          {analytics.nextStepRecommendations && analytics.nextStepRecommendations.length > 0 && (
            <View style={styles.recommendationsSection}>
              <Text style={styles.sectionTitle}>Recommendations</Text>
              {analytics.nextStepRecommendations.map((recommendation, index) => (
                <View key={index} style={styles.recommendationCard}>
                  <Ionicons name="bulb-outline" size={20} color="#FFD700" />
                  <Text style={styles.recommendationText}>{recommendation}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Graduation Status */}
          {analytics.graduationReadiness && (
            <View style={styles.graduationSection}>
              <View style={styles.graduationCard}>
                <Ionicons name="school-outline" size={32} color="#4CAF50" />
                <Text style={styles.graduationTitle}>Ready for Graduation!</Text>
                <Text style={styles.graduationText}>
                  You've made excellent progress and are ready to complete your healing journey.
                </Text>
                <TouchableOpacity style={styles.graduationButton}>
                  <Text style={styles.graduationButtonText}>Begin Graduation Process</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Poppins-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  errorText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    fontFamily: 'Poppins-Regular',
  },
  retryButton: {
    backgroundColor: '#4a90a4',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  shareButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  overallProgressSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    fontFamily: 'Poppins-SemiBold',
  },
  overallProgressCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressCircleContainer: {
    position: 'relative',
    marginRight: 24,
  },
  progressInfo: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressPercentage: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: '700',
    fontFamily: 'Poppins-Bold',
  },
  progressLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
  },
  journeyStats: {
    flex: 1,
  },
  statItem: {
    marginBottom: 16,
  },
  statValue: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  statLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
  },
  timeframeSection: {
    marginBottom: 32,
  },
  timeframeButtons: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 4,
  },
  timeframeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  timeframeButtonActive: {
    backgroundColor: '#4a90a4',
  },
  timeframeButtonText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Poppins-Medium',
  },
  timeframeButtonTextActive: {
    color: '#ffffff',
  },
  metricsSection: {
    marginBottom: 32,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    width: (width - 60) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metricIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metricValue: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '700',
    fontFamily: 'Poppins-Bold',
  },
  metricTitle: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  metricDescription: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 12,
    fontFamily: 'Poppins-Regular',
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  actionsSection: {
    marginBottom: 32,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - 60) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  actionTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
    fontFamily: 'Poppins-SemiBold',
  },
  actionDescription: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
    fontFamily: 'Poppins-Regular',
  },
  recommendationsSection: {
    marginBottom: 32,
  },
  recommendationCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  recommendationText: {
    color: '#ffffff',
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 12,
    flex: 1,
    fontFamily: 'Poppins-Regular',
  },
  graduationSection: {
    marginBottom: 32,
  },
  graduationCard: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  graduationTitle: {
    color: '#4CAF50',
    fontSize: 20,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  graduationText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
    fontFamily: 'Poppins-Regular',
  },
  graduationButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  graduationButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
});

export default JourneyProgressScreen;