import React from 'react';

import { StyleSheet, TouchableOpacity, Switch } from 'react-native';

import { Feather } from '@expo/vector-icons';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Text } from './ui/Text';
import { View } from './ui/View';

interface SettingsItemProps {
  title: string;
  description?: string;
  icon: string;
  iconColor?: string;
  type: 'toggle' | 'navigation' | 'action';
  value?: boolean;
  onToggle?: (value: boolean) => void;
  onPress?: () => void;
  rightElement?: React.ReactNode;
}

export const SettingsItem = ({
  title,
  description,
  icon,
  iconColor,
  type,
  value,
  onToggle,
  onPress,
  rightElement,
}: SettingsItemProps) => {
  const handlePress = () => {
    if (type === 'toggle' && onToggle) {
      onToggle(!value);
    } else if (onPress) {
      onPress();
    }
  };

  const isActionable = type === 'navigation' || type === 'action';
  const iconBgColor = iconColor || colors.primary;

  return (
    <TouchableOpacity
      style={[styles.container, { borderBottomColor: colors.border }]}
      onPress={handlePress}
      disabled={type === 'toggle' && !onToggle && !onPress}
    >
      <View
        style={[styles.iconContainer, { backgroundColor: iconBgColor + '15' }]}
      ></View>

      <View style={styles.contentContainer}>
        <Text variant="subtitle">{title}</Text>
        {description && (
          <Text
            variant="caption"
            color="textSecondary"
            style={styles.description}
          >
            {description}
          </Text>
        )}
      </View>

      <View style={styles.rightContainer}>
        {type === 'toggle' && onToggle && (
          <Switch
            value={value}
            onValueChange={onToggle}
            trackColor={{ false: colors.border, true: colors.primary + '80' }}
            thumbColor={value ? colors.primary : colors.background}
          />
        )}
        {type === 'navigation' && (
          <Feather
            name="chevron-right"
            size={20}
            color={colors.textSecondary}
          />
        )}
        {rightElement}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Theme.spacing.m,
    borderBottomWidth: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.m,
  },
  contentContainer: {
    flex: 1,
  },
  description: {
    marginTop: 2,
  },
  rightContainer: {
    marginLeft: Theme.spacing.s,
  },
});
