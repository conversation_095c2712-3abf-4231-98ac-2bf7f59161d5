import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Path, Circle, Text as SvgText, G } from 'react-native-svg';
import { LayerProgressData } from '../../app/journey/progress/wheel'; // Assuming path

interface HealingWheelProps {
  layersData: LayerProgressData[]; // Expect 5 layers
  size?: number;
  strokeWidth?: number;
}

// Helper function to format layer names for display in the wheel
const formatLayerDisplayName = (layerName: string): string => {
  const layerDisplayNames: Record<string, string> = {
    'Jism': 'JISM',
    'Nafs': 'NAFS', 
    'Aql': 'AQL',
    'Qalb': 'QALB',
    'Ruh': 'RUH',
  };
  
  return layerDisplayNames[layerName] || layerName.toUpperCase();
};

const HealingWheel: React.FC<HealingWheelProps> = ({
  layersData,
  size = 320, // Increased default size
  strokeWidth = 35, // Slightly reduced stroke width for better text visibility
}) => {
  if (!layersData || layersData.length !== 5) {
    // Fallback or error for incorrect data
    return <Text style={styles.errorText}>Healing Wheel requires data for 5 layers.</Text>;
  }

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;

  // Helper to calculate SVG path for an arc segment
  const getPathForSegment = (startAngle: number, endAngle: number, segmentRadius: number): string => {
    const start = polarToCartesian(center, center, segmentRadius, endAngle);
    const end = polarToCartesian(center, center, segmentRadius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';
    const d = [
      'M', start.x, start.y,
      'A', segmentRadius, segmentRadius, 0, largeArcFlag, 0, end.x, end.y,
    ].join(' ');
    return d;
  };

  const polarToCartesian = (centerX: number, centerY: number, r: number, angleInDegrees: number) => {
    const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
    return {
      x: centerX + r * Math.cos(angleInRadians),
      y: centerY + r * Math.sin(angleInRadians),
    };
  };

  const segmentAngle = 360 / 5; // 72 degrees per segment
  const segmentGap = 2; // Small gap between segments

  const getProgressColor = (progress: number): string => {
    if (progress < 34) return '#F44336'; // Red
    if (progress < 67) return '#FFC107'; // Yellow
    return '#4CAF50'; // Green
  };

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <G rotation="-90" origin={`${center}, ${center}`}>
          {layersData.map((layer, index) => {
            const startAngleBase = index * segmentAngle;
            const endAngleBase = (index + 1) * segmentAngle - segmentGap;

            const progressAngle = (layer.progressValue / 100) * (segmentAngle - segmentGap);
            const endAngleProgress = startAngleBase + progressAngle;

            const baseSegmentPath = getPathForSegment(startAngleBase, endAngleBase, radius);
            const progressSegmentPath = getPathForSegment(startAngleBase, endAngleProgress, radius);

            const color = layer.color || getProgressColor(layer.progressValue);

            const midAngleLabel = startAngleBase + (segmentAngle - segmentGap) / 2;
            // Position labels further outside for better visibility
            const labelRadius = radius + strokeWidth / 2 + 25; // Increased distance from wheel
            const labelPos = polarToCartesian(center, center, labelRadius, midAngleLabel + 90);

            const progressTextRadius = radius - strokeWidth/2; // Position progress text in center of segment
            const progressTextPos = polarToCartesian(center, center, progressTextRadius, midAngleLabel + 90);


            return (
              <G key={layer.layerName}>
                {/* Background track for the segment */}
                <Path
                  d={baseSegmentPath}
                  fill="none"
                  stroke="rgba(255, 255, 255, 0.15)" // Light grey track
                  strokeWidth={strokeWidth}
                  strokeLinecap="butt"
                />
                {/* Progress arc for the segment */}
                {layer.progressValue > 0 && (
                    <Path
                    d={progressSegmentPath}
                    fill="none"
                    stroke={color}
                    strokeWidth={strokeWidth}
                    strokeLinecap="round" // Rounded ends for progress
                    />
                )}
                {/* Layer Name Label - No rotation for better readability */}
                 <SvgText
                    x={labelPos.x}
                    y={labelPos.y}
                    fill="#FFFFFF"
                    fontSize="14"
                    fontWeight="bold"
                    textAnchor="middle"
                    alignmentBaseline="central"
                >
                    {formatLayerDisplayName(layer.layerName)}
                </SvgText>
                {/* Progress Percentage Text on segment */}
                <SvgText
                    x={progressTextPos.x}
                    y={progressTextPos.y}
                    fill="#FFFFFF"
                    fontSize="12"
                    fontWeight="bold"
                    textAnchor="middle"
                    alignmentBaseline="central"
                >
                    {`${layer.progressValue}%`}
                </SvgText>
              </G>
            );
          })}
        </G>
        {/* Central Circle */}
        <Circle cx={center} cy={center} r={radius - strokeWidth + 5} fill="#1F1F1F" />
        <SvgText
            x={center}
            y={center - 12}
            fill="white"
            fontSize="18"
            fontWeight="bold"
            textAnchor="middle"
            alignmentBaseline="central"
        >
            Overall
        </SvgText>
        <SvgText
            x={center}
            y={center + 15}
            fill="#4DD0E1"
            fontSize="22"
            fontWeight="bold"
            textAnchor="middle"
            alignmentBaseline="central"
        >
            {/* Placeholder for overall progress */}
            {`${Math.round(layersData.reduce((sum, l) => sum + l.progressValue, 0) / 5)}%`}
        </SvgText>
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
      color: 'red',
      fontSize: 16,
  }
});

export default HealingWheel;
