import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { DailyPractice, PracticeType } from '../../../../../libs/shared-types/src/lib/journey.types';
import { DailyPracticeCard } from '../DailyPracticeCard'; // Assuming this exists and can be a fallback

// Import the specific view components
import MorningCheckInView from './daily_modules/MorningCheckInView';
import NameOfAllahView from './daily_modules/NameOfAllahView';
import QuranicVerseView from './daily_modules/QuranicVerseView';
import PersonalReflectionJournalingView from './daily_modules/PersonalReflectionJournalingView';
import SunnahPracticeView from './daily_modules/SunnahPracticeView';

interface DailyModuleComponentWrapperProps {
  practice: DailyPractice;
  onPress: (practice: DailyPractice) => void; // For fallback DailyPracticeCard navigation
  onProgressSubmit: (practiceId: string, componentProgress: any) => void; // For new component views
}

const DailyModuleComponentWrapper: React.FC<DailyModuleComponentWrapperProps> = ({ practice, onPress, onProgressSubmit }) => {
  // Simplified debugging
  if (!practice) {
    return (
      <View style={styles.componentContainer}>
        <Text style={styles.placeholderText}>No practice data</Text>
      </View>
    );
  }

  // Render the actual component based on type
  return (
    <View style={styles.componentContainer}>
      {practice.type === 'MorningCheckIn' && (
        <MorningCheckInView practice={practice} onProgressSubmit={onProgressSubmit || (() => {})} />
      )}
      {practice.type === 'NameOfAllahSpotlight' && (
        <NameOfAllahView practice={practice} onProgressSubmit={onProgressSubmit || (() => {})} />
      )}
      {practice.type === 'QuranicVerseReflection' && (
        <QuranicVerseView practice={practice} onProgressSubmit={onProgressSubmit || (() => {})} />
      )}
      {practice.type === 'PersonalReflectionJournaling' && (
        <PersonalReflectionJournalingView practice={practice} onProgressSubmit={onProgressSubmit || (() => {})} />
      )}
      {practice.type === 'SunnahPractice' && (
        <SunnahPracticeView practice={practice} onProgressSubmit={onProgressSubmit || (() => {})} />
      )}
      {!['MorningCheckIn', 'NameOfAllahSpotlight', 'QuranicVerseReflection', 'PersonalReflectionJournaling', 'SunnahPractice'].includes(practice.type) && (
        <Text style={styles.placeholderText}>Unsupported type: {practice.type}</Text>
      )}
    </View>
  );

  // For the new component types, we might want a different wrapper or just the content directly.
  // If DailyPracticeCard styling is desired for all, wrap renderContent() in a TouchableOpacity similar to DailyPracticeCard.
  // For now, just rendering the specific content.
  // The onPress handler might need to be invoked differently or not at all for these new views if they are not card-like.

  // Check if practice and practice.type exist before proceeding
  if (!practice || !practice.type) {
    return (
      <View style={styles.componentContainer}>
        <Text style={styles.placeholderText}>Invalid practice data</Text>
      </View>
    );
  }

  // Define the new component types that should use specific views
  const newComponentTypes: PracticeType[] = [
    'MorningCheckIn',
    'NameOfAllahSpotlight',
    'QuranicVerseReflection',
    'PersonalReflectionJournaling',
    'SunnahPractice',
  ];

  // Check if this is one of the new component types
  const isNewComponentType = newComponentTypes.includes(practice.type);

  if (isNewComponentType) {
     // For the new 5 components, render their specific content directly
     return (
        <View style={styles.componentContainer}>
            {renderContent()}
        </View>
     );
  } else {
    // Fallback to DailyPracticeCard for existing types that might use it
    return <DailyPracticeCard practice={practice as any} onPress={() => onPress(practice)} />;
  }
};

const styles = StyleSheet.create({
  componentContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  placeholderText: {
    color: '#fff',
    fontSize: 14,
  },
});

export default DailyModuleComponentWrapper;
