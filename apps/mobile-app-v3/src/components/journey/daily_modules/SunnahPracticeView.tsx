import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { DailyPractice } from '../../../../libs/shared-types/src/lib/journey.types';
import { Ionicons } from '@expo/vector-icons';

// Define interfaces for structured steps and duas if they have a consistent format
interface PracticeStep {
  stepNumber?: number; // Optional if just an array of strings
  instruction: string;
  durationMinutes?: number;
  duaArabic?: string;
  duaTranslation?: string;
}
interface PracticeDua {
  name?: string;
  arabic: string;
  translation: string;
  audioUrl?: string;
}

interface SunnahPracticeViewProps {
  practice: DailyPractice;
  onProgressSubmit: (practiceId: string, progressData: any) => void; // Updated signature
}

const SunnahPracticeView: React.FC<SunnahPracticeViewProps> = ({ practice, onProgressSubmit }) => {
  const details = practice.componentDetails || practice.componentData || {};
  const steps: (PracticeStep | string)[] = details.stepsJson || details.steps || [];
  const duas: PracticeDua[] = details.duasJson || details.duas || [];
  const [isCompleted, setIsCompleted] = useState(false);

  const handleComplete = () => {
    if (isCompleted) return;
    onProgressSubmit(practice.id, { // Pass practice.id as first arg
      practiceId: practice.id,
      title: practice.title,
      completed: true,
    });
    setIsCompleted(true);
  };

  const getCategoryIcon = (category?: string) => {
    switch (category?.toLowerCase()) {
      case 'physical': return 'body-outline';
      case 'spiritual': return 'sparkles-outline';
      case 'social': return 'people-outline';
      case 'mental': return 'bulb-outline';
      case 'emotional': return 'happy-outline';
      default: return 'leaf-outline';
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainerScrollView}>
      <View style={styles.header}>
        <Ionicons name={getCategoryIcon(details.category)} size={28} color="#4DD0E1" style={styles.icon} />
        <Text style={styles.title}>{practice.title} <Text style={styles.categoryText}>({details.category || 'General'})</Text></Text>
      </View>
      <Text style={styles.description}>{details.description || practice.description}</Text>

      {details.intention && (
        <View style={styles.section}>
          <Text style={styles.label}>Intention (Niyyah):</Text>
          <Text style={styles.textBlockItalic}>{details.intention}</Text>
        </View>
      )}

      {steps.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.label}>Steps to Follow:</Text>
          {steps.map((step, index) => {
            // Handle both old format (string/object with stepNumber) and new format (object with action)
            let stepNumber, instruction, duaText, mindfulnessNote;
            
            if (typeof step === 'string') {
              stepNumber = index + 1;
              instruction = step;
              duaText = null;
              mindfulnessNote = null;
            } else if (step.action) {
              // New backend format
              stepNumber = index + 1;
              instruction = step.action;
              duaText = step.dua;
              mindfulnessNote = step.mindfulness;
            } else {
              // Old format
              stepNumber = step.stepNumber || index + 1;
              instruction = step.instruction;
              duaText = step.duaArabic;
              mindfulnessNote = step.duaTranslation;
            }

            return (
              <View key={`step-${index}`} style={styles.stepItemContainer}>
                <View style={styles.stepNumberContainer}>
                    <Text style={styles.stepNumberText}>{stepNumber}</Text>
                </View>
                <View style={styles.stepContent}>
                  <Text style={styles.stepInstructionText}>{instruction}</Text>
                  {mindfulnessNote && (
                    <Text style={styles.mindfulnessText}>💭 {mindfulnessNote}</Text>
                  )}
                  {duaText && (
                    <View style={styles.stepDuaWrapper}>
                      <Text style={styles.stepDuaArabic}>{duaText}</Text>
                    </View>
                  )}
                </View>
              </View>
            );
          })}
        </View>
      )}

      {details.finalDua && (
        <View style={styles.section}>
          <Text style={styles.label}>Final Du'a:</Text>
          <View style={styles.duaContainer}>
            <Text style={styles.arabicTextLarge}>{details.finalDua}</Text>
          </View>
        </View>
      )}

      {duas.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.label}>Relevant Du'as:</Text>
          {duas.map((dua, index) => (
            <View key={`dua-${index}`} style={styles.duaContainer}>
              {dua.name && <Text style={styles.duaName}>{dua.name}</Text>}
              {dua.arabic && <Text style={styles.arabicTextLarge}>{dua.arabic}</Text>}
              {dua.translation && <Text style={styles.translationTextLarge}>{dua.translation}</Text>}
              {/* TODO: Add audio player for dua.audioUrl if present */}
            </View>
          ))}
        </View>
      )}

      {details.reflection && (
        <View style={styles.section}>
          <Text style={styles.label}>Point for Reflection:</Text>
          <Text style={styles.textBlock}>{details.reflection}</Text>
        </View>
      )}

      {(details.benefits as string[])?.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.label}>Benefits of this Practice:</Text>
          {(details.benefits as string[]).map((benefit: string, index: number) => (
            <Text key={`benefit-${index}`} style={styles.listItem}><Ionicons name="leaf-outline" size={16} color="#4DD0E1"/> {benefit}</Text>
          ))}
        </View>
      )}

      {details.sourceReference && (
         <View style={styles.section}>
          <Text style={styles.label}>Source / Reference:</Text>
          <Text style={styles.sourceText}>{details.sourceReference}</Text>
        </View>
      )}

      <TouchableOpacity
        style={[styles.completeButton, isCompleted && styles.completeButtonDisabled]}
        onPress={handleComplete}
        disabled={isCompleted}
      >
        <Ionicons name={isCompleted ? "checkmark-circle" : "ellipse-outline"} size={20} color="#fff" style={{marginRight: 8}}/>
        <Text style={styles.completeButtonText}>{isCompleted ? 'Practice Completed' : 'Mark as Completed'}</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainerScrollView: {
    padding: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  icon: {
    marginRight: 12,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    flexShrink: 1,
  },
  categoryText: {
    fontSize: 15,
    fontWeight: 'normal',
    color: 'rgba(255,255,255,0.75)',
  },
  description: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    marginBottom: 20,
    lineHeight: 23,
  },
  section: {
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  label: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'rgba(255,255,255,0.95)',
    marginTop: 10,
    marginBottom: 10,
  },
  textBlock: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 24,
    marginBottom: 10,
  },
  textBlockItalic: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 24,
    marginBottom: 10,
    fontStyle: 'italic',
  },
  stepItemContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  stepNumberContainer: {
    marginRight: 10,
    paddingTop: 2, // Align with text better
  },
  stepNumberText: {
    fontSize: 16,
    color: '#4DD0E1', // Accent color for step number
    fontWeight: 'bold',
  },
  stepContent: {
    flex: 1,
  },
  stepInstructionText: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 23,
    marginBottom: 6,
  },
  mindfulnessText: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.7)',
    fontStyle: 'italic',
    marginBottom: 6,
    marginTop: 4,
  },
  stepDuaWrapper: {
    marginTop: 8,
    paddingTop: 8,
    marginLeft: 10, // Indent the dua section
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.15)',
  },
  stepDuaArabic: {
    fontSize: 17,
    color: '#E1F5FE',
    fontFamily: 'ArabicFont', // Applied custom font
    textAlign: 'right',
    marginTop: 6,
    lineHeight: 28,
  },
  stepDuaTranslation: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.7)',
    fontStyle: 'italic',
    marginTop: 3,
  },
  listItem: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 24,
    marginLeft: 5,
    marginBottom: 8,
    flexDirection: 'row', // To align icon with text
    alignItems: 'center',
  },
  duaContainer: {
    marginBottom: 15,
    padding: 15,
    backgroundColor: 'rgba(0,0,0,0.2)',
    borderRadius: 10,
  },
  duaName: {
    fontSize: 17,
    fontWeight: 'bold',
    color: 'rgba(255,255,255,0.95)',
    marginBottom: 8,
  },
  arabicTextLarge: { // For main duas
    fontSize: 22,
    color: '#E1F5FE',
    textAlign: 'right',
    fontFamily: 'ArabicFont', // Applied custom font
    marginBottom: 8,
    lineHeight: 38,
  },
  translationTextLarge: { // For main duas
    fontSize: 15,
    fontStyle: 'italic',
    color: 'rgba(255,255,255,0.8)',
  },
  sourceText: {
    fontSize: 14,
    fontStyle: 'italic',
    color: 'rgba(255,255,255,0.75)',
    marginBottom: 15,
  },
  completeButton: {
    flexDirection: 'row',
    backgroundColor: '#5cb85c',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  completeButtonDisabled: {
    backgroundColor: '#777',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default SunnahPracticeView;
