import React, { useState, useEffect } from 'react'; // Import useEffect
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native'; // Import ActivityIndicator
import { DailyPractice } from '../../../../libs/shared-types/src/lib/journey.types';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av'; // Import Audio


interface QuranicVerseViewProps {
  practice: DailyPractice;
  onProgressSubmit: (practiceId: string, progressData: any) => void;
}

const QuranicVerseView: React.FC<QuranicVerseViewProps> = ({ practice, onProgressSubmit }) => {
  const details = practice.componentDetails || practice.componentData || {};
  const [isCompleted, setIsCompleted] = useState(false);

  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoadingAudio, setIsLoadingAudio] = useState(false);

  // State for selected reciter
  const [selectedAudioUrl, setSelectedAudioUrl] = useState<string | null>(details.audioUrl || null);
  const [currentReciterName, setCurrentReciterName] = useState<string | null>('Default');

  useEffect(() => {
    // Initialize selectedAudioUrl and currentReciterName when component mounts or practice details change
    setSelectedAudioUrl(details.audioUrl || null);
    // Try to find if the default audioUrl matches one of the reciter options to set initial name
    let initialReciterName = 'Default';
    if (details.audioUrl && details.reciterOptions) {
      for (const [name, url] of Object.entries(details.reciterOptions)) {
        if (url === details.audioUrl) {
          initialReciterName = name;
          break;
        }
      }
    }
    setCurrentReciterName(initialReciterName);
  }, [details.audioUrl, details.reciterOptions]);

  const handleComplete = () => {
    if (isCompleted) return;
    onProgressSubmit(practice.id, {
      practiceId: practice.id,
      title: practice.title,
      completed: true,
    });
    setIsCompleted(true);
    if (sound) { // Stop audio if playing
      sound.stopAsync();
      setIsPlaying(false);
    }
  };

  async function playSound() {
    if (isCompleted || isLoadingAudio) return;
    if (!selectedAudioUrl) { // Use selectedAudioUrl
      console.log("No audio URL selected/provided for Quranic Verse");
      return;
    }

    setIsLoadingAudio(true);
    try {
      // If a sound is already loaded, or if the selectedAudioUrl has changed, unload previous sound first.
      if (sound) {
        const status = await sound.getStatusAsync();
        if (status.isLoaded) {
            // If it's the same sound and just toggling play/pause
            if (status.uri === selectedAudioUrl) {
                if (isPlaying) {
                    await sound.pauseAsync();
                } else {
                    await sound.playAsync();
                }
                // setIsPlaying will be handled by _onPlaybackStatusUpdate
                setIsLoadingAudio(false);
                return;
            }
        }
        // If different URL or not loaded properly, unload before loading new one
        console.log('Unloading previous sound before playing new selection.');
        await sound.unloadAsync();
        setSound(null); // Explicitly set to null after unload
        setIsPlaying(false);
      }

      console.log('Loading Quranic Verse Sound from:', selectedAudioUrl);
      const { sound: newSound } = await Audio.Sound.createAsync(
         { uri: selectedAudioUrl }, // Use selectedAudioUrl
         { shouldPlay: true }
      );
      setSound(newSound); // This will trigger the useEffect for cleanup if newSound is different
      newSound.setOnPlaybackStatusUpdate(_onPlaybackStatusUpdate);
    } catch (error) {
      console.error("Error loading or playing Quranic verse sound:", error);
      setSound(null); // Ensure sound is null on error
      setIsPlaying(false);
    } finally {
      setIsLoadingAudio(false);
    }
  }

  const _onPlaybackStatusUpdate = (playbackStatus: Audio.PlaybackStatus) => {
    if (!playbackStatus.isLoaded) {
      if (playbackStatus.error) {
        console.log(`Quranic Verse Playback Error: ${playbackStatus.error}`);
      }
      setIsPlaying(false); // Ensure isPlaying is false if unloaded or error
    } else {
      setIsPlaying(playbackStatus.isPlaying);
      if (playbackStatus.didJustFinish && !playbackStatus.isLooping) {
        setIsPlaying(false);
        // Consider unloading or resetting sound here if desired after finishing
        // sound?.setPositionAsync(0); // Rewind
      }
    }
  };

  useEffect(() => {
    return sound
      ? () => {
          console.log('Unloading Quranic Verse Sound');
          sound.unloadAsync();
          setSound(null);
          setIsPlaying(false);
        }
      : undefined;
  }, [sound]);


  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainerScrollView}>
      <View style={styles.header}>
        <Ionicons name="book-outline" size={28} color="#81C784" style={styles.icon}/>
        <Text style={styles.title}>{details.titleOverride || practice.title}</Text>
      </View>
      {practice.description && <Text style={styles.description}>{practice.description}</Text>}

      {details.arabicText && (
        <View style={styles.arabicTextContainer}>
            <Text style={styles.arabicText}>{details.arabicText}</Text>
        </View>
      )}

      {details.audioUrl && (
        <View style={styles.audioSection}>
            <TouchableOpacity
                style={[styles.audioButton, (isCompleted || isLoadingAudio) && styles.disabledInput]}
                onPress={playSound} // Changed from playAudio
                disabled={isCompleted || isLoadingAudio}
            >
            {isLoadingAudio ? (
              <ActivityIndicator size="small" color="#81C784" />
            ) : (
              <Ionicons name={isPlaying ? "pause-circle-outline" : "play-circle-outline"} size={28} color={(isCompleted || isLoadingAudio) ? "grey" : "#81C784"} />
            )}
            <Text style={[styles.audioText, (isCompleted || isLoadingAudio) && styles.disabledText]}>
              {isLoadingAudio ? "Loading..." : isPlaying ? `Pause (${currentReciterName || 'Default'})` : `Listen (${currentReciterName || 'Default'})`}
            </Text>
            </TouchableOpacity>
            {details.reciterOptions && Object.keys(details.reciterOptions).length > 0 && (
              <View style={styles.reciterSection}>
                <Text style={styles.subLabel}>Change Reciter:</Text>
                {Object.entries(details.reciterOptions).map(([name, urlValue]) => {
                  const reciterAudioUrl = urlValue as string; // Assuming urlValue is a string
                  const isSelected = currentReciterName === name;
                  return (
                    <TouchableOpacity
                      key={name}
                      style={[
                        styles.reciterOptionButton,
                        isSelected && styles.reciterOptionButtonSelected,
                        (isCompleted || isLoadingAudio) && styles.disabledInput // General disabled style
                      ]}
                      onPress={async () => {
                        if (isCompleted || isLoadingAudio) return;
                        if (sound) {
                          await sound.stopAsync(); // Stop current sound
                          await sound.unloadAsync(); // Unload it
                          setSound(null);
                          setIsPlaying(false);
                        }
                        setSelectedAudioUrl(reciterAudioUrl);
                        setCurrentReciterName(name);
                        playSound(); // Auto-play the new reciter
                      }}
                      disabled={isCompleted || isLoadingAudio}
                    >
                      <Ionicons
                        name={isSelected ? "checkmark-circle" : "person-circle-outline"}
                        size={20}
                        color={isCompleted || isLoadingAudio ? "grey" : (isSelected ? "#81C784" : "#D1C4E9")}
                        style={{marginRight: 8}} />
                      <Text style={[
                        styles.reciterOptionText,
                        isSelected && styles.reciterOptionTextSelected,
                        (isCompleted || isLoadingAudio) && styles.disabledText
                        ]}>{name}</Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}
        </View>
      )}

      {details.translationEn && (
        <View style={styles.section}>
          <Text style={styles.label}>Translation (English):</Text>
          <Text style={styles.textBlock}>{details.translationEn}</Text>
        </View>
      )}
      {details.translationUr && (
        <View style={styles.section}>
          <Text style={styles.label}>Translation (Urdu):</Text>
          <Text style={[styles.textBlock, styles.urduText]}>{details.translationUr}</Text>
        </View>
      )}
      {/* Placeholder for other translations - could be a loop if translations are an object/array */}
      {/* Example: details.translations?.fr && (
        <View style={styles.section}>
          <Text style={styles.label}>Translation (French):</Text>
          <Text style={styles.textBlock}>{details.translations.fr}</Text>
        </View>
      )*/}

      {details.contextualExplanation && (
        <View style={styles.section}>
          <Text style={styles.label}>Contextual Explanation:</Text>
          <Text style={styles.textBlock}>{details.contextualExplanation}</Text>
        </View>
      )}

      {details.tafsirEn && (
        <View style={styles.section}>
          <Text style={styles.label}>Tafsir / Commentary ({details.tafsirSource || 'Insights'}):</Text>
          <Text style={styles.textBlock}>{details.tafsirEn}</Text>
        </View>
      )}

      {(details.reflectionPrompts as string[])?.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.label}>Reflection Prompts:</Text>
          {(details.reflectionPrompts as string[]).map((prompt: string, index: number) => (
            <Text key={index} style={styles.listItem}><Ionicons name="caret-forward-outline" size={16} color="#81C784"/> {prompt}</Text>
          ))}
        </View>
      )}

      {details.practicalApplication && (
        <View style={styles.section}>
          <Text style={styles.label}>Practical Application in Daily Life:</Text>
          <Text style={styles.textBlock}>{details.practicalApplication}</Text>
        </View>
      )}

      <TouchableOpacity
        style={[styles.completeButton, isCompleted && styles.completeButtonDisabled]}
        onPress={handleComplete}
        disabled={isCompleted}
      >
        <Ionicons name={isCompleted ? "checkmark-circle" : "ellipse-outline"} size={20} color="#fff" style={{marginRight: 8}}/>
        <Text style={styles.completeButtonText}>{isCompleted ? 'Reflection Completed' : 'Mark as Reflected'}</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainerScrollView: {
    padding: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10, // Adjusted
  },
  icon: {
    marginRight: 10, // Adjusted
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    flex:1,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    marginBottom: 20,
    lineHeight: 23,
    fontStyle: 'italic',
  },
  arabicTextContainer: { // Added container for padding/margin
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 20, // Increased vertical padding
    marginBottom: 20,
  },
  arabicText: {
    fontSize: 28,
    color: '#E1F5FE', // Light blueish white for contrast
    textAlign: 'right',
    fontFamily: 'ArabicFont', // Applied custom font
    lineHeight: 50,
  },
  audioSection: { // Group audio button and future picker
    alignItems: 'center',
    marginBottom: 20,
  },
  audioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 25,
    backgroundColor: 'rgba(129, 200, 132, 0.15)', // Light green background
    borderRadius: 30, // Pill shape
    borderWidth: 1,
    borderColor: 'rgba(129, 200, 132, 0.4)',
  },
  audioText: {
    color: '#81C784', // Green to match icon
    marginLeft: 10,
    fontSize: 16,
    fontWeight: '600',
  },
  smallHintText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.5)',
    marginTop: 5,
  },
  section: { // Added for better grouping
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  label: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'rgba(255,255,255,0.95)',
    marginTop: 10,
    marginBottom: 8,
  },
  textBlock: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 24,
    marginBottom: 10,
  },
  urduText: { // Specific style for Urdu if needed
    textAlign: 'right',
    fontFamily: 'UrduFont', // Applied custom font
    fontSize: 17,
  },
  listItem: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 24,
    marginBottom: 8,
    paddingLeft: 5, // Indent text after icon
  },
  completeButton: {
    flexDirection: 'row',
    backgroundColor: '#5cb85c', // Green
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  completeButtonDisabled: {
    backgroundColor: '#777',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  disabledInput: {
    opacity: 0.5,
  },
  disabledText: {
    color: 'grey',
  },
  reciterSection: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.1)',
  },
  subLabel: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.7)',
    marginBottom: 8,
  },
  reciterOptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0,0,0,0.2)',
    borderRadius: 6,
    marginBottom: 6,
  },
  reciterOptionText: {
    fontSize: 15,
    color: '#D1C4E9',
  },
  reciterOptionButtonSelected: {
    backgroundColor: 'rgba(129, 200, 132, 0.2)', // Use a highlight color, e.g., audio button's green
    borderColor: '#81C784',
    borderWidth: 1,
  },
  reciterOptionTextSelected: {
    fontWeight: 'bold',
    color: '#81C784',
  },
});

export default QuranicVerseView;
