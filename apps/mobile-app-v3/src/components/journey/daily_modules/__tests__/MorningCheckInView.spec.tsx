import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MorningCheckInView from '../MorningCheckInView'; // Adjust path as necessary
import { DailyPractice } from '../../../../../libs/shared-types/src/lib/journey.types'; // Adjust path

// Mock Ionicons
jest.mock('@expo/vector-icons', () => {
  const { View } = require('react-native');
  return {
    Ionicons: (props: any) => <View testID="ionicons-mock" {...props} />,
  };
});

// Mock react-native-slider if it's a direct dependency and causes issues.
// Usually, it's better to test what the user sees and interacts with rather than internal slider state.
// For a basic render test, this might not be needed unless it throws errors during render.
// jest.mock('@react-native-community/slider', () => 'Slider');


describe('MorningCheckInView', () => {
  const mockPractice: DailyPractice = {
    id: 'mci-123',
    type: 'MorningCheckIn',
    title: 'Morning Check-In & Intention', // This title is used by the component
    description: 'Start your day with intention and a mood check.',
    duration: 3,
    layerFocus: 'qalb',
    difficultyLevel: 'beginner',
    componentDetails: {
      originalContentId: 'mci-orig-123',
      moodScaleMin: 1,
      moodScaleMax: 10,
      intentionPrompt: 'What is your Niyyah (intention) for today?',
      // Default values that might be expected by component
      moodValue: 5,
      intentionText: '',
      energyLevel: 3,
    },
    dayNumber: 1,
    journeyId: 'journey-abc',
    status: 'pending',
  };

  const mockOnProgressSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    const { getByText, getByPlaceholderText } = render(
      <MorningCheckInView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    // Check for the title (directly from practice.title)
    expect(getByText('Morning Check-In & Intention')).toBeTruthy();

    // Check for labels and prompts
    expect(getByText('How are you feeling this morning?')).toBeTruthy();
    expect(getByText('Set your intention for the day:')).toBeTruthy();
    expect(getByText(mockPractice.componentDetails.intentionPrompt!)).toBeTruthy();
    expect(getByPlaceholderText('Write your intention here...')).toBeTruthy();
    expect(getByText('Energy Level (1-5):')).toBeTruthy();

    // Check for the submit button
    expect(getByText('Submit Check-In')).toBeTruthy();
  });

  it('allows input for intention', () => {
    const { getByPlaceholderText } = render(
      <MorningCheckInView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );
    const intentionInput = getByPlaceholderText('Write your intention here...');
    fireEvent.changeText(intentionInput, 'My test intention.');
    expect(intentionInput.props.value).toBe('My test intention.');
  });

  it('calls onProgressSubmit with the current state when "Submit Check-In" is pressed', () => {
    const { getByText, getByPlaceholderText } = render(
      <MorningCheckInView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    const intentionInput = getByPlaceholderText('Write your intention here...');
    fireEvent.changeText(intentionInput, 'A focused intention.');

    // Note: Testing the Slider interaction for mood and energy level requires more specific mocking or interaction.
    // For this basic test, we'll assume default values for mood (5) and energy (3) are submitted if not changed.
    // The component initializes moodValue to 5 and energyLevel to 3 in its state.

    const submitButton = getByText('Submit Check-In');
    fireEvent.press(submitButton);

    expect(mockOnProgressSubmit).toHaveBeenCalledWith(mockPractice.id, {
      practiceId: mockPractice.id,
      title: mockPractice.title,
      completed: true,
      mood: 5, // Default initial mood from component's state
      intention: 'A focused intention.',
      energy: 3, // Default initial energy from component's state
    });
    expect(getByText('Check-In Submitted!')).toBeTruthy();
  });

  // TODO: Add tests for mood slider interaction and energy level slider interaction.
  // This would likely involve mocking the Slider component to control its value or finding it by testID
  // and simulating value changes if possible with @testing-library/react-native.
});
