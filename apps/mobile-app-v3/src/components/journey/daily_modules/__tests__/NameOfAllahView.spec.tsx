import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import NameOfAllahView from '../NameOfAllahView'; // Adjust path as necessary
import { DailyPractice } from '../../../../../libs/shared-types/src/lib/journey.types'; // Adjust path

// Mock Ionicons if not already handled globally in test setup
jest.mock('@expo/vector-icons', () => {
  const { View } = require('react-native');
  return {
    Ionicons: (props: any) => <View testID="ionicons-mock" {...props} />,
  };
});

// Mock expo-av Audio
// Define mock sound object methods first
const mockPlayAsync = jest.fn().mockResolvedValue({});
const mockPauseAsync = jest.fn().mockResolvedValue({});
const mockStopAsync = jest.fn().mockResolvedValue({});
const mockUnloadAsync = jest.fn().mockResolvedValue({});
const mockGetStatusAsync = jest.fn().mockResolvedValue({ isLoaded: false, isPlaying: false, didJustFinish: false });
const mockSetOnPlaybackStatusUpdate = jest.fn();

const mockSoundObject = {
  playAsync: mockPlayAsync,
  pauseAsync: mockPauseAsync,
  stopAsync: mockStopAsync,
  unloadAsync: mockUnloadAsync,
  getStatusAsync: mockGetStatusAsync,
  setOnPlaybackStatusUpdate: mockSetOnPlaybackStatusUpdate,
};

jest.mock('expo-av', () => ({
  Audio: {
    Sound: {
      createAsync: jest.fn().mockResolvedValue({ sound: mockSoundObject }),
    },
  },
}));

describe('NameOfAllahView', () => {
  const mockPractice: DailyPractice = {
    id: 'noa-123',
    type: 'NameOfAllahSpotlight',
    title: 'Test Name of Allah Practice',
    description: 'Test description',
    duration: 5,
    layerFocus: 'qalb',
    difficultyLevel: 'beginner',
    componentDetails: {
      originalContentId: 'noa-orig-123',
      name: 'Ar-Rahman', // This is what we'll check for rendering
      arabicScript: 'الرحمن',
      meaning: 'The Most Merciful',
      significance: 'Significance text',
      reflectionPrompt: 'Reflect on mercy.',
      practicalApplication: 'Be merciful.',
      dhikrCount: 33,
      audioUrl: 'https://example.com/audio.mp3',
      calligraphyUrl: 'https://example.com/calligraphy.png',
    },
    dayNumber: 1,
    journeyId: 'journey-abc',
    status: 'pending',
  };

  const mockOnProgressSubmit = jest.fn();

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders correctly with minimal valid props', () => {
    const { getByText } = render(
      <NameOfAllahView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    // Check if the name from componentDetails is rendered
    expect(getByText('Ar-Rahman')).toBeTruthy();

    // Check if some other key elements are present (optional, but good for basic structure)
    expect(getByText('Meaning:')).toBeTruthy();
    expect(getByText('The Most Merciful')).toBeTruthy();
    expect(getByText('Dhikr Practice:')).toBeTruthy(); // Assuming dhikrCount is provided
  });

  it('renders practice title if componentDetails.name is not available', () => {
    const practiceWithoutNameDetail: DailyPractice = {
      ...mockPractice,
      componentDetails: {
        ...mockPractice.componentDetails,
        name: undefined, // Explicitly set name to undefined
      },
    };
    const { getByText } = render(
      <NameOfAllahView practice={practiceWithoutNameDetail} onProgressSubmit={mockOnProgressSubmit} />
    );
    // Should fall back to practice.title
    expect(getByText(mockPractice.title)).toBeTruthy();
  });

  it('calls onProgressSubmit when "Mark as Completed" button is pressed', () => {
    const { getByText } = render(
      <NameOfAllahView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    const completeButton = getByText('Mark as Completed');
    fireEvent.press(completeButton);

    expect(mockOnProgressSubmit).toHaveBeenCalledWith('noa-123', {
      practiceId: 'noa-123',
      title: 'Test Name of Allah Practice',
      completed: true,
      dhikr_actual_count: 0, // Initial dhikr count
    });
    expect(getByText('Practice Completed')).toBeTruthy(); // Button text changes
  });

  it('increments and decrements dhikr count', () => {
    const { getByText, getAllByTestId } = render( // Assuming Ionicons mock adds testID
      <NameOfAllahView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    const incrementButton = getByTestId('dhikr-increment-button');
    const decrementButton = getByTestId('dhikr-decrement-button');

    // Initial count
    expect(getByText('0')).toBeTruthy();

    // Increment
    fireEvent.press(incrementButton);
    expect(getByText('1')).toBeTruthy();

    fireEvent.press(incrementButton);
    expect(getByText('2')).toBeTruthy();

    // Decrement
    fireEvent.press(decrementButton);
    expect(getByText('1')).toBeTruthy();

    // Cannot go below 0
    fireEvent.press(decrementButton); // Now at 0
    expect(getByText('0')).toBeTruthy();
    fireEvent.press(decrementButton); // Try to go below 0
    expect(getByText('0')).toBeTruthy(); // Still 0, button should be disabled but text remains

    // Cannot exceed targetDhikrCount (mockPractice.componentDetails.dhikrCount is 33)
    // Simulate reaching target
    for (let i = 0; i < 33; i++) {
      fireEvent.press(incrementButton);
    }
    expect(getByText('33')).toBeTruthy();

    // "Goal reached!" text should appear
    expect(getByText('Goal reached! You can continue if you wish.')).toBeTruthy();

    // Try to increment beyond target
    fireEvent.press(incrementButton);
    expect(getByText('33')).toBeTruthy(); // Still 33, button should be disabled

    // Mark as complete and check if counter buttons are disabled (implicitly tested by not changing count)
    const completeButton = getByText('Mark as Completed');
    fireEvent.press(completeButton);
    expect(getByText('Practice Completed')).toBeTruthy();

    fireEvent.press(incrementButton); // Try incrementing after completion
    expect(getByText('33')).toBeTruthy(); // Count should not change

    fireEvent.press(decrementButton); // Try decrementing after completion
    expect(getByText('33')).toBeTruthy(); // Count should not change
  });

  it('plays and pauses audio when audio button is pressed', async () => {
    const { getByText } = render(
      <NameOfAllahView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    const audioButton = getByText('Listen to Pronunciation');
    fireEvent.press(audioButton);

    // Check if createAsync was called (it's part of the mock)
    expect(Audio.Sound.createAsync).toHaveBeenCalled();

    // At this point, the mock sound's playAsync should have been called by the component's logic
    // This requires the mock for createAsync to return a sound object whose playAsync can be spied upon or checked.
    expect(Audio.Sound.createAsync).toHaveBeenCalledWith({ uri: mockPractice.componentDetails.audioUrl }, { shouldPlay: true });

    // At this point, the component calls setSound(newSound) and newSound.setOnPlaybackStatusUpdate.
    // The playAsync is called by createAsync itself due to { shouldPlay: true }.
    // To test play/pause logic *after* initial load, we need to simulate subsequent presses.

    // Simulate first press to play (sound is now "loaded" by createAsync)
    // The createAsync mock directly calls playAsync because of shouldPlay: true
    // So, after the first press and successful createAsync, playAsync on the mockSoundObject should have been called by implication of shouldPlay:true
    // However, the component's logic is: if (sound) { if (isPlaying) pause else play } else { create }
    // Let's refine the test to correctly reflect the component's stateful logic for play/pause *after* initial creation.

    // For the first press (audio not yet loaded in component state):
    await mockSoundObject.getStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: true, didJustFinish: false }); // Assume createAsync plays it
    // The component sets isPlaying to true based on playback status update.

    // Now, simulate a second press to PAUSE
    // Update the mock for getStatusAsync for the second interaction
    mockSoundObject.getStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: true, didJustFinish: false });
    fireEvent.press(audioButton); // Press again (should attempt to pause)
    expect(mockSoundObject.pauseAsync).toHaveBeenCalledTimes(1); // Check if pause was called

    // Simulate a third press to PLAY again (after being paused)
    mockSoundObject.getStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: false, didJustFinish: false }); // Sound is loaded but not playing
    fireEvent.press(audioButton); // Press again (should attempt to play)
    expect(mockSoundObject.playAsync).toHaveBeenCalledTimes(1); // Check if play was called
    // Note: playAsync might be called initially by createAsync if shouldPlay is true,
    // so if we want to test the explicit call from the button logic, more nuanced mocking or state checking is needed.
    // For this test, we're checking the sequence: create->(implicit play), button press -> pause, button press -> play.
  });


  // TODO: Add more tests:
  // - Image rendering logic (calligraphyUrl vs arabicScript as URL vs arabicScript as text)
  // - Audio error handling (if Alert was used, how to test Alert.alert calls)
  // - Dhikr counter reaching target and behavior
  // - Disabled states of buttons when practice is completed
});
