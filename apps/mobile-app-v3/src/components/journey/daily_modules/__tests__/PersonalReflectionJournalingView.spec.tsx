import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import PersonalReflectionJournalingView from '../PersonalReflectionJournalingView'; // Adjust path
import { DailyPractice } from '../../../../../libs/shared-types/src/lib/journey.types'; // Adjust path

// Mock Ionicons
jest.mock('@expo/vector-icons', () => {
  const { View } = require('react-native');
  return {
    Ionicons: (props: any) => <View testID="ionicons-mock" {...props} />,
  };
});

// Mock expo-av for voice recording if it's used directly and needs mocking for basic render
// For now, assuming basic render doesn't immediately try to record.
// If `Audio.Recording` is used on mount or during render, it would need a mock.
jest.mock('expo-av', () => ({
  Audio: {
    Recording: jest.fn().mockImplementation(() => ({
      prepareToRecordAsync: jest.fn().mockResolvedValue({}),
      startAsync: jest.fn().mockResolvedValue({}),
      stopAndUnloadAsync: jest.fn().mockResolvedValue({}),
      getURI: jest.fn(() => 'mock://recording.uri'),
      getStatusAsync: jest.fn().mockResolvedValue({ canRecord: true, isRecording: false, durationMillis: 0 }),
    })),
    Sound: jest.fn(), // Add Sound mock if it's also used for playback of recordings
    setAudioModeAsync: jest.fn().mockResolvedValue({}), // Mock if component calls this
    requestPermissionsAsync: jest.fn().mockResolvedValue({ granted: true }), // Mock permission request
  },
  InterruptionModeIOS: { DoNotMix: 'DoNotMix' }, // Provide mock values for enums if used
  InterruptionModeAndroid: { DoNotMix: 'DoNotMix' },
}));


describe('PersonalReflectionJournalingView', () => {
  const mockPractice: DailyPractice = {
    id: 'prj-123',
    type: 'PersonalReflectionJournaling',
    title: 'Personal Reflection & Journaling', // This title is used by the component
    description: 'Reflect on your day and insights.',
    duration: 7,
    layerFocus: 'qalb',
    difficultyLevel: 'beginner',
    componentDetails: {
      originalContentId: 'prj-orig-123',
      prompts: ['What was a moment of gratitude today?', 'How did you overcome a challenge?'],
      allowVoiceNote: true,
      maxDurationSeconds: 300, // Example
    },
    dayNumber: 1,
    journeyId: 'journey-abc',
    status: 'pending',
  };

  const mockOnProgressSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with prompts', () => {
    const { getByText, getByPlaceholderText } = render(
      <PersonalReflectionJournalingView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    // Check for the title (directly from practice.title)
    expect(getByText('Personal Reflection & Journaling')).toBeTruthy();

    // Check for prompts
    expect(getByText('Prompts for Reflection:')).toBeTruthy();
    expect(getByText(mockPractice.componentDetails.prompts[0])).toBeTruthy();
    expect(getByText(mockPractice.componentDetails.prompts[1])).toBeTruthy();

    // Check for text input area
    expect(getByPlaceholderText('Type your reflections here...')).toBeTruthy();

    // Check for voice note button if allowVoiceNote is true
    if (mockPractice.componentDetails.allowVoiceNote) {
      expect(getByText('Record Voice Note')).toBeTruthy(); // Or whatever the button text is
    }

    // Check for the submit button
    expect(getByText('Save Reflection')).toBeTruthy();
  });

  it('allows input for reflection text', () => {
    const { getByPlaceholderText } = render(
      <PersonalReflectionJournalingView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );
    const reflectionInput = getByPlaceholderText('Type your reflections here...');
    fireEvent.changeText(reflectionInput, 'Today I felt grateful for...');
    expect(reflectionInput.props.value).toBe('Today I felt grateful for...');
  });

  it('calls onProgressSubmit with the reflection text when "Save Reflection" is pressed', () => {
    const { getByText, getByPlaceholderText } = render(
      <PersonalReflectionJournalingView practice={mockPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    const reflectionInput = getByPlaceholderText('Type your reflections here...');
    fireEvent.changeText(reflectionInput, 'My deep thoughts.');

    const saveButton = getByText('Save Reflection');
    fireEvent.press(saveButton);

    expect(mockOnProgressSubmit).toHaveBeenCalledWith(mockPractice.id, {
      practiceId: mockPractice.id,
      title: mockPractice.title,
      completed: true,
      reflectionText: 'My deep thoughts.',
      voiceNoteUrl: null, // No voice note recorded in this test
      voiceNoteDuration: 0,
    });
    expect(getByText('Reflection Saved!')).toBeTruthy();
  });

  // TODO: Add tests for voice recording functionality:
  // - Requesting permissions
  // - Starting recording
  // - Stopping recording
  // - Handling recording errors
  // - Submitting with a voice note URL
});
