import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import SunnahPracticeView from '../SunnahPracticeView'; // Adjust path as necessary
import { DailyPractice } from '../../../../../libs/shared-types/src/lib/journey.types'; // Adjust path

// Mock Ionicons
jest.mock('@expo/vector-icons', () => {
  const { View } = require('react-native');
  return {
    Ionicons: (props: any) => <View testID="ionicons-mock" {...props} />,
  };
});

// Mock useState for isCompleted if needed for specific tests, but not for basic render
// const mockSetIsCompleted = jest.fn();
// jest.mock('react', () => ({
//   ...jest.requireActual('react'),
//   useState: (initial: any) => [initial, mockSetIsCompleted], // Basic mock, might need more specific for multiple useStates
// }));


describe('SunnahPracticeView', () => {
  const mockPracticeBase: DailyPractice = {
    id: 'sp-123',
    type: 'SunnahPractice',
    title: 'Test Sunnah Practice',
    description: 'A wonderful practice from the Sunnah.',
    duration: 10,
    layerFocus: 'qalb', // Example, adjust as needed
    difficultyLevel: 'beginner',
    componentDetails: {
      originalContentId: 'sp-orig-123',
      category: 'Spiritual',
      description: 'Detailed description of this Sunnah practice.', // This should be practice.description if not overridden
      intention: 'To please Allah and follow the Prophet (PBUH).',
      stepsJson: [
        { stepNumber: 1, instruction: 'Begin with Bismillah.' },
        { instruction: 'Perform the action with mindfulness.' }, // String step
        { stepNumber: 3, instruction: 'Conclude with Alhamdulillah.', duaArabic: 'الحمد لله', duaTranslation: 'Praise be to Allah.'}
      ],
      duasJson: [
        { name: 'Dua for Starting', arabic: 'بِسْمِ اللهِ', translation: 'In the name of Allah.' },
      ],
      reflection: 'Reflect on the ease and beauty of the Sunnah.',
      benefits: ['Increased Iman', 'Closeness to Allah'],
      sourceReference: 'Hadith XYZ, Book ABC',
    },
    dayNumber: 1,
    journeyId: 'journey-abc',
    status: 'pending',
  };

  const mockOnProgressSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with all details provided', () => {
    const { getByText, getAllByText } = render(
      <SunnahPracticeView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />
    );

    // Check for title and category
    expect(getByText(mockPracticeBase.title)).toBeTruthy();
    expect(getByText('(Spiritual)')).toBeTruthy(); // Category from componentDetails

    // Check for description (from componentDetails as it's provided)
    expect(getByText('Detailed description of this Sunnah practice.')).toBeTruthy();

    // Check for intention
    expect(getByText('Intention (Niyyah):')).toBeTruthy();
    expect(getByText('To please Allah and follow the Prophet (PBUH).')).toBeTruthy();

    // Check for steps
    expect(getByText('Steps to Follow:')).toBeTruthy();
    expect(getByText('1')).toBeTruthy(); // Step number
    expect(getByText('Begin with Bismillah.')).toBeTruthy();
    expect(getByText('Perform the action with mindfulness.')).toBeTruthy(); // String step rendered with auto-numbering (index + 1)
    expect(getByText('3')).toBeTruthy();
    expect(getByText('Conclude with Alhamdulillah.')).toBeTruthy();
    expect(getByText('الحمد لله')).toBeTruthy(); // Dua Arabic in step
    expect(getByText('Praise be to Allah.')).toBeTruthy(); // Dua Translation in step

    // Check for Duas section
    expect(getByText("Relevant Du'as:")).toBeTruthy();
    expect(getByText('Dua for Starting')).toBeTruthy();
    expect(getAllByText('بِسْمِ اللهِ').length).toBeGreaterThan(0); // Arabic can appear in steps and duas
    expect(getAllByText('In the name of Allah.').length).toBeGreaterThan(0);

    // Check for reflection
    expect(getByText('Point for Reflection:')).toBeTruthy();
    expect(getByText('Reflect on the ease and beauty of the Sunnah.')).toBeTruthy();

    // Check for benefits
    expect(getByText('Benefits of this Practice:')).toBeTruthy();
    expect(getByText('Increased Iman')).toBeTruthy();
    expect(getByText('Closeness to Allah')).toBeTruthy();

    // Check for source
    expect(getByText('Source / Reference:')).toBeTruthy();
    expect(getByText('Hadith XYZ, Book ABC')).toBeTruthy();

    // Check for complete button
    expect(getByText('Mark as Completed')).toBeTruthy();
  });

  it('renders practice.description if componentDetails.description is not available', () => {
    const practiceWithoutDetailDesc: DailyPractice = {
      ...mockPracticeBase,
      componentDetails: {
        ...mockPracticeBase.componentDetails,
        description: undefined,
      },
    };
    const { getByText } = render(
      <SunnahPracticeView practice={practiceWithoutDetailDesc} onProgressSubmit={mockOnProgressSubmit} />
    );
    // Should fall back to practice.description
    expect(getByText(mockPracticeBase.description)).toBeTruthy();
  });

  it('does not render optional sections if data is missing', () => {
    const minimalPractice: DailyPractice = {
      ...mockPracticeBase,
      description: "Minimal description.", // Ensure this is used
      componentDetails: {
        originalContentId: 'sp-min-123',
        category: 'Physical',
        // description: undefined, // Use practice.description
        intention: undefined,
        stepsJson: [], // Empty steps
        duasJson: undefined, // Undefined duas
        reflection: undefined,
        benefits: [], // Empty benefits
        sourceReference: undefined,
      },
    };
    const { getByText, queryByText } = render(
      <SunnahPracticeView practice={minimalPractice} onProgressSubmit={mockOnProgressSubmit} />
    );

    expect(getByText(minimalPractice.title)).toBeTruthy();
    expect(getByText('(Physical)')).toBeTruthy();
    expect(getByText("Minimal description.")).toBeTruthy(); // practice.description

    expect(queryByText('Intention (Niyyah):')).toBeNull();
    expect(queryByText('Steps to Follow:')).toBeNull();
    expect(queryByText("Relevant Du'as:")).toBeNull();
    expect(queryByText('Point for Reflection:')).toBeNull();
    expect(queryByText('Benefits of this Practice:')).toBeNull();
    expect(queryByText('Source / Reference:')).toBeNull();
  });

  it('calls onProgressSubmit when "Mark as Completed" button is pressed', () => {
    const { getByText } = render(
      <SunnahPracticeView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />
    );

    const completeButton = getByText('Mark as Completed');
    fireEvent.press(completeButton);

    expect(mockOnProgressSubmit).toHaveBeenCalledWith('sp-123', {
      practiceId: 'sp-123',
      title: mockPracticeBase.title,
      completed: true,
    });
    expect(getByText('Practice Completed')).toBeTruthy(); // Button text changes
  });

  // TODO: Add more specific tests for rendering different structures of stepsJson/duasJson

  describe('Steps and Duas Rendering', () => {
    it('renders string steps correctly with inferred numbering', () => {
      const practiceWithStringSteps: DailyPractice = {
        ...mockPracticeBase,
        componentDetails: {
          ...mockPracticeBase.componentDetails,
          stepsJson: [
            'First simple step.',
            'Second simple step.',
          ],
          duasJson: [], // No duas for this specific test
        },
      };
      const { getByText } = render(
        <SunnahPracticeView practice={practiceWithStringSteps} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(getByText('Steps to Follow:')).toBeTruthy();
      expect(getByText('1')).toBeTruthy(); // Inferred step number
      expect(getByText('First simple step.')).toBeTruthy();
      expect(getByText('2')).toBeTruthy(); // Inferred step number
      expect(getByText('Second simple step.')).toBeTruthy();
    });

    it('renders object steps with provided numbers, instructions, and inline duas', () => {
      const practiceWithObjectSteps: DailyPractice = {
        ...mockPracticeBase,
        componentDetails: {
          ...mockPracticeBase.componentDetails,
          stepsJson: [
            { stepNumber: 1, instruction: 'Instruction for step 1.' },
            { stepNumber: 2, instruction: 'Instruction with Dua.', duaArabic: 'دعاء عربي', duaTranslation: 'Arabic Dua' },
          ],
          duasJson: [],
        },
      };
      const { getByText } = render(
        <SunnahPracticeView practice={practiceWithObjectSteps} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(getByText('1')).toBeTruthy();
      expect(getByText('Instruction for step 1.')).toBeTruthy();
      expect(getByText('2')).toBeTruthy();
      expect(getByText('Instruction with Dua.')).toBeTruthy();
      expect(getByText('دعاء عربي')).toBeTruthy();
      expect(getByText('Arabic Dua')).toBeTruthy();
    });

    it('renders multiple duas in the "Relevant Duas" section correctly', () => {
      const practiceWithMultipleDuas: DailyPractice = {
        ...mockPracticeBase,
        componentDetails: {
          ...mockPracticeBase.componentDetails,
          stepsJson: [],
          duasJson: [
            { name: 'Morning Dua', arabic: 'دعاء الصباح', translation: 'Dua for morning.' },
            { arabic: 'دعاء المساء', translation: 'Dua for evening.' }, // Dua without a name
          ],
        },
      };
      const { getByText, queryByText } = render(
        <SunnahPracticeView practice={practiceWithMultipleDuas} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(getByText("Relevant Du'as:")).toBeTruthy();
      expect(getByText('Morning Dua')).toBeTruthy();
      expect(getByText('دعاء الصباح')).toBeTruthy();
      expect(getByText('Dua for morning.')).toBeTruthy();

      // For the dua without a name, the name Text component should not be rendered
      // This depends on how the component handles missing optional fields like `dua.name`
      // Assuming if dua.name is undefined, the Text component for it isn't rendered.
      // A more robust way would be to check that queryByText('Morning Dua') is not null,
      // and then somehow ensure that another name is not present for the second dua.
      // For now, checking presence of arabic/translation of second dua.
      expect(getByText('دعاء المساء')).toBeTruthy();
      expect(getByText('Dua for evening.')).toBeTruthy();
    });

    it('does not render Steps section if stepsJson is empty or undefined', () => {
      const practiceWithoutSteps1: DailyPractice = {
        ...mockPracticeBase, componentDetails: { ...mockPracticeBase.componentDetails, stepsJson: [] }
      };
      const { queryByText: queryByText1 } = render(
        <SunnahPracticeView practice={practiceWithoutSteps1} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(queryByText1('Steps to Follow:')).toBeNull();

      const practiceWithoutSteps2: DailyPractice = {
        ...mockPracticeBase, componentDetails: { ...mockPracticeBase.componentDetails, stepsJson: undefined }
      };
      const { queryByText: queryByText2 } = render(
        <SunnahPracticeView practice={practiceWithoutSteps2} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(queryByText2('Steps to Follow:')).toBeNull();
    });

    it('does not render Relevant Duas section if duasJson is empty or undefined', () => {
      const practiceWithoutDuas1: DailyPractice = {
        ...mockPracticeBase, componentDetails: { ...mockPracticeBase.componentDetails, duasJson: [] }
      };
      const { queryByText: queryByText1 } = render(
        <SunnahPracticeView practice={practiceWithoutDuas1} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(queryByText1("Relevant Du'as:")).toBeNull();

      const practiceWithoutDuas2: DailyPractice = {
        ...mockPracticeBase, componentDetails: { ...mockPracticeBase.componentDetails, duasJson: undefined }
      };
      const { queryByText: queryByText2 } = render(
        <SunnahPracticeView practice={practiceWithoutDuas2} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(queryByText2("Relevant Du'as:")).toBeNull();
    });
  });
});
