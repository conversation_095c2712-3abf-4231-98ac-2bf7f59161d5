import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import QuranicVerseView from '../QuranicVerseView'; // Adjust path as necessary
import { DailyPractice } from '../../../../../libs/shared-types/src/lib/journey.types'; // Adjust path

// Mock Ionicons
jest.mock('@expo/vector-icons', () => {
  const { View } = require('react-native');
  return {
    Ionicons: (props: any) => <View testID="ionicons-mock" {...props} />,
  };
});

// Mock expo-av Audio (similar to NameOfAllahView.spec.tsx)
const mockPlayAsync = jest.fn().mockResolvedValue({});
const mockPauseAsync = jest.fn().mockResolvedValue({});
const mockStopAsync = jest.fn().mockResolvedValue({});
const mockUnloadAsync = jest.fn().mockResolvedValue({});
const mockGetStatusAsync = jest.fn().mockResolvedValue({ isLoaded: false, isPlaying: false, didJustFinish: false });
const mockSetOnPlaybackStatusUpdate = jest.fn();

const mockSoundObject = {
  playAsync: mockPlayAsync,
  pauseAsync: mockPauseAsync,
  stopAsync: mockStopAsync,
  unloadAsync: mockUnloadAsync,
  getStatusAsync: mockGetStatusAsync,
  setOnPlaybackStatusUpdate: mockSetOnPlaybackStatusUpdate,
};

jest.mock('expo-av', () => ({
  Audio: {
    Sound: {
      createAsync: jest.fn().mockResolvedValue({ sound: mockSoundObject }),
    },
  },
}));

describe('QuranicVerseView', () => {
  const mockPracticeBase: DailyPractice = {
    id: 'qvr-123',
    type: 'QuranicVerseReflection',
    title: 'Surah Al-Fatihah Reflection',
    description: 'Reflect on the opening chapter.',
    duration: 10,
    layerFocus: 'ruh',
    difficultyLevel: 'beginner',
    componentDetails: {
      originalContentId: 'qvr-orig-123',
      titleOverride: 'Surah Al-Fatihah (The Opening)',
      surahName: 'Al-Fatihah',
      surahNumber: 1,
      ayahNumber: 1,
      ayahEndNumber: 7,
      arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ...',
      translationEn: 'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
      audioUrl: 'https://example.com/fatihah.mp3',
      reciterOptions: {
        'Mishary Rashid Alafasy': 'https://example.com/fatihah_alafasy.mp3',
        'Abdur-Rahman as-Sudais': 'https://example.com/fatihah_sudais.mp3',
      },
      contextualExplanation: 'This Surah is the essence of the Quran.',
      tafsirEn: 'A brief tafsir.',
      tafsirSource: 'Simplified Tafsir',
      reflectionPrompts: ['What does guidance mean to you?'],
      practicalApplication: 'Recite with presence in prayers.',
    },
    dayNumber: 1,
    journeyId: 'journey-xyz',
    status: 'pending',
  };

  const mockOnProgressSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset individual sound mock functions if they maintain state across tests within a describe block
    mockPlayAsync.mockClear();
    mockPauseAsync.mockClear();
    mockStopAsync.mockClear();
    mockUnloadAsync.mockClear();
    mockGetStatusAsync.mockClear().mockResolvedValue({ isLoaded: false, isPlaying: false, didJustFinish: false });
    mockSetOnPlaybackStatusUpdate.mockClear();
    (Audio.Sound.createAsync as jest.Mock).mockClear().mockResolvedValue({ sound: mockSoundObject });
  });

  it('renders correctly with all details', () => {
    const { getByText } = render(
      <QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />
    );

    // Check for title (uses titleOverride if present)
    expect(getByText('Surah Al-Fatihah (The Opening)')).toBeTruthy();
    // Check for Arabic text
    expect(getByText('بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ...')).toBeTruthy();
    // Check for English translation
    expect(getByText('In the name of Allah, the Entirely Merciful, the Especially Merciful.')).toBeTruthy();
    // Check for a reflection prompt
    expect(getByText('What does guidance mean to you?')).toBeTruthy();
    // Check for audio button (text implies default reciter or first in list)
    expect(getByText('Listen (Default)')).toBeTruthy(); // Initial reciter name might be 'Default' or derived
  });

  it('renders practice.title if componentDetails.titleOverride is not available', () => {
    const practiceWithoutTitleOverride: DailyPractice = {
      ...mockPracticeBase,
      componentDetails: {
        ...mockPracticeBase.componentDetails,
        titleOverride: undefined,
      },
    };
    const { getByText } = render(
      <QuranicVerseView practice={practiceWithoutTitleOverride} onProgressSubmit={mockOnProgressSubmit} />
    );
    expect(getByText(mockPracticeBase.title)).toBeTruthy(); // Falls back to practice.title
  });

  it('calls onProgressSubmit when "Mark as Reflected" button is pressed', () => {
    const { getByText } = render(
      <QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />
    );

    const completeButton = getByText('Mark as Reflected');
    fireEvent.press(completeButton);

    expect(mockOnProgressSubmit).toHaveBeenCalledWith('qvr-123', {
      practiceId: 'qvr-123',
      title: 'Surah Al-Fatihah Reflection', // This is practice.title
      completed: true,
    });
    expect(getByText('Reflection Completed')).toBeTruthy(); // Button text changes
  });

  // More tests to be added for audio playback, reciter selection, etc.
  // For example:
  it('displays reciter options if provided', () => {
    const { getByText } = render(
      <QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />
    );
    expect(getByText('Change Reciter:')).toBeTruthy();
    expect(getByText('Mishary Rashid Alafasy')).toBeTruthy();
    expect(getByText('Abdur-Rahman as-Sudais')).toBeTruthy();
  });

  // TODO: Add tests for audio play/pause, reciter selection logic, and different content states (e.g., no audioUrl)

  describe('Content Display based on Props', () => {
    it('renders Urdu translation when provided', () => {
      const practiceWithUrdu = {
        ...mockPracticeBase,
        componentDetails: {
          ...mockPracticeBase.componentDetails,
          translationUr: 'یہ ایک اردو ترجمہ ہے۔',
        },
      };
      const { getByText } = render(<QuranicVerseView practice={practiceWithUrdu} onProgressSubmit={mockOnProgressSubmit} />);
      expect(getByText('Translation (Urdu):')).toBeTruthy();
      expect(getByText('یہ ایک اردو ترجمہ ہے۔')).toBeTruthy();
    });

    it('renders contextual explanation when provided', () => {
      const { getByText } = render(<QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />);
      expect(getByText('Contextual Explanation:')).toBeTruthy();
      expect(getByText(mockPracticeBase.componentDetails.contextualExplanation!)).toBeTruthy();
    });

    it('renders Tafsir and its source when provided', () => {
      const { getByText } = render(<QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />);
      expect(getByText(`Tafsir / Commentary (${mockPracticeBase.componentDetails.tafsirSource || 'Insights'}):`)).toBeTruthy();
      expect(getByText(mockPracticeBase.componentDetails.tafsirEn!)).toBeTruthy();
    });

    it('renders Tafsir with default source "Insights" if tafsirSource is not provided', () => {
      const practiceWithoutTafsirSource = {
        ...mockPracticeBase,
        componentDetails: {
          ...mockPracticeBase.componentDetails,
          tafsirSource: undefined,
        },
      };
      const { getByText } = render(<QuranicVerseView practice={practiceWithoutTafsirSource} onProgressSubmit={mockOnProgressSubmit} />);
      expect(getByText('Tafsir / Commentary (Insights):')).toBeTruthy();
    });

    it('renders practical application when provided', () => {
      const { getByText } = render(<QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />);
      expect(getByText('Practical Application in Daily Life:')).toBeTruthy();
      expect(getByText(mockPracticeBase.componentDetails.practicalApplication!)).toBeTruthy();
    });

    it('renders all reflection prompts when multiple are provided', () => {
      const practiceWithMultiplePrompts = {
        ...mockPracticeBase,
        componentDetails: {
          ...mockPracticeBase.componentDetails,
          reflectionPrompts: ['Prompt 1?', 'Prompt 2?', 'Prompt 3?'],
        },
      };
      const { getByText } = render(<QuranicVerseView practice={practiceWithMultiplePrompts} onProgressSubmit={mockOnProgressSubmit} />);
      expect(getByText('Reflection Prompts:')).toBeTruthy();
      expect(getByText('Prompt 1?')).toBeTruthy();
      expect(getByText('Prompt 2?')).toBeTruthy();
      expect(getByText('Prompt 3?')).toBeTruthy();
    });

    it('does not render optional text sections if their data is missing', () => {
      const minimalDetailsPractice: DailyPractice = {
        ...mockPracticeBase,
        componentDetails: { // Only essential fields for QuranicVerse
          originalContentId: 'qvr-min-456',
          arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
          // All other optional text fields are undefined
          translationEn: undefined,
          translationUr: undefined,
          contextualExplanation: undefined,
          tafsirEn: undefined,
          tafsirSource: undefined,
          reflectionPrompts: [], // or undefined
          practicalApplication: undefined,
          audioUrl: undefined, // No audio for this test
          reciterOptions: undefined,
        },
      };
      const { queryByText } = render(
        <QuranicVerseView practice={minimalDetailsPractice} onProgressSubmit={mockOnProgressSubmit} />
      );
      expect(queryByText('Translation (English):')).toBeNull();
      expect(queryByText('Translation (Urdu):')).toBeNull();
      expect(queryByText('Contextual Explanation:')).toBeNull();
      expect(queryByText(/Tafsir \/ Commentary/)).toBeNull();
      expect(queryByText('Reflection Prompts:')).toBeNull();
      expect(queryByText('Practical Application in Daily Life:')).toBeNull();
      // Ensure Arabic text is still there
      expect(queryByText('الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ')).toBeTruthy();
    });
  });

  it('plays and pauses audio for the default reciter', async () => {
    const { getByText, findByText } = render(
      <QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />
    );

    const audioButton = getByText('Listen (Default)'); // Initial state with default reciter

    // 1. First press: Play
    fireEvent.press(audioButton);
    expect(Audio.Sound.createAsync).toHaveBeenCalledWith(
      { uri: mockPracticeBase.componentDetails.audioUrl }, // Default audio URL
      { shouldPlay: true }
    );
    // Simulate sound loaded and playing (useEffect updates isPlaying)
    mockGetStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: true, didJustFinish: false });
    // Manually trigger a status update if needed, or rely on createAsync's shouldPlay:true to implicitly start
    // For testing, we might need to simulate the callback being called that updates `isPlaying`
    // Let's assume `setOnPlaybackStatusUpdate` is called and we can simulate its invocation if needed
    // However, the text change is a good indicator.
    await findByText('Pause (Default)'); // Wait for text to change due to isPlaying state update

    // 2. Second press: Pause
    mockGetStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: true, didJustFinish: false }); // Sound is playing
    fireEvent.press(audioButton);
    expect(mockPauseAsync).toHaveBeenCalledTimes(1);
    await findByText('Listen (Default)'); // Text changes back

    // 3. Third press: Play again
    mockGetStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: false, didJustFinish: false }); // Sound is paused
    fireEvent.press(audioButton);
    expect(mockPlayAsync).toHaveBeenCalledTimes(1); // playAsync on the existing sound object
    await findByText('Pause (Default)');
  });

  it('selecting a new reciter updates button text and auto-plays new audio', async () => {
    const { getByText, findByText } = render(
      <QuranicVerseView practice={mockPracticeBase} onProgressSubmit={mockOnProgressSubmit} />
    );

    const newReciterName = 'Mishary Rashid Alafasy';
    const newReciterUrl = mockPracticeBase.componentDetails.reciterOptions![newReciterName];

    const reciterButton = getByText(newReciterName);

    // Mock that a sound might already be loaded (e.g., default one)
    // This ensures the unload path is tested if a sound object exists.
    mockGetStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: false, didJustFinish: false });


    fireEvent.press(reciterButton);

    // Check if existing sound was stopped/unloaded
    // The component logic: if (sound) { await sound.stopAsync(); await sound.unloadAsync(); setSound(null); }
    // So, if 'sound' was not null initially (which it is in this test setup unless we play default first),
    // these would be called. For a clean test of reciter change, let's ensure sound object isn't null.
    // For simplicity, we assume the initial sound object (mockSoundObject) is the one being interacted with.
    // If a sound was playing or loaded from default, it should be unloaded.
    // If no sound was loaded initially, createAsync will just be called for the new one.

    // The playSound function will call unloadAsync if `sound` is not null.
    // In this test's current setup, `sound` is initially null before any play action.
    // So, when a reciter is selected first, it will directly go to createAsync.
    // If we want to test the unload path, we'd need to play default first.

    // Let's test the current path: no prior sound played.
    expect(mockUnloadAsync).not.toHaveBeenCalled(); // Because sound was initially null

    // Check if new sound is created with the new reciter's URL
    expect(Audio.Sound.createAsync).toHaveBeenCalledWith(
      { uri: newReciterUrl },
      { shouldPlay: true } // Due to auto-play
    );

    // Wait for the button text to update to the new reciter and playing state
    // This relies on setOnPlaybackStatusUpdate and the subsequent state changes.
    mockGetStatusAsync.mockResolvedValueOnce({ isLoaded: true, isPlaying: true, didJustFinish: false });
    await findByText(`Pause (${newReciterName})`);
  });
});
