import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, ScrollView, Alert } from 'react-native';
import { DailyPractice } from '../../../../libs/shared-types/src/lib/journey.types';
import { Ionicons } from '@expo/vector-icons';

interface PersonalReflectionJournalingViewProps {
  practice: DailyPractice;
  onProgressSubmit: (progressData: any) => void;
}

const PersonalReflectionJournalingView: React.FC<PersonalReflectionJournalingViewProps> = ({ practice, onProgressSubmit }) => {
  const [journalEntry, setJournalEntry] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const details = practice.componentDetails || practice.componentData || {};
  const prompts: string[] = details.prompts || []; // Ensure prompts is an array of strings

  const handleSubmit = () => {
    if (isSubmitted || !journalEntry.trim()) return;

    const progressDetails = {
      practiceId: practice.id,
      title: practice.title,
      dailyReflection: journalEntry,
      completed: true,
      // Optional: include selected gratitude/challenges/insights if collected here
      // gratitude: [],
      // challenges: [],
      // insights: [],
    };
    onProgressSubmit(practice.id, progressDetails);
    setIsSubmitted(true);
    // setJournalEntry(''); // Optionally clear or keep for user to see
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainerScrollView}>
      <View style={styles.header}>
        <Ionicons name="create-outline" size={28} color="#A78BFA" style={styles.icon} />
        <Text style={styles.title}>{practice.title}</Text>
      </View>
      <Text style={styles.description}>{practice.description}</Text>

      {prompts.length > 0 && (
        <View style={styles.promptsContainer}>
          <Text style={styles.label}>Prompts to guide your reflection:</Text>
          {prompts.map((prompt, index) => (
            <Text key={index} style={styles.promptItem}><Ionicons name="bulb-outline" size={16} color="#A78BFA"/> {prompt}</Text>
          ))}
        </View>
      )}

      <TextInput
        style={[styles.journalInput, isSubmitted && styles.disabledInput]}
        multiline
        placeholder="Pour out your thoughts and feelings here. This is a safe space for you to reflect with Allah's presence in mind."
        placeholderTextColor="rgba(255,255,255,0.5)" // Slightly more visible placeholder
        value={journalEntry}
        onChangeText={setJournalEntry}
        editable={!isSubmitted}
        textAlignVertical="top" // Ensure text starts from top
      />

      {details.allowVoiceNote && ( // Assuming details.allowVoiceNote might come from componentDetails
        <TouchableOpacity
            style={[styles.voiceButton, isSubmitted && styles.disabledInput]}
            disabled={isSubmitted} // Disable button itself
            onPress={() => Alert.alert("Voice Note", "Voice note recording feature is coming soon!")} // Placeholder action
        >
          <Ionicons name="mic-circle-outline" size={28} color={isSubmitted ? "grey" :"#8E7FFA"} />
          <Text style={[styles.voiceButtonText, isSubmitted && styles.disabledText]}>Record Voice Note</Text>
        </TouchableOpacity>
      )}

      <TouchableOpacity
        style={[styles.submitButton, (isSubmitted || !journalEntry.trim()) && styles.submitButtonDisabled]}
        onPress={handleSubmit}
        disabled={isSubmitted || !journalEntry.trim()}
      >
        <Ionicons name={isSubmitted ? "checkmark-done-circle-outline" : "save-outline"} size={20} color="#fff" style={{marginRight: 8}}/>
        <Text style={styles.submitButtonText}>{isSubmitted ? 'Reflection Saved' : 'Save Reflection'}</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainerScrollView: {
     padding: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  icon: {
    marginRight: 10,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    flex:1,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    marginBottom: 20,
    lineHeight: 23,
  },
  promptsContainer: {
    marginBottom: 20,
    paddingVertical: 15,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0,0,0,0.15)',
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#A78BFA',
  },
  label: {
    fontSize: 18, // Standardized
    fontWeight: 'bold',
    color: 'rgba(255,255,255,0.95)',
    marginBottom: 10,
  },
  promptItem: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.88)',
    lineHeight: 24,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  journalInput: {
    backgroundColor: 'rgba(0,0,0,0.25)',
    color: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 10,
    minHeight: 200, // Increased height
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  voiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(142, 127, 250, 0.15)',
    borderRadius: 25,
    marginBottom: 25,
    alignSelf: 'center',
    borderWidth: 1,
    borderColor: 'rgba(142, 127, 250, 0.4)',
  },
  voiceButtonText: {
    color: '#A78BFA',
    marginLeft: 10,
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    flexDirection: 'row',
    backgroundColor: '#5cb85c',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10, // Reduced top margin
    marginBottom: 20,
  },
  submitButtonDisabled: {
    backgroundColor: '#777',
    opacity: 0.7, // Keep opacity for visual cue along with color change
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  disabledInput: {
    opacity: 0.6,
  },
  disabledText: {
     color: 'grey',
  }
});

export default PersonalReflectionJournalingView;
