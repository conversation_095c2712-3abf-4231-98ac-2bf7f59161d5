import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput } from 'react-native';
import { DailyPractice } from '../../../../libs/shared-types/src/lib/journey.types'; // Adjusted path
import { Ionicons } from '@expo/vector-icons'; // Assuming Ionicons are used

interface MorningCheckInViewProps {
  practice: DailyPractice; // Contains componentDetails for prompts etc.
  onProgressSubmit: (progressData: any) => void; // Callback to submit progress
}

const MorningCheckInView: React.FC<MorningCheckInViewProps> = ({ practice, onProgressSubmit }) => {
  const [mood, setMood] = useState<number>(5); // Default to neutral
  const [energy, setEnergy] = useState<number>(5); // Default to neutral
  const [spiritualState, setSpiritualState] = useState('');
  const [intention, setIntention] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Add safety checks for practice data
  if (!practice) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Practice data not available</Text>
      </View>
    );
  }

  const details = practice.componentDetails || practice.componentData || {};
  const moodScaleMin = details.moodScaleMin || 1;
  const moodScaleMax = details.moodScaleMax || 10;
  const moodDescriptorLow = details.moodDescriptorLow || "Heavy";
  const moodDescriptorHigh = details.moodDescriptorHigh || "Light";
  const energyPrompt = details.energyPrompt || "Your energy level (1-10):";
  const spiritualStatePrompt = details.spiritualStatePrompt || "Briefly describe your spiritual state:";
  const intentionPrompt = details.intentionPrompt || "Today's Niyyah (Intention for Allah's sake):";


  const handleSubmit = () => {
    if (isSubmitted) return; // Prevent double submission

    const progressDetails = {
      practiceId: practice.id, // ID of this MorningCheckIn practice component
      title: practice.title, // To help identify in dashboard's aggregate
      moodBefore: mood,
      energyLevelBefore: energy,
      spiritualStateBefore: spiritualState,
      dailyIntention: intention,
      completed: true, // Mark this specific component as completed
      // No need for completedAt here as it's part of the wrapper in JourneyDashboard
    };
    onProgressSubmit(practice.id, progressDetails);
    setIsSubmitted(true);
  };

  // Basic mood/energy selector component
  const ScaleSelector = ({ value, setValue, min, max, labelLow, labelHigh }) => (
    <View>
        <View style={styles.scaleLabels}>
            <Text style={styles.scaleDescriptor}>{labelLow}</Text>
            <Text style={styles.scaleDescriptor}>{labelHigh}</Text>
        </View>
        <View style={styles.scaleSelector}>
            {[...Array(max - min + 1)].map((_, i) => {
                const val = min + i;
                return (
                <TouchableOpacity
                    key={val}
                    style={[styles.scaleButton, value === val && styles.scaleSelected]}
                    onPress={() => setValue(val)}
                    disabled={isSubmitted}
                >
                    <Text style={styles.scaleText}>{val}</Text>
                </TouchableOpacity>
                );
            })}
        </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="sunny-outline" size={28} color="#FFCA28" style={styles.icon} />
        <Text style={styles.title}>{practice.title}</Text>
      </View>
      <Text style={styles.description}>{practice.description}</Text>

      <View style={styles.section}>
        <Text style={styles.label}>1. How is your heart feeling today?</Text>
        <ScaleSelector value={mood} setValue={setMood} min={moodScaleMin} max={moodScaleMax} labelLow={moodDescriptorLow} labelHigh={moodDescriptorHigh} disabled={isSubmitted}/>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>2. {energyPrompt}</Text>
        <ScaleSelector value={energy} setValue={setEnergy} min={1} max={10} labelLow="Low" labelHigh="High" disabled={isSubmitted} />
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>3. {spiritualStatePrompt}</Text>
        <TextInput
          style={[styles.input, isSubmitted && styles.disabledInput]}
          placeholder="e.g., hopeful, peaceful, seeking connection..."
          placeholderTextColor="rgba(255,255,255,0.4)"
          value={spiritualState}
          onChangeText={setSpiritualState}
          editable={!isSubmitted}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>4. {intentionPrompt}</Text>
        <TextInput
          style={[styles.input, styles.textArea, isSubmitted && styles.disabledInput]}
          placeholder="Example: Ya Allah, I intend to be more patient today and to remember You often..."
          placeholderTextColor="rgba(255,255,255,0.4)"
          value={intention}
          onChangeText={setIntention}
          multiline
          editable={!isSubmitted}
        />
      </View>

      <TouchableOpacity
        style={[styles.submitButton, (isSubmitted || !intention.trim() || !spiritualState.trim()) && styles.submitButtonDisabled]}
        onPress={handleSubmit}
        disabled={isSubmitted || !intention.trim() || !spiritualState.trim()}
      >
        <Ionicons name={isSubmitted ? "checkmark-circle" : "send-outline"} size={20} color="#fff" style={{marginRight: 8}}/>
        <Text style={styles.submitButtonText}>{isSubmitted ? 'Check-In Submitted' : 'Submit Check-In'}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  icon: {
    marginRight: 10,
  },
  title: {
    fontSize: 22, // Increased
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
  },
  description: {
    fontSize: 16, // Increased
    color: 'rgba(255,255,255,0.85)',
    marginBottom: 20, // Increased spacing
    lineHeight: 23,
  },
  section: { // Added for grouping questions
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  label: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'rgba(255,255,255,0.95)',
    marginBottom: 10,
  },
  input: {
    backgroundColor: 'rgba(0,0,0,0.25)', // Slightly darker
    color: '#fff',
    paddingHorizontal: 15, // More padding
    paddingVertical: 12, // More padding
    borderRadius: 8, // More rounded
    marginBottom: 10,
    fontSize: 16, // Increased
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  disabledInput: {
    opacity: 0.6,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  textArea: {
    minHeight: 80, // Increased
    textAlignVertical: 'top',
  },
  scaleLabels: { // For Low/High labels in ScaleSelector
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
    paddingHorizontal: 5, // Align with buttons
  },
  scaleDescriptor: {
    fontSize: 13,
    color: 'rgba(255,255,255,0.7)',
  },
  scaleSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around', // Use space-around for better distribution
    alignItems: 'center',
    marginBottom: 10,
    paddingVertical: 5,
  },
  scaleButton: {
    borderWidth: 1.5, // Slightly thicker border
    borderColor: 'rgba(255,255,255,0.6)',
    borderRadius: 22, // Circular
    width: 36, // Larger touch target
    height: 36, // Larger touch target
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2, // Add some horizontal margin
  },
  scaleSelected: {
    backgroundColor: '#FFCA28', // Use a color that matches the sun icon
    borderColor: '#FFCA28',
  },
  scaleText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  submitButton: {
    flexDirection: 'row', // For icon
    backgroundColor: '#5cb85c', // Green, consistent with other complete buttons
    paddingVertical: 15, // Increased padding
    borderRadius: 10, // More rounded
    alignItems: 'center',
    justifyContent: 'center', // Center content
    marginTop: 25, // Increased spacing
  },
  submitButtonDisabled: {
    backgroundColor: '#777', // Darker grey for disabled
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18, // Increased
    fontWeight: 'bold',
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 16,
    textAlign: 'center',
    padding: 20,
  },
});

export default MorningCheckInView;
