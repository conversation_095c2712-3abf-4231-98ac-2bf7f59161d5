import React, { useState, useEffect } from 'react'; // Added useEffect
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView, ActivityIndicator, Alert } from 'react-native'; // Added ActivityIndicator, Alert
import { DailyPractice } from '../../../../libs/shared-types/src/lib/journey.types';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av'; // Import Audio

interface NameOfAllahViewProps {
  practice: DailyPractice;
  onProgressSubmit: (practiceId: string, progressData: any) => void;
}

const NameOfAllahView: React.FC<NameOfAllahViewProps> = ({ practice, onProgressSubmit }) => {
  const details = practice.componentDetails || practice.componentData || {};
  const [dhikrCurrentCount, setDhikrCurrentCount] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);

  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoadingAudio, setIsLoadingAudio] = useState(false);

  const targetDhikrCount = details.dhikrCount || 33;

  const handleDhikrIncrement = () => {
    if (isCompleted) return;
    setDhikrCurrentCount(prev => Math.min(prev + 1, targetDhikrCount));
  };

  const handleDhikrDecrement = () => {
    if (isCompleted) return;
    setDhikrCurrentCount(prev => Math.max(prev - 1, 0));
  };

  const handleComplete = () => {
    if (isCompleted) return;
    onProgressSubmit(practice.id, {
      practiceId: practice.id,
      title: practice.title,
      completed: true,
      dhikr_actual_count: dhikrCurrentCount,
    });
    setIsCompleted(true);
    if (sound) { // Stop audio if playing when component is marked completed
      sound.stopAsync();
      setIsPlaying(false);
    }
  };

  async function playSound() {
    if (isCompleted) return;
    if (!details.audioUrl) {
      console.log("No audio URL provided");
      return;
    }

    setIsLoadingAudio(true);
    console.log('Loading Sound from:', details.audioUrl);
    try {
      if (sound) { // If sound object exists
        const status = await sound.getStatusAsync();
        if (status.isLoaded) {
          if (isPlaying) {
            await sound.pauseAsync();
            setIsPlaying(false);
          } else {
            await sound.playAsync();
            setIsPlaying(true);
          }
          setIsLoadingAudio(false);
          return;
        }
      }

      // If sound is not loaded or doesn't exist, create and load new sound
      const { sound: newSound } = await Audio.Sound.createAsync(
         { uri: details.audioUrl }, // Assuming details.audioUrl is a valid URL
         { shouldPlay: true }
      );
      setSound(newSound);
      setIsPlaying(true);
      newSound.setOnPlaybackStatusUpdate(_onPlaybackStatusUpdate);
    } catch (error) {
      console.error("Error loading or playing sound:", error);
      Alert.alert(
        "Audio Error",
        "Could not load or play the audio at this time. Please check your connection or try again later."
      );
    } finally {
      setIsLoadingAudio(false);
    }
  }

  const _onPlaybackStatusUpdate = (playbackStatus: Audio.PlaybackStatus) => {
    if (!playbackStatus.isLoaded) {
      // Update your UI for the unloaded state
      if (playbackStatus.error) {
        console.log(`Encountered a fatal error during playback: ${playbackStatus.error}`);
        // Alert.alert("Playback Error", "An unexpected error occurred during playback."); // Optional: alert here too
        // Send Expo team the error if this happens!
      }
    } else {
      // Update your UI for the loaded state
      setIsPlaying(playbackStatus.isPlaying);

      if (playbackStatus.didJustFinish && !playbackStatus.isLooping) {
        // The player has just finished playing and will stop. Maybe unload the sound?
        console.log("Playback finished");
        setIsPlaying(false);
        // Optionally unload sound
        // sound?.unloadAsync();
        // setSound(null);
      }
    }
  };

  useEffect(() => {
    return sound
      ? () => {
          console.log('Unloading Sound');
          sound.unloadAsync();
          setSound(null); // Clear the sound state
          setIsPlaying(false); // Reset playing state
        }
      : undefined;
  }, [sound]);


  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainerScrollView}>
      <View style={styles.header}>
         <Ionicons name="heart-circle-outline" size={28} color="#E91E63" style={styles.icon} />
         <Text style={styles.title}>{details.name || practice.title}</Text>
      </View>

      {/* Image Display Logic: Prioritize calligraphyUrl, then check if arabicScript is a URL, then render arabicScript as text */}
      {details.calligraphyUrl ? (
        <Image
          source={{ uri: details.calligraphyUrl }}
          style={styles.calligraphyImage}
          resizeMode="contain"
          onError={(e) => console.warn("Failed to load calligraphyUrl:", e.nativeEvent.error)}
        />
      ) : details.arabicScript && (details.arabicScript.startsWith('http://') || details.arabicScript.startsWith('https://') || /\.(png|svg|jpg|jpeg|webp)$/i.test(details.arabicScript)) ? (
        <Image
          source={{ uri: details.arabicScript }}
          style={styles.calligraphyImage} // Can use the same style or a different one if needed
          resizeMode="contain"
          onError={(e) => console.warn("Failed to load arabicScript as image:", e.nativeEvent.error)}
        />
      ) : details.arabicScript ? (
        <Text style={styles.arabicName}>{details.arabicScript}</Text>
      ) : null}

      {details.audioUrl && (
        <TouchableOpacity
            style={[styles.audioButton, (isCompleted || isLoadingAudio) && styles.disabledInput]}
            onPress={playSound} // Changed from playAudio
            disabled={isCompleted || isLoadingAudio}
        >
          {isLoadingAudio ? (
            <ActivityIndicator size="small" color="#69d2e7" />
          ) : (
            <Ionicons name={isPlaying ? "pause-circle-outline" : "play-circle-outline"} size={28} color={(isCompleted || isLoadingAudio) ? "grey" : "#69d2e7"} />
          )}
          <Text style={[styles.audioText, (isCompleted || isLoadingAudio) && styles.disabledText]}>
            {isLoadingAudio ? "Loading..." : isPlaying ? "Pause Pronunciation" : "Listen to Pronunciation"}
          </Text>
        </TouchableOpacity>
      )}

      <View style={styles.section}>
        <Text style={styles.label}>Meaning:</Text>
        <Text style={styles.textBlock}>{details.meaning}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Spiritual Significance:</Text>
        <Text style={styles.textBlock}>{details.significance}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Reflection Prompt:</Text>
        <Text style={styles.textBlock}>{details.reflectionPrompt}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Practical Application:</Text>
        <Text style={styles.textBlock}>{details.practicalApplication}</Text>
      </View>

      {details.dhikrCount && (
        <View style={[styles.section, styles.dhikrSection]}>
          <Text style={styles.label}>Dhikr Practice:</Text>
          <Text style={styles.textBlock}>Repeat "Ya {details.name}" <Text style={styles.boldText}>{targetDhikrCount}</Text> times with heart focus.</Text>

          <View style={styles.counterContainer}>
            <TouchableOpacity
              testID="dhikr-decrement-button"
              style={[styles.counterAdjustButton, (isCompleted || dhikrCurrentCount === 0) && styles.disabledInput]}
              onPress={handleDhikrDecrement}
              disabled={isCompleted || dhikrCurrentCount === 0}
            >
              <Ionicons name="remove-circle-outline" size={36} color={(isCompleted || dhikrCurrentCount === 0) ? "grey" : "#FF7043"} />
            </TouchableOpacity>
            <Text style={styles.counterText}>{dhikrCurrentCount}</Text>
            <TouchableOpacity
              testID="dhikr-increment-button"
              style={[styles.counterAdjustButton, (isCompleted || dhikrCurrentCount >= targetDhikrCount) && styles.disabledInput]}
              onPress={handleDhikrIncrement}
              disabled={isCompleted || dhikrCurrentCount >= targetDhikrCount}
            >
              <Ionicons name="add-circle-outline" size={36} color={(isCompleted || dhikrCurrentCount >= targetDhikrCount) ? "grey" : "#4CAF50"} />
            </TouchableOpacity>
          </View>
           {dhikrCurrentCount >= targetDhikrCount && !isCompleted && (
             <Text style={styles.dhikrGoalReachedText}>Goal reached! You can continue if you wish.</Text>
           )}
        </View>
      )}

      <TouchableOpacity
        style={[styles.completeButton, isCompleted && styles.completeButtonDisabled]}
        onPress={handleComplete}
        disabled={isCompleted}
      >
        <Ionicons name={isCompleted ? "checkmark-circle" : "ellipse-outline"} size={20} color="#fff" style={{marginRight: 8}}/>
        <Text style={styles.completeButtonText}>{isCompleted ? 'Practice Completed' : 'Mark as Completed'}</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1, // Ensure ScrollView takes up space
  },
  contentContainerScrollView: {
    padding: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15, // Increased spacing
  },
  icon: {
    marginRight: 10,
  },
  title: {
    fontSize: 22, // Increased
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
  },
  arabicName: {
    fontSize: 48, // Significantly larger for prominence
    color: '#fff',
    textAlign: 'center',
    fontFamily: 'ArabicFont', // Applied custom font
    marginBottom: 20, // Increased spacing
    lineHeight: 60, // Adjust line height for larger font
  },
  calligraphyImage: {
    width: '100%',
    height: 100, // Adjust as needed, or make it dynamic based on image aspect ratio
    marginBottom: 20,
    alignSelf: 'center',
  },
  audioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(74, 144, 164, 0.15)',
    borderRadius: 25, // More rounded
    marginBottom: 25,
    alignSelf: 'center',
    borderWidth: 1,
    borderColor: 'rgba(74, 144, 164, 0.3)',
  },
  audioText: {
    color: '#69d2e7', // Brighter color
    marginLeft: 10,
    fontSize: 16,
    fontWeight: '600', // Bolder
  },
  section: { // Added for better grouping
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  label: {
    fontSize: 18, // Increased
    fontWeight: '600', // Bolder (adjusted from 'bold')
    color: 'rgba(255,255,255,1)', // More opaque
    marginTop: 10, // Adjusted
    marginBottom: 8,
  },
  textBlock: {
    fontSize: 16, // Increased
    color: 'rgba(255,255,255,0.8)', // Slightly less opaque
    lineHeight: 24, // Increased
    marginBottom: 10, // Adjusted
  },
  dhikrSection: {
    marginTop: 20,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.2)',
  },
  counterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around', // Better distribution
    marginTop: 12,
    marginBottom: 10,
    backgroundColor: 'rgba(0,0,0,0.2)',
    borderRadius: 12,
    paddingVertical: 15, // More padding
  },
  counterAdjustButton: { // New style for +/- buttons
    padding: 10,
  },
  counterText: {
    color: '#fff',
    fontSize: 28, // Larger counter text
    fontWeight: 'bold',
    minWidth: 70,
    textAlign: 'center',
  },
  dhikrGoalReachedText: {
    color: '#4CAF50', // Green color for success
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: 15,
  },
  completeButton: {
    flexDirection: 'row', // For icon and text
    backgroundColor: '#5cb85c', // Green for completion
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center', // Center content
    marginTop: 30,
    marginBottom: 30, // More bottom margin
  },
  completeButtonDisabled: {
    backgroundColor: '#777', // Darker grey when disabled
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 18, // Larger button text
    fontWeight: 'bold',
  },
  disabledInput: {
    opacity: 0.5, // General disabled style
  },
  disabledText: { // For text within disabled elements
    color: 'grey',
  },
  boldText: { // Utility for bolding parts of text
      fontWeight: 'bold',
  }
});

export default NameOfAllahView;
