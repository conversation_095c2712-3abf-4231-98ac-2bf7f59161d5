import React from 'react';

import { StyleSheet } from 'react-native';

import { Feather } from '@expo/vector-icons';
import { format } from 'date-fns';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { JournalEntry } from '../services/api/types';

import { Badge } from './ui/Badge';
import { Card } from './ui/Card';
import { Text } from './ui/Text';
import { View } from './ui/View';

interface JournalEntryCardProps {
  entry: JournalEntry;
  onPress: (entry: JournalEntry) => void;
}

export const JournalEntryCard = ({ entry, onPress }: JournalEntryCardProps) => {
  // Format date
  const formattedDate = format(new Date(entry.createdAt), 'MMM d, yyyy');
  const formattedTime = format(new Date(entry.createdAt), 'h:mm a');

  // Get category information
  const getCategoryInfo = () => {
    switch (entry.category) {
      case 'jism':
        return {
          label: 'Body',
          icon: 'activity',
          color: colors.error,
        };
      case 'nafs':
        return {
          label: 'Emotions',
          icon: 'wind',
          color: colors.secondary,
        };
      case 'aql':
        return {
          label: 'Mind',
          icon: 'cpu',
          color: colors.aqlYellow,
        };
      case 'qalb':
        return {
          label: 'Heart',
          icon: 'heart',
          color: colors.primary,
        };
      case 'ruh':
        return {
          label: 'Soul',
          icon: 'star',
          color: colors.accent,
        };
      default:
        return {
          label: 'General',
          icon: 'book',
          color: colors.primary,
        };
    }
  };

  const categoryInfo = getCategoryInfo();

  // Truncate content for preview
  const previewContent =
    entry.content.length > 120
      ? `${entry.content.substring(0, 120)}...`
      : entry.content;

  return (
    <Card style={styles.card} onPress={() => onPress(entry)}>
      <View style={styles.header}>
        <View style={styles.dateContainer}>
          <Text variant="caption" color="textSecondary">
            {formattedDate}
          </Text>
          <Text variant="caption" color="textSecondary">
            {formattedTime}
          </Text>
        </View>

        <View
          style={[
            styles.categoryBadge,
            { backgroundColor: categoryInfo.color + '20' },
          ]}
        >
          <Feather
            name={categoryInfo.icon as any}
            size={12}
            style={styles.categoryIcon}
          />
          <Text variant="caption" style={{ color: categoryInfo.color }}>
            {categoryInfo.label}
          </Text>
        </View>
      </View>

      <Text variant="subtitle" style={styles.title}>
        {entry.title}
      </Text>

      <Text
        variant="body"
        color="textSecondary"
        style={styles.preview}
        numberOfLines={3}
      >
        {previewContent}
      </Text>

      <View style={styles.footer}>
        <View style={styles.emotionsContainer}>
          {entry.emotions.slice(0, 3).map((emotion, index) => (
            <Badge key={index} label={emotion} style={styles.emotionBadge} />
          ))}
          {entry.emotions.length > 3 && (
            <Text
              variant="caption"
              color="textSecondary"
              style={styles.moreEmotions}
            >
              +{entry.emotions.length - 3} more
            </Text>
          )}
        </View>

        {entry.isPrivate && (
          <View style={styles.privateIndicator}>
            <Feather name="lock" size={12} style={styles.privateIcon} />
            <Text variant="caption" color="textSecondary">
              Private
            </Text>
          </View>
        )}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: Theme.spacing.m,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.s,
  },
  dateContainer: {
    flexDirection: 'column',
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.s,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.pill,
  },
  categoryIcon: {
    marginRight: Theme.spacing.xs,
  },
  title: {
    marginBottom: Theme.spacing.xs,
  },
  preview: {
    marginBottom: Theme.spacing.s,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emotionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    flex: 1,
  },
  emotionBadge: {
    marginRight: Theme.spacing.xs,
    marginBottom: Theme.spacing.xs,
  },
  moreEmotions: {
    marginLeft: Theme.spacing.xs,
  },
  privateIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  privateIcon: {
    marginRight: Theme.spacing.xs,
  },
});
