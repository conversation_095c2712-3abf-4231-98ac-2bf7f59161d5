import React, { useEffect, useRef, useState } from 'react';

import {
  ActivityIndicator,
  Animated,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

import { Feather } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Card } from './ui/Card';
import { Text } from './ui/Text';
import { View } from './ui/View';

interface Verse {
  id?: string;
  surah?: string;
  ayah?: number;
  arabic: string;
  transliteration: string;
  translation: string;
  reference?: string;
  audioUrl?: string;
}

interface RuqyahPlayerProps {
  verses?: Verse[];
  onComplete?: () => void;
  autoPlay?: boolean;
}

export const RuqyahPlayer = ({
  verses = [],
  onComplete,
  autoPlay = true,
}: RuqyahPlayerProps) => {
  // Safety check for empty verses
  if (!verses || verses.length === 0) {
    return (
      <View style={styles.container}>
        <Card style={styles.card}>
          <Text variant="body" style={styles.emptyText}>
            Select Verse
          </Text>
          <Text variant="body" style={styles.emptyText}>
            Repeat
          </Text>
          <TouchableOpacity style={styles.playButton}>
            <Text variant="body">Play</Text>
          </TouchableOpacity>
        </Card>
      </View>
    );
  }

  // State
  const [currentVerseIndex, setCurrentVerseIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(0);
  const [allVersesPlayed, setAllVersesPlayed] = useState(false);

  // Animation
  const progressAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Timer reference
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Current verse
  const currentVerse = verses[currentVerseIndex];

  // Load audio when component mounts or verse changes
  useEffect(() => {
    loadAudio();

    return () => {
      // Unload audio when component unmounts
      if (sound) {
        sound.unloadAsync();
      }

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [currentVerseIndex]);

  // Auto-play next verse when current verse ends
  useEffect(() => {
    if (allVersesPlayed) {
      fadeOut();
      onComplete();
    }
  }, [allVersesPlayed, onComplete]);

  // Load audio
  const loadAudio = async () => {
    try {
      setIsLoading(true);

      // Unload previous sound if exists
      if (sound) {
        await sound.unloadAsync();
      }

      // Create and load new sound
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: currentVerse.audioUrl },
        { shouldPlay: autoPlay },
        onPlaybackStatusUpdate
      );

      setSound(newSound);
      setIsPlaying(autoPlay);
      setIsLoading(false);

      // Start progress animation if autoplay
      if (autoPlay) {
        startProgressTimer();
      }
    } catch (error) {
      console.error('Failed to load audio:', error);
      setIsLoading(false);
    }
  };

  // Play/pause audio
  const togglePlayback = async () => {
    if (!sound) {
      return;
    }

    try {
      if (isPlaying) {
        await sound.pauseAsync();

        // Stop progress timer
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      } else {
        await sound.playAsync();

        // Start progress timer
        startProgressTimer();
      }

      setIsPlaying(!isPlaying);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error toggling playback:', error);
    }
  };

  // Start progress timer
  const startProgressTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      if (sound) {
        sound.getStatusAsync().then((status) => {
          if (status.isLoaded) {
            setPosition(status.positionMillis);

            // Update progress animation
            const duration = status.durationMillis || 1;
            Animated.timing(progressAnim, {
              toValue: status.positionMillis / duration,
              duration: 100,
              useNativeDriver: false,
            }).start();
          }
        });
      }
    }, 100);
  };

  // Playback status update callback
  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      setDuration(status.durationMillis || 0);
      setPosition(status.positionMillis || 0);

      if (status.didJustFinish) {
        // Play next verse or complete
        if (currentVerseIndex < verses.length - 1) {
          goToNextVerse();
        } else {
          setAllVersesPlayed(true);
        }
      }
    }
  };

  // Go to previous verse
  const goToPreviousVerse = () => {
    if (currentVerseIndex > 0) {
      setCurrentVerseIndex((prev) => prev - 1);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  // Go to next verse
  const goToNextVerse = () => {
    if (currentVerseIndex < verses.length - 1) {
      setCurrentVerseIndex((prev) => prev + 1);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      // All verses played
      setAllVersesPlayed(true);
    }
  };

  // Format time (e.g., 01:23)
  const formatTime = (milliseconds: number): string => {
    if (!milliseconds) {
      return '00:00';
    }

    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    return `${minutes.toString().padStart(2, '0')}:${seconds
      .toString()
      .padStart(2, '0')}`;
  };

  // Fade out animation
  const fadeOut = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <Card style={styles.card}>
        {/* Verse info */}
        <View style={styles.verseInfo}>
          <Text variant="subtitle" style={styles.surahName}>
            {currentVerse.surah}
          </Text>
          <Text variant="caption" color="textSecondary">
            Verse {currentVerse.ayah}
          </Text>
        </View>

        {/* Arabic text */}
        <View style={styles.arabicContainer}>
          <Text style={styles.arabicText}>{currentVerse.arabic}</Text>
        </View>

        {/* Transliteration and translation */}
        <View style={styles.translationContainer}>
          <Text variant="body" style={styles.transliteration}>
            {currentVerse.transliteration}
          </Text>
          <Text variant="body" color="textSecondary" style={styles.translation}>
            &ldquo;{currentVerse.translation}&rdquo;
          </Text>
        </View>

        {/* Progress bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBarContainer}>
            <View
              style={[
                styles.progressBackground,
                { backgroundColor: colors.border },
              ]}
            >
              <Animated.View
                style={[
                  styles.progressFill,
                  {
                    width: progressAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    }),
                    backgroundColor: colors.primary,
                  },
                ]}
              />
            </View>
          </View>

          <View style={styles.timeInfo}>
            <Text variant="caption" color="textSecondary">
              {formatTime(position)}
            </Text>
            <Text variant="caption" color="textSecondary">
              {formatTime(duration)}
            </Text>
          </View>
        </View>

        {/* Controls */}
        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={goToPreviousVerse}
            disabled={currentVerseIndex === 0}
          >
            <Feather name="skip-back" size={24} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.playButton}
            onPress={togglePlayback}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Feather name={isPlaying ? 'pause' : 'play'} size={30} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={goToNextVerse}
            disabled={currentVerseIndex === verses.length - 1}
          >
            <Feather
              name="skip-forward"
              size={24}
              color={
                currentVerseIndex === verses.length - 1
                  ? colors.disabled
                  : colors.text
              }
            />
          </TouchableOpacity>
        </View>

        {/* Verse counter */}
        <View style={styles.verseCounter}>
          <Text variant="caption" color="textSecondary">
            {currentVerseIndex + 1} of {verses.length}
          </Text>
        </View>
      </Card>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Theme.spacing.m,
  },
  card: {
    padding: Theme.spacing.m,
  },
  verseInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  surahName: {
    fontWeight: '600',
  },
  arabicContainer: {
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
    paddingVertical: Theme.spacing.m,
    paddingHorizontal: Theme.spacing.s,
  },
  arabicText: {
    fontSize: 28,
    lineHeight: 42,
    textAlign: 'center',
    fontWeight: '600',
  },
  translationContainer: {
    marginBottom: Theme.spacing.m,
  },
  transliteration: {
    textAlign: 'center',
    marginBottom: Theme.spacing.s,
    fontStyle: 'italic',
  },
  translation: {
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: Theme.spacing.m,
  },
  progressBarContainer: {
    marginBottom: Theme.spacing.xs,
  },
  progressBackground: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  timeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  controlButton: {
    padding: Theme.spacing.m,
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: Theme.spacing.l,
  },
  verseCounter: {
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
});
