/**
 * Daily Practice Card Component
 * Displays individual practice cards for daily journey activities
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Practice {
  id: string;
  title: string;
  description?: string;
  duration: number;
  type: string;
  completed?: boolean;
  arabicText?: string;
  transliteration?: string;
  benefits?: string[];
  layerFocus?: string;
}

interface DailyPracticeCardProps {
  practice: Practice;
  onPress: () => void;
  style?: ViewStyle;
}

const practiceIcons: Record<string, string> = {
  dhikr: 'heart',
  prayer: 'moon',
  reflection: 'book',
  study: 'library',
  community: 'people',
  ruqya: 'shield',
  mindfulness: 'leaf',
  gratitude: 'star',
};

const practiceColors: Record<string, string> = {
  dhikr: '#4a90a4',
  prayer: '#2d5a87',
  reflection: '#6b73ff',
  study: '#9f7aea',
  community: '#38a169',
  ruqya: '#d69e2e',
  mindfulness: '#38b2ac',
  gratitude: '#ed8936',
};

export function DailyPracticeCard({ practice, onPress, style }: DailyPracticeCardProps) {
  const icon = practiceIcons[practice.type] || 'heart';
  const color = practiceColors[practice.type] || '#4a90a4';

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {/* Practice Icon and Type */}
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: color }]}>
            <Ionicons name={icon as any} size={24} color="#ffffff" />
          </View>
          
          <View style={styles.headerInfo}>
            <Text style={styles.title}>{practice.title}</Text>
            <View style={styles.metadata}>
              <Text style={styles.duration}>{practice.duration} min</Text>
              <Text style={styles.separator}>•</Text>
              <Text style={styles.type}>{practice.type}</Text>
              {practice.layerFocus && (
                <>
                  <Text style={styles.separator}>•</Text>
                  <Text style={styles.layer}>{practice.layerFocus}</Text>
                </>
              )}
            </View>
          </View>

          {/* Completion Status */}
          <View style={styles.statusContainer}>
            {practice.completed ? (
              <View style={styles.completedBadge}>
                <Ionicons name="checkmark" size={16} color="#ffffff" />
              </View>
            ) : (
              <View style={styles.pendingBadge}>
                <Ionicons name="time" size={16} color="#4a90a4" />
              </View>
            )}
          </View>
        </View>

        {/* Practice Description */}
        {practice.description && (
          <Text style={styles.description} numberOfLines={2}>
            {practice.description}
          </Text>
        )}

        {/* Arabic Text Preview */}
        {practice.arabicText && (
          <View style={styles.arabicContainer}>
            <Text style={styles.arabicText} numberOfLines={1}>
              {practice.arabicText}
            </Text>
            {practice.transliteration && (
              <Text style={styles.transliteration} numberOfLines={1}>
                {practice.transliteration}
              </Text>
            )}
          </View>
        )}

        {/* Benefits Preview */}
        {practice.benefits && practice.benefits.length > 0 && (
          <View style={styles.benefitsContainer}>
            <Text style={styles.benefitsLabel}>Benefits:</Text>
            <Text style={styles.benefitsText} numberOfLines={1}>
              {practice.benefits.slice(0, 2).join(', ')}
              {practice.benefits.length > 2 && '...'}
            </Text>
          </View>
        )}

        {/* Action Indicator */}
        <View style={styles.footer}>
          <Text style={styles.actionText}>
            {practice.completed ? 'Review Practice' : 'Start Practice'}
          </Text>
          <Ionicons
            name="chevron-forward"
            size={16}
            color="rgba(255, 255, 255, 0.6)"
          />
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  title: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  metadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  duration: {
    color: '#4a90a4',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Poppins-Medium',
  },
  separator: {
    color: 'rgba(255, 255, 255, 0.4)',
    fontSize: 14,
    marginHorizontal: 6,
  },
  type: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    textTransform: 'capitalize',
  },
  layer: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    textTransform: 'capitalize',
  },
  statusContainer: {
    marginLeft: 8,
  },
  completedBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#38a169',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pendingBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(74, 144, 164, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  description: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
    fontFamily: 'Poppins-Regular',
  },
  arabicContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  arabicText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'right',
    marginBottom: 4,
    fontFamily: 'Amiri-Regular',
  },
  transliteration: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontStyle: 'italic',
    fontFamily: 'Poppins-Regular',
  },
  benefitsContainer: {
    marginBottom: 12,
  },
  benefitsLabel: {
    color: '#4a90a4',
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
    fontFamily: 'Poppins-Medium',
  },
  benefitsText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  actionText: {
    color: '#4a90a4',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Poppins-Medium',
  },
});
