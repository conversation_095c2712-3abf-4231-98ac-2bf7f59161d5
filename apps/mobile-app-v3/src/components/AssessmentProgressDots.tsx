import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../constants/Colors';

interface AssessmentProgressDotsProps {
  currentStep: string;
  completedSteps: string[];
  onJumpToStep: (stepId: string) => void;
}

const orderedSteps = [
  { id: 'physical_experiences', title: 'Physical' },
  { id: 'emotional_experiences', title: 'Emotional' },
  { id: 'mental_experiences', title: 'Mental' },
  { id: 'spiritual_experiences', title: 'Spiritual' },
  { id: 'reflections', title: 'Reflect' },
];

const AssessmentProgressDots: React.FC<AssessmentProgressDotsProps> = ({
  currentStep,
  completedSteps,
  onJumpToStep,
}) => {
  const currentIndex = orderedSteps.findIndex(step => step.id === currentStep);

  return (
    <View style={styles.container}>
      {orderedSteps.map((step, index) => {
        const isCurrent = step.id === currentStep;
        const isCompleted = completedSteps.includes(step.id);
        const isFuture = !isCurrent && !isCompleted && index > currentIndex;

        return (
          <React.Fragment key={step.id}>
            <TouchableOpacity
              style={styles.dotContainer}
              onPress={() => onJumpToStep(step.id)}
              disabled={isFuture} // Disable jumping to future steps
            >
              <View
                style={[
                  styles.dot,
                  isCurrent && styles.dotCurrent,
                  isCompleted && styles.dotCompleted,
                ]}
              >
                {isCompleted && !isCurrent && (
                  <Ionicons name="checkmark" size={14} color="white" />
                )}
              </View>
              <Text
                style={[
                  styles.dotText,
                  isCurrent && styles.dotTextCurrent,
                  isCompleted && styles.dotTextCompleted,
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {step.title}
              </Text>
            </TouchableOpacity>
            {index < orderedSteps.length - 1 && (
              <View style={styles.line} />
            )}
          </React.Fragment>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 10,
    marginBottom: 20,
    marginTop: 10,
  },
  dotContainer: {
    alignItems: 'center',
    maxWidth: 60, // Limit width to allow text to be readable but not overflow
  },
  dot: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.border, // Default for incomplete/future
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: 4,
  },
  dotCurrent: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
    borderWidth: 2,
  },
  dotCompleted: {
    backgroundColor: colors.primary, // Changed from primaryLight to primary
    borderColor: colors.primary, // Changed from primaryLight to primary
  },
  dotText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
    textAlign: 'center',
  },
  dotTextCurrent: {
    fontWeight: '600',
    color: colors.primary,
  },
  dotTextCompleted: {
    color: colors.text,
  },
  line: {
    flex: 1,
    height: 2,
    backgroundColor: colors.border,
    marginHorizontal: 4,
    marginTop: 11, // Align with the vertical center of the dot
  },
});

export default AssessmentProgressDots;
