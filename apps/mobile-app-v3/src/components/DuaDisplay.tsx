import React, { useEffect, useRef, useState } from 'react';

import { Animated, StyleSheet, TouchableOpacity } from 'react-native';

import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Card } from './ui/Card';
import { Text } from './ui/Text';
import { View } from './ui/View';

interface Dua {
  id?: string;
  arabic: string;
  transliteration: string;
  translation: string;
  category?: string;
  source?: string;
  reference?: string;
}

interface DuaDisplayProps {
  arabic?: string;
  transliteration?: string;
  translation?: string;
  reference?: string;
  onComplete?: () => void;
  duas?: Dua[];
}

export const DuaDisplay = ({
  arabic = '',
  transliteration = '',
  translation = '',
  reference = '',
  onComplete,
  duas = [],
}: DuaDisplayProps) => {
  // If duas array is provided, use the first dua or display all
  const displayDuas =
    duas.length > 0
      ? duas
      : [{ arabic, transliteration, translation, reference }];

  // Animation
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  // State
  const [isCompleted, setIsCompleted] = useState(false);

  // Fade in animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Handle completion
  const handleComplete = () => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    setIsCompleted(true);

    // Fade out and then call onComplete
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      onComplete();
    });
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <Card style={styles.card}>
        <LinearGradient
          colors={[colors.primary + '20', colors.accent + '20']}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />

        <View style={styles.iconContainer}>
          <Text variant="heading3" style={styles.title}>
            Healing Du&apos;a
          </Text>
        </View>

        <View style={styles.divider} />

        {displayDuas.map((dua, index) => (
          <View key={index} style={styles.duaContainer}>
            <View style={styles.arabicContainer}>
              <Text variant="heading2" style={styles.arabicText}>
                {dua.arabic}
              </Text>
            </View>

            <View style={styles.transliterationContainer}>
              <Text variant="subtitle" style={styles.transliterationText}>
                {dua.transliteration}
              </Text>
            </View>

            <View style={styles.translationContainer}>
              <Text
                variant="body"
                color="textSecondary"
                style={styles.translationText}
              >
                {dua.translation}
              </Text>
            </View>

            <View style={styles.referenceContainer}>
              <Text
                variant="caption"
                color="textSecondary"
                style={styles.referenceText}
              >
                Source: {dua.reference || dua.source || 'Unknown'}
              </Text>
            </View>

            {dua.category && (
              <View style={styles.categoryContainer}>
                <Text variant="caption" style={styles.categoryText}>
                  {dua.category}
                </Text>
              </View>
            )}

            {index < displayDuas.length - 1 && (
              <View style={styles.duaDivider} />
            )}
          </View>
        ))}

        <TouchableOpacity
          style={[
            styles.completeButton,
            {
              backgroundColor: isCompleted ? colors.success : colors.primary,
            },
          ]}
          onPress={handleComplete}
          disabled={isCompleted}
        >
          <Text
            variant="body"
            color="surface"
            style={styles.completeButtonText}
          >
            {isCompleted ? 'Completed' : 'Mark as Recited'}
          </Text>
          <Feather
            name={isCompleted ? 'check' : 'check-circle'}
            size={18}
            color="#fff"
            style={styles.completeButtonIcon}
          />
        </TouchableOpacity>
      </Card>

      <View style={styles.instructionContainer}>
        <Feather name="info" size={16} style={styles.infoIcon} />
        <Text
          variant="caption"
          color="textSecondary"
          style={styles.instructionText}
        >
          Recite the du&apos;a out loud or silently, focusing on its meaning.
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Theme.spacing.m,
  },
  card: {
    overflow: 'hidden',
    padding: Theme.spacing.l,
    position: 'relative',
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  title: {
    marginTop: Theme.spacing.xs,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginBottom: Theme.spacing.m,
  },
  arabicContainer: {
    marginBottom: Theme.spacing.m,
    alignItems: 'center',
  },
  arabicText: {
    textAlign: 'center',
    lineHeight: 40,
  },
  transliterationContainer: {
    marginBottom: Theme.spacing.m,
  },
  transliterationText: {
    textAlign: 'center',
    fontStyle: 'italic',
  },
  translationContainer: {
    marginBottom: Theme.spacing.m,
  },
  translationText: {
    textAlign: 'center',
  },
  referenceContainer: {
    marginBottom: Theme.spacing.m,
    alignItems: 'flex-end',
  },
  referenceText: {
    fontStyle: 'italic',
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Theme.spacing.s,
    paddingHorizontal: Theme.spacing.m,
    borderRadius: Theme.borderRadius.medium,
  },
  completeButtonText: {
    fontWeight: '500',
  },
  completeButtonIcon: {
    marginLeft: Theme.spacing.xs,
  },
  instructionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Theme.spacing.m,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: Theme.spacing.s,
    borderRadius: Theme.borderRadius.small,
  },
  infoIcon: {
    marginRight: Theme.spacing.xs,
  },
  instructionText: {
    flex: 1,
  },
  duaContainer: {
    marginBottom: Theme.spacing.m,
  },
  categoryContainer: {
    alignItems: 'flex-start',
    marginTop: Theme.spacing.s,
  },
  categoryText: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: Theme.spacing.s,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.small,
    color: colors.primary,
    fontWeight: '500',
  },
  duaDivider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginVertical: Theme.spacing.m,
  },
});
