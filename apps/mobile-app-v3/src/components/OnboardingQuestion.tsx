/**
 * Onboarding Question Component for Feature 0
 * Adaptive question rendering based on question type
 */

import React, { useState, useEffect } from 'react'; // Added useEffect
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface Option {
  id: string;
  text: string;
  icon?: string;
  description?: string;
}

interface Question {
  id: string;
  type:
    | 'single_choice'
    | 'multiple_choice'
    | 'adaptive_flow'
    | 'multi_section'
    | 'text_input';
  title: string;
  subtitle?: string;
  content?: string;
  options?: Option[];
  sections?: any[];
  followUps?: Record<string, any[]>;
  required?: boolean;
}

interface OnboardingQuestionProps {
  question: Question;
  onResponse: (response: any) => void;
  isLoading: boolean;
}

export const OnboardingQuestion: React.FC<OnboardingQuestionProps> = ({
  question,
  onResponse,
  isLoading,
}) => {
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [textInput, setTextInput] = useState<string>('');
  const [currentSection, setCurrentSection] = useState<number>(0);
  // sectionResponses will now be keyed by section.id (string) rather than index (number)
  const [sectionResponses, setSectionResponses] = useState<Record<string, any>>(
    {}
  );

  // Reset currentSection and sectionResponses when the question prop changes to a new question
  // or a different type, especially when navigating away from a multi_section question.
  useEffect(() => {
    setCurrentSection(0);
    setSectionResponses({});
    setSelectedOptions([]); // Also reset options for multiple_choice
    setTextInput('');     // And text input
  }, [question.id, question.type]);


  const handleSingleChoice = (optionId: string) => {
    if (isLoading) return;

    const response = {
      [question.id]: optionId,
      questionType: 'single_choice',
    };

    onResponse(response);
  };

  const handleMultipleChoice = () => {
    if (isLoading || selectedOptions.length === 0) return;

    const response = {
      [question.id]: selectedOptions,
      questionType: 'multiple_choice',
    };

    onResponse(response);
  };

  const toggleMultipleChoice = (optionId: string) => {
    setSelectedOptions((prev) => {
      if (prev.includes(optionId)) {
        return prev.filter((id) => id !== optionId);
      } else {
        return [...prev, optionId];
      }
    });
  };

  const handleTextInput = () => {
    if (isLoading || !textInput.trim()) return;

    const response = {
      [question.id]: textInput.trim(),
      questionType: 'text_input',
    };

    onResponse(response);
  };

  // handleMultiSection and handleSectionResponse are removed as their logic
  // will be integrated into the onPress handler within renderMultiSection.

  const renderSingleChoice = () => (
    <View style={styles.optionsContainer}>
      {question.options?.map((option, index) => (
        <TouchableOpacity
          key={option.id}
          style={[styles.optionButton, { marginTop: index > 0 ? 12 : 0 }]}
          onPress={() => handleSingleChoice(option.id)}
          disabled={isLoading}
        >
          <LinearGradient
            colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
            style={styles.optionGradient}
          >
            <View style={styles.optionContent}>
              {option.icon && (
                <Ionicons
                  name={option.icon as any}
                  size={24}
                  color="#ffffff"
                  style={styles.optionIcon}
                />
              )}
              <View style={styles.optionTextContainer}>
                <Text style={styles.optionText}>{option.text}</Text>
                {option.description && (
                  <Text style={styles.optionDescription}>
                    {option.description}
                  </Text>
                )}
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color="rgba(255, 255, 255, 0.6)"
              />
            </View>
          </LinearGradient>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderMultipleChoice = () => (
    <View style={styles.optionsContainer}>
      {question.options?.map((option, index) => {
        const isSelected = selectedOptions.includes(option.id);
        return (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.optionButton,
              { marginTop: index > 0 ? 12 : 0 },
              isSelected && styles.selectedOption,
            ]}
            onPress={() => toggleMultipleChoice(option.id)}
            disabled={isLoading}
          >
            <LinearGradient
              colors={
                isSelected
                  ? ['rgba(74, 144, 164, 0.3)', 'rgba(74, 144, 164, 0.1)']
                  : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
              }
              style={styles.optionGradient}
            >
              <View style={styles.optionContent}>
                <View
                  style={[styles.checkbox, isSelected && styles.checkedBox]}
                >
                  {isSelected && (
                    <Ionicons name="checkmark" size={16} color="#ffffff" />
                  )}
                </View>
                <View style={styles.optionTextContainer}>
                  <Text style={styles.optionText}>{option.text}</Text>
                  {option.description && (
                    <Text style={styles.optionDescription}>
                      {option.description}
                    </Text>
                  )}
                </View>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        );
      })}

      {selectedOptions.length > 0 && (
        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleMultipleChoice}
          disabled={isLoading}
        >
          <LinearGradient
            colors={['#4a90a4', '#2d5a87']}
            style={styles.continueGradient}
          >
            <Text style={styles.continueText}>Continue</Text>
            <Ionicons name="arrow-forward" size={20} color="#ffffff" />
          </LinearGradient>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderTextInput = () => (
    <View style={styles.textInputContainer}>
      <TextInput
        style={styles.textInput}
        value={textInput}
        onChangeText={setTextInput}
        placeholder="Share your thoughts..."
        placeholderTextColor="rgba(255, 255, 255, 0.5)"
        multiline
        numberOfLines={4}
        textAlignVertical="top"
      />

      {textInput.trim().length > 0 && (
        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleTextInput}
          disabled={isLoading}
        >
          <LinearGradient
            colors={['#4a90a4', '#2d5a87']}
            style={styles.continueGradient}
          >
            <Text style={styles.continueText}>Continue</Text>
            <Ionicons name="arrow-forward" size={20} color="#ffffff" />
          </LinearGradient>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderMultiSection = () => {
    if (!question.sections) return null;

    const section = question.sections[currentSection];
    if (!section) return null;

    return (
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>{section.question}</Text>

        {/* Render section based on type */}
        {section.type === 'single_choice' && (
          <View style={styles.optionsContainer}>
            {section.options?.map((option: Option, index: number) => (
              <TouchableOpacity
                key={option.id}
                style={[styles.optionButton, { marginTop: index > 0 ? 12 : 0 }]}
                onPress={() => {
                  if (isLoading) return;
                  const currentSectionData = question.sections![currentSection]; // Not null due to checks above
                  const updatedSectionResponses = {
                    ...sectionResponses,
                    [currentSectionData.id]: option.id, // Use section.id as key
                  };
                  setSectionResponses(updatedSectionResponses);

                  if (currentSection < question.sections!.length - 1) {
                    setCurrentSection(prev => prev + 1);
                  } else {
                    // Last section answered, submit the collected responses
                    const finalResponse = {
                      [question.id]: updatedSectionResponses,
                      questionType: 'multi_section',
                    };
                    onResponse(finalResponse);
                    // Reset for potential reuse if this component instance handles another multi_section
                    // setCurrentSection(0);
                    // setSectionResponses({});
                    // Resetting is now handled by useEffect watching question.id and question.type
                  }
                }}
                disabled={isLoading}
              >
                <LinearGradient
                  colors={[
                    'rgba(255, 255, 255, 0.1)',
                    'rgba(255, 255, 255, 0.05)',
                  ]}
                  style={styles.optionGradient}
                >
                  <View style={styles.optionContent}>
                    <Text style={styles.optionText}>{option.text}</Text>
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color="rgba(255, 255, 255, 0.6)"
                    />
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Section progress indicator */}
        <View style={styles.sectionProgress}>
          <Text style={styles.sectionProgressText}>
            {currentSection + 1} of {question.sections.length}
          </Text>
        </View>
      </View>
    );
  };

  const renderContent = () => {
    switch (question.type) {
      case 'single_choice':
        return renderSingleChoice();
      case 'multiple_choice':
        return renderMultipleChoice();
      case 'text_input':
        return renderTextInput();
      case 'multi_section':
        return renderMultiSection();
      case 'adaptive_flow':
        return renderSingleChoice(); // Same as single choice for now
      default:
        return renderSingleChoice();
    }
  };

  return (
    <View style={styles.container}>
      {/* Question Header */}
      <View style={styles.questionHeader}>
        <Text style={styles.questionTitle}>{question.title}</Text>
        {question.subtitle && (
          <Text style={styles.questionSubtitle}>{question.subtitle}</Text>
        )}
        {question.content && (
          <Text style={styles.questionContent}>{question.content}</Text>
        )}
      </View>

      {/* Question Content */}
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderContent()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  questionHeader: {
    marginBottom: 32,
  },
  questionTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  questionSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'Poppins-Regular',
  },
  questionContent: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: 'Poppins-Regular',
  },
  scrollContainer: {
    flex: 1,
  },
  optionsContainer: {
    flex: 1,
  },
  optionButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  selectedOption: {
    borderWidth: 2,
    borderColor: '#4a90a4',
  },
  optionGradient: {
    padding: 16,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    marginRight: 12,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionText: {
    fontSize: 16,
    color: '#ffffff',
    fontFamily: 'Poppins-Medium',
  },
  optionDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 4,
    fontFamily: 'Poppins-Regular',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedBox: {
    backgroundColor: '#4a90a4',
    borderColor: '#4a90a4',
  },
  continueButton: {
    marginTop: 24,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  continueText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginRight: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  textInputContainer: {
    flex: 1,
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    color: '#ffffff',
    fontSize: 16,
    minHeight: 120,
    fontFamily: 'Poppins-Regular',
  },
  sectionContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 24,
    textAlign: 'center',
    fontFamily: 'Poppins-SemiBold',
  },
  sectionProgress: {
    alignItems: 'center',
    marginTop: 24,
  },
  sectionProgressText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    fontFamily: 'Poppins-Regular',
  },
});

export default OnboardingQuestion;
