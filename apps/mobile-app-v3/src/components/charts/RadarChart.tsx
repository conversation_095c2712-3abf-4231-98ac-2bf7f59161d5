import React, { useState } from 'react';
import { View, StyleSheet, LayoutChangeEvent, Text } from 'react-native';
import { colors, ColorTheme } from '../../constants/Colors';
import Theme from '../../constants/Theme';

interface RadarChartData {
  value: number;
  label: string;
}

interface RadarChartProps {
  data: RadarChartData[];
}

const CustomRadarChart = ({ data }: RadarChartProps) => {
  const styles = createStyles(colors);
  const [chartDimensions, setChartDimensions] = useState<{ width: number; height: number } | null>(null);

  const onLayout = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    if (width > 0 && height > 0) {
      setChartDimensions({ width, height });
    }
  };

  const getMaxValue = () => {
    if (!data || data.length === 0) {
      return 50;
    }
    const maxDataValue = Math.max(...data.map(item => item.value));
    return Math.max(50, Math.ceil(maxDataValue / 10) * 10);
  };

  // Enhanced circular progress visualization
  const renderEnhancedVisualization = () => {
    const layerColors = {
      'Jism': colors.jism,
      'Nafs': colors.nafs, 
      'Aql': colors.aql,
      'Qalb': colors.qalb,
      'Ruh': colors.ruh
    };

    return (
      <View style={styles.enhancedContainer}>
        <Text style={styles.enhancedTitle}>Five-Layer Spiritual Analysis</Text>
        <View style={styles.circularGrid}>
          {data.map((item, index) => {
            const percentage = (item.value / getMaxValue()) * 100;
            const color = layerColors[item.label as keyof typeof layerColors] || colors.primary;
            
            return (
              <View key={index} style={styles.circularItem}>
                <View style={styles.circularProgressContainer}>
                  <View style={[styles.circularProgressBg, { borderColor: color + '30' }]}>
                    <View 
                      style={[
                        styles.circularProgress,
                        { 
                          borderColor: color,
                          transform: [{ rotate: `${(percentage / 100) * 360}deg` }]
                        }
                      ]} 
                    />
                    <View style={styles.circularCenter}>
                      <Text style={[styles.circularValue, { color }]}>{item.value}</Text>
                    </View>
                  </View>
                </View>
                <Text style={styles.circularLabel}>{item.label}</Text>
                <View style={[styles.layerIndicator, { backgroundColor: color }]} />
              </View>
            );
          })}
        </View>
        <Text style={styles.enhancedDescription}>
          Each circle represents the impact level of that spiritual layer
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container} onLayout={onLayout}>
      {chartDimensions ? (
        // Render enhanced visualization
        <View style={styles.chartWrapper}>
          {renderEnhancedVisualization()}
        </View>
      ) : (
        // Render a placeholder until dimensions are known
        <View style={styles.placeholder}>
          <Text style={styles.placeholderText}>Loading chart...</Text>
        </View>
      )}
    </View>
  );
};

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      justifyContent: 'center',
      alignItems: 'center',
      padding: 16,
      backgroundColor: colors.cardBackground,
      borderRadius: Theme.borderRadius.large,
      ...Theme.shadows.medium,
      minHeight: 300,
    },
    chartWrapper: {
      width: '100%',
      height: 250,
    },
    placeholder: {
      height: 250,
      justifyContent: 'center',
      alignItems: 'center',
    },
    placeholderText: {
      color: colors.textSecondary,
      fontSize: Theme.typography.fontSize.s,
    },
    fallbackContainer: {
      width: '100%',
      padding: 16,
    },
    fallbackTitle: {
      fontSize: Theme.typography.fontSize.l,
      fontWeight: 'bold',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 20,
    },
    fallbackItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    fallbackLabel: {
      fontSize: Theme.typography.fontSize.s,
      color: colors.text,
      width: 60,
      fontWeight: '600',
    },
    fallbackBarContainer: {
      flex: 1,
      height: 20,
      backgroundColor: colors.border,
      borderRadius: 10,
      marginHorizontal: 12,
      overflow: 'hidden',
    },
    fallbackBar: {
      height: '100%',
      backgroundColor: colors.primary,
      borderRadius: 10,
    },
    fallbackValue: {
      fontSize: Theme.typography.fontSize.s,
      color: colors.textSecondary,
      width: 30,
      textAlign: 'right',
    },
    label: {
      color: colors.textSecondary,
      fontSize: Theme.typography.fontSize.xs,
    },
    // Enhanced visualization styles
    enhancedContainer: {
      width: '100%',
      alignItems: 'center',
    },
    enhancedTitle: {
      fontSize: Theme.typography.fontSize.l,
      fontWeight: 'bold',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 20,
    },
    circularGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
      alignItems: 'center',
      gap: 16,
    },
    circularItem: {
      alignItems: 'center',
      width: 80,
    },
    circularProgressContainer: {
      marginBottom: 8,
    },
    circularProgressBg: {
      width: 60,
      height: 60,
      borderRadius: 30,
      borderWidth: 3,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    circularProgress: {
      position: 'absolute',
      width: 54,
      height: 54,
      borderRadius: 27,
      borderWidth: 3,
      borderTopColor: 'transparent',
      borderRightColor: 'transparent',
      borderBottomColor: 'transparent',
    },
    circularCenter: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: 'white',
      justifyContent: 'center',
      alignItems: 'center',
    },
    circularValue: {
      fontSize: 14,
      fontWeight: 'bold',
    },
    circularLabel: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 4,
    },
    layerIndicator: {
      width: 20,
      height: 3,
      borderRadius: 2,
    },
    enhancedDescription: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 16,
      lineHeight: 16,
    },
  });

export default CustomRadarChart;