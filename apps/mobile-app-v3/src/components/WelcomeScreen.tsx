/**
 * Welcome Screen Component for Onboarding
 * Displays the initial welcome message and introduction
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

interface WelcomeScreenProps {
  question: any;
  onResponse: (response: any) => void;
  isLoading: boolean;
}

const { width } = Dimensions.get('window');

export function WelcomeScreen({ question, onResponse, isLoading }: WelcomeScreenProps) {
  const handleContinue = () => {
    onResponse({ action: 'continue', timestamp: new Date().toISOString() });
  };

  return (
    <View style={styles.container}>
      {/* Islamic Greeting */}
      <View style={styles.greetingContainer}>
        <Text style={styles.arabicGreeting}>السلام عليكم ورحمة ال��ه وبركاته</Text>
        <Text style={styles.transliteration}>As<PERSON><PERSON><PERSON> wa <PERSON> wa Bar<PERSON>tuh</Text>
        <Text style={styles.translation}>Peace be upon you and Allah's mercy and blessings</Text>
      </View>

      {/* Welcome Content */}
      <View style={styles.contentContainer}>
        <View style={styles.iconContainer}>
          <Ionicons name="heart" size={60} color="#4a90a4" />
        </View>

        <Text style={styles.title}>Welcome to Qalb Healing</Text>
        <Text style={styles.subtitle}>Your Journey to Inner Peace</Text>

        <Text style={styles.description}>
          {question?.content || 
            "We're here to support you on your path to spiritual and emotional wellness through the beautiful guidance of Islam."
          }
        </Text>

        <View style={styles.featuresContainer}>
          <FeatureItem 
            icon="shield-checkmark"
            text="Safe & Confidential"
          />
          <FeatureItem 
            icon="book"
            text="Islamic Guidance"
          />
          <FeatureItem 
            icon="people"
            text="Community Support"
          />
        </View>

        <Text style={styles.timeEstimate}>
          This will take about 5-10 minutes
        </Text>
      </View>

      {/* Continue Button */}
      <TouchableOpacity
        style={[styles.continueButton, isLoading && styles.disabledButton]}
        onPress={handleContinue}
        disabled={isLoading}
      >
        <LinearGradient
          colors={['#4a90a4', '#2d5a87']}
          style={styles.buttonGradient}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Loading...' : 'Begin Journey'}
          </Text>
          <Ionicons name="arrow-forward" size={20} color="#ffffff" />
        </LinearGradient>
      </TouchableOpacity>

      {/* Islamic Quote */}
      <View style={styles.quoteContainer}>
        <Text style={styles.quoteArabic}>
          وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ
        </Text>
        <Text style={styles.quoteTranslation}>
          "And whoever relies upon Allah - then He is sufficient for him"
        </Text>
        <Text style={styles.quoteReference}>- Quran 65:3</Text>
      </View>
    </View>
  );
}

interface FeatureItemProps {
  icon: string;
  text: string;
}

function FeatureItem({ icon, text }: FeatureItemProps) {
  return (
    <View style={styles.featureItem}>
      <Ionicons name={icon as any} size={24} color="#4a90a4" />
      <Text style={styles.featureText}>{text}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 20,
  },
  greetingContainer: {
    alignItems: 'center',
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  arabicGreeting: {
    fontSize: 20,
    fontFamily: 'Amiri-Regular',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
  },
  transliteration: {
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    color: '#e2e8f0',
    textAlign: 'center',
    marginBottom: 4,
  },
  translation: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#cbd5e0',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Poppins-Medium',
    color: '#e2e8f0',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Poppins-Regular',
    color: '#f7fafc',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 30,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    fontFamily: 'Poppins-Medium',
    color: '#ffffff',
    marginLeft: 16,
  },
  timeEstimate: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#cbd5e0',
    textAlign: 'center',
  },
  continueButton: {
    marginHorizontal: 20,
    marginVertical: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  disabledButton: {
    opacity: 0.6,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  buttonText: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#ffffff',
    marginRight: 8,
  },
  quoteContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  quoteArabic: {
    fontSize: 18,
    fontFamily: 'Amiri-Regular',
    color: '#e2e8f0',
    textAlign: 'center',
    marginBottom: 8,
  },
  quoteTranslation: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#cbd5e0',
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  quoteReference: {
    fontSize: 12,
    fontFamily: 'Poppins-Medium',
    color: '#a0aec0',
    textAlign: 'center',
  },
});