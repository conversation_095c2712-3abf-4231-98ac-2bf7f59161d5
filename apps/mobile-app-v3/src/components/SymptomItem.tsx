import React from 'react';

import { StyleSheet, TouchableOpacity } from 'react-native';

import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Symptom } from '../services/api/types';

import { Text } from './ui/Text';
import { View } from './ui/View';

interface SymptomItemProps {
  symptom: Symptom;
  selected: boolean;
  onPress: (symptom: Symptom) => void;
}

export const SymptomItem = ({
  symptom,
  selected,
  onPress,
}: SymptomItemProps) => {
  // Get color based on category
  const getCategoryColor = () => {
    switch (symptom.category) {
      case 'jism':
        return colors.error;
      case 'nafs':
        return colors.secondary;
      case 'aql':
        return colors.aqlYellow;
      case 'qalb':
        return colors.primary;
      case 'ruh':
        return colors.accent;
      default:
        return colors.primary;
    }
  };

  const categoryColor = getCategoryColor();

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onPress(symptom);
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: selected ? categoryColor + '20' : 'transparent',
          borderColor: selected ? categoryColor : colors.border,
        },
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: categoryColor + '20' },
        ]}
      >
        <Feather name={symptom.iconName || 'circle'} size={22} />
      </View>
      <Text variant="body" style={styles.name}>
        {symptom.name}
      </Text>
      {selected && (
        <Feather name="check-circle" size={20} style={styles.checkIcon} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Theme.spacing.s,
    borderRadius: Theme.borderRadius.medium,
    borderWidth: 1,
    marginBottom: Theme.spacing.s,
    minHeight: 60,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.m,
  },
  name: {
    flex: 1,
  },
  checkIcon: {
    marginLeft: Theme.spacing.s,
  },
});
