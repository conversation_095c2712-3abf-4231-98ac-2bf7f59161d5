import React from 'react';
import { ImageBackground, StyleSheet, TouchableOpacity } from 'react-native'; // Removed unused Image import

import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { Journey, JourneyProgress } from '../services/api/types';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { HealingJourney } from '../types';

import { SoulLayerBadge } from './SoulLayerBadge';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { Card } from './ui/Card';
import { ProgressBar } from './ui/ProgressBar';
import { Text } from './ui/Text';
import { View } from './ui/View';

// Combined the two interface declarations
interface JourneyCardProps {
  journey: Journey | HealingJourney;
  progress?: JourneyProgress;
  onPress: (journey: Journey) => void;
}

// Single export of JourneyCard
export const JourneyCard = ({
  journey,
  progress,
  onPress,
}: JourneyCardProps) => {
  // For HealingJourney type (second implementation)
  if ('progress' in journey && typeof journey.progress === 'number') {
    return (
      <Card
        style={styles.card}
        title={journey.title}
        subtitle={`${journey.duration} days journey`}
        footer={
          <Button
            title={journey.progress > 0 ? 'Continue' : 'Start Journey'}
            onPress={() => onPress(journey as Journey)}
            variant="primary"
            icon={journey.progress > 0 ? 'arrow-right' : 'play'}
          />
        }
      >
        <View style={styles.content}>
          <Text variant="body" style={styles.description}>
            {journey.description}
          </Text>

          <View style={styles.badgesContainer}>
            {journey.targetSoulLayers?.map((layer) => (
              <SoulLayerBadge key={layer} layer={layer} size="small" />
            ))}
          </View>

          <View style={styles.progressContainer}>
            <ProgressBar
              progress={journey.progress}
              showLabel
              label="Journey Progress"
            />
            <Text
              variant="caption"
              color="textSecondary"
              style={styles.activitiesCount}
            >
              {journey.activities?.filter((a) => a.completed).length || 0} of{' '}
              {journey.activities?.length || 0} activities completed
            </Text>
          </View>
        </View>
      </Card>
    );
  }

  // Original implementation for Journey type
  const getCategoryColor = () => {
    switch (journey.category) {
      case 'jism':
        return colors.error;
      case 'nafs':
        return colors.secondary;
      case 'aql':
        return colors.aqlYellow;
      case 'qalb':
        return colors.primary;
      case 'ruh':
        return colors.accent;
      case 'all':
        return colors.primary;
      default:
        return colors.primary;
    }
  };

  const categoryColor = getCategoryColor();

  const getCategoryIcon = () => {
    switch (journey.category) {
      case 'jism':
        return 'activity';
      case 'nafs':
        return 'wind';
      case 'aql':
        return 'cpu';
      case 'qalb':
        return 'heart';
      case 'ruh':
        return 'star';
      case 'all':
        return 'layers';
      default:
        return 'help-circle';
    }
  };

  const categoryIcon = getCategoryIcon();
  const isInProgress = progress && progress.status === 'in_progress';
  const isCompleted = progress && progress.status === 'completed';

  return (
    <Card style={styles.card} onPress={() => onPress(journey as Journey)}>
      <View style={styles.content}>
        <ImageBackground
          source={{
            uri: journey.imageUrl || 'https://via.placeholder.com/400x200',
          }}
          style={styles.imageBackground}
          imageStyle={styles.image}
        >
          <LinearGradient
            colors={['rgba(0,0,0,0.1)', 'rgba(0,0,0,0.7)']}
            style={styles.gradient}
          />
          <View style={styles.imageOverlay}>
            <Badge
              label={`${journey.duration} Days`}
              style={styles.durationBadge}
            />

            {isInProgress && (
              <Badge label="In Progress" style={styles.statusBadge} />
            )}

            {isCompleted && (
              <Badge label="Completed" style={styles.statusBadge} />
            )}
          </View>
        </ImageBackground>

        <View style={styles.details}>
          <View style={styles.titleRow}>
            <Feather
              name={categoryIcon}
              size={18}
              style={styles.categoryIcon}
            />
            <Text variant="title" style={styles.title} numberOfLines={1}>
              {journey.title}
            </Text>
          </View>

          <Text
            variant="body"
            color="textSecondary"
            style={styles.description}
            numberOfLines={2}
          >
            {journey.description}
          </Text>

          <View style={styles.metaInfo}>
            <View style={styles.metaItem}>
              <Feather name="clock" size={14} style={styles.metaIcon} />
              <Text variant="caption" color="textSecondary">
                {journey.dailyCommitmentMinutes} min/day
              </Text>
            </View>

            <View style={styles.metaItem}>
              <Feather name="bar-chart-2" size={14} style={styles.metaIcon} />
              <Text variant="caption" color="textSecondary">
                {journey.level}
              </Text>
            </View>
          </View>

          {progress && (
            <View style={styles.progressContainer}>
              <View style={styles.progressTextRow}>
                <Text variant="caption">
                  {isCompleted
                    ? 'Completed'
                    : `Day ${progress.currentDay} of ${progress.totalDays}`}
                </Text>
                <Text variant="caption" color="textSecondary">
                  {progress.percentage}%
                </Text>
              </View>
              <ProgressBar progress={progress.percentage / 100} />
            </View>
          )}

          {!progress && (
            <TouchableOpacity
              style={[styles.startButton, { backgroundColor: categoryColor }]}
              onPress={() => onPress(journey as Journey)}
            >
              <Text
                variant="body"
                color="surface"
                style={styles.startButtonText}
              >
                Start Journey
              </Text>
              <Feather name="arrow-right" size={16} color="#fff" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Card>
  );
};

// Combined styles
const styles = StyleSheet.create({
  card: {
    marginBottom: Theme.spacing.m,
    overflow: 'hidden',
    borderRadius: Theme.borderRadius.medium,
  },
  content: {
    padding: 0,
    gap: 12,
  },
  imageBackground: {
    height: 150,
    width: '100%',
    justifyContent: 'flex-end',
  },
  image: {
    borderTopLeftRadius: Theme.borderRadius.medium,
    borderTopRightRadius: Theme.borderRadius.medium,
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: 150,
    borderTopLeftRadius: Theme.borderRadius.medium,
    borderTopRightRadius: Theme.borderRadius.medium,
  },
  imageOverlay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Theme.spacing.s,
  },
  details: {
    padding: Theme.spacing.m,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.xs,
  },
  categoryIcon: {
    marginRight: Theme.spacing.xs,
  },
  title: {
    flex: 1,
  },
  description: {
    marginBottom: Theme.spacing.s,
  },
  durationBadge: {
    marginRight: Theme.spacing.xs,
  },
  statusBadge: {},
  metaInfo: {
    flexDirection: 'row',
    marginBottom: Theme.spacing.s,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Theme.spacing.m,
  },
  metaIcon: {
    marginRight: Theme.spacing.xs,
  },
  progressContainer: {
    marginTop: Theme.spacing.xs,
  },
  progressTextRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Theme.spacing.xs,
  },
  progressBar: {
    height: 6,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Theme.spacing.s,
    paddingHorizontal: Theme.spacing.m,
    borderRadius: Theme.borderRadius.small,
    marginTop: Theme.spacing.s,
  },
  startButtonText: {
    marginRight: Theme.spacing.xs,
    fontWeight: '500',
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  activitiesCount: {
    marginTop: 4,
    textAlign: 'right',
  },
});
