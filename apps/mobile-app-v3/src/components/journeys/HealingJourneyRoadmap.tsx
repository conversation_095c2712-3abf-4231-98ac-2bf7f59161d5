import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { colors, ColorTheme } from '../../constants/Colors';
import { IslamicIcons, IslamicIconName } from '../../constants/IslamicIcons';

interface Milestone {
  title: string;
  icon: IslamicIconName;
  status: 'completed' | 'current' | 'locked';
}

interface HealingJourneyRoadmapProps {
  milestones: Milestone[];
}

const HealingJourneyRoadmap = ({ milestones }: HealingJourneyRoadmapProps) => {
  const styles = createStyles(colors);

  const renderItem = ({ item, index }: { item: Milestone; index: number }) => (
    <View style={styles.milestoneContainer}>
      <View style={styles.iconContainer}>
        <MaterialCommunityIcons
          name={IslamicIcons[item.icon] as any}
          size={32}
          color={
            item.status === 'completed'
              ? colors.primary
              : item.status === 'current'
              ? colors.secondary
              : colors.textDisabled
          }
        />
      </View>
      <Text style={styles.milestoneTitle}>{item.title}</Text>
      {index < milestones.length - 1 && <View style={styles.connector} />}
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={milestones}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      paddingVertical: 16,
    },
    milestoneContainer: {
      alignItems: 'center',
      width: 100,
    },
    iconContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: colors.cardBackground,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
      borderWidth: 2,
      borderColor: colors.border,
    },
    milestoneTitle: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    connector: {
      position: 'absolute',
      top: 30,
      left: '50%',
      width: '100%',
      height: 2,
      backgroundColor: colors.border,
      zIndex: -1,
    },
  });

export default HealingJourneyRoadmap;
