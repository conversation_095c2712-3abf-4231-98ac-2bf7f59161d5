import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, ColorTheme } from '../../constants/Colors';

interface IntensitySliderProps {
  intensity: number;
  onIntensityChange: (value: number) => void;
  color: string;
}

const IntensitySlider = ({ intensity, onIntensityChange, color }: IntensitySliderProps) => {
  const styles = createStyles(colors);

  const getTrackColor = (value: number) => {
    if (value <= 4) return colors.mild;
    if (value <= 7) return colors.moderate;
    return colors.severe;
  };

  const gradientColors = [colors.mild, colors.moderate, colors.severe];

  // Create intensity buttons for 1-10 scale
  const renderIntensityButtons = () => {
    const buttons = [];
    for (let i = 1; i <= 10; i++) {
      const isSelected = i === intensity;
      const buttonColor = getTrackColor(i);
      
      buttons.push(
        <TouchableOpacity
          key={i}
          style={[
            styles.intensityButton,
            isSelected && { backgroundColor: buttonColor },
            !isSelected && { borderColor: buttonColor }
          ]}
          onPress={() => onIntensityChange(i)}
        >
          <Text style={[
            styles.intensityButtonText,
            isSelected && { color: 'white' },
            !isSelected && { color: buttonColor }
          ]}>
            {i}
          </Text>
        </TouchableOpacity>
      );
    }
    return buttons;
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientTrack}
      />
      
      <View style={styles.buttonsContainer}>
        {renderIntensityButtons()}
      </View>
      
      <View style={styles.labelsContainer}>
        <Text style={styles.label}>Mild (1-4)</Text>
        <Text style={styles.label}>Moderate (5-7)</Text>
        <Text style={styles.label}>Severe (8-10)</Text>
      </View>
      
      <Text style={[styles.intensityValue, { color: getTrackColor(intensity) }]}>
        Intensity: {intensity}/10
      </Text>
    </View>
  );
};

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      width: '100%',
      alignItems: 'center',
    },
    gradientTrack: {
      height: 8,
      borderRadius: 4,
      width: '100%',
      marginBottom: 16,
    },
    buttonsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
      marginBottom: 16,
    },
    intensityButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      borderWidth: 2,
      justifyContent: 'center',
      alignItems: 'center',
      margin: 4,
      backgroundColor: 'white',
    },
    intensityButtonText: {
      fontSize: 14,
      fontWeight: 'bold',
    },
    labelsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      marginBottom: 8,
    },
    label: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    intensityValue: {
      fontSize: 16,
      fontWeight: 'bold',
    },
  });

export default IntensitySlider;