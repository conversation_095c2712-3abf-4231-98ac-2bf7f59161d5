import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import serviceRegistry from '../../services/ServiceRegistry';
import { mockSymptomsService } from '../../services/mockSymptomsService';
import CategoryCard, { CATEGORIES } from './CategoryCard';
import DurationSelector from './DurationSelector';
import { colors } from '../../constants/Colors';
import QuestionCard from '../ui/QuestionCard';
import { SymptomSubmission } from '../../services/api/types';

interface SymptomSelectorContentProps {
  /**
   * Called when submission is complete
   */
  onSubmit: (submission: SymptomSubmission, diagnosis: any) => void;

  /**
   * Called when the user cancels
   */
  onCancel?: () => void;

  /**
   * Initial symptoms (for editing existing submission)
   */
  initialSymptoms?: Partial<SymptomSubmission>;
}

/**
 * A component for selecting symptoms across the five categories
 */
export default function SymptomSelectorContent({
  onSubmit,
  onCancel,
  initialSymptoms,
}: SymptomSelectorContentProps) {
  const styles = createStyles(colors);

  // State for selected symptoms
  const [selectedSymptoms, setSelectedSymptoms] = useState<{
    jism: string[];
    nafs: string[];
    aql: string[];
    qalb: string[];
    ruh: string[];
  }>({
    jism: initialSymptoms?.jism || [],
    nafs: initialSymptoms?.nafs || [],
    aql: initialSymptoms?.aql || [],
    qalb: initialSymptoms?.qalb || [],
    ruh: initialSymptoms?.ruh || [],
  });

  // State for intensity ratings
  const [intensity, setIntensity] = useState<{
    jism: number;
    nafs: number;
    aql: number;
    qalb: number;
    ruh: number;
  }>({
    jism: initialSymptoms?.intensity?.jism || 5,
    nafs: initialSymptoms?.intensity?.nafs || 5,
    aql: initialSymptoms?.intensity?.aql || 5,
    qalb: initialSymptoms?.intensity?.qalb || 5,
    ruh: initialSymptoms?.intensity?.ruh || 5,
  });

  // State for duration
  const [duration, setDuration] = useState<string>(
    initialSymptoms?.duration || '1-4_weeks'
  );

  // State for available symptoms
  const [availableSymptoms, setAvailableSymptoms] = useState<{
    jism: string[];
    nafs: string[];
    aql: string[];
    qalb: string[];
    ruh: string[];
  }>({
    jism: [],
    nafs: [],
    aql: [],
    qalb: [],
    ruh: [],
  });

  // Loading and error states
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // Custom symptom modal state
  const [customSymptomModal, setCustomSymptomModal] = useState<{
    visible: boolean;
    category: keyof typeof CATEGORIES | null;
    value: string;
  }>({
    visible: false,
    category: null,
    value: '',
  });

  // Fetch available symptoms on mount
  useEffect(() => {
    const fetchSymptoms = async () => {
      try {
        setLoading(true);
        setError(null);

        let categories;
        try {
          // Try to use the real service first
          const symptomsService = await serviceRegistry.getSymptoms();
          categories = await symptomsService.getSymptomCategories();
        } catch (serviceError) {
          console.warn('Real service failed, using mock data:', serviceError);
          // Fallback to mock service
          categories = await mockSymptomsService.getSymptomCategories();
        }

        // Convert categories array to object format
        const categoriesObj = Array.isArray(categories)
          ? categories.reduce((acc: any, cat: any) => {
              acc[cat.id] = cat.symptoms || [];
              return acc;
            }, {})
          : categories;

        setAvailableSymptoms({
          jism: categoriesObj.jism || [],
          nafs: categoriesObj.nafs || [],
          aql: categoriesObj.aql || [],
          qalb: categoriesObj.qalb || [],
          ruh: categoriesObj.ruh || [],
        });
      } catch (err) {
        console.error('Failed to fetch symptoms:', err);
        setError('Failed to load symptom categories. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchSymptoms();
  }, []);

  // Handle symptom selection/deselection
  const handleSelectSymptom = (
    category: keyof typeof CATEGORIES,
    symptom: string
  ) => {
    setSelectedSymptoms((prev) => {
      const categorySymptoms = [...prev[category]];

      if (categorySymptoms.includes(symptom)) {
        // Remove symptom if already selected
        return {
          ...prev,
          [category]: categorySymptoms.filter((s) => s !== symptom),
        };
      } else {
        // Add symptom if not already selected
        return {
          ...prev,
          [category]: [...categorySymptoms, symptom],
        };
      }
    });
  };

  // Handle intensity change
  const handleIntensityChange = (
    category: keyof typeof CATEGORIES,
    value: number
  ) => {
    setIntensity((prev) => ({
      ...prev,
      [category]: value,
    }));
  };

  // Open custom symptom modal
  const handleAddCustomSymptom = (category: keyof typeof CATEGORIES) => {
    setCustomSymptomModal({
      visible: true,
      category,
      value: '',
    });
  };

  // Add custom symptom
  const addCustomSymptom = () => {
    const { category, value } = customSymptomModal;

    if (category && value.trim()) {
      // Add to selected symptoms
      handleSelectSymptom(category, value.trim());

      // Close modal
      setCustomSymptomModal({
        visible: false,
        category: null,
        value: '',
      });
    }
  };

  // Cancel custom symptom
  const cancelCustomSymptom = () => {
    setCustomSymptomModal({
      visible: false,
      category: null,
      value: '',
    });
  };

  // Check if any symptoms are selected
  const hasSelectedSymptoms = () => {
    return Object.values(selectedSymptoms).some(
      (symptoms) => symptoms.length > 0
    );
  };

  // Handle submission
  const handleSubmit = async () => {
    if (!hasSelectedSymptoms()) {
      setError('Please select at least one symptom');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Prepare submission data
      const submission: SymptomSubmission = {
        jism: selectedSymptoms.jism,
        nafs: selectedSymptoms.nafs,
        aql: selectedSymptoms.aql,
        qalb: selectedSymptoms.qalb,
        ruh: selectedSymptoms.ruh,
        intensity: {
          jism: intensity.jism,
          nafs: intensity.nafs,
          aql: intensity.aql,
          qalb: intensity.qalb,
          ruh: intensity.ruh,
        },
        duration,
      };

      // Submit to API
      let response;
      try {
        const symptomsService = await serviceRegistry.getSymptoms();
        response = await symptomsService.submitSymptoms(submission);
      } catch (serviceError) {
        console.warn('Real service failed for submission, using mock:', serviceError);
        // Fallback to mock service
        response = await mockSymptomsService.submitSymptoms(submission);
      }

      // Call onSubmit with response
      onSubmit(submission, response.diagnosis);
    } catch (err) {
      console.error('Failed to submit symptoms:', err);
      setError('Failed to submit symptoms. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Get total selected symptoms count
  const getSelectedSymptomsCount = () => {
    return Object.values(selectedSymptoms).reduce(
      (total, symptoms) => total + symptoms.length,
      0
    );
  };

  // Handle duration selection
  const handleSelectDuration = (value: string) => {
    setDuration(value);
  };

  // Render loading state
  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading symptom categories...</Text>
      </View>
    );
  }

  // Render error state
  if (error && !loading && !submitting) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            // Re-fetch symptoms
            serviceRegistry
              .getSymptoms()
              .then((service) => service.getSymptomCategories())
              .then((categories) => {
                // Convert categories array to object format
                const categoriesObj = Array.isArray(categories)
                  ? categories.reduce((acc: any, cat: any) => {
                      acc[cat.id] = cat.symptoms || [];
                      return acc;
                    }, {})
                  : categories;

                setAvailableSymptoms({
                  jism: categoriesObj.jism || [],
                  nafs: categoriesObj.nafs || [],
                  aql: categoriesObj.aql || [],
                  qalb: categoriesObj.qalb || [],
                  ruh: categoriesObj.ruh || [],
                });
                setError(null);
              })
              .catch((err) => {
                console.error('Retry failed:', err);
                setError(
                  'Failed to load symptom categories. Please try again.'
                );
              })
              .finally(() => setLoading(false));
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <QuestionCard
          question="What are you experiencing?"
          description="Select all symptoms you're experiencing in each category"
        >
          <CategoryCard
            category="jism"
            symptoms={availableSymptoms.jism}
            selectedSymptoms={selectedSymptoms.jism}
            onSelectSymptom={(symptom) => handleSelectSymptom('jism', symptom)}
            intensity={intensity.jism}
            onIntensityChange={(value) => handleIntensityChange('jism', value)}
            onAddCustomSymptom={handleAddCustomSymptom}
          />

          <CategoryCard
            category="nafs"
            symptoms={availableSymptoms.nafs}
            selectedSymptoms={selectedSymptoms.nafs}
            onSelectSymptom={(symptom) => handleSelectSymptom('nafs', symptom)}
            intensity={intensity.nafs}
            onIntensityChange={(value) => handleIntensityChange('nafs', value)}
            onAddCustomSymptom={handleAddCustomSymptom}
          />

          <CategoryCard
            category="aql"
            symptoms={availableSymptoms.aql}
            selectedSymptoms={selectedSymptoms.aql}
            onSelectSymptom={(symptom) => handleSelectSymptom('aql', symptom)}
            intensity={intensity.aql}
            onIntensityChange={(value) => handleIntensityChange('aql', value)}
            onAddCustomSymptom={handleAddCustomSymptom}
          />

          <CategoryCard
            category="qalb"
            symptoms={availableSymptoms.qalb}
            selectedSymptoms={selectedSymptoms.qalb}
            onSelectSymptom={(symptom) => handleSelectSymptom('qalb', symptom)}
            intensity={intensity.qalb}
            onIntensityChange={(value) => handleIntensityChange('qalb', value)}
            onAddCustomSymptom={handleAddCustomSymptom}
          />

          <CategoryCard
            category="ruh"
            symptoms={availableSymptoms.ruh}
            selectedSymptoms={selectedSymptoms.ruh}
            onSelectSymptom={(symptom) => handleSelectSymptom('ruh', symptom)}
            intensity={intensity.ruh}
            onIntensityChange={(value) => handleIntensityChange('ruh', value)}
            onAddCustomSymptom={handleAddCustomSymptom}
          />
        </QuestionCard>

        <QuestionCard question="How long have you been experiencing this?">
          <DurationSelector value={duration} onChange={handleSelectDuration} />
        </QuestionCard>

        {/* Summary Section */}
        {hasSelectedSymptoms() && (
          <View style={styles.summarySection}>
            <Text style={styles.sectionTitle}>Summary</Text>
            <View style={styles.summaryContent}>
              <Text style={styles.summaryText}>
                You've selected{' '}
                <Text style={styles.summaryHighlight}>
                  {getSelectedSymptomsCount()}
                </Text>{' '}
                symptoms across{' '}
                <Text style={styles.summaryHighlight}>
                  {
                    Object.entries(selectedSymptoms).filter(
                      ([_, symptoms]) => symptoms.length > 0
                    ).length
                  }
                </Text>{' '}
                categories.
              </Text>
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {onCancel && (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onCancel}
              disabled={submitting}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.submitButton,
              (!hasSelectedSymptoms() || submitting) &&
                styles.submitButtonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={!hasSelectedSymptoms() || submitting}
          >
            {submitting ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.submitButtonText}>Submit</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Custom Symptom Modal */}
      <Modal
        visible={customSymptomModal.visible}
        transparent
        animationType="fade"
        onRequestClose={cancelCustomSymptom}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              Add Custom Symptom for{' '}
              {customSymptomModal.category
                ? CATEGORIES[customSymptomModal.category].title
                : ''}
            </Text>

            <TextInput
              style={styles.modalInput}
              placeholder="Enter symptom description"
              value={customSymptomModal.value}
              onChangeText={(text) =>
                setCustomSymptomModal((prev) => ({ ...prev, value: text }))
              }
              autoFocus
              returnKeyType="done"
              onSubmitEditing={addCustomSymptom}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={cancelCustomSymptom}
              >
                <Text style={styles.modalCancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.modalAddButton,
                  !customSymptomModal.value.trim() &&
                    styles.modalAddButtonDisabled,
                ]}
                onPress={addCustomSymptom}
                disabled={!customSymptomModal.value.trim()}
              >
                <Text style={styles.modalAddButtonText}>Add</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: 16,
      paddingBottom: 100, // Extra padding for bottom
    },
    centerContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      marginBottom: 24,
      textAlign: 'center',
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: colors.textSecondary,
    },
    errorText: {
      marginTop: 16,
      fontSize: 16,
      color: colors.error,
      textAlign: 'center',
    },
    retryButton: {
      marginTop: 16,
      paddingVertical: 10,
      paddingHorizontal: 20,
      backgroundColor: colors.primary,
      borderRadius: 8,
    },
    retryButtonText: {
      fontSize: 16,
      color: 'white',
      fontWeight: '600',
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 12,
    },
    summarySection: {
      marginVertical: 16,
      padding: 16,
      backgroundColor: '#F0F7FF',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#D0E5FF',
    },
    summaryContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    summaryText: {
      fontSize: 16,
      color: colors.text,
    },
    summaryHighlight: {
      fontWeight: 'bold',
      color: colors.primary,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 24,
      marginBottom: 24,
    },
    cancelButton: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      backgroundColor: 'white',
      flex: 1,
      marginRight: 8,
      alignItems: 'center',
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.textSecondary,
    },
    submitButton: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      backgroundColor: colors.primary,
      flex: 2,
      alignItems: 'center',
    },
    submitButtonDisabled: {
      backgroundColor: colors.primaryLight,
      opacity: 0.7,
    },
    submitButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: 'white',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 16,
    },
    modalContent: {
      backgroundColor: 'white',
      borderRadius: 12,
      padding: 20,
      width: '90%',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    modalInput: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
      marginBottom: 20,
    },
    modalButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    modalCancelButton: {
      paddingVertical: 10,
      paddingHorizontal: 16,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      backgroundColor: 'white',
      flex: 1,
      marginRight: 8,
      alignItems: 'center',
    },
    modalCancelButtonText: {
      fontSize: 16,
      color: colors.textSecondary,
    },
    modalAddButton: {
      paddingVertical: 10,
      paddingHorizontal: 16,
      borderRadius: 8,
      backgroundColor: colors.primary,
      flex: 1,
      alignItems: 'center',
    },
    modalAddButtonDisabled: {
      backgroundColor: colors.primaryLight,
      opacity: 0.7,
    },
    modalAddButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: 'white',
    },
  });
