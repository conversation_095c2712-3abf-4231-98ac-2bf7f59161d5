/**
 * Example usage of the ConfirmationModal component
 * This file demonstrates various ways to use the reusable ConfirmationModal
 */

import React, { useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { ConfirmationModal } from './ConfirmationModal';

export const ConfirmationModalExample = () => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);

  // Example 1: Destructive action (delete)
  const handleDeleteConfirm = () => {
    setShowDeleteModal(false);
    // Perform delete action
    console.log('Item deleted');
  };

  // Example 2: Logout confirmation
  const handleLogoutConfirm = () => {
    setShowLogoutModal(false);
    // Perform logout action
    console.log('User logged out');
  };

  // Example 3: Save confirmation (non-destructive)
  const handleSaveConfirm = () => {
    setShowSaveModal(false);
    // Perform save action
    console.log('Data saved');
  };

  return (
    <View style={styles.container}>
      {/* Example buttons to trigger modals */}
      <TouchableOpacity
        style={[styles.button, styles.deleteButton]}
        onPress={() => setShowDeleteModal(true)}
      >
        <Text style={styles.buttonText}>Delete Item</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.logoutButton]}
        onPress={() => setShowLogoutModal(true)}
      >
        <Text style={styles.buttonText}>Logout</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.saveButton]}
        onPress={() => setShowSaveModal(true)}
      >
        <Text style={styles.buttonText}>Save Changes</Text>
      </TouchableOpacity>

      {/* Example 1: Destructive delete confirmation */}
      <ConfirmationModal
        visible={showDeleteModal}
        title="Delete Item"
        message="Are you sure you want to delete this item? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        onCancel={() => setShowDeleteModal(false)}
        isDestructive={true}
        iconName="trash"
        iconColor="#ef4444"
      />

      {/* Example 2: Logout confirmation */}
      <ConfirmationModal
        visible={showLogoutModal}
        title="Logout"
        message="Are you sure you want to logout? You will need to sign in again to access your account."
        confirmText="Logout"
        cancelText="Stay Logged In"
        onConfirm={handleLogoutConfirm}
        onCancel={() => setShowLogoutModal(false)}
        isDestructive={true}
        iconName="log-out"
        iconColor="#f59e0b"
      />

      {/* Example 3: Save confirmation (non-destructive) */}
      <ConfirmationModal
        visible={showSaveModal}
        title="Save Changes"
        message="Do you want to save your changes before continuing?"
        confirmText="Save"
        cancelText="Don't Save"
        onConfirm={handleSaveConfirm}
        onCancel={() => setShowSaveModal(false)}
        isDestructive={false}
        iconName="save"
        iconColor="#4ade80"
        confirmButtonColor="#4ade80"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    gap: 20,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 150,
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: '#ef4444',
  },
  logoutButton: {
    backgroundColor: '#f59e0b',
  },
  saveButton: {
    backgroundColor: '#4ade80',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});