import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { colors, ColorTheme } from '../../constants/Colors';
import { IslamicIconName, IslamicIcons } from '../../constants/IslamicIcons';

interface InsightCardProps {
  insight: string;
  islamicContext?: string;
  actionable?: string;
  icon?: IslamicIconName;
}

const InsightCard = ({
  insight,
  islamicContext,
  actionable,
  icon = 'light',
}: InsightCardProps) => {
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <MaterialCommunityIcons
          name={IslamicIcons[icon] as any}
          size={32}
          color={colors.primary}
        />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.insightText}>{insight}</Text>
        <Text style={styles.islamicContextText}>{islamicContext}</Text>
        <Text style={styles.actionableText}>{actionable}</Text>
      </View>
    </View>
  );
};

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 16,
      flexDirection: 'row',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      marginVertical: 8,
    },
    iconContainer: {
      marginRight: 16,
    },
    textContainer: {
      flex: 1,
    },
    insightText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 4,
    },
    islamicContextText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    actionableText: {
      fontSize: 14,
      color: colors.primary,
      fontWeight: '600',
    },
  });

export default InsightCard;
