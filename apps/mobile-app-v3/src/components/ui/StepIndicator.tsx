import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { colors, ColorTheme } from '../../constants/Colors';
import { IslamicIcons, IslamicIconName } from '../../constants/IslamicIcons';

interface Step {
  title: string;
  icon: IslamicIconName;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
}

const StepIndicator = ({ steps, currentStep }: StepIndicatorProps) => {
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      {steps.map((step, index) => {
        const isCompleted = index < currentStep;
        const isCurrent = index === currentStep;

        return (
          <React.Fragment key={index}>
            <View style={styles.stepContainer}>
              <View
                style={[
                  styles.iconContainer,
                  isCompleted && styles.completedIconContainer,
                  isCurrent && styles.currentIconContainer,
                ]}
              >
                {isCompleted ? (
                  <MaterialCommunityIcons
                    name={IslamicIcons.check as any}
                    size={24}
                    color="white"
                  />
                ) : (
                  <MaterialCommunityIcons
                    name={IslamicIcons[step.icon] as any}
                    size={24}
                    color={isCurrent ? colors.primary : colors.textSecondary}
                  />
                )}
              </View>
              <Text
                style={[
                  styles.stepTitle,
                  isCurrent && styles.currentStepTitle,
                ]}
              >
                {step.title}
              </Text>
            </View>
            {index < steps.length - 1 && <View style={styles.connector} />}
          </React.Fragment>
        );
      })}
    </View>
  );
};

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-between',
      width: '100%',
      paddingHorizontal: 20,
    },
    stepContainer: {
      alignItems: 'center',
      flex: 1,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    completedIconContainer: {
      backgroundColor: colors.success,
    },
    currentIconContainer: {
      backgroundColor: colors.primary,
      transform: [{ scale: 1.1 }],
    },
    stepTitle: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    currentStepTitle: {
      color: colors.primary,
      fontWeight: 'bold',
    },
    connector: {
      height: 2,
      backgroundColor: colors.border,
      flex: 1,
      marginHorizontal: -10,
      marginTop: 19,
    },
  });

export default StepIndicator;
