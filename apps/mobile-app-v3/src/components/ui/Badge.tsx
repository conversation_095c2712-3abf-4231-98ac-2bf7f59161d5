import React from 'react';

import { StyleSheet, StyleProp, ViewStyle } from 'react-native';

import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View } from './View';

export type BadgeProps = {
  label: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'small' | 'medium' | 'large';
  style?: StyleProp<ViewStyle>;
};

export function Badge({
  label,
  variant = 'primary',
  size = 'medium',
  style,
}: BadgeProps) {
  // Determine badge styles based on variant
  const getBadgeStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: colors.primary,
        };
      case 'secondary':
        return {
          backgroundColor: colors.secondary,
        };
      case 'success':
        return {
          backgroundColor: colors.success,
        };
      case 'warning':
        return {
          backgroundColor: colors.warning,
        };
      case 'error':
        return {
          backgroundColor: colors.error,
        };
      case 'info':
        return {
          backgroundColor: colors.info,
        };
      default:
        return {
          backgroundColor: colors.primary,
        };
    }
  };

  // Determine badge size
  const getBadgeSize = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: Theme.spacing.xs,
          paddingHorizontal: Theme.spacing.s,
          borderRadius: Theme.borderRadius.small,
        };
      case 'medium':
        return {
          paddingVertical: Theme.spacing.xs,
          paddingHorizontal: Theme.spacing.m,
          borderRadius: Theme.borderRadius.medium,
        };
      case 'large':
        return {
          paddingVertical: Theme.spacing.s,
          paddingHorizontal: Theme.spacing.l,
          borderRadius: Theme.borderRadius.medium,
        };
      default:
        return {
          paddingVertical: Theme.spacing.xs,
          paddingHorizontal: Theme.spacing.m,
          borderRadius: Theme.borderRadius.medium,
        };
    }
  };

  // Determine text size based on badge size
  const getTextSize = () => {
    switch (size) {
      case 'small':
        return 'caption';
      case 'medium':
        return 'caption';
      case 'large':
        return 'body';
      default:
        return 'caption';
    }
  };

  const badgeStyles = getBadgeStyles();
  const badgeSize = getBadgeSize();
  const textVariant = getTextSize();

  return (
    <View style={[styles.badge, badgeStyles, badgeSize, style]}>
      <Text variant={textVariant} color="surface" style={styles.text}>
        {label}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  badge: {
    alignSelf: 'flex-start',
  },
  text: {
    fontWeight: '500',
  },
});
