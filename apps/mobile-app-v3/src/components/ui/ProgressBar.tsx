import React, { useEffect, useRef } from 'react';
import { StyleSheet, View as RNView, Animated } from 'react-native';

import { colors, ColorTheme } from '../../constants/Colors';
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View } from './View';

export type ProgressBarProps = {
  progress: number; // 0 to 100
  height?: number;
  color?: string;
  backgroundColor?: string;
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
};

export function ProgressBar({
  progress,
  height = 5,
  color = '#64B5F6', // Soft blue color
  backgroundColor = 'rgba(255, 255, 255, 0.2)', // Semi-transparent white
  showLabel = false,
  label,
  animated = true,
}: ProgressBarProps) {
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      Animated.timing(progressAnim, {
        toValue: progress,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      progressAnim.setValue(progress);
    }
  }, [progress, animated, progressAnim]);

  // Ensure progress is between 0 and 100 for interpolation
  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  const widthInterpolated = progressAnim.interpolate({
    inputRange: [0, 100],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });

  return (
    <RNView style={styles.container}>
      {(showLabel || label) && (
        <RNView style={styles.labelContainer}>
          <Text variant="caption" color="textSecondary">
            {label || 'Progress'}
          </Text>
          <Text variant="caption" color="textSecondary">
            {`${Math.round(clampedProgress)}%`}
          </Text>
        </RNView>
      )}

      <RNView
        style={[
          styles.progressBackground,
          {
            height,
            backgroundColor,
            borderRadius: height / 2,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.progressFill,
            {
              width: widthInterpolated,
              height,
              backgroundColor: color,
              borderRadius: height / 2,
            },
          ]}
        />
      </RNView>
    </RNView>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Theme.spacing.xs,
  },
  progressBackground: {
    width: '100%',
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
});
