import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors, ColorTheme } from '../../constants/Colors';

interface QuestionCardProps {
  question: string;
  description?: string;
  children: React.ReactNode;
}

const QuestionCard = ({
  question,
  description,
  children,
}: QuestionCardProps) => {
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <Text style={styles.questionText}>{question}</Text>
      {description && (
        <Text style={styles.descriptionText}>{description}</Text>
      )}
      <View style={styles.childrenContainer}>{children}</View>
    </View>
  );
};

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      marginVertical: 8,
    },
    questionText: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    descriptionText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 16,
    },
    childrenContainer: {
      marginTop: 8,
    },
  });

export default QuestionCard;
