// React Native type declarations for Qalb Healing Mobile App

declare module '*.png' {
  const value: any;
  export default value;
}

declare module '*.jpg' {
  const value: any;
  export default value;
}

declare module '*.jpeg' {
  const value: any;
  export default value;
}

declare module '*.gif' {
  const value: any;
  export default value;
}

declare module '*.svg' {
  import React from 'react';
  import { SvgProps } from 'react-native-svg';
  const content: React.FC<SvgProps>;
  export default content;
}

declare module '*.json' {
  const value: any;
  export default value;
}

// Global React Native types
declare global {
  namespace ReactNative {
    interface ProcessedColorValue {
      __processedColorValue: true;
    }
  }
}

// Expo Vector Icons types
declare module '@expo/vector-icons' {
  import { ComponentType } from 'react';

  export interface IconProps {
    name: string;
    size?: number;
    color?: string;
    style?: any;
  }

  export const Feather: ComponentType<IconProps>;
  export const AntDesign: ComponentType<IconProps>;
  export const MaterialIcons: ComponentType<IconProps>;
  export const Ionicons: ComponentType<IconProps>;
}

// Expo specific types
declare module 'expo-constants' {
  export interface Constants {
    expoConfig?: any;
    manifest?: any;
    platform?: {
      ios?: any;
      android?: any;
      web?: any;
    };
  }

  const Constants: Constants;
  export default Constants;
}

// React Navigation types
declare global {
  namespace ReactNavigation {
    interface RootParamList {
      '(tabs)': undefined;
      modal: undefined;
      assessment: undefined;
      journey: { journeyId?: string };
      practices: undefined;
      journal: undefined;
      journeys: undefined;
      emergency: undefined;
    }
  }
}

export {};
