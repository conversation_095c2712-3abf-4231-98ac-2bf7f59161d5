// Emergency types for the mobile app
import { SoulLayer } from './index';

export interface EmergencyResource {
  id: string;
  title: string;
  description: string;
  type: 'breathing' | 'dhikr' | 'ruqyah' | 'dua';
  steps: string[];
  duration: number;
  audioUrl?: string;
  priority: number;
}

export interface EmergencySession {
  id: string;
  trigger_type: 'manual' | 'automatic' | 'scheduled';
  current_symptoms: string[];
  start_time: string;
  end_time?: string;
  status: 'active' | 'completed' | 'interrupted';
  recommended_actions: string[];
  estimated_duration: number;
  feedback?: string;
  effectiveness_rating?: number;
}

export interface EmergencyContact {
  id: string;
  name: string;
  phone: string;
  relationship: string;
  isPrimary: boolean;
}

export interface EmergencyContent {
  id: string;
  title: string;
  content: string;
  type: 'breathing' | 'dhikr' | 'dua' | 'ruqyah';
  duration: number;
  audioUrl?: string;
  instructions: string[];
}

export interface RuqyaResource {
  id: string;
  title: string;
  description?: string; // For backward compatibility
  arabic?: string;
  transliteration?: string;
  translation?: string;
  audioUrl?: string;
  category: string;
  benefits?: string[];
  soulLayers?: SoulLayer[];
  content?: string; // For backward compatibility
}

export interface EmergencyContent {
  title: string;
  description: string;
  breathingPattern: {
    inhale: number;
    hold: number;
    exhale: number;
    repetitions: number;
  };
  dhikr: {
    arabic: string;
    transliteration: string;
    translation: string;
    count: number;
  };
  ruqyah: {
    verses: any[];
  };
  dua: {
    arabic: string;
    transliteration: string;
    translation: string;
    reference: string;
  };
}
