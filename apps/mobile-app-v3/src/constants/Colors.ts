/**
 * Color definitions for Qalb Healing app
 * Includes both light and dark theme colors and Islamic-themed colors for the soul layers
 * Version 2.0: Enhanced with Islamic Visual Design System
 */

// Define base colors from the new design system
const tintColorLight = '#50C878'; // emerald
const tintColorDark = '#fff';

// Define theme color types with TypeScript
export type ThemeColorScheme = 'light' | 'dark';

// Interface defining required properties for each theme, incorporating the new design system
export interface ColorTheme {
  // Base UI colors
  text: string;
  textSecondary: string;
  textDisabled: string;
  background: string;
  cardBackground: string;
  tint: string;
  tabIconDefault: string;
  tabIconSelected: string;
  primary: string;
  primaryLight: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  surface: string;
  border: string;
  disabled: string;
  emergencyRed: string;

  // New Islamic Color System
  emerald: string;
  forestGreen: string;
  mintGreen: string;
  islamicGold: string;
  warmGold: string;
  lightGold: string;
  skyBlue: string;
  deepBlue: string;
  lightBlue: string;
  sandBeige: string;
  warmBrown: string;
  lightBrown: string;

  // Severity Indicators
  mild: string;
  moderate: string;
  severe: string;
  critical: string;

  // Updated Islamic Soul Layer Colors
  jism: string; // Body layer (Earth Brown)
  nafs: string; // Ego layer (Passionate Red)
  aql: string; // Mind layer (Calm Teal)
  qalb: string; // Heart layer (Spiritual Blue)
  ruh: string; // Soul layer (Divine Green)

  // Missing colors referenced in components
  aqlYellow: string; // Mind layer alternative color
  accent: string; // General accent color

  // Allow for additional theme-specific colors
  [key: string]: string;
}

// Type for the full color theme object
export interface ColorThemes {
  light: ColorTheme;
  dark: ColorTheme;
}

// Define and export the colors object with the new visual identity
const Colors: ColorThemes = {
  light: {
    // Base UI
    text: '#212121',
    textSecondary: '#757575',
    textDisabled: '#BDBDBD',
    background: '#F5F5DC', // sandBeige
    cardBackground: '#fff',
    tint: tintColorLight,
    tabIconDefault: '#ccc',
    tabIconSelected: tintColorLight,
    primary: '#50C878', // emerald
    primaryLight: '#98FB98', // mintGreen
    secondary: '#DAA520', // warmGold
    success: '#4CAF50', // mild
    warning: '#FF9800', // moderate
    error: '#F44336', // severe
    info: '#87CEEB', // skyBlue
    surface: '#fff',
    border: '#E0E0E0',
    disabled: '#cccccc',
    emergencyRed: '#D32F2F', // critical

    // Islamic Color System
    emerald: '#50C878',
    forestGreen: '#228B22',
    mintGreen: '#98FB98',
    islamicGold: '#FFD700',
    warmGold: '#DAA520',
    lightGold: '#F0E68C',
    skyBlue: '#87CEEB',
    deepBlue: '#4682B4',
    lightBlue: '#E6F3FF',
    sandBeige: '#F5F5DC',
    warmBrown: '#8B4513',
    lightBrown: '#DEB887',

    // Severity Indicators
    mild: '#4CAF50',
    moderate: '#FF9800',
    severe: '#F44336',
    critical: '#D32F2F',

    // Updated Islamic Soul Layer Colors
    jism: '#8B4513', // Earth brown
    nafs: '#FF6B6B', // Passionate red
    aql: '#4ECDC4', // Calm teal
    qalb: '#45B7D1', // Spiritual blue
    ruh: '#96CEB4', // Divine green

    // Missing colors referenced in components
    aqlYellow: '#F0E68C', // lightGold for mind layer
    accent: '#96CEB4', // ruh color as accent
  },
  dark: {
    // Base UI
    text: '#E0E0E0',
    textSecondary: '#B0B0B0',
    textDisabled: '#666666',
    background: '#121212',
    cardBackground: '#1E1E1E',
    tint: tintColorDark,
    tabIconDefault: '#666',
    tabIconSelected: tintColorDark,
    primary: '#50C878', // emerald
    primaryLight: '#98FB98', // mintGreen
    secondary: '#DAA520', // warmGold
    success: '#66BB6A', // Lighter mild
    warning: '#FFA726', // Lighter moderate
    error: '#EF5350', // Lighter severe
    info: '#87CEEB', // skyBlue
    surface: '#1e1e1e',
    border: '#333333',
    disabled: '#666666',
    emergencyRed: '#E57373', // Lighter critical

    // Islamic Color System (Dark Theme Variants)
    emerald: '#50C878',
    forestGreen: '#2E7D32',
    mintGreen: '#A5D6A7',
    islamicGold: '#FFEB3B',
    warmGold: '#FBC02D',
    lightGold: '#FFF59D',
    skyBlue: '#81D4FA',
    deepBlue: '#64B5F6',
    lightBlue: '#B3E5FC',
    sandBeige: '#3A3A3A', // Darker beige for background
    warmBrown: '#A1887F',
    lightBrown: '#BCAAA4',

    // Severity Indicators
    mild: '#66BB6A',
    moderate: '#FFA726',
    severe: '#EF5350',
    critical: '#E57373',

    // Updated Islamic Soul Layer Colors (Dark Theme Variants)
    jism: '#A1887F', // Lighter Earth brown
    nafs: '#E57373', // Lighter Passionate red
    aql: '#4DD0E1', // Lighter Calm teal
    qalb: '#4FC3F7', // Lighter Spiritual blue
    ruh: '#A5D6A7', // Lighter Divine green

    // Missing colors referenced in components
    aqlYellow: '#FFF59D', // lighter gold for mind layer
    accent: '#A5D6A7', // lighter ruh color as accent
  },
};

// Export the full theme object as default
export default Colors;

// Export the full theme object for theme switching
export const ColorThemes = Colors;

// Export a default theme colors for direct access (defaulting to light theme)
// This allows components to use Colors.primary instead of Colors.light.primary
export const colors = Colors.light;