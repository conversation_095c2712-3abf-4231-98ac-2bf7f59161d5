// Navigation types for React Navigation compatibility
import { StackNavigationProp } from '@react-navigation/stack';

export type RootStackParamList = {
  Home: undefined;
  Dashboard: undefined;
  SymptomSelector: {
    initialSymptoms?: any;
    onSubmitRedirect?: string;
  };
  JourneyStartScreen: {
    diagnosis: any;
    symptoms: any;
  };
  EmergencyMode: undefined;
  Journal: undefined;
  Journeys: undefined;
  Knowledge: undefined;
  Community: undefined;
  Dhikr: undefined;
  Practices: undefined;
  Assessment: undefined;
  Onboarding: undefined;
  Profile: undefined;
};

export type RootStackNavigationProp<T extends keyof RootStackParamList> = StackNavigationProp<
  RootStackParamList,
  T
>;
