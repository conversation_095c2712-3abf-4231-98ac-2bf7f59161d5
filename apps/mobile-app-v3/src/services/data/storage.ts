import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: '@QalbHealing:auth_token',
  AUTH_REFRESH_TOKEN: '@QalbHealing:auth_refresh_token',
  AUTH_EXPIRY: '@QalbHealing:auth_expiry',
  USER_DATA: '@QalbHealing:user_data', // Comprehensive user object including profile
  DATA_SOURCE: '@QalbHealing:data_source', // 'api' or 'dummy'
  DARK_MODE: '@QalbHealing:dark_mode',
  JOURNEY_PROGRESS: '@QalbHealing:journey_progress',
  EMERGENCY_SESSIONS: '@QalbHealing:emergency_sessions',
  JOURNAL_ENTRIES: '@QalbHealing:journal_entries',
};

/**
 * Get the stored authentication token
 */
export const getStoredToken = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  } catch (error) {
    console.error('Error retrieving auth token:', error);
    return null;
  }
};

/**
 * Store authentication tokens
 */
export const storeAuthTokens = async (
  token: string,
  refreshToken: string,
  expiryDate: string
): Promise<void> => {
  try {
    await AsyncStorage.multiSet([
      [STORAGE_KEYS.AUTH_TOKEN, token],
      [STORAGE_KEYS.AUTH_REFRESH_TOKEN, refreshToken],
      [STORAGE_KEYS.AUTH_EXPIRY, expiryDate],
    ]);
  } catch (error) {
    console.error('Error storing auth tokens:', error);
  }
};

/**
 * Clear authentication tokens (logout)
 */
export const clearAuthTokens = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.AUTH_TOKEN,
      STORAGE_KEYS.AUTH_REFRESH_TOKEN,
      STORAGE_KEYS.AUTH_EXPIRY,
    ]);
  } catch (error) {
    console.error('Error clearing auth tokens:', error);
  }
};

/**
 * Store full user data object
 */
export const storeUserData = async (userData: any): Promise<void> => {
  try {
    await AsyncStorage.setItem(
      STORAGE_KEYS.USER_DATA,
      JSON.stringify(userData)
    );
  } catch (error) {
    console.error('Error storing user data:', error);
  }
};

/**
 * Get full user data object
 */
export const getStoredUserData = async (): Promise<any | null> => {
  try {
    const userDataJson = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
    return userDataJson ? JSON.parse(userDataJson) : null;
  } catch (error) {
    console.error('Error retrieving user data:', error);
    return null;
  }
};
