/**
 * Dummy data for testing and development
 * Mimics the structure of the API responses
 */
import * as Types from '../api/types';

// User and profile data
export const dummyUser: Types.User = {
  id: 'user-123',
  email: '<EMAIL>',
  created_at: '2025-01-01T00:00:00Z',
  createdAt: '2025-01-01T00:00:00Z',
};

export const dummyProfile: Types.UserProfile = {
  user_id: 'user-123',
  email: '<EMAIL>',
  selected_layers: ['qalb', 'nafs'],
  journey_type: '7-day',
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
};

// Journey data
export const dummyJourney: Types.Journey = {
  id: 'journey-123',
  user_id: 'user-123',
  journey_type: '7-day',
  focus_layers: ['qalb', 'nafs'],
  duration_days: 7,
  total_days: 7,
  start_date: '2025-05-20T00:00:00Z',
  status: 'active',
  current_day: 3,
  modules_plan: [
    { id: 'module-1', day: 1, title: 'Understanding Your Qalb' },
    { id: 'module-2', day: 2, title: 'Purifying the Heart' },
    { id: 'module-3', day: 3, title: 'Regulating the Nafs' },
    { id: 'module-4', day: 4, title: 'Emotional Healing' },
    { id: 'module-5', day: 5, title: 'Gratitude Practice' },
    { id: 'module-6', day: 6, title: 'Healing Through Dhikr' },
    { id: 'module-7', day: 7, title: 'Integrating Your Healing' },
  ],
  journey_modules: [
    {
      id: 'module-1',
      day: 1,
      title: 'Understanding Your Qalb',
      module_completions: [
        {
          status: 'completed',
          completion_date: '2025-05-21T12:00:00Z',
        },
      ],
    },
    {
      id: 'module-2',
      day: 2,
      title: 'Purifying the Heart',
      module_completions: [
        {
          status: 'completed',
          completion_date: '2025-05-22T12:00:00Z',
        },
      ],
    },
  ],
};

export const dummyJourneyAnalytics: Types.JourneyAnalytics = {
  totalDays: 7,
  completedDays: 3,
  streakDays: 3,
  averageRating: 4.2,
  layerProgress: {
    jism: 0.6,
    nafs: 0.8,
    aql: 0.4,
    qalb: 0.9,
    ruh: 0.5,
  },
  insights: [
    'Great progress on emotional regulation',
    'Consider more physical practices',
  ],
  totalModulesCompleted: 2,
  currentStreak: 3,
  longestStreak: 3,
  completionRate: 75,
  lastWeekActivity: 5,
};

// Symptom data
export const dummySymptomSubmission: Types.SymptomSubmission = {
  jism: ['headache', 'fatigue'],
  nafs: ['anxiety', 'irritability'],
  aql: ['overthinking'],
  qalb: ['disconnection', 'emptiness'],
  ruh: ['lack of purpose'],
  intensity: {
    jism: 6,
    nafs: 8,
    aql: 7,
    qalb: 9,
    ruh: 5,
  },
  duration: '3-6 months',
};

export const dummySymptomSubmissionResponse: Types.SymptomSubmissionResponse = {
  success: true,
  recommendations: [
    'Focus on heart purification',
    'Practice emotional regulation',
  ],
  sessionId: 'session-123',
  timestamp: '2025-05-25T08:30:00Z',
  submission: {
    id: 123,
    user_id: 'user-123',
    jism_symptoms: ['headache', 'fatigue'],
    nafs_symptoms: ['anxiety', 'irritability'],
    aql_symptoms: ['overthinking'],
    qalb_symptoms: ['disconnection', 'emptiness'],
    ruh_symptoms: ['lack of purpose'],
    intensity_ratings: {
      jism: 6,
      nafs: 8,
      aql: 7,
      qalb: 9,
      ruh: 5,
    },
    duration: '3-6 months',
    submission_date: '2025-05-25T08:30:00Z',
  },
  diagnosis: {
    layers_affected: ['Qalb', 'Nafs'],
    spotlight:
      'You appear to be experiencing spiritual disconnection combined with emotional turbulence. The Qalb (spiritual heart) shows significant impact, suggesting a need for reconnection practices. Your Nafs (lower self) is also affected, indicating emotional regulation could be beneficial.',
    recommended_journey: '14-day',
  },
};

export const dummySymptomHistory: Types.SymptomHistory = {
  history: [
    {
      id: 123,
      user_id: 'user-123',
      jism_symptoms: ['headache', 'fatigue'],
      nafs_symptoms: ['anxiety', 'irritability'],
      aql_symptoms: ['overthinking'],
      qalb_symptoms: ['disconnection', 'emptiness'],
      ruh_symptoms: ['lack of purpose'],
      submission_date: '2025-05-25T08:30:00Z',
      user_diagnoses: {
        layers_affected: ['Qalb', 'Nafs'],
        spotlight:
          'You appear to be experiencing spiritual disconnection combined with emotional turbulence.',
        recommended_journey: '14-day',
      },
    },
    {
      id: 124,
      user_id: 'user-123',
      jism_symptoms: ['insomnia'],
      nafs_symptoms: ['frustration', 'worry'],
      aql_symptoms: ['racing thoughts', 'indecision'],
      qalb_symptoms: ['loneliness'],
      ruh_symptoms: [],
      submission_date: '2025-05-15T14:45:00Z',
      user_diagnoses: {
        layers_affected: ['Aql', 'Nafs'],
        spotlight:
          'Your mind appears to be overactive, causing emotional disturbances.',
        recommended_journey: '7-day',
      },
    },
  ],
};

export const dummyLatestDiagnosis: Types.LatestDiagnosis = {
  diagnosis: {
    id: 456,
    user_id: 'user-123',
    submission_id: 123,
    layers_affected: ['Qalb', 'Nafs'],
    spotlight:
      'You appear to be experiencing spiritual disconnection combined with emotional turbulence. The Qalb (spiritual heart) shows significant impact, suggesting a need for reconnection practices. Your Nafs (lower self) is also affected, indicating emotional regulation could be beneficial.',
    recommended_journey: '14-day',
    severity_level: 'moderate',
    diagnosis_date: '2025-05-25T08:35:00Z',
    symptom_submissions: {
      jism_symptoms: ['headache', 'fatigue'],
      nafs_symptoms: ['anxiety', 'irritability'],
      intensity_ratings: {
        jism: 6,
        nafs: 8,
      },
    },
  },
};

export const dummySymptomTrackingResponse: Types.SymptomTrackingResponse = {
  tracking: {
    id: 789,
    user_id: 'user-123',
    symptom_id: 123,
    intensity: 4,
    notes: 'Feeling better today after increased dhikr practice',
    tracking_date: '2025-05-27T09:15:00Z',
  },
};

// Journal data
export const dummyJournalEntries: Types.JournalEntry[] = [
  {
    id: 'journal-1',
    title: 'Morning Reflection',
    content:
      'I felt a deep connection during Fajr prayer today. The stillness of the early morning helped me focus.',
    entryType: 'reflection',
    mood: 'peaceful',
    emotions: ['peaceful', 'connected'],
    tags: ['prayer', 'morning', 'connection'],
    layers: ['qalb', 'ruh'],
    createdAt: '2025-05-27T06:30:00Z',
    entryDate: '2025-05-27T06:30:00Z',
  },
  {
    id: 'journal-2',
    title: 'Afternoon Struggle',
    content:
      'Had difficulty focusing during work. Felt disconnected and restless. Need to incorporate more breaks for dhikr.',
    entryType: 'challenge',
    mood: 'restless',
    emotions: ['restless', 'disconnected'],
    tags: ['work', 'focus', 'dhikr'],
    layers: ['nafs', 'aql'],
    createdAt: '2025-05-26T14:15:00Z',
    entryDate: '2025-05-26T14:15:00Z',
  },
];

// Emergency mode data
export const dummyEmergencySession: Types.EmergencySession = {
  id: 'emergency-123',
  user_id: 'user-123',
  trigger_type: 'anxiety',
  current_symptoms: [
    'rapid heartbeat',
    'shallow breathing',
    'overwhelmed',
    'fearful',
  ],
  recommended_actions: ['breathing_exercise', 'dhikr', 'ruqyah'],
  estimated_duration: 15,
  symptoms: {
    physical: ['rapid heartbeat', 'shallow breathing'],
    emotional: ['overwhelmed', 'fearful'],
  },
  start_time: '2025-05-26T19:45:00Z',
  end_time: '2025-05-26T20:00:00Z',
  status: 'completed',
  effectiveness_rating: 4,
  feedback:
    'The breathing exercise and Ruqyah verses were particularly helpful',
};
