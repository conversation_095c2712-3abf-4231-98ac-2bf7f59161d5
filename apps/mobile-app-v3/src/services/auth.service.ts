/**
 * Authentication Service
 * Handles user authentication and profile management
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../constants/Config';
import { STORAGE_KEYS, storeUserData, getStoredUserData } from './data/storage';

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  profile?: UserProfile;
}

export interface UserProfile {
  awarenessLevel?: string;
  ruqyaFamiliarity?: string;
  profession?: string;
  culturalBackground?: string;
  completionStatus?: string;
  recommendedPathway?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

class AuthServiceClass {
  private currentUser: User | null = null;
  private authToken: string | null = null;

  private async _ensureAuthDataLoaded(): Promise<void> {
    if (!this.currentUser || !this.authToken) {
      console.log('[AuthService] Ensuring auth data is loaded...');
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      const userDataString = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      if (token) {
        this.authToken = token;
        console.log('[AuthService] Auth token loaded from AsyncStorage.');
      } else {
        console.log(
          '[AuthService] No auth token found in AsyncStorage during ensure.'
        );
      }
      if (userDataString) {
        try {
          this.currentUser = JSON.parse(userDataString);
          console.log('[AuthService] User data loaded from AsyncStorage.');
        } catch (e) {
          console.error(
            '[AuthService] Failed to parse user data from AsyncStorage:',
            e
          );
          this.currentUser = null; // Ensure currentUser is null if parsing fails
        }
      } else {
        console.log(
          '[AuthService] No user data found in AsyncStorage during ensure.'
        );
      }
    }
  }

  /**
   * Initialize auth service and check for existing session
   */
  async initialize(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      const userData = await getStoredUserData();

      if (token && userData) {
        this.currentUser = userData;
        this.isAuthenticated = true;
      } else {
        this.isAuthenticated = false;
      }
    } catch (error) {
      console.error('Failed to initialize auth service:', error);
    }
  }

  /**
   * Request to resend email confirmation link.
   */
  async requestResendConfirmation(email: string): Promise<void> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/auth/resend-confirmation`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        }
      );

      if (!response.ok) {
        // Even if the backend returns a generic success message for security,
        // a !response.ok here might indicate a server error or network issue.
        const errorData = await response.json().catch(() => ({})); // Try to parse error, default to empty obj
        throw new Error(
          errorData.message || 'Failed to request resend confirmation.'
        );
      }
      // Success is indicated by a 200 OK from the backend with its generic message.
      // The actual message to the user will be handled by the UI calling this service.
      console.log(
        'Request to resend confirmation email sent successfully for:',
        email
      );
    } catch (error) {
      console.error('Error requesting resend confirmation:', error);
      throw error; // Re-throw to be handled by the UI (e.g., show a toast)
    }
  }

  /**
   * Handles session establishment after an auth redirect (e.g., email confirmation, OAuth).
   */
  async handleAuthRedirect(params: {
    access_token: string;
    refresh_token: string;
    expires_in?: number;
  }): Promise<User | null> {
    console.log('[handleAuthRedirect] Received params:', {
      access_token: params.access_token ? 'PRESENT' : 'MISSING', // Don't log full token
      refresh_token: params.refresh_token ? 'PRESENT' : 'MISSING', // Don't log full token
      expires_in: params.expires_in,
    });

    try {
      this.authToken = params.access_token;
      console.log('[handleAuthRedirect] Set this.authToken');

      const itemsToStore: [string, string][] = [
        ['authToken', params.access_token],
        ['refreshToken', params.refresh_token],
      ];

      console.log(
        '[handleAuthRedirect] Attempting to store tokens in AsyncStorage:',
        itemsToStore.map((item) => item[0])
      );
      await AsyncStorage.multiSet(itemsToStore);
      console.log(
        '[handleAuthRedirect] Tokens stored in AsyncStorage successfully.'
      );

      const profileApiUrl = `${API_BASE_URL}/api/auth/profile`;
      console.log(
        '[handleAuthRedirect] Fetching user profile from:',
        profileApiUrl
      );
      const profileResponse = await fetch(profileApiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${params.access_token}`,
        },
      });

      if (!profileResponse.ok) {
        let errorResponseText = 'Could not read error response';
        try {
          errorResponseText = await profileResponse.text();
        } catch (e) {
          // ignore
        }
        console.error(
          '[handleAuthRedirect] Failed to fetch profile. Status:',
          profileResponse.status,
          'Response Text:',
          errorResponseText
        );
        this.currentUser = null;
        await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
        throw new Error(
          `Failed to fetch user profile after redirect. Status: ${profileResponse.status}`
        );
      }

      const profileData = await profileResponse.json();
      console.log(
        '[handleAuthRedirect] Successfully fetched profile data:',
        profileData
      );

      const user =
        profileData.data?.user ||
        profileData.data?.profile ||
        profileData.data ||
        profileData;
      console.log('[handleAuthRedirect] Extracted user object:', user);

      if (!user || !user.id || !user.email) {
        console.error(
          '[handleAuthRedirect] Fetched profile data is incomplete or malformed:',
          user
        );
        this.currentUser = null;
        await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
        throw new Error(
          'Fetched user profile data is incomplete or malformed.'
        );
      }

      this.currentUser = user;
      console.log(
        '[handleAuthRedirect] Set this.currentUser:',
        this.currentUser
      );

      console.log(
        '[handleAuthRedirect] Attempting to store userData in AsyncStorage.'
      );
      await storeUserData(this.currentUser);
      console.log(
        '[handleAuthRedirect] userData stored in AsyncStorage successfully.'
      );

      console.log(
        '[handleAuthRedirect] Auth session established from redirect:',
        { userId: this.currentUser.id, email: this.currentUser.email }
      );
      return this.currentUser;
    } catch (error) {
      // Log the error *before* clearing auth data, so we know what caused the failure.
      console.error(
        '[handleAuthRedirect] Error caught in try-catch block:',
        error
      );
      await this.clearAuthData();
      console.log('[handleAuthRedirect] Cleared auth data due to error.');
      throw error;
    }
  }

  /**
   * Sign in with email and password
   */
  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        // Changed to /login
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Sign in failed');
      }

      const result = await response.json();
      const authData: AuthResponse = result.data; // Extract data from wrapper

      // Store auth data
      await this.storeAuthData(authData);

      return authData;
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  /**
   * Sign up with email and password
   */
  async signUp(
    email: string,
    password: string,
    firstName: string, // Changed to required
    lastName?: string
  ): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, firstName, lastName }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Sign up failed');
      }

      const result = await response.json();
      const authData: AuthResponse = result.data; // Extract data from wrapper

      // For signup, tokens might be null if email confirmation is required
      if (authData.token) {
        // Store auth data only if we have a token
        await this.storeAuthData(authData);
      } else {
        // Store user data but no token (email confirmation required)
        this.currentUser = authData.user;
        await storeUserData(authData.user);
        console.log('Signup successful but email confirmation required');
      }

      return authData;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  /**
   * Sign out current user
   */
  async signOut(): Promise<void> {
    try {
      // Call backend to invalidate token
      if (this.authToken) {
        await fetch(`${API_BASE_URL}/api/auth/signout`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.authToken}`,
          },
        });
      }
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      // Clear local data regardless of backend response
      await this.clearAuthData();
    }
  }

  /**
   * Get current authenticated user
   */
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return this.authToken;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.currentUser !== null && this.authToken !== null;
  }

  /**
   * Update user profile
   */
  async updateUserProfile(profileData: Partial<UserProfile>): Promise<void> {
    try {
      await this._ensureAuthDataLoaded(); // Ensure auth data is loaded

      if (!this.currentUser || !this.authToken) {
        // Check both after attempting to load
        console.error(
          '[AuthService] updateUserProfile: Authentication details missing even after ensure load.'
        );
        throw new Error('No authenticated user or token');
      }

      // Update backend
      const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.authToken}`, // this.authToken should be populated now
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Profile update failed');
      }

      // Update local user data
      this.currentUser = {
        ...this.currentUser,
        profile: {
          ...this.currentUser.profile,
          ...profileData,
        },
      };

      // Store updated user data
      await storeUserData(this.currentUser);
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  }

  /**
   * Refresh auth token
   */
  async refreshToken(): Promise<any> {
    try {
      const refreshToken = await AsyncStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const result = await response.json();

      this.authToken = result.token;
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, result.token);

      if (result.refreshToken) {
        await AsyncStorage.setItem(
          STORAGE_KEYS.AUTH_REFRESH_TOKEN,
          result.refreshToken
        );
      }

      return result;
    } catch (error) {
      console.error('Token refresh error:', error);
      // If refresh fails, sign out user
      await this.signOut();
      throw error;
    }
  }

  /**
   * Store authentication data locally
   */
  private async storeAuthData(authData: AuthResponse): Promise<void> {
    try {
      this.currentUser = authData.user;
      this.authToken = authData.token;

      await storeUserData(authData.user);
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, authData.token);
      await AsyncStorage.setItem(
        STORAGE_KEYS.AUTH_REFRESH_TOKEN,
        authData.refreshToken
      );
    } catch (error) {
      console.error('Failed to store auth data:', error);
      throw error;
    }
  }

  /**
   * Clear authentication data
   */
  private async clearAuthData(): Promise<void> {
    try {
      this.currentUser = null;
      this.authToken = null;

      await AsyncStorage.multiRemove([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.AUTH_REFRESH_TOKEN,
      ]);
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }

  /**
   * Guest mode - create temporary user for onboarding
   */
  async createGuestUser(): Promise<User> {
    const guestUser: User = {
      id: `guest_${Date.now()}`,
      email: '<EMAIL>',
      firstName: 'Guest',
      profile: {
        completionStatus: 'incomplete',
      },
    };

    this.currentUser = guestUser;
    await storeUserData(guestUser);

    return guestUser;
  }

  /**
   * Convert guest user to registered user
   */
  async convertGuestToUser(
    email: string,
    password: string
  ): Promise<AuthResponse> {
    try {
      const guestData = this.currentUser;

      const response = await fetch(`${API_BASE_URL}/api/auth/convert-guest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          guestData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Account creation failed');
      }

      const authData: AuthResponse = await response.json();

      // Store auth data
      await this.storeAuthData(authData);

      return authData;
    } catch (error) {
      console.error('Guest conversion error:', error);
      throw error;
    }
  }

  /**
   * Login user with credentials
   */
  async login(credentials: {
    email: string;
    password: string;
  }): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
      }

      const result = await response.json();
      const authData: AuthResponse = result.data; // Extract data from wrapper

      // Store auth data
      await this.storeAuthData(authData);

      return authData;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Store authentication token securely
   */
  async storeToken(token: string): Promise<void> {
    // This method is likely to be superseded by handleAuthRedirect for full session setup.
    // For now, ensuring key consistency if it's called directly.
    try {
      this.authToken = token;
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    } catch (error) {
      console.error('Failed to store token:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const authService = new AuthServiceClass();
export default authService;
