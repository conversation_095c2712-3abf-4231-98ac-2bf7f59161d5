/**
 * Mock Symptoms Service for Development and Testing
 * Provides fallback data when the real service is unavailable
 */

import { SymptomSubmission } from './api/types';

export const mockSymptomCategories = {
  jism: [
    'Headaches',
    'Fatigue',
    'Sleep problems',
    'Appetite changes',
    'Body aches',
    'Digestive issues',
    'Chest tightness',
    'Muscle tension',
    'Dizziness',
    'Heart palpitations',
  ],
  nafs: [
    'Anxiety',
    'Sadness',
    'Anger',
    'Fear',
    'Guilt',
    'Shame',
    'Loneliness',
    'Irritability',
    'Mood swings',
    'Emotional numbness',
  ],
  aql: [
    'Racing thoughts',
    'Difficulty concentrating',
    'Memory problems',
    'Overthinking',
    'Negative self-talk',
    'Confusion',
    'Indecisiveness',
    'Mental fog',
    'Obsessive thoughts',
    'Worry',
  ],
  qalb: [
    'Feeling disconnected from Allah',
    'Loss of spiritual motivation',
    'Difficulty in prayer',
    'Lack of khushu (humility)',
    'Spiritual emptiness',
    'Loss of faith',
    'Feeling spiritually numb',
    'Difficulty reading Quran',
    'Loss of Islamic identity',
    'Spiritual confusion',
  ],
  ruh: [
    'Existential crisis',
    'Loss of purpose',
    'Feeling lost in life',
    'Questioning life meaning',
    'Spiritual darkness',
    'Soul emptiness',
    'Disconnection from inner self',
    'Loss of hope',
    'Feeling spiritually dead',
    'Deep inner pain',
  ],
};

export const mockDiagnosis = {
  id: 'mock-diagnosis-1',
  diagnosisData: {
    ai_response: {
      personalized_message: 'Based on your responses, it appears you are experiencing challenges primarily in the emotional and spiritual realms. This is a common experience for many believers, and with proper guidance and practice, healing is possible.',
      islamic_insights: [
        'Remember that Allah tests those He loves, and your struggles are a means of purification.',
        'The Prophet (peace be upon him) said: "No fatigue, nor disease, nor sorrow, nor sadness, nor hurt, nor distress befalls a Muslim, not even if it were the prick he receives from a thorn, but that Allah expiates some of his sins for that."',
        'Your awareness of these challenges is the first step toward healing and spiritual growth.',
      ],
    },
  },
  nextSteps: [
    'Begin with consistent dhikr (remembrance of Allah) throughout the day',
    'Establish regular prayer times and focus on improving khushu',
    'Seek knowledge about Islamic approaches to mental wellness',
    'Consider speaking with a knowledgeable Islamic counselor',
    'Practice gratitude by listing three things you\'re thankful for each day',
  ],
  layerAnalyses: [
    {
      id: 'layer-nafs',
      diagnosisId: 'mock-diagnosis-1',
      layer: 'nafs' as const,
      layerName: 'Nafs (Emotional Self)',
      impactScore: 75,
      priority: 'primary' as const,
      insights: [
        'Your emotional state shows signs of imbalance, which is affecting your overall well-being.',
        'The nafs requires constant attention and purification through Islamic practices.',
      ],
      recommendations: [
        'Practice regular istighfar (seeking forgiveness)',
        'Engage in dhikr to calm the heart',
        'Seek refuge in Allah from negative emotions',
      ],
      islamicContext: 'The nafs is mentioned in the Quran as requiring purification. Allah says: "And [by] the soul and He who proportioned it, And inspired it [with discernment of] its wickedness and its righteousness, He has succeeded who purifies it." (91:7-9)',
    },
    {
      id: 'layer-qalb',
      diagnosisId: 'mock-diagnosis-1',
      layer: 'qalb' as const,
      layerName: 'Qalb (Spiritual Heart)',
      impactScore: 60,
      priority: 'secondary' as const,
      insights: [
        'Your spiritual connection shows some areas that need attention.',
        'The heart is the center of faith and requires constant nourishment.',
      ],
      recommendations: [
        'Increase Quran recitation and reflection',
        'Make dua with presence of heart',
        'Seek Islamic knowledge to strengthen faith',
      ],
      islamicContext: 'The Prophet (peace be upon him) said: "Verily, in the body there is a piece of flesh which, if it be sound, all of the body is sound, and which, if it be diseased, all of the body is diseased. Verily, it is the heart."',
    },
    {
      id: 'layer-jism',
      diagnosisId: 'mock-diagnosis-1',
      layer: 'jism' as const,
      layerName: 'Jism (Physical Body)',
      impactScore: 45,
      priority: 'tertiary' as const,
      insights: [
        'Physical symptoms may be manifestations of emotional and spiritual imbalances.',
      ],
      recommendations: [
        'Maintain regular sleep schedule',
        'Eat halal and wholesome foods',
        'Exercise regularly as a form of worship',
      ],
      islamicContext: 'The body is an amanah (trust) from Allah and should be cared for properly.',
    },
  ],
};

export class MockSymptomsService {
  async getSymptomCategories() {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return Object.entries(mockSymptomCategories).map(([id, symptoms]) => ({
      id,
      name: id.charAt(0).toUpperCase() + id.slice(1),
      symptoms,
    }));
  }

  async submitSymptoms(submission: SymptomSubmission) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('Mock submission:', submission);
    
    return {
      success: true,
      diagnosis: mockDiagnosis,
    };
  }
}

export const mockSymptomsService = new MockSymptomsService();