/**
 * Notification Service
 * Handles push notifications and local notifications
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { storageService } from './storage.service';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface NotificationData {
  title: string;
  body: string;
  data?: any;
  categoryId?: string;
  sound?: boolean;
  badge?: number;
}

export interface ScheduledNotification {
  id: string;
  title: string;
  body: string;
  trigger: any;
  data?: any;
}

class NotificationService {
  private isInitialized = false;
  private pushToken: string | null = null;

  /**
   * Initialize notification service
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) return;

      // Request permissions
      const { status } = await Notifications.requestPermissionsAsync();

      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
        return;
      }

      // Get push token for remote notifications
      if (Platform.OS !== 'web') {
        const token = await Notifications.getExpoPushTokenAsync();
        this.pushToken = token.data;

        // Store token for backend registration
        await storageService.setItem('push_token', this.pushToken);
      }

      // Set up notification categories
      await this.setupNotificationCategories();

      this.isInitialized = true;
      console.log('Notification service initialized');
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
    }
  }

  /**
   * Get push token
   */
  getPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Schedule a local notification
   */
  async scheduleNotification(
    notification: NotificationData,
    trigger: any
  ): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: notification.sound !== false,
          badge: notification.badge,
          categoryIdentifier: notification.categoryId,
        },
        trigger,
      });

      return notificationId;
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      throw error;
    }
  }

  /**
   * Schedule daily reminder notifications
   */
  async scheduleDailyReminders(): Promise<void> {
    try {
      // Cancel existing daily reminders
      await this.cancelNotificationsByCategory('daily_reminder');

      // Morning dhikr reminder
      await this.scheduleNotification(
        {
          title: 'Morning Dhikr',
          body: 'Start your day with remembrance of Allah ﷻ',
          categoryId: 'daily_reminder',
          data: { type: 'morning_dhikr' },
        },
        {
          hour: 7,
          minute: 0,
          repeats: true,
        }
      );

      // Evening reflection reminder
      await this.scheduleNotification(
        {
          title: 'Evening Reflection',
          body: 'Take a moment to reflect on your day',
          categoryId: 'daily_reminder',
          data: { type: 'evening_reflection' },
        },
        {
          hour: 20,
          minute: 0,
          repeats: true,
        }
      );

      console.log('Daily reminders scheduled');
    } catch (error) {
      console.error('Failed to schedule daily reminders:', error);
    }
  }

  /**
   * Schedule journey-specific notifications
   */
  async scheduleJourneyNotifications(
    journeyId: string,
    preferences: any
  ): Promise<void> {
    try {
      // Cancel existing journey notifications
      await this.cancelNotificationsByCategory(`journey_${journeyId}`);

      if (preferences.dailyReminder) {
        await this.scheduleNotification(
          {
            title: 'Journey Practice',
            body: 'Time for your daily healing practice',
            categoryId: `journey_${journeyId}`,
            data: { type: 'journey_practice', journeyId },
          },
          {
            hour: preferences.reminderTime?.hour || 9,
            minute: preferences.reminderTime?.minute || 0,
            repeats: true,
          }
        );
      }

      if (preferences.weeklyCheck) {
        await this.scheduleNotification(
          {
            title: 'Weekly Check-in',
            body: 'How has your healing journey been this week?',
            categoryId: `journey_${journeyId}`,
            data: { type: 'weekly_checkin', journeyId },
          },
          {
            weekday: 1, // Monday
            hour: 10,
            minute: 0,
            repeats: true,
          }
        );
      }

      console.log('Journey notifications scheduled');
    } catch (error) {
      console.error('Failed to schedule journey notifications:', error);
    }
  }

  /**
   * Send immediate local notification
   */
  async sendLocalNotification(notification: NotificationData): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: notification.sound !== false,
          badge: notification.badge,
          categoryIdentifier: notification.categoryId,
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Failed to send local notification:', error);
    }
  }

  /**
   * Cancel specific notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Failed to cancel notification:', error);
    }
  }

  /**
   * Cancel notifications by category
   */
  async cancelNotificationsByCategory(categoryId: string): Promise<void> {
    try {
      const scheduledNotifications =
        await Notifications.getAllScheduledNotificationsAsync();

      const notificationsToCancel = scheduledNotifications
        .filter(
          (notification) =>
            (notification.content as any).categoryIdentifier === categoryId
        )
        .map((notification) => notification.identifier);

      await Promise.all(
        notificationsToCancel.map((id) =>
          Notifications.cancelScheduledNotificationAsync(id)
        )
      );
    } catch (error) {
      console.error('Failed to cancel notifications by category:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to cancel all notifications:', error);
    }
  }

  /**
   * Get all scheduled notifications
   */
  async getScheduledNotifications(): Promise<ScheduledNotification[]> {
    try {
      const notifications =
        await Notifications.getAllScheduledNotificationsAsync();

      return notifications.map((notification) => ({
        id: notification.identifier,
        title: notification.content.title || '',
        body: notification.content.body || '',
        trigger: notification.trigger,
        data: notification.content.data,
      }));
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Handle notification received while app is in foreground
   */
  addNotificationReceivedListener(
    listener: (notification: Notifications.Notification) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationReceivedListener(listener);
  }

  /**
   * Handle notification response (user tapped notification)
   */
  addNotificationResponseReceivedListener(
    listener: (response: Notifications.NotificationResponse) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  /**
   * Set up notification categories with actions
   */
  private async setupNotificationCategories(): Promise<void> {
    try {
      await Notifications.setNotificationCategoryAsync('daily_reminder', [
        {
          identifier: 'complete',
          buttonTitle: 'Mark Complete',
          options: { opensAppToForeground: false },
        },
        {
          identifier: 'snooze',
          buttonTitle: 'Remind Later',
          options: { opensAppToForeground: false },
        },
      ]);

      await Notifications.setNotificationCategoryAsync('journey_practice', [
        {
          identifier: 'start_practice',
          buttonTitle: 'Start Practice',
          options: { opensAppToForeground: true },
        },
        {
          identifier: 'skip_today',
          buttonTitle: 'Skip Today',
          options: { opensAppToForeground: false },
        },
      ]);

      await Notifications.setNotificationCategoryAsync('emergency_support', [
        {
          identifier: 'get_help',
          buttonTitle: 'Get Help Now',
          options: { opensAppToForeground: true },
        },
        {
          identifier: 'im_safe',
          buttonTitle: "I'm Safe",
          options: { opensAppToForeground: false },
        },
      ]);
    } catch (error) {
      console.error('Failed to setup notification categories:', error);
    }
  }

  /**
   * Register device for push notifications with backend
   */
  async registerForPushNotifications(userId: string): Promise<void> {
    try {
      if (!this.pushToken) {
        console.warn('No push token available for registration');
        return;
      }

      // This would typically call your backend API
      // await apiService.registerPushToken(userId, this.pushToken);

      console.log('Push token registered for user:', userId);
    } catch (error) {
      console.error('Failed to register for push notifications:', error);
    }
  }

  /**
   * Unregister device from push notifications
   */
  async unregisterFromPushNotifications(userId: string): Promise<void> {
    try {
      if (!this.pushToken) return;

      // This would typically call your backend API
      // await apiService.unregisterPushToken(userId, this.pushToken);

      console.log('Push token unregistered for user:', userId);
    } catch (error) {
      console.error('Failed to unregister from push notifications:', error);
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;
