import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from './apiClient';
import dataSourceProvider, {
  DataSourceConfig,
} from '../data/DataSourceProvider';

/**
 * Error types for service operations
 */
export enum ServiceErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION = 'VALIDATION',
  SERVER = 'SERVER',
  TIMEOUT = 'TIMEOUT',
  OFFLINE = 'OFFLINE',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Custom error class for service operations
 */
export class ServiceError extends Error {
  type: ServiceErrorType;
  statusCode?: number;
  details?: any;

  constructor(
    message: string,
    type: ServiceErrorType,
    statusCode?: number,
    details?: any
  ) {
    super(message);
    this.name = 'ServiceError';
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * Configuration interface for services
 */
export interface ServiceConfig {
  cacheTimeout: number; // in milliseconds
  retryAttempts: number;
  retryDelay: number; // in milliseconds
}

/**
 * Default service configuration
 */
const DEFAULT_SERVICE_CONFIG: ServiceConfig = {
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
};

/**
 * Base service class with common functionality for API operations
 */
export abstract class BaseService {
  protected readonly config: ServiceConfig;
  protected readonly serviceName: string;

  constructor(serviceName: string, config?: Partial<ServiceConfig>) {
    this.serviceName = serviceName;
    this.config = { ...DEFAULT_SERVICE_CONFIG, ...config };
  }

  /**
   * Fetch data from the appropriate source (API or dummy data)
   * with retry logic and error handling
   */
  protected async fetchData<T>(
    endpoint: string,
    fallbackData: T,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
      body?: any;
      useCache?: boolean;
      forceRefresh?: boolean;
    } = {}
  ): Promise<T> {
    const {
      method = 'GET',
      body = undefined,
      useCache = true,
      forceRefresh = false,
    } = options;

    // Check if we should use real API or dummy data
    const useRealApi = await dataSourceProvider.isUsingRealApi();

    if (!useRealApi) {
      // Return fallback data if using dummy data
      return fallbackData;
    }

    // Handle caching for GET requests
    const cacheKey = `${this.serviceName}:${endpoint}:${method}`;
    if (method === 'GET' && useCache && !forceRefresh) {
      try {
        const cachedData = await this.getCachedData<T>(cacheKey);
        if (cachedData) {
          return cachedData;
        }
      } catch (error) {
        console.warn(`Cache retrieval failed for ${cacheKey}:`, error);
        // Continue with API request if cache retrieval fails
      }
    }

    // Initialize retry counter
    let retryCount = 0;
    let lastError: Error;

    while (retryCount <= this.config.retryAttempts) {
      try {
        // Make API request
        const response = await apiClient.request({
          url: endpoint,
          method,
          data: body,
        });

        // Cache successful GET responses
        if (method === 'GET' && useCache) {
          await this.setCachedData(cacheKey, response.data);
        }

        return response.data as T;
      } catch (error: any) {
        lastError = error;

        // Convert axios error to ServiceError
        const serviceError = this.handleError(error);

        // Don't retry for certain error types
        if (
          serviceError.type === ServiceErrorType.AUTHENTICATION ||
          serviceError.type === ServiceErrorType.AUTHORIZATION ||
          serviceError.type === ServiceErrorType.NOT_FOUND ||
          serviceError.type === ServiceErrorType.VALIDATION
        ) {
          throw serviceError;
        }

        retryCount++;

        // If we've exhausted retries, throw the error
        if (retryCount > this.config.retryAttempts) {
          throw serviceError;
        }

        // Wait before retrying
        await this.delay(this.config.retryDelay * retryCount);
      }
    }

    // This should never be reached, but just in case
    throw this.handleError(lastError!);
  }

  /**
   * Handle and convert errors to ServiceError
   */
  protected handleError(error: any): ServiceError {
    if (error instanceof ServiceError) {
      return error;
    }

    // Handle axios errors
    if (error.response) {
      const statusCode = error.response.status;
      const message = error.response.data?.message || error.message;

      switch (statusCode) {
        case 401:
          return new ServiceError(
            message,
            ServiceErrorType.AUTHENTICATION,
            statusCode
          );
        case 403:
          return new ServiceError(
            message,
            ServiceErrorType.AUTHORIZATION,
            statusCode
          );
        case 404:
          return new ServiceError(
            message,
            ServiceErrorType.NOT_FOUND,
            statusCode
          );
        case 422:
          return new ServiceError(
            message,
            ServiceErrorType.VALIDATION,
            statusCode,
            error.response.data
          );
        case 500:
        case 502:
        case 503:
        case 504:
          return new ServiceError(message, ServiceErrorType.SERVER, statusCode);
        default:
          return new ServiceError(
            message,
            ServiceErrorType.UNKNOWN,
            statusCode
          );
      }
    } else if (error.request) {
      // Network error
      return new ServiceError(
        'Network error occurred',
        ServiceErrorType.NETWORK
      );
    } else if (error.code === 'ECONNABORTED') {
      // Timeout error
      return new ServiceError('Request timeout', ServiceErrorType.TIMEOUT);
    } else {
      // Unknown error
      return new ServiceError(
        error.message || 'Unknown error occurred',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get cached data
   */
  protected async getCachedData<T>(key: string): Promise<T | null> {
    try {
      const cachedItem = await AsyncStorage.getItem(key);
      if (!cachedItem) return null;

      const { data, timestamp } = JSON.parse(cachedItem);

      // Check if cache has expired
      if (Date.now() - timestamp > this.config.cacheTimeout) {
        await AsyncStorage.removeItem(key);
        return null;
      }

      return data;
    } catch (error) {
      console.warn(`Failed to get cached data for ${key}:`, error);
      return null;
    }
  }

  /**
   * Set cached data
   */
  protected async setCachedData<T>(key: string, data: T): Promise<void> {
    try {
      const cacheItem = {
        data,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem(key, JSON.stringify(cacheItem));
    } catch (error) {
      console.warn(`Failed to cache data for ${key}:`, error);
      // Don't throw error for cache failures
    }
  }

  /**
   * Clear cache for this service
   */
  protected async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const serviceKeys = keys.filter((key) =>
        key.startsWith(`${this.serviceName}:`)
      );
      await AsyncStorage.multiRemove(serviceKeys);
    } catch (error) {
      console.warn(`Failed to clear cache for ${this.serviceName}:`, error);
    }
  }

  /**
   * Delay utility for retries
   */
  protected delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Check if device is online
   */
  protected async isOnline(): Promise<boolean> {
    try {
      // This would typically use NetInfo
      // For now, assume online
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Log service operations (can be extended for analytics)
   */
  protected log(
    level: 'info' | 'warn' | 'error',
    message: string,
    data?: any
  ): void {
    const logMessage = `[${this.serviceName}] ${message}`;

    switch (level) {
      case 'info':
        console.log(logMessage, data);
        break;
      case 'warn':
        console.warn(logMessage, data);
        break;
      case 'error':
        console.error(logMessage, data);
        break;
    }
  }
}

export default BaseService;
