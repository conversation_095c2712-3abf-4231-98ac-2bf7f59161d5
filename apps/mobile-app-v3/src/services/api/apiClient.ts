import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { getStoredToken } from '../data/storage';

// API configuration
const API_URL = 'https://api.qalbhealing.com/v1';
const LOCAL_API_URL = 'http://localhost:3333'; // Updated to match backend port

/**
 * Base API client for making HTTP requests to the backend
 */
class ApiClient {
  private api: AxiosInstance;
  private baseUrl: string;

  constructor(baseUrl: string = API_URL) {
    this.baseUrl = baseUrl;
    this.api = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Add request interceptor for authentication
    this.api.interceptors.request.use(
      async (config) => {
        const token = await getStoredToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          // Handle specific error status codes
          switch (error.response.status) {
            case 401:
              console.log('Unauthorized - Authentication required');
              // Handle authentication error (e.g., redirect to login)
              break;
            case 403:
              console.log('Forbidden - Insufficient permissions');
              break;
            case 404:
              console.log('Not found - Resource not available');
              break;
            case 500:
              console.log('Server error - Please try again later');
              break;
            default:
              console.log(`Error ${error.response.status}: ${error.response.data.message || 'Unknown error'}`);
          }
        } else if (error.request) {
          console.log('Network error - No response received');
        } else {
          console.log('Error setting up request:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a generic request to the API
   */
  async request<T>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.request(config);
  }

  /**
   * Make a GET request to the API
   */
  async get<T>(endpoint: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<{ status: string; data: T }> = await this.api.get(endpoint, config);
    return response.data.data;
  }

  /**
   * Make a POST request to the API
   */
  async post<T>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<{ status: string; data: T }> = await this.api.post(endpoint, data, config);
    return response.data.data;
  }

  /**
   * Make a PUT request to the API
   */
  async put<T>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<{ status: string; data: T }> = await this.api.put(endpoint, data, config);
    return response.data.data;
  }

  /**
   * Make a PATCH request to the API
   */
  async patch<T>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<{ status: string; data: T }> = await this.api.patch(endpoint, data, config);
    return response.data.data;
  }

  /**
   * Make a DELETE request to the API
   */
  async delete<T>(endpoint: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<{ status: string; data: T }> = await this.api.delete(endpoint, config);
    return response.data.data;
  }

  /**
   * Switch the API URL between production and local development
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
    this.api.defaults.baseURL = url;
  }

  /**
   * Use local development API
   */
  useLocalApi(): void {
    this.setBaseUrl(LOCAL_API_URL);
  }

  /**
   * Use production API
   */
  useProductionApi(): void {
    this.setBaseUrl(API_URL);
  }
}

// Export a singleton instance
const apiClient = new ApiClient();
export default apiClient;
