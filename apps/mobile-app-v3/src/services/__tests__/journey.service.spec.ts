import AsyncStorage from '@react-native-async-storage/async-storage';
import { journeyService } from '../journey.service'; // Using the exported singleton instance
import { API_BASE_URL } from '../../constants/Config'; // Assuming path
import { STORAGE_KEYS } from '../data/storage';
import { Journey } from '../../../../libs/shared-types/src/lib/journey.types';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

// Mock global fetch
global.fetch = jest.fn() as jest.Mock;

const mockAuthToken = 'test-auth-token';
const mockApiBaseUrl = API_BASE_URL; // Use the actual constant

const mockJourney: Journey = {
  id: 'journey-123',
  userId: 'user-abc',
  title: 'Test Healing Journey',
  description: 'A journey for testing.',
  type: 'custom', // JourneyType
  status: 'active', // JourneyStatus
  currentDay: 5,
  totalDays: 21,
  startedAt: new Date().toISOString(),
  completedAt: null,
  lastActivityAt: new Date().toISOString(),
  config: { duration: 21, primaryLayer: 'qalb' },
  days: [], // Array of JourneyDay
  // other fields as per Journey type
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

describe('JourneyService', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(mockAuthToken); // Default mock for auth token
  });

  describe('getCurrentJourney', () => {
    it('should fetch and return the current journey successfully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce({ journey: mockJourney }),
      });
      (AsyncStorage.setItem as jest.Mock).mockResolvedValueOnce(undefined); // For cacheJourney

      const result = await journeyService.getCurrentJourney();

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/current`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('currentJourney', JSON.stringify(mockJourney));
      expect(result).toEqual(mockJourney);
    });

    it('should return null if no active journey is found (404)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: jest.fn().mockResolvedValueOnce({ message: 'Not Found' }),
      });
      // Ensure cache is also empty for this test case or getItem for 'currentJourney' returns null
      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce(mockAuthToken) // For auth
        .mockResolvedValueOnce(null); // For getCachedJourney returning null

      const result = await journeyService.getCurrentJourney();

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(result).toBeNull();
    });

    it('should return cached journey if network request fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));
      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce(mockAuthToken) // For auth
        .mockResolvedValueOnce(JSON.stringify(mockJourney)); // For getCachedJourney

      const result = await journeyService.getCurrentJourney();

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(AsyncStorage.getItem).toHaveBeenCalledWith('currentJourney');
      expect(result).toEqual(mockJourney);
    });

    it('should return null if network request fails and no cached journey exists', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));
      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce(mockAuthToken) // For auth
        .mockResolvedValueOnce(null); // For getCachedJourney returning null

      const result = await journeyService.getCurrentJourney();

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(AsyncStorage.getItem).toHaveBeenCalledWith('currentJourney');
      expect(result).toBeNull();
    });
  });

  // TODO: Add tests for other methods like:
  // - createPersonalizedJourney
  // - startJourney
  // - getJourneyById
  // - recordDailyProgress
  // - getJourneyAnalytics
  // - etc.

  describe('getJourneyById', () => {
    const journeyId = 'specific-journey-456';
    const specificMockJourney = { ...mockJourney, id: journeyId, title: 'Specific Test Journey' };

    it('should fetch and return a specific journey by ID successfully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce({ journey: specificMockJourney }),
      });

      const result = await journeyService.getJourneyById(journeyId);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
      expect(result).toEqual(specificMockJourney);
    });

    it('should return null if the journey is not found (404)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: jest.fn().mockResolvedValueOnce({ message: 'Journey Not Found' }),
      });

      const result = await journeyService.getJourneyById(journeyId);

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(result).toBeNull();
    });

    it('should return null if there is a generic network error', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network connection failed'));
      // Mock console.error to suppress expected error logging during this test
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const result = await journeyService.getJourneyById(journeyId);

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to get journey by ID:', expect.any(Error));

      consoleErrorSpy.mockRestore(); // Restore console.error
    });

    it('should return null if response.ok is false but not 404 (e.g. 500)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Internal Server Error' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const result = await journeyService.getJourneyById(journeyId);

      expect(fetch).toHaveBeenCalledTimes(1);
      expect(result).toBeNull(); // Based on current implementation, catches error and returns null
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to get journey by ID:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('getJourneyCompletedMilestones', () => {
    const journeyId = 'journey-milestones-123';
    const mockMilestonesResponse = [
      { id: 'ms1', name: 'Milestone One', achievedDate: new Date().toISOString() },
      { id: 'ms2', name: 'Milestone Two', achievedDate: new Date().toISOString() },
    ];

    it('should fetch completed milestones successfully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce(mockMilestonesResponse), // Assuming API returns array directly
      });

      const result = await journeyService.getJourneyCompletedMilestones(journeyId);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}/completed-milestones`, {
        method: 'GET',
        headers: expect.objectContaining({ Authorization: `Bearer ${mockAuthToken}` }),
      });
      expect(result).toEqual(mockMilestonesResponse);
    });

    it('should throw an error if API call fails', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false, status: 500, json: jest.fn().mockResolvedValueOnce({ message: 'Server Error' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.getJourneyCompletedMilestones(journeyId))
        .rejects.toThrow('Unable to get journey milestones');
      expect(consoleErrorSpy).toHaveBeenCalledWith(`Failed to get completed milestones for journey ${journeyId}:`, expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should throw an error if network request fails', async () => {
        (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

        await expect(journeyService.getJourneyCompletedMilestones(journeyId))
          .rejects.toThrow('Unable to get journey milestones');

        expect(consoleErrorSpy).toHaveBeenCalledWith(`Failed to get completed milestones for journey ${journeyId}:`, expect.any(Error));
        consoleErrorSpy.mockRestore();
      });
  });

  describe('getJourneyProgressHistoryList', () => {
    const journeyId = 'journey-hist-123';
    const mockProgressList: JourneyProgress[] = [
      { id: 'ph1', journeyId, dayNumber: 1, mood: 7, reflectionText: 'Day 1 reflection', completedAt: new Date().toISOString() },
      { id: 'ph2', journeyId, dayNumber: 2, mood: 8, reflectionText: 'Day 2 reflection', completedAt: new Date().toISOString() },
    ];

    it('should fetch progress history list successfully without params', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce(mockProgressList), // Assuming API returns array directly if data.data is not used
      });

      const result = await journeyService.getJourneyProgressHistoryList(journeyId);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}/progress-history`, {
        method: 'GET',
        headers: expect.objectContaining({ Authorization: `Bearer ${mockAuthToken}` }),
      });
      expect(result).toEqual(mockProgressList);
    });

    it('should fetch progress history list with limit and offset params', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true, status: 200, json: jest.fn().mockResolvedValueOnce(mockProgressList),
      });

      await journeyService.getJourneyProgressHistoryList(journeyId, 10, 5);

      expect(fetch).toHaveBeenCalledWith(
        `${mockApiBaseUrl}/api/journey/${journeyId}/progress-history?limit=10&offset=5`,
        expect.any(Object)
      );
    });

    it('should fetch progress history list with only limit param', async () => {
        (fetch as jest.Mock).mockResolvedValueOnce({
          ok: true, status: 200, json: jest.fn().mockResolvedValueOnce(mockProgressList),
        });

        await journeyService.getJourneyProgressHistoryList(journeyId, 5);

        expect(fetch).toHaveBeenCalledWith(
          `${mockApiBaseUrl}/api/journey/${journeyId}/progress-history?limit=5`,
          expect.any(Object)
        );
      });

    it('should throw an error if API call fails', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false, status: 500, json: jest.fn().mockResolvedValueOnce({ message: 'Server Error' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.getJourneyProgressHistoryList(journeyId))
        .rejects.toThrow('Unable to get journey progress history');
      expect(consoleErrorSpy).toHaveBeenCalledWith(`Failed to get journey progress history for ${journeyId}:`, expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('clearJourneyCache', () => {
    it('should call AsyncStorage.removeItem for all relevant keys', async () => {
      (AsyncStorage.removeItem as jest.Mock).mockResolvedValue(undefined);

      await journeyService.clearJourneyCache();

      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('currentJourney');
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('journeyProgress');
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('journeyLastUpdated');
      expect(AsyncStorage.removeItem).toHaveBeenCalledTimes(3);
    });

    it('should log an error if AsyncStorage.removeItem fails for any key', async () => {
      const removeItemError = new Error('AsyncStorage failed');
      (AsyncStorage.removeItem as jest.Mock).mockRejectedValueOnce(removeItemError); // Fail for the first call

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await journeyService.clearJourneyCache();

      // It will attempt to remove all three, even if one fails, due to separate await calls.
      // The current implementation logs the error but doesn't re-throw.
      expect(AsyncStorage.removeItem).toHaveBeenCalledTimes(3);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error clearing journey cache:', removeItemError);

      consoleErrorSpy.mockRestore();
    });
  });

  describe('generatePersonalizedJourney', () => {
    const assessmentResults = {
      assessmentId: 'assessment-gen-123',
      keyFindings: { primaryLayer: 'qalb', severity: 'high' },
    };
    const generatedJourneyMock = { ...mockJourney, id: 'generated-journey-789', title: 'Generated Test Journey' };

    it('should successfully generate a personalized journey and cache it', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce(generatedJourneyMock), // Assuming API returns journey directly, not nested under 'journey' key like createPersonalizedJourney
      });
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const result = await journeyService.generatePersonalizedJourney(assessmentResults);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
        body: JSON.stringify({ assessmentResults }),
      });
      expect(result).toEqual(generatedJourneyMock);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('currentJourney', JSON.stringify(generatedJourneyMock));
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('journeyLastUpdated', expect.any(String));
    });

    it('should throw an error if API call fails', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Error generating journey' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.generatePersonalizedJourney(assessmentResults))
        .rejects.toThrow('Unable to generate personalized journey'); // The service method re-throws a generic error.

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to generate personalized journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('trackProgress (note similarity to recordDailyProgress)', () => {
    const progressData = { journeyId: 'journey-123', day: 5, mood: 'good' };
    // Assuming backend might return a simple success boolean or some status object
    const mockTrackProgressResponse = { success: true, message: "Progress tracked" };

    it('should successfully track progress and cache input data', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce(mockTrackProgressResponse),
      });
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce(mockAuthToken) // auth
        .mockResolvedValueOnce(JSON.stringify([])); // existing progress for cacheProgress

      const result = await journeyService.trackProgress(progressData);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
        body: JSON.stringify(progressData),
      });
      expect(result).toBe(true); // Based on `(result as any).success as boolean;`
      // Verifies that cacheProgress was called with the input data, not response data
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('journeyProgress', JSON.stringify([progressData]));
    });

    it('should throw an error if API call fails', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: jest.fn().mockResolvedValueOnce({ message: 'Invalid progress data' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.trackProgress(progressData))
        .rejects.toThrow('Unable to track progress'); // Service re-throws generic error

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to track progress:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('resumeJourney', () => {
    const journeyId = 'journey-to-resume-456';

    it('should successfully call the resume journey endpoint', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200, // Or 204
        json: jest.fn().mockResolvedValueOnce({}), // Assuming empty success response
      });

      await journeyService.resumeJourney(journeyId);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}/resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
    });

    it('should throw an error if API call fails (response not ok)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Server error resuming journey' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.resumeJourney(journeyId))
        .rejects.toThrow('Unable to resume journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to resume journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should throw an error if network request fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.resumeJourney(journeyId))
        .rejects.toThrow('Unable to resume journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to resume journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('pauseJourney', () => {
    const journeyId = 'journey-to-pause-123';
    const reason = 'Taking a short break';

    it('should successfully call the pause journey endpoint with a reason', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200, // Or 204 if no content
        json: jest.fn().mockResolvedValueOnce({}), // Assuming empty success response
      });

      await journeyService.pauseJourney(journeyId, reason);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}/pause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
        body: JSON.stringify({ reason }),
      });
    });

    it('should successfully call the pause journey endpoint without a reason', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce({}),
      });

      await journeyService.pauseJourney(journeyId); // No reason provided

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journeys/${journeyId}/pause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
        body: JSON.stringify({ reason: undefined }), // Service sends {reason: undefined}
      });
    });

    it('should throw an error if API call fails (response not ok)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Server error pausing journey' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.pauseJourney(journeyId, reason))
        .rejects.toThrow('Unable to pause journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to pause journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should throw an error if network request fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.pauseJourney(journeyId, reason))
        .rejects.toThrow('Unable to pause journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to pause journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('getJourneyAnalytics', () => {
    const journeyId = 'journey-analytics-123';
    const mockAnalyticsData: JourneyAnalytics = {
      journeyId: journeyId,
      completionRate: 75,
      practiceAdherence: 0.8,
      communityEngagement: 0.5,
      symptomImprovement: { primaryLayer: 0.6, secondaryLayers: { nafs: 0.5 }, overallWellness: 0.55 },
      spiritualDevelopment: { islamicPracticeIntegration: 0.7, spiritualConnection: 0.75, communityConnection: 0.4 },
      nextStepRecommendations: ['Continue daily reflections'],
      graduationReadiness: false,
    };
    const defaultAnalyticsData: JourneyAnalytics = {
        journeyId,
        completionRate: 0, practiceAdherence: 0, communityEngagement: 0,
        symptomImprovement: { primaryLayer: 0, secondaryLayers: {}, overallWellness: 0, },
        spiritualDevelopment: { islamicPracticeIntegration: 0, spiritualConnection: 0, communityConnection: 0, },
        nextStepRecommendations: [], graduationReadiness: false,
    };


    it('should fetch and return journey analytics successfully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce(mockAnalyticsData), // API returns the data directly
      });

      const result = await journeyService.getJourneyAnalytics(journeyId);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}/analytics`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
      expect(result).toEqual(mockAnalyticsData);
    });

    it('should return default analytics if API call fails (response not ok)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Server error fetching analytics' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const result = await journeyService.getJourneyAnalytics(journeyId);

      expect(fetch).toHaveBeenCalledTimes(1);
      // The service method catches the error and returns default analytics
      expect(result).toEqual(defaultAnalyticsData);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to get journey analytics:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should return default analytics if network request fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const result = await journeyService.getJourneyAnalytics(journeyId);

      expect(fetch).toHaveBeenCalledTimes(1);
      // The service method catches the error and returns default analytics
      expect(result).toEqual(defaultAnalyticsData);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to get journey analytics:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('getJourneyProgress', () => {
    const journeyId = 'journey-progress-789';
    const mockProgressHistoryResponse = {
      journeyId: journeyId,
      progressHistory: [
        { id: 'p1', dayNumber: 1, mood: 7, reflectionText: 'Day 1 done' },
        { id: 'p2', dayNumber: 2, mood: 8, reflectionText: 'Day 2 done' },
      ],
      summary: { totalDays: 21, averageRating: 7.5, completionRate: 10 },
    };

    it('should fetch and return journey progress history successfully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce(mockProgressHistoryResponse), // Assuming API returns the object directly
      });

      const result = await journeyService.getJourneyProgress(journeyId);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}/progress`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
      expect(result).toEqual(mockProgressHistoryResponse);
    });

    it('should throw an error if API call fails (response not ok)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Server error fetching progress' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.getJourneyProgress(journeyId))
        .rejects.toThrow('Unable to get journey progress');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to get journey progress:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should throw an error if network request fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.getJourneyProgress(journeyId))
        .rejects.toThrow('Unable to get journey progress');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to get journey progress:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('startJourney', () => {
    const journeyId = 'journey-to-start-789';
    const startedJourneyMock = { ...mockJourney, id: journeyId, status: 'active' as const, startedAt: new Date().toISOString() };

    it('should successfully start a journey and cache it', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce({ journey: startedJourneyMock }),
      });
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined); // For cacheJourney

      const result = await journeyService.startJourney(journeyId);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/${journeyId}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
      });
      expect(result).toEqual(startedJourneyMock);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('currentJourney', JSON.stringify(startedJourneyMock));
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('journeyLastUpdated', expect.any(String));
      expect(AsyncStorage.setItem).toHaveBeenCalledTimes(2);
    });

    it('should throw an error if API call to start journey fails (response not ok)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Server error starting journey' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.startJourney(journeyId))
        .rejects.toThrow('Unable to start journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to start journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should throw an error if network request fails when starting journey', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.startJourney(journeyId))
        .rejects.toThrow('Unable to start journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to start journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('createPersonalizedJourney', () => {
    const assessmentId = 'assessment-789';
    const preferences = { duration: 14, focus: 'qalb' };
    const newMockJourney = { ...mockJourney, id: 'new-journey-456', type: '14-day' as any };

    it('should successfully create a personalized journey and cache it', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce({ journey: newMockJourney }),
      });
      // Mock setItem for cacheJourney (called twice)
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const result = await journeyService.createPersonalizedJourney(assessmentId, preferences);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
        body: JSON.stringify({ assessmentId, preferences }),
      });
      expect(result).toEqual(newMockJourney);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('currentJourney', JSON.stringify(newMockJourney));
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('journeyLastUpdated', expect.any(String));
      expect(AsyncStorage.setItem).toHaveBeenCalledTimes(2); // currentJourney and journeyLastUpdated
    });

    it('should create journey with empty preferences if none provided', async () => {
        (fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: jest.fn().mockResolvedValueOnce({ journey: newMockJourney }),
        });
        (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

        await journeyService.createPersonalizedJourney(assessmentId); // No preferences

        expect(fetch).toHaveBeenCalledWith(
          `${mockApiBaseUrl}/api/journey/create`,
          expect.objectContaining({
            body: JSON.stringify({ assessmentId, preferences: {} }), // Ensure empty object is sent
          })
        );
      });

    it('should throw an error if API call fails (response not ok)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Server error creating journey' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.createPersonalizedJourney(assessmentId, preferences))
        .rejects.toThrow('Unable to create personalized journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to create personalized journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should throw an error if network request fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.createPersonalizedJourney(assessmentId, preferences))
        .rejects.toThrow('Unable to create personalized journey');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to create personalized journey:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });

  describe('recordDailyProgress', () => {
    const mockProgressDataInput = {
      journeyId: 'journey-123',
      dayNumber: 5,
      completedPractices: ['practice-1', 'practice-2'],
      mood: 8,
      reflectionText: 'Today was a good day.',
    };
    // Assuming 'id' is added by the backend upon creation
    const mockProgressResponse: JourneyProgress = {
      id: 'progress-xyz',
      ...mockProgressDataInput,
      completedAt: new Date().toISOString(),
      voiceNoteUrl: null,
      voiceNoteDuration: 0
    };

    it('should successfully record daily progress and cache it', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValueOnce({ progress: mockProgressResponse }),
      });
      (AsyncStorage.setItem as jest.Mock).mockResolvedValueOnce(undefined); // For cacheProgress
      (AsyncStorage.getItem as jest.Mock) // For existing progress in cacheProgress
        .mockResolvedValueOnce(mockAuthToken) // For auth header
        .mockResolvedValueOnce(JSON.stringify([])); // No existing progress

      const result = await journeyService.recordDailyProgress(mockProgressDataInput);

      expect(fetch).toHaveBeenCalledWith(`${mockApiBaseUrl}/api/journey/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockAuthToken}`,
        },
        body: JSON.stringify(mockProgressDataInput),
      });
      expect(result).toEqual(mockProgressResponse);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('journeyProgress', JSON.stringify([mockProgressResponse]));
    });

    it('should throw an error if API call fails (response not ok)', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValueOnce({ message: 'Server error while recording progress' }),
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.recordDailyProgress(mockProgressDataInput))
        .rejects.toThrow('Unable to record daily progress');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to record daily progress:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });

    it('should throw an error if network request fails', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failed'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await expect(journeyService.recordDailyProgress(mockProgressDataInput))
        .rejects.toThrow('Unable to record daily progress');

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to record daily progress:', expect.any(Error));
      consoleErrorSpy.mockRestore();
    });
  });
});
