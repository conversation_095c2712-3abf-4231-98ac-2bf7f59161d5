/**
 * Assessment Service for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Frontend service for spiritual assessment and diagnosis
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../constants/Config';
import { getStoredUserData, STORAGE_KEYS } from './data/storage';

interface AssessmentSession {
  id: string;
  userId: string;
  userProfile: any;
  startedAt: Date;
  completedAt?: Date;
  currentStep: string;
  totalSteps: number;
  physicalExperiences: SymptomCategory;
  emotionalExperiences: SymptomCategory;
  mentalExperiences: SymptomCategory;
  spiritualExperiences: SymptomCategory;
  reflections: Record<string, string>;
  timeSpentPerStep: Record<string, number>;
  totalTimeSpent: number;
  diagnosis?: SpiritualDiagnosis;
}

interface PersonalizedWelcome {
  // Updated to align with backend PersonalizedWelcome interface
  userId: string;
  userType: string;
  greeting: string;
  introduction: string;
  explanation: string;
  motivation: string;
  primaryAction: {
    id: string;
    text: string;
    description?: string;
  };
  secondaryActions: {
    id: string;
    text: string;
    description?: string;
  }[];
}

interface AssessmentCompletedResponse {
  assessmentStatus: 'completed';
  lastCompletedAt: string;
  sessionId: string;
  message: string;
  actions: {
    id: string;
    text: string;
    description: string;
  }[];
}

type WelcomeResponse = PersonalizedWelcome | AssessmentCompletedResponse;

interface AssessmentQuestion {
  id: string;
  category: 'physical' | 'emotional' | 'mental' | 'spiritual';
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  title: string;
  description?: string;
  symptoms: AssessmentSymptom[];
  reflectionPrompt?: string;
  reflectionRequired: boolean;
  allowMultipleSelection: boolean;
  intensityScale: boolean;
  customInputAllowed: boolean;
}

interface AssessmentSymptom {
  id: string;
  text: string;
  description?: string;
  severity: 'mild' | 'moderate' | 'severe';
  islamicContext?: string;
  primaryLayer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  secondaryLayers?: ('jism' | 'nafs' | 'aql' | 'qalb' | 'ruh')[];
}

interface SymptomCategory {
  id: string;
  name: string;
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  symptoms: string[];
  intensity: 'mild' | 'moderate' | 'severe';
  userReflection?: string;
}

interface SpiritualDiagnosis {
  id: string;
  userId: string;
  assessmentId: string;
  primaryLayer: LayerAnalysis;
  secondaryLayers: LayerAnalysis[];
  overallSeverity: 'mild' | 'moderate' | 'severe' | 'critical';
  personalizedMessage: string;
  islamicInsights: string[];
  educationalContent: string;
  crisisLevel: 'none' | 'low' | 'moderate' | 'high' | 'critical';
  crisisIndicators: string[];
  immediateActions: string[];
  nextSteps: string[];
  recommendedJourneyType: string;
  estimatedHealingDuration: number;
  confidence: number;
  generatedAt: Date;
}

interface LayerAnalysis {
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  layerName: string;
  impactScore: number;
  affectedSymptoms: string[];
  insights: string[];
  recommendations: string[];
  islamicContext: string;
  priority: 'primary' | 'secondary' | 'tertiary';
}

interface DiagnosisDelivery {
  userId: string;
  userType: string;
  diagnosis: SpiritualDiagnosis;
  deliveryStyle: 'gentle' | 'clinical' | 'advanced' | 'traditional';
  layerIntroduction: string;
  primaryLayerAnalysis: string;
  secondaryLayerConnections: string;
  islamicInsights: string;
  practicalImplications: string;
  layerEducation: {
    jism: string;
    nafs: string;
    aql: string;
    qalb: string;
    ruh: string;
  };
  nextStepsGuidance: string;
  journeyRecommendation: string;
  additionalResources: string[];
}

class AssessmentServiceClass {
  private baseUrl = `${API_BASE_URL}/api/assessment`;

  /**
   * Get authentication headers
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    const data = await response.json();
    // Check if response has the standard format with status and data
    if (data.status === 'success' && data.data) {
      return data.data;
    }
    // Fallback to data.data or data for backward compatibility
    return data.data || data;
  }

  /**
   * Get personalized welcome content
   */
  async getPersonalizedWelcome(): Promise<WelcomeResponse> {
    try {
      const headers = await this.getAuthHeaders();
      const userProfile = await this.getUserProfile();

      const response = await fetch(
        `${this.baseUrl}/welcome`,
        {
          method: 'POST',
          headers,
          body: JSON.stringify(userProfile), // Send userProfile directly
        }
      );

      const result = await this.handleResponse<any>(response);
      
      // Check if this is a completed assessment response
      if (result.assessmentStatus === 'completed') {
        return result as AssessmentCompletedResponse;
      } else {
        // Return the welcome object
        return result.welcome as PersonalizedWelcome;
      }
    } catch (error) {
      console.error('Error getting personalized welcome:', error);
      throw error;
    }
  }

  /**
   * Start a new assessment session
   */
  async startAssessment(): Promise<{
    session: AssessmentSession;
    welcome: PersonalizedWelcome;
  }> {
    try {
      const headers = await this.getAuthHeaders();
      const userProfile = await this.getUserProfile();

      const response = await fetch(`${this.baseUrl}/start`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ userProfile }),
      });

      const result = await this.handleResponse<{
        session: AssessmentSession;
        welcome: PersonalizedWelcome;
        message: string;
      }>(response);

      // Clear local progress for the new session to ensure a clean start
      await this.clearProgressLocally(result.session.id);

      return { session: result.session, welcome: result.welcome };
    } catch (error) {
      console.error('Error starting assessment:', error);
      throw error;
    }
  }

  /**
   * Get assessment questions for a specific step
   */
  async getAssessmentQuestions(
    sessionId: string,
    step: string
  ): Promise<AssessmentQuestion[]> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(
        `${this.baseUrl}/${sessionId}/questions/${step}`,
        {
          method: 'GET',
          headers,
        }
      );

      const result = await this.handleResponse<{
        questions: AssessmentQuestion[];
      }>(response);
      return result.questions;
    } catch (error) {
      console.error('Error getting assessment questions:', error);
      throw error;
    }
  }

  /**
   * Submit assessment response
   */
  async submitAssessmentResponse(
    sessionId: string,
    step: string,
    responses: SymptomCategory,
    timeSpent: number
  ): Promise<{
    nextStep: string | null;
    progress: number;
    crisisDetected?: boolean;
    crisisLevel?: string;
    message?: string;
    emergencyActions?: any[];
  }> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/${sessionId}/submit`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          step,
          responses,
          timeSpent,
        }),
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error submitting assessment response:', error);
      throw error;
    }
  }

  /**
   * Get spiritual diagnosis results
   */
  async getDiagnosis(sessionId: string): Promise<{
    diagnosis: SpiritualDiagnosis;
    delivery: DiagnosisDelivery;
  }> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/${sessionId}/diagnosis`, {
        method: 'GET',
        headers,
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error getting diagnosis:', error);
      throw error;
    }
  }

  /**
   * Force generate diagnosis for a completed session
   */
  async forceGenerateDiagnosis(sessionId: string): Promise<{
    diagnosis: SpiritualDiagnosis;
    delivery: DiagnosisDelivery;
  }> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(
        `${this.baseUrl}/${sessionId}/generate-diagnosis`,
        {
          method: 'POST',
          headers,
        }
      );

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error force generating diagnosis:', error);
      throw error;
    }
  }

  /**
   * Submit diagnosis feedback
   */
  async submitDiagnosisFeedback(
    diagnosisId: string,
    feedback: {
      accuracy: number;
      helpfulness: number;
      comments?: string;
    }
  ): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(
        `${this.baseUrl}/diagnosis/${diagnosisId}/feedback`,
        {
          method: 'POST',
          headers,
          body: JSON.stringify(feedback),
        }
      );

      await this.handleResponse(response);
    } catch (error) {
      console.error('Error submitting diagnosis feedback:', error);
      throw error;
    }
  }

  /**
   * Get assessment session details
   */
  async getSession(sessionId: string): Promise<AssessmentSession> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/session/${sessionId}`, {
        method: 'GET',
        headers,
      });

      const result = await this.handleResponse<{ session: AssessmentSession }>(
        response
      );
      return result.session;
    } catch (error) {
      console.error('Error getting session:', error);
      throw error;
    }
  }

  /**
   * Update an existing assessment session
   */
  async updateSession(
    sessionId: string,
    data: Partial<AssessmentSession>
  ): Promise<{ session: AssessmentSession }> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/session/${sessionId}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data),
      });

      const result = await this.handleResponse<{ session: AssessmentSession }>(
        response
      );
      return result;
    } catch (error) {
      console.error('Error updating session:', error);
      throw error;
    }
  }

  /**
   * Get user's assessment history
   */
  async getAssessmentHistory(
    page: number = 1,
    limit: number = 10
  ): Promise<{
    sessions: AssessmentSession[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(
        `${this.baseUrl}/history?page=${page}&limit=${limit}`,
        {
          method: 'GET',
          headers,
        }
      );

      const result = await this.handleResponse<{ history: any }>(response);
      return result.history;
    } catch (error) {
      console.error('Error getting assessment history:', error);
      throw error;
    }
  }

  /**
   * Resume incomplete assessment
   */
  async resumeAssessment(sessionId: string): Promise<{
    session: AssessmentSession;
    questions: AssessmentQuestion[];
  }> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/resume/${sessionId}`, {
        method: 'POST',
        headers,
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error resuming assessment:', error);
      throw error;
    }
  }

  /**
   * Abandon assessment session
   */
  async abandonAssessment(sessionId: string, reason?: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/${sessionId}/abandon`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ reason }),
      });

      await this.handleResponse(response);
    } catch (error) {
      console.error('Error abandoning assessment:', error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private async getUserProfile(): Promise<any> {
    try {
      const userData = await getStoredUserData();

      // Combine user basic info with profile data
      const userProfile = {
        ...(userData?.profile || {}),
        // Add user's name information to the profile
        firstName: userData?.firstName,
        lastName: userData?.lastName,
        fullName:
          userData?.firstName && userData?.lastName
            ? `${userData.firstName} ${userData.lastName}`
            : userData?.firstName || '',
        name: userData?.firstName || '', // Add a simple 'name' field for easy access
        userId: userData?.id,
        email: userData?.email,
      };

      return userProfile;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return {};
    }
  }

  /**
   * Save assessment progress locally
   */
  async saveProgressLocally(sessionId: string, progress: any): Promise<void> {
    try {
      const key = `assessment_progress_${sessionId}`;
      await AsyncStorage.setItem(
        key,
        JSON.stringify({
          ...progress,
          lastSaved: new Date().toISOString(),
        })
      );
    } catch (error) {
      console.error('Error saving progress locally:', error);
    }
  }

  /**
   * Load assessment progress from local storage
   */
  async loadProgressLocally(sessionId: string): Promise<any | null> {
    try {
      const key = `assessment_progress_${sessionId}`;
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error loading progress locally:', error);
      return null;
    }
  }

  /**
   * Clear local assessment progress
   */
  async clearProgressLocally(sessionId: string): Promise<void> {
    try {
      const key = `assessment_progress_${sessionId}`;
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Error clearing progress locally:', error);
    }
  }

  /**
   * Check for incomplete assessments
   */
  async getIncompleteAssessments(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const assessmentKeys = keys.filter((key) =>
        key.startsWith('assessment_progress_')
      );

      const incompleteSessionIds: string[] = [];

      for (const key of assessmentKeys) {
        try {
          const data = await AsyncStorage.getItem(key);
          if (data) {
            const progress = JSON.parse(data);
            const sessionId = key.replace('assessment_progress_', '');

            // Check if assessment was completed
            if (!progress.completed) {
              incompleteSessionIds.push(sessionId);
            }
          }
        } catch (err) {
          console.error('Error checking incomplete assessment:', err);
        }
      }

      return incompleteSessionIds;
    } catch (error) {
      console.error('Error getting incomplete assessments:', error);
      return [];
    }
  }

  /**
   * Validate assessment responses
   */
  validateResponses(
    step: string,
    responses: SymptomCategory
  ): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Basic validation
    if (!responses.symptoms || responses.symptoms.length === 0) {
      if (step !== 'reflections') {
        errors.push('Please select at least one symptom or experience');
      }
    }

    if (!responses.intensity) {
      errors.push('Please select an intensity level');
    }

    // Step-specific validation
    switch (step) {
      case 'physical_experiences':
        if (responses.symptoms.length === 0) {
          errors.push('Please select your physical experiences');
        }
        break;
      case 'emotional_experiences':
        if (responses.symptoms.length === 0) {
          errors.push('Please select your emotional experiences');
        }
        break;
      case 'mental_experiences':
        if (responses.symptoms.length === 0) {
          errors.push('Please select your mental experiences');
        }
        break;
      case 'spiritual_experiences':
        if (responses.symptoms.length === 0) {
          errors.push('Please select your spiritual experiences');
        }
        break;
      case 'reflections':
        if (
          !responses.userReflection ||
          responses.userReflection.trim().length < 10
        ) {
          errors.push('Please share your reflections (at least 10 characters)');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Generate analysis from symptoms
   */
  async generateAnalysis(symptoms: string[]): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/analyze`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ symptoms }),
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error generating analysis:', error);
      throw error;
    }
  }

  /**
   * Detect crisis during assessment
   */
  async detectCrisisDuringAssessment(symptoms: string[]): Promise<boolean> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(
        `${API_BASE_URL}/api/crisis/detect-assessment`,
        {
          method: 'POST',
          headers,
          body: JSON.stringify({ symptoms }),
        }
      );

      const result = await this.handleResponse<{ crisisDetected: boolean }>(
        response
      );
      return result.crisisDetected;
    } catch (error) {
      console.error('Failed to detect crisis during assessment:', error);
      return false;
    }
  }

  /**
   * Categorize symptom by Islamic soul layer
   */
  async categorizeSymptom(symptom: string): Promise<string> {
    try {
      // Simple categorization logic based on symptom keywords
      const lowerSymptom = symptom.toLowerCase();

      if (
        lowerSymptom.includes('physical') ||
        lowerSymptom.includes('pain') ||
        lowerSymptom.includes('fatigue')
      ) {
        return 'Jism'; // Physical layer
      } else if (
        lowerSymptom.includes('emotional') ||
        lowerSymptom.includes('anger') ||
        lowerSymptom.includes('sadness')
      ) {
        return 'Nafs'; // Emotional layer
      } else if (
        lowerSymptom.includes('mental') ||
        lowerSymptom.includes('concentration') ||
        lowerSymptom.includes('memory')
      ) {
        return 'Aql'; // Mental layer
      } else if (
        lowerSymptom.includes('spiritual') ||
        lowerSymptom.includes('prayer') ||
        lowerSymptom.includes('faith')
      ) {
        return 'Qalb'; // Spiritual layer
      } else if (
        lowerSymptom.includes('transcendent') ||
        lowerSymptom.includes('divine') ||
        lowerSymptom.includes('connection')
      ) {
        return 'Ruh'; // Transcendent layer
      } else {
        return 'Nafs'; // Default to emotional layer
      }
    } catch (error) {
      console.error('Failed to categorize symptom:', error);
      return 'Nafs';
    }
  }

  /**
   * Generate personalized analysis based on user persona
   */
  async generatePersonalizedAnalysis(
    symptoms: string[],
    user: any
  ): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/personalized-analysis`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ symptoms, user }),
      });

      const analysis = await this.handleResponse(response);

      // Determine language style based on user persona
      let language = 'traditional_islamic';
      if (
        user.profession === 'Healthcare Professional' ||
        user.profession === 'healthcare' ||
        user.profession === 'therapist'
      ) {
        language = 'clinical_islamic';
      } else if (user.islamicBackground === 'new_muslim') {
        language = 'gentle_introduction';
      } else if (user.islamicBackground === 'traditional') {
        language = 'gentle_traditional';
      }

      return {
        ...(analysis as any),
        language,
      } as any;
    } catch (error) {
      console.error('Failed to generate personalized analysis:', error);
      throw error;
    }
  }

  /**
   * Preserve assessment data during crisis
   */
  async preserveAssessmentData(assessmentData: any): Promise<void> {
    try {
      await AsyncStorage.setItem(
        'assessment_crisis_backup',
        JSON.stringify(assessmentData)
      );
    } catch (error) {
      console.error('Failed to preserve assessment data:', error);
      throw error;
    }
  }
}

export const assessmentService = new AssessmentServiceClass();
