/**
 * Journey Service for Feature 2: Personalized Healing Journeys
 * Frontend service for journey management and progress tracking
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../constants/Config';
import { STORAGE_KEYS } from './data/storage';
import {
  Journey,
  JourneyProgress,
  JourneyAnalytics,
  JourneyConfig,
  DailyPractice, // Added for Journey.days.practices type
  JourneyDay,    // Added for Journey.days type
  // Enums can be imported if needed for explicit casting or utility functions
  // e.g., PracticeType, LayerFocus, JourneyStatus, JourneyType
} from '../../../libs/shared-types/src/lib/journey.types'; // Corrected relative path

// Type for preferences in createPersonalizedJourney, using JourneyConfig from shared-types
type JourneyCreationPreferences = Partial<JourneyConfig>;


class JourneyServiceClass {
  private baseUrl = `${API_BASE_URL}/api/journey`;

  /**
   * Get authentication headers
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log('🔍 Raw response data in handleResponse:', data);
    
    // Return the data property if it exists, otherwise return the full response
    return (data.data || data) as T;
  }

  /**
   * Create a personalized healing journey
   */
  async createPersonalizedJourney(
    assessmentId: string,
    preferences?: any
  ): Promise<Journey> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/create`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          assessmentId,
          preferences: preferences || {},
        }),
      });

      const result = await this.handleResponse<{ journey: Journey }>(response);

      // Cache journey locally
      await this.cacheJourney(result.journey);

      return result.journey;
    } catch (error) {
      console.error('Failed to create personalized journey:', error);
      throw new Error('Unable to create personalized journey');
    }
  }

  /**
   * Start a journey
   */
  async startJourney(journeyId: string): Promise<Journey> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/${journeyId}/start`, {
        method: 'POST',
        headers,
      });

      const result = await this.handleResponse<{ journey: Journey }>(response);

      // Update cached journey
      await this.cacheJourney(result.journey);

      return result.journey;
    } catch (error) {
      console.error('Failed to start journey:', error);
      throw new Error('Unable to start journey');
    }
  }

  /**
   * Get current active journey (lightweight)
   */
  async getCurrentJourney(): Promise<Journey | null> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/current`, {
        method: 'GET',
        headers,
      });

      if (response.status === 404) {
        return null;
      }

      const result = await this.handleResponse<{ journey: Journey }>(response);

      // Cache journey locally
      await this.cacheJourney(result.journey);

      return result.journey;
    } catch (error) {
      console.error('Failed to get current journey:', error);

      // Try to get from cache if network fails
      try {
        const cachedJourney = await this.getCachedJourney();
        return cachedJourney;
      } catch (cacheError) {
        console.error('Failed to get cached journey:', cacheError);
        return null;
      }
    }
  }

  /**
   * Get current active journey with full details (heavy)
   */
  async getCurrentJourneyWithDetails(): Promise<Journey | null> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/current/details`, {
        method: 'GET',
        headers,
      });

      if (response.status === 404) {
        return null;
      }

      const result = await this.handleResponse<{ journey: Journey }>(response);
      return result.journey;
    } catch (error) {
      console.error('Failed to get current journey with details:', error);
      return null;
    }
  }

  /**
   * Get journey by ID
   */
  async getJourneyById(journeyId: string): Promise<Journey | null> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/${journeyId}`, {
        method: 'GET',
        headers,
      });

      if (response.status === 404) {
        return null;
      }

      const result = await this.handleResponse<{ journey: Journey }>(response);
      return result.journey;
    } catch (error) {
      console.error('Failed to get journey by ID:', error);
      return null;
    }
  }

  /**
   * Record daily progress
   */
  async recordDailyProgress(
    progressData: Omit<JourneyProgress, 'id'>
  ): Promise<JourneyProgress> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/progress`, {
        method: 'POST',
        headers,
        body: JSON.stringify(progressData),
      });

      const result = await this.handleResponse<{ progress: JourneyProgress }>(
        response
      );

      // Cache progress locally
      await this.cacheProgress(result.progress);

      return result.progress;
    } catch (error) {
      console.error('Failed to record daily progress:', error);
      throw new Error('Unable to record daily progress');
    }
  }

  /**
   * Get journey progress history
   */
  async getJourneyProgress(journeyId: string): Promise<{
    journeyId: string;
    progressHistory: JourneyProgress[];
    summary: {
      totalDays: number;
      averageRating: number;
      completionRate: number;
    };
  }> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/${journeyId}/progress`, {
        method: 'GET',
        headers,
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Failed to get journey progress:', error);
      throw new Error('Unable to get journey progress');
    }
  }

  /**
   * Get journey analytics
   */
  async getJourneyAnalytics(journeyId: string): Promise<JourneyAnalytics> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/${journeyId}/analytics`, {
        method: 'GET',
        headers,
      });

      // Assuming the backend returns the JourneyAnalytics object directly.
      // The handleResponse method will return data.data if it exists, otherwise data.
      // If backend returns { ...analyticsData }, then handleResponse returns that.
      const analyticsData = await this.handleResponse<JourneyAnalytics>(
        response
      );
      return analyticsData;
    } catch (error) {
      console.error('Failed to get journey analytics:', error);
      // Return default analytics
      return {
        journeyId,
        completionRate: 0,
        practiceAdherence: 0,
        communityEngagement: 0,
        symptomImprovement: {
          primaryLayer: 0,
          secondaryLayers: {},
          overallWellness: 0,
        },
        spiritualDevelopment: {
          islamicPracticeIntegration: 0,
          spiritualConnection: 0,
          communityConnection: 0,
        },
        nextStepRecommendations: [],
        graduationReadiness: false,
      };
    }
  }

  /**
   * Pause a journey
   */
  async pauseJourney(journeyId: string, reason?: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      await fetch(`${this.baseUrl}/${journeyId}/pause`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ reason }),
      });
    } catch (error) {
      console.error('Failed to pause journey:', error);
      throw new Error('Unable to pause journey');
    }
  }

  /**
   * Resume a paused journey
   */
  async resumeJourney(journeyId: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      await fetch(`${this.baseUrl}/${journeyId}/resume`, {
        method: 'POST',
        headers,
      });
    } catch (error) {
      console.error('Failed to resume journey:', error);
      throw new Error('Unable to resume journey');
    }
  }

  /**
   * Cache journey locally
   */
  private async cacheJourney(journey: Journey): Promise<void> {
    try {
      await AsyncStorage.setItem('currentJourney', JSON.stringify(journey));
      await AsyncStorage.setItem(
        'journeyLastUpdated',
        new Date().toISOString()
      );
    } catch (error) {
      console.error('Error caching journey:', error);
    }
  }

  /**
   * Get cached journey
   */
  private async getCachedJourney(): Promise<Journey | null> {
    try {
      const cachedJourney = await AsyncStorage.getItem('currentJourney');
      return cachedJourney ? JSON.parse(cachedJourney) : null;
    } catch (error) {
      console.error('Error getting cached journey:', error);
      return null;
    }
  }

  /**
   * Cache progress locally
   */
  private async cacheProgress(progress: JourneyProgress): Promise<void> {
    try {
      const existingProgress = await AsyncStorage.getItem('journeyProgress');
      const progressArray = existingProgress
        ? JSON.parse(existingProgress)
        : [];

      // Add or update progress
      const existingIndex = progressArray.findIndex(
        (p: JourneyProgress) =>
          p.journeyId === progress.journeyId &&
          p.dayNumber === progress.dayNumber
      );

      if (existingIndex >= 0) {
        progressArray[existingIndex] = progress;
      } else {
        progressArray.push(progress);
      }

      await AsyncStorage.setItem(
        'journeyProgress',
        JSON.stringify(progressArray)
      );
    } catch (error) {
      console.error('Error caching progress:', error);
    }
  }

  /**
   * Clear journey cache
   */
  async clearJourneyCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem('currentJourney');
      await AsyncStorage.removeItem('journeyProgress');
      await AsyncStorage.removeItem('journeyLastUpdated');
    } catch (error) {
      console.error('Error clearing journey cache:', error);
    }
  }

  /**
   * Generate personalized journey based on assessment
   */
  async generatePersonalizedJourney(assessmentResults: any): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ assessmentResults }),
      });

      const journey: Journey = await this.handleResponse(response);

      // Cache the generated journey
      await this.cacheJourney(journey);

      return journey;
    } catch (error) {
      console.error('Failed to generate personalized journey:', error);
      throw error;
    }
  }

  /**
   * Track progress with mood and spiritual metrics
   */
  async trackProgress(progressData: any): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/progress`, {
        method: 'POST',
        headers,
        body: JSON.stringify(progressData),
      });

      const result = await this.handleResponse(response);

      // Cache progress locally
      await this.cacheProgress(progressData);

      return (result as any).success as boolean;
    } catch (error) {
      console.error('Failed to track progress:', error);
      throw error;
    }
  }

  /**
   * Find appropriate peer support group
   */
  async findPeerSupportGroup(userCriteria: any): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/peer-support`, {
        method: 'POST',
        headers,
        body: JSON.stringify(userCriteria),
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Failed to find peer support group:', error);
      throw error;
    }
  }

  /**
   * Preserve journey context during crisis
   */
  async preserveJourneyContext(journeyContext: any): Promise<void> {
    try {
      await AsyncStorage.setItem(
        'journey_crisis_context',
        JSON.stringify(journeyContext)
      );
    } catch (error) {
      console.error('Failed to preserve journey context:', error);
      throw error;
    }
  }

  /**
   * Get all progress records for a specific journey.
   * It expects the backend to return an array of JourneyProgress items.
   */
  async getJourneyProgressHistoryList(journeyId: string, limit?: number, offset?: number): Promise<JourneyProgress[]> {
    try {
      const headers = await this.getAuthHeaders();
      let url = `${this.baseUrl}/${journeyId}/progress-history`;
      const queryParams = [];
      if (limit !== undefined) queryParams.push(`limit=${limit}`);
      if (offset !== undefined) queryParams.push(`offset=${offset}`);
      if (queryParams.length > 0) url += `?${queryParams.join('&')}`;

      const response = await fetch(url, { method: 'GET', headers });
      // The handleResponse extracts data.data if it exists, or data directly.
      // Assuming the backend for this endpoint returns { status: 'success', data: JourneyProgress[] }
      return await this.handleResponse<JourneyProgress[]>(response);
    } catch (error) {
      console.error(`Failed to get journey progress history for ${journeyId}:`, error);
      throw new Error('Unable to get journey progress history');
    }
  }

  /**
   * Get completed milestones for a specific journey.
   * Expected backend response: { status: 'success', data: DisplayMilestone[] }
   */
  async getJourneyCompletedMilestones(journeyId: string): Promise<any[]> { // Use 'any[]' or define DisplayMilestone in shared-types
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/${journeyId}/completed-milestones`, {
        method: 'GET',
        headers,
      });
      return await this.handleResponse<any[]>(response);
    } catch (error) {
      console.error(`Failed to get completed milestones for journey ${journeyId}:`, error);
      throw new Error('Unable to get journey milestones');
    }
  }

  /**
   * Get journey resources with optional filters
   */
  async getJourneyResources(queryParams?: string): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const url = queryParams ? `${this.baseUrl}/resources?${queryParams}` : `${this.baseUrl}/resources`;
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Failed to get journey resources:', error);
      throw new Error('Unable to get journey resources');
    }
  }

  /**
   * Get individual resource by ID
   */
  async getResourceById(resourceId: string): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/resources/${resourceId}`, {
        method: 'GET',
        headers,
      });
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Failed to get resource by ID:', error);
      throw new Error('Unable to get resource');
    }
  }

}

// Export singleton instance
export const journeyService = new JourneyServiceClass();
export default journeyService;
