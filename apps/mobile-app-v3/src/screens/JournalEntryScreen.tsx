import { Feather } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Switch,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  CategorySelector,
  JournalCategory,
} from '../components/CategorySelector';
import { EmotionPicker } from '../components/EmotionPicker';
import { Button } from '../components/ui/Button';
import { Text } from '../components/ui/Text';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';
// import { useColorScheme } from "../hooks/useColorScheme";
import {
  RootStackNavigationProp,
  RootStackParamList,
} from '../navigation/types';
import { JournalEntry } from '../services/api/types';
// Mock useJournal hook
const useJournal = () => ({
  state: { entries: [], loading: false, error: null },
  createEntry: async (entry: any) => console.log('Create entry:', entry),
  updateEntry: async (id: string, entry: any) =>
    console.log('Update entry:', id, entry),
  deleteEntry: async (id: string) => console.log('Delete entry:', id),
  getEntryById: (id: string) => ({
    id,
    title: 'Sample Entry',
    content: 'Sample content',
    category: 'reflection',
    emotions: ['peaceful'],
    isPrivate: false,
    entryType: 'reflection',
    tags: [],
    layers: [],
    createdAt: new Date().toISOString(),
  }),
});

export default function JournalEntryScreen() {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();

  const insets = useSafeAreaInsets();

  // Journal context
  const {
    state: journalState,
    createEntry,
    updateEntry,
    deleteEntry,
    getEntryById,
  } = useJournal();

  // Get entry ID from route params (if editing)
  const entryId = route.params?.entryId;
  const isEditing = !!entryId;

  // Input refs
  const contentInputRef = useRef<TextInput>(null);

  // State
  const [isLoading, setIsLoading] = useState(isEditing);
  const [isSaving, setIsSaving] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState<JournalCategory>('general');
  const [emotions, setEmotions] = useState<string[]>([]);
  const [isPrivate, setIsPrivate] = useState(true);
  const [originalEntry, setOriginalEntry] = useState<JournalEntry | null>(null);

  // Load entry data if editing
  useEffect(() => {
    if (isEditing) {
      loadEntryData();
    }
  }, [isEditing]);

  // Load entry data from context
  const loadEntryData = async () => {
    if (!entryId) {
      return;
    }

    try {
      setIsLoading(true);
      const entry = await getEntryById(entryId);

      if (entry && typeof entry === 'object') {
        // Set form values
        setTitle(entry.title);
        setContent(entry.content);
        setCategory(entry.category as any);
        setEmotions(entry.emotions || []);
        setIsPrivate(entry.isPrivate);
        setOriginalEntry(entry as any);
      }
    } catch (error) {
      console.error('Error loading entry:', error);
      Alert.alert('Error', 'Failed to load journal entry. Please try again.', [
        { text: 'OK', onPress: () => navigation.goBack() },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle save
  const handleSave = async () => {
    // Validate form
    if (!title.trim()) {
      Alert.alert(
        'Title Required',
        'Please enter a title for your journal entry.'
      );
      return;
    }

    if (!content.trim()) {
      Alert.alert(
        'Content Required',
        'Please enter some content for your journal entry.'
      );
      return;
    }

    try {
      setIsSaving(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const entryData = {
        title: title.trim(),
        content: content.trim(),
        category,
        emotions,
        isPrivate,
        tags: extractTags(content),
      };

      if (isEditing && entryId) {
        // Update existing entry
        await updateEntry(entryId, entryData);
      } else {
        // Create new entry
        await createEntry(entryData);
      }

      navigation.goBack();
    } catch (error) {
      console.error('Error saving entry:', error);
      Alert.alert('Error', 'Failed to save journal entry. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle delete
  const handleDelete = () => {
    if (!isEditing || !entryId) {
      return;
    }

    Alert.alert(
      'Delete Entry',
      'Are you sure you want to delete this journal entry? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsSaving(true);
              Haptics.notificationAsync(
                Haptics.NotificationFeedbackType.Warning
              );
              await deleteEntry(entryId);
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting entry:', error);
              Alert.alert(
                'Error',
                'Failed to delete journal entry. Please try again.'
              );
              setIsSaving(false);
            }
          },
        },
      ]
    );
  };

  // Extract hashtags from content
  const extractTags = (text: string): string[] => {
    const tags: string[] = [];
    const regex = /#(\w+)/g;
    let match;

    while ((match = regex.exec(text)) !== null) {
      tags.push(match[1].toLowerCase());
    }

    return [...new Set(tags)]; // Remove duplicates
  };

  // Handle category selection
  const handleCategorySelect = (selectedCategory: JournalCategory) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCategory(selectedCategory);
  };

  // Handle emotion selection
  const handleEmotionSelect = (emotion: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setEmotions((prev) => {
      if (prev.includes(emotion)) {
        return prev.filter((e) => e !== emotion);
      } else {
        return [...prev, emotion];
      }
    });
  };

  // Check if there are unsaved changes
  const hasUnsavedChanges = useCallback(() => {
    if (!isEditing || !originalEntry) {
      return title.trim() !== '' || content.trim() !== '';
    }

    return (
      title !== originalEntry.title ||
      content !== originalEntry.content ||
      category !== originalEntry.category ||
      emotions.length !== originalEntry.emotions.length ||
      !emotions.every((e) => originalEntry.emotions.includes(e)) ||
      isPrivate !== originalEntry.isPrivate
    );
  }, [isEditing, originalEntry, title, content, category, emotions, isPrivate]);

  // Handle back navigation
  const handleBack = () => {
    if (hasUnsavedChanges()) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to discard them?',
        [
          { text: 'Keep Editing', style: 'cancel' },
          { text: 'Discard', onPress: () => navigation.goBack() },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text variant="body" style={styles.loadingText}>
          Loading journal entry...
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Feather name="arrow-left" size={24} color={colors.text} />
          </TouchableOpacity>

          <Text variant="heading3">
            {isEditing ? 'Edit Entry' : 'New Entry'}
          </Text>

          <View style={styles.headerActions}>
            {isEditing && (
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={handleDelete}
                disabled={isSaving}
              >
                <Feather name="trash-2" size={20} color={colors.error} />
              </TouchableOpacity>
            )}

            <Button
              title="Save"
              onPress={handleSave}
              variant="primary"
              size="small"
              loading={isSaving}
              disabled={isSaving || (!title.trim() && !content.trim())}
              style={styles.saveButton}
            />
          </View>
        </View>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.titleContainer}>
            <TextInput
              style={[styles.titleInput, { color: colors.text }]}
              placeholder="Enter title..."
              placeholderTextColor={colors.textSecondary}
              value={title}
              onChangeText={setTitle}
              maxLength={100}
              returnKeyType="next"
              blurOnSubmit={false}
              onSubmitEditing={() => contentInputRef.current?.focus()}
            />
          </View>

          <View style={styles.contentContainer}>
            <TextInput
              ref={contentInputRef}
              style={[styles.contentInput, { color: colors.text }]}
              placeholder="Write your thoughts here..."
              placeholderTextColor={colors.textSecondary}
              value={content}
              onChangeText={setContent}
              multiline
              textAlignVertical="top"
            />
          </View>

          <CategorySelector
            selectedCategory={category}
            onSelectCategory={handleCategorySelect}
          />

          <EmotionPicker
            selectedEmotions={emotions}
            onSelectEmotion={handleEmotionSelect}
          />

          <View style={styles.privacyContainer}>
            <View style={styles.privacyContent}>
              <Feather
                name={isPrivate ? 'lock' : 'unlock'}
                size={18}
                color={colors.textSecondary}
                style={styles.privacyIcon}
              />
              <View style={styles.privacyTextContainer}>
                <Text variant="subtitle">Make Entry Private</Text>
                <Text variant="caption" color="textSecondary">
                  {isPrivate
                    ? 'Only you can see this entry'
                    : 'Entry may be shared with practitioners'}
                </Text>
              </View>
            </View>
            <Switch
              value={isPrivate}
              onValueChange={setIsPrivate}
              trackColor={{ false: colors.border, true: colors.primary + '80' }}
              thumbColor={isPrivate ? colors.primary : colors.background}
            />
          </View>

          <View style={styles.tagsSection}>
            <Text variant="subtitle" style={styles.tagsTitle}>
              Tags
            </Text>
            <Text
              variant="caption"
              color="textSecondary"
              style={styles.tagsDescription}
            >
              Add #hashtags in your content to automatically tag your entry.
            </Text>
            <View style={styles.tagsContainer}>
              {extractTags(content).length > 0 ? (
                extractTags(content).map((tag, index) => (
                  <View
                    key={index}
                    style={[
                      styles.tagBadge,
                      { backgroundColor: colors.primary + '20' },
                    ]}
                  >
                    <Text variant="caption" style={{ color: colors.primary }}>
                      #{tag}
                    </Text>
                  </View>
                ))
              ) : (
                <Text variant="caption" color="textSecondary">
                  No tags yet. Try adding some #hashtags in your entry.
                </Text>
              )}
            </View>
          </View>

          <View style={styles.footer}>
            <Text variant="caption" color="textSecondary">
              {isEditing
                ? 'Last updated: ' +
                  new Date(originalEntry?.updatedAt || '').toLocaleString()
                : 'Entries are automatically backed up to your account.'}
            </Text>
          </View>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Theme.spacing.m,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.xs,
  },
  saveButton: {
    minWidth: 80,
  },
  content: {
    flex: 1,
    paddingHorizontal: Theme.spacing.m,
    paddingTop: Theme.spacing.m,
  },
  titleContainer: {
    marginBottom: Theme.spacing.m,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  titleInput: {
    fontSize: 22,
    fontWeight: 'bold',
    paddingVertical: Theme.spacing.s,
    minHeight: 50,
  },
  contentContainer: {
    marginBottom: Theme.spacing.l,
  },
  contentInput: {
    fontSize: 16,
    lineHeight: 24,
    minHeight: 150,
    textAlignVertical: 'top',
  },
  privacyContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Theme.spacing.m,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: Theme.borderRadius.medium,
    marginBottom: Theme.spacing.m,
  },
  privacyContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  privacyIcon: {
    marginRight: Theme.spacing.s,
  },
  privacyTextContainer: {
    flex: 1,
  },
  tagsSection: {
    marginBottom: Theme.spacing.l,
  },
  tagsTitle: {
    marginBottom: Theme.spacing.xs,
  },
  tagsDescription: {
    marginBottom: Theme.spacing.s,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  tagBadge: {
    paddingHorizontal: Theme.spacing.s,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.pill,
    marginRight: Theme.spacing.xs,
    marginBottom: Theme.spacing.xs,
  },
  footer: {
    alignItems: 'center',
    marginBottom: Theme.spacing.l,
  },
});
