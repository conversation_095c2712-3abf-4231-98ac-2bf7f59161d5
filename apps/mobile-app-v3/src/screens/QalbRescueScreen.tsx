import React, { useEffect, useState } from 'react';
import { StyleSheet, ScrollView, View, ActivityIndicator, Alert } from 'react-native';
import { Text } from '../components/ui/Text';
import { Button } from '../components/ui/Button';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';
import { Feather } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, Easing, withRepeat, cancelAnimation } from 'react-native-reanimated';
import { useQalbRescue } from '../state/qalbRescue/QalbRescueContext';
import { QalbRescueStepName, QalbRescueContentItem, EmergencyTriggerType, UpdateQalbRescueSessionPayload } from '../services/api/types'; // Ensure this path is correct
// Localization can still be used for static parts if any, or for formatting
// import * as Localization from 'expo-localization';
// import { qalbRescueContent as staticFallbackContent } from '../constants/qalbRescueContent'; // For ultimate fallback

// This screen might be navigated to with a param indicating to start a new session
// or if a session is already active (e.g. app was backgrounded)
const QalbRescueScreen = ({ navigation, route }: any) => {
  const {
    state,
    initiateQalbRescue,
    advanceToNextStep,
    submitFeedbackAndEndSession,
    triggerRequestDua,
    triggerConnectPeer,
    clearQalbRescueSession,
  } = useQalbRescue();

  const { isLoading, error, currentStepName, currentStepContent, isSessionActive, sessionId, duaRequestStatus, peerConnectStatus, peerSupporterDetails } = state;

  // Local state for audio player instance
  const [audioPlayer, setAudioPlayer] = useState<Audio.Sound | null>(null);

  const breathingScale = useSharedValue(1);

  // Effect to start a session if triggered by navigation params
  // This is an example; actual trigger might be a button outside this screen
  useEffect(() => {
    if (route?.params?.startNewSession && !isSessionActive && !isLoading) {
      // Default trigger type and symptoms for now, can be passed via params
      initiateQalbRescue({ triggerType: 'manual', currentSymptoms: ['initial_distress'] });
    }
  }, [route?.params?.startNewSession, isSessionActive, isLoading, initiateQalbRescue]);

  // Effect to manage breathing animation based on current step
  useEffect(() => {
    if (currentStepName === 'breathing' && currentStepContent?.visualElements?.type === 'breathing_animation') {
      breathingScale.value = withRepeat(
        withTiming(1.2, { duration: 2000, easing: Easing.inOut(Easing.ease) }), -1, true
      );
    } else {
      cancelAnimation(breathingScale);
      breathingScale.value = withTiming(1, { duration: 300 });
    }
    // Cleanup animation on unmount or step change
    return () => cancelAnimation(breathingScale);
  }, [currentStepName, currentStepContent, breathingScale]);

  const animatedBreathingStyle = useAnimatedStyle(() => {
    return { transform: [{ scale: breathingScale.value }] };
  });

  // Audio playback function
  const playAudio = async (uri: string | undefined) => {
    if (!uri) return;
    try {
      if (audioPlayer) {
        await audioPlayer.unloadAsync();
      }
      const { sound } = await Audio.Sound.createAsync({ uri }, { shouldPlay: true });
      setAudioPlayer(sound);
      await sound.playAsync();
    } catch (e) {
      console.error('Error playing audio:', e);
      Alert.alert("Audio Error", "Could not play audio.");
    }
  };

  // Cleanup audio on component unmount or when audioPlayer changes
  useEffect(() => {
    return audioPlayer ? () => { audioPlayer.unloadAsync(); } : undefined;
  }, [audioPlayer]);

  // Handlers
  const handleNextStep = () => {
    if (currentStepName && !isLoading) {
      // timeSpentOnStep can be tracked locally if needed, or passed as undefined
      advanceToNextStep({ completedStep: currentStepName, timeSpentOnStep: undefined });
    }
  };

  const handleFinishSession = () => {
    // Example feedback, could be collected from user
    const feedbackPayload: UpdateQalbRescueSessionPayload = {
      status: 'completed',
      feedback: 'User completed the session.',
      effectivenessRating: 5, // Example
    };
    submitFeedbackAndEndSession(feedbackPayload);
    navigation.goBack(); // Or navigate to a summary/home screen
  };

  const handleRequestDua = () => {
    if(!isLoading && duaRequestStatus !== 'loading'){
        triggerRequestDua();
    }
  };

  const handleConnectPeer = () => {
     if(!isLoading && peerConnectStatus !== 'loading'){
        triggerConnectPeer(); // criteria can be added if needed
     }
  };


  // Render Logic
  if (isLoading && !currentStepContent) { // Initial loading for the very first step
    return <View style={styles.container}><ActivityIndicator size="large" color={colors.primary} /></View>;
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text variant="heading3" style={{ color: colors.error, textAlign: 'center' }}>Error</Text>
        <Text style={{ textAlign: 'center', marginVertical: Theme.spacing.m }}>{error}</Text>
        <Button title="Try Again or Go Back" onPress={() => {
          clearQalbRescueSession(); // Clear potentially inconsistent state
          if (navigation.canGoBack()) navigation.goBack();
          // Or, provide a specific retry for the failed action
        }} />
      </View>
    );
  }

  if (!isSessionActive || !currentStepContent) {
     // This screen should ideally only be active if a session is active.
     // If landed here without an active session, show a message or redirect.
     // For demo, we'll allow initiating from here if `startNewSession` param was passed.
     // Otherwise, show an 'idle' state or a button to start.
    return (
      <View style={styles.container}>
        <Text variant="heading2" style={styles.initialTitle}>Qalb Rescue</Text>
        <Text style={styles.initialDescription}>
          Tap below to begin your guided session for immediate calm and spiritual grounding.
        </Text>
        <Button
            title="Start Qalb Rescue"
            onPress={() => initiateQalbRescue({ triggerType: 'manual' })}
            style={styles.startButton}
            leftIcon={<Feather name="play" size={20} color="#fff" />}
            isLoading={isLoading}
        />
      </View>
    );
  }

  // Main content rendering based on currentStepContent
  const renderStepSpecificContent = () => (
    <View style={styles.stepContainer}>
      <Text variant="heading3" style={styles.stepTitle}>{currentStepContent.title}</Text>
      <Text variant="body" style={styles.stepDescription}>{currentStepContent.description}</Text>

      {currentStepContent.audioUrl && (
        <Button title="Play Guided Audio" onPress={() => playAudio(currentStepContent.audioUrl)} style={styles.audioButton} />
      )}

      {currentStepContent.items?.map((item: QalbRescueContentItem) => (
        <View key={item.id} style={styles.itemContainer}>
          <Text style={styles.itemTextArabic}>{item.text}</Text>
          {item.translation && <Text style={styles.itemTextTranslation}>"{item.translation}"</Text>}
          {item.transliteration && <Text style={styles.itemTextTransliteration}>({item.transliteration})</Text>}
          {item.audioUrl && <Button title={`Play ${item.type}`} onPress={() => playAudio(item.audioUrl)} size="sm" style={styles.itemAudioButton} />}
        </View>
      ))}

      {currentStepName === 'breathing' && currentStepContent.visualElements?.type === 'breathing_animation' && (
          <Animated.View style={[styles.breathingCircle, animatedBreathingStyle]} />
      )}

      {currentStepName === 'connection' && currentStepContent.interactiveElements?.buttons?.map(btn => {
        let action = () => console.warn("Action not implemented for:", btn.id);
        let btnIsLoading = false;
        let btnTitle = btn.label;

        if(btn.actionType === 'request_dua') {
            action = handleRequestDua;
            btnIsLoading = duaRequestStatus === 'loading';
            if(duaRequestStatus === 'success') btnTitle = "Du'a Requested!";
        } else if (btn.actionType === 'connect_peer') {
            action = handleConnectPeer;
            btnIsLoading = peerConnectStatus === 'loading';
            if(peerConnectStatus === 'pending_acceptance') btnTitle = "Connecting...";
            if(peerConnectStatus === 'supporter_found') btnTitle = `Connected with ${peerSupporterDetails?.name || 'Supporter'}`;
            if(peerConnectStatus === 'unavailable') btnTitle = "Peers Unavailable";
        }
        // Add more actions like find_professional

        return (
            <Button
                key={btn.id}
                title={btnTitle}
                onPress={action}
                style={styles.actionButton}
                isLoading={btnIsLoading}
                disabled={btnIsLoading || duaRequestStatus === 'success' && btn.actionType === 'request_dua'}
            />
        );
      })}
       {peerConnectStatus === 'supporter_found' && peerSupporterDetails && (
        <Text style={styles.supporterInfoText}>You are connected with {peerSupporterDetails.name}. Please proceed to your chat application.</Text>
      )}


      {isLoading && <ActivityIndicator style={{marginTop: Theme.spacing.m}} size="small" color={colors.primary} />}

      {currentStepName !== 'connection' ? (
        <Button title="Next" onPress={handleNextStep} style={styles.nextButton} disabled={isLoading} />
      ) : (
        <Button title="Finish Session" onPress={handleFinishSession} style={styles.nextButton} disabled={isLoading} />
      )}
    </View>
  );

  return (
    <ScrollView contentContainerStyle={styles.container}>
      {isSessionActive && currentStepContent ? renderStepSpecificContent() : <Text>Loading session...</Text> }
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Theme.spacing.l,
    backgroundColor: colors.background,
  },
  initialContainer: {
    alignItems: 'center',
    padding: Theme.spacing.xl,
    backgroundColor: colors.surface,
    borderRadius: Theme.borderRadius.large,
    ...Theme.shadows.medium,
  },
  initialTitle: {
    color: colors.primary,
    marginBottom: Theme.spacing.m,
  },
  initialDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.xl,
    color: colors.textSecondary,
    fontSize: Theme.typography.fontSize.l,
  },
  startButton: {
    minWidth: 200,
  },
  stepContainer: {
    alignItems: 'center',
    padding: Theme.spacing.xl,
    backgroundColor: colors.surface,
    borderRadius: Theme.borderRadius.large,
    ...Theme.shadows.medium,
    width: '100%',
  },
  stepTitle: {
    color: colors.primary,
    marginBottom: Theme.spacing.m,
    textAlign: 'center',
  },
  stepDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.xl,
    color: colors.textSecondary,
    fontSize: Theme.typography.fontSize.l,
  },
  nextButton: {
    marginTop: Theme.spacing.l,
  },
  audioButton: {
    marginTop: Theme.spacing.m,
  },
  breathingCircle: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: colors.primary,
    marginTop: Theme.spacing.xl,
    marginBottom: Theme.spacing.xl,
  },
});

export default QalbRescueScreen;
