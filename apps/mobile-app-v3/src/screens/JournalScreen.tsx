import React, { useCallback, useState } from 'react';

import {
  ActivityIndicator,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';

import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { JournalEntry } from '../services/api/types';
import { JournalEntryCard } from '../components/JournalEntryCard';
import { Button } from '../components/ui/Button';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';
// import { useColorScheme from "../hooks/useColorScheme";
import { RootStackNavigationProp } from '../navigation/types';
// Mock useJournal hook
const useJournal = () => ({
  entries: [] as JournalEntry[],
  loading: false,
  isLoading: false,
  error: null,
  fetchEntries: async () => console.log('Fetch entries'),
  refreshEntries: async () => console.log('Refresh entries'),
  filterEntries: (filter: any) => console.log('Filter entries:', filter),
  pendingChanges: false,
});

// Define category filter type
type CategoryFilter =
  | 'all'
  | 'jism'
  | 'nafs'
  | 'aql'
  | 'qalb'
  | 'ruh'
  | 'general';

export default function JournalScreen() {
  const navigation = useNavigation<any>();

  const insets = useSafeAreaInsets();

  // Get journal context
  const { entries, isLoading, refreshEntries, filterEntries, pendingChanges } =
    useJournal();

  // Local state
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] =
    useState<CategoryFilter>('all');
  const [filteredEntries, setFilteredEntries] = useState<JournalEntry[]>([]);

  // Filter entries based on search query and category
  const updateFilteredEntries = useCallback(() => {
    let filtered = entries;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(
        (entry) => entry.category === selectedCategory
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (entry) =>
          entry.title.toLowerCase().includes(query) ||
          entry.content.toLowerCase().includes(query)
      );
    }

    setFilteredEntries(filtered);
  }, [entries, searchQuery, selectedCategory]);

  // Update filtered entries when entries, search query or category changes
  React.useEffect(() => {
    updateFilteredEntries();
  }, [entries, searchQuery, selectedCategory, updateFilteredEntries]);

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshEntries();
    setIsRefreshing(false);
  };

  // Handle search
  const handleSearch = (text: string) => {
    setSearchQuery(text);
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
  };

  // Handle category filter
  const handleCategoryFilter = (category: CategoryFilter) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedCategory(category);
  };

  // Navigate to journal entry detail
  const navigateToEntryDetail = (entry: JournalEntry) => {
    navigation.navigate('JournalEntry', { entryId: entry.id });
  };

  // Create new journal entry
  const createNewEntry = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    navigation.navigate('JournalEntry', {});
  };

  // Render category pill
  const renderCategoryPill = (
    category: CategoryFilter,
    label: string,
    icon: string
  ) => {
    const isActive = selectedCategory === category;
    let categoryColor = colors.primary;

    switch (category) {
      case 'jism':
        categoryColor = colors.jismRed;
        break;
      case 'nafs':
        categoryColor = colors.nafsOrange;
        break;
      case 'aql':
        categoryColor = colors.aqlYellow;
        break;
      case 'qalb':
        categoryColor = colors.qalbGreen;
        break;
      case 'ruh':
        categoryColor = colors.spiritualBlue;
        break;
      default:
        categoryColor = colors.primary;
    }

    return (
      <TouchableOpacity
        style={[
          styles.categoryPill,
          {
            backgroundColor: isActive ? categoryColor + '20' : 'transparent',
            borderColor: isActive ? categoryColor : colors.border,
          },
        ]}
        onPress={() => handleCategoryFilter(category)}
      >
        <Feather
          name={icon}
          size={16}
          color={isActive ? categoryColor : colors.textSecondary}
          style={styles.categoryIcon}
        />
        <Text
          variant="body"
          style={{
            color: isActive ? categoryColor : colors.text,
          }}
        >
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Feather
        name="book"
        size={64}
        color={colors.textSecondary}
        style={styles.emptyIcon}
      />
      <Text variant="subtitle" style={styles.emptyTitle}>
        No Journal Entries Found
      </Text>
      <Text
        variant="body"
        color="textSecondary"
        style={styles.emptyDescription}
      >
        {searchQuery || selectedCategory !== 'all'
          ? 'Try adjusting your search or filters to see more entries.'
          : 'Start documenting your healing journey by creating your first entry.'}
      </Text>
      {!searchQuery && selectedCategory === 'all' && (
        <Button
          title="Create First Entry"
          onPress={createNewEntry}
          variant="primary"
          icon="plus"
          style={styles.emptyButton}
        />
      )}
    </View>
  );

  // Show loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text variant="body" style={styles.loadingText}>
          Loading journal entries...
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <Text variant="heading2">Journal</Text>
          <View style={styles.headerActions}>
            {pendingChanges && (
              <View style={styles.syncIndicator}>
                <Feather name="cloud-off" size={18} color={colors.warning} />
              </View>
            )}
            <TouchableOpacity
              style={styles.newEntryButton}
              onPress={createNewEntry}
            >
              <Feather name="plus" size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.searchContainer}>
          <View
            style={[
              styles.searchInputContainer,
              { borderColor: colors.border },
            ]}
          >
            <Feather
              name="search"
              size={20}
              color={colors.textSecondary}
              style={styles.searchIcon}
            />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder="Search journal entries..."
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={clearSearch}
              >
                <Feather name="x" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesScrollView}
          contentContainerStyle={styles.categoriesContainer}
        >
          {renderCategoryPill('all', 'All', 'grid')}
          {renderCategoryPill('jism', 'Body', 'activity')}
          {renderCategoryPill('nafs', 'Emotions', 'wind')}
          {renderCategoryPill('aql', 'Mind', 'cpu')}
          {renderCategoryPill('qalb', 'Heart', 'heart')}
          {renderCategoryPill('ruh', 'Soul', 'star')}
          {renderCategoryPill('general', 'General', 'book')}
        </ScrollView>

        <FlatList
          data={filteredEntries}
          renderItem={({ item }) => (
            <JournalEntryCard entry={item} onPress={navigateToEntryDetail} />
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.entriesContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          ListEmptyComponent={renderEmptyState()}
        />
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Theme.spacing.m,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.m,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  syncIndicator: {
    marginRight: Theme.spacing.s,
    padding: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.small,
    backgroundColor: colors.warning + '20',
  },
  newEntryButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary + '20',
  },
  searchContainer: {
    paddingHorizontal: Theme.spacing.m,
    marginBottom: Theme.spacing.m,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Theme.borderRadius.medium,
    paddingHorizontal: Theme.spacing.s,
    height: 46,
  },
  searchIcon: {
    marginRight: Theme.spacing.s,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  clearButton: {
    padding: Theme.spacing.xs,
  },
  categoriesScrollView: {
    marginBottom: Theme.spacing.m,
  },
  categoriesContainer: {
    paddingHorizontal: Theme.spacing.m,
  },
  categoryPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
    borderWidth: 1,
    marginRight: Theme.spacing.s,
  },
  categoryIcon: {
    marginRight: Theme.spacing.xs,
  },
  entriesContainer: {
    paddingHorizontal: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl,
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Theme.spacing.xl,
    minHeight: 300,
  },
  emptyIcon: {
    marginBottom: Theme.spacing.m,
    opacity: 0.5,
  },
  emptyTitle: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  emptyDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.l,
  },
  emptyButton: {
    minWidth: 200,
  },
});
