// useSymptoms hook for symptoms management
import { useContext } from 'react';
import { SymptomsContext } from '../state/SymptomsContext';

export const useSymptoms = () => {
  const context = useContext(SymptomsContext);

  if (!context) {
    throw new Error('useSymptoms must be used within a SymptomsProvider');
  }

  return {
    symptomsState: context.state,
    state: context.state, // For backward compatibility
    selectSymptom: context.selectSymptom,
    deselectSymptom: context.deselectSymptom,
    setIntensity: (symptomId: string, intensity: number) => {
      // Placeholder implementation - add if needed
      console.log('setIntensity called:', symptomId, intensity);
    },
    fetchSymptoms: context.fetchSymptoms,
    submitSymptoms: context.submitSymptoms,
    clearSymptoms: context.clearSelection,
    dispatch: context.dispatch,
  };
};
