import { useEffect } from 'react';
import * as Linking from 'expo-linking';
import { useNavigation } from '@react-navigation/native';
import authService from '../services/auth.service';
import Toast from 'react-native-toast-message'; // Import Toast
import { Platform } from 'react-native'; // Import Platform

/**
 * Hook to handle OAuth deep link and extract Supabase token
 * Usage: Call in your main App or Auth screen
 */
export function useOAuthDeepLinkHandler() {
  const navigation = useNavigation();

  useEffect(() => {
    const handleDeepLink = async (event: Linking.EventType) => {
      try {
        const url = event.url;
        // Example: qalbhealing://auth-callback#access_token=XXX&refresh_token=YYY&expires_in=3600
        // Or for web: http://localhost:8081/#access_token=XXX&refresh_token=YYY&expires_in=3600

        if (url && url.includes('#')) {
          const params = getHashParams(url);
          const access_token = params.access_token;
          const refresh_token = params.refresh_token;
          const expires_in = params.expires_in ? parseInt(params.expires_in, 10) : undefined;
          // Supabase might also include token_type, type (e.g. 'signup', 'recovery')

          if (access_token && refresh_token) {
            // Call the new method in authService to handle the full session setup
            await authService.handleAuthRedirect({
              access_token,
              refresh_token,
              expires_in
            });

            // Navigate to main app screen after session is handled
            if (navigation.isFocused()) {
              navigation.reset({ index: 0, routes: [{ name: '(tabs)' }] });
            }

            // Clear the URL hash if on web
            if (Platform.OS === 'web') {
              window.history.replaceState({}, document.title, window.location.pathname + window.location.search);
            }

          } else if (params.error_description || params.error) {
            const errorMessage = params.error_description?.replace(/\+/g, ' ') || params.error || 'Authentication failed via link.';
            console.error('OAuth Error from redirect:', errorMessage);
            Toast.show({
              type: 'error',
              text1: 'Authentication Error',
              text2: errorMessage,
              visibilityTime: 4000,
            });
          } else {
            // Case where there's a hash but no tokens and no specific error
            console.warn('Deep link with hash processed, but no auth tokens or specific error found in params:', params);
          }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Could not process the authentication link.';
        console.error('Error handling deep link:', errorMessage, error);
        Toast.show({
          type: 'error',
          text1: 'Link Processing Error',
          text2: errorMessage,
          visibilityTime: 4000,
        });
      }
    };
    const subscription = Linking.addEventListener('url', handleDeepLink);
    return () => subscription.remove();
  }, [navigation]);
}

function getHashParams(url: string) {
  const hash = url.split('#')[1] || '';
  return Object.fromEntries(new URLSearchParams(hash));
}
