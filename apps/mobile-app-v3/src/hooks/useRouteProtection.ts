import { useEffect } from 'react';
import { router, usePathname } from 'expo-router';
import { authService } from '../services/auth.service';

/**
 * Route protection HOC/hook for Expo Router
 * Usage: Call in your main layout or protected screens
 * Set REACT_NATIVE_ROUTE_PROTECTION=true in .env for production
 */
export function useRouteProtection() {
  useEffect(() => {
    const checkAuth = async () => {
      const enabled = process.env.EXPO_PUBLIC_ROUTE_PROTECTION === 'true';
      if (!enabled) return; // Skip in dev mode
      const token = await authService.getAuthToken();
      const pathname = usePathname();
      if (!token && pathname !== '/login' && pathname !== '/signup') {
        router.replace('/login');
      }
    };
    checkAuth();
  }, []);
}
