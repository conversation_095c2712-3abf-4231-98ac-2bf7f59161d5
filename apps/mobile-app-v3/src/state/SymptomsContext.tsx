import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useReducer } from 'react';
import {
  SymptomCategory,
  SymptomEntry,
  // SymptomItem,
  SymptomSubmission,
} from '../services/api/types';
// import serviceRegistry from '../services/ServiceRegistry';
import { ActionType, SymptomsState } from '../types';
import { useAppState } from './AppStateContext';

// Initial state
const initialSymptomsState: SymptomsState = {
  categories: [],
  recentEntries: [],
  selectedSymptoms: [],
  isLoading: false,
  loading: false,
  error: null,
  lastUpdated: null,
  lastSubmission: null,
  lastDiagnosis: null,
};

// Create context
export const SymptomsContext = createContext<{
  state: SymptomsState;
  dispatch: React.Dispatch<ActionType>;
  fetchSymptoms: () => Promise<void>;
  selectSymptom: (symptom: any) => void;
  deselectSymptom: (symptomId: string) => void;
  clearSelection: () => void;
  logSymptoms: (entry: Omit<SymptomEntry, 'id' | 'timestamp'>) => Promise<void>;
  fetchRecentEntries: () => Promise<void>;
  submitSymptoms: (submission: SymptomSubmission) => Promise<any>;
}>({
  state: initialSymptomsState,
  dispatch: () => null,
  fetchSymptoms: async () => {},
  selectSymptom: () => null,
  deselectSymptom: () => null,
  clearSelection: () => null,
  logSymptoms: async () => {},
  fetchRecentEntries: async () => {},
  submitSymptoms: async () => ({}),
});

// Reducer function
function symptomsReducer(
  state: SymptomsState,
  action: ActionType
): SymptomsState {
  switch (action.type) {
    case 'SYMPTOMS_FETCH_REQUEST':
      return {
        ...state,
        isLoading: true,
        loading: true,
        error: null,
      };
    case 'SYMPTOMS_FETCH_SUCCESS':
      return {
        ...state,
        categories: action.payload.categories,
        isLoading: false,
        loading: false,
        lastUpdated: new Date().toISOString(),
      };
    case 'SYMPTOMS_FETCH_FAILURE':
      return {
        ...state,
        isLoading: false,
        loading: false,
        error: action.payload,
      };
    case 'SYMPTOMS_SELECT':
      // Check if symptom already exists in selection to avoid duplicates
      if (state.selectedSymptoms.some((s) => s.id === action.payload.id)) {
        return state;
      }
      return {
        ...state,
        selectedSymptoms: [...state.selectedSymptoms, action.payload],
      };
    case 'SYMPTOMS_DESELECT':
      return {
        ...state,
        selectedSymptoms: state.selectedSymptoms.filter(
          (symptom) => symptom.id !== action.payload
        ),
      };
    case 'SYMPTOMS_CLEAR_SELECTION':
      return {
        ...state,
        selectedSymptoms: [],
      };
    case 'SYMPTOMS_LOG_ENTRY':
      return {
        ...state,
        recentEntries: [action.payload, ...state.recentEntries.slice(0, 9)], // Keep most recent 10
        selectedSymptoms: [], // Clear selection after logging
      };
    case 'SYMPTOMS_LOAD_RECENT_ENTRIES':
      return {
        ...state,
        recentEntries: action.payload,
      };
    case 'SYMPTOMS_SUBMIT_REQUEST':
      return {
        ...state,
        isLoading: true,
        loading: true,
        error: null,
      };
    case 'SYMPTOMS_SUBMIT_SUCCESS':
      return {
        ...state,
        isLoading: false,
        loading: false,
        lastSubmission: action.payload.submission,
        lastDiagnosis: action.payload.diagnosis,
      };
    case 'SYMPTOMS_SUBMIT_FAILURE':
      return {
        ...state,
        isLoading: false,
        loading: false,
        error: action.payload,
      };
    default:
      return state;
  }
}

// Storage keys
const SYMPTOMS_STORAGE_KEY = 'qalb_symptoms_data';
const SELECTED_SYMPTOMS_KEY = 'qalb_selected_symptoms';
const RECENT_ENTRIES_KEY = 'qalb_recent_symptom_entries';

// Provider component
export const SymptomsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(symptomsReducer, initialSymptomsState);
  const { state: appState, dispatch: appDispatch } = useAppState();

  // Load data from storage on mount
  useEffect(() => {
    const loadSymptomsData = async () => {
      try {
        const symptomsData = await AsyncStorage.getItem(SYMPTOMS_STORAGE_KEY);
        const selectedSymptoms = await AsyncStorage.getItem(
          SELECTED_SYMPTOMS_KEY
        );
        const recentEntries = await AsyncStorage.getItem(RECENT_ENTRIES_KEY);

        if (symptomsData) {
          const parsedData = JSON.parse(symptomsData);
          dispatch({
            type: 'SYMPTOMS_FETCH_SUCCESS',
            payload: { categories: parsedData.categories },
          });
        }

        if (selectedSymptoms) {
          const parsedSelected = JSON.parse(selectedSymptoms);
          parsedSelected.forEach((symptom: any) => {
            dispatch({ type: 'SYMPTOMS_SELECT', payload: symptom });
          });
        }

        if (recentEntries) {
          const parsedEntries = JSON.parse(recentEntries);
          dispatch({
            type: 'SYMPTOMS_LOAD_RECENT_ENTRIES',
            payload: parsedEntries,
          });
        }
      } catch (error) {
        console.error('Failed to load symptoms data from storage:', error);
      }
    };

    loadSymptomsData();

    // Also fetch fresh data from service if we're online
    if (appState.isOnline) {
      fetchSymptoms();
    }
  }, [appState.isOnline]);

  // Save selected symptoms to storage when they change
  useEffect(() => {
    const saveSelectedSymptoms = async () => {
      try {
        await AsyncStorage.setItem(
          SELECTED_SYMPTOMS_KEY,
          JSON.stringify(state.selectedSymptoms)
        );
      } catch (error) {
        console.error('Failed to save selected symptoms to storage:', error);
      }
    };

    saveSelectedSymptoms();
  }, [state.selectedSymptoms]);

  // Save recent entries to storage when they change
  useEffect(() => {
    const saveRecentEntries = async () => {
      try {
        await AsyncStorage.setItem(
          RECENT_ENTRIES_KEY,
          JSON.stringify(state.recentEntries)
        );
      } catch (error) {
        console.error('Failed to save recent entries to storage:', error);
      }
    };

    saveRecentEntries();
  }, [state.recentEntries]);

  // Save symptoms data to storage when it changes
  useEffect(() => {
    const saveSymptomsData = async () => {
      try {
        if (state.categories.length > 0) {
          await AsyncStorage.setItem(
            SYMPTOMS_STORAGE_KEY,
            JSON.stringify({
              categories: state.categories,
              lastUpdated: state.lastUpdated,
            })
          );
        }
      } catch (error) {
        console.error('Failed to save symptoms data to storage:', error);
      }
    };

    saveSymptomsData();
  }, [state.categories, state.lastUpdated]);

  // Helper functions for context consumers
  const fetchSymptoms = async () => {
    dispatch({ type: 'SYMPTOMS_FETCH_REQUEST' });

    try {
      // Mock symptoms service calls
      const categoriesData = {};

      // Convert the Record<string, string[]> to SymptomCategory[]
      const categories: any[] = Object.entries(categoriesData).map(
        ([key, symptoms]) => ({
          id: key,
          name: key.charAt(0).toUpperCase() + key.slice(1),
          symptoms,
        })
      );

      dispatch({
        type: 'SYMPTOMS_FETCH_SUCCESS',
        payload: { categories },
      });
    } catch (error) {
      console.error('Failed to fetch symptoms:', error);

      dispatch({
        type: 'SYMPTOMS_FETCH_FAILURE',
        payload:
          error instanceof Error ? error.message : 'Failed to fetch symptoms',
      });
    }
  };

  const selectSymptom = (symptom: any) => {
    dispatch({ type: 'SYMPTOMS_SELECT', payload: symptom });
  };

  const deselectSymptom = (symptomId: string) => {
    dispatch({ type: 'SYMPTOMS_DESELECT', payload: symptomId });
  };

  const clearSelection = () => {
    dispatch({ type: 'SYMPTOMS_CLEAR_SELECTION' });
  };

  const logSymptoms = async (
    entryData: Omit<SymptomEntry, 'id' | 'timestamp'>
  ): Promise<void> => {
    try {
      // Mock symptoms service calls

      const entry: SymptomEntry = {
        id: Math.random().toString(36).substring(2, 15),
        timestamp: new Date().toISOString(),
        ...entryData,
        symptoms: state.selectedSymptoms as any,
      };

      // Save to service if online
      if (appState.isOnline) {
        // Mock service call
      } else {
        // Queue for syncing when back online
        appDispatch({
          type: 'APP_ADD_SYNC_ACTION',
          payload: {
            type: 'LOG_SYMPTOMS',
            payload: entry,
          },
        });
      }

      // Update local state
      dispatch({ type: 'SYMPTOMS_LOG_ENTRY', payload: entry });
    } catch (error) {
      console.error('Failed to log symptoms:', error);
      throw error;
    }
  };

  const fetchRecentEntries = async () => {
    try {
      // Mock symptoms service calls
      const entries: any[] = [];

      dispatch({ type: 'SYMPTOMS_LOAD_RECENT_ENTRIES', payload: entries });
    } catch (error) {
      console.error('Failed to fetch recent symptom entries:', error);
      // Don't update error state - just keep using cached entries
    }
  };

  const submitSymptoms = async (submission: SymptomSubmission) => {
    try {
      dispatch({ type: 'SYMPTOMS_SUBMIT_REQUEST' });

      // Mock symptoms service calls
      const response = { diagnosis: null };

      dispatch({
        type: 'SYMPTOMS_SUBMIT_SUCCESS',
        payload: { submission, diagnosis: response.diagnosis },
      });

      return response;
    } catch (error) {
      console.error('Failed to submit symptoms:', error);

      dispatch({
        type: 'SYMPTOMS_SUBMIT_FAILURE',
        payload:
          error instanceof Error ? error.message : 'Failed to submit symptoms',
      });

      throw error;
    }
  };

  return (
    <SymptomsContext.Provider
      value={{
        state,
        dispatch,
        fetchSymptoms,
        selectSymptom,
        deselectSymptom,
        clearSelection,
        logSymptoms,
        fetchRecentEntries,
        submitSymptoms,
      }}
    >
      {children}
    </SymptomsContext.Provider>
  );
};

// Hook to use the symptoms context
export const useSymptoms = () => {
  const context = useContext(SymptomsContext);
  if (!context) {
    throw new Error('useSymptoms must be used within a SymptomsProvider');
  }
  return context;
};
