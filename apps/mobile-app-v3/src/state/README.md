# State Management System

This directory contains the complete state management system for the Qalb Healing app using React Context API.

## Structure

```
state/
├── context/
│   ├── AppStateContext.tsx      # App-wide state (online status, sync)
│   ├── UserContext.tsx          # User authentication and preferences
│   ├── SymptomsContext.tsx      # Symptoms tracking and analysis
│   ├── JourneysContext.tsx      # Journey progress and activities
│   ├── JournalContext.tsx       # Journal entries and analytics
│   ├── emergency/
│   │   ├── EmergencyContext.tsx # Emergency contacts and resources
│   │   ├── types.ts            # Emergency-specific types
│   │   └── storage.ts          # Emergency data persistence
│   ├── RootProvider.tsx         # Combines all context providers
│   └── index.ts                # Exports all providers and hooks
├── types.ts                     # All state and action type definitions
└── README.md                   # This file
```

## Usage

### 1. Wrap your app with RootProvider

```tsx
import React from 'react';
import { RootProvider } from './state/context';
import YourAppContent from './YourAppContent';

export default function App() {
  return (
    <RootProvider>
      <YourAppContent />
    </RootProvider>
  );
}
```

### 2. Use individual hooks in components

```tsx
import React from 'react';
import { useSymptoms, useUser, useJourneys } from './state/context';

export default function SomeComponent() {
  const { state: symptomsState, fetchSymptoms } = useSymptoms();
  const { state: userState, login } = useUser();
  const { state: journeysState, fetchJourneys } = useJourneys();

  // Use the state and actions as needed
  return (
    <div>
      {/* Your component content */}
    </div>
  );
}
```

## Available Contexts

### AppStateContext
- **Purpose**: App-wide state management
- **Hook**: `useAppState()`
- **Features**: Online status, sync actions, onboarding state

### UserContext
- **Purpose**: User authentication and preferences
- **Hook**: `useUser()`
- **Features**: Login/logout, profile management, preferences

### SymptomsContext
- **Purpose**: Symptom tracking and analysis
- **Hook**: `useSymptoms()`
- **Features**: Symptom selection, intensity tracking, diagnosis

### JourneysContext
- **Purpose**: Journey progress and activities
- **Hook**: `useJourneys()`
- **Features**: Journey selection, progress tracking, activity completion

### JournalContext
- **Purpose**: Journal entries and analytics
- **Hook**: `useJournal()`
- **Features**: Entry management, mood tracking, trend analysis

### EmergencyContext
- **Purpose**: Emergency contacts and resources
- **Hook**: `useEmergency()`
- **Features**: Contact management, resource access, crisis support

## State Structure

Each context follows a consistent pattern:

```tsx
interface ContextState {
  // Data
  items: Item[];
  selectedItem: Item | null;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Metadata
  lastUpdated: string | null;
}

interface ContextValue {
  state: ContextState;
  dispatch: React.Dispatch<ActionType>;
  
  // Helper functions
  fetchItems: () => Promise<void>;
  selectItem: (id: string) => void;
  // ... other actions
}
```

## Offline Support

The state management system includes built-in offline support:

- Actions are queued when offline
- Automatic sync when connection is restored
- Optimistic updates for better UX
- Retry logic for failed sync operations

## Type Safety

All contexts are fully typed with TypeScript:

- State interfaces defined in `types.ts`
- Action types with discriminated unions
- Proper typing for all hook return values
- IntelliSense support throughout

## Best Practices

1. **Use the RootProvider**: Always wrap your app with the RootProvider
2. **Import from index**: Use `import { useSymptoms } from './state/context'`
3. **Handle loading states**: Check `isLoading` before rendering data
4. **Handle errors**: Display error messages when `error` is not null
5. **Optimize re-renders**: Use React.memo() for expensive components
6. **Test contexts**: Write tests for context providers and reducers

## Testing

Each context can be tested individually:

```tsx
import { renderHook } from '@testing-library/react-hooks';
import { SymptomsProvider, useSymptoms } from './SymptomsContext';

test('should fetch symptoms', async () => {
  const wrapper = ({ children }) => (
    <SymptomsProvider>{children}</SymptomsProvider>
  );
  
  const { result } = renderHook(() => useSymptoms(), { wrapper });
  
  await act(async () => {
    await result.current.fetchSymptoms();
  });
  
  expect(result.current.state.categories).toBeDefined();
});
```
