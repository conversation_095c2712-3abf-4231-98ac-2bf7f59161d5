import React from 'react';
import { AppStateProvider } from './AppStateContext';
import { UserProvider } from './UserContext';
import { SymptomsProvider } from './SymptomsContext';
import { JourneysProvider } from './JourneysContext';
import { EmergencyProvider } from './emergency/EmergencyContext';
import { JournalProvider } from './JournalContext';

interface RootProviderProps {
  children: React.ReactNode;
}

/**
 * Root provider that wraps all context providers in the correct order
 * 
 * Order matters:
 * 1. AppStateProvider - provides app-wide state (online status, sync actions)
 * 2. UserProvider - provides user authentication and preferences
 * 3. All feature providers - depend on app state and user context
 */
export const RootProvider: React.FC<RootProviderProps> = ({ children }) => {
  return (
    <AppStateProvider>
      <UserProvider>
        <SymptomsProvider>
          <JourneysProvider>
            <EmergencyProvider>
              <JournalProvider>
                {children}
              </JournalProvider>
            </EmergencyProvider>
          </JourneysProvider>
        </SymptomsProvider>
      </UserProvider>
    </AppStateProvider>
  );
};

export default RootProvider;
