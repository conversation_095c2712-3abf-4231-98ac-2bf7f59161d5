import React, { createContext, useContext, useReducer, useCallback } from 'react';
import emergencyService from '../../services/api/EmergencyService'; // Adjust path as needed
import {
    QalbRescueState,
    QalbRescueAction,
    initialQalbRescueState
} from './types';
import { qalbRescueReducer } from './reducer';
import * as ApiTypes from '../../services/api/types'; // For request/response types

interface QalbRescueContextProps {
  state: QalbRescueState;
  initiateQalbRescue: (triggerData: { triggerType: ApiTypes.EmergencyTriggerType; currentSymptoms?: string[] }) => Promise<void>;
  advanceToNextStep: (payload: ApiTypes.ProgressQalbRescuePayload) => Promise<void>;
  submitFeedbackAndEndSession: (payload: ApiTypes.UpdateQalbRescueSessionPayload) => Promise<void>;
  triggerRequestDua: () => Promise<void>;
  triggerConnectPeer: (criteria?: any) => Promise<void>;
  clearQalbRescueSession: () => void;
  // Potentially add a function to manually set error or loading for specific UI needs
  // setError: (message: string | null) => void;
}

const QalbRescueContext = createContext<QalbRescueContextProps | undefined>(undefined);

export const QalbRescueProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(qalbRescueReducer, initialQalbRescueState);

  const initiateQalbRescue = useCallback(async (triggerData: { triggerType: ApiTypes.EmergencyTriggerType; currentSymptoms?: string[] }) => {
    dispatch({ type: 'SESSION_START_INIT' });
    try {
      const response = await emergencyService.startEmergencySession(triggerData);
      dispatch({
        type: 'SESSION_START_SUCCESS',
        payload: {
          sessionId: response.sessionId,
          currentStepName: response.currentStep,
          currentStepContent: response.stepContent,
        },
      });
    } catch (error: any) {
      dispatch({ type: 'SESSION_START_FAILURE', payload: error.message || 'Failed to start session' });
    }
  }, []);

  const advanceToNextStep = useCallback(async (payload: ApiTypes.ProgressQalbRescuePayload) => {
    if (!state.sessionId) {
      dispatch({ type: 'STEP_PROGRESS_FAILURE', payload: 'No active session ID' });
      return;
    }
    dispatch({ type: 'STEP_PROGRESS_INIT' });
    try {
      const response = await emergencyService.progressToNextStep(state.sessionId, payload);
      dispatch({
        type: 'STEP_PROGRESS_SUCCESS',
        payload: {
          currentStepName: response.currentStep,
          currentStepContent: response.stepContent,
        },
      });
       if (response.currentStep === null) { // Session completed by backend
         // Additional local cleanup if needed, though reducer already handles it
         console.log("Qalb Rescue session completed via backend progression.");
       }
    } catch (error: any) {
      dispatch({ type: 'STEP_PROGRESS_FAILURE', payload: error.message || 'Failed to progress step' });
    }
  }, [state.sessionId]);

  const submitFeedbackAndEndSession = useCallback(async (payload: ApiTypes.UpdateQalbRescueSessionPayload) => {
    if (!state.sessionId) {
      dispatch({ type: 'SESSION_END_FAILURE', payload: 'No active session ID' });
      return;
    }
    dispatch({ type: 'SESSION_END_INIT' });
    try {
      await emergencyService.updateQalbRescueSession(state.sessionId, payload);
      dispatch({ type: 'SESSION_END_SUCCESS' });
    } catch (error: any) {
      dispatch({ type: 'SESSION_END_FAILURE', payload: error.message || 'Failed to end session' });
    }
  }, [state.sessionId]);

  const triggerRequestDua = useCallback(async () => {
    if (!state.sessionId) {
      dispatch({ type: 'REQUEST_DUA_FAILURE', payload: 'No active session ID' });
      return;
    }
    dispatch({ type: 'REQUEST_DUA_INIT' });
    try {
      await emergencyService.requestDua(state.sessionId);
      dispatch({ type: 'REQUEST_DUA_SUCCESS' });
    } catch (error: any) {
      dispatch({ type: 'REQUEST_DUA_FAILURE', payload: error.message || 'Failed to request Du\'a' });
    }
  }, [state.sessionId]);

  const triggerConnectPeer = useCallback(async (criteria?: any) => {
    if (!state.sessionId) {
        dispatch({ type: 'CONNECT_PEER_FAILURE', payload: { message: 'No active session ID' } });
        return;
    }
    dispatch({ type: 'CONNECT_PEER_INIT' });
    try {
        const response = await emergencyService.connectPeerSupporter(state.sessionId, criteria);
        dispatch({ type: 'CONNECT_PEER_SUCCESS', payload: response });
    } catch (error: any) {
        // The service might return a structured error/status for unavailability, handle that.
        if (error.status && error.message) { // Assuming service error might have a status
             dispatch({ type: 'CONNECT_PEER_FAILURE', payload: { message: error.message, status: error.status } });
        } else {
            dispatch({ type: 'CONNECT_PEER_FAILURE', payload: { message: error.message || 'Failed to connect with peer supporter' } });
        }
    }
  }, [state.sessionId]);

  const clearQalbRescueSession = useCallback(() => {
    dispatch({ type: 'CLEAR_SESSION' });
  }, []);

  return (
    <QalbRescueContext.Provider
      value={{
        state,
        initiateQalbRescue,
        advanceToNextStep,
        submitFeedbackAndEndSession,
        triggerRequestDua,
        triggerConnectPeer,
        clearQalbRescueSession,
      }}
    >
      {children}
    </QalbRescueContext.Provider>
  );
};

export const useQalbRescue = (): QalbRescueContextProps => {
  const context = useContext(QalbRescueContext);
  if (context === undefined) {
    throw new Error('useQalbRescue must be used within a QalbRescueProvider');
  }
  return context;
};
