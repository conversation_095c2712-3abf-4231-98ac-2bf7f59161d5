import React from 'react';
import { renderHook, act } from '@testing-library/react-hooks';
import { QalbRescueProvider, useQalbRescue } from '../QalbRescueContext';
import emergencyService from '../../../services/api/EmergencyService'; // Adjust path
import {
    StartQalbRescueResponse,
    ProgressQalbRescueResponse,
    UpdateQalbRescueSessionResponse,
    RequestDuaResponse,
    ConnectPeerSupporterResponse,
    QalbRescueStepName,
    QalbRescueStepContent,
    EmergencyTriggerType,
    ProgressQalbRescuePayload,
    UpdateQalbRescueSessionPayload
} from '../../../services/api/types'; // Adjust path

// Mock the emergencyService
jest.mock('../../../services/api/EmergencyService', () => ({
  startEmergencySession: jest.fn(),
  progressToNextStep: jest.fn(),
  updateQalbRescueSession: jest.fn(),
  requestDua: jest.fn(),
  connectPeerSupporter: jest.fn(),
}));

const mockStartResponse: StartQalbRescueResponse = {
  sessionId: 'sess-123',
  startTime: new Date().toISOString(),
  currentStep: 'grounding' as QalbRescueStepName,
  stepContent: { title: 'Grounding Step', description: '...' } as QalbRescueStepContent,
};

const mockProgressResponse: ProgressQalbRescueResponse = {
  sessionId: 'sess-123',
  currentStep: 'breathing' as QalbRescueStepName,
  stepContent: { title: 'Breathing Step', description: '...' } as QalbRescueStepContent,
};

const mockSessionEndResponse: UpdateQalbRescueSessionResponse = {
    sessionId: 'sess-123',
    message: 'Session ended',
};

const mockDuaResponse: RequestDuaResponse = {
    status: 'success',
    message: 'Dua requested',
};

const mockPeerConnectFoundResponse: ConnectPeerSupporterResponse = {
    status: 'supporter_found',
    message: 'Supporter found',
    peerSupportRequestId: 'peer-req-1',
    supporterDetails: { id: 'supp-1', name: 'Ali' }
};
const mockPeerConnectUnavailableResponse: ConnectPeerSupporterResponse = {
    status: 'unavailable',
    message: 'No supporters available',
};


describe('QalbRescueContext Actions', () => {
  const wrapper: React.FC = ({ children }) => <QalbRescueProvider>{children}</QalbRescueProvider>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('initiateQalbRescue should dispatch success on API success', async () => {
    (emergencyService.startEmergencySession as jest.Mock).mockResolvedValueOnce(mockStartResponse);
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    act(() => {
      result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType });
    });
    expect(result.current.state.isLoading).toBe(true);
    await waitForNextUpdate();

    expect(emergencyService.startEmergencySession).toHaveBeenCalledWith({ triggerType: 'manual' });
    expect(result.current.state.isLoading).toBe(false);
    expect(result.current.state.isSessionActive).toBe(true);
    expect(result.current.state.sessionId).toBe(mockStartResponse.sessionId);
    expect(result.current.state.currentStepName).toBe(mockStartResponse.currentStep);
    expect(result.current.state.currentStepContent).toEqual(mockStartResponse.stepContent);
  });

  it('initiateQalbRescue should dispatch failure on API error', async () => {
    const errorMessage = 'API Error Start';
    (emergencyService.startEmergencySession as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    act(() => {
      result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType });
    });
    expect(result.current.state.isLoading).toBe(true);
    await waitForNextUpdate();

    expect(result.current.state.isLoading).toBe(false);
    expect(result.current.state.isSessionActive).toBe(false);
    expect(result.current.state.error).toBe(errorMessage);
  });

  it('advanceToNextStep should dispatch success on API success', async () => {
    (emergencyService.progressToNextStep as jest.Mock).mockResolvedValueOnce(mockProgressResponse);
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    // First, initiate a session to set sessionId
    (emergencyService.startEmergencySession as jest.Mock).mockResolvedValueOnce(mockStartResponse);
    act(() => { result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType }); });
    await waitForNextUpdate(); // Wait for session start to complete

    const payload: ProgressQalbRescuePayload = { completedStep: 'grounding' as QalbRescueStepName };
    act(() => {
      result.current.advanceToNextStep(payload);
    });
    expect(result.current.state.isLoading).toBe(true);
    await waitForNextUpdate();

    expect(emergencyService.progressToNextStep).toHaveBeenCalledWith(mockStartResponse.sessionId, payload);
    expect(result.current.state.isLoading).toBe(false);
    expect(result.current.state.currentStepName).toBe(mockProgressResponse.currentStep);
    expect(result.current.state.currentStepContent).toEqual(mockProgressResponse.stepContent);
  });

  it('advanceToNextStep should handle session completion', async () => {
    const mockCompletionResponse: ProgressQalbRescueResponse = { sessionId: 'sess-123', currentStep: null, stepContent: null, message: "Completed" };
    (emergencyService.progressToNextStep as jest.Mock).mockResolvedValueOnce(mockCompletionResponse);
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    (emergencyService.startEmergencySession as jest.Mock).mockResolvedValueOnce(mockStartResponse);
    act(() => { result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType }); });
    await waitForNextUpdate();

    act(() => { result.current.advanceToNextStep({ completedStep: 'connection' as QalbRescueStepName }); });
    await waitForNextUpdate();

    expect(result.current.state.isSessionActive).toBe(false);
    expect(result.current.state.sessionId).toBeNull();
  });


  it('submitFeedbackAndEndSession should dispatch success on API success', async () => {
    (emergencyService.updateQalbRescueSession as jest.Mock).mockResolvedValueOnce(mockSessionEndResponse);
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    (emergencyService.startEmergencySession as jest.Mock).mockResolvedValueOnce(mockStartResponse);
     act(() => { result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType }); });
    await waitForNextUpdate();

    const payload: UpdateQalbRescueSessionPayload = { status: 'completed', feedback: 'Good session' };
    act(() => {
      result.current.submitFeedbackAndEndSession(payload);
    });
    expect(result.current.state.isLoading).toBe(true);
    await waitForNextUpdate();

    expect(emergencyService.updateQalbRescueSession).toHaveBeenCalledWith(mockStartResponse.sessionId, payload);
    expect(result.current.state.isLoading).toBe(false);
    expect(result.current.state.isSessionActive).toBe(false); // Session should be inactive
    expect(result.current.state.sessionId).toBeNull(); // Session ID cleared
  });

  it('triggerRequestDua should dispatch success', async () => {
    (emergencyService.requestDua as jest.Mock).mockResolvedValueOnce(mockDuaResponse);
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    (emergencyService.startEmergencySession as jest.Mock).mockResolvedValueOnce(mockStartResponse);
    act(() => { result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType }); });
    await waitForNextUpdate();

    act(() => { result.current.triggerRequestDua(); });
    expect(result.current.state.duaRequestStatus).toBe('loading');
    await waitForNextUpdate();

    expect(emergencyService.requestDua).toHaveBeenCalledWith(mockStartResponse.sessionId);
    expect(result.current.state.duaRequestStatus).toBe('success');
  });

  it('triggerConnectPeer should dispatch success when supporter found', async () => {
    (emergencyService.connectPeerSupporter as jest.Mock).mockResolvedValueOnce(mockPeerConnectFoundResponse);
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    (emergencyService.startEmergencySession as jest.Mock).mockResolvedValueOnce(mockStartResponse);
    act(() => { result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType }); });
    await waitForNextUpdate();

    act(() => { result.current.triggerConnectPeer(); });
    expect(result.current.state.peerConnectStatus).toBe('loading');
    await waitForNextUpdate();

    expect(emergencyService.connectPeerSupporter).toHaveBeenCalledWith(mockStartResponse.sessionId, undefined);
    expect(result.current.state.peerConnectStatus).toBe('supporter_found');
    expect(result.current.state.peerSupportRequestId).toBe(mockPeerConnectFoundResponse.peerSupportRequestId);
    expect(result.current.state.peerSupporterDetails).toEqual(mockPeerConnectFoundResponse.supporterDetails);
  });

   it('triggerConnectPeer should dispatch success when no supporter available', async () => {
    (emergencyService.connectPeerSupporter as jest.Mock).mockResolvedValueOnce(mockPeerConnectUnavailableResponse);
    const { result, waitForNextUpdate } = renderHook(() => useQalbRescue(), { wrapper });

    (emergencyService.startEmergencySession as jest.Mock).mockResolvedValueOnce(mockStartResponse);
    act(() => { result.current.initiateQalbRescue({ triggerType: 'manual' as EmergencyTriggerType }); });
    await waitForNextUpdate();

    act(() => { result.current.triggerConnectPeer(); });
    await waitForNextUpdate();

    expect(result.current.state.peerConnectStatus).toBe('unavailable');
  });


  it('clearQalbRescueSession should reset state', () => {
    const { result } = renderHook(() => useQalbRescue(), { wrapper });
    // Set some state first
    act(() => {
        result.current.dispatch({
            type: 'SESSION_START_SUCCESS',
            payload: { sessionId: 'sess-123', currentStepName: 'grounding' as QalbRescueStepName, currentStepContent: {} as QalbRescueStepContent }
        });
    });
    expect(result.current.state.sessionId).not.toBeNull();

    act(() => {
      result.current.clearQalbRescueSession();
    });
    expect(result.current.state).toEqual(initialQalbRescueState);
  });
});
