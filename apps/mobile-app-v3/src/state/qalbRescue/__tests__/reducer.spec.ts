import { qalbRescueReducer } from '../reducer';
import { initialQalbRescueState, QalbRescueState, QalbRescueAction } from '../types';
import { QalbRescueStepContent, QalbRescueStepName, ConnectPeerSupporterResponse } from '../../../services/api/types';

describe('qalbRescueReducer', () => {
  let initialState: QalbRescueState;

  beforeEach(() => {
    initialState = { ...initialQalbRescueState };
  });

  it('should return the initial state for unknown action', () => {
    const unknownAction = { type: 'UNKNOWN_ACTION' } as any;
    expect(qalbRescueReducer(initialState, unknownAction)).toEqual(initialState);
  });

  it('should handle SET_LOADING', () => {
    const action: QalbRescueAction = { type: 'SET_LOADING', payload: true };
    const expectedState: QalbRescueState = { ...initialState, isLoading: true };
    expect(qalbRescueReducer(initialState, action)).toEqual(expectedState);
  });

  it('should handle SET_ERROR', () => {
    const errorMessage = 'An error occurred';
    const action: QalbRescueAction = { type: 'SET_ERROR', payload: errorMessage };
    const expectedState: QalbRescueState = { ...initialState, error: errorMessage, isLoading: false };
    expect(qalbRescueReducer(initialState, action)).toEqual(expectedState);
  });

  describe('Session Start Actions', () => {
    it('should handle SESSION_START_INIT', () => {
      const action: QalbRescueAction = { type: 'SESSION_START_INIT' };
      // Modify initial state to simulate a previous session to ensure it's reset
      const prevState: QalbRescueState = { ...initialState, sessionId: 'old-session', error: 'old-error' };
      const expectedState: QalbRescueState = { ...initialQalbRescueState, isLoading: true, isSessionActive: false };
      expect(qalbRescueReducer(prevState, action)).toEqual(expectedState);
    });

    it('should handle SESSION_START_SUCCESS', () => {
      const mockStepContent: QalbRescueStepContent = { title: 'Grounding', description: 'Desc' };
      const payload = { sessionId: 'new-id', currentStepName: 'grounding' as QalbRescueStepName, currentStepContent: mockStepContent };
      const action: QalbRescueAction = { type: 'SESSION_START_SUCCESS', payload };
      const expectedState: QalbRescueState = {
        ...initialState,
        isLoading: false,
        isSessionActive: true,
        sessionId: 'new-id',
        currentStepName: 'grounding',
        currentStepContent: mockStepContent,
        error: null,
      };
      expect(qalbRescueReducer({ ...initialState, isLoading: true }, action)).toEqual(expectedState);
    });

    it('should handle SESSION_START_FAILURE', () => {
      const errorMessage = 'Failed to start';
      const action: QalbRescueAction = { type: 'SESSION_START_FAILURE', payload: errorMessage };
      const expectedState: QalbRescueState = {
        ...initialState,
        isLoading: false,
        isSessionActive: false,
        error: errorMessage,
      };
      expect(qalbRescueReducer({ ...initialState, isLoading: true }, action)).toEqual(expectedState);
    });
  });

  describe('Step Progress Actions', () => {
    const activeSessionState: QalbRescueState = {
        ...initialState,
        isSessionActive: true,
        sessionId: 'active-session',
        currentStepName: 'grounding',
        currentStepContent: { title: 'Grounding', description: '...'}
    };

    it('should handle STEP_PROGRESS_INIT', () => {
      const action: QalbRescueAction = { type: 'STEP_PROGRESS_INIT' };
      const expectedState: QalbRescueState = { ...activeSessionState, isLoading: true, error: null };
      expect(qalbRescueReducer(activeSessionState, action)).toEqual(expectedState);
    });

    it('should handle STEP_PROGRESS_SUCCESS with new step', () => {
      const mockStepContent: QalbRescueStepContent = { title: 'Breathing', description: '...' };
      const payload = { currentStepName: 'breathing' as QalbRescueStepName, currentStepContent: mockStepContent };
      const action: QalbRescueAction = { type: 'STEP_PROGRESS_SUCCESS', payload };
      const expectedState: QalbRescueState = {
        ...activeSessionState,
        isLoading: false,
        currentStepName: 'breathing',
        currentStepContent: mockStepContent,
      };
      expect(qalbRescueReducer({ ...activeSessionState, isLoading: true }, action)).toEqual(expectedState);
    });

    it('should handle STEP_PROGRESS_SUCCESS with session completion (null step)', () => {
      const payload = { currentStepName: null, currentStepContent: null };
      const action: QalbRescueAction = { type: 'STEP_PROGRESS_SUCCESS', payload };
      // Expect reset to initial state but session inactive
      const expectedState: QalbRescueState = { ...initialQalbRescueState, isSessionActive: false };
      expect(qalbRescueReducer({ ...activeSessionState, isLoading: true }, action)).toEqual(expectedState);
    });

    it('should handle STEP_PROGRESS_FAILURE', () => {
      const errorMessage = 'Failed to progress';
      const action: QalbRescueAction = { type: 'STEP_PROGRESS_FAILURE', payload: errorMessage };
      const expectedState: QalbRescueState = { ...activeSessionState, isLoading: false, error: errorMessage };
      expect(qalbRescueReducer({ ...activeSessionState, isLoading: true }, action)).toEqual(expectedState);
    });
  });

  describe('Session End Actions', () => {
     const activeSessionState: QalbRescueState = {
        ...initialState,
        isSessionActive: true,
        sessionId: 'active-session',
    };
    it('should handle SESSION_END_INIT', () => {
      const action: QalbRescueAction = { type: 'SESSION_END_INIT' };
      expect(qalbRescueReducer(activeSessionState, action)).toEqual({ ...activeSessionState, isLoading: true, error: null });
    });

    it('should handle SESSION_END_SUCCESS', () => {
      const action: QalbRescueAction = { type: 'SESSION_END_SUCCESS' };
      expect(qalbRescueReducer({ ...activeSessionState, isLoading: true }, action)).toEqual({...initialQalbRescueState, isSessionActive: false});
    });

    it('should handle SESSION_END_FAILURE', () => {
      const errorMessage = 'Failed to end';
      const action: QalbRescueAction = { type: 'SESSION_END_FAILURE', payload: errorMessage };
      // Session remains active on failure to end, error is shown
      expect(qalbRescueReducer({ ...activeSessionState, isLoading: true }, action))
        .toEqual({ ...activeSessionState, isLoading: false, error: errorMessage });
    });
  });

  describe('Community Actions', () => {
    const activeSessionState: QalbRescueState = {
        ...initialState,
        isSessionActive: true,
        sessionId: 'active-session',
    };

    // Request Dua
    it('should handle REQUEST_DUA_INIT', () => {
      const action: QalbRescueAction = { type: 'REQUEST_DUA_INIT' };
      expect(qalbRescueReducer(activeSessionState, action)).toEqual({ ...activeSessionState, duaRequestStatus: 'loading', error: null });
    });
    it('should handle REQUEST_DUA_SUCCESS', () => {
      const action: QalbRescueAction = { type: 'REQUEST_DUA_SUCCESS' };
      expect(qalbRescueReducer({ ...activeSessionState, duaRequestStatus: 'loading' }, action)).toEqual({ ...activeSessionState, duaRequestStatus: 'success' });
    });
    it('should handle REQUEST_DUA_FAILURE', () => {
      const errorMessage = 'Dua request failed';
      const action: QalbRescueAction = { type: 'REQUEST_DUA_FAILURE', payload: errorMessage };
      expect(qalbRescueReducer({ ...activeSessionState, duaRequestStatus: 'loading' }, action))
        .toEqual({ ...activeSessionState, duaRequestStatus: 'error', error: errorMessage });
    });

    // Connect Peer
    it('should handle CONNECT_PEER_INIT', () => {
      const action: QalbRescueAction = { type: 'CONNECT_PEER_INIT' };
      expect(qalbRescueReducer(activeSessionState, action)).toEqual({ ...activeSessionState, peerConnectStatus: 'loading', error: null });
    });
    it('should handle CONNECT_PEER_SUCCESS', () => {
      const mockResponse: ConnectPeerSupporterResponse = { status: 'supporter_found', message: 'Found!', peerSupportRequestId: 'req-1', supporterDetails: { id: 'sup-1', name: 'Ali' }};
      const action: QalbRescueAction = { type: 'CONNECT_PEER_SUCCESS', payload: mockResponse };
      expect(qalbRescueReducer({ ...activeSessionState, peerConnectStatus: 'loading' }, action))
        .toEqual({ ...activeSessionState, peerConnectStatus: 'supporter_found', peerSupportRequestId: 'req-1', peerSupporterDetails: { id: 'sup-1', name: 'Ali' }});
    });
     it('should handle CONNECT_PEER_SUCCESS (unavailable)', () => {
      const mockResponse: ConnectPeerSupporterResponse = { status: 'unavailable', message: 'None available.'};
      const action: QalbRescueAction = { type: 'CONNECT_PEER_SUCCESS', payload: mockResponse };
      expect(qalbRescueReducer({ ...activeSessionState, peerConnectStatus: 'loading' }, action))
        .toEqual({ ...activeSessionState, peerConnectStatus: 'unavailable', peerSupportRequestId: undefined, peerSupporterDetails: undefined});
    });
    it('should handle CONNECT_PEER_FAILURE', () => {
      const errorPayload = { message: 'Peer connect failed', status: 'error' as ConnectPeerSupporterResponse['status'] };
      const action: QalbRescueAction = { type: 'CONNECT_PEER_FAILURE', payload: errorPayload };
      expect(qalbRescueReducer({ ...activeSessionState, peerConnectStatus: 'loading' }, action))
        .toEqual({ ...activeSessionState, peerConnectStatus: 'error', error: errorPayload.message });
    });
  });

  it('should handle CLEAR_SESSION', () => {
    const prevState: QalbRescueState = {
      sessionId: 'some-id',
      currentStepName: 'comfort',
      currentStepContent: { title: 'Comfort', description: '...' },
      isLoading: false,
      error: null,
      isSessionActive: true,
      duaRequestStatus: 'success',
      peerConnectStatus: 'supporter_found',
      peerSupportRequestId: 'req-123',
      peerSupporterDetails: {id: 'supp-123', name: 'Ali'}
    };
    const action: QalbRescueAction = { type: 'CLEAR_SESSION' };
    expect(qalbRescueReducer(prevState, action)).toEqual(initialQalbRescueState);
  });
});
