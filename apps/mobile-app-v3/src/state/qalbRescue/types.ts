import {
    QalbRescueStepName,
    QalbRescueStepContent,
    ConnectPeerSupporterResponse // For the status property
} from '../../services/api/types';

export interface QalbRescueState {
  sessionId: string | null;
  currentStepName: QalbRescueStepName | null;
  currentStepContent: QalbRescueStepContent | null;
  isLoading: boolean;
  error: string | null;
  isSessionActive: boolean;
  duaRequestStatus: 'idle' | 'loading' | 'success' | 'error';
  peerConnectStatus: ConnectPeerSupporterResponse['status'] | 'idle' | 'loading'; // Use status from API type
  peerSupportRequestId?: string | null;
  peerSupporterDetails?: ConnectPeerSupporterResponse['supporterDetails'] | null;
}

export const initialQalbRescueState: QalbRescueState = {
  sessionId: null,
  currentStepName: null,
  currentStepContent: null,
  isLoading: false,
  error: null,
  isSessionActive: false,
  duaRequestStatus: 'idle',
  peerConnectStatus: 'idle',
  peerSupportRequestId: null,
  peerSupporterDetails: null,
};

export type QalbRescueAction =
  | { type: 'SESSION_START_INIT' }
  | { type: 'SESSION_START_SUCCESS'; payload: { sessionId: string; currentStepName: QalbRescueStepName; currentStepContent: QalbRescueStepContent } }
  | { type: 'SESSION_START_FAILURE'; payload: string }
  | { type: 'STEP_PROGRESS_INIT' }
  | { type: 'STEP_PROGRESS_SUCCESS'; payload: { currentStepName: QalbRescueStepName | null; currentStepContent: QalbRescueStepContent | null } }
  | { type: 'STEP_PROGRESS_FAILURE'; payload: string }
  | { type: 'SESSION_END_INIT' }
  | { type: 'SESSION_END_SUCCESS' }
  | { type: 'SESSION_END_FAILURE'; payload: string }
  | { type: 'REQUEST_DUA_INIT' }
  | { type: 'REQUEST_DUA_SUCCESS' }
  | { type: 'REQUEST_DUA_FAILURE'; payload: string }
  | { type: 'CONNECT_PEER_INIT' }
  | { type: 'CONNECT_PEER_SUCCESS'; payload: ConnectPeerSupporterResponse }
  | { type: 'CONNECT_PEER_FAILURE'; payload: { message: string, status?: ConnectPeerSupporterResponse['status'] } }
  | { type: 'CLEAR_SESSION' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };
