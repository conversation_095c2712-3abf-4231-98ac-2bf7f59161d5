import { QalbRescueState, QalbRescueAction, initialQalbRescueState } from './types';

export const qalbRescueReducer = (state: QalbRescueState, action: QalbRescueAction): QalbRescueState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };

    case 'SESSION_START_INIT':
      return {
        ...initialQalbRescueState, // Reset to initial on new session start
        isLoading: true,
        isSessionActive: false, // Explicitly false until success
      };
    case 'SESSION_START_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isSessionActive: true,
        error: null,
        sessionId: action.payload.sessionId,
        currentStepName: action.payload.currentStepName,
        currentStepContent: action.payload.currentStepContent,
      };
    case 'SESSION_START_FAILURE':
      return {
        ...state,
        isLoading: false,
        isSessionActive: false,
        error: action.payload,
        sessionId: null,
        currentStepName: null,
        currentStepContent: null,
      };

    case 'STEP_PROGRESS_INIT':
      return { ...state, isLoading: true, error: null };
    case 'STEP_PROGRESS_SUCCESS':
      if (action.payload.currentStepName === null) { // Session ended
        return {
          ...initialQalbRescueState, // Or a specific "completed" state
          isSessionActive: false,
        };
      }
      return {
        ...state,
        isLoading: false,
        currentStepName: action.payload.currentStepName,
        currentStepContent: action.payload.currentStepContent,
      };
    case 'STEP_PROGRESS_FAILURE':
      return { ...state, isLoading: false, error: action.payload };

    case 'SESSION_END_INIT':
      return { ...state, isLoading: true, error: null };
    case 'SESSION_END_SUCCESS':
      return {
        ...initialQalbRescueState,
        isSessionActive: false // Ensure session is marked inactive
      };
    case 'SESSION_END_FAILURE':
      // Keep current session active, but show error. User might retry ending.
      return { ...state, isLoading: false, error: action.payload };

    case 'REQUEST_DUA_INIT':
      return { ...state, duaRequestStatus: 'loading', error: null };
    case 'REQUEST_DUA_SUCCESS':
      return { ...state, duaRequestStatus: 'success' };
    case 'REQUEST_DUA_FAILURE':
      return { ...state, duaRequestStatus: 'error', error: action.payload };

    case 'CONNECT_PEER_INIT':
      return { ...state, peerConnectStatus: 'loading', error: null };
    case 'CONNECT_PEER_SUCCESS':
      return {
        ...state,
        peerConnectStatus: action.payload.status,
        peerSupportRequestId: action.payload.peerSupportRequestId || null,
        peerSupporterDetails: action.payload.supporterDetails || null,
      };
    case 'CONNECT_PEER_FAILURE':
      return {
        ...state,
        peerConnectStatus: action.payload.status || 'error',
        error: action.payload.message
      };

    case 'CLEAR_SESSION':
      return initialQalbRescueState;

    default:
      return state;
  }
};
