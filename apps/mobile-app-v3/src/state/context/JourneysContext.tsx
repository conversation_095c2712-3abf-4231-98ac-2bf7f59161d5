import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Journey, JourneysState } from '../../types';

interface JourneysContextType {
  state: JourneysState;
  dispatch: React.Dispatch<any>;
  fetchJourneys: () => Promise<void>;
  startJourney: (journey: Journey) => Promise<void>;
  updateProgress: (journeyId: string, progress: number) => Promise<void>;
}

const initialState: JourneysState = {
  journeys: [],
  currentJourney: null,
  activities: {},
  isLoading: false,
  error: null,
};

const journeysReducer = (state: JourneysState, action: any): JourneysState => {
  switch (action.type) {
    case 'FETCH_JOURNEYS_REQUEST':
      return { ...state, isLoading: true, error: null };
    case 'FETCH_JOURNEYS_SUCCESS':
      return { ...state, isLoading: false, journeys: action.payload };
    case 'FETCH_JOURNEYS_FAILURE':
      return { ...state, isLoading: false, error: action.payload };
    default:
      return state;
  }
};

export const JourneysContext = createContext<JourneysContextType | undefined>(
  undefined
);

export const JourneysProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(journeysReducer, initialState);

  const fetchJourneys = async () => {
    dispatch({ type: 'FETCH_JOURNEYS_REQUEST' });
    try {
      // Mock implementation
      const journeys: Journey[] = [];
      dispatch({ type: 'FETCH_JOURNEYS_SUCCESS', payload: journeys });
    } catch (error) {
      dispatch({
        type: 'FETCH_JOURNEYS_FAILURE',
        payload: 'Failed to fetch journeys',
      });
    }
  };

  const startJourney = async (journey: Journey) => {
    // Mock implementation
    console.log('Starting journey:', journey);
  };

  const updateProgress = async (journeyId: string, progress: number) => {
    // Mock implementation
    console.log('Updating progress:', journeyId, progress);
  };

  return (
    <JourneysContext.Provider
      value={{
        state,
        dispatch,
        fetchJourneys,
        startJourney,
        updateProgress,
      }}
    >
      {children}
    </JourneysContext.Provider>
  );
};

export const useJourneys = () => {
  const context = useContext(JourneysContext);
  if (!context) {
    throw new Error('useJourneys must be used within a JourneysProvider');
  }
  return context;
};
