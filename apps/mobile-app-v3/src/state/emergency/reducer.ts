import { EmergencyState, EmergencyAction } from './types';

export const initialEmergencyState: EmergencyState = {
  contacts: [],
  resources: [],
  hasCompletedSetup: false,
  loading: false,
  error: null,
};

export function emergencyReducer(state: EmergencyState, action: EmergencyAction): EmergencyState {
  switch (action.type) {
    case 'EMERGENCY_FETCH_REQUEST':
      return { ...state, loading: true, error: null };
    case 'EMERGENCY_FETCH_SUCCESS':
      return {
        ...state,
        contacts: action.payload.contacts,
        resources: action.payload.resources,
        loading: false,
        error: null,
      };
    case 'EMERGENCY_FETCH_FAILURE':
      return { ...state, loading: false, error: action.payload };
    case 'EMERGENCY_ADD_CONTACT':
      return { ...state, contacts: [...state.contacts, action.payload] };
    case 'EMERGENCY_UPDATE_CONTACT':
      return {
        ...state,
        contacts: state.contacts.map(contact =>
          contact.id === action.payload.id ? action.payload : contact
        ),
      };
    case 'EMERGENCY_DELETE_CONTACT':
      return {
        ...state,
        contacts: state.contacts.filter(contact => contact.id !== action.payload),
      };
    case 'EMERGENCY_COMPLETE_SETUP':
      return { ...state, hasCompletedSetup: true };
    default:
      return state;
  }
}

