/**
 * Mock Today's Practices Data
 * For testing and development of the 5 daily components
 */

import { DailyPractice } from '../../../../libs/shared-types/src/lib/journey.types';

export const mockTodaysPractices: DailyPractice[] = [
  {
    id: 'morning-checkin-1',
    type: 'MorningCheckIn',
    title: 'Morning Spiritual Check-In',
    description: 'Begin your day with intention and spiritual awareness. Set your niyyah and assess your current state.',
    duration: 2,
    instructions: 'Take a moment to center yourself and honestly assess your spiritual and emotional state.',
    benefits: [
      'Establishes daily spiritual baseline',
      'Creates mindful intention setting',
      'Builds self-awareness',
      'Connects you with <PERSON> at day\'s start'
    ],
    layerFocus: 'qalb',
    difficultyLevel: 'beginner',
    componentDetails: {
      moodScaleMin: 1,
      moodScaleMax: 10,
      moodDescriptorLow: 'Heavy',
      moodDescriptorHigh: 'Light',
      energyPrompt: 'How is your energy level today?',
      spiritualStatePrompt: 'Describe your spiritual state in a few words:',
      intentionPrompt: 'What is your niyyah (intention) for today?'
    }
  },
  {
    id: 'name-of-allah-1',
    type: 'NameOfAllahSpotlight',
    title: '<PERSON><PERSON><PERSON><PERSON> (The Most Merciful)',
    description: 'Reflect deeply on <PERSON>\'s infinite mercy and how it manifests in your life.',
    duration: 8,
    instructions: 'Contemplate the meaning of Ar-Rahman and recite it with presence and devotion.',
    arabicText: 'الرَّحْمَن',
    transliteration: 'Ar-Rahman',
    translation: 'The Most Merciful, The Beneficent',
    benefits: [
      'Deepens connection with Allah\'s mercy',
      'Softens the heart',
      'Increases gratitude',
      'Promotes self-compassion'
    ],
    layerFocus: 'qalb',
    difficultyLevel: 'beginner',
    componentDetails: {
      name: 'Ar-Rahman',
      arabicScript: 'الرَّحْمَن',
      meaning: 'Ar-Rahman refers to Allah\'s all-encompassing mercy that extends to all creation, believers and non-believers alike.',
      significance: 'This beautiful name reminds us that Allah\'s mercy encompasses everything and everyone.',
      reflectionPrompt: 'How have you experienced Allah\'s mercy in your life today?',
      practicalApplication: 'Practice showing mercy and compassion to others as a reflection of Allah\'s mercy.',
      dhikrCount: 33,
      audioUrl: '/audio/ar-rahman-recitation.mp3'
    }
  },
  {
    id: 'quranic-verse-1',
    type: 'QuranicVerseReflection',
    title: 'Verse of Comfort and Hope',
    description: 'Find peace and guidance in Allah\'s words, specifically chosen for your healing journey.',
    duration: 10,
    instructions: 'Read, listen, and reflect on this verse. Consider how it applies to your current situation.',
    arabicText: 'وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ ۚ إِنَّ اللَّهَ بَالِغُ أَمْرِهِ ۚ قَدْ جَعَلَ اللَّهُ لِكُلِّ شَيْءٍ قَدْرًا',
    transliteration: 'Wa man yatawakkal \'ala Allahi fa-huwa hasbuhu. Inna Allaha baligu amrihi. Qad ja\'ala Allahu li-kulli shay\'in qadra.',
    translation: 'And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose. Allah has already set for everything a [decreed] extent.',
    benefits: [
      'Builds trust in Allah (Tawakkul)',
      'Reduces anxiety and worry',
      'Strengthens faith',
      'Provides divine perspective'
    ],
    layerFocus: 'qalb',
    difficultyLevel: 'beginner',
    componentDetails: {
      arabicText: 'وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ ۚ إِنَّ اللَّهَ بَالِغُ أَمْرِهِ ۚ قَدْ جَعَلَ اللَّهُ لِكُلِّ شَيْءٍ قَدْرًا',
      translationEn: 'And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose. Allah has already set for everything a [decreed] extent.',
      surahName: 'At-Talaq',
      verseNumber: 3,
      contextualExplanation: 'This verse reminds us that Allah is sufficient for those who trust in Him completely.',
      reflectionPrompts: ['What areas of your life need more tawakkul (trust in Allah)?', 'How can you strengthen your reliance on Allah today?'],
      practicalApplication: 'When facing challenges, remember to place your trust in Allah and make du\'a.',
      audioUrl: '/audio/quran-65-3.mp3'
    }
  },
  {
    id: 'reflection-journaling-1',
    type: 'PersonalReflectionJournaling',
    title: 'Daily Spiritual Reflection',
    description: 'Process your spiritual insights and track your growth through mindful journaling.',
    duration: 10,
    instructions: 'Write honestly about your spiritual journey. Use the prompts to guide your reflection.',
    benefits: [
      'Processes spiritual insights',
      'Tracks personal growth',
      'Increases self-awareness',
      'Strengthens connection with Allah'
    ],
    layerFocus: 'aql',
    difficultyLevel: 'beginner',
    componentDetails: {
      prompts: [
        'What did I learn about myself today?',
        'How did I see Allah\'s guidance in my day?',
        'What am I grateful for?',
        'What challenges did I face and how did I handle them?'
      ],
      gratitudeCount: 3,
      voiceNoteOption: true
    }
  },
  {
    id: 'sunnah-practice-1',
    type: 'SunnahPractice',
    title: 'Mindful Wudu for Heart Purification',
    description: 'Transform your ablution into a mindful spiritual practice following the Sunnah.',
    duration: 8,
    instructions: 'Perform wudu with complete presence and intention, using it as a means of spiritual purification.',
    benefits: [
      'Combines physical and spiritual cleansing',
      'Follows prophetic example',
      'Increases mindfulness',
      'Prepares heart for prayer'
    ],
    layerFocus: 'jism',
    difficultyLevel: 'beginner',
    ruqyaComponent: false,
    componentDetails: {
      steps: [
        {
          action: 'Intention (Niyyah)',
          dua: 'Bismillah',
          mindfulness: 'Set intention for spiritual purification'
        },
        {
          action: 'Wash hands three times',
          dua: 'Allahumma ighfir li dhanbi wa wassi\' li fi dari wa barik li fi rizqi',
          mindfulness: 'Feel the water cleansing away impurities'
        },
        {
          action: 'Rinse mouth three times',
          dua: 'Allahumma a\'inni \'ala dhikrika wa shukrika wa husni \'ibadatika',
          mindfulness: 'Purify your speech and intentions'
        }
      ],
      finalDua: 'Ash-hadu an la ilaha illa Allah wahdahu la sharika lahu, wa ash-hadu anna Muhammadan \'abduhu wa rasuluhu.'
    }
  }
];

export default mockTodaysPractices;