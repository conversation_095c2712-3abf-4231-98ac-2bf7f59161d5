{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "noEmit": true, "isolatedModules": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "inlineSources": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/constants/*": ["constants/*"], "@/hooks/*": ["hooks/*"], "@/services/*": ["services/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/state/*": ["state/*"], "@/data/*": ["data/*"], "@/screens/*": ["screens/*"], "@/navigation/*": ["navigation/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "references": [{"path": "../../libs/shared-types"}]}