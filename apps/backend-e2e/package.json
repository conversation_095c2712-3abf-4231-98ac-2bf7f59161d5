{"name": "@qalb-healing-workspace/backend-e2e", "version": "0.0.1", "private": true, "nx": {"implicitDependencies": ["@qalb-healing-workspace/backend"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{projectRoot}/test-output/jest/coverage"], "options": {"jestConfig": "apps/backend-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["@qalb-healing-workspace/backend:build", "@qalb-healing-workspace/backend:serve"]}}}}