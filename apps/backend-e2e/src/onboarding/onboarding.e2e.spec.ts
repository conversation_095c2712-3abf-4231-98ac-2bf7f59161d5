import axios, { AxiosInstance } from 'axios';

// Base URL for the E2E backend instance
// This should be configurable, e.g., via environment variables
const API_URL = process.env.E2E_API_URL || 'http://localhost:3333/api'; // Default for local backend

interface TestUser {
    id: string;
    token: string;
}

// Placeholder for authentication - in a real scenario, this would involve:
// 1. Creating a test user (if not existing) via an API or DB script.
// 2. Logging in that user to get a token.
// For now, we'll assume a token can be made available or tests run against non-auth endpoints first.
let testUser: TestUser | null = null;
let apiClient: AxiosInstance;

// Function to simulate user login and get a token (replace with actual login)
// This is a simplified placeholder. A real implementation would hit an /auth/login endpoint.
const authenticateTestUser = async (): Promise<TestUser> => {
    // In a real E2E setup, you would:
    // 1. Ensure a test user exists (e.g. <EMAIL> / password123)
    // 2. Call your actual login endpoint:
    //    const response = await axios.post(`${API_URL}/auth/login`, { email: '<EMAIL>', password: 'password123' });
    //    return { id: response.data.user.id, token: response.data.token };
    
    // For now, returning a placeholder. Tests requiring auth will need this to be real or will be skipped/adapted.
    console.warn("E2E Authentication is using a placeholder. Real authentication flow needed for full E2E.");
    return { id: `e2e-user-${Date.now()}`, token: 'placeholder-e2e-jwt-token' };
};


describe('Onboarding E2E Tests', () => {
    beforeAll(async () => {
        // It's good practice to ensure the backend is reachable before running tests
        try {
            await axios.get(`${API_URL}/health`); // Assuming a health check endpoint exists at /api/health
        } catch (error) {
            console.error(`E2E Test Backend not reachable at ${API_URL}. Error: ${error.message}`);
            // Optionally, throw to stop tests if backend is down
            // throw new Error(`E2E Test Backend not reachable at ${API_URL}`);
        }
        
        // Authenticate a test user once for the suite
        // testUser = await authenticateTestUser(); // Uncomment when auth is ready
        // For now, some tests might fail or need to be adapted if they require real auth
        apiClient = axios.create({
            baseURL: API_URL,
            // headers: testUser ? { 'Authorization': `Bearer ${testUser.token}` } : {}
        });
    });

    // Helper to set auth header for tests that need it
    const getAuthHeaders = () => {
        return testUser ? { 'Authorization': `Bearer ${testUser.token}` } : {};
    }

    describe('POST /onboarding/start', () => {
        it('should start an onboarding session successfully', async () => {
            // This test might pass without real auth if /start is public or auth is loosely checked
            // If /start requires strict auth, this will fail until authenticateTestUser is implemented.
            // For now, let's assume we can get a placeholder token or the endpoint is testable.
             if (!testUser) testUser = await authenticateTestUser(); // Simulate login for this test
             apiClient.defaults.headers.common['Authorization'] = `Bearer ${testUser.token}`;


            const deviceInfo = { platform: 'e2e-test-runner', screenSize: '800x600' };
            try {
                const response = await apiClient.post('/onboarding/start', { deviceInfo });
                expect(response.status).toBe(201);
                expect(response.data.status).toBe('success');
                expect(response.data.data.session.sessionId).toBeDefined();
                expect(response.data.data.session.currentStep).toBe('welcome');
                expect(response.data.data.question.question.id).toBe('welcome');
            } catch (error) {
                console.error("Error in /onboarding/start E2E test:", error.response?.data || error.message);
                throw error;
            }
        });

        it('should fail to start session without authentication if endpoint is protected', async () => {
            // Temporarily remove auth header for this test
            const tempClient = axios.create({ baseURL: API_URL });
            const deviceInfo = { platform: 'e2e-no-auth' };
            try {
                await tempClient.post('/onboarding/start', { deviceInfo });
                // If it reaches here, the endpoint might not be protected as expected
            } catch (error) {
                expect(error.response.status).toBe(401); // Expect Unauthorized
            }
        });
    });

    describe('Full Onboarding Flow', () => {
        let sessionId: string;

        it('should allow a user to complete the onboarding flow', async () => {
            if (!testUser) testUser = await authenticateTestUser();
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${testUser.token}`;

            // 1. Start Onboarding
            const startResponse = await apiClient.post('/onboarding/start', {
                deviceInfo: { platform: 'e2e-full-flow' }
            });
            expect(startResponse.status).toBe(201);
            sessionId = startResponse.data.data.session.sessionId;
            let currentQuestionId = startResponse.data.data.question.question.id; // e.g. 'welcome'
            let nextExpectedStepId = startResponse.data.data.question.step; // e.g. 'mental_health_awareness'

            expect(currentQuestionId).toBe('welcome');

            // 2. Respond to Welcome
            let responsePayload = { sessionId, stepId: currentQuestionId, response: { action: 'begin' }, timeSpent: 5 };
            let respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // e.g. 'mental_health_awareness'
            nextExpectedStepId = respondResponse.data.data.question.step; // e.g. 'ruqya_knowledge'
            expect(currentQuestionId).toBe('mental_health_awareness');


            // 3. Respond to Mental Health Awareness (select symptom_aware for a common path)
            responsePayload = { sessionId, stepId: currentQuestionId, response: { mental_health_awareness: 'symptom_aware' }, timeSpent: 10 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // e.g. 'ruqya_knowledge' (based on service logic for 'symptom_aware')
            nextExpectedStepId = respondResponse.data.data.question.step; // e.g. 'professional_context'
            expect(currentQuestionId).toBe('mha_experiences'); // symptom_aware -> mha_experiences

            // 4. Respond to mha_experiences
            responsePayload = { sessionId, stepId: currentQuestionId, response: { experiences: ['sleep_problems_fatigue'] }, timeSpent: 10 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // mha_concepts_familiarity
            nextExpectedStepId = respondResponse.data.data.question.step; // ruqya_knowledge
            expect(currentQuestionId).toBe('mha_concepts_familiarity');
            
            // 5. Respond to mha_concepts_familiarity
            responsePayload = { sessionId, stepId: currentQuestionId, response: { familiarity: 'somewhat_familiar_concepts' }, timeSpent: 10 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // ruqya_knowledge
            nextExpectedStepId = respondResponse.data.data.question.step; // professional_context (default from ruqya_knowledge if 'aware' or 'skeptical')
            expect(currentQuestionId).toBe('ruqya_knowledge');

            // 6. Respond to Ruqya Knowledge (select 'unaware' for another common path)
            responsePayload = { sessionId, stepId: currentQuestionId, response: { ruqya_knowledge: 'unaware' }, timeSpent: 8 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // rk_unaware_openness
            nextExpectedStepId = respondResponse.data.data.question.step; // rk_unaware_comfort
            expect(currentQuestionId).toBe('rk_unaware_openness');

            // 7. Respond to rk_unaware_openness
            responsePayload = { sessionId, stepId: currentQuestionId, response: { openness: 'rk_very_interested' }, timeSpent: 8 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // rk_unaware_comfort
            nextExpectedStepId = respondResponse.data.data.question.step; // professional_context
            expect(currentQuestionId).toBe('rk_unaware_comfort');

            // 8. Respond to rk_unaware_comfort
            responsePayload = { sessionId, stepId: currentQuestionId, response: { comfort: 'rk_comfort_somewhat' }, timeSpent: 8 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // professional_context
            nextExpectedStepId = respondResponse.data.data.question.step; // pc_work_challenges
            expect(currentQuestionId).toBe('professional_context');
            
            // 9. Respond to Professional Context
            responsePayload = { sessionId, stepId: currentQuestionId, response: { professional_context: 'student_pc' }, timeSpent: 7 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // pc_work_challenges
            nextExpectedStepId = respondResponse.data.data.question.step; // demographics
            expect(currentQuestionId).toBe('pc_work_challenges');

            // 10. Respond to pc_work_challenges
            responsePayload = { sessionId, stepId: currentQuestionId, response: { challenges: ['none_challenges_pc'] }, timeSpent: 7 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // demographics
            nextExpectedStepId = respondResponse.data.data.question.step; // life_circumstances
            expect(currentQuestionId).toBe('demographics');

            // 11. Respond to Demographics
            const demoResponse = { age_range_section: "18_25_age", gender_section: "male_gender", family_status_section: "single_fs"};
            responsePayload = { sessionId, stepId: currentQuestionId, response: demoResponse, timeSpent: 12 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('continue');
            currentQuestionId = respondResponse.data.data.question.question.id; // life_circumstances
            nextExpectedStepId = respondResponse.data.data.question.step; // null (completion)
            expect(currentQuestionId).toBe('life_circumstances');
            
            // 12. Respond to Life Circumstances (final step before completion)
            const lcResponse = ["academic_work_pressure_lc"];
            responsePayload = { sessionId, stepId: currentQuestionId, response: lcResponse, timeSpent: 6 };
            respondResponse = await apiClient.post('/onboarding/respond', responsePayload);
            
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('completed');
            expect(respondResponse.data.data.profile).toBeDefined();
            expect(respondResponse.data.data.profile.userId).toBe(testUser.id);
            expect(respondResponse.data.data.recommendedPathway).toBeDefined(); // Exact pathway depends on AI service / mock
            expect(respondResponse.data.data.featureConfiguration).toBeDefined();

            // Optional: verify overall status
            const statusResponse = await apiClient.get('/onboarding/status');
            expect(statusResponse.data.data.onboardingCompleted).toBe(true);
            expect(statusResponse.data.data.profile.userId).toBe(testUser.id);
        }, 20000); // Increase timeout for longer E2E flow
    });
    
    // Add more E2E scenarios:
    // - Flow leading to crisis detection
    // - Skip onboarding
    // - Invalid inputs to /respond

    describe('Onboarding with Crisis Detection', () => {
        let sessionId: string;

        beforeEach(async () => {
            if (!testUser) testUser = await authenticateTestUser();
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${testUser.token}`;

            const startResponse = await apiClient.post('/onboarding/start', {
                deviceInfo: { platform: 'e2e-crisis-test' }
            });
            sessionId = startResponse.data.data.session.sessionId;
            // Respond to welcome to move to a step where crisis can be triggered
            await apiClient.post('/onboarding/respond', { sessionId, stepId: 'welcome', response: { action: 'begin' }, timeSpent: 2 });
        });

        it('should trigger crisis detected status when response indicates crisis', async () => {
            // The backend's CrisisDetectionService (and its AI interaction) would determine if this is a crisis.
            // For E2E, we assume the backend is configured such that certain inputs ARE crisis-inducing.
            // Or, the external AI mock (if used by backend for E2E) is set to respond with crisis for this.
            const crisisResponsePayload = { sessionId, stepId: 'mental_health_awareness', response: { mental_health_awareness: 'crisis' }, timeSpent: 5 };
            
            const respondResponse = await apiClient.post('/onboarding/respond', crisisResponsePayload);
            
            expect(respondResponse.status).toBe(200);
            expect(respondResponse.data.status).toBe('crisis_detected');
            expect(respondResponse.data.data.level).toBeDefined(); // e.g., 'high', 'critical'
            expect(respondResponse.data.data.actions).toBeInstanceOf(Array);
        });
    });

    describe('POST /onboarding/skip', () => {
        it('should allow user to skip onboarding and receive a minimal profile', async () => {
            if (!testUser) testUser = await authenticateTestUser();
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${testUser.token}`;

            const skipResponse = await apiClient.post('/onboarding/skip', { reason: 'time_constraint' });

            expect(skipResponse.status).toBe(200);
            expect(skipResponse.data.status).toBe('success');
            expect(skipResponse.data.data.profile).toBeDefined();
            expect(skipResponse.data.data.recommendedPathway).toBe('gentle_introduction'); // Default for non-crisis skip
            expect(skipResponse.data.data.warnings).toContain('Profile incomplete - limited personalization available');
        });

        it('should handle skip due to crisis', async () => {
             if (!testUser) testUser = await authenticateTestUser();
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${testUser.token}`;

            const skipResponse = await apiClient.post('/onboarding/skip', { reason: 'crisis' });
            expect(skipResponse.status).toBe(200);
            expect(skipResponse.data.data.recommendedPathway).toBe('crisis_support');
        });
    });
    
    describe('Input Validation and Error Handling for /respond', () => {
        let sessionId: string;
        beforeAll(async () => {
            // Need a session ID that is valid for these tests
            if (!testUser) testUser = await authenticateTestUser();
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${testUser.token}`;
            const startResponse = await apiClient.post('/onboarding/start', {});
            sessionId = startResponse.data.data.session.sessionId;
        });
        
        it('should return 400 for missing sessionId', async () => {
            try {
                await apiClient.post('/onboarding/respond', { stepId: 'q1', response: {} });
            } catch (error) {
                expect(error.response.status).toBe(400);
                // Further check error message if API provides structured errors
            }
        });
        
        it('should return 400 for missing stepId', async () => {
            try {
                await apiClient.post('/onboarding/respond', { sessionId, response: {} });
            } catch (error) {
                expect(error.response.status).toBe(400);
            }
        });
        
        it('should return 400 for missing response body', async () => {
             try {
                await apiClient.post('/onboarding/respond', { sessionId, stepId: 'q1' });
            } catch (error) {
                expect(error.response.status).toBe(400);
            }
        });
    });
});
