#!/bin/bash

# Custom script to run backend in development mode with auto-restart
# This script doesn't rely on the NX daemon

echo "🚀 Starting Backend in Development Mode with Auto-Restart"
echo "📦 Building required libraries first..."

# Build required libraries
npm run build:libs

# Change to backend directory
cd apps/backend

echo "🔧 Starting backend with nodemon..."
echo "💡 Changes to source files will trigger automatic restart"

# Run nodemon with custom configuration
# --inspect flag enables debugging on port 9229
NODE_ENV=development nodemon --config nodemon.json

# This script can be terminated with Ctrl+C
