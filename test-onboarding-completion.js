/**
 * Test script to verify onboarding completion error handling
 * Run this with: node test-onboarding-completion.js
 */

const API_BASE_URL = 'http://localhost:3333';

// Mock auth token - replace with a real token for testing
const AUTH_TOKEN = 'your-test-token-here';

async function testOnboardingCompletion() {
  console.log('🧪 Testing Onboarding Completion Error Handling...\n');

  try {
    // Test 1: Try to start onboarding for a user who has already completed it
    console.log('📋 Test 1: Starting onboarding for completed user...');
    
    const response = await fetch(`${API_BASE_URL}/api/onboarding/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        deviceInfo: {
          platform: 'test',
          version: '1.0.0',
          locale: 'en-US',
        }
      }),
    });

    const responseData = await response.json();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📊 Response Data:`, JSON.stringify(responseData, null, 2));

    // Check if we get the expected 409 response
    if (response.status === 409 && responseData.status === 'already_completed') {
      console.log('✅ Test 1 PASSED: Received expected 409 response with completion data');
      
      // Verify the response structure
      const expectedFields = ['code', 'completedAt', 'message', 'actions'];
      const hasAllFields = expectedFields.every(field => 
        responseData.data && responseData.data.hasOwnProperty(field)
      );
      
      if (hasAllFields) {
        console.log('✅ Response structure is correct');
        
        // Check if actions are properly formatted
        const actions = responseData.data.actions;
        if (Array.isArray(actions) && actions.length >= 2) {
          const hasGoToDashboard = actions.some(action => action.id === 'go_to_dashboard');
          const hasRestart = actions.some(action => action.id === 'restart_onboarding');
          
          if (hasGoToDashboard && hasRestart) {
            console.log('✅ Action buttons are properly configured');
          } else {
            console.log('❌ Missing expected action buttons');
          }
        } else {
          console.log('❌ Actions array is malformed');
        }
      } else {
        console.log('❌ Response missing expected fields:', expectedFields);
      }
    } else if (response.status === 201) {
      console.log('ℹ️  Test 1 INFO: User hasn\'t completed onboarding yet (got 201)');
      console.log('   This is expected if testing with a new user');
    } else {
      console.log('❌ Test 1 FAILED: Unexpected response');
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Test restart endpoint
    console.log('📋 Test 2: Testing restart endpoint...');
    
    const restartResponse = await fetch(`${API_BASE_URL}/api/onboarding/restart`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        deviceInfo: {
          platform: 'test',
          version: '1.0.0',
          locale: 'en-US',
        }
      }),
    });

    const restartData = await response.json();
    
    console.log(`📊 Restart Response Status: ${restartResponse.status}`);
    console.log(`📊 Restart Response Data:`, JSON.stringify(restartData, null, 2));

    if (restartResponse.status === 201) {
      console.log('✅ Test 2 PASSED: Restart endpoint working correctly');
    } else {
      console.log('❌ Test 2 FAILED: Restart endpoint returned unexpected status');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('💡 Make sure the backend server is running on http://localhost:3333');
    }
    
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      console.log('💡 Make sure to set a valid AUTH_TOKEN in this test script');
    }
  }
}

// Helper function to test the frontend service logic
function testFrontendServiceLogic() {
  console.log('\n🧪 Testing Frontend Service Logic...\n');

  // Mock response data that matches backend format
  const mockAlreadyCompletedResponse = {
    status: 'already_completed',
    message: 'Onboarding has already been completed',
    data: {
      code: 'ONBOARDING_ALREADY_COMPLETED',
      completedAt: '2025-07-08T14:51:08.730Z',
      message: 'User has already completed onboarding. To restart, use the restart endpoint.',
      actions: [
        {
          id: 'go_to_dashboard',
          text: 'Go to Dashboard',
          primary: true,
        },
        {
          id: 'restart_onboarding',
          text: 'Restart Onboarding',
          secondary: true,
        },
      ],
    },
  };

  // Test the logic that would be in the frontend service
  if (mockAlreadyCompletedResponse.status === 'already_completed') {
    console.log('✅ Frontend would correctly identify already completed status');
    
    const actions = mockAlreadyCompletedResponse.data.actions;
    const primaryAction = actions.find(action => action.primary);
    const secondaryAction = actions.find(action => action.secondary);
    
    if (primaryAction && secondaryAction) {
      console.log('✅ Frontend would correctly parse action buttons');
      console.log(`   Primary: ${primaryAction.text}`);
      console.log(`   Secondary: ${secondaryAction.text}`);
    } else {
      console.log('❌ Frontend would fail to parse action buttons');
    }
    
    const completedDate = new Date(mockAlreadyCompletedResponse.data.completedAt);
    if (!isNaN(completedDate.getTime())) {
      console.log('✅ Frontend would correctly parse completion date');
      console.log(`   Completed: ${completedDate.toLocaleDateString()}`);
    } else {
      console.log('❌ Frontend would fail to parse completion date');
    }
  } else {
    console.log('❌ Frontend would not identify already completed status');
  }
}

// Run tests
console.log('🚀 Starting Onboarding Completion Tests...\n');

// Test frontend logic first (doesn't require server)
testFrontendServiceLogic();

// Then test actual API (requires server and auth)
if (AUTH_TOKEN && AUTH_TOKEN !== 'your-test-token-here') {
  testOnboardingCompletion();
} else {
  console.log('\n⚠️  Skipping API tests - please set a valid AUTH_TOKEN in the script');
  console.log('💡 To test the API endpoints:');
  console.log('   1. Start the backend server: npm run dev');
  console.log('   2. Get a valid auth token from login');
  console.log('   3. Replace AUTH_TOKEN in this script');
  console.log('   4. Run: node test-onboarding-completion.js');
}

console.log('\n✨ Test completed!');