# Sadaqah System Documentation

## Overview
The Sadaqah System enables users to gift premium healing journeys to others as an act of charity (sadaqah), amplifying the spiritual benefits of the platform. It integrates Islamic principles of giving with modern digital gifting mechanics, allowing donors to support others' healing paths while gaining rewards for their generosity. The system manages the full lifecycle of gifts: creation, payment processing, invitation delivery, redemption, and premium feature activation.

## System Architecture

### Components
1. **Gift Management Layer**
   - Journey gift creation
   - Unique invitation code generation
   - Gift status tracking
   - Redemption processing

2. **Payment Layer**
   - Secure payment processing
   - Transaction recording
   - Refund management
   - Receipt generation

3. **Premium Feature Layer**
   - Feature activation and provisioning
   - Access control management
   - Duration enforcement
   - Feature expiration handling

4. **Integration Layer**
   - Journey system connection
   - User account integration
   - Notification services
   - Analytics tracking

### Data Flow
```
Gift Creation → Payment Processing → Invitation Generation → 
Notification → Redemption → Feature Activation → Journey Creation
```

## Core Features

### 1. Gift Journey Creation
```javascript
// Example of gift journey creation flow
const createGiftJourney = async (donorId, recipientEmail, journeyType, durationDays, paymentData) => {
  // Generate unique invitation code
  const invitationCode = generateSecureInvitationCode();
  
  // Process payment
  const paymentResult = await processPayment(paymentData, journeyType, durationDays);
  
  if (!paymentResult.success) {
    throw new Error(paymentResult.message || 'Payment processing failed');
  }
  
  // Create gift record
  const gift = await storeGiftJourney({
    donorId,
    recipientEmail,
    journeyType,
    durationDays,
    invitationCode,
    paymentStatus: 'completed',
    paymentReference: paymentResult.transactionId,
    paymentAmount: paymentResult.amount,
    giftStatus: 'active',
    expiryDate: calculateExpiryDate()
  });
  
  // Add premium features
  const features = getPremiumFeatures(journeyType, durationDays);
  await storeGiftFeatures(gift.id, features);
  
  // Send invitation notification
  await sendGiftInvitation(gift, recipientEmail, invitationCode);
  
  return { gift, features };
};
```

### 2. Invitation and Redemption System
- Secure cryptographic invitation codes
- Email notification system
- Redemption validation
- Donor/recipient relationship management

### 3. Payment Processing
```javascript
// Example payment processing flow
const processPayment = async (paymentMethod, paymentToken, journeyType, durationDays) => {
  // Calculate amount based on journey type and duration
  const basePrice = getJourneyBasePrice(journeyType);
  const amount = calculateProRatedAmount(basePrice, durationDays);
  
  // Connect to payment gateway
  const paymentGateway = getPaymentGateway(paymentMethod);
  
  try {
    // Process payment through gateway
    const transaction = await paymentGateway.processPayment({
      amount,
      currency: 'USD',
      paymentToken,
      description: `Gift Journey: ${journeyType} (${durationDays} days)`,
      metadata: {
        journeyType,
        durationDays,
        type: 'gift_journey'
      }
    });
    
    return {
      success: true,
      transactionId: transaction.id,
      amount,
      message: 'Payment processed successfully'
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Payment processing failed'
    };
  }
};
```

### 4. Premium Feature Management
- Tiered premium features by journey type
- Duration-based feature availability
- Feature activation/deactivation logic
- Cross-system premium access control

### 5. Gift Lifecycle Management
- Gift creation, tracking, and management
- Status updates (active, redeemed, expired, canceled)
- Refund processing for eligible cancelations
- Analytics on gift usage and effectiveness

## Data Models

### Sadaqah Gifts
```sql
create table public.sadaqah_gifts (
    id uuid primary key default uuid_generate_v4(),
    donor_id uuid references auth.users not null,
    recipient_email text not null,
    journey_type text not null,
    duration_days integer not null,
    invitation_code text unique not null,
    payment_status text not null,
    payment_reference text,
    payment_amount numeric not null,
    gift_status text not null,
    personal_message text,
    created_at timestamp with time zone default now() not null,
    expires_at timestamp with time zone not null,
    redeemed_at timestamp with time zone,
    recipient_id uuid references auth.users,
    cancelled_at timestamp with time zone,
    refund_status text,
    refund_date timestamp with time zone,
    refund_reference text,
    invitation_resent_count integer default 0,
    last_resent_at timestamp with time zone
);
```

### Sadaqah Gift Features
```sql
create table public.sadaqah_gift_features (
    id uuid primary key default uuid_generate_v4(),
    gift_id uuid references public.sadaqah_gifts not null,
    feature_name text not null,
    feature_description text not null,
    feature_status text not null,
    activation_date timestamp with time zone,
    expiry_date timestamp with time zone,
    created_at timestamp with time zone default now() not null
);
```

### Gift Redemption Records
```sql
create table public.gift_redemption_records (
    id uuid primary key default uuid_generate_v4(),
    gift_id uuid references public.sadaqah_gifts not null unique,
    recipient_id uuid references auth.users not null,
    journey_id uuid references public.user_journeys,
    redemption_date timestamp with time zone not null,
    ip_address text,
    user_agent text,
    created_at timestamp with time zone default now() not null
);
```

## API Endpoints

### Create Gift Journey
```http
POST /api/sadaqah/gifts/create
Request Body:
{
  "recipientEmail": "<EMAIL>",
  "journeyType": "40-day",
  "durationDays": 40,
  "personalMessage": "May this journey bring peace to your heart",
  "paymentMethod": "card",
  "paymentToken": "tok_visa"
}

Response:
{
  "status": "success",
  "data": {
    "gift": {
      "id": "uuid",
      "invitation_code": "ABCD-1234",
      "journey_type": "40-day",
      "duration_days": 40,
      "payment_status": "completed",
      "gift_status": "active",
      "expires_at": "2025-07-25T13:45:26.563Z"
    },
    "features": [
      {
        "name": "premium_content",
        "description": "Access to premium content and exclusive resources"
      },
      {
        "name": "scholar_qa",
        "description": "Direct access to scholar Q&A sessions"
      },
      ...
    ],
    "message": "Gift journey created successfully"
  }
}
```

### Get Created Gifts
```http
GET /api/sadaqah/gifts
Query Parameters:
- status: "active" | "redeemed" | "expired" | "cancelled"
- page: number
- limit: number

Response:
{
  "status": "success",
  "data": {
    "gifts": [
      {
        "id": "uuid",
        "recipient_email": "<EMAIL>",
        "journey_type": "40-day",
        "duration_days": 40,
        "invitation_code": "ABCD-1234",
        "gift_status": "active",
        "created_at": "2025-05-26T13:45:26.563Z",
        "features": [...],
        "recipient": null
      },
      ...
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "pages": 1
    }
  }
}
```

### Redeem Gift Journey
```http
POST /api/sadaqah/gifts/redeem/:code
Response:
{
  "status": "success",
  "data": {
    "gift": {
      "id": "uuid",
      "journey_type": "40-day",
      "duration_days": 40,
      "gift_status": "redeemed",
      "redeemed_at": "2025-05-26T13:45:26.563Z"
    },
    "journey": {
      "id": "uuid",
      "journey_type": "40-day",
      "status": "active",
      "start_date": "2025-05-26T13:45:26.563Z",
      "end_date": "2025-07-05T13:45:26.563Z",
      "duration_days": 40,
      "is_premium": true
    },
    "message": "Gift journey redeemed successfully"
  }
}
```

### Cancel Gift
```http
PATCH /api/sadaqah/gifts/:id/cancel
Response:
{
  "status": "success",
  "data": {
    "gift": {
      "id": "uuid",
      "gift_status": "cancelled",
      "cancelled_at": "2025-05-26T13:45:26.563Z"
    },
    "refundProcessed": true,
    "message": "Gift cancelled and refund processed"
  }
}
```

### Resend Gift Invitation
```http
POST /api/sadaqah/gifts/:id/resend
Response:
{
  "status": "success",
  "message": "Gift invitation resent successfully"
}
```

## Security Implementation

### Payment Security
- PCI-DSS compliant payment handling
- Tokenized payment information
- Secure payment gateway integration
- Encrypted transaction data

### Invitation Code Security
```javascript
// Secure invitation code generation
function generateInvitationCode() {
  const length = 8;
  const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Excluding similar-looking characters
  let code = '';
  
  // Create cryptographically secure random code
  const randomBytes = crypto.randomBytes(length);
  for (let i = 0; i < length; i++) {
    const randomIndex = randomBytes[i] % charset.length;
    code += charset[randomIndex];
  }
  
  // Format as XXXX-XXXX for readability
  return `${code.substring(0, 4)}-${code.substring(4, 8)}`;
}
```

### Access Control
```sql
-- Row-level security policies
create policy "Users can view their own created gifts"
  on public.sadaqah_gifts
  for select
  using (auth.uid() = donor_id);

create policy "Users can view gifts they've redeemed"
  on public.sadaqah_gifts
  for select
  using (auth.uid() = recipient_id);

create policy "Users can only cancel their own gifts"
  on public.sadaqah_gifts
  for update
  using (auth.uid() = donor_id AND gift_status = 'active');
```

### Data Protection
- Encrypted personal messages
- Limited recipient information visibility
- Gift code rate limiting
- Expiration date enforcement

## Performance Optimization

### Indexing Strategy
```sql
-- Optimize query performance with indexes
create index idx_sadaqah_gifts_donor_id 
  on public.sadaqah_gifts(donor_id);
  
create index idx_sadaqah_gifts_invitation_code
  on public.sadaqah_gifts(invitation_code);
  
create index idx_sadaqah_gift_features_gift_id
  on public.sadaqah_gift_features(gift_id);
```

### Caching Implementation
```javascript
// Example caching for gift details
const getGiftByInvitationCode = async (code) => {
  const cacheKey = `gift:invitation:${code}`;
  
  // Try to get from cache first
  const cachedGift = await cache.get(cacheKey);
  if (cachedGift) {
    return JSON.parse(cachedGift);
  }
  
  // Fetch from database if not in cache
  const gift = await fetchGiftByInvitationCode(code);
  
  // Only cache active gifts for security
  if (gift && gift.gift_status === 'active') {
    // Store in cache with 15-minute expiry
    await cache.set(cacheKey, JSON.stringify(gift), 900);
  }
  
  return gift;
};
```

### Notification Optimization
- Batch processing for notifications
- Asynchronous email delivery
- Notification throttling
- Retry mechanisms for failed deliveries

## Integration Points

### Journey System Integration
```javascript
// Example of journey creation upon gift redemption
const createJourneyFromGift = async (userId, gift) => {
  const now = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + gift.duration_days);
  
  // Create a journey using the Journey System
  const journey = await createJourney({
    userId,
    journeyType: gift.journey_type,
    status: 'active',
    startDate: now,
    endDate,
    durationDays: gift.duration_days,
    giftId: gift.id,
    isPremium: true
  });
  
  // Initialize journey content
  await initializeJourneyContent(journey.id, gift.journey_type);
  
  return journey;
};
```

### User System Integration
- Donor/recipient relationship tracking
- User notifications
- Account linking for recipients
- Profile enrichment

### Analytics System Integration
- Gift conversion tracking
- Usage analytics
- Feature effectiveness measurement
- Donation pattern analysis

## Error Handling

### Error Types
- `SADAQAH_001`: Gift creation failed
- `SADAQAH_002`: Payment processing failed
- `SADAQAH_003`: Redemption failed
- `SADAQAH_004`: Gift not found or invalid

### Graceful Degradation
```javascript
// Example of fallback mechanism for gift redemption
const redeemGiftJourney = async (userId, invitationCode) => {
  try {
    // Attempt to process full redemption flow
    const result = await processFullGiftRedemption(userId, invitationCode);
    return result;
  } catch (error) {
    logger.warn(`Full redemption failed: ${error.message}`);
    
    // If journey creation fails but gift redemption succeeded
    if (error.code === 'JOURNEY_CREATION_FAILED' && error.giftData) {
      // Mark gift as special case
      await markGiftForManualProcessing(error.giftData.id);
      
      // Return partial success
      return {
        success: true,
        partialProcessing: true,
        message: 'Gift redeemed but journey creation delayed',
        gift: error.giftData
      };
    }
    
    // Re-throw for other errors
    throw error;
  }
};
```

### Error Response Format
```javascript
{
  "status": "error",
  "code": "SADAQAH_002",
  "message": "Payment processing failed",
  "details": {
    "reason": "Card declined",
    "suggestion": "Please try another payment method"
  }
}
```

## Monitoring and Analytics

### Key Metrics
1. **Gift Creation**: Volume, conversion rate, average value
2. **Redemption Rate**: Percentage of gifts redeemed
3. **Time-to-Redemption**: Average time from gift to redemption
4. **Feature Usage**: Which premium features are most used

### Insight Generation
- Optimal gift pricing analysis
- Feature popularity ranking
- Redemption pattern analysis
- Donor behavior insights

## Islamic Principles Integration

### Sadaqah Concepts
- Digital sadaqah jariyah (ongoing charity)
- Intention (niyyah) recording
- Dua prompts for donors
- Islamic gifting etiquette

### Ethical Considerations
- Transparent pricing and fees
- Privacy protection for both parties
- Avoiding excessive pressure to give
- Respecting financial boundaries

## Best Practices

### Implementation Guidelines
1. **Privacy First**: Protect both donor and recipient information
2. **Transparency**: Clear communication about what is being gifted
3. **Simplicity**: Intuitive gifting and redemption process
4. **Respect**: Appropriate messaging that honors Islamic principles

### Content Guidelines
- Culturally sensitive gift messaging
- Appropriate language for notifications
- Islamic etiquette in gift presentation
- Spiritual benefit emphasis over material value

## Future Enhancements

### Planned Features
1. **Gift Bundles**: Multiple journeys or features in one gift
2. **Subscription Gifting**: Ongoing monthly sadaqah gifts
3. **Group Gifting**: Multiple donors contribute to a premium gift
4. **Targeted Gifts**: Specific feature or content area gifts
5. **Anonymous Giving**: Option for completely anonymous donations

### Technical Improvements
- Additional payment methods integration
- Enhanced gift recommendation engine
- Advanced analytics for gift impact
- Improved security measures for high-value gifts

