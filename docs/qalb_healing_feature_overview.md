# Feature: Personalized Healing Journeys (Feature_02_Personalized_Healing_Journeys_REVISED)

## Overview

The Personalized Healing Journeys feature is designed to provide users with tailored, AI-driven healing plans based on their individual assessments, profiles, and preferences. It aims to guide users through a structured path of Islamic practices, reflections, and educational content to support their spiritual and emotional well-being.

This document provides a high-level overview of the feature's components and purpose.

## Main Components

The feature is primarily composed of three main application services:

1.  **AI Service (`apps/ai-service`):**
    *   Responsible for the intelligent aspects of journey creation, including parameter generation (duration, type, focus) and content generation (daily practices, themes, personalization) based on user data.
    *   Utilizes (currently basic) journey templates, a practice library, and personalization rules.

2.  **Backend Service (`apps/backend`):**
    *   Manages the lifecycle of journeys (creation, starting, progress tracking, completion).
    *   Interfaces with the AI Service for journey personalization and content.
    *   Persists all journey-related data via Supabase.
    *   Provides APIs for the mobile application.
    *   Includes placeholder logic for future enhancements like notifications and advanced analytics.

3.  **Mobile Application (`apps/mobile-app`):**
    *   Presents the personalized healing journey to the user through an intuitive interface (e.g., `JourneyDashboard`).
    *   Allows users to interact with daily practices, track their progress, and view basic analytics.
    *   Communicates with the Backend Service to fetch and update journey information.

## Detailed Operational Flows

For a more in-depth understanding of the operational sequences and data flow between these components for various actions (e.g., journey creation, daily progress tracking, analytics viewing), please refer to the [Detailed Feature Flows](./qalb-healing-docs/feature_flows.md) document (located in the `qalb-healing-docs` subdirectory).

## Current Status & Notes

*   The core structure for personalized journey generation, management, and display is in place.
*   The AI Service's content generation capabilities are based on foundational templates and libraries; advanced, dynamic content generation is a future enhancement.
*   Several backend features (e.g., real-time notifications, full-scale crisis response, advanced community matching) are currently implemented as placeholders.
*   This documentation provides a starting point for understanding the feature. Further details on specific API contracts or data schemas can be found within the respective service code and shared type definitions.
