# Progress Analytics API Documentation

## Overview
The Progress Analytics API provides comprehensive insights into user progress, journey analytics, healing metrics, and achievements. This system is designed to help track and analyze user engagement and healing progress across the Qalb-centric healing platform.

## Base URL
```
/api/v1/analytics
```

## Authentication
All endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### User Progress Analytics
#### GET /analytics/progress
Get detailed progress analytics for the authenticated user.

**Query Parameters:**
- `timeframe`: (optional) - "week" | "month" | "year" | "all" (default: "month")
- `layers`: (optional) - Array of healing layers to filter by ["Qalb", "Ruh", "Nafs", "Aql", "Jism"]

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "overallProgress": {
      "completionRate": 85,
      "totalModules": 45,
      "completedModules": 38,
      "averageEngagement": 92
    },
    "layerProgress": {
      "Qalb": {
        "completion": 90,
        "engagement": 95,
        "consistency": 88
      },
      // ... other layers
    },
    "timeline": [
      {
        "date": "2024-01-01",
        "modulesCompleted": 2,
        "engagementScore": 95
      }
      // ... more dates
    ]
  }
}
```

### Journey Analytics
#### GET /analytics/journey
Get journey-specific analytics and insights.

**Query Parameters:**
- `journeyId`: (optional) - Specific journey ID to analyze
- `includeCompleted`: (optional) - Boolean to include completed journeys (default: false)

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "currentJourney": {
      "type": "40-day",
      "progress": 45,
      "daysPassed": 18,
      "daysRemaining": 22,
      "consistency": 92
    },
    "milestones": [
      {
        "id": "milestone-1",
        "name": "First Week Complete",
        "achievedDate": "2024-01-07"
      }
      // ... more milestones
    ],
    "insights": {
      "strongestLayer": "Qalb",
      "areasForImprovement": ["Nafs"],
      "recommendedFocus": "Emotional Regulation"
    }
  }
}
```

### Healing Metrics
#### GET /analytics/healing
Get detailed healing progress metrics and insights.

**Query Parameters:**
- `timeframe`: (optional) - "week" | "month" | "year" | "all" (default: "month")
- `metrics`: (optional) - Array of specific metrics to include

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "overallHealth": {
      "score": 85,
      "trend": "+5",
      "lastUpdated": "2024-01-15"
    },
    "layerMetrics": {
      "Qalb": {
        "baseline": 65,
        "current": 82,
        "improvement": 17,
        "keyIndicators": {
          "emotionalBalance": 80,
          "spiritualConnection": 85
        }
      }
      // ... other layers
    },
    "practiceEffectiveness": {
      "dhikr": 90,
      "meditation": 85,
      "ruqya": 88
    }
  }
}
```

### Achievement Analytics
#### GET /analytics/achievements
Get detailed achievement and milestone analytics.

**Query Parameters:**
- `category`: (optional) - Filter by achievement category
- `status`: (optional) - "earned" | "in-progress" | "all" (default: "all")

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "totalAchievements": 25,
    "earnedAchievements": 15,
    "completion": 60,
    "recentAchievements": [
      {
        "id": "achievement-1",
        "name": "Consistency Master",
        "earnedDate": "2024-01-10",
        "category": "Journey"
      }
      // ... more achievements
    ],
    "nextMilestones": [
      {
        "id": "milestone-5",
        "name": "30-Day Streak",
        "progress": 80,
        "requirements": {
          "current": 24,
          "required": 30
        }
      }
      // ... more milestones
    ]
  }
}
```

### Dashboard Analytics
#### GET /analytics/dashboard
Get aggregated analytics for dashboard display.

**Query Parameters:**
- `modules`: (optional) - Array of dashboard modules to include
- `timeframe`: (optional) - "day" | "week" | "month" (default: "week")

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "summary": {
      "journeyProgress": 45,
      "currentStreak": 12,
      "totalPracticeHours": 24.5,
      "weeklyGrowth": "+15%"
    },
    "healingWheel": {
      "Qalb": 85,
      "Ruh": 78,
      "Nafs": 72,
      "Aql": 80,
      "Jism": 75
    },
    "recentMilestones": [
      {
        "name": "Completed First Ruqya Series",
        "date": "2024-01-12",
        "impact": "high"
      }
    ],
    "trends": {
      "consistency": [
        {"date": "2024-01-01", "value": 80},
        // ... more trend data
      ],
      "engagement": [
        {"date": "2024-01-01", "value": 85},
        // ... more trend data
      ]
    }
  }
}
```

## Error Responses
All endpoints follow the standard error response format:

```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description"
  }
}
```

Common Error Codes:
- `INVALID_TIMEFRAME`: Invalid time frame specified
- `INVALID_METRICS`: Invalid metrics requested
- `DATA_NOT_FOUND`: Requested analytics data not found
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions

## Rate Limiting
Analytics endpoints are rate-limited to:
- 60 requests per minute for basic users
- 120 requests per minute for premium users

## Data Freshness
- Real-time data has a maximum delay of 5 minutes
- Aggregated analytics are updated every hour
- Historical data is processed daily at midnight UTC

## Best Practices
1. Use appropriate timeframes for optimal performance
2. Cache dashboard analytics on the client side
3. Implement progressive loading for large data sets
4. Use specific metrics parameters to reduce payload size

## Webhook Notifications
Subscribe to analytics updates via webhooks:
```
POST /analytics/webhooks
{
  "url": "https://your-domain.com/webhook",
  "events": ["milestone_achieved", "streak_updated", "journey_completed"]
}
```