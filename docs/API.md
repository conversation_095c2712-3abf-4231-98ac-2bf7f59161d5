# Qalb Healing API Documentation

## Overview

This document provides comprehensive documentation for the Qalb Healing Backend API. The API follows RESTful principles and uses JSON for request and response payloads.

## Base URL

```
https://api.qalbhealing.com/v1
```

For local development:

```
http://localhost:3000
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Most endpoints require a valid JWT token to be included in the request header.

### Headers

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "status": "success",
  "data": {
    // Response data specific to the endpoint
  }
}
```

### Error Response

```json
{
  "status": "error",
  "error": {
    "code": 400,
    "message": "Invalid input data"
  }
}
```

## API Endpoints

### 1. Authentication

#### 1.1 Sign Up

Create a new user account.

- **URL**: `/auth/signup`
- **Method**: `POST`
- **Auth Required**: No
- **Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

- **Success Response** (201 Created):

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "created_at": "2023-01-01T12:00:00Z"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `409 Conflict`: Email already registered

#### 1.2 Login

Authenticate a user and get a JWT token.

- **URL**: `/auth/login`
- **Method**: `POST`
- **Auth Required**: No
- **Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>"
    },
    "session": {
      "access_token": "jwt-token",
      "refresh_token": "refresh-token",
      "expires_at": "2023-01-02T12:00:00Z"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Invalid login credentials

#### 1.3 Get Profile

Get the current user's profile.

- **URL**: `/auth/profile`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "profile": {
      "user_id": "user-uuid",
      "email": "<EMAIL>",
      "selected_layers": ["qalb", "nafs"],
      "journey_type": "7-day",
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: Profile not found

#### 1.4 Update Profile

Update the current user's profile.

- **URL**: `/auth/profile`
- **Method**: `PATCH`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "selectedLayers": ["qalb", "nafs", "aql"],
  "journeyType": "14-day"
}
```

- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "profile": {
      "user_id": "user-uuid",
      "email": "<EMAIL>",
      "selected_layers": ["qalb", "nafs", "aql"],
      "journey_type": "14-day",
      "updated_at": "2023-01-02T12:00:00Z"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token

### 2. Symptom Analysis

#### 2.1 Submit Symptoms

Submit symptoms for AI analysis.

- **URL**: `/symptoms/submit`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "jism": ["headache", "fatigue"],
  "nafs": ["anxiety", "irritability"],
  "aql": ["overthinking"],
  "qalb": ["disconnection", "emptiness"],
  "ruh": ["lack of purpose"],
  "intensity": {
    "jism": 7,
    "nafs": 8,
    "aql": 6,
    "qalb": 9,
    "ruh": 7
  },
  "duration": "3-6 months"
}
```

- **Success Response** (201 Created):

```json
{
  "status": "success",
  "data": {
    "submission": {
      "id": 123,
      "user_id": "user-uuid",
      "jism_symptoms": ["headache", "fatigue"],
      "nafs_symptoms": ["anxiety", "irritability"],
      "aql_symptoms": ["overthinking"],
      "qalb_symptoms": ["disconnection", "emptiness"],
      "ruh_symptoms": ["lack of purpose"],
      "intensity_ratings": {
        "jism": 7,
        "nafs": 8,
        "aql": 6,
        "qalb": 9,
        "ruh": 7
      },
      "duration": "3-6 months",
      "submission_date": "2023-01-01T12:00:00Z"
    },
    "diagnosis": {
      "layers_affected": ["Qalb", "Nafs"],
      "spotlight": "You may be experiencing spiritual disconnection combined with emotional turbulence.",
      "recommended_journey": "7-day"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token
  - `500 Internal Server Error`: AI processing error

#### 2.2 Get Symptom History

Retrieve the user's symptom submission history.

- **URL**: `/symptoms/history`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "history": [
      {
        "id": 123,
        "user_id": "user-uuid",
        "jism_symptoms": ["headache", "fatigue"],
        "nafs_symptoms": ["anxiety", "irritability"],
        "submission_date": "2023-01-01T12:00:00Z",
        "user_diagnoses": {
          "layers_affected": ["Qalb", "Nafs"],
          "spotlight": "Analysis result",
          "recommended_journey": "7-day"
        }
      },
      {
        "id": 124,
        "user_id": "user-uuid",
        "jism_symptoms": ["insomnia"],
        "nafs_symptoms": ["stress"],
        "submission_date": "2023-01-15T12:00:00Z",
        "user_diagnoses": {
          "layers_affected": ["Nafs", "Aql"],
          "spotlight": "Updated analysis",
          "recommended_journey": "14-day"
        }
      }
    ]
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `500 Internal Server Error`: Database error

#### 2.3 Get Latest Diagnosis

Retrieve the user's most recent diagnosis.

- **URL**: `/symptoms/latest-diagnosis`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "diagnosis": {
      "id": 456,
      "user_id": "user-uuid",
      "submission_id": 123,
      "layers_affected": ["Qalb", "Nafs"],
      "spotlight": "You may be experiencing spiritual disconnection combined with emotional turbulence.",
      "recommended_journey": "7-day",
      "severity_level": "moderate",
      "diagnosis_date": "2023-01-01T12:00:00Z",
      "symptom_submissions": {
        "jism_symptoms": ["headache", "fatigue"],
        "nafs_symptoms": ["anxiety"],
        "intensity_ratings": {
          "jism": 7,
          "nafs": 8
        }
      }
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: No diagnosis found

#### 2.4 Track Symptom Progress

Track the progress of a specific symptom over time.

- **URL**: `/symptoms/track`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "symptomId": 123,
  "intensity": 5,
  "notes": "Feeling better today"
}
```

- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "tracking": {
      "id": 789,
      "user_id": "user-uuid",
      "symptom_id": 123,
      "intensity": 5,
      "notes": "Feeling better today",
      "tracking_date": "2023-01-05T12:00:00Z"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: Symptom not found

### 3. Journey Management

#### 3.1 Start Journey

Start a new healing journey.

- **URL**: `/journey/start`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "journeyType": "7-day",
  "focusLayers": ["qalb", "nafs"],
  "customDuration": null
}
```

- **Success Response** (201 Created):

```json
{
  "status": "success",
  "data": {
    "journey": {
      "id": "journey-123",
      "user_id": "user-uuid",
      "journey_type": "7-day",
      "focus_layers": ["qalb", "nafs"],
      "duration_days": 7,
      "start_date": "2023-01-01T12:00:00Z",
      "status": "active",
      "current_day": 1,
      "modules_plan": [
        { "day": 1, "title": "Understanding Qalb" },
        { "day": 2, "title": "Healing Nafs" }
      ]
    },
    "todaysModule": {
      "day": 1,
      "title": "Understanding Qalb"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data or user already has an active journey
  - `401 Unauthorized`: Missing or invalid token
  - `500 Internal Server Error`: Journey creation error

#### 3.2 Get Current Journey

Get the user's current active journey.

- **URL**: `/journey/current`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "journey": {
      "id": "journey-123",
      "user_id": "user-uuid",
      "journey_type": "7-day",
      "focus_layers": ["qalb", "nafs"],
      "duration_days": 7,
      "start_date": "2023-01-01T12:00:00Z",
      "status": "active",
      "current_day": 3,
      "modules_plan": [
        { "day": 1, "title": "Understanding Qalb" },
        { "day": 2, "title": "Healing Nafs" },
        { "day": 3, "title": "Current Module" }
      ],
      "journey_modules": [
        {
          "id": "module-1",
          "day": 1,
          "title": "Understanding Qalb",
          "module_completions": [
            {
              "status": "completed",
              "completion_date": "2023-01-01T18:00:00Z"
            }
          ]
        },
        {
          "id": "module-2",
          "day": 2,
          "title": "Healing Nafs",
          "module_completions": [
            {
              "status": "completed",
              "completion_date": "2023-01-02T18:00:00Z"
            }
          ]
        }
      ]
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: No active journey

#### 3.3 Update Journey Progress

Update the progress of the current journey.

- **URL**: `/journey/progress`
- **Method**: `PATCH`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "moduleId": "module-123",
  "status": "completed",
  "reflections": "I learned a lot about my qalb today",
  "challenges": "Found it difficult to stay focused"
}
```

- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "completion": {
      "id": "completion-456",
      "user_id": "user-uuid",
      "module_id": "module-123",
      "status": "completed",
      "reflections": "I learned a lot about my qalb today",
      "challenges": "Found it difficult to stay focused",
      "completion_date": "2023-01-03T18:00:00Z"
    },
    "journey": {
      "id": "journey-123",
      "user_id": "user-uuid",
      "current_day": 4,
      "last_activity_date": "2023-01-03T18:00:00Z"
    },
    "newAchievements": ["CONSISTENT_SEEKER"]
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: Module or journey not found

#### 3.4 Get Journey Analytics

Get analytics data for the user's journey.

- **URL**: `/journey/analytics`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "analytics": {
      "totalModulesCompleted": 3,
      "currentStreak": 3,
      "longestStreak": 5,
      "completionRate": 75,
      "lastWeekActivity": 4
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `500 Internal Server Error`: Analytics calculation error

#### 3.5 Get Achievements

Get the user's earned achievements.

- **URL**: `/journey/achievements`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "achievements": [
      {
        "id": "ua-123",
        "user_id": "user-uuid",
        "achievement_type": "WEEK_WARRIOR",
        "earned_date": "2023-01-07T12:00:00Z",
        "achievements": {
          "id": "ach-1",
          "name": "Week Warrior",
          "description": "Completed 7 daily modules",
          "icon": "warrior.svg"
        }
      },
      {
        "id": "ua-124",
        "user_id": "user-uuid",
        "achievement_type": "CONSISTENT_SEEKER",
        "earned_date": "2023-01-10T12:00:00Z",
        "achievements": {
          "id": "ach-2",
          "name": "Consistent Seeker",
          "description": "Maintained a 10-day streak",
          "icon": "seeker.svg"
        }
      }
    ]
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token

#### 3.6 Submit Daily Check-In

Submit a daily check-in to track progress.

- **URL**: `/journey/check-in`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "mood": "content",
  "dhikrCount": 33,
  "prayerConsistency": 4,
  "notes": "Felt more connected today"
}
```

- **Success Response** (201 Created):

```json
{
  "status": "success",
  "data": {
    "checkIn": {
      "id": "checkin-123",
      "user_id": "user-uuid",
      "mood": "content",
      "dhikr_count": 33,
      "prayer_consistency": 4,
      "notes": "Felt more connected today",
      "check_in_date": "2023-01-03T12:00:00Z"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token

#### 3.7 Get Recommended Resources

Get personalized resources based on the current journey.

- **URL**: `/journey/resources`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "recommendations": {
      "resources": [
        {
          "id": 1,
          "title": "Understanding Qalb",
          "type": "article",
          "url": "/content/understanding-qalb"
        },
        {
          "id": 2,
          "title": "Nafs Purification",
          "type": "video",
          "url": "/content/nafs-purification"
        }
      ]
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: No active journey

#### 3.8 Modify Journey

Modify an existing journey (reset, extend, or change focus).

- **URL**: `/journey/modify`
- **Method**: `PATCH`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "action": "extend",
  "additionalDays": 7
}
```

- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "journey": {
      "id": "journey-123",
      "user_id": "user-uuid",
      "duration_days": 14,
      "status": "active"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: Journey not found

### 4. Content Delivery

#### 4.1 Get Content Feed

Get a personalized content feed.

- **URL**: `/content/feed`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `limit` (optional): Items per page (default: 10)
  - `type` (optional): Content type filter (e.g., "article", "video")
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "items": [
      {
        "id": "content-1",
        "title": "Understanding the Qalb in Islamic Tradition",
        "type": "article",
        "summary": "An exploration of the concept of Qalb in Islamic spirituality.",
        "thumbnail_url": "https://cdn.qalbhealing.com/thumbnails/qalb-intro.jpg",
        "url": "/content/qalb-intro",
        "tags": ["qalb", "spirituality", "beginner"],
        "created_at": "2023-01-01T12:00:00Z",
        "reading_time": 5
      },
      {
        "id": "content-2",
        "title": "Dhikr Meditation Techniques",
        "type": "video",
        "summary": "Learn effective dhikr techniques for spiritual healing.",
        "thumbnail_url": "https://cdn.qalbhealing.com/thumbnails/dhikr-techniques.jpg",
        "url": "/content/dhikr-techniques",
        "tags": ["dhikr", "meditation", "practice"],
        "created_at": "2023-01-02T12:00:00Z",
        "duration": 15
      }
    ],
    "pagination": {
      "total": 24,
      "pages": 3,
      "current_page": 1,
      "has_more": true
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `400 Bad Request`: Invalid query parameters

#### 4.2 Get Content By Category

Get content items by category.

- **URL**: `/content/category/:categoryId`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `limit` (optional): Items per page (default: 10)
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "category": {
      "id": "cat-1",
      "name": "Qalb Healing",
      "description": "Content focused on healing the spiritual heart"
    },
    "items": [
      {
        "id": "content-1",
        "title": "Understanding the Qalb in Islamic Tradition",
        "type": "article",
        "summary": "An exploration of the concept of Qalb in Islamic spirituality.",
        "thumbnail_url": "https://cdn.qalbhealing.com/thumbnails/qalb-intro.jpg",
        "url": "/content/qalb-intro",
        "tags": ["qalb", "spirituality", "beginner"],
        "created_at": "2023-01-01T12:00:00Z",
        "reading_time": 5
      }
    ],
    "pagination": {
      "total": 15,
      "pages": 2,
      "current_page": 1,
      "has_more": true
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: Category not found

#### 4.3 Get Content Item

Get a specific content item by ID.

- **URL**: `/content/item/:contentId`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "item": {
      "id": "content-1",
      "title": "Understanding the Qalb in Islamic Tradition",
      "type": "article",
      "body": "<p>Full article content here...</p>",
      "thumbnail_url": "https://cdn.qalbhealing.com/thumbnails/qalb-intro.jpg",
      "url": "/content/qalb-intro",
      "tags": ["qalb", "spirituality", "beginner"],
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z",
      "reading_time": 5,
      "author": {
        "name": "Dr. Ahmed Khan",
        "bio": "Islamic spirituality researcher and author"
      },
      "related_items": [
        {
          "id": "content-3",
          "title": "Purifying the Heart",
          "type": "article"
        }
      ]
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: Content item not found

#### 4.4 Search Content

Search for content items.

- **URL**: `/content/search`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**:
  - `q`: Search query
  - `type` (optional): Content type filter
  - `tags` (optional): Comma-separated tags to filter by
  - `page` (optional): Page number (default: 1)
  - `limit` (optional): Items per page (default: 10)
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "query": "qalb meditation",
    "items": [
      {
        "id": "content-5",
        "title": "Qalb-Centered Meditation Practices",
        "type": "article",
        "summary": "Learn meditation techniques focused on the spiritual heart.",
        "thumbnail_url": "https://cdn.qalbhealing.com/thumbnails/qalb-meditation.jpg",
        "url": "/content/qalb-meditation",
        "tags": ["qalb", "meditation", "practice"],
        "created_at": "2023-01-05T12:00:00Z",
        "reading_time": 8
      }
    ],
    "pagination": {
      "total": 3,
      "pages": 1,
      "current_page": 1,
      "has_more": false
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `400 Bad Request`: Missing search query

#### 4.5 Record Content Interaction

Record a user interaction with content (view, like, share, etc.).

- **URL**: `/content/interaction`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "contentId": "content-1",
  "interactionType": "view",
  "metadata": {
    "time_spent": 120,
    "completed": true
  }
}
```

- **Success Response** (201 Created):

```json
{
  "status": "success",
  "data": {
    "interaction": {
      "id": "interaction-123",
      "user_id": "user-uuid",
      "content_id": "content-1",
      "interaction_type": "view",
      "metadata": {
        "time_spent": 120,
        "completed": true
      },
      "interaction_date": "2023-01-03T12:00:00Z"
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token
  - `404 Not Found`: Content item not found

### 5. Ruqya (Spiritual Healing)

#### 5.1 Submit Self-Assessment

Submit a self-assessment for ruqya guidance.

- **URL**: `/ruqya/self-check`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:

```json
{
  "questions": [
    { "id": "q1", "answer": "yes" },
    { "id": "q2", "answer": "sometimes" },
    { "id": "q3", "answer": "no" }
  ],
  "symptoms": ["negative_thoughts", "anxiety", "insomnia"],
  "duration": "1-3 months",
  "previous_treatment": false
}
```

- **Success Response** (201 Created):

```json
{
  "status": "success",
  "data": {
    "assessment": {
      "id": "assessment-123",
      "user_id": "user-uuid",
      "questions": [
        { "id": "q1", "answer": "yes" },
        { "id": "q2", "answer": "sometimes" },
        { "id": "q3", "answer": "no" }
      ],
      "symptoms": ["negative_thoughts", "anxiety", "insomnia"],
      "duration": "1-3 months",
      "previous_treatment": false,
      "assessment_date": "2023-01-03T12:00:00Z"
    },
    "recommendation": {
      "risk_level": "medium",
      "requires_professional": false,
      "recommended_plan": "self_ruqya_basic",
      "focus_areas": ["protection", "cleansing"]
    }
  }
}
```

- **Error Responses**:
  - `400 Bad Request`: Invalid input data
  - `401 Unauthorized`: Missing or invalid token

#### 5.2 Get Treatment Plan

Get a personalized ruqya treatment plan.

- **URL**: `/ruqya/plan`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response** (200 OK):

```json
{
  "status": "success",
  "data": {
    "plan": {
      "id": "plan-123",
      "user_id": "user-uuid",
      "plan_type": "self_ruqya_basic",
      "duration_days": 7,
      "created_at": "2023-01-03T12:00:00Z",
      "daily_practices": [
        {
          "day": 1,
          "title": "Protection Routine",
          "description": "Morning and evening protection adhkar",
          "practices": [
            {
              "id": "practice-1",
              "title": "Morning Adhkar",
              "instructions": "Recite the following...",
              "duration_minutes": 15,
              "audio_url": "https://cdn.qalbhealing.com/audio/morning-adhkar.mp3"
            },
            {
              "id": "practice-2",
              "title": "Evening Adhkar",
              "instructions": "Recite the following...",
              "duration_minutes": 15,
              "audio_url": "https://cdn.qalbhealing.com/audio/evening-adhkar.mp3"
            }
          ]
        }
      ],
      "resources": [
        {
          "id": "resource-1",
          "title": "Understanding Spiritual Protection",
          "type": "article",
          "url": "/content/spiritual-protection"
        }
      ]
    }
  }
}
```

- **Error Responses**:
  - `401 Unauthorized`: Missing or invalid token
  - `404 

