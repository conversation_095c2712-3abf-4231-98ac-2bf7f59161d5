# Dashboard System Documentation

## Overview
The Dashboard System serves as the central hub of the Qalb Healing platform, aggregating data from multiple systems to provide users with a unified, personalized, and actionable view of their healing journey. It dynamically compiles daily focus content, tracks progress across the five healing layers, manages quick actions, and visualizes the user's overall healing state.

## System Architecture

### Components
1. **Aggregation Layer**
   - Cross-system data collection
   - Content personalization
   - Real-time data synchronization
   - Cache management

2. **Presentation Layer**
   - Today's focus compilation
   - Quick actions management
   - Journey status tracking
   - Emergency mode access

3. **Metrics Layer**
   - Streak calculation
   - Healing wheel visualization
   - Progress tracking
   - Consistency scoring

4. **Interaction Layer**
   - Action completion handling
   - User engagement tracking
   - Content recommendation
   - Notification management

### Data Flow
```
Multiple Systems → Data Aggregation → Personalization → 
Caching → Presentation → User Interaction → Action Tracking
```

## Core Features

### 1. Today's Dashboard
```javascript
// Example of dashboard content structure
const dashboardContent = {
  date: new Date().toISOString(),
  todaysFocus: {
    nameOfAllah: {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      transliteration: 'The Most Merciful',
      benefits: ['Compassion', 'Kindness', 'Love']
    },
    quranicVerse: {
      arabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      translation: 'In the name of Allah, the Most Merciful, the Most Compassionate',
      reference: 'Al-Fatihah 1:1'
    },
    reflectionPrompt: 'How has Allah\'s mercy manifested in your life today?'
  },
  journeyStatus: {
    active: true,
    currentDay: 12,
    totalDays: 40,
    journeyType: '40-day',
    checkedIn: true
  },
  quickActions: [
    {
      id: 'dhikr',
      title: 'Start Dhikr',
      description: 'SubhanAllah',
      icon: 'tasbih',
      completed: false,
      actionUrl: '/dhikr'
    },
    // More actions...
  ],
  emergencyMode: {
    available: true,
    recentlyUsed: false
  },
  healingWheel: {
    jism: 65,
    nafs: 40,
    aql: 75,
    qalb: 80,
    ruh: 60
  }
};
```

### 2. Metrics and Analytics
- Current and longest streaks across systems
- Healing wheel visualization
- Journey progress tracking
- Engagement and consistency scoring

### 3. Quick Actions Management
```javascript
// Example of quick actions structure
const quickActions = [
  {
    id: 'dhikr',
    type: 'dhikr',
    title: 'Daily Dhikr',
    description: 'Complete today\'s dhikr practice',
    priority: 'high',
    estimatedTime: '5 minutes',
    icon: 'tasbih',
    actionUrl: '/dhikr',
    completed: false
  },
  {
    id: 'journal',
    type: 'journal',
    title: 'Reflection Journal',
    description: 'Write today\'s reflection',
    priority: 'medium',
    estimatedTime: '3 minutes',
    icon: 'journal',
    actionUrl: '/journal/new',
    completed: true
  },
  // More actions...
];
```

### 4. Cross-System Integration
- Journey content integration
- Emergency mode availability
- Ruqya practice status
- Journal streak tracking

### 5. Personalization Engine
- Layer-specific content recommendation
- User preference-based adaptations
- Usage pattern recognition
- Content rotation algorithms

## Data Models

### User Action Status
```sql
create table public.user_action_status (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid references auth.users not null,
    action_type text not null,
    date date not null,
    completed boolean default false,
    completion_time timestamp with time zone,
    created_at timestamp with time zone default now() not null,
    
    constraint unique_user_action_day
    unique (user_id, action_type, date)
);
```

### Daily Check-ins
```sql
create table public.daily_check_ins (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid references auth.users not null,
    mood text not null,
    dhikr_count integer,
    prayer_consistency integer,
    notes text,
    check_in_date timestamp with time zone not null,
    created_at timestamp with time zone default now() not null,
    
    constraint unique_user_daily_checkin
    unique (user_id, date(check_in_date))
);
```

### Journey Daily Content
```sql
create table public.journey_daily_content (
    id uuid primary key default uuid_generate_v4(),
    journey_id uuid references public.user_journeys not null,
    day_number integer not null,
    name_spotlight_id uuid references public.names_of_allah,
    quranic_verse_id uuid references public.quranic_verses,
    reflection_prompt_id uuid references public.reflection_prompts,
    action_step_id uuid references public.action_steps,
    created_at timestamp with time zone default now() not null,
    
    constraint unique_journey_day
    unique (journey_id, day_number)
);
```

### Analytics Metrics
```sql
create table public.analytics_metrics (
    time timestamp with time zone not null,
    user_id uuid references auth.users not null,
    metric_name text not null,
    metric_value numeric not null,
    metadata jsonb,
    
    primary key (time, user_id, metric_name)
);
```

## API Endpoints

### Today's Dashboard
```http
GET /api/dashboard/today
Response:
{
  "status": "success",
  "data": {
    "date": "2025-05-26T13:45:26.563Z",
    "todaysFocus": {
      "nameOfAllah": {
        "name": "Ar-Rahman",
        "transliteration": "The Most Merciful",
        "benefits": ["Compassion", "Kindness", "Love"]
      },
      "quranicVerse": {
        "arabic": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
        "translation": "In the name of Allah, the Most Merciful, the Most Compassionate",
        "reference": "Al-Fatihah 1:1"
      },
      "reflectionPrompt": "How has Allah's mercy manifested in your life today?"
    },
    "journeyStatus": {
      "active": true,
      "currentDay": 12,
      "totalDays": 40,
      "journeyType": "40-day",
      "checkedIn": true
    },
    "quickActions": [...],
    "emergencyMode": {
      "available": true,
      "recentlyUsed": false
    },
    "healingWheel": {
      "jism": 65,
      "nafs": 40,
      "aql": 75,
      "qalb": 80,
      "ruh": 60
    }
  }
}
```

### Dashboard Metrics
```http
GET /api/dashboard/metrics
Response:
{
  "status": "success",
  "data": {
    "streaks": {
      "journal": 12,
      "dhikr": 8,
      "ruqya": 15,
      "overall": 15
    },
    "totals": {
      "journalEntries": 45,
      "dhikrSessions": 52,
      "completedPractices": 87
    },
    "scores": {
      "consistency": 85,
      "engagement": 72
    },
    "recentActivities": [...]
  }
}
```

### Quick Actions
```http
GET /api/dashboard/quick-actions
Response:
{
  "status": "success",
  "data": {
    "actions": [
      {
        "id": "dhikr",
        "type": "dhikr",
        "title": "Daily Dhikr",
        "description": "Complete today's dhikr practice",
        "priority": "high",
        "estimatedTime": "5 minutes",
        "icon": "tasbih",
        "actionUrl": "/dhikr",
        "completed": false
      },
      ...
    ]
  }
}
```

### Complete Quick Action
```http
POST /api/dashboard/quick-actions/:actionType/complete
Response:
{
  "status": "success",
  "data": {
    "action": {
      "id": "uuid",
      "user_id": "user-uuid",
      "action_type": "dhikr",
      "date": "2025-05-26",
      "completed": true,
      "completion_time": "2025-05-26T13:45:26.563Z"
    }
  }
}
```

## Security Implementation

### Access Control
- JWT-based authentication for all endpoints
- Role-based authorization
- Rate limiting on sensitive endpoints
- Session tracking for suspicious activity

### Data Protection
```sql
-- Row-level security policy
create policy "Users can only view their own action status"
  on public.user_action_status
  for select
  using (auth.uid() = user_id);

create policy "Users can only update their own action status"
  on public.user_action_status
  for all
  using (auth.uid() = user_id);
```

### Privacy Considerations
- Personal data segregation
- Anonymized analytics
- Configurable data sharing options
- User control over dashboard content

## Performance Optimization

### Caching Strategy
```javascript
// Example caching implementation for dashboard content
const getTodayDashboard = async (userId) => {
  const cacheKey = `dashboard:today:${userId}`;
  
  // Try to get from cache first
  const cachedDashboard = await cache.get(cacheKey);
  if (cachedDashboard) {
    return JSON.parse(cachedDashboard);
  }
  
  // Aggregate dashboard content from multiple systems
  const dashboardContent = await aggregateDashboardContent(userId);
  
  // Store in cache with 15-minute expiry
  await cache.set(cacheKey, JSON.stringify(dashboardContent), 900);
  
  return dashboardContent;
};
```

### Data Aggregation Optimization
- Parallel data fetching from multiple systems
- Prioritized loading of critical components
- Background processing of non-critical metrics
- Incremental updates for real-time data

### Response Time Requirements
- Initial dashboard load: < 1000ms
- Dashboard updates: < 500ms
- Quick action completion: < 300ms
- Metrics calculation: < 2000ms (background)

## Integration Points

### Journey System Integration
```javascript
// Example of journey status integration
const getJourneyStatus = async (userId) => {
  const { data: journey } = await supabase
    .from('user_journeys')
    .select('*')
    .eq('user_id', userId)
    .eq('status', 'active')
    .order('created_at', { ascending: false })
    .limit(1)
    .single();
  
  if (!journey) {
    return {
      active: false,
      currentDay: 0,
      totalDays: 0,
      journeyType: 'Not started'
    };
  }
  
  const today = new Date();
  const startDate = new Date(journey.start_date);
  const currentDay = Math.ceil((today - startDate) / (1000 * 60 * 60 * 24));
  
  return {
    active: true,
    currentDay: currentDay,
    totalDays: journey.duration_days,
    journeyType: journey.journey_type,
    progress: (currentDay / journey.duration_days) * 100
  };
};
```

### Emergency System Integration
- Real-time emergency mode availability
- Recent session tracking
- Quick access to emergency resources
- Post-session recommendations

### Journal System Integration
- Recent entries display
- Streak calculation
- Mood trend analysis
- Reflection prompt suggestions

### Ruqya System Integration
- Daily protection status
- Practice recommendation
- Treatment plan progress
- Dua integration

## Error Handling

### Error Types
- `DASHBOARD_001`: Content aggregation failed
- `DASHBOARD_002`: Metrics calculation failed
- `DASHBOARD_003`: Action completion failed
- `DASHBOARD_004`: Data synchronization failed

### Graceful Degradation
```javascript
// Example of fallback mechanism for dashboard content
const getDailyFocus = async (userId) => {
  try {
    // Try to get personalized content
    const personalizedFocus = await getPersonalizedDailyFocus(userId);
    return personalizedFocus;
  } catch (error) {
    logger.warn(`Failed to get personalized focus: ${error.message}`);
    
    // Fall back to default content
    return getDefaultDailyFocus();
  }
};
```

### Error Response Format
```javascript
{
  "status": "error",
  "code": "DASHBOARD_001",
  "message": "Failed to load dashboard content",
  "details": {
    "component": "todaysFocus",
    "reason": "Journey data unavailable"
  }
}
```

## Monitoring and Analytics

### Key Metrics
1. **Usage Patterns**: Dashboard access frequency, interaction time
2. **Action Completion**: Percentage of quick actions completed
3. **Feature Engagement**: Feature popularity and usage duration
4. **Performance**: Load times, component rendering speeds

### Real-time Monitoring
- Dashboard load time anomalies
- Action completion failure rates
- Cache hit/miss ratios
- Cross-system integration failures

## Best Practices

### Implementation Guidelines
1. **Progressive Loading**: Critical content first, then non-critical
2. **Contextual Relevance**: Time-aware content presentation
3. **Personalization**: User preference-based adaptations
4. **Accessibility**: Support for diverse user needs

### Content Guidelines
- Culturally sensitive content presentation
- Time-appropriate recommendations
- Prayer time-aware scheduling
- Respectful and encouraging language

## Future Enhancements

### Planned Features
1. **Smart Recommendations**: ML-based personalized content
2. **Predictive Insights**: Anticipating user needs based on patterns
3. **Widget Customization**: User-configurable dashboard layout
4. **Extended Metrics**: Advanced healing analytics visualizations
5. **Community Integration**: Social progress sharing and encouragement

### Technical Improvements
- GraphQL implementation for optimized data fetching
- Real-time updates via WebSockets
- Offline support with background synchronization
- Progressive Web App enhancements

