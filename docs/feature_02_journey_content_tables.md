# Feature 2: Personalized Healing Journeys - Content Table Structures

This document outlines the Prisma schema for the new and updated tables supporting the rich content used in Personalized Healing Journeys.

## Core Daily Practice Component

### `DailyPracticeInJourney` (Updated)

This model represents each of the 5 components within a single day of a user's journey.

```prisma
model DailyPracticeInJourney {
  id                      String       @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  journeyDayId            String       @map("journey_day_id") @db.Uuid
  journeyDay              JourneyDay   @relation(fields: [journeyDayId], references: [id], onDelete: Cascade)

  type                    PracticeType // Enum: MorningCheckIn, NameOfAllahSpotlight, QuranicVerseReflection, PersonalReflectionJournaling, SunnahPractice, etc.
  title                   String
  description             String
  duration                Int          // Estimated duration in minutes
  instructions            String
  orderIndex              Int          @default(0) @map("order_index") // Order of this component within the day (0-4 for the 5 components)

  componentData           Json?        @map("component_data") // Holds specific details for the component type if not linked to a dedicated content table, or for UI hints.
                                       // e.g., For MorningCheckIn: { moodScaleMin: 1, moodScaleMax: 10, ... }
                                       // e.g., For PersonalReflectionJournaling: { prompts: ["...", "..."], allowVoiceNote: true }

  // Links to specific rich content tables
  nameOfAllahContentId    String?      @db.Uuid
  nameOfAllahContent      NameOfAllahContent? @relation(fields: [nameOfAllahContentId], references: [id])

  quranicVerseContentId   String?      @db.Uuid
  quranicVerseContent     QuranicVerseContent? @relation(fields: [quranicVerseContentId], references: [id])

  sunnahPracticeContentId String?      @db.Uuid
  sunnahPracticeContent   SunnahPracticeContent? @relation(fields: [sunnahPracticeContentId], references: [id])

  // Common optional fields from original schema (can be populated by AI or from linked content)
  arabicText              String?      @map("arabic_text")
  transliteration         String?
  translation             String?
  benefits                String[]     @default([])
  layerFocus              LayerFocus   @map("layer_focus") // Enum: jism, nafs, aql, qalb, ruh
  difficultyLevel         String       @default("beginner") @map("difficulty_level") // E.g., beginner, intermediate, advanced
  ruqyaComponent          Boolean      @default(false) @map("ruqya_component")
  professionalContext     String?      @map("professional_context")
  culturalNotes           String?      @map("cultural_notes")

  createdAt               DateTime     @default(now()) @map("created_at") @db.Timestamp(6)
}

// Enum PracticeType (ensure these are present)
// enum PracticeType {
//   MorningCheckIn
//   NameOfAllahSpotlight
//   QuranicVerseReflection
//   PersonalReflectionJournaling
//   SunnahPractice
//   // ... other existing types like dhikr, prayer, etc.
// }
```

**Notes for `DailyPracticeInJourney`:**
*   `componentData`: This JSON field can store UI hints or specific data for components that don't have a dedicated rich content table (like `MorningCheckIn` prompts or `PersonalReflectionJournaling` settings). For components linked to rich content tables, this field might be less used or store supplementary details.
*   The foreign key fields (`nameOfAllahContentId`, etc.) are used when `type` is `NameOfAllahSpotlight`, `QuranicVerseReflection`, or `SunnahPractice` to link to the detailed content.

## Rich Content Tables

These tables store the detailed, reusable Islamic content.

### 1. `NameOfAllahContent`

Stores details for the "Name of Allah Spotlight" component.

```prisma
model NameOfAllahContent {
  id                  String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name                String    @unique // e.g., Ar-Rahman
  arabicScript        String?   @map("arabic_script") // URL to calligraphy image or direct script
  audioUrl            String?   @map("audio_url")     // URL to audio pronunciation
  meaning             String
  significance        String    @db.Text
  reflectionPrompt    String?   @map("reflection_prompt") @db.Text
  practicalApplication String?  @map("practical_application") @db.Text
  dhikrCount          Int?      @map("dhikr_count")   // Recommended count for dhikr
  layerFocus          LayerFocus[] @default([]) @map("layer_focus") // Associated healing layers
  benefits            String[]  @default([])
  sourceReference     String?   @map("source_reference")
  isActive            Boolean   @default(true) @map("is_active")
  createdAt           DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt           DateTime  @updatedAt @map("updated_at") @db.Timestamp(6)

  dailyPractices      DailyPracticeInJourney[] // Relation back to daily practices using this content
}
```

### 2. `QuranicVerseContent`

Stores details for the "Quranic Verse Reflection" component.

```prisma
model QuranicVerseContent {
  id                      String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  surahNumber             Int       @map("surah_number")
  ayahNumber              Int       @map("ayah_number")
  arabicText              String    @map("arabic_text") @db.Text
  audioUrl                String?   @map("audio_url")     // URL to recitation of this specific verse
  reciterOptions          Json?     @map("reciter_options") // e.g., { "Mishary Alafasy": "url1", "Abdur-Rahman As-Sudais": "url2" }
  translationEn           String?   @map("translation_en") @db.Text
  translationUr           String?   @map("translation_ur") @db.Text
  // Add other translation fields as needed: translationFr, translationEs, etc.
  tafsirSource            String?   @map("tafsir_source") // e.g., "Tafsir Ibn Kathir (Abridged)", "Maariful Quran"
  tafsirEn                String?   @map("tafsir_en") @db.Text // Tafsir in English
  // Add other tafsir language fields as needed
  contextualExplanation   String?   @map("contextual_explanation") @db.Text // Brief context or theme
  reflectionPrompts       String[]  @default([]) @map("reflection_prompts")
  practicalApplication    String?   @map("practical_application") @db.Text
  layerFocus              LayerFocus[] @default([]) @map("layer_focus")
  themes                  String[]  @default([]) // e.g., ["Patience", "Gratitude", "Tawakkul", "Justice"]
  relatedHadith           String[]  @default([]) @map("related_hadith") // References or short hadith texts
  isActive                Boolean   @default(true) @map("is_active")
  createdAt               DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt               DateTime  @updatedAt @map("updated_at") @db.Timestamp(6)

  dailyPractices          DailyPracticeInJourney[] // Relation back to daily practices using this content

  @@unique([surahNumber, ayahNumber])
}
```

### 3. `SunnahPracticeContent`

Stores details for the "Sunnah Practice Integration" component.

```prisma
enum SunnahPracticeCategory {
  Physical
  Spiritual
  Social
  Mental
  Emotional
}

model SunnahPracticeContent {
  id                  String                 @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title               String                 @unique
  category            SunnahPracticeCategory
  description         String                 @db.Text
  stepsJson           Json?                  @map("steps_json") // Array of step objects: [{ "stepNumber": 1, "instruction": "...", "durationMinutes": 2, "duaArabic": "...", "duaTranslation": "..." }]
  duasJson            Json?                  @map("duas_json")    // Array of relevant dua objects: [{ "name": "Dua before X", "arabic": "...", "translation": "...", "audioUrl": "..." }]
  intention           String?                @db.Text // Suggested Niyyah
  reflection          String?                @db.Text // Points for reflection after the practice
  benefits            String[]               @default([])
  estimatedDuration   Int?                   @map("estimated_duration_minutes")
  difficultyLevel     String?                @map("difficulty_level") // beginner, intermediate, advanced
  sourceReference     String?                @map("source_reference") // e.g., Hadith reference (Sahih Bukhari 123, etc.)
  layerFocus          LayerFocus[]           @default([]) @map("layer_focus")
  relatedContentIds   String[]               @default([]) @map("related_content_ids") // For linking to other practices, verses, or Names of Allah by their IDs
  isActive            Boolean                @default(true) @map("is_active")
  createdAt           DateTime               @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt           DateTime               @updatedAt @map("updated_at") @db.Timestamp(6)

  dailyPractices      DailyPracticeInJourney[] // Relation back to daily practices using this content
}
```

## Initial Content Population Guide

Below are the key fields to focus on when populating these tables with initial content.

### For `NameOfAllahContent`:
*   **`name`**: The Name of Allah (e.g., "Ar-Rahman"). (Required, Unique)
*   **`arabicScript`**: URL to a high-quality calligraphy image or the Arabic script itself. (Recommended)
*   **`audioUrl`**: URL to an audio file with clear pronunciation. (Recommended)
*   **`meaning`**: Concise and accurate meaning. (Required)
*   **`significance`**: A paragraph explaining the spiritual importance and essence of the Name. (Required)
*   **`reflectionPrompt`**: A thought-provoking question to encourage user reflection. (Highly Recommended)
*   **`practicalApplication`**: A small, actionable way the user can try to embody or observe this attribute in their day. (Highly Recommended)
*   **`dhikrCount`**: Suggested number of repetitions if applicable for dhikr. (Optional)
*   **`layerFocus`**: Link to relevant `LayerFocus` enum values (e.g., `qalb`, `ruh`). (Recommended)
*   **`benefits`**: Short list of spiritual benefits. (Optional)
*   **`sourceReference`**: Quranic verse or Hadith where the name or its meaning is emphasized. (Optional)
*   **`isActive`**: Set to `true`.

### For `QuranicVerseContent`:
*   **`surahNumber`**: (Required)
*   **`ayahNumber`**: (Required)
*   **`arabicText`**: The full Arabic text of the verse. (Required)
*   **`audioUrl`**: URL to a recitation of this specific verse. (Highly Recommended)
*   **`reciterOptions`**: JSON object mapping reciter names to audio URLs if multiple recitations are available. (Optional)
*   **`translationEn`**: Clear English translation. (Required)
*   **`translationUr`**: Urdu translation. (Recommended if targeting Urdu speakers)
*   **`tafsirSource`**: Name of the Tafsir (e.g., "Tafsir Ibn Kathir (Abridged)"). (Recommended)
*   **`tafsirEn`**: A concise excerpt or summary of the Tafsir in English, relevant to the verse's healing aspect. (Highly Recommended)
*   **`contextualExplanation`**: Brief explanation of the verse's theme or immediate context. (Recommended)
*   **`reflectionPrompts`**: 1-2 questions related to the verse for user reflection. (Highly Recommended)
*   **`practicalApplication`**: How the user can apply the verse's message in their life. (Highly Recommended)
*   **`layerFocus`**: Associated `LayerFocus` values. (Recommended)
*   **`themes`**: Keywords like "Patience", "Gratitude". (Recommended)
*   **`isActive`**: Set to `true`.

### For `SunnahPracticeContent`:
*   **`title`**: Clear title of the Sunnah practice (e.g., "Mindful Wudu", "Sunnah of Smiling"). (Required, Unique)
*   **`category`**: `SunnahPracticeCategory` enum value (e.g., `Physical`, `Spiritual`). (Required)
*   **`description`**: Brief overview of the practice. (Required)
*   **`stepsJson`**: JSON array detailing each step. Each object in array could have `{"stepNumber": 1, "instruction": "...", "durationMinutes": 2, "duaArabic": "...", "duaTranslation": "..."}`. (Highly Recommended for multi-step practices)
*   **`duasJson`**: JSON array for any specific duas associated with the practice, not part of steps. Each object could have `{"name": "Dua before X", "arabic": "...", "translation": "...", "audioUrl": "..."}`. (Optional)
*   **`intention`**: Suggested Niyyah for performing the practice. (Recommended)
*   **`reflection`**: A point for the user to reflect on after performing the practice. (Recommended)
*   **`benefits`**: List of spiritual, mental, or physical benefits. (Recommended)
*   **`estimatedDurationMinutes`**: Approximate time to perform. (Optional)
*   **`difficultyLevel`**: e.g., "beginner". (Optional)
*   **`sourceReference`**: Hadith reference (e.g., "Sahih al-Bukhari, Hadith 5678"). (Highly Recommended for authenticity)
*   **`layerFocus`**: Associated `LayerFocus` values. (Recommended)
*   **`isActive`**: Set to `true`.

This documentation should help in understanding the data model for the new content types and how they integrate with the daily journey structure, as well as guide the initial content population effort.
