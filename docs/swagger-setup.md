# Swagger API Documentation Setup

This document explains how to use and maintain the Swagger API documentation for the Qalb Healing Backend.

## Overview

The API documentation is automatically generated using Swagger/OpenAPI 3.0 specification with JSDoc comments in the route files. The documentation is served at `/api-docs` endpoint when the server is running.

## Installation

The required dependencies have been added to `package.json`:

```bash
npm install swagger-jsdoc swagger-ui-express
```

## Accessing the Documentation

1. Start the development server:

   ```bash
   npm run dev
   ```

2. Open your browser and navigate to:

   ```
   http://localhost:3000/api-docs
   ```

3. You'll see the interactive Swagger UI interface where you can:
   - Browse all available endpoints
   - View request/response schemas
   - Test API endpoints directly
   - Download the OpenAPI specification

## Configuration

The Swagger configuration is located in `src/config/swagger.js` and includes:

- **API Information**: Title, version, description, contact details
- **Servers**: Development and production server URLs
- **Security Schemes**: JWT Bearer token authentication
- **Reusable Components**: Common schemas for requests/responses

## Authentication in Swagger UI

To test authenticated endpoints:

1. First, use the `/auth/login` endpoint to get a JWT token
2. Click the "Authorize" button in the Swagger UI
3. Enter the token in the format: `Bearer your-jwt-token-here`
4. Now you can test protected endpoints

## Adding Documentation to New Endpoints

When creating new API endpoints, add JSDoc comments above the route definition:

```javascript
/**
 * @swagger
 * /your-endpoint:
 *   post:
 *     summary: Brief description of what this endpoint does
 *     tags: [YourTag]
 *     security:
 *       - bearerAuth: []  # Include this for protected endpoints
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - requiredField
 *             properties:
 *               requiredField:
 *                 type: string
 *                 example: "example value"
 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/your-endpoint", yourController.yourMethod);
```

## Available Schema Components

The following reusable schemas are defined in `src/config/swagger.js`:

- `Success`: Standard success response format
- `Error`: Standard error response format
- `User`: User object schema
- `SymptomSubmission`: Schema for symptom submission data

## Tags Organization

Endpoints are organized by the following tags:

- **Authentication**: User signup, login, profile management
- **Symptoms**: Symptom tracking and analysis
- **Journey**: Spiritual healing journey management
- **Analytics**: Data analytics and insights
- **Emergency**: Emergency support features
- **Content**: Educational content and resources

## Best Practices

1. **Consistent Response Format**: All endpoints should use the standard `Success` and `Error` schemas
2. **Detailed Examples**: Provide realistic examples for all request/response data
3. **Proper HTTP Status Codes**: Use appropriate status codes (200, 201, 400, 401, 404, 500)
4. **Security Documentation**: Mark protected endpoints with `bearerAuth` security scheme
5. **Descriptive Summaries**: Write clear, concise summaries for each endpoint

## Maintenance

- Update the API version in `swagger.js` when making breaking changes
- Add new schema components for complex data structures
- Keep examples up-to-date with actual API behavior
- Review and update documentation when modifying existing endpoints

## Production Considerations

- The Swagger UI is available in all environments
- Consider adding authentication to the `/api-docs` endpoint in production
- The documentation reflects the current codebase state
- Server URLs are configured for both development and production environments

## Documentation Status

### ✅ Fully Documented Routes

1. **Authentication (`/auth/*`)** - Complete

   - POST `/auth/signup` - User registration
   - POST `/auth/login` - User authentication
   - GET `/auth/profile` - Get user profile
   - PATCH `/auth/profile` - Update user profile

2. **Symptoms (`/symptoms/*`)** - Complete

   - POST `/symptoms/submit` - Submit symptoms for AI analysis
   - GET `/symptoms/history` - Get symptom history
   - GET `/symptoms/latest-diagnosis` - Get latest diagnosis
   - PATCH `/symptoms/track` - Update symptom tracking

3. **Journey (`/journey/*`)** - Partially Complete

   - POST `/journey/start` - Start new spiritual journey
   - _Remaining endpoints need documentation_

4. **Analytics (`/analytics/*`)** - Partially Complete

   - GET `/analytics/progress` - Get user progress analytics
   - GET `/analytics/journey` - Get journey analytics
   - _Remaining endpoints need documentation_

5. **Journal (`/journal/*`)** - Partially Complete

   - POST `/journal/entries` - Create new journal entry
   - _Remaining endpoints need documentation_

6. **Emergency (`/emergency/*`)** - Partially Complete

   - POST `/emergency/start` - Start emergency session (Sakina Mode)
   - _Remaining endpoints need documentation_

7. **Ruqya (`/ruqya/*`)** - Partially Complete
   - GET `/ruqya/self-check/questions` - Get self-assessment questions
   - _Remaining endpoints need documentation_

### 🔄 Routes Needing Documentation

- `community.routes.js` - Community features
- `content.routes.js` - Educational content
- `dashboard.routes.js` - Dashboard analytics
- `sadaqah.routes.js` - Charity/donation features

### 📝 Quick Documentation Template

For remaining endpoints, use this template:

```javascript
/**
 * @swagger
 * /your-route:
 *   method:
 *     summary: Brief description
 *     tags: [YourTag]
 *     security:
 *       - bearerAuth: []  # For protected routes
 *     parameters:  # For query/path parameters
 *       - in: query
 *         name: paramName
 *         schema:
 *           type: string
 *         description: Parameter description
 *     requestBody:  # For POST/PATCH/PUT
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 example: "example value"
 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
```
