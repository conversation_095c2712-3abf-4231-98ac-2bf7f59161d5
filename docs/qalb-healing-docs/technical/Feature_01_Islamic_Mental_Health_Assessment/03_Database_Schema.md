# Feature 01: Islamic Mental Health Assessment - Database Schema

## 📋 Document Overview

**Feature**: Islamic Mental Health Assessment v7  
**Document Type**: Database Schema Specification  
**Version**: 1.0  
**Date**: January 2025  
**Status**: Draft  

## 🗄️ Database Overview

### **Database Technology**
- **Primary Database**: PostgreSQL 15+ with Supabase
- **ORM**: Prisma with TypeScript
- **Caching Layer**: Redis for session and response caching
- **Search Engine**: Elasticsearch for analytics and reporting
- **Backup Strategy**: Automated daily backups with point-in-time recovery

### **Schema Design Principles**
- **Islamic Privacy**: Extra security layers for spiritual and religious data
- **Scalability**: Designed for millions of users globally
- **Performance**: Optimized indexes for common query patterns
- **Compliance**: HIPAA-compliant healthcare data handling
- **Audit Trail**: Complete logging of all data changes

## 📊 Core Assessment Tables

### **assessment_sessions**
Primary table for managing assessment sessions

```sql
CREATE TABLE assessment_sessions (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User Reference
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Session Configuration
    session_type VARCHAR(50) NOT NULL CHECK (session_type IN ('initial', 'spiritual', 'follow_up', 'crisis')),
    session_version VARCHAR(10) NOT NULL DEFAULT 'v7',
    
    -- Session Status
    status VARCHAR(20) NOT NULL DEFAULT 'in_progress' 
        CHECK (status IN ('in_progress', 'completed', 'paused', 'abandoned', 'expired')),
    
    -- Timing Information
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    paused_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    total_duration_minutes INTEGER,
    
    -- Assessment Configuration
    adaptive_logic_enabled BOOLEAN DEFAULT TRUE,
    crisis_monitoring_enabled BOOLEAN DEFAULT TRUE,
    language_preference VARCHAR(10) DEFAULT 'en' CHECK (language_preference IN ('en', 'ar')),
    
    -- Crisis Detection
    crisis_level VARCHAR(20) DEFAULT 'none' 
        CHECK (crisis_level IN ('none', 'low', 'moderate', 'high', 'critical')),
    crisis_detected_at TIMESTAMP WITH TIME ZONE,
    crisis_resolved_at TIMESTAMP WITH TIME ZONE,
    
    -- Progress Tracking
    current_question_id VARCHAR(100),
    questions_answered INTEGER DEFAULT 0,
    estimated_questions_remaining INTEGER,
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    
    -- User Profile Context
    user_profile_snapshot JSONB DEFAULT '{}',
    
    -- Session Metadata
    metadata JSONB DEFAULT '{}',
    device_info JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    CONSTRAINT valid_completion_percentage CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    CONSTRAINT valid_duration CHECK (total_duration_minutes >= 0),
    CONSTRAINT valid_questions_answered CHECK (questions_answered >= 0)
);

-- Indexes for performance
CREATE INDEX idx_assessment_sessions_user_id ON assessment_sessions(user_id);
CREATE INDEX idx_assessment_sessions_status ON assessment_sessions(status);
CREATE INDEX idx_assessment_sessions_session_type ON assessment_sessions(session_type);
CREATE INDEX idx_assessment_sessions_created_at ON assessment_sessions(created_at);
CREATE INDEX idx_assessment_sessions_crisis_level ON assessment_sessions(crisis_level);
CREATE INDEX idx_assessment_sessions_user_status ON assessment_sessions(user_id, status);

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_assessment_sessions_updated_at 
    BEFORE UPDATE ON assessment_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **assessment_responses**
Stores individual question responses with detailed metadata

```sql
CREATE TABLE assessment_responses (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Session Reference
    session_id UUID NOT NULL REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    
    -- Question Information
    question_id VARCHAR(100) NOT NULL,
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL 
        CHECK (question_type IN ('scale', 'multiple_choice', 'text', 'boolean', 'multi_select', 'frequency')),
    question_version VARCHAR(10) DEFAULT 'v7',
    
    -- Layer Classification
    layer_category VARCHAR(20) NOT NULL 
        CHECK (layer_category IN ('jism', 'nafs', 'aql', 'qalb', 'ruh', 'general', 'crisis')),
    layer_subcategory VARCHAR(50),
    
    -- Response Data
    response_value JSONB NOT NULL,
    response_confidence DECIMAL(3,2) CHECK (response_confidence >= 0 AND response_confidence <= 1),
    
    -- Timing Metrics
    response_time_seconds INTEGER NOT NULL CHECK (response_time_seconds >= 0),
    hesitation_time_seconds INTEGER DEFAULT 0,
    question_display_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    response_submitted_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- User Behavior
    answer_changed BOOLEAN DEFAULT FALSE,
    skip_requested BOOLEAN DEFAULT FALSE,
    help_accessed BOOLEAN DEFAULT FALSE,
    
    -- AI Analysis
    ai_confidence_score DECIMAL(3,2),
    ai_analysis_result JSONB DEFAULT '{}',
    pattern_indicators JSONB DEFAULT '[]',
    
    -- Crisis Detection
    crisis_indicators JSONB DEFAULT '[]',
    crisis_score DECIMAL(3,2) DEFAULT 0.00,
    
    -- Validation
    validation_status VARCHAR(20) DEFAULT 'valid' 
        CHECK (validation_status IN ('valid', 'invalid', 'flagged', 'review_required')),
    validation_notes TEXT,
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_response_confidence CHECK (response_confidence IS NULL OR (response_confidence >= 0 AND response_confidence <= 1)),
    CONSTRAINT valid_ai_confidence CHECK (ai_confidence_score IS NULL OR (ai_confidence_score >= 0 AND ai_confidence_score <= 1)),
    CONSTRAINT valid_crisis_score CHECK (crisis_score >= 0 AND crisis_score <= 1)
);

-- Indexes for performance
CREATE INDEX idx_assessment_responses_session_id ON assessment_responses(session_id);
CREATE INDEX idx_assessment_responses_question_id ON assessment_responses(question_id);
CREATE INDEX idx_assessment_responses_layer_category ON assessment_responses(layer_category);
CREATE INDEX idx_assessment_responses_created_at ON assessment_responses(created_at);
CREATE INDEX idx_assessment_responses_crisis_score ON assessment_responses(crisis_score);
CREATE INDEX idx_assessment_responses_session_layer ON assessment_responses(session_id, layer_category);

-- GIN index for JSONB fields
CREATE INDEX idx_assessment_responses_response_value ON assessment_responses USING GIN (response_value);
CREATE INDEX idx_assessment_responses_ai_analysis ON assessment_responses USING GIN (ai_analysis_result);
CREATE INDEX idx_assessment_responses_pattern_indicators ON assessment_responses USING GIN (pattern_indicators);
```

### **five_layer_analysis**
Stores AI analysis results for each of the five Islamic health layers

```sql
CREATE TABLE five_layer_analysis (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Session Reference
    session_id UUID NOT NULL REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    
    -- Layer Information
    layer_name VARCHAR(20) NOT NULL 
        CHECK (layer_name IN ('jism', 'nafs', 'aql', 'qalb', 'ruh')),
    
    -- Impact Assessment
    impact_level VARCHAR(20) NOT NULL 
        CHECK (impact_level IN ('none', 'mild', 'moderate', 'severe', 'critical', 'unknown')),
    impact_score DECIMAL(4,2) CHECK (impact_score >= 0 AND impact_score <= 10),
    
    -- Confidence Metrics
    confidence_level VARCHAR(20) NOT NULL 
        CHECK (confidence_level IN ('low', 'medium', 'high')),
    confidence_score DECIMAL(3,2) NOT NULL CHECK (confidence_score >= 0 AND confidence_score <= 1),
    
    -- Symptom Analysis
    primary_symptoms JSONB DEFAULT '[]',
    secondary_symptoms JSONB DEFAULT '[]',
    symptom_patterns JSONB DEFAULT '{}',
    
    -- AI Analysis Results
    ai_analysis JSONB DEFAULT '{}',
    pattern_recognition_results JSONB DEFAULT '{}',
    severity_assessment JSONB DEFAULT '{}',
    
    -- Assessment Completeness
    requires_deeper_assessment BOOLEAN DEFAULT FALSE,
    assessment_completeness DECIMAL(3,2) DEFAULT 1.00,
    missing_data_indicators JSONB DEFAULT '[]',
    
    -- Spiritual Indicators (for Qalb and Ruh layers)
    spiritual_indicators JSONB DEFAULT '[]',
    spiritual_illness_probability DECIMAL(3,2),
    
    -- Treatment Implications
    treatment_urgency VARCHAR(20) DEFAULT 'normal' 
        CHECK (treatment_urgency IN ('low', 'normal', 'high', 'urgent')),
    recommended_interventions JSONB DEFAULT '[]',
    
    -- Validation and Review
    analysis_version VARCHAR(10) DEFAULT 'v7',
    reviewed_by_human BOOLEAN DEFAULT FALSE,
    human_reviewer_id UUID REFERENCES profiles(id),
    review_notes TEXT,
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_confidence_score CHECK (confidence_score >= 0 AND confidence_score <= 1),
    CONSTRAINT valid_impact_score CHECK (impact_score IS NULL OR (impact_score >= 0 AND impact_score <= 10)),
    CONSTRAINT valid_assessment_completeness CHECK (assessment_completeness >= 0 AND assessment_completeness <= 1),
    CONSTRAINT valid_spiritual_probability CHECK (spiritual_illness_probability IS NULL OR (spiritual_illness_probability >= 0 AND spiritual_illness_probability <= 1))
);

-- Indexes for performance
CREATE INDEX idx_five_layer_analysis_session_id ON five_layer_analysis(session_id);
CREATE INDEX idx_five_layer_analysis_layer_name ON five_layer_analysis(layer_name);
CREATE INDEX idx_five_layer_analysis_impact_level ON five_layer_analysis(impact_level);
CREATE INDEX idx_five_layer_analysis_confidence_level ON five_layer_analysis(confidence_level);
CREATE INDEX idx_five_layer_analysis_requires_deeper ON five_layer_analysis(requires_deeper_assessment);
CREATE INDEX idx_five_layer_analysis_session_layer ON five_layer_analysis(session_id, layer_name);

-- GIN indexes for JSONB fields
CREATE INDEX idx_five_layer_analysis_primary_symptoms ON five_layer_analysis USING GIN (primary_symptoms);
CREATE INDEX idx_five_layer_analysis_ai_analysis ON five_layer_analysis USING GIN (ai_analysis);
CREATE INDEX idx_five_layer_analysis_spiritual_indicators ON five_layer_analysis USING GIN (spiritual_indicators);

-- Trigger for updated_at
CREATE TRIGGER update_five_layer_analysis_updated_at 
    BEFORE UPDATE ON five_layer_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 🕌 Spiritual Diagnosis Tables

### **spiritual_diagnosis_sessions**
Manages individual spiritual diagnosis sessions for each spiritual illness type

```sql
CREATE TABLE spiritual_diagnosis_sessions (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Assessment Reference
    assessment_session_id UUID NOT NULL REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    
    -- Spiritual Illness Information
    spiritual_illness_type VARCHAR(50) NOT NULL 
        CHECK (spiritual_illness_type IN ('sihr', 'ayn', 'mass', 'waswas')),
    session_number INTEGER NOT NULL CHECK (session_number >= 1 AND session_number <= 4),
    
    -- Session Status
    status VARCHAR(20) NOT NULL DEFAULT 'pending' 
        CHECK (status IN ('pending', 'in_progress', 'completed', 'abandoned', 'failed')),
    
    -- Timing Information
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER CHECK (duration_minutes >= 0),
    
    -- Preparation Status
    preparation_completed JSONB DEFAULT '{}',
    spiritual_readiness_score DECIMAL(3,2),
    
    -- Session Configuration
    audio_content_urls JSONB DEFAULT '{}',
    recitation_language VARCHAR(10) DEFAULT 'arabic' CHECK (recitation_language IN ('arabic', 'english')),
    recitation_speed VARCHAR(10) DEFAULT 'medium' CHECK (recitation_speed IN ('slow', 'medium', 'fast')),
    guidance_level VARCHAR(20) DEFAULT 'beginner' CHECK (guidance_level IN ('beginner', 'intermediate', 'advanced')),
    
    -- Reaction Data
    reactions JSONB DEFAULT '{}',
    overall_intensity INTEGER CHECK (overall_intensity >= 1 AND overall_intensity <= 10),
    reaction_patterns JSONB DEFAULT '{}',
    
    -- Analysis Results
    spiritual_illness_likelihood VARCHAR(20) 
        CHECK (spiritual_illness_likelihood IN ('none', 'low', 'moderate', 'high', 'very_high')),
    likelihood_score DECIMAL(3,2) CHECK (likelihood_score >= 0 AND likelihood_score <= 1),
    confidence_level VARCHAR(20) 
        CHECK (confidence_level IN ('low', 'medium', 'high')),
    
    -- Key Indicators
    significant_reactions JSONB DEFAULT '[]',
    reaction_interpretation JSONB DEFAULT '{}',
    
    -- Safety and Support
    safety_concerns JSONB DEFAULT '[]',
    support_provided JSONB DEFAULT '[]',
    emergency_intervention_required BOOLEAN DEFAULT FALSE,
    
    -- Scholar Review
    scholar_review_requested BOOLEAN DEFAULT FALSE,
    scholar_review_completed BOOLEAN DEFAULT FALSE,
    scholar_notes TEXT,
    scholar_id UUID REFERENCES profiles(id),
    
    -- Session Metadata
    user_notes TEXT,
    session_metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_likelihood_score CHECK (likelihood_score IS NULL OR (likelihood_score >= 0 AND likelihood_score <= 1)),
    CONSTRAINT valid_spiritual_readiness CHECK (spiritual_readiness_score IS NULL OR (spiritual_readiness_score >= 0 AND spiritual_readiness_score <= 1)),
    CONSTRAINT unique_session_per_illness UNIQUE (assessment_session_id, spiritual_illness_type, session_number)
);

-- Indexes for performance
CREATE INDEX idx_spiritual_diagnosis_sessions_assessment_id ON spiritual_diagnosis_sessions(assessment_session_id);
CREATE INDEX idx_spiritual_diagnosis_sessions_illness_type ON spiritual_diagnosis_sessions(spiritual_illness_type);
CREATE INDEX idx_spiritual_diagnosis_sessions_status ON spiritual_diagnosis_sessions(status);
CREATE INDEX idx_spiritual_diagnosis_sessions_likelihood ON spiritual_diagnosis_sessions(spiritual_illness_likelihood);
CREATE INDEX idx_spiritual_diagnosis_sessions_scholar_review ON spiritual_diagnosis_sessions(scholar_review_requested);
CREATE INDEX idx_spiritual_diagnosis_sessions_created_at ON spiritual_diagnosis_sessions(created_at);

-- GIN indexes for JSONB fields
CREATE INDEX idx_spiritual_diagnosis_reactions ON spiritual_diagnosis_sessions USING GIN (reactions);
CREATE INDEX idx_spiritual_diagnosis_significant_reactions ON spiritual_diagnosis_sessions USING GIN (significant_reactions);

-- Trigger for updated_at
CREATE TRIGGER update_spiritual_diagnosis_sessions_updated_at 
    BEFORE UPDATE ON spiritual_diagnosis_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **spiritual_reactions**
Detailed tracking of reactions during spiritual diagnosis sessions

```sql
CREATE TABLE spiritual_reactions (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Session Reference
    spiritual_session_id UUID NOT NULL REFERENCES spiritual_diagnosis_sessions(id) ON DELETE CASCADE,
    
    -- Timing Information
    reaction_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_phase VARCHAR(50) NOT NULL 
        CHECK (session_phase IN ('preparation', 'main_recitation', 'post_recitation', 'water_application')),
    phase_time_offset INTEGER NOT NULL CHECK (phase_time_offset >= 0),
    
    -- Reaction Classification
    reaction_category VARCHAR(50) NOT NULL 
        CHECK (reaction_category IN ('physical', 'emotional', 'spiritual', 'energy', 'cognitive')),
    reaction_type VARCHAR(100) NOT NULL,
    reaction_subtype VARCHAR(100),
    
    -- Intensity and Duration
    intensity INTEGER NOT NULL CHECK (intensity >= 1 AND intensity <= 10),
    duration_seconds INTEGER CHECK (duration_seconds >= 0),
    
    -- Location and Description
    body_location VARCHAR(100),
    detailed_description TEXT NOT NULL,
    
    -- User Assessment
    user_comfort_level INTEGER CHECK (user_comfort_level >= 1 AND user_comfort_level <= 10),
    user_interpretation TEXT,
    
    -- Pattern Analysis
    reaction_pattern VARCHAR(100),
    correlation_with_recitation BOOLEAN,
    correlation_with_water BOOLEAN,
    
    -- Significance Assessment
    significance_level VARCHAR(20) DEFAULT 'normal' 
        CHECK (significance_level IN ('minimal', 'normal', 'significant', 'very_significant', 'extreme')),
    diagnostic_relevance DECIMAL(3,2) CHECK (diagnostic_relevance >= 0 AND diagnostic_relevance <= 1),
    
    -- Safety Considerations
    safety_concern BOOLEAN DEFAULT FALSE,
    intervention_required BOOLEAN DEFAULT FALSE,
    intervention_provided TEXT,
    
    -- Metadata
    reaction_metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_spiritual_reactions_session_id ON spiritual_reactions(spiritual_session_id);
CREATE INDEX idx_spiritual_reactions_category ON spiritual_reactions(reaction_category);
CREATE INDEX idx_spiritual_reactions_intensity ON spiritual_reactions(intensity);
CREATE INDEX idx_spiritual_reactions_significance ON spiritual_reactions(significance_level);
CREATE INDEX idx_spiritual_reactions_timestamp ON spiritual_reactions(reaction_timestamp);
CREATE INDEX idx_spiritual_reactions_session_phase ON spiritual_reactions(spiritual_session_id, session_phase);

-- GIN index for metadata
CREATE INDEX idx_spiritual_reactions_metadata ON spiritual_reactions USING GIN (reaction_metadata);
```

## 🎯 Treatment Plan Tables

### **treatment_plans**
Comprehensive treatment plans based on assessment and spiritual diagnosis

```sql
CREATE TABLE treatment_plans (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User and Assessment References
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    assessment_session_id UUID NOT NULL REFERENCES assessment_sessions(id),
    
    -- Plan Configuration
    plan_tier INTEGER NOT NULL CHECK (plan_tier IN (1, 2)),
    plan_version VARCHAR(10) DEFAULT 'v7',
    
    -- Treatment Strategy
    treatment_approach VARCHAR(50) NOT NULL DEFAULT 'inner_to_outer' 
        CHECK (treatment_approach IN ('inner_to_outer', 'symptom_focused', 'integrated', 'crisis_intervention')),
    treatment_order JSONB NOT NULL,
    
    -- Timeline Information
    estimated_timeline_weeks INTEGER CHECK (estimated_timeline_weeks > 0),
    estimated_start_date DATE DEFAULT CURRENT_DATE,
    estimated_completion_date DATE,
    actual_start_date DATE,
    actual_completion_date DATE,
    
    -- Plan Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' 
        CHECK (status IN ('draft', 'active', 'paused', 'completed', 'abandoned', 'modified')),
    
    -- Milestones and Progress
    milestones JSONB DEFAULT '[]',
    current_phase INTEGER DEFAULT 1,
    current_layer VARCHAR(20) CHECK (current_layer IN ('jism', 'nafs', 'aql', 'qalb', 'ruh')),
    
    -- Recommendations
    tier1_recommendations JSONB DEFAULT '{}',
    tier2_recommendations JSONB DEFAULT '{}',
    emergency_protocols JSONB DEFAULT '{}',
    
    -- Spiritual Components
    spiritual_diagnosis_results JSONB DEFAULT '{}',
    primary_spiritual_illness VARCHAR(50),
    secondary_spiritual_factors JSONB DEFAULT '[]',
    
    -- User Preferences
    user_preferences JSONB DEFAULT '{}',
    treatment_intensity VARCHAR(20) DEFAULT 'moderate' 
        CHECK (treatment_intensity IN ('light', 'moderate', 'intensive')),
    daily_time_commitment INTEGER CHECK (daily_time_commitment > 0),
    
    -- Success Metrics
    success_criteria JSONB DEFAULT '{}',
    expected_outcomes JSONB DEFAULT '{}',
    
    -- Customization and Adjustments
    customizations JSONB DEFAULT '{}',
    adjustment_history JSONB DEFAULT '[]',
    last_adjustment_date DATE,
    
    -- Professional Involvement
    professional_oversight_required BOOLEAN DEFAULT FALSE,
    assigned_counselor_id UUID REFERENCES profiles(id),
    scholar_consultation_required BOOLEAN DEFAULT FALSE,
    assigned_scholar_id UUID REFERENCES profiles(id),
    
    -- Plan Metadata
    generation_algorithm_version VARCHAR(10) DEFAULT 'v7',
    plan_metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_timeline CHECK (estimated_timeline_weeks IS NULL OR estimated_timeline_weeks > 0),
    CONSTRAINT valid_daily_commitment CHECK (daily_time_commitment IS NULL OR daily_time_commitment > 0),
    CONSTRAINT valid_current_phase CHECK (current_phase >= 1),
    CONSTRAINT valid_dates CHECK (estimated_completion_date IS NULL OR estimated_completion_date >= estimated_start_date)
);

-- Indexes for performance
CREATE INDEX idx_treatment_plans_user_id ON treatment_plans(user_id);
CREATE INDEX idx_treatment_plans_assessment_id ON treatment_plans(assessment_session_id);
CREATE INDEX idx_treatment_plans_status ON treatment_plans(status);
CREATE INDEX idx_treatment_plans_plan_tier ON treatment_plans(plan_tier);
CREATE INDEX idx_treatment_plans_current_phase ON treatment_plans(current_phase);
CREATE INDEX idx_treatment_plans_created_at ON treatment_plans(created_at);
CREATE INDEX idx_treatment_plans_user_status ON treatment_plans(user_id, status);

-- GIN indexes for JSONB fields
CREATE INDEX idx_treatment_plans_treatment_order ON treatment_plans USING GIN (treatment_order);
CREATE INDEX idx_treatment_plans_milestones ON treatment_plans USING GIN (milestones);
CREATE INDEX idx_treatment_plans_spiritual_results ON treatment_plans USING GIN (spiritual_diagnosis_results);

-- Trigger for updated_at
CREATE TRIGGER update_treatment_plans_updated_at 
    BEFORE UPDATE ON treatment_plans 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **treatment_progress**
Tracks progress on treatment plan milestones and protocols

```sql
CREATE TABLE treatment_progress (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Plan Reference
    treatment_plan_id UUID NOT NULL REFERENCES treatment_plans(id) ON DELETE CASCADE,
    
    -- Milestone Information
    milestone_id VARCHAR(100) NOT NULL,
    milestone_type VARCHAR(50) NOT NULL 
        CHECK (milestone_type IN ('layer_completion', 'phase_completion', 'protocol_mastery', 'symptom_reduction', 'spiritual_healing')),
    
    -- Progress Details
    target_layer VARCHAR(20) CHECK (target_layer IN ('jism', 'nafs', 'aql', 'qalb', 'ruh')),
    target_phase INTEGER,
    target_week INTEGER,
    
    -- Completion Status
    completion_status VARCHAR(20) NOT NULL DEFAULT 'not_started' 
        CHECK (completion_status IN ('not_started', 'in_progress', 'completed', 'skipped', 'failed')),
    completion_percentage DECIMAL(5,2) DEFAULT 0.00 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    
    -- Progress Metrics
    practice_adherence DECIMAL(3,2) CHECK (practice_adherence >= 0 AND practice_adherence <= 1),
    symptom_improvement DECIMAL(3,2) CHECK (symptom_improvement >= 0 AND symptom_improvement <= 1),
    user_satisfaction_rating INTEGER CHECK (user_satisfaction_rating >= 1 AND user_satisfaction_rating <= 10),
    difficulty_rating INTEGER CHECK (difficulty_rating >= 1 AND difficulty_rating <= 10),
    effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 10),
    
    -- Specific Outcomes
    specific_outcomes JSONB DEFAULT '[]',
    achieved_indicators JSONB DEFAULT '[]',
    remaining_challenges JSONB DEFAULT '[]',
    
    -- User Feedback
    user_feedback TEXT,
    user_notes TEXT,
    
    -- Timing Information
    started_at TIMESTAMP WITH TIME ZONE,
    target_completion_date DATE,
    actual_completion_date DATE,
    
    -- Adjustments and Modifications
    adjustments_made JSONB DEFAULT '[]',
    modification_reasons JSONB DEFAULT '[]',
    
    -- Professional Review
    professional_review_required BOOLEAN DEFAULT FALSE,
    reviewed_by_professional BOOLEAN DEFAULT FALSE,
    professional_notes TEXT,
    reviewer_id UUID REFERENCES profiles(id),
    
    -- Progress Metadata
    progress_metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_completion_percentage CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    CONSTRAINT valid_adherence CHECK (practice_adherence IS NULL OR (practice_adherence >= 0 AND practice_adherence <= 1)),
    CONSTRAINT valid_improvement CHECK (symptom_improvement IS NULL OR (symptom_improvement >= 0 AND symptom_improvement <= 1))
);

-- Indexes for performance
CREATE INDEX idx_treatment_progress_plan_id ON treatment_progress(treatment_plan_id);
CREATE INDEX idx_treatment_progress_milestone_id ON treatment_progress(milestone_id);
CREATE INDEX idx_treatment_progress_status ON treatment_progress(completion_status);
CREATE INDEX idx_treatment_progress_layer ON treatment_progress(target_layer);
CREATE INDEX idx_treatment_progress_phase ON treatment_progress(target_phase);
CREATE INDEX idx_treatment_progress_created_at ON treatment_progress(created_at);
CREATE INDEX idx_treatment_progress_plan_status ON treatment_progress(treatment_plan_id, completion_status);

-- GIN indexes for JSONB fields
CREATE INDEX idx_treatment_progress_outcomes ON treatment_progress USING GIN (specific_outcomes);
CREATE INDEX idx_treatment_progress_adjustments ON treatment_progress USING GIN (adjustments_made);

-- Trigger for updated_at
CREATE TRIGGER update_treatment_progress_updated_at 
    BEFORE UPDATE ON treatment_progress 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 🚨 Crisis Management Tables

### **crisis_events**
Comprehensive crisis event tracking and management

```sql
CREATE TABLE crisis_events (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User and Session References
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    session_id UUID REFERENCES assessment_sessions(id),
    treatment_plan_id UUID REFERENCES treatment_plans(id),
    
    -- Crisis Classification
    crisis_level VARCHAR(20) NOT NULL 
        CHECK (crisis_level IN ('low', 'moderate', 'high', 'critical', 'emergency')),
    crisis_type VARCHAR(50) NOT NULL 
        CHECK (crisis_type IN ('suicidal_ideation', 'self_harm', 'psychotic_episode', 'severe_spiritual_distress', 'panic_attack', 'substance_abuse', 'domestic_violence')),
    crisis_subtype VARCHAR(100),
    
    -- Detection Information
    detection_method VARCHAR(50) NOT NULL 
        CHECK (detection_method IN ('assessment_response', 'user_report', 'family_report', 'professional_referral', 'system_algorithm')),
    detection_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Trigger Factors
    trigger_factors JSONB DEFAULT '[]',
    contributing_factors JSONB DEFAULT '[]',
    environmental_factors JSONB DEFAULT '[]',
    
    -- Risk Assessment
    immediate_risk_level INTEGER NOT NULL CHECK (immediate_risk_level >= 1 AND immediate_risk_level <= 10),
    long_term_risk_level INTEGER CHECK (long_term_risk_level >= 1 AND long_term_risk_level <= 10),
    protective_factors JSONB DEFAULT '[]',
    risk_factors JSONB DEFAULT '[]',
    
    -- Response Actions
    immediate_response_actions JSONB DEFAULT '[]',
    resources_provided JSONB DEFAULT '[]',
    professional_contacted BOOLEAN DEFAULT FALSE,
    emergency_services_contacted BOOLEAN DEFAULT FALSE,
    family_notified BOOLEAN DEFAULT FALSE,
    
    -- Contact Information
    emergency_contacts_reached JSONB DEFAULT '[]',
    professional_contacts JSONB DEFAULT '[]',
    
    -- Resolution Information
    crisis_status VARCHAR(20) NOT NULL DEFAULT 'active' 
        CHECK (crisis_status IN ('active', 'stabilized', 'resolved', 'escalated', 'transferred')),
    resolution_timestamp TIMESTAMP WITH TIME ZONE,
    resolution_method VARCHAR(100),
    resolution_notes TEXT,
    
    -- Follow-up Requirements
    follow_up_required BOOLEAN DEFAULT TRUE,
    follow_up_timeline VARCHAR(50),
    follow_up_completed BOOLEAN DEFAULT FALSE,
    
    -- Professional Involvement
    professional_intervention_required BOOLEAN DEFAULT FALSE,
    assigned_crisis_counselor_id UUID REFERENCES profiles(id),
    hospitalization_required BOOLEAN DEFAULT FALSE,
    
    -- Location and Context
    user_location JSONB DEFAULT '{}',
    crisis_context JSONB DEFAULT '{}',
    
    -- Documentation
    crisis_notes TEXT,
    intervention_documentation TEXT,
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_immediate_risk CHECK (immediate_risk_level >= 1 AND immediate_risk_level <= 10),
    CONSTRAINT valid_long_term_risk CHECK (long_term_risk_level IS NULL OR (long_term_risk_level >= 1 AND long_term_risk_level <= 10))
);

-- Indexes for performance
CREATE INDEX idx_crisis_events_user_id ON crisis_events(user_id);
CREATE INDEX idx_crisis_events_crisis_level ON crisis_events(crisis_level);
CREATE INDEX idx_crisis_events_crisis_type ON crisis_events(crisis_type);
CREATE INDEX idx_crisis_events_status ON crisis_events(crisis_status);
CREATE INDEX idx_crisis_events_detection_timestamp ON crisis_events(detection_timestamp);
CREATE INDEX idx_crisis_events_immediate_risk ON crisis_events(immediate_risk_level);
CREATE INDEX idx_crisis_events_follow_up ON crisis_events(follow_up_required, follow_up_completed);

-- GIN indexes for JSONB fields
CREATE INDEX idx_crisis_events_trigger_factors ON crisis_events USING GIN (trigger_factors);
CREATE INDEX idx_crisis_events_response_actions ON crisis_events USING GIN (immediate_response_actions);

-- Trigger for updated_at
CREATE TRIGGER update_crisis_events_updated_at 
    BEFORE UPDATE ON crisis_events 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 📊 Analytics and Reporting Tables

### **assessment_analytics**
Aggregated analytics for assessment performance and patterns

```sql
CREATE TABLE assessment_analytics (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Time Period
    analytics_date DATE NOT NULL,
    analytics_period VARCHAR(20) NOT NULL 
        CHECK (analytics_period IN ('daily', 'weekly', 'monthly', 'quarterly')),
    
    -- Assessment Metrics
    total_assessments_started INTEGER DEFAULT 0,
    total_assessments_completed INTEGER DEFAULT 0,
    total_assessments_abandoned INTEGER DEFAULT 0,
    completion_rate DECIMAL(5,2),
    average_completion_time DECIMAL(6,2),
    
    -- Layer Analysis Metrics
    layer_analysis_distribution JSONB DEFAULT '{}',
    most_affected_layers JSONB DEFAULT '[]',
    spiritual_diagnosis_conversion_rate DECIMAL(5,2),
    
    -- Crisis Detection Metrics
    crisis_events_detected INTEGER DEFAULT 0,
    crisis_level_distribution JSONB DEFAULT '{}',
    crisis_resolution_rate DECIMAL(5,2),
    
    -- User Demographics
    user_demographics JSONB DEFAULT '{}',
    geographic_distribution JSONB DEFAULT '{}',
    
    -- Performance Metrics
    average_ai_processing_time DECIMAL(6,2),
    system_performance_metrics JSONB DEFAULT '{}',
    
    -- Quality Metrics
    user_satisfaction_scores JSONB DEFAULT '{}',
    assessment_accuracy_metrics JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for analytics
CREATE INDEX idx_assessment_analytics_date ON assessment_analytics(analytics_date);
CREATE INDEX idx_assessment_analytics_period ON assessment_analytics(analytics_period);
CREATE INDEX idx_assessment_analytics_date_period ON assessment_analytics(analytics_date, analytics_period);

-- GIN indexes for JSONB analytics data
CREATE INDEX idx_assessment_analytics_layer_distribution ON assessment_analytics USING GIN (layer_analysis_distribution);
CREATE INDEX idx_assessment_analytics_demographics ON assessment_analytics USING GIN (user_demographics);
```

## 🔐 Security and Audit Tables

### **data_access_logs**
Comprehensive audit trail for all data access

```sql
CREATE TABLE data_access_logs (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User and Session Information
    user_id UUID REFERENCES profiles(id),
    session_id VARCHAR(255),
    
    -- Access Details
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    operation_type VARCHAR(20) NOT NULL 
        CHECK (operation_type IN ('SELECT', 'INSERT', 'UPDATE', 'DELETE')),
    
    -- Request Information
    api_endpoint VARCHAR(255),
    http_method VARCHAR(10),
    request_ip INET,
    user_agent TEXT,
    
    -- Data Sensitivity
    data_classification VARCHAR(50) NOT NULL 
        CHECK (data_classification IN ('public', 'internal', 'confidential', 'restricted', 'spiritual_sensitive')),
    contains_pii BOOLEAN DEFAULT FALSE,
    contains_phi BOOLEAN DEFAULT FALSE,
    contains_spiritual_data BOOLEAN DEFAULT FALSE,
    
    -- Access Result
    access_granted BOOLEAN NOT NULL,
    access_denied_reason VARCHAR(255),
    
    -- Audit Information
    access_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Compliance
    gdpr_applicable BOOLEAN DEFAULT FALSE,
    hipaa_applicable BOOLEAN DEFAULT FALSE,
    islamic_privacy_applicable BOOLEAN DEFAULT FALSE
);

-- Indexes for audit queries
CREATE INDEX idx_data_access_logs_user_id ON data_access_logs(user_id);
CREATE INDEX idx_data_access_logs_table_name ON data_access_logs(table_name);
CREATE INDEX idx_data_access_logs_timestamp ON data_access_logs(access_timestamp);
CREATE INDEX idx_data_access_logs_operation ON data_access_logs(operation_type);
CREATE INDEX idx_data_access_logs_classification ON data_access_logs(data_classification);
CREATE INDEX idx_data_access_logs_spiritual ON data_access_logs(contains_spiritual_data);
```

## 🔄 Database Views

### **user_assessment_summary**
Comprehensive view of user assessment history and progress

```sql
CREATE VIEW user_assessment_summary AS
SELECT 
    p.id as user_id,
    p.email,
    p.full_name,
    COUNT(DISTINCT ass.id) as total_assessments,
    COUNT(DISTINCT CASE WHEN ass.status = 'completed' THEN ass.id END) as completed_assessments,
    COUNT(DISTINCT CASE WHEN ass.session_type = 'initial' THEN ass.id END) as initial_assessments,
    COUNT(DISTINCT CASE WHEN ass.session_type = 'spiritual' THEN ass.id END) as spiritual_assessments,
    MAX(ass.completed_at) as last_assessment_date,
    COUNT(DISTINCT tp.id) as treatment_plans_created,
    COUNT(DISTINCT CASE WHEN tp.status = 'active' THEN tp.id END) as active_treatment_plans,
    COUNT(DISTINCT ce.id) as crisis_events,
    MAX(ce.detection_timestamp) as last_crisis_event,
    AVG(ass.total_duration_minutes) as avg_assessment_duration
FROM profiles p
LEFT JOIN assessment_sessions ass ON p.id = ass.user_id
LEFT JOIN treatment_plans tp ON p.id = tp.user_id
LEFT JOIN crisis_events ce ON p.id = ce.user_id
GROUP BY p.id, p.email, p.full_name;
```

### **spiritual_diagnosis_summary**
Summary view of spiritual diagnosis results and patterns

```sql
CREATE VIEW spiritual_diagnosis_summary AS
SELECT 
    sds.assessment_session_id,
    ass.user_id,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN sds.status = 'completed' THEN 1 END) as completed_sessions,
    JSONB_AGG(
        JSONB_BUILD_OBJECT(
            'illness_type', sds.spiritual_illness_type,
            'likelihood', sds.spiritual_illness_likelihood,
            'likelihood_score', sds.likelihood_score,
            'confidence', sds.confidence_level
        )
    ) as diagnosis_results,
    MAX(sds.completed_at) as diagnosis_completion_date,
    AVG(sds.duration_minutes) as avg_session_duration,
    BOOL_OR(sds.emergency_intervention_required) as emergency_intervention_needed,
    BOOL_OR(sds.scholar_review_requested) as scholar_review_requested
FROM spiritual_diagnosis_sessions sds
JOIN assessment_sessions ass ON sds.assessment_session_id = ass.id
WHERE sds.status = 'completed'
GROUP BY sds.assessment_session_id, ass.user_id;
```

## 📈 Performance Optimization

### **Partitioning Strategy**
```sql
-- Partition assessment_responses by month for better performance
CREATE TABLE assessment_responses_y2025m01 PARTITION OF assessment_responses
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE assessment_responses_y2025m02 PARTITION OF assessment_responses
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- Continue partitioning for each month...
```

### **Materialized Views for Analytics**
```sql
-- Daily assessment metrics
CREATE MATERIALIZED VIEW daily_assessment_metrics AS
SELECT 
    DATE(created_at) as assessment_date,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
    AVG(total_duration_minutes) as avg_duration,
    COUNT(CASE WHEN crisis_level != 'none' THEN 1 END) as crisis_sessions
FROM assessment_sessions
GROUP BY DATE(created_at)
ORDER BY assessment_date;

-- Refresh daily
CREATE UNIQUE INDEX ON daily_assessment_metrics (assessment_date);
```

## 🔒 Row Level Security (RLS)

### **User Data Protection**
```sql
-- Enable RLS on sensitive tables
ALTER TABLE assessment_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE five_layer_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE spiritual_diagnosis_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE treatment_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE crisis_events ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY user_own_data_policy ON assessment_sessions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY user_own_responses_policy ON assessment_responses
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM assessment_sessions 
            WHERE id = assessment_responses.session_id 
            AND user_id = auth.uid()
        )
    );

-- Similar policies for other tables...
```

### **Professional Access Policies**
```sql
-- Mental health professionals can access assigned users
CREATE POLICY professional_access_policy ON assessment_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND role IN ('therapist', 'counselor', 'admin')
        )
    );
```

---

This comprehensive database schema provides a robust foundation for Feature 01, ensuring data integrity, performance, security, and compliance with healthcare and Islamic privacy standards.