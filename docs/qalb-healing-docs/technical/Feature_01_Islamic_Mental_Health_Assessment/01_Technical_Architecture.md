# Feature 01: Islamic Mental Health Assessment - Technical Architecture

## 📋 Document Overview

**Feature**: Islamic Mental Health Assessment v7  
**Document Type**: Technical Architecture Specification  
**Version**: 1.0  
**Date**: January 2025  
**Status**: Draft  

## 🏗️ Feature 01 Architecture Overview

> **Note**: This architecture diagram shows Feature 01-specific components. For overall app architecture, please refer to the main technical documentation.

### **Assessment Feature Architecture**

```mermaid
graph TB
    subgraph "Assessment Frontend"
        A[Assessment UI] --> B[Progressive Disclosure]
        B --> C[Question Renderer]
        C --> D[Response Validator]
        D --> E[Crisis Monitor]
    end
    
    subgraph "Assessment Backend"
        F[Assessment Controller] --> G[Session Manager]
        G --> H[Question Engine]
        H --> I[Response Processor]
        I --> J[AI Analyzer]
    end
    
    subgraph "Spiritual Diagnosis"
        K[Ruqya Session] --> L[Audio Manager]
        L --> M[Reaction Tracker]
        M --> N[Pattern Analyzer]
        N --> O[Scholar Integration]
    end
    
    subgraph "Treatment Planning"
        P[Tier 1 Generator] --> Q[Spiritual Processor]
        Q --> R[Tier 2 Generator]
        R --> S[Plan Optimizer]
    end
    
    A --> F
    J --> K
    O --> P
    S --> T[User Journey]
```

### **Component Architecture**

```mermaid
graph LR
    subgraph "Assessment Engine"
        A1[Progressive Disclosure Controller]
        A2[Adaptive Question Engine]
        A3[Response Validator]
        A4[Session Manager]
    end
    
    subgraph "AI Analysis System"
        B1[Pattern Recognition ML]
        B2[Five-Layer Mapper]
        B3[Spiritual Indicator Detector]
        B4[Confidence Calculator]
    end
    
    subgraph "Crisis Detection"
        C1[Real-time Monitor]
        C2[Risk Assessment Algorithm]
        C3[Emergency Response Trigger]
        C4[Professional Referral System]
    end
    
    subgraph "Treatment Planning"
        D1[Tier 1 Recommendation Engine]
        D2[Spiritual Diagnosis Processor]
        D3[Tier 2 Plan Generator]
        D4[Milestone Tracker]
    end
```

## 🔧 Feature-Specific Technical Components

> **Note**: For app-level technology stack information (React Native, Node.js, PostgreSQL, etc.), please refer to the main technical documentation. This section focuses on Feature 01-specific technical components.

### **Assessment Engine Components**
- **Progressive Disclosure Algorithm**: Custom adaptive questioning logic
- **Five-Layer Analysis Engine**: AI-powered Islamic health layer mapping
- **Crisis Detection System**: Real-time monitoring with Islamic context
- **Response Validation**: Islamic content-aware input validation
- **Session Management**: Assessment state and progress tracking

### **Spiritual Diagnosis Components**
- **Ruqya Session Manager**: Self-diagnosis session orchestration
- **Audio Integration**: Quranic recitation playback and management
- **Reaction Monitoring**: Real-time spiritual reaction tracking
- **Pattern Recognition**: AI analysis of spiritual illness indicators
- **Scholar Integration**: Complex case review workflow

### **AI/ML Specialized Models**
- **Islamic Mental Health Classifier**: Fine-tuned for Muslim context
- **Spiritual Indicator Detector**: Custom neural network for spiritual health
- **Crisis Risk Assessor**: Real-time emergency detection algorithm
- **Treatment Recommender**: Personalized Islamic healing recommendations
- **Cultural Context Processor**: Islamic content and guidance integration

## 📊 Data Architecture

### **Database Schema Design**

```sql
-- Core Assessment Tables
CREATE TABLE assessment_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    session_type VARCHAR(50) NOT NULL, -- 'initial' | 'spiritual' | 'follow_up'
    status VARCHAR(20) DEFAULT 'in_progress', -- 'in_progress' | 'completed' | 'abandoned'
    started_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    total_duration_minutes INTEGER,
    crisis_level VARCHAR(20) DEFAULT 'none', -- 'none' | 'low' | 'moderate' | 'high' | 'critical'
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Assessment Responses
CREATE TABLE assessment_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES assessment_sessions(id),
    question_id VARCHAR(100) NOT NULL,
    question_text TEXT NOT NULL,
    response_value JSONB NOT NULL, -- Flexible for different response types
    response_time_seconds INTEGER,
    layer_category VARCHAR(20), -- 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh'
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    created_at TIMESTAMP DEFAULT NOW()
);

-- Five-Layer Analysis Results
CREATE TABLE five_layer_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES assessment_sessions(id),
    layer_name VARCHAR(20) NOT NULL, -- 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh'
    impact_level VARCHAR(20), -- 'none' | 'mild' | 'moderate' | 'severe' | 'critical'
    confidence_level VARCHAR(20), -- 'low' | 'medium' | 'high'
    primary_symptoms JSONB DEFAULT '[]',
    secondary_symptoms JSONB DEFAULT '[]',
    ai_analysis JSONB DEFAULT '{}',
    requires_deeper_assessment BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Spiritual Diagnosis Sessions
CREATE TABLE spiritual_diagnosis_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assessment_session_id UUID REFERENCES assessment_sessions(id),
    spiritual_illness_type VARCHAR(50) NOT NULL, -- 'sihr' | 'ayn' | 'mass' | 'waswas'
    session_number INTEGER NOT NULL, -- 1-4
    status VARCHAR(20) DEFAULT 'pending', -- 'pending' | 'in_progress' | 'completed'
    reactions JSONB DEFAULT '{}', -- User-reported reactions
    intensity_level INTEGER, -- 1-10 scale
    duration_minutes INTEGER,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Treatment Plans
CREATE TABLE treatment_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    assessment_session_id UUID REFERENCES assessment_sessions(id),
    plan_tier INTEGER NOT NULL, -- 1 or 2
    treatment_order JSONB NOT NULL, -- Array of layer healing order
    estimated_timeline_weeks INTEGER,
    milestones JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'active', -- 'active' | 'paused' | 'completed'
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Crisis Events
CREATE TABLE crisis_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    session_id UUID REFERENCES assessment_sessions(id),
    crisis_level VARCHAR(20) NOT NULL,
    trigger_factors JSONB DEFAULT '[]',
    response_actions JSONB DEFAULT '[]',
    resolved_at TIMESTAMP,
    professional_contacted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### **Data Flow Architecture**

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Assessment API
    participant AI as AI Engine
    participant D as Database
    participant C as Crisis Service
    
    U->>F: Start Assessment
    F->>A: Initialize Session
    A->>D: Create assessment_session
    
    loop Progressive Questions
        F->>A: Submit Response
        A->>AI: Analyze Response
        AI->>A: Return Analysis + Next Question
        A->>D: Store Response + Analysis
        
        alt Crisis Detected
            A->>C: Trigger Crisis Protocol
            C->>U: Emergency Support
        end
    end
    
    A->>AI: Generate Five-Layer Analysis
    AI->>A: Return Complete Analysis
    A->>D: Store five_layer_analysis
    A->>F: Return Tier 1 Results
```

## 🤖 AI/ML Architecture

### **Machine Learning Pipeline**

```mermaid
graph TB
    subgraph "Data Preprocessing"
        A[Raw Assessment Responses] --> B[Text Normalization]
        B --> C[Feature Extraction]
        C --> D[Islamic Context Enrichment]
    end
    
    subgraph "Pattern Recognition Models"
        E[Mental Health Classifier] --> F[Symptom Severity Predictor]
        F --> G[Five-Layer Impact Mapper]
        G --> H[Spiritual Indicator Detector]
    end
    
    subgraph "Analysis Engine"
        I[Confidence Calculator] --> J[Crisis Risk Assessor]
        J --> K[Treatment Recommender]
        K --> L[Timeline Estimator]
    end
    
    D --> E
    H --> I
    L --> M[Final Analysis Output]
```

### **AI Model Specifications**

#### **Mental Health Pattern Recognition**
- **Model Type**: Fine-tuned GPT-4 with Islamic mental health training data
- **Input**: User responses, demographic data, Islamic practice level
- **Output**: Mental health pattern classification with confidence scores
- **Training Data**: 10,000+ anonymized Islamic mental health assessments
- **Accuracy Target**: >85% correlation with professional assessments

#### **Spiritual Illness Indicator Detection**
- **Model Type**: Custom neural network with Islamic spiritual health features
- **Input**: Spiritual practice responses, unexplained symptoms, energy patterns
- **Output**: Probability scores for each spiritual illness type
- **Training Data**: Traditional ruqya practitioner assessments and outcomes
- **Validation**: Scholar-verified spiritual assessment protocols

#### **Five-Layer Impact Mapping**
- **Model Type**: Multi-label classification with layer-specific features
- **Input**: All assessment responses with Islamic context
- **Output**: Impact severity and confidence for each of the five layers
- **Features**: Symptom patterns, spiritual practices, lifestyle factors
- **Optimization**: Balanced accuracy across all five layers

### **Crisis Detection Algorithm**

```python
def assess_crisis_level(responses, user_profile, session_context):
    """
    Real-time crisis detection algorithm
    """
    crisis_indicators = {
        'immediate_danger': 0,
        'self_harm_risk': 0,
        'spiritual_distress': 0,
        'social_isolation': 0,
        'functional_impairment': 0
    }
    
    # Analyze responses for crisis patterns
    for response in responses:
        crisis_indicators.update(
            analyze_response_for_crisis(response)
        )
    
    # Calculate weighted crisis score
    crisis_score = calculate_weighted_score(crisis_indicators)
    
    # Determine crisis level
    if crisis_score >= 0.9:
        return 'critical'
    elif crisis_score >= 0.7:
        return 'high'
    elif crisis_score >= 0.5:
        return 'moderate'
    elif crisis_score >= 0.3:
        return 'low'
    else:
        return 'none'
```

## 🔐 Security Architecture

### **Authentication & Authorization**

```mermaid
graph LR
    subgraph "Authentication Flow"
        A[User Login] --> B[Supabase Auth]
        B --> C[JWT Token]
        C --> D[Role-Based Access]
    end
    
    subgraph "Authorization Layers"
        E[API Gateway] --> F[Route Protection]
        F --> G[Resource Access Control]
        G --> H[Data Encryption]
    end
    
    D --> E
```

### **Data Protection Measures**

#### **Encryption Standards**
- **At Rest**: AES-256 encryption for all sensitive data
- **In Transit**: TLS 1.3 for all API communications
- **Database**: Transparent Data Encryption (TDE) enabled
- **Backups**: Encrypted backups with separate key management

#### **Privacy Controls**
- **Data Minimization**: Collect only necessary assessment data
- **Anonymization**: Remove PII from analytics and ML training data
- **User Consent**: Granular consent for different data usage types
- **Right to Deletion**: Complete data removal upon user request

#### **Islamic Privacy Considerations**
- **Spiritual Data Protection**: Extra security for spiritual assessment results
- **Scholar Confidentiality**: Secure communication channels for scholar consultations
- **Community Privacy**: Anonymous participation in community features
- **Family Privacy**: Protection of family history and genetic information

### **Security Monitoring**

```yaml
security_monitoring:
  real_time_alerts:
    - unauthorized_access_attempts
    - data_breach_indicators
    - unusual_api_usage_patterns
    - crisis_event_escalations
  
  audit_logging:
    - all_user_actions
    - data_access_patterns
    - system_configuration_changes
    - security_policy_violations
  
  compliance_checks:
    - hipaa_requirements
    - gdpr_compliance
    - islamic_privacy_standards
    - data_retention_policies
```

## 📱 Mobile Architecture

### **React Native App Structure**

```
src/
├── components/
│   ├── assessment/
│   │   ├── ProgressiveDisclosure/
│   │   ├── QuestionTypes/
│   │   ├── CrisisDetection/
│   │   └── ResultsVisualization/
│   ├── spiritual/
│   │   ├── SpiritualDiagnosis/
│   │   ├── RuqyaSession/
│   │   └── SpiritualProgress/
│   └── common/
│       ├── IslamicUI/
│       ├── AudioPlayer/
│       └── OfflineSupport/
├── screens/
│   ├── AssessmentFlow/
│   ├── SpiritualDiagnosis/
│   ├── TreatmentPlan/
│   └── CrisisSupport/
├── services/
│   ├── api/
│   ├── offline/
│   ├── audio/
│   └── notifications/
├── store/
│   ├── assessment/
│   ├── spiritual/
│   ├── treatment/
│   └── crisis/
└── utils/
    ├── islamic/
    ├── analytics/
    └── security/
```

### **State Management Architecture**

```typescript
// Redux Store Structure
interface RootState {
  auth: AuthState;
  assessment: {
    currentSession: AssessmentSession;
    responses: AssessmentResponse[];
    analysis: FiveLayerAnalysis;
    crisisLevel: CrisisLevel;
  };
  spiritual: {
    diagnosisSessions: SpiritualDiagnosisSession[];
    currentSession: SpiritualSession;
    reactions: SpiritualReaction[];
  };
  treatment: {
    currentPlan: TreatmentPlan;
    milestones: Milestone[];
    progress: ProgressData;
  };
  offline: {
    queuedActions: OfflineAction[];
    syncStatus: SyncStatus;
  };
}
```

### **Offline Support Strategy**

```mermaid
graph TB
    subgraph "Offline Capabilities"
        A[Assessment Questions Cache] --> B[Response Queue]
        B --> C[Audio Content Cache]
        C --> D[Educational Content Cache]
    end
    
    subgraph "Sync Strategy"
        E[Background Sync] --> F[Conflict Resolution]
        F --> G[Data Validation]
        G --> H[Server Reconciliation]
    end
    
    D --> E
```

## 🔄 API Architecture

### **RESTful API Design**

```yaml
# Assessment Endpoints
POST /api/v1/assessment/sessions
  description: Initialize new assessment session
  body: { user_id, session_type, metadata }
  response: { session_id, first_question }

POST /api/v1/assessment/sessions/{session_id}/responses
  description: Submit assessment response
  body: { question_id, response_value, response_time }
  response: { next_question, analysis, crisis_level }

GET /api/v1/assessment/sessions/{session_id}/results
  description: Get assessment results
  response: { five_layer_analysis, tier_1_recommendations }

# Spiritual Diagnosis Endpoints
POST /api/v1/spiritual/sessions
  description: Start spiritual diagnosis session
  body: { assessment_session_id, spiritual_illness_type }
  response: { session_id, instructions, audio_urls }

POST /api/v1/spiritual/sessions/{session_id}/reactions
  description: Record spiritual diagnosis reactions
  body: { reactions, intensity_level, duration }
  response: { analysis, next_steps }

# Treatment Plan Endpoints
GET /api/v1/treatment/plans/{user_id}
  description: Get user's treatment plan
  response: { plan_tier, treatment_order, milestones }

POST /api/v1/treatment/plans/{plan_id}/progress
  description: Update treatment progress
  body: { milestone_id, completion_status, notes }
  response: { updated_plan, next_milestones }
```

### **WebSocket Events**

```typescript
// Real-time Events
interface WebSocketEvents {
  // Crisis Detection
  'crisis:detected': {
    level: CrisisLevel;
    triggers: string[];
    resources: CrisisResource[];
  };
  
  // Assessment Progress
  'assessment:progress': {
    session_id: string;
    completion_percentage: number;
    current_layer: LayerType;
  };
  
  // Spiritual Diagnosis
  'spiritual:reaction': {
    session_id: string;
    intensity: number;
    timestamp: Date;
  };
  
  // Treatment Updates
  'treatment:milestone': {
    plan_id: string;
    milestone: Milestone;
    achievement_date: Date;
  };
}
```

## 📊 Analytics & Monitoring

### **Performance Monitoring**

```yaml
performance_metrics:
  api_response_times:
    - assessment_question_load: <2s
    - ai_analysis_completion: <10s
    - results_generation: <5s
    - crisis_detection: <1s
  
  mobile_app_metrics:
    - app_startup_time: <3s
    - question_transition: <1s
    - offline_sync_time: <30s
    - audio_loading: <5s
  
  user_experience:
    - assessment_completion_rate: >85%
    - session_abandonment: <15%
    - crisis_response_time: <30s
    - user_satisfaction: >4.5/5
```

### **Business Intelligence**

```mermaid
graph TB
    subgraph "Data Collection"
        A[User Interactions] --> B[Assessment Responses]
        B --> C[Treatment Progress]
        C --> D[Crisis Events]
    end
    
    subgraph "Analytics Pipeline"
        E[Data Warehouse] --> F[ETL Processing]
        F --> G[ML Feature Engineering]
        G --> H[Predictive Models]
    end
    
    subgraph "Insights Dashboard"
        I[User Journey Analytics] --> J[Treatment Effectiveness]
        J --> K[Crisis Prevention Metrics]
        K --> L[Islamic Content Engagement]
    end
    
    D --> E
    H --> I
```

## 🚀 Deployment Architecture

### **Feature-Specific Deployment Considerations**

> **Note**: For general infrastructure and CI/CD pipeline information, please refer to the main deployment documentation. This section covers Feature 01-specific deployment requirements.

#### **Assessment Engine Deployment**
- **AI Model Deployment**: Custom models for Islamic mental health analysis
- **Audio Content CDN**: Quranic recitation files with global distribution
- **Crisis Response Infrastructure**: High-availability emergency detection system
- **Scholar Integration**: Secure communication channels for content review

#### **Performance Requirements**
- **Assessment Response Time**: <2 seconds for question loading
- **AI Analysis Speed**: <10 seconds for five-layer analysis
- **Crisis Detection**: <1 second for emergency identification
- **Audio Streaming**: <5 seconds for Quranic recitation loading

#### **Scaling Considerations**
- **Concurrent Assessments**: Support for thousands of simultaneous sessions
- **AI Model Scaling**: Auto-scaling for analysis workloads
- **Crisis Response Scaling**: Dedicated resources for emergency situations
- **Audio Content Delivery**: Global CDN for Islamic content

## 🔍 Feature-Specific Testing Requirements

> **Note**: For comprehensive testing strategy and app-level testing standards, please refer to `05_Testing_Strategy.md`. This section highlights Feature 01-specific testing considerations.

### **Critical Testing Areas**
- **Assessment Algorithm Accuracy**: Progressive disclosure logic validation
- **Crisis Detection Reliability**: 98%+ accuracy for emergency situations
- **Islamic Content Authenticity**: 100% scholar verification requirement
- **Spiritual Diagnosis Precision**: Correlation with traditional ruqya assessments
- **Cultural Sensitivity**: Appropriate Islamic context throughout

### **Performance Testing Focus**
- **Assessment Flow Performance**: Sub-2-second question transitions
- **AI Analysis Speed**: Real-time five-layer analysis
- **Crisis Response Time**: Immediate emergency detection
- **Audio Streaming**: Smooth Quranic recitation playback
- **Offline Functionality**: Core features without internet

## 📚 Feature Documentation Standards

> **Note**: For general documentation standards, please refer to the main technical documentation. This section covers Feature 01-specific documentation requirements.

### **Islamic Context Documentation**
- **Spiritual Algorithm Explanations**: Clear reasoning for Islamic health analysis
- **Scholar Verification Process**: Documentation of content review workflow
- **Cultural Sensitivity Guidelines**: Appropriate Islamic context integration
- **Crisis Response Protocols**: Islamic-appropriate emergency procedures
- **Audio Content Sources**: Verification of Quranic recitation authenticity

---

This technical architecture document provides a comprehensive foundation for implementing Feature 01. The architecture emphasizes scalability, security, and cultural sensitivity while maintaining high performance and reliability standards appropriate for a healthcare application serving the global Muslim community.