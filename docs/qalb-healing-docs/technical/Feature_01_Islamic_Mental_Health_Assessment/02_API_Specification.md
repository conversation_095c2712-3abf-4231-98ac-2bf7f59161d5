# Feature 01: Islamic Mental Health Assessment - API Specification

## 📋 Document Overview

**Feature**: Islamic Mental Health Assessment v7  
**Document Type**: API Specification  
**Version**: 1.0  
**Date**: January 2025  
**Status**: Draft  

## 🌐 API Overview

### **Base Configuration**
- **Base URL**: `https://api.qalbhealing.com/v1`
- **Authentication**: Bearer JWT tokens via Supabase Auth
- **Content Type**: `application/json`
- **Rate Limiting**: 100 requests/minute per user
- **API Version**: v1 (header: `X-API-Version: v1`)

### **Common Headers**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-API-Version: v1
X-Request-ID: <unique_request_id>
Accept-Language: en-US,ar-SA
```

### **Standard Response Format**
```json
{
  "status": "success" | "fail" | "error",
  "data": {},
  "meta": {
    "timestamp": "2025-01-27T10:30:00Z",
    "request_id": "req_123456789",
    "islamicContext": {
      "blessing": "الحمد لله",
      "guidance": "May <PERSON> grant you healing and peace."
    }
  },
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {},
    "islamicGuidance": "Islamic context for the error"
  }
}
```

## 📊 Assessment Session Management

### **Initialize Assessment Session**

```http
POST /assessment/sessions
```

**Description**: Creates a new assessment session for the user

**Request Body**:
```json
{
  "sessionType": "initial" | "spiritual" | "follow_up",
  "userProfile": {
    "ageRange": "18-25" | "26-35" | "36-45" | "46-55" | "55+",
    "gender": "male" | "female" | "prefer_not_to_say",
    "islamicPracticeLevel": "beginner" | "moderate" | "advanced",
    "previousAssessments": boolean,
    "preferredLanguage": "en" | "ar"
  },
  "metadata": {
    "deviceType": "mobile" | "web",
    "timezone": "UTC+3",
    "culturalBackground": string
  }
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "sessionId": "sess_abc123def456",
    "sessionType": "initial",
    "estimatedDuration": 12,
    "firstQuestion": {
      "questionId": "q_001",
      "questionText": "How would you rate your overall mental well-being in the past two weeks?",
      "questionType": "scale",
      "options": {
        "min": 1,
        "max": 10,
        "labels": {
          "1": "Very Poor",
          "10": "Excellent"
        }
      },
      "layerCategory": "general",
      "isRequired": true,
      "islamicContext": {
        "guidance": "Rate honestly, as Allah knows what is in your heart.",
        "verse": "And whoever relies upon Allah - then He is sufficient for him."
      }
    },
    "progressInfo": {
      "currentStep": 1,
      "totalEstimatedSteps": 25,
      "completionPercentage": 4
    }
  },
  "meta": {
    "timestamp": "2025-01-27T10:30:00Z",
    "islamicContext": {
      "blessing": "بسم الله الرحمن الرحيم",
      "guidance": "Begin your healing journey with Allah's blessing."
    }
  }
}
```

### **Submit Assessment Response**

```http
POST /assessment/sessions/{sessionId}/responses
```

**Description**: Submits a response to an assessment question and receives the next question

**Path Parameters**:
- `sessionId` (string, required): The assessment session ID

**Request Body**:
```json
{
  "questionId": "q_001",
  "responseValue": {
    "type": "scale" | "multiple_choice" | "text" | "boolean" | "multi_select",
    "value": 7,
    "selectedOptions": ["option1", "option2"],
    "textValue": "I feel anxious most days",
    "confidence": 0.8
  },
  "responseTime": 15,
  "metadata": {
    "hesitationTime": 3,
    "changedAnswer": false,
    "skipRequested": false
  }
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "responseRecorded": true,
    "nextQuestion": {
      "questionId": "q_002",
      "questionText": "In the past month, how often have you felt that your prayers lack concentration (khushu)?",
      "questionType": "frequency",
      "options": [
        { "value": "never", "label": "Never" },
        { "value": "rarely", "label": "Rarely (1-2 times)" },
        { "value": "sometimes", "label": "Sometimes (3-10 times)" },
        { "value": "often", "label": "Often (11-20 times)" },
        { "value": "always", "label": "Almost always (21+ times)" }
      ],
      "layerCategory": "qalb",
      "isRequired": true,
      "islamicContext": {
        "guidance": "Honest reflection helps in spiritual healing.",
        "hadith": "The Prophet (ﷺ) said: 'Prayer is the pillar of religion.'"
      }
    },
    "progressInfo": {
      "currentStep": 2,
      "totalEstimatedSteps": 24,
      "completionPercentage": 8
    },
    "crisisDetection": {
      "level": "none" | "low" | "moderate" | "high" | "critical",
      "triggers": [],
      "immediateSupport": null
    },
    "adaptiveLogic": {
      "layersToExplore": ["qalb", "nafs", "aql"],
      "skipReasons": ["low_relevance", "time_optimization"],
      "deepDiveAreas": ["spiritual_practices", "emotional_regulation"]
    }
  }
}
```

### **Get Assessment Progress**

```http
GET /assessment/sessions/{sessionId}/progress
```

**Description**: Retrieves current progress and status of an assessment session

**Response**:
```json
{
  "status": "success",
  "data": {
    "sessionId": "sess_abc123def456",
    "status": "in_progress" | "completed" | "abandoned",
    "progressInfo": {
      "currentStep": 15,
      "totalSteps": 23,
      "completionPercentage": 65,
      "estimatedTimeRemaining": 4
    },
    "layerProgress": {
      "jism": { "questionsAnswered": 3, "confidence": 0.85 },
      "nafs": { "questionsAnswered": 4, "confidence": 0.92 },
      "aql": { "questionsAnswered": 5, "confidence": 0.78 },
      "qalb": { "questionsAnswered": 2, "confidence": 0.65 },
      "ruh": { "questionsAnswered": 1, "confidence": 0.45 }
    },
    "crisisLevel": "none",
    "canPause": true,
    "canResume": true
  }
}
```

### **Pause/Resume Assessment Session**

```http
PATCH /assessment/sessions/{sessionId}/status
```

**Request Body**:
```json
{
  "action": "pause" | "resume" | "abandon",
  "reason": "time_constraint" | "technical_issue" | "user_choice",
  "metadata": {
    "pauseLocation": "question_15",
    "deviceChange": false
  }
}
```

## 📊 Assessment Results & Analysis

### **Get Assessment Results**

```http
GET /assessment/sessions/{sessionId}/results
```

**Description**: Retrieves comprehensive assessment results with Five-Layer analysis

**Response**:
```json
{
  "status": "success",
  "data": {
    "sessionId": "sess_abc123def456",
    "assessmentType": "initial",
    "completedAt": "2025-01-27T11:15:00Z",
    "totalDuration": 11,
    "fiveLayerAnalysis": {
      "jism": {
        "impactLevel": "moderate",
        "confidenceLevel": "high",
        "primarySymptoms": [
          "sleep_disturbances",
          "fatigue",
          "headaches"
        ],
        "secondarySymptoms": [
          "muscle_tension",
          "digestive_issues"
        ],
        "aiAnalysis": {
          "patternRecognition": "stress_related_physical_symptoms",
          "severity": 6.5,
          "recommendations": ["prophetic_sleep_hygiene", "islamic_breathing"]
        }
      },
      "nafs": {
        "impactLevel": "severe",
        "confidenceLevel": "high",
        "primarySymptoms": [
          "anxiety",
          "irritability",
          "emotional_instability"
        ],
        "aiAnalysis": {
          "patternRecognition": "emotional_dysregulation",
          "severity": 8.2,
          "recommendations": ["dhikr_therapy", "gratitude_practices"]
        }
      },
      "aql": {
        "impactLevel": "moderate",
        "confidenceLevel": "medium",
        "primarySymptoms": [
          "racing_thoughts",
          "concentration_difficulties",
          "memory_issues"
        ],
        "aiAnalysis": {
          "patternRecognition": "cognitive_overload",
          "severity": 7.1,
          "recommendations": ["islamic_mindfulness", "quranic_meditation"]
        }
      },
      "qalb": {
        "impactLevel": "unknown",
        "confidenceLevel": "low",
        "requiresDeeperAssessment": true,
        "spiritualIndicators": [
          "prayer_concentration_issues",
          "spiritual_disconnection",
          "reduced_islamic_motivation"
        ],
        "preliminaryAnalysis": {
          "possibleSpiritualFactors": true,
          "recommendedNextStep": "spiritual_diagnosis"
        }
      },
      "ruh": {
        "impactLevel": "unknown",
        "confidenceLevel": "low",
        "requiresDeeperAssessment": true,
        "spiritualIndicators": [
          "unexplained_energy_drain",
          "spiritual_resistance",
          "unusual_spiritual_reactions"
        ],
        "preliminaryAnalysis": {
          "possibleSpiritualIllness": true,
          "recommendedNextStep": "spiritual_diagnosis"
        }
      }
    },
    "tier1Recommendations": {
      "immediate_actions": {
        "jism": [
          {
            "category": "sleep_hygiene",
            "title": "Prophetic Sleep Practices",
            "description": "Follow Sunnah sleep practices for better rest",
            "instructions": [
              "Sleep on your right side",
              "Recite Ayat al-Kursi before sleep",
              "Make dua: 'Bismika Allahumma amutu wa ahya'"
            ],
            "expectedBenefit": "Improved sleep quality and spiritual protection",
            "timeframe": "1-2 weeks"
          }
        ],
        "nafs": [
          {
            "category": "emotional_regulation",
            "title": "Islamic Anxiety Management",
            "description": "Use Islamic techniques for emotional stability",
            "instructions": [
              "Practice dhikr during anxiety: 'La hawla wa la quwwata illa billah'",
              "Perform wudu when feeling overwhelmed",
              "Recite Surah Al-Sharh for comfort"
            ],
            "expectedBenefit": "Reduced anxiety and emotional stability",
            "timeframe": "Immediate to 1 week"
          }
        ],
        "aql": [
          {
            "category": "mental_clarity",
            "title": "Quranic Meditation for Focus",
            "description": "Use Quranic verses for mental clarity",
            "instructions": [
              "Recite Ayat al-Kursi slowly with contemplation",
              "Practice 99 Names of Allah meditation",
              "Use Istighfar for mental cleansing"
            ],
            "expectedBenefit": "Improved concentration and mental peace",
            "timeframe": "1-3 weeks"
          }
        ]
      },
      "limitations": {
        "scope": "symptom_management",
        "effectiveness": "temporary_relief",
        "note": "These recommendations address confirmed symptoms but may not target root causes if spiritual factors are involved."
      }
    },
    "spiritualDiagnosisProposal": {
      "reasoning": "Your responses indicate potential spiritual factors affecting your Qalb (Heart) and Ruh (Soul) layers. Spiritual illnesses can be root causes that, when addressed, often resolve symptoms in all other layers.",
      "benefits": [
        "Identify root spiritual causes",
        "Receive targeted spiritual healing protocols",
        "Potentially reduce overall healing timeline by 40-60%",
        "Address core issues rather than just symptoms"
      ],
      "timeInvestment": "60-80 minutes total (4 sessions of 20 minutes each)",
      "flexibility": "Self-paced, can be completed over several days",
      "nextStep": "Module 1: Spiritual Root Cause Diagnosis"
    },
    "crisisAssessment": {
      "level": "none",
      "riskFactors": [],
      "protectiveFactors": [
        "strong_islamic_faith",
        "family_support",
        "regular_prayer"
      ],
      "monitoringRecommended": false
    }
  },
  "meta": {
    "timestamp": "2025-01-27T11:15:00Z",
    "islamicContext": {
      "blessing": "الحمد لله الذي هدانا لهذا",
      "guidance": "All praise to Allah who guided us to this understanding. Your healing journey begins with this knowledge."
    }
  }
}
```

## 🕌 Spiritual Diagnosis API

### **Initialize Spiritual Diagnosis Session**

```http
POST /spiritual/sessions
```

**Description**: Starts a spiritual diagnosis session for a specific spiritual illness type

**Request Body**:
```json
{
  "assessmentSessionId": "sess_abc123def456",
  "spiritualIllnessType": "sihr" | "ayn" | "mass" | "waswas",
  "sessionNumber": 1,
  "preparationCompleted": {
    "wudu": true,
    "privateSpace": true,
    "materials": ["water_glass"],
    "spiritualReadiness": true
  },
  "userPreferences": {
    "audioLanguage": "arabic" | "english",
    "recitationSpeed": "slow" | "medium" | "fast",
    "guidanceLevel": "beginner" | "intermediate" | "advanced"
  }
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "spiritualSessionId": "spirit_xyz789abc123",
    "spiritualIllnessType": "sihr",
    "sessionNumber": 1,
    "estimatedDuration": 20,
    "instructions": {
      "preparation": [
        "Ensure you are in a private, quiet space",
        "Have a glass of water ready",
        "Maintain your wudu throughout the session",
        "Set intention (niyyah) for healing"
      ],
      "process": [
        "Listen to the Quranic recitation",
        "Apply water to affected areas as guided",
        "Monitor your reactions carefully",
        "Record any sensations or changes"
      ],
      "safety": [
        "Stop immediately if you feel severe discomfort",
        "Contact support if you experience intense reactions",
        "Remember this is a diagnostic process, not treatment"
      ]
    },
    "audioContent": {
      "mainRecitation": {
        "url": "https://cdn.qalbhealing.com/audio/sihr/surah-falaq-diagnosis.mp3",
        "duration": 180,
        "reciter": "Sheikh Abdullah Al-Matrood",
        "language": "arabic",
        "translation_url": "https://cdn.qalbhealing.com/audio/sihr/surah-falaq-translation.mp3"
      },
      "preparationGuidance": {
        "url": "https://cdn.qalbhealing.com/audio/guidance/sihr-preparation.mp3",
        "duration": 120,
        "language": "english"
      }
    },
    "reactionMonitoring": {
      "categories": [
        "physical_sensations",
        "emotional_responses",
        "spiritual_reactions",
        "energy_changes"
      ],
      "intensityScale": {
        "min": 1,
        "max": 10,
        "descriptions": {
          "1-2": "Very mild or barely noticeable",
          "3-4": "Mild but clearly present",
          "5-6": "Moderate and noticeable",
          "7-8": "Strong and significant",
          "9-10": "Very intense or overwhelming"
        }
      }
    }
  },
  "meta": {
    "islamicContext": {
      "blessing": "أعوذ بالله من الشيطان الرجيم",
      "guidance": "Seek Allah's protection as you begin this spiritual diagnosis."
    }
  }
}
```

### **Record Spiritual Diagnosis Reactions**

```http
POST /spiritual/sessions/{spiritualSessionId}/reactions
```

**Description**: Records user reactions during spiritual diagnosis session

**Request Body**:
```json
{
  "sessionPhase": "preparation" | "main_recitation" | "post_recitation",
  "timestamp": "2025-01-27T11:30:00Z",
  "reactions": {
    "physical": {
      "sensations": [
        {
          "type": "tingling",
          "location": "hands",
          "intensity": 6,
          "duration": 30,
          "description": "Warm tingling sensation in palms"
        },
        {
          "type": "pressure",
          "location": "chest",
          "intensity": 4,
          "duration": 60,
          "description": "Mild pressure in chest area"
        }
      ],
      "movements": [
        {
          "type": "involuntary_movement",
          "bodyPart": "hands",
          "intensity": 3,
          "description": "Slight trembling in hands"
        }
      ]
    },
    "emotional": {
      "feelings": [
        {
          "type": "anxiety",
          "intensity": 5,
          "duration": 45,
          "description": "Mild anxiety during recitation"
        },
        {
          "type": "peace",
          "intensity": 7,
          "duration": 120,
          "description": "Sense of peace after recitation"
        }
      ]
    },
    "spiritual": {
      "experiences": [
        {
          "type": "resistance",
          "intensity": 4,
          "description": "Felt resistance to continuing"
        },
        {
          "type": "connection",
          "intensity": 8,
          "description": "Strong connection to Allah during dua"
        }
      ]
    },
    "energy": {
      "changes": [
        {
          "type": "drain",
          "intensity": 3,
          "description": "Slight energy drain during middle of session"
        },
        {
          "type": "restoration",
          "intensity": 6,
          "description": "Energy restoration at end of session"
        }
      ]
    }
  },
  "overallIntensity": 5,
  "sessionCompleted": true,
  "additionalNotes": "Felt more peaceful towards the end. Some initial resistance but overall positive experience."
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "reactionsRecorded": true,
    "sessionAnalysis": {
      "spiritualIllnessType": "sihr",
      "reactionPattern": "moderate_positive_indicators",
      "confidenceLevel": 0.75,
      "significantReactions": [
        "physical_sensations_during_recitation",
        "initial_resistance_followed_by_peace",
        "energy_changes_pattern"
      ],
      "interpretation": {
        "likelihood": "moderate",
        "reasoning": "Physical sensations and energy changes during Quranic recitation suggest possible spiritual interference. The pattern of initial resistance followed by peace is consistent with sihr indicators.",
        "nextSteps": "Continue with remaining spiritual diagnosis sessions for comprehensive assessment."
      }
    },
    "nextSession": {
      "recommendedType": "ayn",
      "estimatedWaitTime": "24_hours",
      "preparation": "Same preparation as previous session"
    },
    "supportResources": {
      "normalReactions": "https://knowledge.qalbhealing.com/spiritual/normal-reactions",
      "intensiveSupport": "https://support.qalbhealing.com/spiritual/intensive",
      "scholarConsultation": "Available for complex reactions"
    }
  }
}
```

### **Get Spiritual Diagnosis Results**

```http
GET /spiritual/sessions/{assessmentSessionId}/complete-results
```

**Description**: Retrieves complete spiritual diagnosis results after all sessions

**Response**:
```json
{
  "status": "success",
  "data": {
    "assessmentSessionId": "sess_abc123def456",
    "completedSessions": 4,
    "completionDate": "2025-01-27T15:30:00Z",
    "spiritualDiagnosisResults": {
      "sihr": {
        "likelihood": "high",
        "confidenceLevel": 0.85,
        "severity": "moderate",
        "reactionSummary": {
          "physicalReactions": 8,
          "emotionalReactions": 6,
          "spiritualReactions": 9,
          "energyChanges": 7
        },
        "keyIndicators": [
          "strong_physical_reactions_to_sihr_verses",
          "energy_drain_during_recitation",
          "resistance_to_specific_verses",
          "improvement_after_session_completion"
        ]
      },
      "ayn": {
        "likelihood": "low",
        "confidenceLevel": 0.65,
        "severity": "mild",
        "reactionSummary": {
          "physicalReactions": 2,
          "emotionalReactions": 1,
          "spiritualReactions": 3,
          "energyChanges": 2
        }
      },
      "mass": {
        "likelihood": "none",
        "confidenceLevel": 0.90,
        "severity": "none",
        "reactionSummary": {
          "physicalReactions": 0,
          "emotionalReactions": 0,
          "spiritualReactions": 1,
          "energyChanges": 0
        }
      },
      "waswas": {
        "likelihood": "moderate",
        "confidenceLevel": 0.70,
        "severity": "mild",
        "reactionSummary": {
          "physicalReactions": 3,
          "emotionalReactions": 5,
          "spiritualReactions": 4,
          "energyChanges": 3
        }
      }
    },
    "overallAssessment": {
      "primarySpiritualIllness": "sihr",
      "secondaryFactors": ["waswas"],
      "treatmentPriority": ["sihr_treatment", "waswas_management"],
      "estimatedTreatmentDuration": "3-6 months",
      "urgencyLevel": "moderate"
    },
    "scholarVerification": {
      "reviewStatus": "pending",
      "estimatedReviewTime": "24-48 hours",
      "scholarNotes": null
    }
  }
}
```

## 🎯 Treatment Plan API

### **Generate Complete Treatment Plan**

```http
POST /treatment/plans
```

**Description**: Generates Tier 2 complete treatment plan based on assessment and spiritual diagnosis

**Request Body**:
```json
{
  "assessmentSessionId": "sess_abc123def456",
  "spiritualDiagnosisResults": {
    "primarySpiritualIllness": "sihr",
    "secondaryFactors": ["waswas"],
    "severity": "moderate"
  },
  "userPreferences": {
    "treatmentIntensity": "moderate",
    "timeAvailability": "30_minutes_daily",
    "preferredPractices": ["quran", "dhikr", "dua"],
    "avoidances": ["complex_rituals"]
  }
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "treatmentPlanId": "plan_def456ghi789",
    "planTier": 2,
    "generatedAt": "2025-01-27T16:00:00Z",
    "treatmentStrategy": {
      "approach": "inner_to_outer_healing",
      "totalEstimatedDuration": "4-6 months",
      "phases": 3
    },
    "fiveLayerTreatmentOrder": [
      {
        "layer": "ruh",
        "priority": 1,
        "phase": 1,
        "focus": "spiritual_illness_elimination",
        "estimatedDuration": "6-8 weeks",
        "protocols": [
          {
            "name": "Sihr Elimination Protocol",
            "type": "ruqya_treatment",
            "frequency": "daily",
            "duration": "20 minutes",
            "instructions": [
              "Recite Surah Al-Falaq 7 times",
              "Recite Surah An-Nas 7 times",
              "Apply blessed water to affected areas",
              "Maintain consistent timing"
            ],
            "expectedOutcomes": [
              "Reduction in spiritual illness symptoms",
              "Increased spiritual energy",
              "Improved prayer concentration"
            ]
          }
        ],
        "milestones": [
          {
            "week": 2,
            "target": "Initial symptom reduction",
            "indicators": ["less_spiritual_resistance", "improved_energy"]
          },
          {
            "week": 4,
            "target": "Significant improvement",
            "indicators": ["consistent_prayer_focus", "spiritual_peace"]
          },
          {
            "week": 6,
            "target": "Spiritual illness elimination",
            "indicators": ["no_spiritual_symptoms", "strong_spiritual_energy"]
          }
        ]
      },
      {
        "layer": "qalb",
        "priority": 2,
        "phase": 2,
        "focus": "spiritual_connection_restoration",
        "estimatedDuration": "4-6 weeks",
        "protocols": [
          {
            "name": "Heart Purification Protocol",
            "type": "spiritual_development",
            "frequency": "daily",
            "duration": "15 minutes",
            "instructions": [
              "Istighfar 100 times daily",
              "Recite 99 Names of Allah",
              "Practice gratitude reflection",
              "Maintain night prayers"
            ]
          }
        ]
      },
      {
        "layer": "aql",
        "priority": 3,
        "phase": 2,
        "focus": "mental_clarity_enhancement",
        "estimatedDuration": "3-4 weeks",
        "protocols": [
          {
            "name": "Enhanced Cognitive Protocol",
            "type": "islamic_cognitive_therapy",
            "frequency": "daily",
            "duration": "10 minutes",
            "instructions": [
              "Continue Tier 1 mental practices",
              "Add spiritual energy enhancement",
              "Practice Quranic meditation",
              "Use dhikr for focus"
            ]
          }
        ]
      },
      {
        "layer": "nafs",
        "priority": 4,
        "phase": 3,
        "focus": "emotional_mastery",
        "estimatedDuration": "3-4 weeks",
        "protocols": [
          {
            "name": "Nafs Purification Protocol",
            "type": "character_development",
            "frequency": "daily",
            "duration": "15 minutes",
            "instructions": [
              "Practice patience exercises",
              "Implement gratitude practices",
              "Work on anger management",
              "Develop spiritual resilience"
            ]
          }
        ]
      },
      {
        "layer": "jism",
        "priority": 5,
        "phase": 3,
        "focus": "physical_wellness_completion",
        "estimatedDuration": "2-3 weeks",
        "protocols": [
          {
            "name": "Holistic Physical Wellness",
            "type": "prophetic_medicine",
            "frequency": "daily",
            "duration": "varies",
            "instructions": [
              "Continue Tier 1 physical practices",
              "Add spiritual energy support",
              "Implement prophetic health practices",
              "Maintain physical spiritual practices"
            ]
          }
        ]
      }
    ],
    "overallMilestones": [
      {
        "phase": 1,
        "timeframe": "6-8 weeks",
        "target": "Spiritual Foundation Established",
        "indicators": [
          "Spiritual illness symptoms eliminated",
          "Strong spiritual energy restored",
          "Consistent Islamic practice joy"
        ]
      },
      {
        "phase": 2,
        "timeframe": "10-14 weeks",
        "target": "Mental and Emotional Healing",
        "indicators": [
          "Mental clarity significantly improved",
          "Emotional stability achieved",
          "Strong Allah connection established"
        ]
      },
      {
        "phase": 3,
        "timeframe": "16-20 weeks",
        "target": "Complete Five-Layer Harmony",
        "indicators": [
          "All layers functioning optimally",
          "Sustainable healing practices established",
          "Resilience against future challenges"
        ]
      }
    ],
    "successMetrics": {
      "spiritualHealing": "85% symptom reduction expected",
      "mentalImprovement": "70% faster than symptom-only approach",
      "emotionalStability": "90% improvement in regulation",
      "physicalWellness": "Natural resolution through spiritual healing",
      "overallSuccess": "85% complete healing rate"
    }
  }
}
```

### **Update Treatment Progress**

```http
POST /treatment/plans/{planId}/progress
```

**Description**: Records progress on treatment plan milestones

**Request Body**:
```json
{
  "milestoneId": "milestone_week2_ruh",
  "completionStatus": "completed" | "in_progress" | "not_started" | "skipped",
  "progressData": {
    "layer": "ruh",
    "week": 2,
    "practiceAdherence": 0.85,
    "symptomImprovement": 0.70,
    "userRating": 8,
    "specificOutcomes": [
      {
        "indicator": "less_spiritual_resistance",
        "achieved": true,
        "notes": "Much easier to focus during prayers"
      },
      {
        "indicator": "improved_energy",
        "achieved": true,
        "notes": "Feeling more energetic throughout the day"
      }
    ]
  },
  "userFeedback": {
    "overallSatisfaction": 9,
    "difficultyLevel": 6,
    "timeManagement": 8,
    "effectivenessRating": 9,
    "notes": "The ruqya protocol is working well. I feel much more peaceful and my prayers are more focused."
  },
  "adjustmentRequests": {
    "scheduleChanges": false,
    "intensityChanges": false,
    "protocolModifications": false,
    "additionalSupport": false
  }
}
```

## 🚨 Crisis Detection & Support API

### **Crisis Event Reporting**

```http
POST /crisis/events
```

**Description**: Reports a crisis event detected during assessment or treatment

**Request Body**:
```json
{
  "sessionId": "sess_abc123def456",
  "crisisLevel": "low" | "moderate" | "high" | "critical",
  "triggerFactors": [
    "suicidal_ideation",
    "self_harm_risk",
    "severe_spiritual_distress",
    "psychotic_symptoms"
  ],
  "userLocation": {
    "country": "Saudi Arabia",
    "timezone": "UTC+3",
    "emergencyContacts": ["family_member", "friend"]
  },
  "immediateNeeds": [
    "professional_intervention",
    "spiritual_support",
    "family_notification"
  ]
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "crisisEventId": "crisis_urgent123",
    "responseLevel": "immediate",
    "supportResources": {
      "emergency": {
        "hotline": "+966-920-033-707",
        "text": "Text 'HELP' to 741741",
        "online": "https://crisis.qalbhealing.com/immediate"
      },
      "professional": {
        "islamicCounselors": [
          {
            "name": "Dr. Fatima Al-Zahra",
            "specialization": "Islamic Psychology",
            "availability": "24/7",
            "contact": "<EMAIL>"
          }
        ],
        "mentalHealthProfessionals": [
          {
            "service": "National Mental Health Hotline",
            "number": "+966-920-033-707",
            "availability": "24/7"
          }
        ]
      },
      "spiritual": {
        "scholars": [
          {
            "name": "Sheikh Abdullah Al-Mahmoud",
            "specialization": "Spiritual Healing",
            "availability": "Emergency consultation available",
            "contact": "<EMAIL>"
          }
        ],
        "emergencyRuqya": {
          "audio": "https://cdn.qalbhealing.com/emergency/protection-verses.mp3",
          "instructions": "Recite immediately for spiritual protection"
        }
      }
    },
    "followUpProtocol": {
      "immediateCheckIn": "1 hour",
      "shortTermFollowUp": "24 hours",
      "professionalReferral": "Required within 48 hours",
      "familyNotification": "If user consents"
    }
  }
}
```

## 📊 Analytics & Reporting API

### **Assessment Analytics**

```http
GET /analytics/assessments/{sessionId}
```

**Description**: Retrieves detailed analytics for an assessment session

**Response**:
```json
{
  "status": "success",
  "data": {
    "sessionAnalytics": {
      "completionMetrics": {
        "totalQuestions": 23,
        "questionsAnswered": 23,
        "completionRate": 1.0,
        "totalDuration": 11.5,
        "averageResponseTime": 30
      },
      "layerDistribution": {
        "jism": { "questions": 5, "avgConfidence": 0.85 },
        "nafs": { "questions": 6, "avgConfidence": 0.92 },
        "aql": { "questions": 5, "avgConfidence": 0.78 },
        "qalb": { "questions": 4, "avgConfidence": 0.65 },
        "ruh": { "questions": 3, "avgConfidence": 0.45 }
      },
      "adaptiveLogicEfficiency": {
        "questionsSkipped": 7,
        "timeSaved": 3.5,
        "accuracyMaintained": 0.89
      },
      "userEngagement": {
        "hesitationPatterns": ["spiritual_questions", "personal_history"],
        "confidencePatterns": ["high_on_symptoms", "lower_on_spiritual"],
        "responseConsistency": 0.87
      }
    }
  }
}
```

## 🔒 Error Handling

### **Standard Error Codes**

```json
{
  "ASSESSMENT_001": "Assessment session not found",
  "ASSESSMENT_002": "Assessment session already completed",
  "ASSESSMENT_003": "Invalid question response format",
  "ASSESSMENT_004": "Assessment session expired",
  "ASSESSMENT_005": "Crisis intervention required",
  
  "SPIRITUAL_001": "Spiritual diagnosis session not found",
  "SPIRITUAL_002": "Spiritual preparation requirements not met",
  "SPIRITUAL_003": "Invalid spiritual illness type",
  "SPIRITUAL_004": "Spiritual session not completed",
  
  "TREATMENT_001": "Treatment plan not found",
  "TREATMENT_002": "Treatment plan not accessible",
  "TREATMENT_003": "Invalid milestone update",
  "TREATMENT_004": "Treatment plan expired",
  
  "CRISIS_001": "Crisis level assessment failed",
  "CRISIS_002": "Emergency resources unavailable",
  "CRISIS_003": "Crisis intervention timeout",
  
  "AUTH_001": "Authentication required",
  "AUTH_002": "Insufficient permissions",
  "AUTH_003": "Session expired",
  
  "VALIDATION_001": "Invalid request format",
  "VALIDATION_002": "Required fields missing",
  "VALIDATION_003": "Data validation failed",
  
  "RATE_LIMIT_001": "Rate limit exceeded",
  "RATE_LIMIT_002": "Daily quota exceeded",
  
  "INTERNAL_001": "Internal server error",
  "INTERNAL_002": "Database connection failed",
  "INTERNAL_003": "External service unavailable"
}
```

### **Error Response Format**

```json
{
  "status": "error",
  "error": {
    "code": "ASSESSMENT_003",
    "message": "Invalid question response format",
    "details": {
      "field": "responseValue",
      "expectedType": "object",
      "receivedType": "string",
      "validationErrors": [
        "responseValue.type is required",
        "responseValue.value is required"
      ]
    },
    "islamicGuidance": "Please provide complete information so we can better assist you in your healing journey. Allah loves those who are thorough and careful."
  },
  "meta": {
    "timestamp": "2025-01-27T16:30:00Z",
    "request_id": "req_error123456",
    "islamicContext": {
      "blessing": "حسبنا الله ونعم الوكيل",
      "guidance": "Allah is sufficient for us, and He is the best Disposer of affairs."
    }
  }
}
```

---

This comprehensive API specification provides all the technical details needed to implement Feature 01's backend services, ensuring proper Islamic context integration, robust error handling, and scalable architecture for the global Muslim community.