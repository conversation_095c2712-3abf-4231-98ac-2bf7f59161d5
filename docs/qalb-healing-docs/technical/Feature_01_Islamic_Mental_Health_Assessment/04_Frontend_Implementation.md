# Feature 01: Islamic Mental Health Assessment - Frontend Implementation

## 📋 Document Overview

**Feature**: Islamic Mental Health Assessment v7  
**Document Type**: Frontend Implementation Guide  
**Version**: 1.0  
**Date**: January 2025  
**Status**: Draft  

## 📱 Mobile App Architecture

### **Technology Stack**
- **Framework**: React Native 0.72+ with Expo SDK 49+
- **Language**: TypeScript for type safety
- **State Management**: Redux Toolkit with RTK Query
- **Navigation**: React Navigation v6 with Islamic-themed transitions
- **UI Framework**: Custom Islamic design system
- **Animations**: React Native Reanimated v3
- **Audio**: Expo AV for Quranic recitations
- **Storage**: AsyncStorage with Redux Persist
- **Testing**: Jest + React Native Testing Library

### **Project Structure**
```
src/
├── components/
│   ├── assessment/
│   │   ├── ProgressiveDisclosure/
│   │   │   ├── QuestionRenderer.tsx
│   │   │   ├── ProgressIndicator.tsx
│   │   │   ├── AdaptiveLogic.tsx
│   │   │   └── CrisisDetection.tsx
│   │   ├── QuestionTypes/
│   │   │   ├── ScaleQuestion.tsx
│   │   │   ├── MultipleChoiceQuestion.tsx
│   │   │   ├── TextInputQuestion.tsx
│   │   │   ├── FrequencyQuestion.tsx
│   │   │   └── BooleanQuestion.tsx
│   │   ├── Results/
│   │   │   ├── FiveLayerVisualization.tsx
│   │   │   ├── Tier1Recommendations.tsx
│   │   │   ├── SpiritualDiagnosisProposal.tsx
│   │   │   └── CrisisSupport.tsx
│   │   └── Session/
│   │       ├── SessionManager.tsx
│   │       ├── PauseResumeControls.tsx
│   │       └── ProgressSaving.tsx
│   ├── spiritual/
│   │   ├── SpiritualDiagnosis/
│   │   │   ├── ReadinessCheck.tsx
│   │   │   ├── SessionSelector.tsx
│   │   │   ├── PreparationGuide.tsx
│   │   │   └── SafetyProtocols.tsx
│   │   ├── RuqyaSession/
│   │   │   ├── AudioPlayer.tsx
│   │   │   ├── ReactionMonitor.tsx
│   │   │   ├── IntensityTracker.tsx
│   │   │   └── SessionTimer.tsx
│   │   ├── Results/
│   │   │   ├── SpiritualAnalysis.tsx
│   │   │   ├── DiagnosisResults.tsx
│   │   │   └── ScholarReview.tsx
│   │   └── Progress/
│   │       ├── SessionProgress.tsx
│   │       ├── CompletionTracker.tsx
│   │       └── NextSteps.tsx
│   ├── treatment/
│   │   ├── TreatmentPlan/
│   │   │   ├── PlanOverview.tsx
│   │   │   ├── LayerSequence.tsx
│   │   │   ├── MilestoneTracker.tsx
│   │   │   └── ProgressVisualization.tsx
│   │   ├── Protocols/
│   │   │   ├── ProtocolCard.tsx
│   │   │   ├── InstructionGuide.tsx
│   │   │   ├── PracticeTimer.tsx
│   │   │   └── CompletionLogger.tsx
│   │   └── Progress/
│   │       ├── DailyProgress.tsx
│   │       ├── WeeklyReview.tsx
│   │       └── MilestoneAchievement.tsx
│   ├── crisis/
│   │   ├── CrisisDetection/
│   │   │   ├── RealTimeMonitor.tsx
│   │   │   ├── RiskAssessment.tsx
│   │   │   └── AlertSystem.tsx
│   │   ├── Support/
│   │   │   ├── EmergencyContacts.tsx
│   │   │   ├── CrisisResources.tsx
│   │   │   ├── ProfessionalReferral.tsx
│   │   │   └── SpiritualSupport.tsx
│   │   └── Intervention/
│   │       ├── ImmediateSupport.tsx
│   │       ├── SafetyPlan.tsx
│   │       └── FollowUpScheduler.tsx
│   └── common/
│       ├── IslamicUI/
│       │   ├── IslamicButton.tsx
│       │   ├── IslamicCard.tsx
│       │   ├── IslamicInput.tsx
│       │   ├── IslamicModal.tsx
│       │   ├── IslamicProgress.tsx
│       │   └── IslamicTypography.tsx
│       ├── Audio/
│       │   ├── QuranicPlayer.tsx
│       │   ├── AudioControls.tsx
│       │   └── DownloadManager.tsx
│       ├── Navigation/
│       │   ├── IslamicTabBar.tsx
│       │   ├── HeaderWithDua.tsx
│       │   └── NavigationHelpers.tsx
│       └── Offline/
│           ├── OfflineIndicator.tsx
│           ├── SyncManager.tsx
│           └── CacheManager.tsx
├── screens/
│   ├── Assessment/
│   │   ├── AssessmentIntroScreen.tsx
│   │   ├── AssessmentFlowScreen.tsx
│   │   ├── AssessmentResultsScreen.tsx
│   │   └── AssessmentHistoryScreen.tsx
│   ├── SpiritualDiagnosis/
│   │   ├── SpiritualIntroScreen.tsx
│   │   ├── ReadinessCheckScreen.tsx
│   │   ├── DiagnosisSessionScreen.tsx
│   │   ├── ReactionInputScreen.tsx
│   │   └── DiagnosisResultsScreen.tsx
│   ├── TreatmentPlan/
│   │   ├── TreatmentOverviewScreen.tsx
│   │   ├── DailyPracticeScreen.tsx
│   │   ├── ProgressTrackingScreen.tsx
│   │   └── MilestoneScreen.tsx
│   ├── Crisis/
│   │   ├── CrisisDetectedScreen.tsx
│   │   ├── EmergencySupportScreen.tsx
│   │   └── CrisisResourcesScreen.tsx
│   └── Common/
│       ├── LoadingScreen.tsx
│       ├── ErrorScreen.tsx
│       └── OfflineScreen.tsx
├── services/
│   ├── api/
│   │   ├── assessmentApi.ts
│   │   ├── spiritualApi.ts
│   │   ├── treatmentApi.ts
│   │   ├── crisisApi.ts
│   │   └── analyticsApi.ts
│   ├── offline/
│   │   ├── offlineManager.ts
│   │   ├── syncService.ts
│   │   └── cacheService.ts
│   ├── audio/
│   │   ├── audioManager.ts
│   │   ├── downloadService.ts
│   │   └── playbackService.ts
│   ├── notifications/
│   │   ├── notificationService.ts
│   │   ├── reminderService.ts
│   │   └── crisisAlerts.ts
│   └── analytics/
│       ├── trackingService.ts
│       ├── performanceMonitor.ts
│       └── userBehaviorAnalytics.ts
├── store/
│   ├── slices/
│   │   ├── assessmentSlice.ts
│   │   ├── spiritualSlice.ts
│   │   ├── treatmentSlice.ts
│   │   ├── crisisSlice.ts
│   │   ├── authSlice.ts
│   │   └── offlineSlice.ts
│   ├── api/
│   │   ├── assessmentApi.ts
│   │   ├── spiritualApi.ts
│   │   ├── treatmentApi.ts
│   │   └── crisisApi.ts
│   └── store.ts
├── utils/
│   ├── islamic/
│   │   ├── islamicHelpers.ts
│   │   ├── quranicUtils.ts
│   │   ├── prayerTimeUtils.ts
│   │   └── islamicValidation.ts
│   ├── analytics/
│   │   ├── eventTracking.ts
│   │   ├── performanceMetrics.ts
│   │   └── userJourneyAnalytics.ts
│   ├── security/
│   │   ├── dataEncryption.ts
│   │   ├── biometricAuth.ts
│   │   └── secureStorage.ts
│   └── accessibility/
│       ├── screenReader.ts
│       ├── voiceOver.ts
│       └── islamicAccessibility.ts
└── types/
    ├── assessment.ts
    ├── spiritual.ts
    ├── treatment.ts
    ├── crisis.ts
    └── common.ts
```

## 🎨 Islamic Design System

### **Color Palette**
```typescript
export const IslamicColors = {
  // Primary Colors
  primary: {
    50: '#E8F5E8',   // Light green (nature, peace)
    100: '#C3E6C3',  
    500: '#2E7D32',  // Main green (Islamic tradition)
    700: '#1B5E20',  // Dark green
    900: '#0D4F14',  // Deepest green
  },
  
  // Secondary Colors
  secondary: {
    50: '#FFF8E1',   // Light gold (wisdom, enlightenment)
    100: '#FFECB3',
    500: '#FF8F00',  // Main gold
    700: '#E65100',  // Dark gold
    900: '#BF360C',  // Deep gold
  },
  
  // Spiritual Colors
  spiritual: {
    light: '#E1F5FE',  // Light blue (peace, spirituality)
    main: '#0277BD',   // Blue (trust, divine)
    dark: '#01579B',   // Dark blue
  },
  
  // Crisis Colors
  crisis: {
    light: '#FFEBEE',  // Light red (gentle alert)
    main: '#D32F2F',   // Red (urgent attention)
    dark: '#B71C1C',   // Dark red (critical)
  },
  
  // Neutral Colors
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // Semantic Colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
};
```

### **Typography System**
```typescript
export const IslamicTypography = {
  // Arabic Typography
  arabic: {
    fontFamily: 'Amiri', // Traditional Arabic font
    sizes: {
      h1: 28,
      h2: 24,
      h3: 20,
      body: 16,
      caption: 14,
    },
    lineHeights: {
      h1: 36,
      h2: 32,
      h3: 28,
      body: 24,
      caption: 20,
    },
  },
  
  // English Typography
  english: {
    fontFamily: 'Inter', // Modern, readable font
    sizes: {
      h1: 24,
      h2: 20,
      h3: 18,
      body: 16,
      caption: 14,
    },
    lineHeights: {
      h1: 32,
      h2: 28,
      h3: 24,
      body: 22,
      caption: 18,
    },
  },
  
  // Quranic Text
  quranic: {
    fontFamily: 'Uthmanic', // Traditional Quranic script
    sizes: {
      large: 22,
      medium: 18,
      small: 16,
    },
    lineHeights: {
      large: 32,
      medium: 26,
      small: 22,
    },
  },
};
```

### **Islamic UI Components**

#### **IslamicButton Component**
```typescript
import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { IslamicColors, IslamicTypography } from '../theme';

interface IslamicButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'spiritual' | 'crisis';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  islamicBlessing?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const IslamicButton: React.FC<IslamicButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  islamicBlessing,
  style,
  textStyle,
}) => {
  const buttonStyle = [
    styles.base,
    styles[variant],
    styles[size],
    disabled && styles.disabled,
    style,
  ];

  const textStyleCombined = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityLabel={title}
      accessibilityHint={islamicBlessing}
    >
      <Text style={textStyleCombined}>
        {loading ? 'جاري التحميل...' : title}
      </Text>
      {islamicBlessing && (
        <Text style={styles.blessing}>
          {islamicBlessing}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  
  // Variants
  primary: {
    backgroundColor: IslamicColors.primary[500],
  },
  secondary: {
    backgroundColor: IslamicColors.secondary[500],
  },
  spiritual: {
    backgroundColor: IslamicColors.spiritual.main,
  },
  crisis: {
    backgroundColor: IslamicColors.crisis.main,
  },
  
  // Sizes
  small: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  medium: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  large: {
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  
  // Text styles
  text: {
    fontFamily: IslamicTypography.english.fontFamily,
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryText: {
    color: '#FFFFFF',
  },
  secondaryText: {
    color: '#FFFFFF',
  },
  spiritualText: {
    color: '#FFFFFF',
  },
  crisisText: {
    color: '#FFFFFF',
  },
  
  // Size text styles
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
  
  // States
  disabled: {
    backgroundColor: IslamicColors.neutral[300],
    elevation: 0,
    shadowOpacity: 0,
  },
  disabledText: {
    color: IslamicColors.neutral[500],
  },
  
  blessing: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    fontFamily: IslamicTypography.arabic.fontFamily,
  },
});
```

#### **FiveLayerVisualization Component**
```typescript
import React from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { IslamicColors, IslamicTypography } from '../theme';

interface LayerData {
  name: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  impactLevel: 'none' | 'mild' | 'moderate' | 'severe' | 'critical' | 'unknown';
  confidenceLevel: 'low' | 'medium' | 'high';
  requiresDeeperAssessment?: boolean;
}

interface FiveLayerVisualizationProps {
  layers: LayerData[];
  animated?: boolean;
}

export const FiveLayerVisualization: React.FC<FiveLayerVisualizationProps> = ({
  layers,
  animated = true,
}) => {
  const animatedValues = React.useRef(
    layers.map(() => new Animated.Value(0))
  ).current;

  React.useEffect(() => {
    if (animated) {
      const animations = animatedValues.map((value, index) =>
        Animated.timing(value, {
          toValue: 1,
          duration: 500,
          delay: index * 100,
          useNativeDriver: true,
        })
      );
      
      Animated.stagger(100, animations).start();
    }
  }, [animated, animatedValues]);

  const getLayerInfo = (layerName: string) => {
    const layerMap = {
      jism: { 
        arabic: 'الجسم', 
        english: 'Body (Jism)', 
        icon: '🤲',
        description: 'Physical health and bodily symptoms'
      },
      nafs: { 
        arabic: 'النفس', 
        english: 'Ego (Nafs)', 
        icon: '💭',
        description: 'Emotional patterns and psychological state'
      },
      aql: { 
        arabic: 'العقل', 
        english: 'Mind (Aql)', 
        icon: '🧠',
        description: 'Mental clarity and cognitive function'
      },
      qalb: { 
        arabic: 'القلب', 
        english: 'Heart (Qalb)', 
        icon: '💖',
        description: 'Spiritual connection and Islamic practice'
      },
      ruh: { 
        arabic: 'الروح', 
        english: 'Soul (Ruh)', 
        icon: '🌟',
        description: 'Spiritual energy and divine connection'
      },
    };
    return layerMap[layerName as keyof typeof layerMap];
  };

  const getImpactColor = (impactLevel: string, confidenceLevel: string) => {
    const baseColors = {
      none: IslamicColors.success,
      mild: IslamicColors.warning,
      moderate: IslamicColors.secondary[500],
      severe: IslamicColors.crisis.main,
      critical: IslamicColors.crisis.dark,
      unknown: IslamicColors.neutral[400],
    };

    const opacity = confidenceLevel === 'high' ? 1 : 
                   confidenceLevel === 'medium' ? 0.7 : 0.4;

    return {
      backgroundColor: baseColors[impactLevel as keyof typeof baseColors],
      opacity,
    };
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        تحليل الطبقات الخمس للصحة الإسلامية
      </Text>
      <Text style={styles.subtitle}>
        Five-Layer Islamic Health Analysis
      </Text>

      <View style={styles.layersContainer}>
        {layers.map((layer, index) => {
          const layerInfo = getLayerInfo(layer.name);
          const impactColor = getImpactColor(layer.impactLevel, layer.confidenceLevel);
          
          const animatedStyle = animated ? {
            opacity: animatedValues[index],
            transform: [{
              translateY: animatedValues[index].interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              }),
            }],
          } : {};

          return (
            <Animated.View
              key={layer.name}
              style={[styles.layerCard, animatedStyle]}
            >
              <View style={styles.layerHeader}>
                <Text style={styles.layerIcon}>{layerInfo.icon}</Text>
                <View style={styles.layerTitles}>
                  <Text style={styles.layerArabic}>{layerInfo.arabic}</Text>
                  <Text style={styles.layerEnglish}>{layerInfo.english}</Text>
                </View>
                <View style={[styles.impactIndicator, impactColor]}>
                  <Text style={styles.impactText}>
                    {layer.impactLevel.toUpperCase()}
                  </Text>
                </View>
              </View>

              <Text style={styles.layerDescription}>
                {layerInfo.description}
              </Text>

              <View style={styles.layerMetrics}>
                <View style={styles.metric}>
                  <Text style={styles.metricLabel}>Confidence</Text>
                  <Text style={styles.metricValue}>
                    {layer.confidenceLevel.toUpperCase()}
                  </Text>
                </View>
                
                {layer.requiresDeeperAssessment && (
                  <View style={styles.assessmentNeeded}>
                    <Text style={styles.assessmentText}>
                      Requires Spiritual Assessment
                    </Text>
                  </View>
                )}
              </View>
            </Animated.View>
          );
        })}
      </View>

      <View style={styles.legend}>
        <Text style={styles.legendTitle}>Impact Levels</Text>
        <View style={styles.legendItems}>
          {['none', 'mild', 'moderate', 'severe', 'critical'].map((level) => (
            <View key={level} style={styles.legendItem}>
              <View style={[
                styles.legendColor, 
                { backgroundColor: getImpactColor(level, 'high').backgroundColor }
              ]} />
              <Text style={styles.legendLabel}>{level}</Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  
  title: {
    fontSize: IslamicTypography.arabic.sizes.h2,
    fontFamily: IslamicTypography.arabic.fontFamily,
    textAlign: 'center',
    color: IslamicColors.primary[700],
    marginBottom: 4,
  },
  
  subtitle: {
    fontSize: IslamicTypography.english.sizes.h3,
    fontFamily: IslamicTypography.english.fontFamily,
    textAlign: 'center',
    color: IslamicColors.neutral[600],
    marginBottom: 24,
  },
  
  layersContainer: {
    gap: 16,
  },
  
  layerCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: IslamicColors.primary[500],
  },
  
  layerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  
  layerIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  
  layerTitles: {
    flex: 1,
  },
  
  layerArabic: {
    fontSize: IslamicTypography.arabic.sizes.body,
    fontFamily: IslamicTypography.arabic.fontFamily,
    color: IslamicColors.neutral[800],
    fontWeight: '600',
  },
  
  layerEnglish: {
    fontSize: IslamicTypography.english.sizes.caption,
    fontFamily: IslamicTypography.english.fontFamily,
    color: IslamicColors.neutral[600],
  },
  
  impactIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  
  impactText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  
  layerDescription: {
    fontSize: IslamicTypography.english.sizes.caption,
    fontFamily: IslamicTypography.english.fontFamily,
    color: IslamicColors.neutral[600],
    marginBottom: 12,
    lineHeight: 18,
  },
  
  layerMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  metric: {
    alignItems: 'center',
  },
  
  metricLabel: {
    fontSize: 10,
    color: IslamicColors.neutral[500],
    textTransform: 'uppercase',
  },
  
  metricValue: {
    fontSize: 12,
    fontWeight: '600',
    color: IslamicColors.neutral[700],
  },
  
  assessmentNeeded: {
    backgroundColor: IslamicColors.spiritual.light,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  
  assessmentText: {
    fontSize: 10,
    color: IslamicColors.spiritual.dark,
    fontWeight: '500',
  },
  
  legend: {
    marginTop: 24,
    padding: 16,
    backgroundColor: IslamicColors.neutral[50],
    borderRadius: 8,
  },
  
  legendTitle: {
    fontSize: IslamicTypography.english.sizes.caption,
    fontWeight: '600',
    color: IslamicColors.neutral[700],
    marginBottom: 8,
  },
  
  legendItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  
  legendLabel: {
    fontSize: 10,
    color: IslamicColors.neutral[600],
    textTransform: 'capitalize',
  },
});
```

## 📊 Assessment Flow Implementation

### **Progressive Disclosure Engine**
```typescript
import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { 
  submitAssessmentResponse, 
  updateAssessmentProgress,
  detectCrisis 
} from '../store/slices/assessmentSlice';

interface Question {
  questionId: string;
  questionText: string;
  questionType: 'scale' | 'multiple_choice' | 'text' | 'boolean' | 'frequency';
  options?: any;
  layerCategory: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh' | 'general';
  isRequired: boolean;
  islamicContext?: {
    guidance?: string;
    verse?: string;
    hadith?: string;
  };
}

interface ProgressiveDisclosureEngineProps {
  sessionId: string;
  onComplete: (results: any) => void;
  onCrisisDetected: (crisisData: any) => void;
}

export const ProgressiveDisclosureEngine: React.FC<ProgressiveDisclosureEngineProps> = ({
  sessionId,
  onComplete,
  onCrisisDetected,
}) => {
  const dispatch = useDispatch();
  const { 
    currentQuestion, 
    progressInfo, 
    crisisLevel,
    adaptiveLogic 
  } = useSelector((state: any) => state.assessment);

  const [responseStartTime, setResponseStartTime] = useState<Date>(new Date());
  const [currentResponse, setCurrentResponse] = useState<any>(null);

  useEffect(() => {
    // Monitor for crisis detection
    if (crisisLevel && crisisLevel !== 'none') {
      onCrisisDetected({
        level: crisisLevel,
        sessionId,
        timestamp: new Date(),
      });
    }
  }, [crisisLevel, sessionId, onCrisisDetected]);

  const handleResponseSubmit = async (responseValue: any) => {
    const responseTime = Math.floor(
      (new Date().getTime() - responseStartTime.getTime()) / 1000
    );

    const response = {
      questionId: currentQuestion.questionId,
      responseValue,
      responseTime,
      metadata: {
        hesitationTime: responseTime > 30 ? responseTime - 30 : 0,
        changedAnswer: currentResponse !== null,
        skipRequested: false,
      },
    };

    try {
      // Submit response and get next question
      const result = await dispatch(submitAssessmentResponse({
        sessionId,
        response,
      })).unwrap();

      // Update progress
      dispatch(updateAssessmentProgress({
        sessionId,
        progressInfo: result.progressInfo,
      }));

      // Check for crisis detection
      if (result.crisisDetection?.level !== 'none') {
        dispatch(detectCrisis({
          sessionId,
          crisisData: result.crisisDetection,
        }));
      }

      // Check if assessment is complete
      if (result.progressInfo.completionPercentage >= 100) {
        onComplete(result);
      } else {
        // Reset for next question
        setResponseStartTime(new Date());
        setCurrentResponse(null);
      }
    } catch (error) {
      console.error('Failed to submit response:', error);
      // Handle error appropriately
    }
  };

  const renderQuestion = () => {
    if (!currentQuestion) return null;

    const questionProps = {
      question: currentQuestion,
      onResponse: handleResponseSubmit,
      currentValue: currentResponse,
      onValueChange: setCurrentResponse,
    };

    switch (currentQuestion.questionType) {
      case 'scale':
        return <ScaleQuestion {...questionProps} />;
      case 'multiple_choice':
        return <MultipleChoiceQuestion {...questionProps} />;
      case 'text':
        return <TextInputQuestion {...questionProps} />;
      case 'boolean':
        return <BooleanQuestion {...questionProps} />;
      case 'frequency':
        return <FrequencyQuestion {...questionProps} />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <ProgressIndicator 
        current={progressInfo?.currentStep || 1}
        total={progressInfo?.totalEstimatedSteps || 25}
        percentage={progressInfo?.completionPercentage || 0}
      />
      
      {renderQuestion()}
      
      <CrisisDetection 
        level={crisisLevel}
        onEmergencyAction={() => onCrisisDetected({ level: 'critical', sessionId })}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
```

### **Question Type Components**

#### **ScaleQuestion Component**
```typescript
import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Slider from '@react-native-community/slider';
import { IslamicButton } from '../common/IslamicUI/IslamicButton';
import { IslamicColors, IslamicTypography } from '../theme';

interface ScaleQuestionProps {
  question: {
    questionText: string;
    options: {
      min: number;
      max: number;
      labels: { [key: string]: string };
    };
    islamicContext?: {
      guidance?: string;
      verse?: string;
    };
  };
  onResponse: (value: any) => void;
  currentValue: number | null;
  onValueChange: (value: number) => void;
}

export const ScaleQuestion: React.FC<ScaleQuestionProps> = ({
  question,
  onResponse,
  currentValue,
  onValueChange,
}) => {
  const [sliderValue, setSliderValue] = useState(currentValue || question.options.min);

  const handleSliderChange = (value: number) => {
    setSliderValue(value);
    onValueChange(value);
  };

  const handleSubmit = () => {
    onResponse({
      type: 'scale',
      value: sliderValue,
      confidence: 0.8, // Could be calculated based on response time
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.questionText}>
        {question.questionText}
      </Text>

      {question.islamicContext?.guidance && (
        <View style={styles.islamicGuidance}>
          <Text style={styles.guidanceText}>
            {question.islamicContext.guidance}
          </Text>
        </View>
      )}

      {question.islamicContext?.verse && (
        <View style={styles.verseContainer}>
          <Text style={styles.verseText}>
            {question.islamicContext.verse}
          </Text>
        </View>
      )}

      <View style={styles.sliderContainer}>
        <View style={styles.sliderLabels}>
          <Text style={styles.labelText}>
            {question.options.labels[question.options.min.toString()]}
          </Text>
          <Text style={styles.labelText}>
            {question.options.labels[question.options.max.toString()]}
          </Text>
        </View>

        <Slider
          style={styles.slider}
          minimumValue={question.options.min}
          maximumValue={question.options.max}
          step={1}
          value={sliderValue}
          onValueChange={handleSliderChange}
          minimumTrackTintColor={IslamicColors.primary[500]}
          maximumTrackTintColor={IslamicColors.neutral[300]}
          thumbStyle={styles.sliderThumb}
        />

        <View style={styles.currentValue}>
          <Text style={styles.currentValueText}>
            Current: {sliderValue}
          </Text>
        </View>
      </View>

      <IslamicButton
        title="Continue"
        onPress={handleSubmit}
        variant="primary"
        islamicBlessing="بارك الله فيك"
        style={styles.submitButton}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  
  questionText: {
    fontSize: IslamicTypography.english.sizes.h3,
    fontFamily: IslamicTypography.english.fontFamily,
    color: IslamicColors.neutral[800],
    lineHeight: 28,
    marginBottom: 20,
    textAlign: 'center',
  },
  
  islamicGuidance: {
    backgroundColor: IslamicColors.primary[50],
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: IslamicColors.primary[500],
  },
  
  guidanceText: {
    fontSize: IslamicTypography.english.sizes.body,
    fontFamily: IslamicTypography.english.fontFamily,
    color: IslamicColors.primary[700],
    fontStyle: 'italic',
  },
  
  verseContainer: {
    backgroundColor: IslamicColors.spiritual.light,
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: 'center',
  },
  
  verseText: {
    fontSize: IslamicTypography.arabic.sizes.body,
    fontFamily: IslamicTypography.quranic.fontFamily,
    color: IslamicColors.spiritual.dark,
    textAlign: 'center',
    lineHeight: 24,
  },
  
  sliderContainer: {
    marginVertical: 40,
  },
  
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  
  labelText: {
    fontSize: IslamicTypography.english.sizes.caption,
    color: IslamicColors.neutral[600],
    fontWeight: '500',
  },
  
  slider: {
    width: '100%',
    height: 40,
  },
  
  sliderThumb: {
    backgroundColor: IslamicColors.primary[500],
    width: 20,
    height: 20,
  },
  
  currentValue: {
    alignItems: 'center',
    marginTop: 10,
  },
  
  currentValueText: {
    fontSize: IslamicTypography.english.sizes.body,
    fontWeight: '600',
    color: IslamicColors.primary[600],
  },
  
  submitButton: {
    marginTop: 40,
  },
});
```

## 🕌 Spiritual Diagnosis Implementation

### **Ruqya Session Component**
```typescript
import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { Audio } from 'expo-av';
import { IslamicButton } from '../common/IslamicUI/IslamicButton';
import { ReactionMonitor } from './ReactionMonitor';
import { SessionTimer } from './SessionTimer';
import { IslamicColors, IslamicTypography } from '../theme';

interface RuqyaSessionProps {
  sessionId: string;
  spiritualIllnessType: 'sihr' | 'ayn' | 'mass' | 'waswas';
  audioContent: {
    mainRecitation: {
      url: string;
      duration: number;
      reciter: string;
    };
    preparationGuidance: {
      url: string;
      duration: number;
    };
  };
  onReactionRecorded: (reaction: any) => void;
  onSessionComplete: (results: any) => void;
}

export const RuqyaSession: React.FC<RuqyaSessionProps> = ({
  sessionId,
  spiritualIllnessType,
  audioContent,
  onReactionRecorded,
  onSessionComplete,
}) => {
  const [sessionPhase, setSessionPhase] = useState<'preparation' | 'main_recitation' | 'post_recitation'>('preparation');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [reactions, setReactions] = useState<any[]>([]);
  const [sessionStartTime] = useState(new Date());
  
  const soundRef = useRef<Audio.Sound | null>(null);
  const positionUpdateInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      // Cleanup audio and intervals
      if (soundRef.current) {
        soundRef.current.unloadAsync();
      }
      if (positionUpdateInterval.current) {
        clearInterval(positionUpdateInterval.current);
      }
    };
  }, []);

  const loadAudio = async (audioUrl: string) => {
    try {
      if (soundRef.current) {
        await soundRef.current.unloadAsync();
      }

      const { sound } = await Audio.Sound.createAsync(
        { uri: audioUrl },
        { shouldPlay: false }
      );
      
      soundRef.current = sound;
      
      // Set up position updates
      sound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && status.positionMillis) {
          setCurrentTime(Math.floor(status.positionMillis / 1000));
        }
        
        if (status.isLoaded && status.didJustFinish) {
          handleAudioComplete();
        }
      });
      
    } catch (error) {
      console.error('Failed to load audio:', error);
      Alert.alert('Audio Error', 'Failed to load recitation audio. Please check your connection.');
    }
  };

  const playAudio = async () => {
    try {
      if (soundRef.current) {
        await soundRef.current.playAsync();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Failed to play audio:', error);
    }
  };

  const pauseAudio = async () => {
    try {
      if (soundRef.current) {
        await soundRef.current.pauseAsync();
        setIsPlaying(false);
      }
    } catch (error) {
      console.error('Failed to pause audio:', error);
    }
  };

  const handleAudioComplete = () => {
    setIsPlaying(false);
    
    if (sessionPhase === 'preparation') {
      setSessionPhase('main_recitation');
      loadAudio(audioContent.mainRecitation.url);
    } else if (sessionPhase === 'main_recitation') {
      setSessionPhase('post_recitation');
      // Session complete, show results
      handleSessionComplete();
    }
  };

  const handleReactionAdded = (reaction: any) => {
    const reactionWithTimestamp = {
      ...reaction,
      timestamp: new Date(),
      sessionPhase,
      phaseTimeOffset: currentTime,
    };
    
    setReactions(prev => [...prev, reactionWithTimestamp]);
    onReactionRecorded(reactionWithTimestamp);
  };

  const handleSessionComplete = () => {
    const sessionDuration = Math.floor(
      (new Date().getTime() - sessionStartTime.getTime()) / 1000 / 60
    );

    const results = {
      sessionId,
      spiritualIllnessType,
      reactions,
      sessionDuration,
      overallIntensity: calculateOverallIntensity(reactions),
      sessionCompleted: true,
    };

    onSessionComplete(results);
  };

  const calculateOverallIntensity = (reactions: any[]): number => {
    if (reactions.length === 0) return 0;
    
    const totalIntensity = reactions.reduce((sum, reaction) => sum + reaction.intensity, 0);
    return Math.round(totalIntensity / reactions.length);
  };

  const startPreparation = async () => {
    await loadAudio(audioContent.preparationGuidance.url);
    await playAudio();
  };

  const startMainRecitation = async () => {
    setSessionPhase('main_recitation');
    await loadAudio(audioContent.mainRecitation.url);
    await playAudio();
  };

  const getSpiritualIllnessInfo = () => {
    const illnessMap = {
      sihr: {
        arabic: 'السحر',
        english: 'Black Magic (Sihr)',
        description: 'Spiritual interference through magical practices',
        protection: 'Surah Al-Falaq provides protection against sihr',
      },
      ayn: {
        arabic: 'العين',
        english: 'Evil Eye (Ayn)',
        description: 'Harm caused by envious or admiring looks',
        protection: 'Surah Al-Falaq and specific duas for protection',
      },
      mass: {
        arabic: 'المس',
        english: 'Jinn Possession (Mass)',
        description: 'Spiritual interference by jinn entities',
        protection: 'Surah An-Nas and Ayat al-Kursi for protection',
      },
      waswas: {
        arabic: 'الوسواس',
        english: 'Whispers (Waswas)',
        description: 'Spiritual whispers and negative thoughts',
        protection: 'Seeking refuge in Allah from Satan\'s whispers',
      },
    };
    
    return illnessMap[spiritualIllnessType];
  };

  const illnessInfo = getSpiritualIllnessInfo();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.illnessArabic}>{illnessInfo.arabic}</Text>
        <Text style={styles.illnessEnglish}>{illnessInfo.english}</Text>
        <Text style={styles.illnessDescription}>{illnessInfo.description}</Text>
      </View>

      <SessionTimer 
        phase={sessionPhase}
        currentTime={currentTime}
        totalDuration={audioContent.mainRecitation.duration}
      />

      <View style={styles.audioControls}>
        {sessionPhase === 'preparation' && (
          <IslamicButton
            title="Begin Preparation"
            onPress={startPreparation}
            variant="spiritual"
            islamicBlessing="أعوذ بالله من الشيطان الرجيم"
          />
        )}

        {sessionPhase === 'main_recitation' && (
          <View style={styles.playbackControls}>
            <IslamicButton
              title={isPlaying ? "Pause" : "Play"}
              onPress={isPlaying ? pauseAudio : playAudio}
              variant="primary"
              size="large"
            />
            
            <Text style={styles.reciterInfo}>
              Recited by: {audioContent.mainRecitation.reciter}
            </Text>
          </View>
        )}
      </View>

      <ReactionMonitor
        sessionPhase={sessionPhase}
        onReactionAdded={handleReactionAdded}
        isActive={sessionPhase === 'main_recitation'}
      />

      {sessionPhase === 'post_recitation' && (
        <View style={styles.completionSection}>
          <Text style={styles.completionText}>
            Session completed. Please review your reactions.
          </Text>
          <IslamicButton
            title="Complete Session"
            onPress={handleSessionComplete}
            variant="primary"
            islamicBlessing="الحمد لله"
          />
        </View>
      )}

      <View style={styles.safetyNote}>
        <Text style={styles.safetyText}>
          If you experience intense reactions, stop immediately and seek support.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  
  header: {
    alignItems: 'center',
    marginBottom: 30,
    padding: 20,
    backgroundColor: IslamicColors.spiritual.light,
    borderRadius: 12,
  },
  
  illnessArabic: {
    fontSize: IslamicTypography.arabic.sizes.h2,
    fontFamily: IslamicTypography.arabic.fontFamily,
    color: IslamicColors.spiritual.dark,
    marginBottom: 8,
  },
  
  illnessEnglish: {
    fontSize: IslamicTypography.english.sizes.h3,
    fontFamily: IslamicTypography.english.fontFamily,
    color: IslamicColors.spiritual.main,
    fontWeight: '600',
    marginBottom: 8,
  },
  
  illnessDescription: {
    fontSize: IslamicTypography.english.sizes.body,
    fontFamily: IslamicTypography.english.fontFamily,
    color: IslamicColors.neutral[600],
    textAlign: 'center',
    lineHeight: 22,
  },
  
  audioControls: {
    alignItems: 'center',
    marginVertical: 30,
  },
  
  playbackControls: {
    alignItems: 'center',
    gap: 16,
  },
  
  reciterInfo: {
    fontSize: IslamicTypography.english.sizes.caption,
    color: IslamicColors.neutral[600],
    fontStyle: 'italic',
  },
  
  completionSection: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: IslamicColors.success + '20',
    borderRadius: 12,
    marginVertical: 20,
  },
  
  completionText: {
    fontSize: IslamicTypography.english.sizes.body,
    color: IslamicColors.neutral[700],
    textAlign: 'center',
    marginBottom: 16,
  },
  
  safetyNote: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    padding: 12,
    backgroundColor: IslamicColors.warning + '20',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: IslamicColors.warning,
  },
  
  safetyText: {
    fontSize: IslamicTypography.english.sizes.caption,
    color: IslamicColors.warning,
    textAlign: 'center',
    fontWeight: '500',
  },
});
```

## 📊 State Management

### **Assessment Redux Slice**
```typescript
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { assessmentApi } from '../api/assessmentApi';

interface AssessmentState {
  currentSession: {
    sessionId: string | null;
    sessionType: 'initial' | 'spiritual' | 'follow_up' | null;
    status: 'not_started' | 'in_progress' | 'completed' | 'paused';
  };
  currentQuestion: any | null;
  progressInfo: {
    currentStep: number;
    totalEstimatedSteps: number;
    completionPercentage: number;
    estimatedTimeRemaining: number;
  } | null;
  responses: any[];
  fiveLayerAnalysis: any | null;
  tier1Recommendations: any | null;
  spiritualDiagnosisProposal: any | null;
  crisisLevel: 'none' | 'low' | 'moderate' | 'high' | 'critical';
  crisisData: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: AssessmentState = {
  currentSession: {
    sessionId: null,
    sessionType: null,
    status: 'not_started',
  },
  currentQuestion: null,
  progressInfo: null,
  responses: [],
  fiveLayerAnalysis: null,
  tier1Recommendations: null,
  spiritualDiagnosisProposal: null,
  crisisLevel: 'none',
  crisisData: null,
  loading: false,
  error: null,
};

// Async thunks
export const initializeAssessment = createAsyncThunk(
  'assessment/initialize',
  async (params: {
    sessionType: 'initial' | 'spiritual' | 'follow_up';
    userProfile: any;
  }) => {
    const response = await assessmentApi.initializeSession(params);
    return response.data;
  }
);

export const submitAssessmentResponse = createAsyncThunk(
  'assessment/submitResponse',
  async (params: {
    sessionId: string;
    response: any;
  }) => {
    const response = await assessmentApi.submitResponse(params.sessionId, params.response);
    return response.data;
  }
);

export const getAssessmentResults = createAsyncThunk(
  'assessment/getResults',
  async (sessionId: string) => {
    const response = await assessmentApi.getResults(sessionId);
    return response.data;
  }
);

export const pauseAssessment = createAsyncThunk(
  'assessment/pause',
  async (params: {
    sessionId: string;
    reason: string;
  }) => {
    const response = await assessmentApi.pauseSession(params.sessionId, params.reason);
    return response.data;
  }
);

export const resumeAssessment = createAsyncThunk(
  'assessment/resume',
  async (sessionId: string) => {
    const response = await assessmentApi.resumeSession(sessionId);
    return response.data;
  }
);

const assessmentSlice = createSlice({
  name: 'assessment',
  initialState,
  reducers: {
    updateAssessmentProgress: (state, action: PayloadAction<any>) => {
      state.progressInfo = action.payload.progressInfo;
    },
    
    detectCrisis: (state, action: PayloadAction<any>) => {
      state.crisisLevel = action.payload.crisisData.level;
      state.crisisData = action.payload.crisisData;
    },
    
    clearCrisis: (state) => {
      state.crisisLevel = 'none';
      state.crisisData = null;
    },
    
    addResponse: (state, action: PayloadAction<any>) => {
      state.responses.push(action.payload);
    },
    
    resetAssessment: (state) => {
      return initialState;
    },
    
    setCurrentQuestion: (state, action: PayloadAction<any>) => {
      state.currentQuestion = action.payload;
    },
  },
  
  extraReducers: (builder) => {
    builder
      // Initialize Assessment
      .addCase(initializeAssessment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(initializeAssessment.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSession = {
          sessionId: action.payload.sessionId,
          sessionType: action.payload.sessionType,
          status: 'in_progress',
        };
        state.currentQuestion = action.payload.firstQuestion;
        state.progressInfo = action.payload.progressInfo;
      })
      .addCase(initializeAssessment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to initialize assessment';
      })
      
      // Submit Response
      .addCase(submitAssessmentResponse.pending, (state) => {
        state.loading = true;
      })
      .addCase(submitAssessmentResponse.fulfilled, (state, action) => {
        state.loading = false;
        state.currentQuestion = action.payload.nextQuestion;
        state.progressInfo = action.payload.progressInfo;
        
        // Handle crisis detection
        if (action.payload.crisisDetection?.level !== 'none') {
          state.crisisLevel = action.payload.crisisDetection.level;
          state.crisisData = action.payload.crisisDetection;
        }
        
        // Check if assessment is complete
        if (action.payload.progressInfo.completionPercentage >= 100) {
          state.currentSession.status = 'completed';
        }
      })
      .addCase(submitAssessmentResponse.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to submit response';
      })
      
      // Get Results
      .addCase(getAssessmentResults.fulfilled, (state, action) => {
        state.fiveLayerAnalysis = action.payload.fiveLayerAnalysis;
        state.tier1Recommendations = action.payload.tier1Recommendations;
        state.spiritualDiagnosisProposal = action.payload.spiritualDiagnosisProposal;
        state.crisisLevel = action.payload.crisisAssessment?.level || 'none';
      })
      
      // Pause Assessment
      .addCase(pauseAssessment.fulfilled, (state) => {
        state.currentSession.status = 'paused';
      })
      
      // Resume Assessment
      .addCase(resumeAssessment.fulfilled, (state, action) => {
        state.currentSession.status = 'in_progress';
        state.currentQuestion = action.payload.currentQuestion;
        state.progressInfo = action.payload.progressInfo;
      });
  },
});

export const {
  updateAssessmentProgress,
  detectCrisis,
  clearCrisis,
  addResponse,
  resetAssessment,
  setCurrentQuestion,
} = assessmentSlice.actions;

export default assessmentSlice.reducer;
```

## 🔄 Offline Support

### **Offline Manager**
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo';
import { store } from '../store/store';
import { syncOfflineData, addOfflineAction } from '../store/slices/offlineSlice';

interface OfflineAction {
  id: string;
  type: string;
  payload: any;
  timestamp: Date;
  retryCount: number;
}

class OfflineManager {
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;
  private maxRetries: number = 3;

  constructor() {
    this.initializeNetworkListener();
    this.initializePeriodicSync();
  }

  private initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected || false;
      
      if (wasOffline && this.isOnline) {
        // Just came back online, sync data
        this.syncOfflineActions();
      }
    });
  }

  private initializePeriodicSync() {
    // Sync every 5 minutes when online
    setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.syncOfflineActions();
      }
    }, 5 * 60 * 1000);
  }

  async queueAction(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>) {
    const offlineAction: OfflineAction = {
      ...action,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      retryCount: 0,
    };

    // Store in Redux
    store.dispatch(addOfflineAction(offlineAction));

    // Store in AsyncStorage for persistence
    await this.saveOfflineAction(offlineAction);

    // If online, try to sync immediately
    if (this.isOnline) {
      this.syncOfflineActions();
    }
  }

  private async saveOfflineAction(action: OfflineAction) {
    try {
      const existingActions = await this.getStoredOfflineActions();
      const updatedActions = [...existingActions, action];
      await AsyncStorage.setItem('offline_actions', JSON.stringify(updatedActions));
    } catch (error) {
      console.error('Failed to save offline action:', error);
    }
  }

  private async getStoredOfflineActions(): Promise<OfflineAction[]> {
    try {
      const stored = await AsyncStorage.getItem('offline_actions');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get stored offline actions:', error);
      return [];
    }
  }

  private async removeStoredOfflineAction(actionId: string) {
    try {
      const existingActions = await this.getStoredOfflineActions();
      const filteredActions = existingActions.filter(action => action.id !== actionId);
      await AsyncStorage.setItem('offline_actions', JSON.stringify(filteredActions));
    } catch (error) {
      console.error('Failed to remove stored offline action:', error);
    }
  }

  async syncOfflineActions() {
    if (!this.isOnline || this.syncInProgress) {
      return;
    }

    this.syncInProgress = true;

    try {
      const offlineActions = await this.getStoredOfflineActions();
      
      for (const action of offlineActions) {
        try {
          await this.executeOfflineAction(action);
          await this.removeStoredOfflineAction(action.id);
        } catch (error) {
          console.error(`Failed to sync action ${action.id}:`, error);
          
          // Increment retry count
          action.retryCount++;
          
          if (action.retryCount >= this.maxRetries) {
            // Remove action after max retries
            await this.removeStoredOfflineAction(action.id);
            console.warn(`Action ${action.id} removed after ${this.maxRetries} failed attempts`);
          } else {
            // Update stored action with new retry count
            await this.updateStoredOfflineAction(action);
          }
        }
      }

      // Dispatch sync completion
      store.dispatch(syncOfflineData());
      
    } catch (error) {
      console.error('Failed to sync offline actions:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  private async updateStoredOfflineAction(updatedAction: OfflineAction) {
    try {
      const existingActions = await this.getStoredOfflineActions();
      const updatedActions = existingActions.map(action => 
        action.id === updatedAction.id ? updatedAction : action
      );
      await AsyncStorage.setItem('offline_actions', JSON.stringify(updatedActions));
    } catch (error) {
      console.error('Failed to update stored offline action:', error);
    }
  }

  private async executeOfflineAction(action: OfflineAction): Promise<void> {
    switch (action.type) {
      case 'SUBMIT_ASSESSMENT_RESPONSE':
        await this.syncAssessmentResponse(action.payload);
        break;
      case 'RECORD_SPIRITUAL_REACTION':
        await this.syncSpiritualReaction(action.payload);
        break;
      case 'UPDATE_TREATMENT_PROGRESS':
        await this.syncTreatmentProgress(action.payload);
        break;
      default:
        console.warn(`Unknown offline action type: ${action.type}`);
    }
  }

  private async syncAssessmentResponse(payload: any) {
    // Implementation for syncing assessment responses
    const { assessmentApi } = await import('../api/assessmentApi');
    await assessmentApi.submitResponse(payload.sessionId, payload.response);
  }

  private async syncSpiritualReaction(payload: any) {
    // Implementation for syncing spiritual reactions
    const { spiritualApi } = await import('../api/spiritualApi');
    await spiritualApi.recordReaction(payload.sessionId, payload.reaction);
  }

  private async syncTreatmentProgress(payload: any) {
    // Implementation for syncing treatment progress
    const { treatmentApi } = await import('../api/treatmentApi');
    await treatmentApi.updateProgress(payload.planId, payload.progress);
  }

  isOffline(): boolean {
    return !this.isOnline;
  }

  async clearOfflineData() {
    await AsyncStorage.removeItem('offline_actions');
    store.dispatch(syncOfflineData());
  }
}

export const offlineManager = new OfflineManager();
```

---

This comprehensive frontend implementation guide provides the foundation for building a culturally-sensitive, performant, and accessible Islamic mental health assessment application. The implementation emphasizes Islamic design principles, offline functionality, and robust state management while maintaining high code quality and user experience standards.