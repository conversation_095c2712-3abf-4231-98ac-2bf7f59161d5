# Feature 01: Islamic Mental Health Assessment - Testing Strategy

## 📋 Document Overview

**Feature**: Islamic Mental Health Assessment v7  
**Document Type**: Comprehensive Testing Strategy  
**Version**: 1.0  
**Date**: January 2025  
**Status**: Draft  

## 🎯 Testing Philosophy

### **Islamic-Centered Testing Approach**
Our testing strategy prioritizes the unique aspects of Islamic mental health assessment:

- **Cultural Sensitivity Testing**: Ensuring all content respects Islamic values and traditions
- **Spiritual Accuracy Validation**: Verifying Islamic content authenticity with scholar review
- **Crisis Safety Testing**: Rigorous testing of emergency detection and response systems
- **Privacy Protection Testing**: Extra validation for spiritual and religious data handling
- **Accessibility Testing**: Ensuring the app serves Muslims with diverse needs and abilities

### **Quality Assurance Principles**
1. **User Safety First**: Crisis detection and emergency response systems receive highest testing priority
2. **Cultural Authenticity**: All Islamic content validated by qualified scholars
3. **Data Privacy**: Comprehensive testing of sensitive spiritual and mental health data protection
4. **Performance Excellence**: Ensuring smooth experience across diverse devices and network conditions
5. **Accessibility Compliance**: Full support for users with disabilities following Islamic accessibility principles

## 🏗️ Testing Architecture

### **Testing Pyramid Structure**

```mermaid
graph TB
    subgraph "Testing Pyramid"
        A[Unit Tests - 70%] --> B[Integration Tests - 20%]
        B --> C[E2E Tests - 10%]
    end
    
    subgraph "Specialized Testing"
        D[Islamic Content Validation] --> E[Crisis Detection Testing]
        E --> F[Performance Testing]
        F --> G[Security Testing]
        G --> H[Accessibility Testing]
    end
    
    C --> D
```

### **Test Environment Strategy**

```yaml
environments:
  development:
    purpose: "Daily development testing"
    data: "Mock Islamic content and test users"
    features: "All features enabled with debug logging"
    
  staging:
    purpose: "Pre-production validation"
    data: "Sanitized production-like data"
    features: "Production configuration with monitoring"
    
  production:
    purpose: "Live monitoring and smoke tests"
    data: "Real user data with privacy protection"
    features: "Production features with minimal testing impact"
    
  scholar_review:
    purpose: "Islamic content validation"
    data: "Complete Islamic content library"
    features: "Content review tools and scholar access"
```

## 🧪 Unit Testing Strategy

### **Backend Unit Tests**

#### **Assessment Logic Testing**
```typescript
// apps/backend/__tests__/unit/assessment/progressiveDisclosure.test.ts
import { ProgressiveDisclosureEngine } from '../../../src/services/assessment/progressiveDisclosure';
import { mockAssessmentSession, mockUserProfile } from '../../fixtures/assessment';

describe('Progressive Disclosure Engine', () => {
  let engine: ProgressiveDisclosureEngine;
  
  beforeEach(() => {
    engine = new ProgressiveDisclosureEngine();
  });

  describe('Question Selection Algorithm', () => {
    it('should select appropriate first question based on user profile', async () => {
      const userProfile = mockUserProfile({
        islamicPracticeLevel: 'moderate',
        previousAssessments: false
      });
      
      const firstQuestion = await engine.getFirstQuestion(userProfile);
      
      expect(firstQuestion.questionId).toBe('q_general_001');
      expect(firstQuestion.layerCategory).toBe('general');
      expect(firstQuestion.islamicContext).toBeDefined();
    });

    it('should adapt questions based on previous responses', async () => {
      const responses = [
        { questionId: 'q_001', responseValue: { value: 8 }, layerCategory: 'nafs' },
        { questionId: 'q_002', responseValue: { value: 3 }, layerCategory: 'qalb' }
      ];
      
      const nextQuestion = await engine.getNextQuestion(responses);
      
      expect(nextQuestion.layerCategory).toBe('qalb'); // Should focus on lower-scoring layer
      expect(nextQuestion.questionType).toBe('frequency'); // Should dive deeper
    });

    it('should skip irrelevant questions for experienced users', async () => {
      const userProfile = mockUserProfile({
        islamicPracticeLevel: 'advanced',
        previousAssessments: true
      });
      
      const questionSequence = await engine.generateQuestionSequence(userProfile);
      
      expect(questionSequence.length).toBeLessThan(20); // Should be shorter
      expect(questionSequence.some(q => q.questionId.includes('basic'))).toBe(false);
    });
  });

  describe('Crisis Detection', () => {
    it('should detect immediate crisis from responses', async () => {
      const crisisResponse = {
        questionId: 'q_crisis_001',
        responseValue: { value: 'yes' }, // Suicidal ideation
        responseTime: 5
      };
      
      const crisisLevel = await engine.detectCrisis([crisisResponse]);
      
      expect(crisisLevel.level).toBe('critical');
      expect(crisisLevel.triggers).toContain('suicidal_ideation');
      expect(crisisLevel.immediateSupport).toBeDefined();
    });

    it('should calculate cumulative crisis risk', async () => {
      const responses = [
        { questionId: 'q_001', responseValue: { value: 9 }, layerCategory: 'nafs' }, // High anxiety
        { questionId: 'q_002', responseValue: { value: 8 }, layerCategory: 'aql' }, // Racing thoughts
        { questionId: 'q_003', responseValue: { value: 7 }, layerCategory: 'jism' } // Sleep issues
      ];
      
      const crisisLevel = await engine.detectCrisis(responses);
      
      expect(crisisLevel.level).toBe('moderate');
      expect(crisisLevel.cumulativeScore).toBeGreaterThan(0.6);
    });
  });

  describe('Islamic Context Integration', () => {
    it('should include appropriate Islamic guidance for each question', async () => {
      const question = await engine.getQuestion('q_prayer_concentration');
      
      expect(question.islamicContext.guidance).toContain('khushu');
      expect(question.islamicContext.verse || question.islamicContext.hadith).toBeDefined();
    });

    it('should respect Islamic cultural sensitivities', async () => {
      const questions = await engine.getAllQuestions();
      
      questions.forEach(question => {
        expect(question.questionText).not.toContain('dating');
        expect(question.questionText).not.toContain('alcohol');
        // Should use Islamic terminology
        if (question.layerCategory === 'qalb') {
          expect(question.questionText.toLowerCase()).toMatch(/prayer|salah|dua|quran/);
        }
      });
    });
  });
});
```

#### **Spiritual Diagnosis Testing**
```typescript
// apps/backend/__tests__/unit/spiritual/diagnosisEngine.test.ts
import { SpiritualDiagnosisEngine } from '../../../src/services/spiritual/diagnosisEngine';
import { mockSpiritualReactions } from '../../fixtures/spiritual';

describe('Spiritual Diagnosis Engine', () => {
  let engine: SpiritualDiagnosisEngine;
  
  beforeEach(() => {
    engine = new SpiritualDiagnosisEngine();
  });

  describe('Reaction Analysis', () => {
    it('should analyze sihr indicators correctly', async () => {
      const sihrReactions = mockSpiritualReactions({
        spiritualIllnessType: 'sihr',
        reactions: [
          { type: 'physical', subtype: 'tingling', intensity: 8, location: 'hands' },
          { type: 'energy', subtype: 'drain', intensity: 7, duration: 120 },
          { type: 'spiritual', subtype: 'resistance', intensity: 6 }
        ]
      });
      
      const analysis = await engine.analyzeReactions('sihr', sihrReactions);
      
      expect(analysis.likelihood).toBe('high');
      expect(analysis.confidenceLevel).toBeGreaterThan(0.8);
      expect(analysis.keyIndicators).toContain('energy_drain_during_recitation');
    });

    it('should differentiate between spiritual illness types', async () => {
      const aynReactions = mockSpiritualReactions({
        spiritualIllnessType: 'ayn',
        reactions: [
          { type: 'emotional', subtype: 'anxiety', intensity: 6 },
          { type: 'physical', subtype: 'headache', intensity: 5 }
        ]
      });
      
      const analysis = await engine.analyzeReactions('ayn', aynReactions);
      
      expect(analysis.likelihood).toBe('moderate');
      expect(analysis.patternMatch).toContain('ayn_emotional_pattern');
    });

    it('should handle no significant reactions', async () => {
      const minimalReactions = mockSpiritualReactions({
        spiritualIllnessType: 'mass',
        reactions: [
          { type: 'spiritual', subtype: 'peace', intensity: 2 }
        ]
      });
      
      const analysis = await engine.analyzeReactions('mass', minimalReactions);
      
      expect(analysis.likelihood).toBe('none');
      expect(analysis.confidenceLevel).toBeGreaterThan(0.9);
    });
  });

  describe('Scholar Verification Integration', () => {
    it('should flag complex cases for scholar review', async () => {
      const complexReactions = mockSpiritualReactions({
        reactions: [
          { type: 'physical', intensity: 9, safety_concern: true },
          { type: 'spiritual', intensity: 8, unusual_pattern: true }
        ]
      });
      
      const analysis = await engine.analyzeReactions('sihr', complexReactions);
      
      expect(analysis.scholarReviewRequired).toBe(true);
      expect(analysis.reviewReason).toContain('high_intensity_reactions');
    });
  });
});
```

### **Frontend Unit Tests**

#### **Component Testing**
```typescript
// apps/mobile-app-v3/__tests__/unit/components/FiveLayerVisualization.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { FiveLayerVisualization } from '../../../src/components/assessment/Results/FiveLayerVisualization';
import { mockFiveLayerAnalysis } from '../../fixtures/assessment';

describe('FiveLayerVisualization Component', () => {
  const mockLayers = mockFiveLayerAnalysis({
    jism: { impactLevel: 'moderate', confidenceLevel: 'high' },
    nafs: { impactLevel: 'severe', confidenceLevel: 'high' },
    aql: { impactLevel: 'mild', confidenceLevel: 'medium' },
    qalb: { impactLevel: 'unknown', confidenceLevel: 'low', requiresDeeperAssessment: true },
    ruh: { impactLevel: 'unknown', confidenceLevel: 'low', requiresDeeperAssessment: true }
  });

  it('should render all five layers correctly', () => {
    const { getByText } = render(
      <FiveLayerVisualization layers={mockLayers} />
    );

    expect(getByText('الجسم')).toBeTruthy(); // Jism in Arabic
    expect(getByText('Body (Jism)')).toBeTruthy();
    expect(getByText('النفس')).toBeTruthy(); // Nafs in Arabic
    expect(getByText('الروح')).toBeTruthy(); // Ruh in Arabic
  });

  it('should display impact levels with correct colors', () => {
    const { getByText } = render(
      <FiveLayerVisualization layers={mockLayers} />
    );

    const severeIndicator = getByText('SEVERE');
    const moderateIndicator = getByText('MODERATE');
    
    expect(severeIndicator).toBeTruthy();
    expect(moderateIndicator).toBeTruthy();
  });

  it('should show deeper assessment indicators for spiritual layers', () => {
    const { getByText } = render(
      <FiveLayerVisualization layers={mockLayers} />
    );

    expect(getByText('Requires Spiritual Assessment')).toBeTruthy();
  });

  it('should handle animation prop correctly', () => {
    const { rerender } = render(
      <FiveLayerVisualization layers={mockLayers} animated={false} />
    );

    // Should render immediately without animation
    expect(true).toBeTruthy(); // Component renders without error

    rerender(
      <FiveLayerVisualization layers={mockLayers} animated={true} />
    );

    // Should handle animated rendering
    expect(true).toBeTruthy(); // Component renders without error
  });
});
```

#### **Islamic Content Validation**
```typescript
// apps/mobile-app-v3/__tests__/unit/utils/islamicValidation.test.ts
import { 
  validateArabicText, 
  validateQuranicReference, 
  validateIslamicContent 
} from '../../../src/utils/islamic/islamicValidation';

describe('Islamic Content Validation', () => {
  describe('Arabic Text Validation', () => {
    it('should validate correct Arabic text', () => {
      const arabicText = 'بسم الله الرحمن الرحيم';
      const result = validateArabicText(arabicText);
      
      expect(result.isValid).toBe(true);
      expect(result.hasArabicCharacters).toBe(true);
      expect(result.hasDiacritics).toBe(true);
    });

    it('should reject non-Arabic text', () => {
      const englishText = 'This is English text';
      const result = validateArabicText(englishText);
      
      expect(result.isValid).toBe(false);
      expect(result.hasArabicCharacters).toBe(false);
    });

    it('should handle mixed Arabic-English text appropriately', () => {
      const mixedText = 'الحمد لله (Alhamdulillah)';
      const result = validateArabicText(mixedText, { allowMixed: true });
      
      expect(result.isValid).toBe(true);
      expect(result.hasArabicCharacters).toBe(true);
      expect(result.hasMixedContent).toBe(true);
    });
  });

  describe('Quranic Reference Validation', () => {
    it('should validate correct Quranic references', () => {
      const validReferences = [
        { surah: 2, ayah: 255 }, // Ayat al-Kursi
        { surah: 1, ayah: 1 },   // Al-Fatiha
        { surah: 114, ayah: 6 }  // An-Nas
      ];

      validReferences.forEach(ref => {
        const result = validateQuranicReference(ref.surah, ref.ayah);
        expect(result.isValid).toBe(true);
      });
    });

    it('should reject invalid Quranic references', () => {
      const invalidReferences = [
        { surah: 0, ayah: 1 },    // Invalid surah
        { surah: 115, ayah: 1 },  // Surah doesn't exist
        { surah: 2, ayah: 0 },    // Invalid ayah
        { surah: 2, ayah: 300 }   // Ayah doesn't exist in surah
      ];

      invalidReferences.forEach(ref => {
        const result = validateQuranicReference(ref.surah, ref.ayah);
        expect(result.isValid).toBe(false);
      });
    });
  });

  describe('Islamic Content Validation', () => {
    it('should validate authentic Islamic content', () => {
      const islamicContent = {
        type: 'dua',
        arabic: 'اللهم اشفني',
        translation: 'O Allah, heal me',
        source: 'Authentic hadith collection',
        verification: 'scholar_verified'
      };

      const result = validateIslamicContent(islamicContent);
      
      expect(result.isValid).toBe(true);
      expect(result.isAuthentic).toBe(true);
      expect(result.scholarVerified).toBe(true);
    });

    it('should flag unverified Islamic content', () => {
      const unverifiedContent = {
        type: 'dua',
        arabic: 'دعاء غير موثق',
        translation: 'Unverified dua',
        source: 'Unknown source'
      };

      const result = validateIslamicContent(unverifiedContent);
      
      expect(result.isValid).toBe(false);
      expect(result.requiresVerification).toBe(true);
    });
  });
});
```

## 🔗 Integration Testing Strategy

### **API Integration Tests**

#### **Assessment Flow Integration**
```typescript
// apps/backend/__tests__/integration/assessment/assessmentFlow.test.ts
import request from 'supertest';
import { app } from '../../../src/main';
import { createTestUser, getAuthToken } from '../../helpers/auth';
import { cleanupTestData } from '../../helpers/cleanup';

describe('Assessment Flow Integration', () => {
  let authToken: string;
  let userId: string;
  let sessionId: string;

  beforeAll(async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      islamicPracticeLevel: 'moderate'
    });
    userId = testUser.id;
    authToken = await getAuthToken(testUser);
  });

  afterAll(async () => {
    await cleanupTestData(userId);
  });

  describe('Complete Assessment Journey', () => {
    it('should complete full assessment flow with Islamic context', async () => {
      // 1. Initialize assessment session
      const initResponse = await request(app)
        .post('/api/v1/assessment/sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sessionType: 'initial',
          userProfile: {
            ageRange: '26-35',
            gender: 'male',
            islamicPracticeLevel: 'moderate'
          }
        })
        .expect(200);

      expect(initResponse.body.status).toBe('success');
      expect(initResponse.body.data.sessionId).toBeDefined();
      expect(initResponse.body.data.firstQuestion.islamicContext).toBeDefined();
      
      sessionId = initResponse.body.data.sessionId;

      // 2. Submit responses through progressive disclosure
      let currentQuestion = initResponse.body.data.firstQuestion;
      let questionCount = 0;
      const maxQuestions = 30; // Safety limit

      while (currentQuestion && questionCount < maxQuestions) {
        const response = await request(app)
          .post(`/api/v1/assessment/sessions/${sessionId}/responses`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            questionId: currentQuestion.questionId,
            responseValue: {
              type: currentQuestion.questionType,
              value: generateAppropriateResponse(currentQuestion)
            },
            responseTime: Math.floor(Math.random() * 30) + 10
          })
          .expect(200);

        expect(response.body.status).toBe('success');
        
        // Check for crisis detection
        if (response.body.data.crisisDetection?.level !== 'none') {
          expect(response.body.data.crisisDetection.immediateSupport).toBeDefined();
        }

        currentQuestion = response.body.data.nextQuestion;
        questionCount++;

        // Break if assessment is complete
        if (response.body.data.progressInfo.completionPercentage >= 100) {
          break;
        }
      }

      expect(questionCount).toBeGreaterThan(8); // Minimum questions
      expect(questionCount).toBeLessThan(25); // Maximum questions

      // 3. Get assessment results
      const resultsResponse = await request(app)
        .get(`/api/v1/assessment/sessions/${sessionId}/results`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const results = resultsResponse.body.data;
      
      // Validate Five-Layer Analysis
      expect(results.fiveLayerAnalysis).toBeDefined();
      ['jism', 'nafs', 'aql', 'qalb', 'ruh'].forEach(layer => {
        expect(results.fiveLayerAnalysis[layer]).toBeDefined();
        expect(results.fiveLayerAnalysis[layer].impactLevel).toMatch(
          /none|mild|moderate|severe|critical|unknown/
        );
        expect(results.fiveLayerAnalysis[layer].confidenceLevel).toMatch(
          /low|medium|high/
        );
      });

      // Validate Tier 1 Recommendations
      expect(results.tier1Recommendations).toBeDefined();
      expect(results.tier1Recommendations.immediate_actions).toBeDefined();

      // Validate Spiritual Diagnosis Proposal
      expect(results.spiritualDiagnosisProposal).toBeDefined();
      expect(results.spiritualDiagnosisProposal.benefits).toBeInstanceOf(Array);

      // Validate Islamic Context
      expect(resultsResponse.body.meta.islamicContext).toBeDefined();
      expect(resultsResponse.body.meta.islamicContext.blessing).toBeDefined();
    });

    it('should handle crisis detection and intervention', async () => {
      // Initialize session
      const initResponse = await request(app)
        .post('/api/v1/assessment/sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sessionType: 'initial',
          userProfile: { islamicPracticeLevel: 'moderate' }
        });

      const crisisSessionId = initResponse.body.data.sessionId;

      // Submit crisis-indicating response
      const crisisResponse = await request(app)
        .post(`/api/v1/assessment/sessions/${crisisSessionId}/responses`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          questionId: 'q_crisis_001',
          responseValue: {
            type: 'boolean',
            value: true // Indicates suicidal thoughts
          },
          responseTime: 5
        })
        .expect(200);

      // Should detect crisis
      expect(crisisResponse.body.data.crisisDetection.level).toBe('critical');
      expect(crisisResponse.body.data.crisisDetection.immediateSupport).toBeDefined();

      // Should create crisis event
      const crisisEvent = await request(app)
        .post('/api/v1/crisis/events')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sessionId: crisisSessionId,
          crisisLevel: 'critical',
          triggerFactors: ['suicidal_ideation']
        })
        .expect(200);

      expect(crisisEvent.body.data.supportResources).toBeDefined();
      expect(crisisEvent.body.data.supportResources.emergency).toBeDefined();
      expect(crisisEvent.body.data.supportResources.spiritual).toBeDefined();
    });
  });

  function generateAppropriateResponse(question: any) {
    switch (question.questionType) {
      case 'scale':
        return Math.floor(Math.random() * (question.options.max - question.options.min + 1)) + question.options.min;
      case 'boolean':
        return Math.random() > 0.5;
      case 'multiple_choice':
        return question.options[Math.floor(Math.random() * question.options.length)].value;
      case 'frequency':
        return ['never', 'rarely', 'sometimes', 'often', 'always'][Math.floor(Math.random() * 5)];
      default:
        return 'Test response';
    }
  }
});
```

#### **Spiritual Diagnosis Integration**
```typescript
// apps/backend/__tests__/integration/spiritual/spiritualDiagnosis.test.ts
import request from 'supertest';
import { app } from '../../../src/main';
import { createTestUser, getAuthToken } from '../../helpers/auth';

describe('Spiritual Diagnosis Integration', () => {
  let authToken: string;
  let assessmentSessionId: string;

  beforeAll(async () => {
    const testUser = await createTestUser();
    authToken = await getAuthToken(testUser);
    
    // Create completed assessment session
    assessmentSessionId = await createCompletedAssessment(authToken);
  });

  describe('Complete Spiritual Diagnosis Flow', () => {
    it('should complete all four spiritual diagnosis sessions', async () => {
      const spiritualIllnesses = ['sihr', 'ayn', 'mass', 'waswas'];
      const sessionResults = [];

      for (const illnessType of spiritualIllnesses) {
        // Initialize spiritual diagnosis session
        const initResponse = await request(app)
          .post('/api/v1/spiritual/sessions')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            assessmentSessionId,
            spiritualIllnessType: illnessType,
            sessionNumber: spiritualIllnesses.indexOf(illnessType) + 1,
            preparationCompleted: {
              wudu: true,
              privateSpace: true,
              materials: ['water_glass'],
              spiritualReadiness: true
            }
          })
          .expect(200);

        const spiritualSessionId = initResponse.body.data.spiritualSessionId;
        
        // Validate session initialization
        expect(initResponse.body.data.audioContent).toBeDefined();
        expect(initResponse.body.data.instructions).toBeDefined();
        expect(initResponse.body.meta.islamicContext.blessing).toBeDefined();

        // Simulate spiritual diagnosis session with reactions
        const reactions = generateSpiritualReactions(illnessType);
        
        const reactionResponse = await request(app)
          .post(`/api/v1/spiritual/sessions/${spiritualSessionId}/reactions`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            sessionPhase: 'main_recitation',
            timestamp: new Date().toISOString(),
            reactions,
            overallIntensity: calculateOverallIntensity(reactions),
            sessionCompleted: true
          })
          .expect(200);

        // Validate reaction analysis
        expect(reactionResponse.body.data.sessionAnalysis).toBeDefined();
        expect(reactionResponse.body.data.sessionAnalysis.likelihood).toMatch(
          /none|low|moderate|high|very_high/
        );
        expect(reactionResponse.body.data.sessionAnalysis.confidenceLevel).toMatch(
          /low|medium|high/
        );

        sessionResults.push(reactionResponse.body.data.sessionAnalysis);
      }

      // Get complete spiritual diagnosis results
      const completeResults = await request(app)
        .get(`/api/v1/spiritual/sessions/${assessmentSessionId}/complete-results`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const results = completeResults.body.data;
      
      // Validate complete diagnosis
      expect(results.spiritualDiagnosisResults).toBeDefined();
      spiritualIllnesses.forEach(illness => {
        expect(results.spiritualDiagnosisResults[illness]).toBeDefined();
        expect(results.spiritualDiagnosisResults[illness].likelihood).toBeDefined();
        expect(results.spiritualDiagnosisResults[illness].confidenceLevel).toBeDefined();
      });

      expect(results.overallAssessment).toBeDefined();
      expect(results.overallAssessment.primarySpiritualIllness).toBeDefined();
    });

    it('should handle scholar review for complex cases', async () => {
      // Create session with high-intensity reactions requiring scholar review
      const initResponse = await request(app)
        .post('/api/v1/spiritual/sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          assessmentSessionId,
          spiritualIllnessType: 'sihr',
          sessionNumber: 1
        });

      const spiritualSessionId = initResponse.body.data.spiritualSessionId;

      // Submit high-intensity reactions
      const highIntensityReactions = {
        physical: {
          sensations: [
            { type: 'severe_pain', intensity: 9, location: 'chest' },
            { type: 'involuntary_movement', intensity: 8, location: 'hands' }
          ]
        },
        spiritual: {
          experiences: [
            { type: 'extreme_resistance', intensity: 9 },
            { type: 'unusual_phenomena', intensity: 8 }
          ]
        }
      };

      const reactionResponse = await request(app)
        .post(`/api/v1/spiritual/sessions/${spiritualSessionId}/reactions`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          reactions: highIntensityReactions,
          overallIntensity: 9,
          sessionCompleted: true
        })
        .expect(200);

      // Should flag for scholar review
      expect(reactionResponse.body.data.sessionAnalysis.scholarReviewRequired).toBe(true);
      expect(reactionResponse.body.data.supportResources.scholarConsultation).toBeDefined();
    });
  });

  function generateSpiritualReactions(illnessType: string) {
    const reactionPatterns = {
      sihr: {
        physical: [
          { type: 'tingling', intensity: 6, location: 'hands' },
          { type: 'pressure', intensity: 5, location: 'chest' }
        ],
        energy: [
          { type: 'drain', intensity: 7 }
        ],
        spiritual: [
          { type: 'resistance', intensity: 5 }
        ]
      },
      ayn: {
        emotional: [
          { type: 'anxiety', intensity: 4 }
        ],
        physical: [
          { type: 'headache', intensity: 3 }
        ]
      },
      mass: {
        spiritual: [
          { type: 'peace', intensity: 2 }
        ]
      },
      waswas: {
        emotional: [
          { type: 'confusion', intensity: 4 }
        ],
        spiritual: [
          { type: 'doubt', intensity: 3 }
        ]
      }
    };

    return reactionPatterns[illnessType as keyof typeof reactionPatterns] || {};
  }

  function calculateOverallIntensity(reactions: any): number {
    let totalIntensity = 0;
    let count = 0;

    Object.values(reactions).forEach((category: any) => {
      if (Array.isArray(category)) {
        category.forEach((reaction: any) => {
          totalIntensity += reaction.intensity;
          count++;
        });
      }
    });

    return count > 0 ? Math.round(totalIntensity / count) : 0;
  }
});
```

## 🎭 End-to-End Testing Strategy

### **Mobile App E2E Tests**

#### **Complete User Journey Testing**
```typescript
// apps/mobile-app-v3/__tests__/e2e/assessmentJourney.e2e.ts
import { by, device, element, expect } from 'detox';

describe('Islamic Mental Health Assessment E2E', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('Complete Assessment Journey', () => {
    it('should complete full assessment with Islamic context', async () => {
      // 1. Navigate to assessment
      await element(by.id('start-assessment-button')).tap();
      
      // 2. Verify Islamic greeting
      await expect(element(by.text('بسم الله الرحمن الرحيم'))).toBeVisible();
      await expect(element(by.text('In the name of Allah, the Most Gracious, the Most Merciful'))).toBeVisible();

      // 3. Complete user profile setup
      await element(by.id('age-range-selector')).tap();
      await element(by.text('26-35')).tap();
      
      await element(by.id('islamic-practice-level')).tap();
      await element(by.text('Moderate')).tap();
      
      await element(by.id('continue-button')).tap();

      // 4. Answer assessment questions
      let questionCount = 0;
      const maxQuestions = 25;

      while (questionCount < maxQuestions) {
        try {
          // Check if question is visible
          await expect(element(by.id('question-container'))).toBeVisible();
          
          // Check for Islamic context
          const islamicGuidance = element(by.id('islamic-guidance'));
          if (await islamicGuidance.exists()) {
            await expect(islamicGuidance).toBeVisible();
          }

          // Answer based on question type
          const scaleQuestion = element(by.id('scale-slider'));
          const multipleChoice = element(by.id('multiple-choice-option-0'));
          const booleanYes = element(by.id('boolean-yes'));

          if (await scaleQuestion.exists()) {
            await scaleQuestion.swipe('right', 'fast', 0.5);
          } else if (await multipleChoice.exists()) {
            await multipleChoice.tap();
          } else if (await booleanYes.exists()) {
            await booleanYes.tap();
          }

          // Submit response
          await element(by.id('submit-response-button')).tap();
          
          questionCount++;

          // Check if assessment is complete
          const resultsScreen = element(by.id('assessment-results-screen'));
          if (await resultsScreen.exists()) {
            break;
          }

          // Wait for next question to load
          await waitFor(element(by.id('question-container')))
            .toBeVisible()
            .withTimeout(5000);

        } catch (error) {
          // Assessment might be complete
          break;
        }
      }

      // 5. Verify results screen
      await expect(element(by.id('assessment-results-screen'))).toBeVisible();
      await expect(element(by.text('تحليل الطبقات الخمس للصحة الإسلامية'))).toBeVisible();
      await expect(element(by.text('Five-Layer Islamic Health Analysis'))).toBeVisible();

      // 6. Verify Five-Layer visualization
      await expect(element(by.id('layer-jism'))).toBeVisible();
      await expect(element(by.id('layer-nafs'))).toBeVisible();
      await expect(element(by.id('layer-aql'))).toBeVisible();
      await expect(element(by.id('layer-qalb'))).toBeVisible();
      await expect(element(by.id('layer-ruh'))).toBeVisible();

      // 7. Check Tier 1 recommendations
      await element(by.id('tier1-recommendations-tab')).tap();
      await expect(element(by.text('Immediate Islamic Actions'))).toBeVisible();

      // 8. Verify spiritual diagnosis proposal
      await element(by.id('spiritual-diagnosis-tab')).tap();
      await expect(element(by.text('Complete Your Healing Picture'))).toBeVisible();
      await expect(element(by.id('begin-spiritual-diagnosis-button'))).toBeVisible();
    });

    it('should handle crisis detection appropriately', async () => {
      // Start assessment
      await element(by.id('start-assessment-button')).tap();
      
      // Skip to crisis question (this would be done through test data)
      await element(by.id('test-crisis-scenario-button')).tap();
      
      // Trigger crisis response
      await element(by.id('crisis-response-yes')).tap();
      await element(by.id('submit-response-button')).tap();

      // Verify crisis intervention
      await expect(element(by.id('crisis-detected-screen'))).toBeVisible();
      await expect(element(by.text('Immediate Support Available'))).toBeVisible();
      
      // Check emergency resources
      await expect(element(by.id('emergency-hotline'))).toBeVisible();
      await expect(element(by.id('spiritual-support'))).toBeVisible();
      await expect(element(by.id('professional-help'))).toBeVisible();

      // Verify Islamic crisis support
      await expect(element(by.text('أعوذ بالله من الشيطان الرجيم'))).toBeVisible();
      await expect(element(by.id('emergency-duas'))).toBeVisible();
    });
  });

  describe('Spiritual Diagnosis Journey', () => {
    it('should complete spiritual diagnosis sessions', async () => {
      // Navigate to spiritual diagnosis (assuming assessment completed)
      await element(by.id('begin-spiritual-diagnosis-button')).tap();

      // Complete readiness check
      await expect(element(by.text('Spiritual Readiness Check'))).toBeVisible();
      await element(by.id('wudu-completed-checkbox')).tap();
      await element(by.id('private-space-checkbox')).tap();
      await element(by.id('materials-ready-checkbox')).tap();
      await element(by.id('begin-session-button')).tap();

      // Select first session (Sihr)
      await element(by.id('sihr-session-button')).tap();

      // Verify session setup
      await expect(element(by.text('السحر'))).toBeVisible(); // Sihr in Arabic
      await expect(element(by.text('Black Magic (Sihr)'))).toBeVisible();

      // Start preparation phase
      await element(by.id('begin-preparation-button')).tap();
      
      // Wait for audio to complete (simulated)
      await waitFor(element(by.id('main-recitation-button')))
        .toBeVisible()
        .withTimeout(10000);

      // Start main recitation
      await element(by.id('main-recitation-button')).tap();

      // Record reactions during session
      await element(by.id('add-reaction-button')).tap();
      await element(by.id('physical-reaction-tab')).tap();
      await element(by.id('tingling-sensation')).tap();
      await element(by.id('intensity-slider')).swipe('right', 'fast', 0.6);
      await element(by.id('save-reaction-button')).tap();

      // Complete session
      await element(by.id('complete-session-button')).tap();

      // Verify session results
      await expect(element(by.id('session-analysis-screen'))).toBeVisible();
      await expect(element(by.text('Session Analysis'))).toBeVisible();
    });
  });

  describe('Accessibility Testing', () => {
    it('should be accessible for users with disabilities', async () => {
      // Enable accessibility features
      await device.enableSynchronization();

      // Test screen reader compatibility
      await element(by.id('start-assessment-button')).tap();
      
      // Verify accessibility labels
      await expect(element(by.id('question-container'))).toHaveAccessibilityLabel();
      
      // Test keyboard navigation
      await device.pressKey('tab');
      await device.pressKey('enter');

      // Test high contrast mode
      await device.setOrientation('landscape');
      await expect(element(by.id('question-container'))).toBeVisible();
      await device.setOrientation('portrait');
    });

    it('should support Arabic RTL layout', async () => {
      // Switch to Arabic language
      await element(by.id('language-selector')).tap();
      await element(by.text('العربية')).tap();

      // Verify RTL layout
      await element(by.id('start-assessment-button')).tap();
      await expect(element(by.text('بدء التقييم'))).toBeVisible();
      
      // Verify Arabic text rendering
      await expect(element(by.text('بسم الله الرحمن الرحيم'))).toBeVisible();
    });
  });
});
```

## 🔒 Security Testing Strategy

### **Authentication & Authorization Testing**
```typescript
// apps/backend/__tests__/security/auth.security.test.ts
import request from 'supertest';
import { app } from '../../src/main';

describe('Security Testing', () => {
  describe('Authentication Security', () => {
    it('should prevent unauthorized access to assessment endpoints', async () => {
      await request(app)
        .post('/api/v1/assessment/sessions')
        .send({ sessionType: 'initial' })
        .expect(401);

      await request(app)
        .get('/api/v1/assessment/sessions/test-id/results')
        .expect(401);
    });

    it('should validate JWT tokens properly', async () => {
      const invalidToken = 'invalid.jwt.token';
      
      await request(app)
        .post('/api/v1/assessment/sessions')
        .set('Authorization', `Bearer ${invalidToken}`)
        .send({ sessionType: 'initial' })
        .expect(401);
    });

    it('should prevent access to other users\' data', async () => {
      const user1Token = await getAuthToken(await createTestUser());
      const user2Token = await getAuthToken(await createTestUser());
      
      // Create session with user1
      const sessionResponse = await request(app)
        .post('/api/v1/assessment/sessions')
        .set('Authorization', `Bearer ${user1Token}`)
        .send({ sessionType: 'initial' })
        .expect(200);

      const sessionId = sessionResponse.body.data.sessionId;

      // Try to access with user2 token
      await request(app)
        .get(`/api/v1/assessment/sessions/${sessionId}/results`)
        .set('Authorization', `Bearer ${user2Token}`)
        .expect(403);
    });
  });

  describe('Input Validation Security', () => {
    it('should prevent SQL injection attacks', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      const maliciousInput = "'; DROP TABLE assessment_sessions; --";
      
      await request(app)
        .post('/api/v1/assessment/sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sessionType: maliciousInput,
          userProfile: { islamicPracticeLevel: 'moderate' }
        })
        .expect(400);
    });

    it('should sanitize Arabic text input', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      const maliciousArabic = '<script>alert("xss")</script>النص العربي';
      
      const response = await request(app)
        .post('/api/v1/assessment/sessions/test-id/responses')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          questionId: 'q_text_001',
          responseValue: {
            type: 'text',
            textValue: maliciousArabic
          }
        });

      // Should sanitize but preserve Arabic text
      expect(response.body.data.sanitizedText).not.toContain('<script>');
      expect(response.body.data.sanitizedText).toContain('النص العربي');
    });

    it('should validate Islamic content authenticity', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      const fakeVerse = {
        arabic: 'آية مزيفة',
        translation: 'Fake verse',
        surah: 999,
        ayah: 999
      };
      
      await request(app)
        .post('/api/v1/islamic-content/verses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(fakeVerse)
        .expect(400);
    });
  });

  describe('Rate Limiting Security', () => {
    it('should enforce rate limits on assessment endpoints', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      // Make multiple rapid requests
      const requests = Array(150).fill(null).map(() =>
        request(app)
          .post('/api/v1/assessment/sessions')
          .set('Authorization', `Bearer ${authToken}`)
          .send({ sessionType: 'initial' })
      );

      const responses = await Promise.all(requests);
      
      // Should have some 429 (Too Many Requests) responses
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });
});
```

### **Data Privacy Testing**
```typescript
// apps/backend/__tests__/security/privacy.security.test.ts
describe('Data Privacy Security', () => {
  describe('Spiritual Data Protection', () => {
    it('should encrypt spiritual assessment data', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      // Submit spiritual diagnosis data
      const spiritualData = {
        reactions: {
          spiritual: [
            { type: 'resistance', intensity: 8 }
          ]
        }
      };

      await request(app)
        .post('/api/v1/spiritual/sessions/test-id/reactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(spiritualData);

      // Verify data is encrypted in database
      const dbRecord = await prisma.spiritualReactions.findFirst({
        where: { /* conditions */ }
      });

      expect(dbRecord.reactions).not.toEqual(spiritualData.reactions);
      // Should be encrypted
    });

    it('should audit all access to sensitive data', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      await request(app)
        .get('/api/v1/assessment/sessions/test-id/results')
        .set('Authorization', `Bearer ${authToken}`);

      // Check audit log
      const auditLog = await prisma.dataAccessLogs.findFirst({
        where: {
          tableName: 'assessment_sessions',
          operation: 'SELECT',
          containsSpiritualData: true
        }
      });

      expect(auditLog).toBeDefined();
      expect(auditLog.islamicPrivacyApplicable).toBe(true);
    });
  });

  describe('GDPR Compliance', () => {
    it('should support data export requests', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      const exportResponse = await request(app)
        .get('/api/v1/user/data-export')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(exportResponse.body.data.assessmentData).toBeDefined();
      expect(exportResponse.body.data.spiritualData).toBeDefined();
      expect(exportResponse.body.data.treatmentData).toBeDefined();
    });

    it('should support data deletion requests', async () => {
      const authToken = await getAuthToken(await createTestUser());
      
      await request(app)
        .delete('/api/v1/user/data')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify data is deleted
      const userData = await prisma.profiles.findFirst({
        where: { /* user conditions */ }
      });

      expect(userData).toBeNull();
    });
  });
});
```

## 📊 Performance Testing Strategy

### **Load Testing**
```typescript
// apps/backend/__tests__/performance/load.test.ts
import { check, sleep } from 'k6';
import http from 'k6/http';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.1'],     // Error rate under 10%
  },
};

export default function () {
  // Test assessment initialization
  let initResponse = http.post('http://localhost:3333/api/v1/assessment/sessions', 
    JSON.stringify({
      sessionType: 'initial',
      userProfile: {
        islamicPracticeLevel: 'moderate'
      }
    }), 
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${__ENV.TEST_TOKEN}`
      }
    }
  );

  check(initResponse, {
    'assessment init status is 200': (r) => r.status === 200,
    'assessment init response time < 2s': (r) => r.timings.duration < 2000,
    'has Islamic context': (r) => JSON.parse(r.body).meta.islamicContext !== undefined,
  });

  if (initResponse.status === 200) {
    let sessionId = JSON.parse(initResponse.body).data.sessionId;
    
    // Submit multiple responses
    for (let i = 0; i < 10; i++) {
      let responseData = {
        questionId: `q_${i.toString().padStart(3, '0')}`,
        responseValue: {
          type: 'scale',
          value: Math.floor(Math.random() * 10) + 1
        },
        responseTime: Math.floor(Math.random() * 30) + 10
      };

      let submitResponse = http.post(
        `http://localhost:3333/api/v1/assessment/sessions/${sessionId}/responses`,
        JSON.stringify(responseData),
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${__ENV.TEST_TOKEN}`
          }
        }
      );

      check(submitResponse, {
        'response submit status is 200': (r) => r.status === 200,
        'response submit time < 1s': (r) => r.timings.duration < 1000,
      });

      sleep(1); // Wait 1 second between responses
    }
  }

  sleep(1);
}
```

### **Stress Testing for Crisis Detection**
```typescript
// apps/backend/__tests__/performance/crisis.stress.test.ts
export let options = {
  stages: [
    { duration: '1m', target: 50 },   // Ramp up
    { duration: '3m', target: 50 },   // Stay at 50 concurrent crisis scenarios
    { duration: '1m', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<1000'], // Crisis detection must be under 1s
    http_req_failed: ['rate<0.01'],    // Error rate under 1% for crisis
  },
};

export default function () {
  // Simulate crisis detection scenario
  let crisisResponse = http.post('http://localhost:3333/api/v1/crisis/events',
    JSON.stringify({
      sessionId: 'test-session',
      crisisLevel: 'critical',
      triggerFactors: ['suicidal_ideation'],
      immediateNeeds: ['professional_intervention']
    }),
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${__ENV.TEST_TOKEN}`
      }
    }
  );

  check(crisisResponse, {
    'crisis detection status is 200': (r) => r.status === 200,
    'crisis response time < 1s': (r) => r.timings.duration < 1000,
    'has emergency resources': (r) => {
      let body = JSON.parse(r.body);
      return body.data.supportResources.emergency !== undefined;
    },
    'has spiritual support': (r) => {
      let body = JSON.parse(r.body);
      return body.data.supportResources.spiritual !== undefined;
    },
  });
}
```

## 🎭 Islamic Content Validation Testing

### **Scholar Review Integration Testing**
```typescript
// apps/backend/__tests__/islamic/scholarReview.test.ts
describe('Scholar Review Integration', () => {
  describe('Islamic Content Validation', () => {
    it('should validate Quranic verses with scholar verification', async () => {
      const quranicVerse = {
        surah: 2,
        ayah: 255,
        arabic: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ',
        translation: 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence.',
        transliteration: 'Allahu la ilaha illa huwa al-hayyu al-qayyum'
      };

      const validation = await validateQuranicContent(quranicVerse);
      
      expect(validation.isAuthentic).toBe(true);
      expect(validation.scholarVerified).toBe(true);
      expect(validation.source).toBe('Quran 2:255');
    });

    it('should flag non-authentic Islamic content', async () => {
      const suspiciousContent = {
        type: 'hadith',
        arabic: 'حديث مشكوك فيه',
        translation: 'Suspicious hadith',
        source: 'Unknown collection'
      };

      const validation = await validateIslamicContent(suspiciousContent);
      
      expect(validation.isAuthentic).toBe(false);
      expect(validation.requiresScholarReview).toBe(true);
      expect(validation.flaggedReasons).toContain('unverified_source');
    });

    it('should integrate with scholar review workflow', async () => {
      const contentForReview = {
        type: 'dua',
        arabic: 'دعاء جديد للمراجعة',
        translation: 'New dua for review',
        context: 'healing_prayer'
      };

      const reviewRequest = await submitForScholarReview(contentForReview);
      
      expect(reviewRequest.status).toBe('pending_review');
      expect(reviewRequest.assignedScholar).toBeDefined();
      expect(reviewRequest.reviewDeadline).toBeDefined();
    });
  });

  describe('Cultural Sensitivity Validation', () => {
    it('should validate cultural appropriateness', async () => {
      const culturalContent = {
        message: 'May Allah grant you healing and peace',
        context: 'assessment_completion',
        targetAudience: 'global_muslim_community'
      };

      const validation = await validateCulturalSensitivity(culturalContent);
      
      expect(validation.isAppropriate).toBe(true);
      expect(validation.culturalScore).toBeGreaterThan(0.8);
    });

    it('should flag culturally insensitive content', async () => {
      const insensitiveContent = {
        message: 'Just pray harder and you\'ll be fine',
        context: 'crisis_response'
      };

      const validation = await validateCulturalSensitivity(insensitiveContent);
      
      expect(validation.isAppropriate).toBe(false);
      expect(validation.issues).toContain('oversimplification_of_spiritual_practice');
    });
  });
});
```

## 📱 Accessibility Testing Strategy

### **Islamic Accessibility Testing**
```typescript
// apps/mobile-app-v3/__tests__/accessibility/islamic.accessibility.test.ts
describe('Islamic Accessibility Testing', () => {
  describe('Arabic Text Accessibility', () => {
    it('should support Arabic screen readers', async () => {
      await device.launchApp({
        languageAndLocale: {
          language: 'ar',
          locale: 'SA'
        }
      });

      await element(by.text('بدء التقييم')).tap();
      
      // Verify Arabic text is properly announced
      await expect(element(by.text('بسم الله الرحمن الرحيم')))
        .toHaveAccessibilityLabel('بسم الله الرحمن الرحيم');
    });

    it('should support RTL navigation', async () => {
      await device.setOrientation('portrait');
      
      // Test RTL swipe gestures
      await element(by.id('assessment-container')).swipe('left', 'fast');
      await expect(element(by.id('next-question'))).toBeVisible();
      
      await element(by.id('assessment-container')).swipe('right', 'fast');
      await expect(element(by.id('previous-question'))).toBeVisible();
    });
  });

  describe('Prayer Time Accessibility', () => {
    it('should announce prayer times for visually impaired users', async () => {
      await element(by.id('prayer-times-button')).tap();
      
      await expect(element(by.id('fajr-time')))
        .toHaveAccessibilityLabel('Fajr prayer time: 5:30 AM');
      
      await expect(element(by.id('maghrib-time')))
        .toHaveAccessibilityLabel('Maghrib prayer time: 6:45 PM');
    });
  });

  describe('Crisis Support Accessibility', () => {
    it('should provide accessible crisis intervention', async () => {
      // Simulate crisis detection
      await element(by.id('test-crisis-button')).tap();
      
      // Verify emergency button is accessible
      await expect(element(by.id('emergency-call-button')))
        .toHaveAccessibilityLabel('Call emergency hotline immediately')
        .toHaveAccessibilityHint('Double tap to call crisis support');
      
      // Verify spiritual support is accessible
      await expect(element(by.id('spiritual-support-button')))
        .toHaveAccessibilityLabel('Access spiritual crisis support')
        .toHaveAccessibilityHint('Provides Islamic guidance for crisis situations');
    });
  });
});
```

## 📊 Test Reporting and Metrics

### **Test Coverage Requirements**
```yaml
coverage_targets:
  backend:
    unit_tests:
      overall: 90%
      critical_paths: 95%
      crisis_detection: 98%
      spiritual_diagnosis: 92%
      islamic_content: 95%
    
    integration_tests:
      api_endpoints: 85%
      database_operations: 90%
      external_services: 80%
    
  frontend:
    component_tests:
      ui_components: 85%
      islamic_components: 90%
      accessibility: 80%
    
    e2e_tests:
      user_journeys: 75%
      crisis_scenarios: 90%
      spiritual_diagnosis: 80%

quality_gates:
  - name: "Crisis Detection Accuracy"
    threshold: 95%
    description: "Crisis detection must be 95% accurate"
  
  - name: "Islamic Content Authenticity"
    threshold: 100%
    description: "All Islamic content must be scholar-verified"
  
  - name: "Performance Standards"
    threshold: "p95 < 2s"
    description: "95% of requests under 2 seconds"
  
  - name: "Accessibility Compliance"
    threshold: "WCAG 2.1 AA"
    description: "Full accessibility compliance"
```

### **Continuous Testing Pipeline**
```yaml
# .github/workflows/testing.yml
name: Comprehensive Testing Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Backend Unit Tests
        run: |
          cd apps/backend
          npm test -- --coverage
          npm run test:islamic-content
      
      - name: Run Frontend Unit Tests
        run: |
          cd apps/mobile-app-v3
          npm test -- --coverage
          npm run test:accessibility

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - name: Run Integration Tests
        run: |
          cd apps/backend
          npm run test:integration
          npm run test:security

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run E2E Tests
        run: |
          cd apps/mobile-app-v3
          npm run test:e2e
          npm run test:accessibility:e2e

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Performance Tests
        run: |
          cd apps/backend
          npm run test:performance
          npm run test:load

  scholar-review:
    runs-on: ubuntu-latest
    if: contains(github.event.head_commit.message, '[islamic-content]')
    steps:
      - uses: actions/checkout@v3
      - name: Validate Islamic Content
        run: |
          npm run validate:islamic-content
          npm run submit:scholar-review
```

---

This comprehensive testing strategy ensures that Feature 01 meets the highest standards of quality, safety, cultural sensitivity, and accessibility while maintaining the authentic Islamic context that is central to the Qalb Healing platform.