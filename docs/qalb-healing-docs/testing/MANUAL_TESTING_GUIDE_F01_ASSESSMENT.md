# Manual Testing Guide: Feature 01 - Understanding Your Inner Landscape (Assessment)

**Version:** 1.0
**Date:** {{CURRENT_DATE}} <!-- Replace with actual date when finalizing -->

## 1. Introduction

This document outlines manual test cases for Feature 01: Understanding Your Inner Landscape, which involves the user taking a spiritual assessment and receiving a personalized diagnosis. These tests are intended to be performed on a deployed environment (staging or development) with integrated frontend, backend, and AI services.

## 2. Prerequisites

*   **Environment**: Access to a running instance of the Qalb Healing application (specify URL/version).
*   **User Accounts**: Test accounts representing different personas should be available or creatable:
    *   New Muslim
    *   Clinically Aware (e.g., healthcare professional profile)
    *   Symptom Aware (general user, default)
    *   Ruqya Expert
    *   Clinically Aware Spiritual Optimizer
    *   Symptom Aware Spiritual Optimizer (e.g., Imam/Community Leader)
    *   A general test user with minimal profile information.
*   **Onboarding Data (Feature 0)**: Ensure users have completed the onboarding process (Feature 0) as the assessment personalization relies on this profile data. Specific onboarding responses might be needed to trigger certain personas.
*   **Tools**: Web browser with developer tools for inspecting network requests (optional, for deeper debugging).

## 3. Test Case Structure

Each test case will include:
*   **Test ID**: Unique identifier (e.g., F01_MT_001)
*   **Persona(s)**: Target user persona(s) for this test.
*   **Description**: Brief overview of the test objective.
*   **Prerequisites**: Any specific setup beyond the general prerequisites.
*   **Steps**: Detailed steps to execute the test.
*   **Expected Results**: What should happen if the test passes.
*   **Actual Results**: (To be filled by the tester)
*   **Status**: (Pass/Fail - to be filled by the tester)
*   **Notes**: Any observations or issues.

## 4. Test Cases

---

### 4.1 Personalized Welcome and Assessment Start

**Test ID**: F01_MT_001
**Persona(s)**: Clinically Aware (Ahmed persona archetype)
**Description**: Verify that a Clinically Aware user receives the correct personalized welcome message and can start the assessment.
**Prerequisites**: User profile from onboarding indicates "clinical_aware" (e.g., based on profession, mental health familiarity responses from onboarding).
**Steps**:
1.  Log in as the Clinically Aware user.
2.  Navigate to the start of the Assessment feature.
3.  Observe the welcome screen content.
4.  Click the "Begin Assessment" button.
**Expected Results**:
1.  Welcome message should match themes for "Clinically Aware Users (Ahmed)" from the feature document (e.g., mentions dealing with anxiety, healthcare professional context, five layers).
2.  Buttons "Begin Assessment" and "Learn About 5 Layers First" should be visible.
3.  Clicking "Begin Assessment" should transition the user to the first step of the assessment questionnaire (e.g., Physical Experiences).
**Actual Results**:
**Status**:
**Notes**:

**Test ID**: F01_MT_002
**Persona(s)**: New Muslim
**Description**: Verify personalized welcome for a New Muslim and assessment start.
**Prerequisites**: User profile indicates "new_muslim" (e.g., based on onboarding responses about their Islamic journey).
**Steps**:
1.  Log in as the New Muslim user.
2.  Navigate to the start of the Assessment feature.
3.  Observe welcome screen.
4.  Click "Begin Assessment".
**Expected Results**:
1.  Welcome message should align with "New Muslim" persona themes (gentle, welcoming, foundational).
2.  Buttons "Begin Assessment", "Learn Islamic Wellness Basics", and "I Need Immediate Help" should be visible (or as per current UI spec for this persona).
3.  Assessment starts correctly.
**Actual Results**:
**Status**:
**Notes**:

**Test ID**: F01_MT_003
**Persona(s)**: Symptom Aware (Layla persona archetype)
**Description**: Verify personalized welcome for a Symptom Aware user and assessment start.
**Prerequisites**: User profile indicates "symptom_aware" (default or based on specific onboarding responses).
**Steps**:
1.  Log in as the Symptom Aware user.
2.  Navigate to the start of the Assessment feature.
3.  Observe welcome screen.
4.  Click "Begin Assessment".
**Expected Results**:
1.  Welcome message should align with "Symptom-Aware Users (Layla)" themes (e.g., feeling overwhelmed, exploring together, no pressure for specific terms).
2.  Buttons "Begin Assessment" and "I Need Immediate Help" should be visible.
3.  Assessment starts correctly.
**Actual Results**:
**Status**:
**Notes**:

**Test ID**: F01_MT_004
**Persona(s)**: Ruqya Expert (Ustadh Saeed persona archetype)
**Description**: Verify personalized welcome for a Ruqya Expert and assessment start options.
**Prerequisites**: User profile indicates "ruqya_expert".
**Steps**:
1.  Log in as the Ruqya Expert user.
2.  Navigate to the start of the Assessment feature.
3.  Observe welcome screen and available actions.
**Expected Results**:
1.  Welcome message aligns with "Ruqya Experts (Ustadh Saeed)" themes (e.g., acknowledging expertise, integrating with diagnostic approach).
2.  Action buttons like "Advanced Assessment", "Standard Assessment", "Ruqya-Focused Diagnosis", "I Need Immediate Help" should be present as per UI spec for this persona.
3.  Choosing an assessment option proceeds to the correct flow.
**Actual Results**:
**Status**:
**Notes**:

**Test ID**: F01_MT_005
**Persona(s)**: Clinically Aware Spiritual Optimizer (Dr. Fatima persona archetype)
**Description**: Verify welcome and options for Clinically Aware Spiritual Optimizer.
**Prerequisites**: User profile indicates "clinically_aware_spiritual_optimizer".
**Steps**:
1.  Log in as this user.
2.  Navigate to Assessment start.
3.  Observe welcome screen and actions.
**Expected Results**:
1.  Welcome message aligns with "Clinically Aware Spiritual Optimizers (Dr. Fatima)" themes (e.g., integrating clinical knowledge with Islamic spirituality, mapping Islamic psychology).
2.  Action buttons like "Clinical-Islamic Integration Assessment", "Research Mode", "Professional Development Focus", "I Need Immediate Help" as per UI spec.
**Actual Results**:
**Status**:
**Notes**:

**Test ID**: F01_MT_006
**Persona(s)**: Symptom Aware Spiritual Optimizer (Imam Abdullah persona archetype)
**Description**: Verify welcome and options for Symptom Aware Spiritual Optimizer.
**Prerequisites**: User profile indicates "symptom_aware_spiritual_optimizer".
**Steps**:
1.  Log in as this user.
2.  Navigate to Assessment start.
3.  Observe welcome screen and actions.
**Expected Results**:
1.  Welcome message aligns with "Symptom-Aware Spiritual Optimizers (Imam Abdullah)" themes (e.g., spiritual leadership, classical spiritual diseases, modern challenges).
2.  Action buttons like "Traditional-Modern Bridge Assessment", "Community Leadership Focus", "Spiritual Diseases Mapping", "I Need Immediate Help" as per UI spec.
**Actual Results**:
**Status**:
**Notes**:

---

### 4.2 Assessment Questionnaire Flow

**Test ID**: F01_MT_007
**Persona(s)**: Symptom Aware (or any general persona)
**Description**: Verify navigation, input, and data persistence (implicitly) through all assessment categories (Physical, Emotional, Mental, Spiritual).
**Prerequisites**: User has started the assessment.
**Steps**:
1.  Proceed to the "Physical Experiences" section.
2.  Select 2-3 symptoms (e.g., sleep difficulties, physical tension).
3.  Select an "Intensity Scale" option (e.g., Moderate).
4.  Enter a brief text in "Other physical experiences" if available and applicable.
5.  Enter text in the "Reflection" prompt for the physical section.
6.  Click "Next" (or equivalent action to proceed).
7.  Verify transition to the "Emotional Experiences" section.
8.  Repeat steps 2-6 for "Emotional Experiences", selecting different symptoms, intensity, and reflection.
9.  Verify transition to "Mental Experiences".
10. Repeat steps 2-6 for "Mental Experiences".
11. Verify transition to "Spiritual Experiences".
12. Repeat steps 2-6 for "Spiritual Experiences".
13. (If applicable) Test any "Back" button functionality:
    a. Navigate back from "Emotional" to "Physical".
    b. Verify previously selected symptoms, intensity, and reflection text for "Physical" are still present and correctly displayed.
    c. Navigate forward again.
**Expected Results**:
1.  User can select/deselect symptoms in each category.
2.  User can select intensity for each category.
3.  User can input text for reflections and "other experiences" fields.
4.  Navigation (Next/Back) works correctly, and data entered on previous steps is retained when navigating back and forth.
5.  Each section loads correctly and displays relevant questions/prompts as per the feature document.
**Actual Results**:
**Status**:
**Notes**:

---

### 4.3 Assessment Submission and Diagnosis Delivery

**Test ID**: F01_MT_008
**Persona(s)**: Clinically Aware
**Description**: Verify successful assessment submission and display of a personalized diagnosis report tailored for a Clinically Aware user.
**Prerequisites**: User (Clinically Aware persona) has completed all steps of the assessment questionnaire.
**Steps**:
1.  After the final assessment step (e.g., Spiritual Experiences or a final reflection page), click the "Submit" or "Get My Diagnosis" button.
2.  Observe any loading state.
3.  Once the diagnosis report is displayed, carefully review its components.
**Expected Results**:
1.  Assessment submits successfully without errors.
2.  A diagnosis report is displayed in a timely manner.
3.  **Personalized Message**: Aligns with themes for "Clinically Aware Users" (e.g., acknowledging their clinical understanding, bridging with Islamic framework).
4.  **Educational Content**: The five layers are explained. The language should be appropriate (e.g., using the "advanced user" intro for layers). The primary layer identified in the diagnosis should have its specific Islamic context included.
5.  **Islamic Insights**: Insights provided should be relevant and potentially resonate with a clinical background (e.g., holistic well-being).
6.  **Layer Analysis**: A primary layer is identified. Secondary layers (if any) are listed. Key insights and recommendations for the primary layer are shown.
7.  **Next Steps**: Clear, actionable next steps are provided, appropriate for this persona. Options should include "Create My Healing Journey".
8.  The overall tone and content should reflect the "Clinical-Islamic Integration" style if applicable, or generally be respectful of their existing knowledge.
**Actual Results**:
**Status**:
**Notes**:

**Test ID**: F01_MT_009
**Persona(s)**: New Muslim
**Description**: Verify successful assessment submission and diagnosis tailored for a New Muslim.
**Prerequisites**: User (New Muslim persona) has completed all assessment steps.
**Steps**:
1.  Submit the assessment.
2.  Review the diagnosis report.
**Expected Results**:
1.  Assessment submits successfully.
2.  Diagnosis report is displayed.
3.  **Personalized Message**: Content is gentle, encouraging, and references their journey in Islam.
4.  **Educational Content**: Uses the simpler introduction to the five layers (as implemented for "new_muslim" user type).
5.  **Islamic Insights**: Foundational, encouraging insights suitable for a new Muslim (e.g., about Allah's mercy, value of learning).
6.  **Next Steps**: Appropriate for a new Muslim (e.g., may emphasize foundational learning, community support).
**Actual Results**:
**Status**:
**Notes**:

**(Repeat similar diagnosis verification tests for Symptom Aware, Ruqya Expert, Clinically Aware Spiritual Optimizer, and Symptom Aware Spiritual Optimizer personas, checking for content tailored to them as per feature document examples and AI service capabilities for content adaptation.)**

---

### 4.4 Crisis Detection and Intervention Flow (User Experience)

**Test ID**: F01_MT_010
**Persona(s)**: Any
**Description**: Verify that inputting text with clear crisis keywords (e.g., related to self-harm, suicide) in a reflection field triggers a crisis intervention flow.
**Prerequisites**: User is in an assessment step with a reflection text input field.
**Steps**:
1.  In a reflection input field (e.g., during "Mental Experiences"), type a phrase containing strong crisis keywords (e.g., "I want to end my life and feel hopeless").
2.  Submit that assessment step or proceed in a way that would trigger analysis of the text.
**Expected Results**:
1.  The standard assessment flow should be interrupted.
2.  The user should be presented with a crisis warning message and/or UI elements.
3.  Information about emergency resources, Sakina mode, or options to contact support should be displayed, as per the application's defined crisis intervention flow.
4.  The system should not proceed to the next assessment step or generate a standard diagnosis at this point.
**Actual Results**:
**Status**:
**Notes**: **Perform with extreme caution in environments connected to live alerts. Use designated test phrases that are known to trigger the test environment's crisis flags without causing real-world escalations unless intended for that specific type of test.**

**Test ID**: F01_MT_011
**Persona(s)**: Any
**Description**: Verify that selecting an "I Need Immediate Help" button (if available on welcome or early assessment screens) correctly triggers the crisis flow.
**Prerequisites**: User is on a screen with an "I Need Immediate Help" option.
**Steps**:
1.  Click the "I Need Immediate Help" button.
**Expected Results**:
1.  User is immediately taken to the crisis intervention flow/screen.
2.  Appropriate crisis resources and support options are displayed.
**Actual Results**:
**Status**:
**Notes**:

---
### 4.5 Edge Cases and Basic Error Handling

**Test ID**: F01_MT_012
**Persona(s)**: Any
**Description**: Attempt to submit an assessment step with missing required input (e.g., intensity scale not selected, if the UI enforces this before proceeding).
**Prerequisites**: User is on an assessment step with clearly marked required fields.
**Steps**:
1.  Identify a required input (e.g., intensity scale).
2.  Do not provide a value for this required input.
3.  Attempt to proceed to the next step or submit the section.
**Expected Results**:
1.  A user-friendly validation message should appear, indicating the missing required field(s).
2.  The user should not be able to proceed until the required input is provided.
**Actual Results**:
**Status**:
**Notes**: This depends on client-side validation enforcement.

**Test ID**: F01_MT_013
**Persona(s)**: Any
**Description**: (If feasible to simulate) Test behavior during a brief network interruption when submitting the final assessment.
**Prerequisites**: User has completed all assessment steps.
**Steps**:
1.  Reach the final submission point.
2.  (If using browser dev tools) Simulate network going offline.
3.  Click the "Submit" or "Get My Diagnosis" button.
4.  Observe application behavior.
5.  (If applicable) Restore network and see if a retry is possible or if data is lost.
**Expected Results**:
1.  Ideally, a user-friendly error message about network issues ("Unable to connect, please check your internet connection and try again.").
2.  The application should handle the error gracefully. Best case: allows retry without data loss once connection is restored. Worst acceptable case (for this test): clear error, data might need re-entry for the last step or full re-submission if sessioning isn't robust to this.
**Actual Results**:
**Status**:
**Notes**: This is highly dependent on frontend implementation and may be difficult to test consistently manually.

---
**(Further test cases can be added for specific UI interactions, language changes if the application supports them, accessibility (a11y) checks, and performance under normal conditions.)**

```
