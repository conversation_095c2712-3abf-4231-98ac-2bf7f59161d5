
# Presentation: Building Qalb Healing - A Technical Deep Dive

**For Potential Co-Founders, Developers, and Technical Partners**

---

## Slide 1: Title Slide

**Building Qalb Healing: A Technical Deep Dive**

**An opportunity to build a scalable, AI-powered platform for a mission-driven purpose.**

*A code/architecture snippet graphic*

**Contact:** [Your Name/Founder]

---

## Slide 2: The Mission (for Engineers)

*   **The Challenge:** Can we build a platform that delivers authentic, personalized spiritual guidance at scale? Can we solve complex problems in a way that is both technically excellent and spiritually sound?
*   **The Goal:** To build the definitive platform for Islamic mental wellness, leveraging modern technology to solve a deeply human problem.
*   **Why it's Interesting:** This isn't another CRUD app. We're working with AI, personalization engines, cross-platform mobile development, and a unique, rich dataset. It's a chance to do meaningful work that presents significant technical challenges.

---

## Slide 3: System Architecture Overview

*Based on: `docs/qalb-healing-docs/technical/Technical_Architecture_and_Implementation.md`*

We use a modern, decoupled architecture for scalability and maintainability.

**(Diagram of the architecture)**

*   **Frontend (Mobile App):**
    *   **Framework:** Expo (React Native)
    *   **Why:** True cross-platform development (iOS, Android, Web) from a single codebase. Fast iteration with EAS builds and updates.
    *   **State Management:** React Context, TanStack Query for server state.

*   **Backend (API):**
    *   **Framework:** Node.js with Express.js (migrating towards NestJS for structure).
    *   **Language:** TypeScript
    *   **Why:** Robust, type-safe, and excellent for handling I/O-bound operations. The NestJS structure provides dependency injection, modularity, and clear separation of concerns.
    *   **ORM:** Prisma - for type-safe database access.

*   **AI Service:**
    *   **Framework:** Python with FastAPI
    *   **Why:** High-performance, asynchronous, and the standard for Python-based AI/ML model serving. Pydantic provides excellent data validation.

*   **Database & Auth:**
    *   **Provider:** Supabase (PostgreSQL)
    *   **Why:** Provides a powerful, scalable Postgres database, built-in authentication, storage, and auto-generated APIs, which allows us to move faster.

---

## Slide 4: Deep Dive: The Islamic AI Engine

**This is the core of our personalization.**

**How it Works:**

1.  **Data Collection (Onboarding):** The mobile app sends structured data from the user's assessment to the backend.
2.  **Backend Orchestration:** The Node.js backend receives the request, validates it, and orchestrates calls to the AI service.
3.  **AI Processing (Python/FastAPI):**
    *   The AI service receives the user's responses.
    *   **Processors (`SpiritualAnalysisProcessor`, `JourneyGenerationProcessor`):** These modules contain the business logic.
    *   **Prompt Engineering:** We have a sophisticated system for generating prompts for the OpenAI API (GPT-4), combining user data with our internal knowledge base of Qur'anic verses and scholarly interpretations.
    *   **Response Parsing & Structuring:** The AI service parses the LLM's response, structures it into a predictable JSON format, and enriches it with data from our own database (e.g., specific verse texts, audio file URLs).
4.  **Response Delivery:** The structured JSON is sent back to the backend, which then forwards it to the mobile app for rendering.

**Technical Challenge:** How do we ensure the AI's output is always Islamically authentic and safe? This involves rigorous prompt engineering, fallback mechanisms, and a potential future layer of scholar-in-the-loop verification.

---

## Slide 5: Feature Breakdown: "Qalb Rescue" Implementation

*Based on: `docs/qalb-healing-docs/Feature_03_Qalb_Rescue_Documentation_Pending.md`*

Let's look at our crisis intervention feature to see how the pieces fit together.

**(Sequence Diagram of the Qalb Rescue Flow)**

1.  **Activation (Client):** User taps the "Qalb Rescue" button.
2.  **Session Start (Backend):** `POST /api/emergency/start`
    *   The `EmergencyService` creates a new `EmergencySession` in the database (Prisma).
    *   It fetches the content for the *first step* ("Immediate Islamic Grounding").
    *   Returns the `sessionId` and the first step's content to the client.
3.  **Step Progression (Client -> Backend):** `POST /api/emergency/sessions/:id/next-step`
    *   Client sends the completed step and time spent.
    *   The backend updates the session log.
    *   The `EmergencyService` determines the next step.
    *   **For the "Quranic Comfort" step, it calls the AI Service** (`/content-personalization/recommend`) to get a relevant, personalized verse.
    *   The service has fallbacks: if the AI fails, it queries the DB for a high-quality verse; if that fails, it uses a hardcoded one.
4.  **Completion:** The session is marked as complete, and follow-up care suggestions are provided.

**This demonstrates our philosophy: backend-driven logic, client as a view layer, and robust fallbacks for critical features.**

---

## Slide 6: Database Schema Overview

*A simplified ERD showing key relationships*

*   `Users` (from Supabase Auth)
*   `Profiles` (1-to-1 with Users, contains basic info)
*   `UserProfileDetailed` (1-to-1 with Profiles, contains the rich, JSON-based profile from onboarding)
*   `OnboardingSession` (tracks the user's journey through the initial assessment)
*   `EmergencySession` (logs all data from Qalb Rescue sessions for analysis and improvement)
*   `HealingJourney` (defines a user's personalized plan)
*   `JourneyStep` (the individual daily steps within a journey)
*   `QuranVerse`, `Hadith`, etc. (our core Islamic content tables)

**We use Prisma for migrations and as our ORM, ensuring our database interactions are type-safe and easy to manage.**

---

## Slide 7: Development Roadmap & Sprints

**We have a clear, agile-based roadmap.**

*   **Current State:** MVP is nearing completion. Core features are functional. Backend and AI services are deployed.
*   **Next 3 Months (Post-Launch Refinement):**
    *   **Tech Debt:** Address items from `PENDING_TEST_AREAS.md`, improve test coverage across the board.
    *   **Performance:** Optimize API response times and app startup.
    *   **CI/CD:** Fully automate our build, test, and deployment pipelines using GitHub Actions and EAS.
*   **Next 6-12 Months (Expansion):**
    *   Build out the "Practical Self Ruqya" feature, which involves complex state management.
    *   Develop the B2B dashboard for partner organizations.
    *   Scale the AI engine to handle more complex personalization tasks.

---

## Slide 8: Our Technical Roadmap: Where You'll Make an Impact

*Based on: `docs/qalb-healing-docs/PENDING_TEST_AREAS.md`*

**This is where you come in. We have interesting problems to solve.**

*   **Project: Build our NLP-driven content verification system:** How can we fine-tune our own models for higher accuracy and lower cost? How do we build a robust testing framework for LLM-based outputs?
*   **Project: Design and implement our data analytics pipeline:** As we scale, how do we build a data pipeline to analyze user journeys (anonymously) to improve the effectiveness of our healing plans?
*   **Project: Scale our backend and AI services:** How do we ensure our backend and AI services can handle 1M+ users with low latency?
*   **Project: Ensure rock-solid security and privacy:** How do we ensure our user's most private reflections are stored with the highest level of security and privacy, adhering to our Islamic principle of *Amanah* (trust)?

---

## Slide 9: Our Engineering Culture

*   **Mission-Driven:** We are all here to serve a higher purpose. This motivates us to strive for excellence (*Ihsan*).
*   **Pragmatic & Agile:** We aim to ship quickly, learn from user feedback, and iterate. We are not afraid to refactor and improve.
*   **Documentation-First:** We believe in writing things down, as evidenced by our extensive project documentation. This helps us stay aligned and onboard new team members effectively.
*   **Ownership:** We want engineers who can take ownership of a feature from conception to deployment.
*   **Collaborative:** We are a small team, and we value open communication and mutual respect.

---

## Slide 10: Join the Mission

**This is a rare opportunity to:**

*   Work on a product that has a profound, positive impact on people's lives.
*   Solve challenging technical problems with a modern tech stack.
*   Get in on the ground floor of a startup with massive growth potential.
*   Build something that aligns with your values.

**We are looking for:**

*   A Technical Co-founder / Lead Engineer
*   React Native Developers
*   Backend (Node.js/Python) Engineers

**If you are passionate about building high-quality software for a meaningful cause, let's talk.**

**Contact:** [Your Name/Email]
**GitHub Repo:** [Link to a public-facing repo if available]

[Link to Engineering Culture Blog Post]
