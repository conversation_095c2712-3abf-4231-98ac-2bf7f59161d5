# Feature 3: Qalb Rescue - Mermaid Flows (Revised)

This document visualizes the revised detailed data and process flows for Feature 3: Qalb Rescue using Mermaid sequence diagrams, reflecting a backend-driven architecture.

## Flow 1: Qalb Rescue Activation

```mermaid
sequenceDiagram
    actor User
    participant <PERSON>A<PERSON> as "Mobile App (QalbRescueContext)"
    participant EmergencyAPIClient as "Mobile App (EmergencyService.ts)"
    participant BackendCtrl as "Backend (EmergencyController)"
    participant EmergencyServiceBE as "Backend (EmergencyService)"
    participant Database as "DB (Prisma)"

    User->>MobileApp: Initiates Qalb Rescue
    MobileApp->>EmergencyAPIClient: initiateQalbRescue(triggerData)
    activate EmergencyAPIClient
    EmergencyAPIClient->>BackendCtrl: POST /emergency/start (triggerData)
    activate BackendCtrl
    BackendCtrl->>EmergencyServiceBE: startQalbRescueSession(userId, triggerData)
    activate EmergencyServiceBE

    EmergencyServiceBE->>Database: Create EmergencySession (status: active, currentStep: 'grounding', log: [started])
    activate Database
    Database-->>EmergencyServiceBE: New Session Object (e.g., sessionData)
    deactivate Database

    EmergencyServiceBE->>EmergencyServiceBE: getStepContent('grounding', userId, sessionId)
    note right of EmergencyServiceBE: Fetches static content for 'grounding'
    EmergencyServiceBE-->>BackendCtrl: { session: sessionData, initialStepContent }
    deactivate EmergencyServiceBE

    BackendCtrl-->>EmergencyAPIClient: HTTP 200 OK { data: { sessionId, currentStep, stepContent } }
    deactivate BackendCtrl
    EmergencyAPIClient-->>MobileApp: Returns { sessionId, currentStep, stepContent }
    deactivate EmergencyAPIClient
    MobileApp->>MobileApp: Update State (sessionId, currentStepName, currentStepContent)
    MobileApp->>User: Display 'Grounding' Step UI
```

## Flow 2: Step Progression (e.g., from Grounding to Breathing)

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App (QalbRescueContext)"
    participant EmergencyAPIClient as "Mobile App (EmergencyService.ts)"
    participant BackendCtrl as "Backend (EmergencyController)"
    participant EmergencyServiceBE as "Backend (EmergencyService)"
    participant Database as "DB (Prisma)"

    User->>MobileApp: Completes 'Grounding' Step (e.g., taps Next)
    MobileApp->>EmergencyAPIClient: advanceToNextStep(sessionId, {completedStep: 'grounding'})
    activate EmergencyAPIClient
    EmergencyAPIClient->>BackendCtrl: POST /emergency/sessions/{sessionId}/next-step (payload)
    activate BackendCtrl
    BackendCtrl->>EmergencyServiceBE: progressQalbRescueSession(sessionId, userId, payload)
    activate EmergencyServiceBE

    EmergencyServiceBE->>Database: Get EmergencySession (to verify ownership, status)
    activate Database
    Database-->>EmergencyServiceBE: Session Details
    deactivate Database

    EmergencyServiceBE->>Database: Update EmergencySession (currentStep: 'breathing', log: [step_completed])
    activate Database
    Database-->>EmergencyServiceBE: Update Confirmed
    deactivate Database

    EmergencyServiceBE->>EmergencyServiceBE: getStepContent('breathing', userId, sessionId)
    activate EmergencyServiceBE
    EmergencyServiceBE->>Database: Query BreathingExercise table
    activate Database
    Database-->>EmergencyServiceBE: Breathing Exercise Data
    deactivate Database
    EmergencyServiceBE-->>EmergencyServiceBE: Formats StepContent for 'breathing'
    deactivate EmergencyServiceBE

    EmergencyServiceBE-->>BackendCtrl: { currentStep: 'breathing', nextStepContent }
    deactivate EmergencyServiceBE

    BackendCtrl-->>EmergencyAPIClient: HTTP 200 OK { data: { sessionId, currentStep, stepContent } }
    deactivate BackendCtrl
    EmergencyAPIClient-->>MobileApp: Returns { currentStep, stepContent }
    deactivate EmergencyAPIClient
    MobileApp->>MobileApp: Update State (currentStepName, currentStepContent)
    MobileApp->>User: Display 'Breathing' Step UI
```

## Flow 2b: Step Progression to 'Comfort' (with AI Call)

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App (QalbRescueContext)"
    participant EmergencyAPIClient as "Mobile App (EmergencyService.ts)"
    participant BackendCtrl as "Backend (EmergencyController)"
    participant EmergencyServiceBE as "Backend (EmergencyService)"
    participant Database as "DB (Prisma)"
    participant AIService as "AI Service"

    User->>MobileApp: Completes 'Breathing' Step (e.g., taps Next)
    MobileApp->>EmergencyAPIClient: advanceToNextStep(sessionId, {completedStep: 'breathing'})
    activate EmergencyAPIClient
    EmergencyAPIClient->>BackendCtrl: POST /emergency/sessions/{sessionId}/next-step (payload)
    activate BackendCtrl
    BackendCtrl->>EmergencyServiceBE: progressQalbRescueSession(sessionId, userId, payload)
    activate EmergencyServiceBE

    EmergencyServiceBE->>Database: Get EmergencySession (to verify, get symptoms/log for AI)
    activate Database
    Database-->>EmergencyServiceBE: Session Details
    deactivate Database
    EmergencyServiceBE->>Database: Get Profile (for user_preferences for AI)
    activate Database
    Database-->>EmergencyServiceBE: Profile Details
    deactivate Database

    EmergencyServiceBE->>Database: Update EmergencySession (currentStep: 'comfort', log: [step_completed])
    activate Database
    Database-->>EmergencyServiceBE: Update Confirmed
    deactivate Database

    EmergencyServiceBE->>EmergencyServiceBE: getStepContent('comfort', userId, sessionId)
    activate EmergencyServiceBE
    EmergencyServiceBE->>AIService: POST /content-personalization/recommend (AIContextPayload)
    activate AIService
    AIService-->>EmergencyServiceBE: Personalized Quran Verses (or empty/error)
    deactivate AIService

    alt AI Call Successful and returns content
        EmergencyServiceBE-->>EmergencyServiceBE: Formats AI response into StepContent
    else AI Call Fails or No Content
        EmergencyServiceBE->>Database: Query QuranVerseAiContent/RuqyahVerse table (fallback)
        activate Database
        Database-->>EmergencyServiceBE: Fallback Verse Data
        deactivate Database
        EmergencyServiceBE-->>EmergencyServiceBE: Formats fallback into StepContent
    end
    deactivate EmergencyServiceBE

    EmergencyServiceBE-->>BackendCtrl: { currentStep: 'comfort', nextStepContent }
    deactivate EmergencyServiceBE

    BackendCtrl-->>EmergencyAPIClient: HTTP 200 OK { data: { sessionId, currentStep, stepContent } }
    deactivate BackendCtrl
    EmergencyAPIClient-->>MobileApp: Returns { currentStep, stepContent }
    deactivate EmergencyAPIClient
    MobileApp->>MobileApp: Update State (currentStepName, currentStepContent)
    MobileApp->>User: Display 'Comfort' Step UI with personalized/fallback verses
```

## Flow 3: Community Connection (Request Du'a - during 'connection' step)

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App (QalbRescueContext)"
    participant EmergencyAPIClient as "Mobile App (EmergencyService.ts)"
    participant BackendCtrl as "Backend (EmergencyController)"
    participant EmergencyServiceBE as "Backend (EmergencyService)"
    participant Database as "DB (Prisma)"
    participant NotificationSystem as "Notification System (Conceptual)"

    User->>MobileApp: Taps "Request Du'a"
    MobileApp->>EmergencyAPIClient: triggerRequestDua(sessionId)
    activate EmergencyAPIClient
    EmergencyAPIClient->>BackendCtrl: POST /emergency/sessions/{sessionId}/request-dua
    activate BackendCtrl
    BackendCtrl->>EmergencyServiceBE: requestDuaFromCommunity(sessionId, userId)
    activate EmergencyServiceBE

    EmergencyServiceBE->>Database: Create DuaRequestLog entry
    activate Database
    Database-->>EmergencyServiceBE: Log Creation Confirmed
    deactivate Database

    EmergencyServiceBE->>EmergencyServiceBE: _logEvent(sessionId, 'dua_requested_from_community', ...)

    opt Future Notification
      EmergencyServiceBE->>NotificationSystem: Trigger Du'a Broadcast (userId, sessionId)
      activate NotificationSystem
      NotificationSystem-->>EmergencyServiceBE: Broadcast Acknowledged (async)
      deactivate NotificationSystem
    end

    EmergencyServiceBE-->>BackendCtrl: Success
    deactivate EmergencyServiceBE
    BackendCtrl-->>EmergencyAPIClient: HTTP 200 OK { message: "Du'a request initiated." }
    deactivate BackendCtrl
    EmergencyAPIClient-->>MobileApp: Success
    deactivate EmergencyAPIClient
    MobileApp->>MobileApp: Update UI (e.g., button state to "Du'a Requested")
    MobileApp->>User: Show Confirmation
```

## Flow 4: Community Connection (Connect Peer Supporter - during 'connection' step)

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App (QalbRescueContext)"
    participant EmergencyAPIClient as "Mobile App (EmergencyService.ts)"
    participant BackendCtrl as "Backend (EmergencyController)"
    participant EmergencyServiceBE as "Backend (EmergencyService)"
    participant Database as "DB (Prisma)"
    participant NotificationSystem as "Notification System (Conceptual)"

    User->>MobileApp: Taps "Talk to a Peer Supporter"
    MobileApp->>EmergencyAPIClient: triggerConnectPeer(sessionId, criteria?)
    activate EmergencyAPIClient
    EmergencyAPIClient->>BackendCtrl: POST /emergency/sessions/{sessionId}/connect-peer (criteria?)
    activate BackendCtrl
    BackendCtrl->>EmergencyServiceBE: findPeerSupporter(sessionId, userId, criteria?)
    activate EmergencyServiceBE

    EmergencyServiceBE->>Database: Query Profile table (for available supporters)
    activate Database
    Database-->>EmergencyServiceBE: List of available supporters (or empty)
    deactivate Database

    alt Supporter Found
        EmergencyServiceBE->>Database: Create PeerSupportRequest (status: 'pending_acceptance')
        activate Database
        Database-->>EmergencyServiceBE: New PeerSupportRequest object
        deactivate Database
        EmergencyServiceBE->>EmergencyServiceBE: _logEvent(sessionId, 'peer_supporter_matched...', ...)
        opt Future Notification
            EmergencyServiceBE->>NotificationSystem: Notify Selected Peer Supporter (supporterId, requestId)
            activate NotificationSystem
            NotificationSystem-->>EmergencyServiceBE: Notification Acknowledged (async)
            deactivate NotificationSystem
        end
        EmergencyServiceBE-->>BackendCtrl: { status: 'pending_acceptance', message: '...', peerSupportRequestId, supporterDetails }
    else No Supporter Found
        EmergencyServiceBE->>EmergencyServiceBE: _logEvent(sessionId, 'peer_support_unavailable', ...)
        EmergencyServiceBE-->>BackendCtrl: { status: 'unavailable', message: '...' }
    end
    deactivate EmergencyServiceBE

    BackendCtrl-->>EmergencyAPIClient: HTTP 200 OK { data: { status, message, ... } }
    deactivate BackendCtrl
    EmergencyAPIClient-->>MobileApp: Connection Status Response
    deactivate EmergencyAPIClient
    MobileApp->>MobileApp: Update UI based on connection status
    MobileApp->>User: Show Connection Status/Supporter Info
```

## Flow 5: Session Completion and Feedback

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App (QalbRescueContext)"
    participant EmergencyAPIClient as "Mobile App (EmergencyService.ts)"
    participant BackendCtrl as "Backend (EmergencyController)"
    participant EmergencyServiceBE as "Backend (EmergencyService)"
    participant Database as "DB (Prisma)"

    User->>MobileApp: Taps "Finish Session" (or completes last step)
    MobileApp->>MobileApp: (Optional) Collects feedback (rating, text)
    MobileApp->>EmergencyAPIClient: submitFeedbackAndEndSession(sessionId, payload)
    activate EmergencyAPIClient
    EmergencyAPIClient->>BackendCtrl: PATCH /emergency/sessions/{sessionId} (payload with status, feedback)
    activate BackendCtrl
    BackendCtrl->>EmergencyServiceBE: Handles request (calls recordUserFeedback, endQalbRescueSession)
    activate EmergencyServiceBE

    opt Feedback Provided
        EmergencyServiceBE->>Database: Update EmergencySession (feedback, effectivenessRating)
        activate Database
        Database-->>EmergencyServiceBE: Update Confirmed
        deactivate Database
        EmergencyServiceBE->>EmergencyServiceBE: _logEvent(sessionId, 'feedback_recorded', ...)
    end

    EmergencyServiceBE->>Database: Update EmergencySession (status: 'completed'/'aborted', endTime)
    activate Database
    Database-->>EmergencyServiceBE: Update Confirmed
    deactivate Database
    EmergencyServiceBE->>EmergencyServiceBE: _logEvent(sessionId, 'session_ended', ...)

    EmergencyServiceBE-->>BackendCtrl: Success
    deactivate EmergencyServiceBE
    BackendCtrl-->>EmergencyAPIClient: HTTP 200 OK { message: "Session updated successfully." }
    deactivate BackendCtrl
    EmergencyAPIClient-->>MobileApp: Success
    deactivate EmergencyAPIClient
    MobileApp->>MobileApp: Clear session state, navigate user
    MobileApp->>User: Display closing message / Navigate to Home
```
This provides updated Mermaid diagrams reflecting the new backend-driven architecture.
