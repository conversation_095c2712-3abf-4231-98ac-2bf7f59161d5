# Feature 3: Qalb Rescue - Detailed Flows (Revised)

This document outlines the revised detailed data and process flows for Feature 3: Qalb Rescue, reflecting a backend-driven architecture using Prisma, and integration with an AI service for content personalization.

## Key Components Involved:

*   **Mobile App (`apps/mobile-app-v3`):** User interface for activating and progressing through the Qalb Rescue sequence. Uses `QalbRescueContext` for state management and `EmergencyService.ts` (API client) for backend communication.
*   **Backend (`apps/backend`):**
    *   `emergency.controller.ts`: Handles HTTP requests for Qalb Rescue at `/emergency/...` routes.
    *   `emergency.service.ts`: Core logic for managing Qalb Rescue sessions (using Prisma client), fetching/personalizing step content (including calls to AI service), and orchestrating community connections.
    *   `Prisma Client`: ORM for interacting with the PostgreSQL database (models: `EmergencySession`, `DuaRequestLog`, `PeerSupportRequest`, `Profile`, `QuranVerseAiContent`, `DhikrAiContent`, `BreathingExercise`).
    *   `NotificationService` (Conceptual): Would be responsible for sending actual push notifications for Du'a requests and peer support. (Implementation details for notifications are TBD).
*   **AI Service (`apps/ai-service`):**
    *   `content_personalization.py` endpoint: Called by the backend's `EmergencyService` to get personalized Quranic verses (and potentially Dhikr) for the 'comfort' step.
    *   `db_utils.py`: Allows AI service to fetch its content corpus from `QuranVerseAiContent` and `DhikrAiContent` tables.

## Detailed Flows:

### 1. Qalb Rescue Activation Flow

**Actors:** User, Mobile App, Backend (`emergency.controller.ts`, `emergency.service.ts`)

**Trigger:** User initiates Qalb Rescue from the Mobile App.

**Steps:**

1.  **User Action (Mobile App):**
    *   User activates the feature (e.g., taps "Qalb Rescue" button).
2.  **Mobile App Request (`EmergencyService.ts` -> `initiateQalbRescue` in context):**
    *   Sends a request to the backend to start a new Qalb Rescue session.
    *   **Endpoint:** `POST /emergency/start`
    *   **Payload:**
        ```json
        {
          "triggerType": "manual", // e.g., 'manual', 'automatic_detection'
          "currentSymptoms": ["anxiety", "feeling_overwhelmed"] // Optional
        }
        ```
    *   **Headers:** Includes authentication token.
3.  **Backend - `emergency.controller.ts` (`startQalbRescueSessionController`):**
    *   Receives the request. Validates input.
    *   Extracts `userId` from the auth token.
    *   Calls `emergencyService.startQalbRescueSession(userId, triggerType, currentSymptoms)`.
4.  **Backend - `emergency.service.ts` (`startQalbRescueSession`):**
    *   Sets `initialStep` to 'grounding'.
    *   Creates a new `EmergencySession` record in the database using Prisma:
        *   `userId`, `triggerType`, `currentSymptoms`, `startTime`, `status: 'active'`, `currentStep: 'grounding'`.
        *   Initializes `log` with a `session_started` event.
    *   Calls `this.getStepContent('grounding', userId, newSession.id)` to fetch content for the first step.
    *   Returns the created `session` object and `initialStepContent`.
5.  **Backend - Response Handling (`emergency.controller.ts`):**
    *   Sends a JSON response to the mobile app.
    *   **Response Body (Success 201):**
        ```json
        {
          "status": "success",
          "data": {
            "sessionId": "generated-session-id",
            "startTime": "iso-timestamp",
            "currentStep": "grounding",
            "stepContent": { /* ... content for grounding step ... */ }
          }
        }
        ```
6.  **Mobile App - Display First Step (`QalbRescueContext` updates state, `QalbRescueScreen` re-renders):**
    *   Receives `sessionId`, `currentStep`, and `stepContent`.
    *   Stores this in its state (e.g., via `QalbRescueContext`).
    *   Displays the UI for the 'grounding' step based on `stepContent`.

### 2. Qalb Rescue Step Progression Flow

**Actors:** User, Mobile App, Backend (`emergency.controller.ts`, `emergency.service.ts`), AI Service (for 'comfort' step)

**Trigger:** User completes the current step and taps "Next" (or equivalent action) in the Mobile App.

**Steps:**

1.  **Mobile App Action (`QalbRescueScreen` -> `advanceToNextStep` in context):**
    *   The current step (e.g., 'grounding') is considered complete.
    *   Optionally records `timeSpentOnStep`.
2.  **Mobile App Request (`EmergencyService.ts`):**
    *   Notifies the backend of the progression and requests content for the next step.
    *   **Endpoint:** `POST /emergency/sessions/{sessionId}/next-step`
    *   **Path Parameter:** `sessionId`
    *   **Payload:**
        ```json
        {
          "completedStep": "grounding", // Name of the step just finished
          "timeSpentOnStep": 30000 // Optional, in milliseconds
        }
        ```
3.  **Backend - `emergency.controller.ts` (`progressQalbRescueSessionController`):**
    *   Receives the request, validates `sessionId` and payload.
    *   Extracts `userId` from auth token.
    *   Calls `emergencyService.progressQalbRescueSession(sessionId, userId, completedStep, timeSpentOnStep)`.
4.  **Backend - `emergency.service.ts` (`progressQalbRescueSession`):**
    *   Retrieves the `EmergencySession` using Prisma to verify ownership and status.
    *   Determines the `nextStep` in the `QALB_RESCUE_STEPS` sequence.
    *   If `completedStep` was the last step ('connection'), calls `this.endQalbRescueSession(...)` and returns an indicator of completion.
    *   Updates the `EmergencySession` record in the database (Prisma): sets new `currentStep`, appends to `log` (with `completedStep`, `timeSpentOnStep`, `nextStep`).
    *   Calls `this.getStepContent(nextStep, userId, sessionId)` to fetch content for the `nextStep`.
        *   **`getStepContent` internal logic:**
            *   **For 'comfort' step:**
                1.  Calls `getAIContext()` to fetch user preferences (from `Profile`) and session symptoms/log (from `EmergencySession`).
                2.  Makes an HTTP POST request to AI Service (`/content-personalization/recommend`) with this context.
                3.  If AI service responds successfully with Quran verses: formats them into `StepContent.items`.
                4.  If AI service fails or returns no verses: Falls back to querying `RuqyahVerse` table (Prisma) for category 'emergency_comfort'.
                5.  If DB fallback also fails: Uses hardcoded default verses.
            *   **For 'breathing' step:** Queries `BreathingExercise` table (Prisma) for "Qalb Rescue Dhikr Breathing". Includes fixed Dhikr text.
            *   **For 'grounding', 'reflection', 'connection' steps:** Returns statically defined content structure (titles, descriptions, interactive element definitions for 'connection').
    *   Returns the `nextStep` name and its `nextStepContent`.
5.  **Backend - Response Handling (`emergency.controller.ts`):**
    *   If session completed, returns a success message.
    *   Otherwise, returns a JSON response with `sessionId`, `currentStep` (the new step), and `stepContent`.
    *   **Response Body (Success 200 - Next Step):**
        ```json
        {
          "status": "success",
          "data": {
            "sessionId": "session-id",
            "currentStep": "breathing",
            "stepContent": { /* ... content for breathing step ... */ }
          }
        }
        ```
    *   **Response Body (Success 200 - Session Completed):**
        ```json
        {
          "status": "success",
          "message": "Qalb Rescue session completed.",
          "data": {
            "sessionId": "session-id",
            "currentStep": null,
            "stepContent": null
          }
        }
        ```
6.  **Mobile App - Display Next Step (`QalbRescueContext` updates, `QalbRescueScreen` re-renders):**
    *   If session completed, navigates away or shows completion UI.
    *   Otherwise, updates its state with the new `currentStep` and `stepContent`.
    *   Displays the UI for the new step.

### 3. Community Connection Flows (During 'connection' Step)

#### A. Request Du'a

**Actors:** User, Mobile App, Backend (`emergency.controller.ts`, `emergency.service.ts`)

**Trigger:** User taps "Request Du'a from Community" button in the 'connection' step.

**Steps:**

1.  **Mobile App Action (`QalbRescueScreen` -> `triggerRequestDua` in context):**
    *   Initiates the Du'a request.
2.  **Mobile App Request (`EmergencyService.ts`):**
    *   **Endpoint:** `POST /emergency/sessions/{sessionId}/request-dua`
    *   **Path Parameter:** `sessionId`
3.  **Backend - `emergency.controller.ts` (`requestDuaController`):**
    *   Receives request, validates `sessionId`.
    *   Calls `emergencyService.requestDuaFromCommunity(sessionId, userId)`.
4.  **Backend - `emergency.service.ts` (`requestDuaFromCommunity`):**
    *   Creates a record in `DuaRequestLog` table (Prisma) linking `sessionId` and `userId`.
    *   Logs `dua_requested_from_community` event to `EmergencySession.log`.
    *   (Future Enhancement) Triggers a notification to the community (e.g., via N8N or a dedicated notification service).
5.  **Backend - Response Handling:**
    *   Returns a success confirmation.
    *   **Response Body (Success 200):**
        ```json
        {
          "status": "success",
          "message": "Du'a request initiated."
        }
        ```
6.  **Mobile App - UI Update:**
    *   Displays confirmation (e.g., "Du'a Requested!"). Updates button state.

#### B. Connect with Peer Supporter

**Actors:** User, Mobile App, Backend (`emergency.controller.ts`, `emergency.service.ts`)

**Trigger:** User taps "Talk to a Peer Supporter" button in the 'connection' step.

**Steps:**

1.  **Mobile App Action (`QalbRescueScreen` -> `triggerConnectPeer` in context):**
    *   Initiates peer support connection. Optionally passes `criteria`.
2.  **Mobile App Request (`EmergencyService.ts`):**
    *   **Endpoint:** `POST /emergency/sessions/{sessionId}/connect-peer`
    *   **Path Parameter:** `sessionId`
    *   **Payload (Optional):**
        ```json
        {
          "criteria": { "language": "en" /* ... other criteria ... */ }
        }
        ```
3.  **Backend - `emergency.controller.ts` (`connectPeerSupporterController`):**
    *   Receives request, validates `sessionId` and `criteria`.
    *   Calls `emergencyService.findPeerSupporter(sessionId, userId, criteria)`.
4.  **Backend - `emergency.service.ts` (`findPeerSupporter`):**
    *   Queries `Profile` table (Prisma) for available peer supporters (matching `isPeerSupporter=true`, `isAvailableForSupport=true`, and optional `criteria`). Excludes the requesting user.
    *   If a supporter is found:
        *   Creates a `PeerSupportRequest` record (Prisma) with `status: 'pending_acceptance'`, linking `sessionId`, `userId`, `supporterId`.
        *   (Future Enhancement) Triggers a notification to the matched supporter.
        *   Logs `peer_supporter_matched_pending_notification` to `EmergencySession.log`.
        *   Returns status 'pending_acceptance', `peerSupportRequestId`, and basic supporter details.
    *   If no supporter found:
        *   Logs `peer_support_unavailable` to `EmergencySession.log`.
        *   Returns status 'unavailable'.
5.  **Backend - Response Handling:**
    *   **Response Body (Success 200 - Supporter Found/Pending):**
        ```json
        {
          "status": "success",
          "data": {
            "status": "pending_acceptance", // or 'supporter_found' if notifications were instant
            "message": "Peer supporter found and notified. Waiting for acceptance.",
            "peerSupportRequestId": "generated-request-id",
            "supporterDetails": { "id": "supporter-id", "name": "Supporter Name" }
          }
        }
        ```
    *   **Response Body (Success 200 - No Supporter):**
        ```json
        {
          "status": "success",
          "data": {
            "status": "unavailable",
            "message": "No peer supporters are currently available."
          }
        }
        ```
6.  **Mobile App - UI Update:**
    *   Displays status (e.g., "Connecting...", "Peer Supporter Found: [Name]", "No supporters available.").

### 4. Session Completion and Feedback Flow

**Actors:** User, Mobile App, Backend (`emergency.controller.ts`, `emergency.service.ts`)

**Trigger:** User completes all steps (progression flow leads to completion) or manually chooses to exit and provide feedback.

**Steps:**

1.  **Mobile App Action (`QalbRescueScreen` -> `submitFeedbackAndEndSession` in context):**
    *   User provides optional feedback (rating, text).
    *   Indicates session completion or abortion.
2.  **Mobile App Request (`EmergencyService.ts`):**
    *   **Endpoint:** `PATCH /emergency/sessions/{sessionId}`
    *   **Path Parameter:** `sessionId`
    *   **Payload:**
        ```json
        {
          "status": "completed", // or "aborted"
          "feedback": "This was very helpful, Alhumdulillah.", // Optional
          "effectivenessRating": 5, // Optional (1-5)
          "reasonForEnding": "Felt calmer and decided to stop." // Optional, if aborted
        }
        ```
3.  **Backend - `emergency.controller.ts` (`updateQalbRescueSessionController`):**
    *   Receives request, validates `sessionId` and payload.
    *   Calls `emergencyService.recordUserFeedback(...)` if feedback is present.
    *   Calls `emergencyService.endQalbRescueSession(...)` if `status` is 'completed' or 'aborted'.
4.  **Backend - `emergency.service.ts`:**
    *   **`recordUserFeedback`**: Updates `EmergencySession` (Prisma) with `feedback` and `effectivenessRating`. Logs event.
    *   **`endQalbRescueSession`**: Updates `EmergencySession` (Prisma) `status` to 'completed'/'aborted' and sets `endTime`. Logs event.
5.  **Backend - Response Handling:**
    *   Returns a success confirmation.
    *   **Response Body (Success 200):**
        ```json
        {
          "status": "success",
          "data": {
            "sessionId": "session-id",
            "message": "Session updated successfully."
          }
        }
        ```
6.  **Mobile App - UI Update:**
    *   Clears session state.
    *   Navigates user away from Qalb Rescue screen (e.g., to home or a summary page).

## Data Structures (Key Models/Interfaces - Revised)

*   **Backend `EmergencySession` (Prisma Model - Key Fields):**
    ```typescript
    model EmergencySession {
      id                  String    @id @default(uuid()) // Assuming uuid
      userId              String    @map("user_id")
      triggerType         String    @map("trigger_type")
      currentStep         String?   @map("current_step") // 'grounding', 'breathing', etc.
      currentSymptoms     String[]  @map("current_symptoms")
      startTime           DateTime  @map("start_time")
      endTime             DateTime? @map("end_time")
      status              String    // 'active', 'completed', 'aborted', 'escalated'
      feedback            String?
      effectivenessRating Int?      @map("effectiveness_rating")
      log                 Json?     // For storing event timestamps and details
      // ... other fields like escalationReason, recommendedActions, etc.
      // Relations:
      // user Profile
      // duaRequestLogs DuaRequestLog[]
      // peerSupportRequests PeerSupportRequest[]
    }
    ```

*   **Mobile App `QalbRescueStepContent` (from `services/api/types.ts`):**
    ```typescript
    export interface QalbRescueContentItem {
      id: string;
      type: 'verse' | 'dhikr' | 'prompt';
      text: string; // Main text (Arabic for verse/dhikr, or prompt)
      translation?: string;
      transliteration?: string;
      audioUrl?: string;
      surah_name_en?: string;
      ayah_number?: number;
      recommended_count?: number;
    }

    export interface QalbRescueStepContent {
      title: string;
      description: string;
      audioUrl?: string; // Main audio for the step
      visualElements?: Record<string, any>; // e.g., { type: 'breathing_animation' }
      interactiveElements?: {
        buttons?: Array<{ id: string; label: string; actionType: 'request_dua' | 'connect_peer' | 'find_professional' }>;
      };
      items?: QalbRescueContentItem[];
    }
    ```

*   **AI Service Response (`PersonalizedContentResponse` in `ai_service/main.py` - simplified):**
    ```python
    class QuranVerseOutput(BaseModel):
        id: str
        arabic_text: str
        translation_en: str
        surah_name_en: str
        ayah_number: int
        theme: str
        audio_url: Optional[str] = None

    class DhikrPhraseOutput(BaseModel):
        id: str
        arabic_text: str
        transliteration_en: str
        translation_en: str
        recommended_count: Optional[int] = None
        theme: str
        audio_url: Optional[str] = None

    class PersonalizedContentResponse(BaseModel):
        quran_verses: List[QuranVerseOutput]
        dhikr_phrases: List[DhikrPhraseOutput]
        reasoning: str
    ```
This revised flow document provides a more accurate representation of the intended backend-driven Qalb Rescue feature.
