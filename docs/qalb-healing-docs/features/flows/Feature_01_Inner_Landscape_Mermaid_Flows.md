# Feature 1: Understanding Your Inner Landscape - Mermaid Flows

This document visualizes the detailed data and process flows for Feature 1: Understanding Your Inner Landscape using Mermaid sequence diagrams.

## Flow 1: Start New Assessment / Resume Existing Assessment

```mermaid
sequenceDiagram
    actor User
    participant <PERSON><PERSON><PERSON> as "Mobile App"
    participant BackendCtrl as "Backend (AssessmentController)"
    participant BackendSvc as "Backend (AssessmentService)"
    participant BackendAISvc as "Backend (aiService)"
    participant Database
    participant AIService as "AI Service"

    User->>MobileApp: Initiates Assessment (Start/Resume)
    activate MobileApp
    MobileApp->>BackendCtrl: POST /api/assessment/start (userId, initialUserProfile)
    activate BackendCtrl
    BackendCtrl->>BackendSvc: startAssessment(userId, userEmail, initialUserProfile)
    activate BackendSvc

    Note over BackendSvc: Determine Effective User Profile
    BackendSvc->>Database: Find Profile by userId
    activate Database
    Database-->>BackendSvc: UserProfileFromDB_id (or null)
    deactivate Database
    alt Profile not found by Id
        BackendSvc->>Database: Find Profile by userEmail
        activate Database
        Database-->>BackendSvc: UserProfileFromDB_email (or null)
        deactivate Database
        alt Profile not found by Email
            BackendSvc->>Database: Create Profile (userId, userEmail)
            activate Database
            Database-->>BackendSvc: New UserProfileFromDB
            deactivate Database
            Note right of BackendSvc: New profile created, becomes effectiveUserProfile
        else Profile Found by Email
            Note right of BackendSvc: UserProfileFromDB_email becomes effectiveUserProfile
        end
    else Profile Found by Id
        Note right of BackendSvc: UserProfileFromDB_id becomes effectiveUserProfile
    end
    Note over BackendSvc: Effective UserProfile determined for session logic.

    BackendSvc->>Database: Query AssessmentSession (userId, completedAt=null)
    activate Database
    Database-->>BackendSvc: Existing Session? (or null)
    deactivate Database

    alt Existing Session Found
        BackendSvc->>BackendSvc: Log session resumption
        Note right of BackendSvc: Resume Flow
        BackendSvc->>BackendSvc: getPersonalizedWelcome(userId, existingSession.userProfile OR effectiveUserProfile)
    else No Existing Session / New Session
        BackendSvc->>Database: Create AssessmentSession (userId, effectiveUserProfile, startedAt, ...)
        activate Database
        Database-->>BackendSvc: New Session Object
        deactivate Database
        BackendSvc->>BackendSvc: Log new session creation
        Note right of BackendSvc: New Session Flow
        BackendSvc->>BackendSvc: getPersonalizedWelcome(userId, effectiveUserProfile)
    end

    activate BackendSvc # Darker activation for nested getPersonalizedWelcome call
    BackendSvc->>BackendSvc: _prepareProfileForAI(profileToUse, userId)
    BackendSvc->>BackendAISvc: getAIPersonalizedWelcome(preparedProfile)
    activate BackendAISvc
    BackendAISvc->>AIService: POST /generate-assessment-welcome (preparedProfile)
    activate AIService
    AIService-->>BackendAISvc: Personalized Welcome JSON (greeting, intro, etc.)
    deactivate AIService
    BackendAISvc-->>BackendSvc: Welcome Content
    deactivate BackendAISvc
    deactivate BackendSvc # Deactivate nested getPersonalizedWelcome

    BackendSvc-->>BackendCtrl: { session, welcomeContent }
    deactivate BackendSvc
    BackendCtrl-->>MobileApp: HTTP 200 OK { session, welcomeContent }
    deactivate BackendCtrl
    MobileApp->>User: Display AssessmentWelcomeScreen (with personalized content)
    deactivate MobileApp
```

## Flow 2: Assessment Question Flow (Per Step)

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App"
    participant BackendCtrl as "Backend (AssessmentController)"
    participant BackendSvc as "Backend (AssessmentService)"
    participant Database

    User->>MobileApp: Proceeds to assessment step (e.g., taps 'Begin' or 'Next')
    activate MobileApp
    MobileApp->>MobileApp: Determine current step

    MobileApp->>BackendCtrl: GET /api/assessment/{sessionId}/questions/{step}
    activate BackendCtrl
    BackendCtrl->>BackendSvc: getAssessmentQuestions(sessionId, step)
    activate BackendSvc

    BackendSvc->>BackendSvc: _getQuestionsForStepDB(step, userProfile?)
    activate BackendSvc # Nested call
    BackendSvc->>Database: FindMany AssessmentQuestion & Choices (where step, isEnabled)
    activate Database
    Database-->>BackendSvc: PrismaQuestion[]
    deactivate Database
    deactivate BackendSvc # End _getQuestionsForStepDB

    BackendSvc->>BackendSvc: Loop: _transformPrismaQuestionToClientType(prismaQuestion)
    Note right of BackendSvc: Map to AssessmentQuestionClientType

    BackendSvc-->>BackendCtrl: AssessmentQuestionClientType[]
    deactivate BackendSvc
    BackendCtrl-->>MobileApp: HTTP 200 OK [QuestionObjects]
    deactivate BackendCtrl

    MobileApp->>User: Render questions on AssessmentFlowScreen
    deactivate MobileApp
```

## Flow 3: Submit Assessment Response (Per Step)

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App"
    participant BackendCtrl as "Backend (AssessmentController)"
    participant BackendSvc as "Backend (AssessmentService)"
    participant CrisisDetectionSvc as "CrisisDetectionService (Backend)"
    participant Database
    participant AIService as "AI Service (Optional, if CrisisDetectionSvc calls it)"

    User->>MobileApp: Completes step inputs, Taps "Next"
    activate MobileApp
    MobileApp->>MobileApp: Gather responses for current step, timeSpent
    MobileApp->>BackendCtrl: POST /api/assessment/{sessionId}/responses/{step} (payload: {responses, timeSpent})
    activate BackendCtrl

    BackendCtrl->>BackendSvc: submitAssessmentResponse(sessionId, step, responses, timeSpent)
    activate BackendSvc

    BackendSvc->>Database: Get AssessmentSession (sessionId)
    activate Database
    Database-->>BackendSvc: Session Object
    deactivate Database

    BackendSvc->>Database: Update Session (responses, timeSpentPerStep)
    activate Database
    Database-->>BackendSvc: Update Confirmed
    deactivate Database
    Note right of BackendSvc: Session updated with current step's answers

    BackendSvc->>CrisisDetectionSvc: analyzeResponse(responses, step)
    activate CrisisDetectionSvc
    opt Potential AI Call for Crisis Analysis by CrisisDetectionSvc
        CrisisDetectionSvc->>AIService: POST /analyze-crisis (relevant data from responses)
        activate AIService
        AIService-->>CrisisDetectionSvc: Crisis Analysis Result (e.g. level, indicators)
        deactivate AIService
    end
    CrisisDetectionSvc-->>BackendSvc: CrisisCheckResult {isCrisis, level, indicators, ...}
    deactivate CrisisDetectionSvc

    alt Crisis Detected (isCrisis === true)
        BackendSvc->>BackendSvc: handleCrisisDetection(sessionId, crisisCheckResult)
        BackendSvc-->>BackendCtrl: Crisis Response Object {crisisDetected: true, crisisLevel, message, emergencyActions, ...}
        BackendCtrl-->>MobileApp: HTTP 200 OK (Crisis Data)
        MobileApp->>User: Display Crisis Modal / Navigate to Crisis Support Screen
    else No Crisis (isCrisis === false)
        BackendSvc->>BackendSvc: _getNextStepLogic(currentStep, session)
        Note right of BackendSvc: Determines nextStep or 'complete'

        BackendSvc->>Database: Update Session (currentStep = nextStep or 'complete')
        activate Database
        Database-->>BackendSvc: Update Confirmed
        deactivate Database

        opt Assessment Complete (nextStep is null/'complete')
            BackendSvc->>BackendSvc: generateDiagnosis(sessionId)
            Note right of BackendSvc: Triggers Flow 4A (async or sync)
        end

        BackendSvc->>BackendSvc: _calculateProgress(nextStep or 'complete')
        BackendSvc-->>BackendCtrl: {nextStep, progress, crisisDetected: false}
        BackendCtrl-->>MobileApp: HTTP 200 OK {nextStep, progress, ...}
        alt Assessment Incomplete
             MobileApp->>User: Navigate to next assessment step (triggers Flow 2)
        else Assessment Complete
             MobileApp->>User: Navigate to Diagnosis Pending/Results Screen
        end
    end
    deactivate BackendSvc
    deactivate BackendCtrl
    deactivate MobileApp
```

## Flow 4: Diagnosis Generation & Delivery

### Flow 4A: Diagnosis Generation

```mermaid
sequenceDiagram
    participant TriggeringProcess as "Trigger (e.g., AssessmentService.submitAssessmentResponse)"
    participant BackendSvc as "Backend (AssessmentService)"
    participant BackendAISvc as "Backend (aiService)"
    participant Database
    participant AIService as "AI Service"

    TriggeringProcess->>BackendSvc: generateDiagnosis(sessionId)
    activate BackendSvc
    Note over BackendSvc: Assessment complete, start diagnosis generation.

    BackendSvc->>Database: FindUnique AssessmentSession (sessionId, include all responses, profile)
    activate Database
    Database-->>BackendSvc: Full AssessmentSession Object
    deactivate Database

    BackendSvc->>BackendSvc: _prepareProfileForAI(session.userProfile, session.userId)
    BackendSvc->>BackendSvc: Construct AIComprehensiveAssessmentDataType (profile, responses, metadata)
    Note right of BackendSvc: Compiles all data for AI analysis.

    BackendSvc->>BackendAISvc: analyzeSpiritualLandscape(analysisData)
    activate BackendAISvc
    BackendAISvc->>AIService: POST /analyze-spiritual-landscape (AIComprehensiveAssessmentDataType)
    activate AIService
    AIService-->>BackendAISvc: AISpiritualLandscapeResponse (JSON with full diagnosis)
    deactivate AIService
    BackendAISvc-->>BackendSvc: AISpiritualLandscapeResponse
    deactivate BackendAISvc

    BackendSvc->>Database: Create SpiritualDiagnosis (stores full AI response, key fields)
    activate Database
    Database-->>BackendSvc: Saved SpiritualDiagnosis Object (with ID)
    deactivate Database
    Note right of BackendSvc: Diagnosis persisted.

    BackendSvc->>Database: Create LayerAnalysis records (linked to SpiritualDiagnosis)
    activate Database
    Database-->>BackendSvc: Confirm LayerAnalysis creation
    deactivate Database

    BackendSvc->>Database: Update AssessmentSession (completedAt, currentStep='complete')
    activate Database
    Database-->>BackendSvc: Confirm Session Update
    deactivate Database

    BackendSvc-->>TriggeringProcess: (Returns saved SpiritualDiagnosis or void if async)
    deactivate BackendSvc
```

### Flow 4B: Diagnosis Delivery

```mermaid
sequenceDiagram
    actor User
    participant MobileApp as "Mobile App"
    participant BackendCtrl as "Backend (AssessmentController)"
    participant BackendSvc as "Backend (AssessmentService)"
    participant Database

    User->>MobileApp: Navigates to Diagnosis Results Screen (e.g., after completion or from history)
    activate MobileApp
    MobileApp->>MobileApp: Needs diagnosisId

    MobileApp->>BackendCtrl: GET /api/assessment/diagnosis/{diagnosisId}/delivery
    activate BackendCtrl

    BackendCtrl->>BackendSvc: getDiagnosisDelivery(diagnosisId)
    activate BackendSvc

    BackendSvc->>Database: FindUnique SpiritualDiagnosis (diagnosisId, include assessmentSession, layerAnalyses)
    activate Database
    Database-->>BackendSvc: SpiritualDiagnosis Record (with relations)
    deactivate Database
    Note right of BackendSvc: Fetches persisted diagnosis and related data.

    BackendSvc->>BackendSvc: Extract ai_response (AISpiritualLandscapeResponse) from diagnosis record
    BackendSvc->>BackendSvc: _prepareProfileForAI(session.userProfile, session.userId)
    BackendSvc->>BackendSvc: _determineUserTypeFromProfile(), _getDeliveryStyleForUserType()
    BackendSvc->>BackendSvc: Construct DiagnosisDelivery DTO (using AI response, profile, formatted fields, summaries)
    Note right of BackendSvc: Formats diagnosis for client consumption.

    BackendSvc-->>BackendCtrl: DiagnosisDelivery DTO
    deactivate BackendSvc

    BackendCtrl-->>MobileApp: HTTP 200 OK (DiagnosisDelivery DTO)
    deactivate BackendCtrl

    MobileApp->>User: Display DiagnosisResultsScreen (with formatted diagnosis)
    deactivate MobileApp
```
