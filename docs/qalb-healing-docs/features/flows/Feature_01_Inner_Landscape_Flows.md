# Feature 1: Understanding Your Inner Landscape - Detailed Flows

This document outlines the detailed data and process flows for Feature 1: Understanding Your Inner Landscape. This feature enables users to go through a spiritual assessment, receive a diagnosis based on the Five Layers of the Self (Jism, Nafs, Aql, Qalb, Ruh), and understand their current inner state.

## Key Components Involved:

*   **Mobile App:** User interface for assessment, displaying questions, results, and crisis information.
*   **Backend (`apps/backend`):**
    *   `AssessmentController`: Handles HTTP requests related to assessments.
    *   `AssessmentService`: Core logic for managing assessment sessions, orchestrating AI calls, and interacting with the database.
    *   `aiService`: Client for communicating with the AI service.
    *   `crisisDetectionService`: Handles crisis detection logic (potentially using AI).
    *   Prisma Client: ORM for database interactions.
*   **AI Service (`apps/ai-service`):**
    *   `/generate-assessment-welcome` endpoint: Generates personalized welcome messages.
    *   `/analyze-spiritual-landscape` endpoint: Performs the comprehensive spiritual diagnosis.
    *   Potentially other endpoints for crisis analysis if used by `crisisDetectionService`.

## Detailed Flows:

### 1. Start New Assessment / Resume Existing Assessment Flow

This flow is initiated when the user decides to start or continue their spiritual assessment.

**Actors:** User, Mobile App, Backend, AI Service

**Trigger:** User taps "Start Assessment" (or similar button) on the mobile app.

**Steps:**

1.  **User Action (Mobile App):**
    *   User initiates the assessment process.

2.  **Mobile App Request:**
    *   The mobile app sends a request to the backend to start or resume an assessment.
    *   **Endpoint:** `POST /api/assessment/start` (or a similar verified endpoint, e.g., `/api/assessment/initiate` or `/api/assessment/welcome` if it serves dual purpose)
    *   **Payload:** Typically includes authenticated `userId` (from auth token) and potentially `userProfile` data if it's fetched fresh on the client and needs to be synced or used for welcome message personalization directly by the backend before AI call.
        *   `userProfile` (Feature 0 data) is crucial for personalization.

3.  **Backend - `AssessmentController`:**
    *   Receives the request.
    *   Extracts `userId` (from authenticated request) and `userProfile` (from request body).
    *   Calls `AssessmentService.startAssessment(userId, userProfile)`.

4.  **Backend - `AssessmentService.startAssessment(userId, userProfile)`:**
    *   **Check for Existing Incomplete Session:**
        *   Queries the database (Prisma: `prisma.assessmentSession.findFirst`) for an `AssessmentSession` record where `userId` matches and `completedAt` is `null`, ordered by `startedAt` descending.
    *   **If Existing Session Found (Resume Flow):**
        *   Logs the resumption of the session.
        *   Calls `this.getPersonalizedWelcome(userId, userProfile)` (or uses profile from the existing session if preferred for consistency).
        *   Returns the existing `session` object and the `welcome` content.
    *   **If No Existing Session Found (New Session Flow):**
        *   Creates a new `AssessmentSession` record in the database (Prisma: `prisma.assessmentSession.create`).
            *   **Data includes:** `userId`, `userProfile` (stored as JSON), `startedAt` (current timestamp), `currentStep` (e.g., "welcome" or initial step key), `totalSteps` (e.g., based on `ASSESSMENT_STEPS.length`).
            *   Initializes other relevant fields like `responses`, `timeSpentPerStep`, etc., to empty/default values.
        *   Calls `this.getPersonalizedWelcome(userId, userProfile)`.
        *   Logs the creation of the new session.
        *   Returns the new `session` object and the `welcome` content.

5.  **Backend - `AssessmentService.getPersonalizedWelcome(userId, userProfile)`:**
    *   Calls `this._prepareProfileForAI(userProfile, userId)` to standardize the user profile structure for the AI service. This helper ensures `user_id` is present and maps various profile attributes to a consistent format.
    *   Calls `aiService.getAIPersonalizedWelcome(preparedProfile)`.

6.  **Backend - `aiService.getAIPersonalizedWelcome(preparedProfile)`:**
    *   Makes an HTTP POST request to the **AI Service**.
    *   **AI Service Endpoint:** `POST /generate-assessment-welcome`
    *   **Payload:** The `preparedProfile` (JSON format).

7.  **AI Service - `/generate-assessment-welcome` Endpoint:**
    *   Receives the user profile data.
    *   Processes the profile using its internal logic (e.g., rule-based system, simple ML model) to determine `userType` and generate:
        *   `greeting`
        *   `introduction`
        *   `explanation`
        *   `motivation`
        *   `primary_action` (button text/ID)
        *   `secondary_actions` (buttons text/ID)
    *   Returns a JSON response (matching `AIPersonalizedWelcomeResponse` Pydantic model in AI service / `PersonalizedWelcome` interface in backend/mobile).

8.  **Backend - Response Handling:**
    *   `aiService` returns the AI's welcome content to `AssessmentService`.
    *   `AssessmentService` returns the `session` object (either existing or new) and the `welcome` content (from AI) to the `AssessmentController`.
    *   `AssessmentController` sends a JSON response to the mobile app.

9.  **Mobile App - Display Welcome:**
    *   Receives the session object and personalized welcome content.
    *   Stores the `sessionId` for subsequent requests.
    *   Displays the `AssessmentWelcomeScreen` (or equivalent), populating it with the `greeting`, `introduction`, `explanation`, `motivation`, and action buttons received.

**Data Structures (Key Interfaces/Models):**

*   **Backend `AssessmentSession` (Prisma Model - Simplified):**
    ```typescript
    model AssessmentSession {
      id            String    @id @default(cuid())
      userId        String
      userProfile   Json?     // From Feature 0
      startedAt     DateTime  @default(now())
      completedAt   DateTime?
      currentStep   String
      totalSteps    Int
      responses     Json?     // Stores answers from various steps
      // ... other fields like timeSpentPerStep, totalTimeSpent
    }
    ```
*   **Backend/Mobile `PersonalizedWelcome` / AI Service `AIPersonalizedWelcomeResponse`:**
    ```typescript
    interface PersonalizedWelcome {
      userId: string; // Or user_id from AI
      userType: string;
      greeting: string;
      introduction: string;
      explanation?: string | null;
      motivation?: string | null;
      primaryAction: { id: string; text: string; description?: string | null; };
      secondaryActions: Array<{ id: string; text: string; description?: string | null; }>;
    }
    ```

**Diagrammatic Flow (Simplified):**

```
Mobile App                      Backend                            AI Service
----------                      -------                            ----------
User Taps Start
    |                             |                                  |
    |---- POST /api/assessment/start (userId, profile) ->|                                  |
    |                             | AssessmentService.startAssessment() |                                  |
    |                             |  - Check existing session? (DB)     |                                  |
    |                             |  - Create new if none (DB)          |                                  |
    |                             |  - getPersonalizedWelcome()         |                                  |
    |                             |    - _prepareProfileForAI()         |                                  |
    |                             |    - aiService.getAIPersonalizedWelcome() |                                  |
    |                             |        |--- POST /generate-assessment-welcome (profile) ->|
    |                             |        |                             | Process Profile, Gen Welcome
    |                             |        |<-- Welcome Content --------|
    |                             |    Return Welcome                   |                                  |
    |                             | Return Session & Welcome            |                                  |
    |<--- Resp: {session, welcome} --|                                  |
    |                                                                  |
Display AssessmentWelcomeScreen
```

### 2. Assessment Question Flow (Per Step)

This flow describes how the mobile app retrieves and displays questions for each step of the assessment.

**Actors:** User, Mobile App, Backend

**Trigger:** User proceeds from the welcome screen or completes a previous assessment step.

**Steps:**

1.  **User Action / App Navigation (Mobile App):**
    *   User taps "Begin Assessment" on the welcome screen, or "Next" on a previous question step.
    *   The mobile app determines the current `step` (e.g., "physical_experiences", "emotional_reflection") based on the assessment flow logic.

2.  **Mobile App Request:**
    *   The mobile app requests the set of questions for the current `step`.
    *   **Endpoint:** `GET /api/assessment/{sessionId}/questions/{step}`
        *   `{sessionId}`: The ID of the current assessment session.
        *   `{step}`: Identifier for the current assessment step (e.g., "jism_symptoms", "nafs_reflection").
    *   **Headers:** Includes authentication token.

3.  **Backend - `AssessmentController.getQuestionsForStep(sessionId, step)`:
    *   Receives the request.
    *   Validates `sessionId` and `step`.
    *   Calls `AssessmentService.getAssessmentQuestions(sessionId, step)`.
        *   *Note: The `sessionId` might be used in the service if questions need to be adapted based on previous answers stored in the session or the user's profile within the session. Currently, the primary driver is `step`.*

4.  **Backend - `AssessmentService.getAssessmentQuestions(sessionId, step)`:**
    *   Calls its private helper `this._getQuestionsForStepDB(step)`.
        *   *(Optional: If user-specific adaptation beyond `step` is needed, it might fetch the `AssessmentSession` using `sessionId` to pass `userProfile` or prior responses to `_getQuestionsForStepDB`)*.

5.  **Backend - `AssessmentService._getQuestionsForStepDB(step, userProfile?)`:**
    *   Queries the database using Prisma:
        *   `prisma.assessmentQuestion.findMany({ where: { step, isEnabled: true }, include: { choices: { orderBy: { order: 'asc' } } }, orderBy: { order: 'asc' } })`
    *   This fetches all enabled questions for the given `step`, along with their choices (if any), ordered appropriately.

6.  **Backend - `AssessmentService._transformPrismaQuestionToClientType(prismaQuestion)`:**
    *   For each question fetched from the database, this (or a similar mapping logic within `getAssessmentQuestions`) transforms the Prisma model (`PrismaAssessmentQuestion` & `PrismaAssessmentQuestionChoice`) into the client-facing structure (`AssessmentQuestionClientType`). This involves:
        *   Mapping database field names to client-expected field names.
        *   Determining `layer` based on `category`.
        *   Structuring choices into the `symptoms` array if it's a symptom-type question.
        *   Setting `reflectionPrompt`, `reflectionRequired`, `allowMultipleSelection`, `intensityScale`, etc., based on `questionType` or other database fields.

7.  **Backend - Response Handling:**
    *   `AssessmentService` returns an array of `AssessmentQuestionClientType` objects to the `AssessmentController`.
    *   `AssessmentController` sends a JSON response to the mobile app containing this array of questions.

8.  **Mobile App - Display Questions (`AssessmentFlowScreen.tsx`):**
    *   Receives the array of question objects for the current step.
    *   Iterates through the questions and dynamically renders the appropriate UI components based on each `question.questionType`:
        *   **Symptom Selection (`symptom_single_choice`, `symptom_multi_choice`):** Displays a list of symptoms (from `question.symptoms`) with radio buttons or checkboxes.
        *   **Intensity Scale (`symptom_..._with_intensity`):** If applicable, displays an intensity slider/segmented control.
        *   **Reflection Input (`reflection_text`, `reflection_audio`):** Displays a `TextInput` for text reflection or UI for audio recording.
    *   Manages the user's responses for the current step in its local state.

**Data Structures (Key Interfaces/Models):**

*   **Backend `PrismaAssessmentQuestion` (Simplified):**
    ```typescript
    model AssessmentQuestion {
      id            String    @id @default(cuid())
      step          String    // e.g., "jism_symptoms", "nafs_reflection"
      order         Int
      text          String    // Main question text or prompt
      description   String?
      category      String    // e.g., "jism", "nafs", "reflection"
      questionType  String    // e.g., "symptom_multi_choice_with_intensity", "reflection_text"
      required      Boolean   @default(false)
      isEnabled     Boolean   @default(true)
      choices       AssessmentQuestionChoice[]
      // ... other fields like conditions for display
    }
    ```
*   **Backend `PrismaAssessmentQuestionChoice` (Simplified):**
    ```typescript
    model AssessmentQuestionChoice {
      id            String    @id @default(cuid())
      questionId    String
      question      AssessmentQuestion @relation(fields: [questionId], references: [id])
      text          String    // Choice text (e.g., symptom name)
      value         String    // Value for the choice (e.g., symptom_id)
      order         Int
      description   String?
    }
    ```
*   **Client-Side `AssessmentQuestionClientType` (Simplified - from `apps/mobile-app-v3/src/screens/features/assessment/AssessmentFlowScreen.tsx` or similar):**
    ```typescript
    interface AssessmentQuestionClientType {
      id: string;
      category: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh' | 'general' | 'reflection'; // Or similar enum
      layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh' | 'general';
      title: string; // Main question prompt
      description?: string;
      questionType: string; // e.g., "symptom_multi_choice_with_intensity", "reflection_text"
      symptoms: Array<{ id: string; text: string; description?: string; order: number; }>; // For symptom questions
      reflectionPrompt?: string; // For reflection questions
      reflectionRequired?: boolean;
      allowMultipleSelection?: boolean;
      intensityScale?: boolean; // If an intensity input is linked
      // ... other UI relevant fields
    }
    ```

**Diagrammatic Flow (Simplified):**

```
Mobile App                                Backend
----------                                -------
User proceeds to next assessment step
Determines current `step`
    |                                         |
    |---- GET /api/assessment/{id}/questions/{step} -->|
    |                                         | AssessmentController.getQuestionsForStep()
    |                                         |   -> AssessmentService.getAssessmentQuestions()
    |                                         |     -> _getQuestionsForStepDB(step)
    |                                         |       - Prisma: Fetch AssessmentQuestion & Choices
    |                                         |     -> _transformPrismaQuestionToClientType()
    |                                         |   Return Array<AssessmentQuestionClientType>
    |<--- Resp: [QuestionObjects] -------------|
    |                                         |
Render questions on AssessmentFlowScreen
based on questionType and content
```

### 3. Submit Assessment Response (Per Step) Flow

This flow occurs when the user submits their answers for the current set of questions in an assessment step.

**Actors:** User, Mobile App, Backend, Crisis Detection Service (potentially AI Service via Crisis Detection)

**Trigger:** User fills out responses for the current step and taps "Next" (or similar) on the mobile app.

**Steps:**

1.  **User Action (Mobile App):**
    *   User completes inputs for the current assessment step (e.g., selects symptoms, sets intensity, writes reflection).
    *   User taps the "Next" or "Submit" button for that step.

2.  **Mobile App Request:**
    *   The mobile app's `assessment.service.ts` (or equivalent logic) gathers all responses for the current step.
    *   It sends these responses to the backend.
    *   **Endpoint:** `POST /api/assessment/{sessionId}/responses/{step}` (actual endpoint might vary, e.g. a single `/api/assessment/submit-response` with `step` in payload).
        *   `{sessionId}`: The ID of the current assessment session.
        *   `{step}`: Identifier for the step being submitted.
    *   **Payload:**
        ```json
        {
          "responses": { /* Object containing user's answers for the step */ },
          "timeSpent": 120 // Time in seconds spent on this step
        }
        ```
    *   **Headers:** Includes authentication token.

3.  **Backend - `AssessmentController`:**
    *   Receives the request.
    *   Validates `sessionId`, `step`, and the payload.
    *   Calls `AssessmentService.submitAssessmentResponse(sessionId, step, payload.responses, payload.timeSpent)`.

4.  **Backend - `AssessmentService.submitAssessmentResponse(...)`:**
    *   Retrieves the current `AssessmentSession` using `this.getSession(sessionId)`.
    *   **Update Session Data (Initial):**
        *   Updates the session object in memory with the newly submitted `responses` for the given `step` (e.g., updates `session.physicalExperiences` or a general `session.responses[step]`).
        *   Updates `session.timeSpentPerStep[step]` and increments `session.totalTimeSpent`.
        *   Saves these initial updates to the database (Prisma: `prisma.assessmentSession.update`).
    *   Fetches the updated session again to ensure subsequent logic uses the latest data.
    *   **Crisis Detection:**
        *   Calls `crisisDetectionService.analyzeResponse(responses, step, session.userId)`.
            *   `crisisDetectionService` uses its internal logic (keywords, rules, potentially calls to AI Service's `/analyze-crisis` endpoint) to analyze the responses for crisis indicators.
        *   **If Crisis Detected (`crisisCheck.isCrisis === true`):**
            *   Calls `this.handleCrisisDetection(sessionId, crisisCheckResultFromService)`.
                *   This method logs the crisis event.
                *   It prepares a specific response object for the client, including `crisisDetected: true`, `crisisLevel`, `message`, `emergencyActions`, `urgency`, and `crisisIndicators`. The assessment flow is typically halted (`nextStep: null`).
            *   Returns this crisis response object to the `AssessmentController`.
        *   **If No Crisis (`crisisCheck.isCrisis === false`):**
            *   **Determine Next Step:** Calls `this._getNextStepLogic(currentStep, session)` to determine the next logical step in the assessment flow based on `ASSESSMENT_STEPS` array or more complex rules.
            *   Updates `currentStep` in the `AssessmentSession` record in the database (Prisma: `prisma.assessmentSession.update`) to the `nextStep` or "complete".
            *   **If Assessment Complete (`nextStep` is `null`):**
                *   Asynchronously (or synchronously if preferred) calls `this.generateDiagnosis(sessionId)` (see Flow #5).
            *   **Calculate Progress:** Calls `this._calculateProgress(nextStep || 'complete')`.
            *   Returns an object like `{ nextStep, progress, crisisDetected: false }` to the `AssessmentController`.

5.  **Backend - Response Handling:**
    *   `AssessmentController` receives the result from `AssessmentService`.
    *   Sends a JSON response to the mobile app.
        *   If crisis: includes crisis details.
        *   If no crisis: includes `nextStep`, `progress`, `crisisDetected: false`.

6.  **Mobile App - Update UI:**
    *   **If Crisis Detected:**
        *   Displays a crisis modal or navigates to a crisis support screen, showing the message and emergency actions provided by the backend.
        *   The normal assessment flow may be paused or terminated.
    *   **If No Crisis:**
        *   If `nextStep` is not `null`, navigates to the next assessment step (triggering Flow #2 for the new step).
        *   If `nextStep` is `null` (assessment complete), navigates to a pending diagnosis screen or directly to the diagnosis results screen (triggering the delivery part of Flow #5).
        *   Updates any UI elements showing assessment progress.

**Data Structures (Key Interfaces/Models):**

*   **Backend `CrisisCheckResult` (from `CrisisDetectionService` - Simplified):**
    ```typescript
    interface CrisisCheckResult {
      isCrisis: boolean;
      level: 'none' | 'low' | 'moderate' | 'high' | 'critical';
      indicators: string[];
      confidence?: number;
      recommendedActions: Array<{id: string, text: string, primary?: boolean, phone?: string, url?: string}>; // Or simpler string array from AI
      urgency: string;
      message?: string; // Tailored message for the user
    }
    ```
*   **Backend Response (No Crisis - Simplified):**
    ```json
    {
      "nextStep": "emotional_experiences", // or null if complete
      "progress": 50, // Percentage
      "crisisDetected": false
    }
    ```
*   **Backend Response (Crisis Detected - Simplified):**
    ```json
    {
      "nextStep": null,
      "progress": 30, // Progress up to the point of crisis
      "crisisDetected": true,
      "crisisLevel": "high",
      "message": "We are concerned... please seek support.",
      "emergencyActions": [
        { "id": "contact_support", "text": "Contact Support Line", "phone": "************" },
        { "id": "sakina_resources", "text": "Access Sakina Resources" }
      ],
      "urgency": "high",
      "crisisIndicators": ["suicidal_ideation"]
    }
    ```

**Diagrammatic Flow (Simplified):**

```
Mobile App                                Backend                                     CrisisDetectionService / AI
----------                                -------                                     --------------------------
User Submits Responses
    |                                         |                                         |
    |---- POST /api/assessment/{id}/responses/{step} (payload) -->|                                         |
    |                                         | AssessmentService.submitAssessmentResponse()|                                         |
    |                                         |  - Update Session (DB)                  |                                         |
    |                                         |  - crisisDetectionService.analyzeResponse() |                                         |
    |                                         |      |--- (Analyzes responses) -------->| Optional: AI Call for Crisis
    |                                         |      |<-- {isCrisis, level,...} --------|
    |                                         |                                         |
    |                                         |  IF Crisis:                             |
    |                                         |    - handleCrisisDetection()            |
    |                                         |    Return CrisisData                    |
    |<--- Resp: {crisisData} -----------------|                                         |
    |    Display Crisis Modal                   |                                         |
    |                                         |  ELSE (No Crisis):                      |
    |                                         |    - _getNextStepLogic()                |
    |                                         |    - Update Session currentStep (DB)    |
    |                                         |    - IF Complete: generateDiagnosis()   |
    |                                         |    - _calculateProgress()               |
    |                                         |    Return {nextStep, progress}          |
    |<--- Resp: {nextStep, progress} ---------|                                         |
    |                                         |                                         |
Navigate to nextStep or Diagnosis
```

### 4. Diagnosis Generation & Delivery Flow

This flow covers how the comprehensive spiritual diagnosis is generated by the AI service and then formatted and delivered to the mobile app for the user to view.

**Actors:** Mobile App, Backend, AI Service

**Trigger (Generation):**
*   Automatically triggered by `AssessmentService.submitAssessmentResponse` when the assessment's final step is completed (i.e., `nextStep` becomes `null`).
*   Could also potentially be triggered by a re-analysis request (not detailed here).

**Trigger (Delivery):**
*   User navigates to the diagnosis results screen in the mobile app after completing the assessment or when viewing a past completed assessment.

**A. Diagnosis Generation Steps (Backend & AI Service):**

1.  **Backend - `AssessmentService.generateDiagnosis(sessionId)`:**
    *   Retrieves the fully populated `AssessmentSession` from the database using `sessionId` (Prisma: `prisma.assessmentSession.findUnique`). This session includes all user responses, profile data, time spent, etc.
    *   Validates that `session.userId` is present.
    *   Calls `this._prepareProfileForAI(session.userProfile, session.userId)` to get the standardized user profile.
    *   **Prepare Comprehensive Data for AI:** Constructs the `AIComprehensiveAssessmentDataType` payload. This involves:
        *   The `preparedProfile`.
        *   Mapping session responses (e.g., `session.physicalExperiences`, `session.emotionalExperiences`, `session.reflections`) to the `AIServiceSymptomExperienceData` and `reflections` structures expected by the AI.
        *   Including `session_metadata` (total time spent, time per step, session ID, start time).
    *   Calls `aiService.analyzeSpiritualLandscape(analysisData)`.

2.  **Backend - `aiService.analyzeSpiritualLandscape(analysisData)`:**
    *   Makes an HTTP POST request to the **AI Service**.
    *   **AI Service Endpoint:** `POST /analyze-spiritual-landscape`
    *   **Payload:** The `AIComprehensiveAssessmentDataType` object (JSON).

3.  **AI Service - `/analyze-spiritual-landscape` Endpoint:**
    *   Receives the comprehensive assessment data.
    *   **Core Analysis (`SpiritualAnalysisProcessor.py` or similar):**
        *   Analyzes each of the Five Layers (Jism, Nafs, Aql, Qalb, Ruh) based on submitted symptoms, intensity, and reflections. This uses a rule-based engine combined with user profile context.
        *   Calculates `severity_score` for each layer.
        *   Generates specific `insights`, `recommendations`, and `islamic_context` for each layer.
        *   Determines the `primary_layer` of concern.
        *   Re-evaluates `crisis_level` and `crisis_indicators` based on the full assessment.
        *   Generates `personalized_message`, overall `islamic_insights`, `educational_content`.
        *   Suggests `next_steps` for the user post-diagnosis.
        *   Recommends a `recommended_journey_type` and `estimated_healing_duration`.
        *   Calculates an overall `confidence` score for the diagnosis.
    *   Returns a JSON response matching the `AISpiritualLandscapeResponse` Pydantic model.

4.  **Backend - `AssessmentService.generateDiagnosis` (Continued):**
    *   Receives the `AISpiritualLandscapeResponse` from `aiService`.
    *   **Persist Diagnosis to Database:**
        *   Creates a new `SpiritualDiagnosis` record (Prisma: `prisma.spiritualDiagnosis.create`).
            *   Stores the full `ai_response` JSON object in `diagnosisData.ai_response`.
            *   Extracts and stores key fields directly on the `SpiritualDiagnosis` model for indexing/querying (e.g., `primaryLayer`, `overallSeverity`, `crisisLevel`, `recommendedJourneyType`).
        *   For each layer insight in `aiAnalysis.layer_insights`, creates a linked `LayerAnalysis` record in Prisma, populating it with `layer`, `layerName`, `impactScore`, `priority`, `insights`, `recommendations`, `islamicContext`.
    *   **Mark Assessment Session Complete:**
        *   Updates the `AssessmentSession` record (Prisma: `prisma.assessmentSession.update`) to set `completedAt` (current timestamp) and `currentStep` to "complete".
    *   Logs the successful generation and saving of the diagnosis.
    *   Returns the saved `SpiritualDiagnosis` (Prisma model object).

**B. Diagnosis Delivery Steps (Backend & Mobile App):**

1.  **User Action / App Navigation (Mobile App):**
    *   User completes the assessment, and the app automatically navigates to the results screen.
    *   Or, user selects a completed assessment from their history to view the diagnosis.
    *   The mobile app needs the `diagnosisId`.

2.  **Mobile App Request:**
    *   Requests the formatted diagnosis content for display.
    *   **Endpoint:** `GET /api/assessment/diagnosis/{diagnosisId}/delivery`
    *   **Headers:** Authentication token.

3.  **Backend - `AssessmentController.getDiagnosisDelivery(diagnosisId)`:**
    *   Receives the request.
    *   Calls `AssessmentService.getDiagnosisDelivery(diagnosisId)`.

4.  **Backend - `AssessmentService.getDiagnosisDelivery(diagnosisId)`:**
    *   Fetches the `SpiritualDiagnosis` record from the database using `diagnosisId` (Prisma: `prisma.spiritualDiagnosis.findUnique`), including its related `assessmentSession` (for `userProfile` and `userId`) and `layerAnalyses`.
    *   Throws an error if the diagnosis or session is not found.
    *   Extracts the full `ai_response` (which is `AISpiritualLandscapeResponse`) from `diagnosisRecord.diagnosisData.ai_response`. Throws error if missing.
    *   Calls `this._prepareProfileForAI(diagnosisRecord.assessmentSession.userProfile, diagnosisRecord.userId)`.
    *   Calls `this._determineUserTypeFromProfile(preparedProfile)` and `this._getDeliveryStyleForUserType(userType)`.
    *   **Construct `DiagnosisDelivery` DTO:**
        *   Populates `userId`, `userType`, `deliveryStyle`, `generatedAt`.
        *   Sets `diagnosis` field to the `ai_response` object, augmented with `id: diagnosisRecord.id`.
        *   Populates formatted fields like `layerIntroduction` (from `aiResponse.personalized_message`), `educationalContent`, `islamicInsights` (joined list), `nextStepsGuidance` (joined list), `journeyRecommendation`.
        *   Generates `primaryLayerAnalysisSummary` and `secondaryLayersSummary` using `this._getLayerSummary` (which internally uses `this.formatLayerName`) by extracting relevant data from `aiResponse.layer_insights`.
        *   Sets `additionalResources` (currently an empty array).
    *   Returns the populated `DiagnosisDelivery` DTO.

5.  **Backend - Response Handling:**
    *   `AssessmentController` sends the `DiagnosisDelivery` DTO as a JSON response to the mobile app.

6.  **Mobile App - Display Diagnosis (`DiagnosisResultsScreen.tsx`):**
    *   Receives the `DiagnosisDelivery` DTO.
    *   Renders the diagnosis information:
        *   Personalized messages (`layerIntroduction`).
        *   Detailed breakdown for each layer (iterating through `delivery.diagnosis.layer_insights`).
        *   Primary and secondary layer summaries.
        *   Islamic insights, educational content.
        *   Next steps, journey recommendations.
        *   Crisis information if `delivery.diagnosis.crisis_level` is not 'none'.

**Data Structures (Key Interfaces/Models):**

*   **AI Service `AISpiritualLandscapeResponse` (and Backend `aiService.AISpiritualLandscapeResponse`):**
    ```typescript
    interface AISpiritualLandscapeResponse {
      primary_layer: string; // e.g., "nafs", "qalb", "none"
      layer_insights: Record<string, AILayerAnalysisOutput>; // Keyed by layer name
      personalized_message: string;
      islamic_insights: string[];
      educational_content: string;
      crisis_level: string; // 'none', 'low', 'moderate', 'high', 'critical'
      crisis_indicators: string[];
      immediate_actions: string[]; // For crisis response
      next_steps: string[]; // Post-diagnosis general next steps
      recommended_journey_type: string;
      estimated_healing_duration: number; // in days
      confidence: number; // 0-1
    }
    interface AILayerAnalysisOutput {
      layer: string;
      insights: string[];
      recommendations: string[];
      islamic_context: string;
      severity_score: number; // 0-100
    }
    ```
*   **Backend `SpiritualDiagnosis` (Prisma Model - Simplified):**
    ```typescript
    model SpiritualDiagnosis {
      id                        String    @id @default(cuid())
      userId                    String
      assessmentSessionId       String
      assessmentSession         AssessmentSession @relation(fields: [assessmentSessionId], references: [id])
      diagnosisData             Json      // Stores the full AISpiritualLandscapeResponse as 'ai_response'
      primaryLayer              String?
      secondaryLayers           String[]
      overallSeverity           String?
      crisisLevel               String?
      // ... other indexed fields from AI response
      layerAnalyses             LayerAnalysis[] // Relation to Prisma LayerAnalysis model
      generatedAt               DateTime  @default(now())
    }
    ```
*   **Backend `LayerAnalysis` (Prisma Model - Simplified):**
    ```typescript
    model LayerAnalysis {
      id                String    @id @default(cuid())
      spiritualDiagnosisId String
      spiritualDiagnosis  SpiritualDiagnosis @relation(fields: [spiritualDiagnosisId], references: [id])
      layer             String    // "jism", "nafs", etc.
      layerName         String
      impactScore       Float
      priority          String    // "primary", "secondary"
      affectedSymptoms  String[]
      insights          String[]
      recommendations   String[]
      islamicContext    String
    }
    ```
*   **Backend/Mobile `DiagnosisDelivery` (DTO - Simplified, as refined):**
    ```typescript
    interface DiagnosisDelivery {
      userId: string;
      userType: string;
      deliveryStyle: 'gentle' | 'clinical' | 'advanced' | 'traditional' | 'compassionate';
      generatedAt: Date;
      diagnosis: any; // AISpiritualLandscapeResponse + 'id' of diagnosis record
      layerIntroduction?: string;
      educationalContent?: string;
      islamicInsights?: string;
      nextStepsGuidance?: string;
      journeyRecommendation?: string;
      primaryLayerAnalysisSummary?: string;
      secondaryLayersSummary?: string;
      additionalResources?: string[];
    }
    ```

**Diagrammatic Flow (Simplified - Generation & Delivery are distinct but related):**

**Generation:**
```
Backend (AssessmentService.generateDiagnosis)     AI Service
---------------------------------------------     ----------
- Get AssessmentSession (DB)
- Prepare AIComprehensiveAssessmentDataType
- aiService.analyzeSpiritualLandscape()
      |---- POST /analyze-spiritual-landscape (data) -->|
      |                                                 | Perform Full Analysis
      |<--- Resp: AISpiritualLandscapeResponse ---------|
- Save SpiritualDiagnosis & LayerAnalysis (DB)
- Mark Session Complete (DB)
```

**Delivery:**
```
Mobile App                                  Backend
----------                                  -------
User Navigates to Diagnosis Screen
    |                                             |
    |---- GET /api/assessment/diag/{id}/delivery -->|
    |                                             | AssessmentController.getDiagnosisDelivery()
    |                                             |   -> AssessmentService.getDiagnosisDelivery()
    |                                             |     - Fetch SpiritualDiagnosis & Session (DB)
    |                                             |     - Extract AI Response
    |                                             |     - Determine UserType, DeliveryStyle
    |                                             |     - Construct DiagnosisDelivery DTO
    |                                             |   Return DiagnosisDelivery DTO
    |<--- Resp: DiagnosisDelivery DTO ------------|
    |                                             |
Display DiagnosisResultsScreen
```
