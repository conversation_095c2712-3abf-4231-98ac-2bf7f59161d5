# Onboarding Pathway & Question Flow Tree

This document outlines the decision tree and question flow for the Qalb Healing onboarding process. It details how users navigate through different sets of questions based on their responses.

## Root: Welcome

1.  **Step: `welcome`**
    *   **Question:** (Welcome message, option to begin or seek emergency help)
    *   **User Action:**
        *   Selects "Begin Your Journey" -> **Next Step: `mental_health_awareness`**
        *   Selects "I need immediate help" -> **Action: Redirect to Emergency Protool/Sakina Mode** (This is a crisis_detected event, typically handled by frontend routing based on API response, rather than a direct step in `determineNextStep` for a *question*).

## Branch: Mental Health Awareness

2.  **Step: `mental_health_awareness`**
    *   **Question:** "How would you describe what brings you to Qalb Healing today?"
    *   **User Selects Option:**
        *   **If "clinical_aware" (`I know I have anxiety/depression...`) is selected:**
            *   **Next Step: `mha_conditions`**
        *   **If "symptom_aware" (`Something feels wrong...`) is selected:**
            *   **Next Step: `mha_experiences`**
        *   **If "clinical_integration" (`I want to integrate my clinical/professional knowledge...`) is selected:**
            *   **Next Step: `spiritual_optimizer_clinical`** (then -> `ruqya_knowledge`)
        *   **If "traditional_bridge" (`I'm a religious leader seeking to understand...`) is selected:**
            *   **Next Step: `spiritual_optimizer_traditional`** (then -> `ruqya_knowledge`)
        *   **If "crisis" (`I'm having a crisis...`) is selected:**
            *   (This response itself is a crisis indicator. The backend `submitResponse` for `mental_health_awareness` would detect this. The AI service would confirm. The user is typically routed to crisis handling by the frontend based on API response, not necessarily a new "question step" from `determineNextStep` for this specific path, though the AI service will use this info).
            *   **Default Next Step (if no immediate crisis override): `ruqya_knowledge`** (or as per `getOriginalNextStepAfterMha`)
        *   **If "spiritual_growth" or "new_muslim" or other options are selected:**
            *   **Next Step: `ruqya_knowledge`** (as per default path from `getOriginalNextStepAfterMha`)

### Sub-Branch: Clinically Aware Follow-ups

3.  **Step: `mha_conditions`** (Reached if `mental_health_awareness` response was "clinical_aware")
    *   **Question:** "What specific conditions, if any, are you dealing with? (Select all that apply)"
    *   **User provides response.**
    *   **Next Step: `mha_therapy`**

4.  **Step: `mha_therapy`** (Reached after `mha_conditions`)
    *   **Question:** "Have you tried therapy or counseling before for these conditions?"
    *   **User provides response.**
    *   **Next Step:** (Returns to main track, e.g., `ruqya_knowledge` or an optimizer path, based on the original MHA choice).

### Sub-Branch: Symptom-Aware Follow-ups

5.  **Step: `mha_experiences`** (Reached if `mental_health_awareness` response was "symptom_aware")
    *   **Question:** "What physical or emotional experiences concern you most? (Select all that apply)"
    *   **User provides response.**
    *   **Next Step: `mha_concepts_familiarity`**

6.  **Step: `mha_concepts_familiarity`** (Reached after `mha_experiences`)
    *   **Question:** "How familiar are you with looking at these experiences through a mental health lens?"
    *   **User provides response.**
    *   **Next Step:** (Returns to main track, typically `ruqya_knowledge`, based on the original MHA choice).

## Branch: Spiritual Optimizers (Example Path)

7.  **Step: `spiritual_optimizer_clinical`** (Reached if `mental_health_awareness` response was "clinical_integration")
    *   **Question:** (Clinical-Islamic Integration Goals)
    *   **User provides response.**
    *   **Next Step: `ruqya_knowledge`**

8.  **Step: `spiritual_optimizer_traditional`** (Reached if `mental_health_awareness` response was "traditional_bridge")
    *   **Question:** (Traditional-Modern Bridge Building)
    *   **User provides response.**
    *   **Next Step: `ruqya_knowledge`**

## Branch: Ruqya Knowledge

9.  **Step: `ruqya_knowledge`** (Primary Question)
    *   **Question:** "How familiar are you with Islamic spiritual healing (Ruqya)?"
    *   **User Selects Option:**
        *   **If "expert" is selected:**
            *   **Next Step: `rk_expert_aspects`**
        *   **If "practitioner" is selected:**
            *   **Next Step: `rk_practitioner_duration`**
        *   **If "unaware" is selected:**
            *   **Next Step: `rk_unaware_openness`**
        *   **If "aware" or "skeptical" (or other non-handled options) is selected:**
            *   **Next Step: `professional_context`**

### Sub-Branch: Ruqya Expert Follow-ups

9.1. **Step: `rk_expert_aspects`** (Reached if `ruqya_knowledge` response was "expert")
    *   **Question:** "What aspects of ruqya are you most experienced with? (Select all that apply)"
    *   **User provides response.**
    *   **Next Step: `rk_expert_tools`**

9.2. **Step: `rk_expert_tools`** (Reached after `rk_expert_aspects`)
    *   **Question:** "What tools or features here would best help enhance your practice? (Select all that apply)"
    *   **User provides response.**
    *   **Next Step: `professional_context`**

### Sub-Branch: Ruqya Practitioner Follow-up

9.3. **Step: `rk_practitioner_duration`** (Reached if `ruqya_knowledge` response was "practitioner")
    *   **Question:** "How long have you been practicing ruqya for yourself or others?"
    *   **User provides response.**
    *   **Next Step: `professional_context`**

### Sub-Branch: Ruqya Unaware Follow-ups

9.4. **Step: `rk_unaware_openness`** (Reached if `ruqya_knowledge` response was "unaware")
    *   **Question:** "Are you open to learning about Islamic spiritual healing concepts (Ruqya)?"
    *   **User provides response.**
    *   **Next Step: `rk_unaware_comfort`**

9.5. **Step: `rk_unaware_comfort`** (Reached after `rk_unaware_openness`)
    *   **Question:** "What's your comfort level with Islamic terminology for these spiritual concepts?"
    *   **User provides response.**
    *   **Next Step: `professional_context`**

## Branch: Professional Context

10. **Step: `professional_context`** (Primary Question)
    *   **Question:** "Which of these best describes your professional life or primary daily activity?"
    *   **User provides response.**
    *   **Next Step: `pc_work_challenges`**

10.1. **Step: `pc_work_challenges`** (Reached after `professional_context`)
    *   **Question:** "What work-related challenges, if any, currently cause you the most stress? (Select all that apply)"
    *   **User provides response.**
    *   **Next Step: `demographics`**

## Branch: Demographics

11. **Step: `demographics`**
    *   **Question:** (Multi-section: Age Range, Gender Identity, Family Status)
    *   **User provides responses to sections.**
    *   **Next Step: `life_circumstances`**

## Branch: Life Circumstances

12. **Step: `life_circumstances`**
    *   **Question:** "Are any of these current life circumstances particularly relevant to you at the moment? (Select all that apply)"
    *   **User provides response.**
    *   *(Future enhancement: Options review)*
    *   **Next Step: `null` (End of Questioning Phase)** -> Triggers `completeOnboarding` in backend service.

---

## Visual Pathway Diagram (Mermaid)

```mermaid
graph TD
    A[Start Onboarding] --> B(Step: welcome);
    B -- Begin Your Journey --> C{Step: mental_health_awareness};
    B -- Immediate Help --> EMERGENCY_PROTOCOL[Handle Crisis];

    C -- "clinical_aware" --> D(Step: mha_conditions);
    D -- Response --> E(Step: mha_therapy);
    E -- Response --> MAIN_TRACK_CA{Return to Main Track after MHA};

    C -- "symptom_aware" --> F(Step: mha_experiences);
    F -- Response --> G(Step: mha_concepts_familiarity);
    G -- Response --> MAIN_TRACK_SA{Return to Main Track after MHA};

    C -- "clinical_integration" --> H(Step: spiritual_optimizer_clinical);
    H -- Response --> I(Step: ruqya_knowledge);

    C -- "traditional_bridge" --> J(Step: spiritual_optimizer_traditional);
    J -- Response --> I;

    %% Default, actual crisis handling is more complex
    C -- "crisis" --> I;
    C -- "spiritual_growth" --> I;
    C -- "new_muslim" --> I;

    %% Generic label for remaining MHA paths
    C -- "Other MHA options" --> I;

    MAIN_TRACK_CA --> I;
    MAIN_TRACK_SA --> I;

    I -- "expert" --> RK_EX_ASP(Step: rk_expert_aspects);
    RK_EX_ASP -- Response --> RK_EX_TOOLS(Step: rk_expert_tools);
    RK_EX_TOOLS -- Response --> K(Step: professional_context);

    I -- "practitioner" --> RK_PRAC_DUR(Step: rk_practitioner_duration);
    RK_PRAC_DUR -- Response --> K;

    I -- "unaware" --> RK_UN_OPEN(Step: rk_unaware_openness);
    RK_UN_OPEN -- Response --> RK_UN_COMF(Step: rk_unaware_comfort);
    RK_UN_COMF -- Response --> K;

    %% Corrected lines for "aware", "skeptical", and other default paths from Ruqya Knowledge
    I -- "aware" --> K;
    I -- "skeptical" --> K;
    I -- "Other (default path)" --> K;

    K -- Response --> K_FOLLOWUP(Step: pc_work_challenges);
    K_FOLLOWUP -- Response --> L(Step: demographics);

    L -- Response --> M(Step: life_circumstances);
    M -- Response --> END_ONBOARDING[End Questions / CompleteOnboarding];

    subgraph Legend
        direction LR
        L_SQ[Single Choice Question]
        L_MQ[Multiple Choice Question]
        L_MS[Multi-Section Question]
        L_COND{Conditional Branch}
        L_ACTION[Action/Process]
    end

    classDef default fill:#fff,stroke:#333,stroke-width:2px,color:#333;
    classDef crisis fill:#ffdddd,stroke:#cc0000,stroke-width:2px,color:#cc0000;
    class EMERGENCY_PROTOCOL crisis;
```

---
*This document will be updated as further adaptive logic and question sets are implemented in the backend onboarding service.*
---
