# Feature 3: Qalb Rescue - Pending Tasks & Future Enhancements

**Date:** October 26, 2023
**Version:** 1.0

This document lists pending tasks and potential future enhancements for the Qalb Rescue feature following its significant refactoring.

## I. Immediate System & Environment Tasks (Prerequisites for Full Functionality)

1.  **Backend - Database Migrations & Prisma Client:**
    *   [ ] **Run Prisma Generate:** Execute `npx prisma generate` in `apps/backend/` to update the Prisma client after schema changes.
    *   [ ] **Run Prisma Migrate:** Execute `npx prisma migrate dev --name <migration_name>` (e.g., `qalb_rescue_models_and_ai_content`) in `apps/backend/` to apply all schema changes to the database (includes `EmergencySession` updates, `DuaRequestLog`, `PeerSupportRequest`, and the new `QuranVerseAiContent` & `DhikrAiContent` tables).

2.  **AI Service - Environment & Dependencies:**
    *   [ ] **Add `psycopg2-binary` to `apps/ai-service/requirements.txt`**.
    *   [ ] **Install Python Dependencies:** Ensure `pip install -r requirements.txt` is run in the AI service environment.
    *   [ ] **Set `DATABASE_URL` for AI Service:** Configure the `DATABASE_URL` environment variable for the AI service to connect to the PostgreSQL database.
    *   [ ] **Set `AI_SERVICE_BASE_URL` for Backend:** Ensure the backend's `AI_SERVICE_BASE_URL` environment variable correctly points to the deployed AI service.
    *   [ ] **Configure AI Service Port & Logging:** Set `PORT` and `LOG_LEVEL` for the AI service environment.
    *   [ ] **Update AI Service Dockerfile (if applicable):** Ensure it handles new dependencies and environment variables.
    *   [ ] **Network Configuration:** Verify network accessibility between the backend and AI service.

3.  **Content Population:**
    *   [ ] **Populate `QuranVerseAiContent` Table:** Add a comprehensive and well-themed corpus of Quranic verses.
    *   [ ] **Populate `DhikrAiContent` Table:** Add a comprehensive and well-themed corpus of Dhikr phrases.

## II. Backend - Feature Completion & Enhancements

1.  **Community Connection - Notifications:**
    *   [ ] **Du'a Request Notifications:** Design and implement a system to notify the community (or a segment) when a `DuaRequestLog` entry is made (e.g., N8N workflow, direct push notification service integration).
    *   [ ] **Peer Supporter Notifications:** Implement notifications to matched peer supporters when a `PeerSupportRequest` is assigned to them.
    *   [ ] **Peer Support Acceptance/Rejection Flow:**
        *   [ ] Define API endpoints for supporters to accept/reject requests.
        *   [ ] Update `PeerSupportRequest` status accordingly.
        *   [ ] Notify the original user about the supporter's response.

2.  **Peer Supporter Identification & Management:**
    *   [ ] **Update `Profile` Schema (if needed):** Add `isPeerSupporter: Boolean?` and `isAvailableForSupport: Boolean?` fields to `schema.prisma` if not already present and migrate.
    *   [ ] **Admin Interface/Logic:** Create a way to designate users as peer supporters and for supporters to manage their availability.
    *   [ ] **Refine `findPeerSupporter` Logic:** Enhance matching criteria (language, gender, specialization, etc.) based on `Profile` data and request criteria.

3.  **Chat Mechanism for Peer Support:**
    *   [ ] **Design & Implement/Integrate:** Define how the user-peer supporter chat will occur (e.g., in-app custom chat, third-party SDK integration, unique room ID generation). This is a significant sub-feature.

4.  **Rate Limiting for `startQalbRescueSession`:**
    *   [ ] Re-implement or verify rate limiting for session initiation (previously in controller, consider moving to service or as middleware).

5.  **AI Service Integration - Dhikr Personalization:**
    *   [ ] Enhance `emergency.service.ts` (`getStepContent` for 'breathing' or other steps) to call the AI service for personalized Dhikr, similar to how it's done for Quranic verses.
    *   [ ] Update AI service's `ContentPersonalizationProcessor` to handle Dhikr personalization requests effectively.

## III. AI Service - Enhancements & Productionization

1.  **Advanced Personalization Logic (LLM Integration - see `Qalb_Rescue_LLM_Personalization_Roadmap.md`):**
    *   [ ] Research and select an LLM.
    *   [ ] Develop & test prompts for semantic understanding, content ranking, personalized reflection generation.
    *   [ ] Integrate LLM calls into `ContentPersonalizationProcessor`.
    *   [ ] Address latency, cost, and ethical considerations.

2.  **Content Management for AI:**
    *   [ ] Develop a system or process for easily adding, updating, and theming content in the `QuranVerseAiContent` and `DhikrAiContent` tables.

3.  **Performance & Scalability:**
    *   [ ] Implement caching for DB queries in `db_utils.py` if needed.
    *   [ ] Monitor and optimize AI service response times.

## IV. Mobile App - UI/UX Polish & Full Feature Integration

1.  **Detailed UI for Community Features:**
    *   [ ] `Request Du'a`: Meaningful confirmation, loading states, error handling.
    *   [ ] `Connect Peer Supporter`: Clear UI for request status (pending, unavailable, connected), display supporter details, error handling.
    *   [ ] Chat Interface (if in-app).

2.  **Content Display Refinement:**
    *   [ ] Proper styling for Arabic text (fonts, RTL), translations, transliterations.
    *   [ ] Enhanced UI for audio playback controls.
    *   [ ] UI for interactive elements defined in `StepContent.interactiveElements`.

3.  **Feedback Collection UI:**
    *   [ ] Implement UI elements (e.g., star rating, text input) to collect `effectivenessRating` and `feedbackText` before calling `submitFeedbackAndEndSession`.

4.  **Robust Offline Strategy:**
    *   [ ] Design and implement a more comprehensive offline mode for Qalb Rescue (e.g., caching initial steps, a fully static minimal flow if API is unreachable). The current default content fallbacks in the API service are basic.

5.  **Error Handling & User Guidance:**
    *   [ ] Provide more specific and user-friendly error messages for API failures or unexpected states.

## V. Comprehensive Testing (Execution)

*   [ ] **Execute Backend Unit & Integration Tests.**
*   [ ] **Execute AI Service Unit & Integration Tests.**
*   [ ] **Execute Mobile App Unit, Component & E2E Tests.**
*   [ ] **Perform Full System End-to-End Testing.**
*   [ ] **User Acceptance Testing (UAT).**

## VI. Full Documentation Update

*   [ ] **Update `Feature_03_Qalb_Rescue.md`** (main feature doc).
*   [ ] **Update `Feature_03_Qalb_Rescue_Flows.md`** (detailed flows & sequence diagrams).
*   [ ] **Update `Feature_03_Qalb_Rescue_Mermaid_Flows.md`**.
*   [ ] **Finalize API Documentation (Swagger).**
*   [ ] **Add/Review Code Comments** across all modified modules.
*   [ ] **Update `AGENTS.md`** with new architectural notes or guidelines.

## VII. Deployment & Monitoring
*   [ ] Configure CI/CD pipelines for backend and AI service.
*   [ ] Set up monitoring and alerting for all services.
