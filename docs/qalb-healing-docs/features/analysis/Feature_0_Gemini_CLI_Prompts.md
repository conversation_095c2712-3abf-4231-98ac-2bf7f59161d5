# Gemini CLI Prompts for Feature 0 Completion

## 🚀 Quick Setup

```bash
# Install Gemini CLI if not already installed
npm install -g @google/generative-ai-cli

# Set your API key
export GEMINI_API_KEY="your-api-key-here"
```

---

## 🎯 High Priority Tasks

### 1. Enhanced Professional Categorization

#### Generate Healthcare Profession Questions
```bash
gemini prompt "
You are an expert in Islamic mental wellness and healthcare professions. Create detailed onboarding questions for healthcare professionals that will enable personalized Islamic content.

Context: We have a broad 'Healthcare' category and need granular subcategories for better personalization.

Requirements:
1. Create 12-15 specific healthcare roles
2. Include Islamic considerations for each role
3. Consider work stress patterns unique to each role
4. Format as TypeScript interface for React Native app
5. Include follow-up questions about Islamic integration challenges

Healthcare roles to include:
- Physicians (General Practice, Specialists)
- Nurses (RN, NP, etc.)
- Mental Health Professionals
- Allied Health
- Healthcare Students
- Healthcare Administration

Output format: TypeScript interface with question objects containing id, text, and Islamic_context fields.
"
```

#### Generate Education Profession Questions
```bash
gemini prompt "
Create detailed onboarding questions for education professionals in an Islamic mental wellness app.

Context: Need granular categorization beyond 'Education' for personalized Islamic guidance.

Requirements:
1. <PERSON>reate 12-15 specific education roles
2. Consider Islamic teaching principles
3. Address unique stressors for each role
4. Include Islamic school/Madrasa considerations
5. Format as TypeScript interface

Education roles to include:
- Teachers (Elementary, Middle, High School)
- Higher Education (Professors, Instructors)
- Education Support (Counselors, Administrators)
- Islamic Education (Islamic School Teachers, Madrasa)
- Education Students

Include Islamic considerations like:
- Balancing secular and Islamic education
- Teaching Islamic values in diverse environments
- Patience and wisdom in education (Islamic perspective)

Output: TypeScript interface with question objects.
"
```

#### Generate Technology Profession Questions
```bash
gemini prompt "
Create detailed onboarding questions for technology professionals in an Islamic mental wellness app.

Context: Tech workers face unique challenges that need Islamic guidance - work-life balance, ethical considerations, innovation vs. Islamic values.

Requirements:
1. Create 12-15 specific tech roles
2. Address Islamic ethics in technology
3. Consider work-life balance challenges
4. Include startup/entrepreneurship Islamic perspectives
5. Format as TypeScript interface

Technology roles to include:
- Software Engineers/Developers
- Data Scientists/Analysts
- Product Managers
- UX/UI Designers
- DevOps/Infrastructure
- Cybersecurity
- Tech Leadership
- Startup Founders
- Tech Students

Islamic considerations:
- Halal income in tech industry
- Work-life balance and family time
- Innovation and creativity as worship
- Ethical use of technology and data
- Avoiding haram business models

Output: TypeScript interface with question objects.
"
```

### 2. Enhanced Crisis Detection Keywords

#### Generate Crisis Detection Keywords
```bash
gemini prompt "
You are an expert in Islamic mental health and crisis intervention. Create a comprehensive crisis detection keyword system for an Islamic mental wellness app.

Context: We need to detect crisis situations in user responses during onboarding while being culturally sensitive to Islamic expressions.

Requirements:
1. Create keyword categories with severity levels (low, moderate, high, critical)
2. Include Islamic expressions of distress
3. Consider cultural variations in expressing mental health struggles
4. Include profession-specific crisis indicators
5. Avoid false positives from normal Islamic expressions
6. Format as JSON structure

Categories to include:
1. Suicide ideation keywords
2. Self-harm indicators
3. Hopelessness expressions
4. Islamic spiritual crisis terms
5. Family/relationship crisis terms
6. Financial distress terms
7. Professional burnout terms
8. Health crisis terms

Consider Islamic context:
- 'Allah will not forgive me' vs normal seeking forgiveness
- Spiritual struggles vs crisis
- Tests from Allah vs hopelessness
- Community isolation vs normal privacy

Output: JSON structure with keywords, severity levels, and Islamic context flags.
"
```

### 3. Arabic Translation and RTL Support

#### Generate Arabic Translations
```bash
gemini prompt "
You are an expert Arabic translator specializing in Islamic mental health terminology. Translate the onboarding questions for an Islamic mental wellness app.

Context: Need accurate, culturally appropriate Arabic translations that maintain Islamic authenticity.

Requirements:
1. Translate all question text to Modern Standard Arabic
2. Ensure Islamic terminology is authentic and accurate
3. Consider cultural sensitivity for mental health topics
4. Maintain the same meaning and tone
5. Format as JSON with English-Arabic pairs

Questions to translate:
[Paste the current English questions from the onboarding service]

Guidelines:
- Use appropriate Islamic greetings and expressions
- Maintain respectful tone for sensitive topics
- Use classical Arabic for Quranic references
- Use modern Arabic for contemporary concepts
- Ensure gender-neutral language where appropriate

Output: JSON object with English keys and Arabic values, plus RTL formatting notes.
"
```

#### Generate RTL Layout Guidelines
```bash
gemini prompt "
Create comprehensive RTL (Right-to-Left) layout guidelines for an Islamic mental wellness React Native app.

Context: Adding Arabic language support requires proper RTL layout implementation.

Requirements:
1. React Native RTL implementation best practices
2. CSS/styling adjustments for RTL
3. Icon and image positioning for RTL
4. Navigation flow adjustments
5. Form layout considerations
6. Typography and spacing guidelines

Areas to cover:
- Text alignment and direction
- Margin and padding adjustments
- Icon positioning (arrows, navigation)
- Progress indicators
- Button layouts
- Modal and popup positioning
- Animation directions

Output: 
1. React Native configuration steps
2. CSS/StyleSheet modifications
3. Component-specific RTL guidelines
4. Testing checklist for RTL layouts
"
```

### 4. Audio Accessibility Implementation

#### Generate Audio Accessibility Code
```bash
gemini prompt "
Create a comprehensive audio accessibility system for a React Native Islamic mental wellness app.

Context: Need to implement text-to-speech, audio controls, and voice input for onboarding questions.

Requirements:
1. Text-to-speech implementation for React Native
2. Audio control components (play, pause, speed)
3. Voice input for simple responses
4. Screen reader compatibility
5. WCAG 2.1 AA compliance
6. Islamic audio content considerations

Features to implement:
- Question narration with Islamic pronunciation
- Audio feedback for user actions
- Voice input for yes/no and simple choice questions
- Audio preferences and settings
- Offline audio capability
- Performance optimization

Output:
1. React Native audio service implementation
2. Audio control components
3. Voice input integration
4. Accessibility testing guidelines
5. Performance optimization tips

Include code examples and implementation steps.
"
```

### 5. Advanced Analytics Implementation

#### Generate Analytics Dashboard Code
```bash
gemini prompt "
Create a comprehensive analytics dashboard for tracking onboarding performance in an Islamic mental wellness app.

Context: Need real-time analytics for onboarding completion, crisis detection, user pathways, and Islamic content effectiveness.

Requirements:
1. React dashboard components
2. Real-time data visualization
3. Islamic-specific metrics
4. Crisis detection analytics
5. User pathway analysis
6. A/B testing framework
7. Performance monitoring

Metrics to track:
- Onboarding completion rates by user type
- Crisis detection accuracy and response times
- User pathway distribution
- Question effectiveness scores
- Islamic content engagement
- Professional categorization accuracy
- Time-to-completion by pathway
- User satisfaction scores

Output:
1. Dashboard component architecture
2. Data visualization components (charts, graphs)
3. Real-time data fetching logic
4. Analytics service implementation
5. A/B testing framework
6. Performance monitoring setup

Include React/TypeScript code examples and database query examples.
"
```

---

## 🔧 Technical Implementation Prompts

### Database Schema Updates

#### Generate Enhanced Schema
```bash
gemini prompt "
Create enhanced PostgreSQL schema updates for granular professional categorization in an Islamic mental wellness app.

Context: Need to add professional specialization fields and related analytics.

Current schema includes:
- onboarding_sessions table
- user_profiles table
- crisis_events table

Requirements:
1. Add professional_specialization fields
2. Create profession-specific analytics tables
3. Add indexes for performance
4. Include RLS policies
5. Create analytics functions
6. Add data validation constraints

New features needed:
- Professional specialization tracking
- Profession-specific crisis indicators
- Enhanced user profiling
- Professional community matching
- Specialized content recommendations

Output:
1. SQL migration scripts
2. Updated table schemas
3. New indexes and constraints
4. RLS policy updates
5. Analytics function definitions
6. Data validation rules
"
```

### AI Service Enhancements

#### Generate Enhanced AI Prompts
```bash
gemini prompt "
Create enhanced AI prompts and logic for an Islamic mental wellness app's profile generation system.

Context: Need to improve AI-generated user profiles with granular professional data and enhanced Islamic personalization.

Current AI generates:
- User pathways
- Personalization settings
- Crisis indicators
- Feature accessibility

Enhancements needed:
1. Professional specialization-aware prompts
2. Enhanced Islamic context integration
3. Improved crisis detection prompts
4. Cultural adaptation logic
5. Confidence scoring improvements

Requirements:
1. Create system prompts for each user type
2. Include Islamic mental health expertise
3. Add professional context awareness
4. Improve personalization accuracy
5. Enhance crisis detection sensitivity

Output:
1. Enhanced system prompts for AI
2. Professional context integration logic
3. Islamic authenticity validation prompts
4. Crisis detection improvement prompts
5. Confidence scoring algorithms
"
```

---

## 🧪 Testing and Quality Assurance

### Generate Test Cases
```bash
gemini prompt "
Create comprehensive test cases for the enhanced onboarding system in an Islamic mental wellness app.

Context: Need to test granular professional categorization, enhanced crisis detection, and Arabic language support.

Requirements:
1. Unit test cases for new question logic
2. Integration test cases for professional flows
3. E2E test cases for complete user journeys
4. Crisis detection test scenarios
5. Arabic/RTL testing scenarios
6. Accessibility testing checklist

Test categories:
- Professional categorization accuracy
- Crisis detection sensitivity and specificity
- Arabic translation accuracy
- RTL layout functionality
- Audio accessibility features
- Performance under load
- Security and privacy compliance

Output:
1. Jest/React Native test cases
2. API integration test scenarios
3. E2E test scripts (Detox/Appium)
4. Crisis detection test data
5. Accessibility testing checklist
6. Performance testing scenarios

Include test data examples and expected outcomes.
"
```

### Generate Performance Optimization
```bash
gemini prompt "
Create performance optimization strategies for an Islamic mental wellness app's onboarding system.

Context: Need to optimize for mobile devices, handle increased question complexity, and support multiple languages.

Current performance targets:
- API response time: <150ms
- Frontend load time: <1.8s
- Database query time: <30ms
- Audio loading time: <500ms

Areas to optimize:
1. Database query optimization
2. API response caching
3. Frontend rendering performance
4. Audio file optimization
5. Image and asset optimization
6. Memory usage optimization

Requirements:
1. Database optimization strategies
2. Caching implementation
3. Frontend performance improvements
4. Audio optimization techniques
5. Asset optimization
6. Memory management

Output:
1. Database optimization queries and indexes
2. Caching strategy implementation
3. React Native performance optimizations
4. Audio compression and streaming
5. Asset bundling and lazy loading
6. Memory profiling and optimization
"
```

---

## 📱 Mobile-Specific Enhancements

### Generate Mobile UX Improvements
```bash
gemini prompt "
Create mobile UX improvements for an Islamic mental wellness app's onboarding experience.

Context: Need to optimize for various mobile devices, improve touch interactions, and enhance visual design.

Current features:
- Adaptive question flow
- Progress tracking
- Crisis intervention
- Audio accessibility

Improvements needed:
1. Enhanced touch interactions
2. Improved visual hierarchy
3. Better progress indication
4. Smooth animations
5. Gesture support
6. Offline capability

Requirements:
1. React Native component improvements
2. Animation and transition enhancements
3. Gesture handling implementation
4. Responsive design improvements
5. Performance optimization for mobile
6. Battery usage optimization

Output:
1. Enhanced React Native components
2. Animation and transition code
3. Gesture handling implementation
4. Responsive design guidelines
5. Mobile performance optimizations
6. Battery usage best practices

Include code examples and implementation guidelines.
"
```

---

## 🎯 Usage Instructions

### How to Use These Prompts:

1. **Copy the prompt** you want to use
2. **Run in terminal**: `gemini prompt "your-prompt-here"`
3. **Save the output** to appropriate files
4. **Review and adapt** the generated code for your specific needs
5. **Test thoroughly** before implementing

### Best Practices:

1. **Review all generated code** for Islamic authenticity
2. **Test generated translations** with native Arabic speakers
3. **Validate crisis detection** with mental health professionals
4. **Performance test** all enhancements
5. **Security review** any new database changes

### Customization:

- **Modify prompts** to match your specific requirements
- **Add context** about your existing codebase
- **Include examples** from your current implementation
- **Specify output format** (TypeScript, JSON, SQL, etc.)

---

## 📋 Completion Checklist

After running the prompts and implementing the code:

- [ ] Professional categorization questions implemented
- [ ] Crisis detection keywords updated
- [ ] Arabic translations completed
- [ ] RTL layout implemented
- [ ] Audio accessibility features added
- [ ] Analytics dashboard created
- [ ] Database schema updated
- [ ] AI service enhanced
- [ ] Test cases written and passing
- [ ] Performance optimized
- [ ] Mobile UX improved
- [ ] Documentation updated

---

*Use these prompts to accelerate your development and ensure comprehensive implementation of all pending features.*