# Feature 3: Qalb Rescue - Pending Documentation Updates Checklist

**Date:** October 26, 2023
**Version:** 1.0

This document serves as a checklist for all documentation updates required for the Qalb Rescue feature following its recent refactoring and enhancements. The goal is to ensure all user-facing, technical, and API documentation accurately reflects the implemented system.

## I. Core Feature Documentation

1.  **File:** `docs/qalb-healing-docs/features/Feature_03_Qalb_Rescue.md` (Main Feature Document)
    *   [ ] **Update: Technical Architecture Section**
        *   Reflect the backend-driven flow managed by `EmergencyService` (Prisma-based).
        *   Detail the new API endpoints used for session start, step progression, and community features.
        *   Describe the AI service's role, its integration point for the 'comfort' step (Quran personalization), and its new database-driven content sourcing.
        *   Mention the new mobile state management (`QalbRescueContext`).
    *   [ ] **Update: Activation Flow Section**
        *   Detail the `POST /emergency/start` API call and its response (includes `sessionId`, initial `currentStep`, and `stepContent`).
    *   [ ] **Update: 5-Step Crisis Intervention Section**
        *   For each of the 5 steps:
            *   Clarify how content is sourced (static from service, dynamic from AI/DB via service).
            *   **Comfort Step:** Specifically mention AI personalization for Quranic verses and the fallback mechanisms (DB query, then hardcoded if necessary).
            *   **Community Connection Step:** Describe the API calls for "Request Du'a" (`POST /emergency/sessions/:id/request-dua`) and "Connect Peer Supporter" (`POST /emergency/sessions/:id/connect-peer`). Detail the backend processes (logging to Prisma `DuaRequestLog`, creating `PeerSupportRequest`). Note that notification systems are pending.
    *   [ ] **Review & Remove Outdated Information:** Remove any references to purely client-side content fetching for steps, old API endpoints, or previous architectural assumptions.

## II. Detailed Flow Documentation

1.  **File:** `docs/qalb-healing-docs/features/flows/Feature_03_Qalb_Rescue_Flows.md`
    *   [ ] **Update/Redraw: Sequence Diagrams** for:
        *   **Qalb Rescue Activation:** Show Mobile App -> `POST /start` -> `EmergencyController` -> `EmergencyService.startQalbRescueSession` (Prisma) -> `EmergencyService.getStepContent` -> returns `sessionId` + first `stepContent`.
        *   **Step Progression:** Show Mobile App -> `POST /sessions/:id/next-step` -> `EmergencyController` -> `EmergencyService.progressQalbRescueSession` (Prisma) -> (conditionally calls `EmergencyService.getStepContent`) -> returns next `stepContent` or completion.
        *   **Quranic Comfort Step (within `getStepContent` detailed flow):** Show `EmergencyService` calling the AI Service (`/content-personalization/recommend`) and handling response/fallback to Prisma DB query (`QuranVerseAiContent` or `RuqyahVerse`).
        *   **Request Du'a (New Diagram):** Mobile App -> `POST /sessions/:id/request-dua` -> `EmergencyController` -> `EmergencyService.requestDuaFromCommunity` -> logs to `DuaRequestLog` (Prisma).
        *   **Connect Peer Supporter (New Diagram):** Mobile App -> `POST /sessions/:id/connect-peer` -> `EmergencyController` -> `EmergencyService.findPeerSupporter` -> queries `Profile` (Prisma), creates `PeerSupportRequest` (Prisma).
    *   [ ] **Update: Textual Descriptions** to match all diagram changes.
    *   [ ] **Update: Backend Components Involved** - Highlight `EmergencyService` as the central orchestrator using Prisma.
    *   [ ] **Update: Data Structures** - Reflect changes in `EmergencySession` (added `currentStep`, `log`), the structure of `StepContent` (title, description, items, audioUrl, etc.), and new request/response payloads for community features.

2.  **File:** `docs/qalb-healing-docs/features/flows/Feature_03_Qalb_Rescue_Mermaid_Flows.md`
    *   [ ] **Update: All Mermaid Diagrams** to visually represent the revised sequences described above.

## III. API Documentation

1.  **File:** `apps/backend/src/routes/emergency.routes.ts` (Swagger Annotations)
    *   [ ] **Review & Verify `POST /emergency/start`:** Ensure Swagger reflects the new response structure (`sessionId`, `currentStep`, `stepContent`). *(This was updated, needs final verification)*.
    *   [ ] **Review & Verify `POST /emergency/sessions/:id/next-step`:** Ensure Swagger accurately documents request payload (`completedStep`, `timeSpentOnStep`) and response (`currentStep`, `stepContent`, or completion message). *(This was updated, needs final verification)*.
    *   [ ] **Review & Verify `PATCH /emergency/sessions/:id`:** Ensure Swagger reflects updated request body (`status` enum, nullable fields, `reasonForEnding`) and response. *(This was updated, needs final verification)*.
    *   [ ] **Review & Verify `POST /emergency/sessions/:id/request-dua`:** Ensure Swagger documents path parameter and success response. *(This was updated, needs final verification)*.
    *   [ ] **Review & Verify `POST /emergency/sessions/:id/connect-peer`:** Ensure Swagger documents path parameter, optional request body (`criteria`), and response structure. *(This was updated, needs final verification)*.
    *   [ ] **Confirm Removal/Deprecation:** Ensure Swagger docs for old, removed individual content GET routes (e.g., `/emergency/breathing`) are no longer present or clearly marked as deprecated if the routes were kept for other purposes.

## IV. Code-Level Documentation

1.  **Backend (`apps/backend/src/services/emergency.service.ts`):**
    *   [ ] Add/Review JSDoc comments for all public methods, especially new ones (`startQalbRescueSession`, `progressQalbRescueSession`, `getStepContent`, `requestDuaFromCommunity`, `findPeerSupporter`) explaining their purpose, parameters, return values, and key logic (e.g., Prisma usage, AI calls, fallbacks).
    *   [ ] Comment complex internal logic if any.
2.  **AI Service (`apps/ai-service/ai_service/processors/content_personalization.py` & `db_utils.py`):**
    *   [ ] Add/Review docstrings for classes and methods, explaining personalization logic, database interaction, and data mapping.
3.  **Mobile App (`apps/mobile-app-v3/`):**
    *   [ ] `services/api/EmergencyService.ts`: Add/Review JSDoc for updated/new methods.
    *   [ ] `state/qalbRescue/`: Add/Review comments for context, reducer, actions, and state structure.
    *   [ ] `screens/QalbRescueScreen.tsx`: Comment complex rendering logic, state consumption, and action dispatching.

## V. Developer & Agent Guidelines

1.  **File:** `AGENTS.md` (Root or relevant sub-directory)
    *   [ ] **Update (if necessary):** Note the new architecture for Qalb Rescue (backend-driven, Prisma-based service, AI integration point, mobile state management).
    *   [ ] Add any new conventions established during this refactor (e.g., for API error handling, state management patterns, testing AI-integrated features).
    *   [ ] Mention the pending tasks from `Feature_03_Qalb_Rescue_Pending_Tasks.md` that require system-level setup or further feature development (like notifications, full chat).

This checklist should guide the process of bringing all documentation in line with the refactored Qalb Rescue feature.
