# Spiritual Assessment and Diagnosis System Implementation

## 1. Introduction

This document outlines the implementation details of the Spiritual Assessment and Diagnosis System within the Qalb Healing application. The system is designed to guide users through a personalized spiritual assessment, analyze their responses based on an Islamic framework, and provide a comprehensive spiritual diagnosis with actionable insights and recommendations.

## 2. Architecture Overview

The Spiritual Assessment and Diagnosis System is distributed across three main application components:

*   **Mobile App (`apps/mobile-app-v3`):** The user-facing frontend responsible for rendering the assessment flow, collecting user input, and displaying the diagnosis results.
*   **Backend (`apps/backend`):** The primary API server that manages assessment sessions, handles data persistence (via Prisma ORM), and orchestrates calls to the AI Service.
*   **AI Service (`apps/ai-service`):** A dedicated microservice responsible for the core spiritual analysis, diagnosis generation, and personalized content creation using a rule-based system.

## 3. Key Components and Interactions

### 3.1. Mobile App (`apps/mobile-app-v3`)

*   **`app/assessment/welcome.tsx`**:
    *   **Purpose:** The initial entry point for the assessment flow. Displays a personalized welcome message.
    *   **Interaction:** Calls `assessmentService.getPersonalizedWelcome()` to fetch welcome content from the backend. On "Start Assessment," it calls `assessmentService.updateSession()` to transition the session state and navigates to `flow.tsx`.
*   **`app/assessment/flow.tsx`**:
    *   **Purpose:** Manages the main, multi-step assessment experience, displaying questions and collecting user responses.
    *   **Interaction:** Fetches session details and questions from the backend (`assessmentService.getSession()`, `assessmentService.getAssessmentQuestions()`). Submits user responses for each step (`assessmentService.submitAssessmentResponse()`). Handles local progress saving and redirection.
*   **`app/assessment/results.tsx`**:
    *   **Purpose:** Displays the spiritual diagnosis generated by the system.
    *   **Interaction:** Fetches the complete diagnosis data from the backend (`assessmentService.getDiagnosis()`). Renders various sections of the diagnosis, including layer insights, recommendations, and next steps.
*   **`services/assessment.service.ts`**:
    *   **Purpose:** A frontend service that abstracts API calls to the backend's assessment endpoints and manages local storage for assessment progress.
    *   **Key Methods:** `startAssessment()`, `getSession()`, `updateSession()`, `getAssessmentQuestions()`, `submitAssessmentResponse()`, `getDiagnosis()`, `saveProgressLocally()`, `clearProgressLocally()`.

### 3.2. Backend (`apps/backend`)

*   **`routes/assessment.routes.ts`**:
    *   **Purpose:** Defines all API endpoints related to the spiritual assessment.
    *   **Key Routes:**
        *   `POST /api/assessment/start`: Initiates a new assessment session.
        *   `POST /api/assessment/welcome`: Generates personalized welcome content.
        *   `GET /api/assessment/:sessionId/questions/:step`: Retrieves questions for a specific assessment step.
        *   `POST /api/assessment/:sessionId/submit`: Submits user responses for a step.
        *   `GET /api/assessment/session/:sessionId`: Retrieves details of an assessment session.
        *   `PUT /api/assessment/session/:sessionId`: **Updates** an existing assessment session (e.g., changing `currentStep`).
        *   `GET /api/assessment/:sessionId/diagnosis`: Retrieves the final spiritual diagnosis.
*   **`controllers/assessment.controller.ts`**:
    *   **Purpose:** Handles incoming HTTP requests, validates input, and orchestrates calls to the `assessmentService`.
    *   **Interaction:** Each route in `assessment.routes.ts` maps to a corresponding method in this controller (e.g., `startAssessment`, `getQuestions`, `submitResponse`, `updateSession`, `getDiagnosis`).
*   **`services/assessment.service.ts`**:
    *   **Purpose:** Contains the core business logic for managing assessment sessions, interacting with the database (via Prisma), and communicating with the AI Service.
    *   **Key Logic:**
        *   Session creation, retrieval, and updates.
        *   Fetching assessment questions from the database.
        *   Processing submitted responses and updating session state.
        *   **Calling `aiService.analyzeSpiritualLandscape()` to generate the diagnosis.**
        *   Persisting diagnosis data in the database.
*   **Prisma ORM (`config/database.ts` and `prisma/schema.prisma`):**
    *   **Purpose:** Used for database interactions, defining the schema for `AssessmentSession`, `SpiritualDiagnosis`, `LayerAnalysis`, and related models.

### 3.3. AI Service (`apps/ai-service`)

*   **`ai_service/processors/spiritual_analysis.py`**:
    *   **Purpose:** The core AI logic for spiritual diagnosis generation. It's a **rule-based system**.
    *   **Input:** Receives comprehensive assessment data (user profile, responses for all layers, session metadata).
    *   **Key Logic:**
        *   **Layer-by-Layer Analysis:** Analyzes responses for each of the five spiritual layers (Jism, Nafs, Aql, Qalb, Ruh) to determine insights, recommendations, and a `severity_score`.
        *   **Primary Layer Identification:** Determines the most affected spiritual layer based on weighted severity scores.
        *   **Crisis Indicator Analysis:** Detects potential crisis situations based on symptom selection and reflection keywords, assigning a `crisis_level` and `immediate_actions`.
        *   **Content Generation:** Generates personalized messages, Islamic insights, educational content, and next steps based on the analysis.
        *   **Confidence Calculation:** Estimates the confidence level of the generated diagnosis.
    *   **Output:** Returns a structured `SpiritualAnalysisResult` object containing the complete diagnosis.
*   **`ai_service/processors/welcome_generator.py`**:
    *   **Purpose:** Generates personalized welcome messages for the assessment based on user profile data.

## 4. Data Flow (Typical Assessment Completion)

1.  **User starts assessment:** Mobile App calls `backend:/api/assessment/start`.
2.  **Backend creates session:** Backend creates a new `AssessmentSession` record in the database and calls `ai-service:/generate-assessment-welcome` to get personalized welcome content.
3.  **Mobile App displays welcome:** Mobile App renders `welcome.tsx`.
4.  **User proceeds:** Mobile App calls `backend:/api/assessment/session/:sessionId` (PUT) to update the session's `currentStep` to `physical_experiences`.
5.  **Mobile App loads flow:** Mobile App navigates to `flow.tsx`. It fetches questions for `physical_experiences` from `backend:/api/assessment/:sessionId/questions/physical_experiences`.
6.  **User answers questions:** User interacts with `flow.tsx`, selecting symptoms and providing reflections.
7.  **User submits step:** Mobile App calls `backend:/api/assessment/:sessionId/submit` with responses for the current step.
8.  **Backend updates session:** Backend updates the `AssessmentSession` record with the submitted responses and determines the `nextStep`.
9.  **Loop (Steps 5-8):** This process repeats for each assessment step (emotional, mental, spiritual, reflections).
10. **Diagnosis Generation:** Once all steps are completed (or `nextStep` is null), the Backend's `assessment.service` calls `ai-service:/spiritual-analysis` with the complete session data.
11. **AI Service analyzes:** The AI Service processes the data using its rule-based logic and returns a `SpiritualAnalysisResult`.
12. **Backend saves diagnosis:** Backend saves the `SpiritualAnalysisResult` as a `SpiritualDiagnosis` record in the database, linked to the `AssessmentSession`.
13. **Mobile App displays results:** Mobile App navigates to `results.tsx` and fetches the final diagnosis from `backend:/api/assessment/:sessionId/diagnosis`.
14. **Diagnosis rendered:** Mobile App displays the personalized spiritual diagnosis to the user.

## 5. Key Concepts

*   **Five Spiritual Layers:** The assessment is structured around the Islamic concept of five layers of human existence: Jism (Physical Body), Nafs (Ego/Lower Self), Aql (Rational Mind), Qalb (Spiritual Heart), and Ruh (Soul).
*   **Rule-Based AI:** The AI Service utilizes a rule-based system for diagnosis generation, ensuring predictability, explainability, and control over sensitive spiritual and health-related advice.
*   **Session Management:** Assessment progress is maintained server-side in the `AssessmentSession` model and locally on the device for resilience and user experience.

## 6. Future Improvements

*   **LLM Integration for Content Refinement:**
    *   **What it could be:** This would involve using a smaller, specialized LLM (e.g., a fine-tuned version of a model like Llama 3, Mistral, or even a highly constrained GPT-3.5/4 instance) rather than a general-purpose, unconstrained model. Its role would be strictly limited to generating natural language text for the `personalized_message`, `islamic_insights`, `educational_content`, and `next_steps` fields, based on the structured output from the existing rule-based AI. It would *not* be responsible for the core diagnostic logic, severity scoring, or crisis detection.
    *   **How it can be achieved:**
        1.  **Data Preparation:** Curate a high-quality dataset of spiritual advice, Islamic teachings, psychological insights, and empathetic language. This dataset would be used to fine-tune the LLM, ensuring its outputs are aligned with Islamic principles and the application's tone.
        2.  **Fine-Tuning:** Train the chosen LLM on this curated dataset. The fine-tuning process would teach the model to generate responses that are contextually appropriate, spiritually sound, and empathetic, given specific structured inputs (e.g., primary layer, severity score, identified symptoms, user type).
        3.  **Prompt Engineering:** Develop robust and precise prompts that guide the LLM to generate the desired content. These prompts would include clear instructions, examples, and constraints to minimize irrelevant or undesirable outputs.
        4.  **Guardrails and Safety Layers:** This is paramount. Implement multiple layers of safety:
            *   **Input Validation:** Ensure only structured, validated data from the rule-based system is fed to the LLM.
            *   **Output Filtering:** Implement post-processing filters (e.g., keyword blacklists, sentiment analysis, factual checks against a knowledge base) to detect and block or modify any potentially harmful, religiously inaccurate, or off-topic content generated by the LLM.
            *   **Human-in-the-Loop Review:** Initially, and periodically, human experts (Islamic scholars, mental health professionals) would review LLM outputs to identify and correct any deviations.
            *   **Constraint-Based Decoding:** Utilize techniques during inference to constrain the LLM's output to specific formats or content types.
        5.  **Continuous Monitoring and Iteration:** Implement automated monitoring of LLM outputs for quality, safety, and adherence to guidelines. Use this feedback to continuously refine the fine-tuning data, prompts, and guardrails.

*   **Dynamic Question Flow:** Implement more dynamic question branching based on early responses to tailor the assessment path more precisely.
*   **Advanced Analytics:** Integrate more sophisticated analytics to track user progress, engagement, and the effectiveness of recommended journeys.
