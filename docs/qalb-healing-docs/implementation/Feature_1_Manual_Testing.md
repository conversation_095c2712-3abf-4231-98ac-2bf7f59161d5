# Manual Testing Steps for Assessment (Feature 1)

This document provides step-by-step instructions to manually test all assessment scenarios for Feature 1: Understanding Your Inner Landscape.

## 1. Standard Assessment Flow
- Complete onboarding and proceed to the assessment.
- Answer all questions with typical, non-crisis responses.
- Verify adaptive question flow and progress bar.
- Complete assessment and confirm results are generated and user receives a diagnosis.

## 2. Crisis Detection During Assessment
- Enter responses that include crisis keywords (e.g., "panic attack", "hopeless").
- Confirm that the crisis modal appears with appropriate Islamic comfort and emergency resources.
- Test all crisis levels (low, moderate, high, critical) by varying responses.
- Verify that the user can access emergency help and that the session is handled safely.

## 3. Incomplete Session Resume
- Start assessment, answer a few questions, then close the app.
- Reopen the app and verify that assessment resumes from the last step.

## 4. Assessment Results & Recommendations
- Complete assessment with different persona types and symptom profiles.
- Confirm that the generated results and recommendations match the input.
- Verify that results are actionable and clear.

## 5. Islamic Content & Cultural Sensitivity
- Review all assessment screens for correct Islamic greetings, terminology, and Quranic comfort.
- Test with different language settings (Arabic, transliteration, English).
- Check for cultural sensitivity in question phrasing.

## 6. Error Handling & Offline Support
- Disconnect from the internet during assessment.
- Verify that offline support and error messages are shown.
- Reconnect and confirm data syncs correctly.

## 7. Accessibility
- Use screen reader and keyboard navigation to complete assessment.
- Check color contrast and font sizes for readability.

## 8. Analytics & Monitoring
- Complete assessment multiple times and verify analytics (completion, abandonment, crisis events) are logged in the dashboard.

---

Follow these steps for each new build to ensure assessment is robust, safe, and user-friendly.
