# Enhanced Crisis Detection System

## 1. Backend Implementation (`crisis-detection.service.ts`)

```typescript
// apps/backend/src/services/crisis-detection.service.ts

import { Prisma } from '@prisma/client';
import { aiService } from './ai.service';
import { emergencyService } from './emergency.service';
import { metricsCollector } from './metrics.service';

export interface CrisisCheckResult {
  isCrisis: boolean;
  level: 'none' | 'low' | 'moderate' | 'high' | 'critical';
  indicators: CrisisIndicator[];
  actions: CrisisAction[];
  confidence: number;
  responseTime: number;
}

export interface CrisisIndicator {
  type:
    | 'suicidal'
    | 'self_harm'
    | 'psychotic'
    | 'severe_depression'
    | 'panic'
    | 'spiritual_reaction';
  severity: number;
  evidence: string[];
  confidence: number;
}

export interface CrisisAction {
  type:
    | 'immediate_intervention'
    | 'emergency_contact'
    | 'professional_referral'
    | 'safety_plan'
    | 'monitoring';
  priority: number;
  description: string;
  resources: any[];
}

export class CrisisDetectionService {
  private crisisKeywords: Map<string, number> = new Map();
  private spiritualCrisisPatterns: Map<string, number> = new Map();
  private responseTimeThreshold = 500; // milliseconds

  constructor() {
    this.initializeCrisisPatterns();
  }

  async analyzeResponse(
    responses: Prisma.JsonValue,
    step: string
  ): Promise<CrisisCheckResult> {
    const startTime = Date.now();

    try {
      // Multi-layered crisis detection
      const severityScore = this.calculateSeverityScore(responses);
      const reflectionText = this.extractReflectionText(responses);
      const keywordAnalysis = await this.analyzeCrisisKeywords(reflectionText);
      const spiritualCrisisScore = this.analyzeSpirituralCrisis(
        responses,
        step
      );
      const behavioralPatterns = this.analyzeBehavioralPatterns(responses);

      // Combine all analysis methods
      const indicators = this.combineIndicators(
        severityScore,
        keywordAnalysis,
        spiritualCrisisScore,
        behavioralPatterns
      );

      const crisisLevel = this.determineCrisisLevel(indicators);
      const actions = await this.getActionsForCrisisLevel(
        crisisLevel,
        indicators
      );
      const confidence = this.calculateConfidence(indicators);

      const responseTime = Date.now() - startTime;

      // Record metrics
      await metricsCollector.recordCrisisDetection({
        level: crisisLevel,
        responseTime,
        confidence,
        step,
        indicatorCount: indicators.length,
      });

      // Immediate action for critical cases
      if (crisisLevel === 'critical') {
        await this.triggerImmediateIntervention(indicators, actions);
      }

      return {
        isCrisis: crisisLevel !== 'none',
        level: crisisLevel,
        indicators,
        actions,
        confidence,
        responseTime,
      };
    } catch (error) {
      await this.handleCrisisDetectionError(error, step);
      throw error;
    }
  }

  private calculateSeverityScore(responses: Prisma.JsonValue): number {
    const responseObj = responses as any;
    let totalScore = 0;
    let questionCount = 0;

    // Analyze severity ratings across all responses
    for (const [key, value] of Object.entries(responseObj)) {
      if (typeof value === 'object' && value !== null) {
        for (const [subKey, subValue] of Object.entries(value as any)) {
          if (this.isSeverityRating(subValue)) {
            totalScore += this.convertSeverityToScore(subValue as string);
            questionCount++;
          }
        }
      } else if (this.isSeverityRating(value)) {
        totalScore += this.convertSeverityToScore(value as string);
        questionCount++;
      }
    }

    return questionCount > 0 ? totalScore / questionCount : 0;
  }

  private extractReflectionText(responses: Prisma.JsonValue): string {
    const responseObj = responses as any;
    let reflectionText = '';

    // Extract all text-based responses
    const extractText = (obj: any): void => {
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string' && value.length > 10) {
          reflectionText += value + ' ';
        } else if (typeof value === 'object' && value !== null) {
          extractText(value);
        }
      }
    };

    extractText(responseObj);
    return reflectionText.trim();
  }

  private async analyzeCrisisKeywords(text: string): Promise<{
    score: number;
    matches: Array<{ keyword: string; context: string; severity: number }>;
  }> {
    if (!text) return { score: 0, matches: [] };

    const matches = [];
    let totalScore = 0;

    // Check for direct crisis keywords
    for (const [keyword, weight] of this.crisisKeywords.entries()) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      const keywordMatches = text.match(regex);

      if (keywordMatches) {
        const context = this.extractContext(text, keyword);
        matches.push({
          keyword,
          context,
          severity: weight,
        });
        totalScore += weight * keywordMatches.length;
      }
    }

    // Use AI for contextual analysis
    const aiAnalysis = await aiService.analyzeCrisisKeywords(text);
    totalScore = Math.max(totalScore, aiAnalysis.score);

    return {
      score: Math.min(totalScore, 10), // Cap at 10
      matches,
    };
  }

  private analyzeSpirituralCrisis(
    responses: Prisma.JsonValue,
    step: string
  ): number {
    const responseObj = responses as any;
    let spiritualScore = 0;

    // Check for spiritual crisis patterns
    for (const [pattern, weight] of this.spiritualCrisisPatterns.entries()) {
      if (this.matchesSpirituralPattern(responseObj, pattern)) {
        spiritualScore += weight;
      }
    }

    // Special handling for ruqya reactions
    if (step.includes('ruqya') || step.includes('spiritual')) {
      spiritualScore *= 1.5; // Amplify spiritual crisis indicators during spiritual assessment
    }

    return Math.min(spiritualScore, 10);
  }

  private analyzeBehavioralPatterns(responses: Prisma.JsonValue): {
    isolationScore: number;
    functionalImpairmentScore: number;
    riskBehaviorScore: number;
  } {
    const responseObj = responses as any;

    return {
      isolationScore: this.calculateIsolationScore(responseObj),
      functionalImpairmentScore:
        this.calculateFunctionalImpairmentScore(responseObj),
      riskBehaviorScore: this.calculateRiskBehaviorScore(responseObj),
    };
  }

  private combineIndicators(
    severityScore: number,
    keywordAnalysis: any,
    spiritualScore: number,
    behavioralPatterns: any
  ): CrisisIndicator[] {
    const indicators: CrisisIndicator[] = [];

    // Suicidal ideation indicator
    if (
      keywordAnalysis.score > 7 ||
      this.hasSuicidalKeywords(keywordAnalysis.matches)
    ) {
      indicators.push({
        type: 'suicidal',
        severity: keywordAnalysis.score,
        evidence: keywordAnalysis.matches.map((m) => m.context),
        confidence: 0.9,
      });
    }

    // Self-harm indicator
    if (this.hasSelfHarmIndicators(keywordAnalysis.matches)) {
      indicators.push({
        type: 'self_harm',
        severity: keywordAnalysis.score * 0.8,
        evidence: keywordAnalysis.matches
          .filter((m) => this.isSelfHarmKeyword(m.keyword))
          .map((m) => m.context),
        confidence: 0.85,
      });
    }

    // Severe depression indicator
    if (severityScore > 8 && behavioralPatterns.functionalImpairmentScore > 7) {
      indicators.push({
        type: 'severe_depression',
        severity:
          (severityScore + behavioralPatterns.functionalImpairmentScore) / 2,
        evidence: [
          'High severity scores across multiple domains',
          'Significant functional impairment',
        ],
        confidence: 0.8,
      });
    }

    // Psychotic symptoms indicator
    if (this.hasPsychoticIndicators(keywordAnalysis.matches)) {
      indicators.push({
        type: 'psychotic',
        severity: keywordAnalysis.score,
        evidence: keywordAnalysis.matches
          .filter((m) => this.isPsychoticKeyword(m.keyword))
          .map((m) => m.context),
        confidence: 0.75,
      });
    }

    // Panic indicator
    if (behavioralPatterns.riskBehaviorScore > 6) {
      indicators.push({
        type: 'panic',
        severity: behavioralPatterns.riskBehaviorScore,
        evidence: ['High risk behavior patterns identified'],
        confidence: 0.7,
      });
    }

    // Spiritual crisis indicator
    if (spiritualScore > 6) {
      indicators.push({
        type: 'spiritual_reaction',
        severity: spiritualScore,
        evidence: ['Severe spiritual crisis patterns detected'],
        confidence: 0.8,
      });
    }

    return indicators;
  }

  private determineCrisisLevel(
    indicators: CrisisIndicator[]
  ): 'none' | 'low' | 'moderate' | 'high' | 'critical' {
    if (indicators.length === 0) return 'none';

    const maxSeverity = Math.max(...indicators.map((i) => i.severity));
    const criticalIndicators = indicators.filter(
      (i) => i.type === 'suicidal' || i.type === 'self_harm'
    );
    const highSeverityCount = indicators.filter((i) => i.severity > 7).length;

    // Critical level
    if (criticalIndicators.length > 0 && maxSeverity > 8) {
      return 'critical';
    }

    // High level
    if (maxSeverity > 7 || highSeverityCount > 2) {
      return 'high';
    }

    // Moderate level
    if (maxSeverity > 5 || indicators.length > 2) {
      return 'moderate';
    }

    // Low level
    if (maxSeverity > 3 || indicators.length > 0) {
      return 'low';
    }

    return 'none';
  }

  private async getActionsForCrisisLevel(
    level: string,
    indicators: CrisisIndicator[]
  ): Promise<CrisisAction[]> {
    const actions: CrisisAction[] = [];

    switch (level) {
      case 'critical':
        actions.push({
          type: 'immediate_intervention',
          priority: 1,
          description: 'Immediate crisis intervention required',
          resources: await emergencyService.getCrisisResources('immediate'),
        });
        actions.push({
          type: 'emergency_contact',
          priority: 2,
          description: 'Contact emergency services if user consents',
          resources: await emergencyService.getEmergencyContacts(),
        });
        break;

      case 'high':
        actions.push({
          type: 'professional_referral',
          priority: 1,
          description: 'Urgent professional mental health referral',
          resources: await emergencyService.getCrisisResources('urgent'),
        });
        actions.push({
          type: 'safety_plan',
          priority: 2,
          description: 'Create immediate safety plan',
          resources: await emergencyService.getSafetyPlanResources(),
        });
        break;

      case 'moderate':
        actions.push({
          type: 'professional_referral',
          priority: 1,
          description: 'Professional mental health consultation recommended',
          resources: await emergencyService.getCrisisResources('standard'),
        });
        actions.push({
          type: 'monitoring',
          priority: 2,
          description: 'Enhanced monitoring and follow-up',
          resources: await emergencyService.getMonitoringResources(),
        });
        break;

      case 'low':
        actions.push({
          type: 'monitoring',
          priority: 1,
          description: 'Regular check-ins and support',
          resources: await emergencyService.getSupportResources(),
        });
        break;
    }

    // Add spiritual-specific actions if spiritual crisis detected
    const spiritualIndicators = indicators.filter(
      (i) => i.type === 'spiritual_reaction'
    );
    if (spiritualIndicators.length > 0) {
      actions.push({
        type: 'professional_referral',
        priority: 3,
        description: 'Islamic spiritual counseling recommended',
        resources: await emergencyService.getIslamicCounselingResources(),
      });
    }

    return actions;
  }

  private calculateConfidence(indicators: CrisisIndicator[]): number {
    if (indicators.length === 0) return 1.0;

    const avgConfidence =
      indicators.reduce((sum, i) => sum + i.confidence, 0) / indicators.length;
    const severityFactor = Math.min(
      Math.max(...indicators.map((i) => i.severity)) / 10,
      1
    );

    return Math.min(avgConfidence * (0.7 + 0.3 * severityFactor), 1.0);
  }

  private async triggerImmediateIntervention(
    indicators: CrisisIndicator[],
    actions: CrisisAction[]
  ): Promise<void> {
    // Activate Qalb Rescue system
    await emergencyService.activateQalbRescue({
      indicators,
      actions,
      timestamp: new Date(),
    });

    // Notify crisis response team
    await emergencyService.notifyCrisisTeam({
      level: 'critical',
      indicators,
      actions,
    });
  }

  // Helper methods
  private initializeCrisisPatterns(): void {
    // Initialize crisis keywords with weights
    this.crisisKeywords.set('suicide', 10);
    this.crisisKeywords.set('kill myself', 10);
    this.crisisKeywords.set('end my life', 10);
    this.crisisKeywords.set('want to die', 9);
    this.crisisKeywords.set('better off dead', 9);
    this.crisisKeywords.set('hopeless', 7);
    this.crisisKeywords.set('worthless', 6);
    this.crisisKeywords.set("can't go on", 8);
    this.crisisKeywords.set('no point', 7);

    // Initialize spiritual crisis patterns
    this.spiritualCrisisPatterns.set('severe_ruqya_reaction', 8);
    this.spiritualCrisisPatterns.set('violent_spiritual_response', 9);
    this.spiritualCrisisPatterns.set('loss_of_consciousness', 10);
    this.spiritualCrisisPatterns.set('uncontrollable_movements', 8);
  }

  private isSeverityRating(value: any): boolean {
    return (
      typeof value === 'string' &&
      ['minimal', 'moderate', 'severe', 'critical'].includes(value)
    );
  }

  private convertSeverityToScore(severity: string): number {
    const scores = { minimal: 1, moderate: 5, severe: 8, critical: 10 };
    return scores[severity] || 0;
  }

  private extractContext(text: string, keyword: string): string {
    const index = text.toLowerCase().indexOf(keyword.toLowerCase());
    if (index === -1) return '';

    const start = Math.max(0, index - 50);
    const end = Math.min(text.length, index + keyword.length + 50);
    return text.substring(start, end);
  }

  private matchesSpirituralPattern(responses: any, pattern: string): boolean {
    // Implementation to match spiritual crisis patterns
    return false; // Placeholder
  }

  private calculateIsolationScore(responses: any): number {
    // Calculate social isolation score
    return 0; // Placeholder
  }

  private calculateFunctionalImpairmentScore(responses: any): number {
    // Calculate functional impairment score
    return 0; // Placeholder
  }

  private calculateRiskBehaviorScore(responses: any): number {
    // Calculate risk behavior score
    return 0; // Placeholder
  }

  private hasSuicidalKeywords(matches: any[]): boolean {
    const suicidalKeywords = [
      'suicide',
      'kill myself',
      'end my life',
      'want to die',
    ];
    return matches.some((m) =>
      suicidalKeywords.includes(m.keyword.toLowerCase())
    );
  }

  private hasSelfHarmIndicators(matches: any[]): boolean {
    const selfHarmKeywords = ['cut myself', 'hurt myself', 'self harm'];
    return matches.some((m) =>
      selfHarmKeywords.includes(m.keyword.toLowerCase())
    );
  }

  private hasPsychoticIndicators(matches: any[]): boolean {
    const psychoticKeywords = ['hearing voices', 'seeing things', 'not real'];
    return matches.some((m) =>
      psychoticKeywords.includes(m.keyword.toLowerCase())
    );
  }

  private isSelfHarmKeyword(keyword: string): boolean {
    return ['cut myself', 'hurt myself', 'self harm'].includes(
      keyword.toLowerCase()
    );
  }

  private isPsychoticKeyword(keyword: string): boolean {
    return ['hearing voices', 'seeing things', 'not real'].includes(
      keyword.toLowerCase()
    );
  }

  private async handleCrisisDetectionError(
    error: any,
    step: string
  ): Promise<void> {
    await metricsCollector.recordError('crisis_detection', {
      error: error.message,
      step,
      timestamp: new Date(),
    });
  }
}

export const crisisDetectionService = new CrisisDetectionService();
```

## 2. AI Service Implementation (`crisis_detection.py`)

```python
# apps/ai-service/ai_service/processors/crisis_detection.py

def analyze_crisis_keywords(text: str) -> int:
    """
    Analyze user-provided text for crisis-related keywords and sentiment.
    Returns a score from 0 to 10.
    """
    # Use a sophisticated NLP model for analysis
    return 0
```
