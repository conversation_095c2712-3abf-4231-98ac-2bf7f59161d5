# Success Metrics & Validation Framework

## 1. Metrics Collection Service (`metrics.service.ts`)

```typescript
// apps/backend/src/services/metrics.service.ts

import { Prisma } from '@prisma/client';

export interface AssessmentMetrics {
  sessionId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  totalDuration?: number;
  layerMetrics: LayerMetrics[];
  completionRate: number;
  accuracyScore?: number;
  userSatisfaction?: number;
  crisisDetections: number;
  interventionsTriggered: number;
}

export interface LayerMetrics {
  layer: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  questionsPresented: number;
  questionsAnswered: number;
  skipRate: number;
  averageResponseTime: number;
  adaptiveRouting: boolean;
  crisisFlags: number;
}

export interface ValidationMetrics {
  assessmentId: string;
  clinicalValidation?: ClinicalValidation;
  followUpOutcomes?: FollowUpOutcome[];
  treatmentEffectiveness?: TreatmentEffectiveness;
  longTermTracking?: LongTermTracking;
}

export interface ClinicalValidation {
  professionalAssessment: string;
  concordanceScore: number; // 0-1 scale
  discrepancies: string[];
  validatedAt: Date;
  validatedBy: string;
}

export interface FollowUpOutcome {
  followUpDate: Date;
  symptomImprovement: number; // -10 to +10 scale
  treatmentAdherence: number; // 0-1 scale
  qualityOfLife: number; // 1-10 scale
  spiritualWellbeing: number; // 1-10 scale
  notes: string;
}

export class MetricsCollector {
  private activeSessionMetrics: Map<string, AssessmentMetrics> = new Map();
  private metricsBuffer: any[] = [];
  private bufferFlushInterval = 30000; // 30 seconds

  constructor() {
    this.initializeMetricsCollection();
  }

  // Assessment Flow Metrics
  async recordAssessmentStart(
    sessionId: string,
    userId: string
  ): Promise<void> {
    const metrics: AssessmentMetrics = {
      sessionId,
      userId,
      startTime: new Date(),
      layerMetrics: [],
      completionRate: 0,
      crisisDetections: 0,
      interventionsTriggered: 0,
    };

    this.activeSessionMetrics.set(sessionId, metrics);
    await this.persistMetrics('assessment_start', metrics);
  }

  async recordLayerMetrics(
    layer: string,
    data: {
      questionsGenerated: number;
      timeEstimate: number;
      progressPercentage: number;
    }
  ): Promise<void> {
    // Implementation for layer metrics recording
    this.addToBuffer('layer_metrics', {
      layer,
      ...data,
      timestamp: new Date(),
    });
  }

  async recordResponseMetrics(
    sessionId: string,
    data: {
      layer?: string;
      responseTime: number;
      crisisDetected: boolean;
    }
  ): Promise<void> {
    const sessionMetrics = this.activeSessionMetrics.get(sessionId);
    if (sessionMetrics) {
      if (data.crisisDetected) {
        sessionMetrics.crisisDetections++;
      }
    }

    this.addToBuffer('response_metrics', {
      sessionId,
      ...data,
      timestamp: new Date(),
    });
  }

  async recordCrisisDetection(data: {
    level: string;
    responseTime: number;
    confidence: number;
    step: string;
    indicatorCount: number;
  }): Promise<void> {
    this.addToBuffer('crisis_detection', { ...data, timestamp: new Date() });
  }

  async recordRuqyaSessionStart(data: {
    sessionId: string;
    userId: string;
    safetyLevel: string;
    sessionType: string;
  }): Promise<void> {
    this.addToBuffer('ruqya_session_start', { ...data, timestamp: new Date() });
  }

  async recordRuqyaStepCompletion(data: {
    sessionId: string;
    step: string;
    reactionCount: number;
    safetyFlags: string[];
  }): Promise<void> {
    this.addToBuffer('ruqya_step_completion', {
      ...data,
      timestamp: new Date(),
    });
  }

  async recordError(context: string, data: any): Promise<void> {
    this.addToBuffer('error', { context, ...data, timestamp: new Date() });
  }

  // Validation Metrics
  async recordClinicalValidation(
    assessmentId: string,
    validation: ClinicalValidation
  ): Promise<void> {
    await prisma.validationMetrics.upsert({
      where: { assessmentId },
      update: { clinicalValidation: validation },
      create: {
        assessmentId,
        clinicalValidation: validation,
      },
    });
  }

  async recordFollowUpOutcome(
    assessmentId: string,
    outcome: FollowUpOutcome
  ): Promise<void> {
    const existing = await prisma.validationMetrics.findUnique({
      where: { assessmentId },
    });

    const followUpOutcomes =
      (existing?.followUpOutcomes as FollowUpOutcome[]) || [];
    followUpOutcomes.push(outcome);

    await prisma.validationMetrics.upsert({
      where: { assessmentId },
      update: { followUpOutcomes },
      create: {
        assessmentId,
        followUpOutcomes,
      },
    });
  }

  // Analytics and Reporting
  async generateAssessmentReport(sessionId: string): Promise<{
    completionMetrics: any;
    accuracyMetrics: any;
    efficiencyMetrics: any;
    safetyMetrics: any;
    userExperienceMetrics: any;
  }> {
    const sessionMetrics = this.activeSessionMetrics.get(sessionId);
    if (!sessionMetrics) {
      throw new Error('Session metrics not found');
    }

    return {
      completionMetrics: await this.calculateCompletionMetrics(sessionMetrics),
      accuracyMetrics: await this.calculateAccuracyMetrics(sessionMetrics),
      efficiencyMetrics: await this.calculateEfficiencyMetrics(sessionMetrics),
      safetyMetrics: await this.calculateSafetyMetrics(sessionMetrics),
      userExperienceMetrics: await this.calculateUserExperienceMetrics(
        sessionMetrics
      ),
    };
  }

  async generateSystemWideReport(timeRange: {
    start: Date;
    end: Date;
  }): Promise<{
    overallPerformance: any;
    crisisDetectionEffectiveness: any;
    userSatisfactionTrends: any;
    clinicalValidationResults: any;
    systemReliability: any;
  }> {
    const metrics = await this.getMetricsInRange(timeRange);

    return {
      overallPerformance: await this.analyzeOverallPerformance(metrics),
      crisisDetectionEffectiveness:
        await this.analyzeCrisisDetectionEffectiveness(metrics),
      userSatisfactionTrends: await this.analyzeUserSatisfactionTrends(metrics),
      clinicalValidationResults: await this.analyzeClinicalValidationResults(
        metrics
      ),
      systemReliability: await this.analyzeSystemReliability(metrics),
    };
  }

  // Real-time Monitoring
  async getRealtimeMetrics(): Promise<{
    activeSessions: number;
    averageSessionDuration: number;
    crisisDetectionRate: number;
    systemLoad: number;
    errorRate: number;
  }> {
    return {
      activeSessions: this.activeSessionMetrics.size,
      averageSessionDuration: await this.calculateAverageSessionDuration(),
      crisisDetectionRate: await this.calculateCrisisDetectionRate(),
      systemLoad: await this.getSystemLoad(),
      errorRate: await this.calculateErrorRate(),
    };
  }

  // Quality Assurance Metrics
  async validateAssessmentQuality(sessionId: string): Promise<{
    dataCompleteness: number;
    responseConsistency: number;
    logicalCoherence: number;
    timelineValidity: number;
    overallQualityScore: number;
  }> {
    const sessionData = await this.getSessionData(sessionId);

    const dataCompleteness = this.calculateDataCompleteness(sessionData);
    const responseConsistency = this.calculateResponseConsistency(sessionData);
    const logicalCoherence = this.calculateLogicalCoherence(sessionData);
    const timelineValidity = this.calculateTimelineValidity(sessionData);

    const overallQualityScore =
      dataCompleteness * 0.3 +
      responseConsistency * 0.25 +
      logicalCoherence * 0.25 +
      timelineValidity * 0.2;

    return {
      dataCompleteness,
      responseConsistency,
      logicalCoherence,
      timelineValidity,
      overallQualityScore,
    };
  }
}
```
