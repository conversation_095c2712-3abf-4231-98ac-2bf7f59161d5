# Progressive Disclosure Assessment Engine

## 1. API Endpoints

**`POST /assessment/session/:sessionId/next`**

- **Description:** Gets the next set of questions in the assessment.
- **Request Body:** `{}`
- **Response Body:**
  ```json
  {
    "nextStep": "layer_2",
    "questions": [
      {
        "id": "q1",
        "text": "...",
        "options": ["..."]
      }
    ]
  }
  ```

**`POST /assessment/session/:sessionId/response`**

- **Description:** Submits a response for the current step.
- **Request Body:**
  ```json
  {
    "step": "layer_1",
    "responses": {
      "q1": "response1"
    }
  }
  ```
- **Response Body:** `{ "status": "success" }`

## 2. Backend Implementation (`assessment.service.ts`)

```typescript
// apps/backend/src/services/assessment.service.ts

import {
  PrismaAssessmentSession,
  AssessmentQuestionClientType,
} from '../types';
import { aiService } from './ai.service';
import { crisisDetectionService } from './crisis-detection.service';
import { metricsCollector } from './metrics.service';

class AssessmentFlowManager {
  private session: PrismaAssessmentSession;
  private startTime: Date;
  private layerTimings: Map<string, { start: Date; end?: Date }> = new Map();

  constructor(session: PrismaAssessmentSession) {
    this.session = session;
    this.startTime = new Date();
  }

  public async getNextStep(): Promise<{
    nextStep: string | null;
    questions: AssessmentQuestionClientType[];
    timeEstimate?: number;
    progressPercentage?: number;
  }> {
    const currentLayer = this.session.currentLayer || 'layer_1';
    this.recordLayerStart(currentLayer);

    let nextStep: string | null = null;
    let questions: AssessmentQuestionClientType[] = [];
    let timeEstimate = 0;
    let progressPercentage = 0;

    try {
      switch (currentLayer) {
        case 'layer_1':
          questions = await this.getLayer1Questions();
          nextStep = 'layer_2';
          timeEstimate = 2;
          progressPercentage = 20;
          break;
        case 'layer_2':
          const layer2Config = await this.getLayer2Config();
          questions = await this.getLayer2Questions(layer2Config);
          nextStep = 'layer_3';
          timeEstimate = layer2Config.estimatedTime || 4;
          progressPercentage = 50;
          break;
        case 'layer_3':
          questions = await this.getLayer3Questions();
          nextStep = 'layer_4';
          timeEstimate = 2;
          progressPercentage = 70;
          break;
        case 'layer_4':
          const layer4Config = await this.getLayer4Config();
          questions = await this.getLayer4Questions(layer4Config);
          nextStep = 'layer_5';
          timeEstimate = 2;
          progressPercentage = 85;
          break;
        case 'layer_5':
          const layer5Config = await this.getLayer5Config();
          questions = await this.getLayer5Questions(layer5Config);
          nextStep = 'complete';
          timeEstimate = 2;
          progressPercentage = 100;
          break;
      }

      // Record metrics for this layer
      await metricsCollector.recordLayerMetrics(currentLayer, {
        questionsGenerated: questions.length,
        timeEstimate,
        progressPercentage,
      });

      return { nextStep, questions, timeEstimate, progressPercentage };
    } catch (error) {
      await this.handleAssessmentError(error, currentLayer);
      throw error;
    }
  }

  public async processResponse(response: any): Promise<{
    crisisDetected?: boolean;
    crisisLevel?: string;
    nextAction?: string;
  }> {
    try {
      // Store the response in the session's responsesJson field
      const currentResponses = (this.session.responsesJson as any) || {};
      const updatedResponses = {
        ...currentResponses,
        [this.session.currentLayer || 'unknown']: response,
      };

      await this.updateSessionResponses(updatedResponses);

      // Perform crisis detection on the response
      const crisisResult = await crisisDetectionService.analyzeResponse(
        response,
        this.session.currentLayer || 'unknown'
      );

      // Record response metrics
      await metricsCollector.recordResponseMetrics(this.session.id, {
        layer: this.session.currentLayer,
        responseTime:
          Date.now() -
            this.layerTimings
              .get(this.session.currentLayer || '')
              ?.start?.getTime() || 0,
        crisisDetected: crisisResult.isCrisis,
      });

      if (crisisResult.isCrisis) {
        return {
          crisisDetected: true,
          crisisLevel: crisisResult.level,
          nextAction: 'crisis_intervention',
        };
      }

      return {};
    } catch (error) {
      await this.handleResponseError(error);
      throw error;
    }
  }

  private async getLayer1Questions(): Promise<AssessmentQuestionClientType[]> {
    // Fetch the 5 category overview questions from the database
    const questions = await this.fetchQuestionsFromDB('layer_1', {
      categories: [
        'physical',
        'emotional',
        'mental',
        'spiritual',
        'life_patterns',
      ],
      questionType: 'impact_overview',
    });

    return questions.map((q) => ({
      ...q,
      examples: this.getContextualExamples(q.category),
      culturalAdaptations: await this.getCulturalAdaptations(q.id),
    }));
  }

  private async getLayer2Config(): Promise<any> {
    // Analyze Layer 1 responses from responsesJson to determine which categories to explore
    const layer1Responses = (this.session.responsesJson as any)?.layer_1 || {};

    const categoriesToExplore = Object.entries(layer1Responses)
      .filter(
        ([_, severity]) => severity === 'moderate' || severity === 'severe'
      )
      .map(([category, _]) => category);

    const estimatedTime = Math.min(2 + categoriesToExplore.length * 1.5, 6);

    return {
      categoriesToExplore,
      estimatedTime,
      adaptiveRouting: true,
      skipCategories: Object.entries(layer1Responses)
        .filter(
          ([_, severity]) => severity === 'minimal' || severity === 'none'
        )
        .map(([category, _]) => category),
    };
  }

  private async getLayer2Questions(
    config: any
  ): Promise<AssessmentQuestionClientType[]> {
    // Fetch targeted deep-dive questions based on the config
    const questions = [];

    for (const category of config.categoriesToExplore) {
      const categoryQuestions = await this.fetchQuestionsFromDB('layer_2', {
        category,
        questionType: 'detailed_exploration',
      });
      questions.push(...categoryQuestions);
    }

    return questions.map((q) => ({
      ...q,
      adaptiveLogic: this.getAdaptiveLogic(q.category),
      skipConditions: this.getSkipConditions(q.id, config.skipCategories),
    }));
  }

  private async getLayer3Questions(): Promise<AssessmentQuestionClientType[]> {
    // Fetch the essential safety net questions - always asked regardless of previous responses
    const safetyQuestions = await this.fetchQuestionsFromDB('layer_3', {
      questionType: 'safety_net',
      priority: 'critical',
    });

    const spiritualIndicators = await this.fetchQuestionsFromDB('layer_3', {
      questionType: 'spiritual_screening',
      priority: 'essential',
    });

    const mentalHealthScreening = await this.fetchQuestionsFromDB('layer_3', {
      questionType: 'mental_health_screening',
      priority: 'essential',
    });

    return [
      ...safetyQuestions,
      ...spiritualIndicators,
      ...mentalHealthScreening,
    ];
  }

  private async getLayer4Config(): Promise<any> {
    // Use AI to detect information gaps and inconsistencies
    const allResponses = this.session.responsesJson as any;
    const gaps = await aiService.detectInformationGaps(allResponses);

    return {
      detectedGaps: gaps.gaps,
      inconsistencies: gaps.inconsistencies,
      followUpQuestions: gaps.recommendedQuestions,
      confidenceScore: gaps.confidenceScore,
    };
  }

  private async getLayer4Questions(
    config: any
  ): Promise<AssessmentQuestionClientType[]> {
    // Generate or fetch follow-up questions based on the AI's analysis
    const questions = [];

    // Add gap-filling questions
    for (const gap of config.detectedGaps) {
      const gapQuestions = await this.generateGapQuestions(gap);
      questions.push(...gapQuestions);
    }

    // Add inconsistency clarification questions
    for (const inconsistency of config.inconsistencies) {
      const clarificationQuestions = await this.generateClarificationQuestions(
        inconsistency
      );
      questions.push(...clarificationQuestions);
    }

    return questions;
  }

  private async getLayer5Config(): Promise<any> {
    // Analyze all previous responses to identify the top 6-8 symptoms
    const allResponses = this.session.responsesJson as any;
    const symptomAnalysis = await aiService.analyzeSymptomSeverity(
      allResponses
    );

    return {
      topSymptoms: symptomAnalysis.topSymptoms.slice(0, 8),
      symptomInteractions: symptomAnalysis.interactions,
      treatmentPriority: symptomAnalysis.treatmentPriority,
    };
  }

  private async getLayer5Questions(
    config: any
  ): Promise<AssessmentQuestionClientType[]> {
    // Fetch questions for the top symptoms for precision analysis
    const questions = [];

    for (const symptom of config.topSymptoms) {
      const precisionQuestions = await this.fetchQuestionsFromDB('layer_5', {
        symptom: symptom.id,
        questionType: 'precision_severity',
      });
      questions.push(...precisionQuestions);
    }

    return questions.map((q) => ({
      ...q,
      symptomContext: config.symptomInteractions[q.symptomId],
      treatmentRelevance: config.treatmentPriority[q.symptomId],
    }));
  }

  // Helper methods
  private recordLayerStart(layer: string): void {
    this.layerTimings.set(layer, { start: new Date() });
  }

  private async updateSessionResponses(responses: any): Promise<void> {
    // Update session in database
    await prisma.assessmentSession.update({
      where: { id: this.session.id },
      data: { responsesJson: responses },
    });
    this.session.responsesJson = responses;
  }

  private async fetchQuestionsFromDB(
    layer: string,
    criteria: any
  ): Promise<any[]> {
    // Implementation to fetch questions from database based on criteria
    return await prisma.assessmentQuestion.findMany({
      where: {
        layer,
        ...criteria,
      },
    });
  }

  private getContextualExamples(category: string): string[] {
    const examples = {
      physical: [
        'Sleep problems',
        'headaches',
        'heart racing',
        'breathing difficulties',
      ],
      emotional: ['Persistent sadness', 'anxiety', 'anger', 'guilt', 'shame'],
      mental: ['Racing thoughts', 'constant worry', 'concentration problems'],
      spiritual: [
        'Prayer concentration problems',
        'feeling distant from Allah',
      ],
      life_patterns: [
        'Sudden family conflicts',
        'repeated failures',
        'marriage problems',
      ],
    };
    return examples[category] || [];
  }

  private async getCulturalAdaptations(questionId: string): Promise<any> {
    // Get cultural adaptations for the question based on user's profile
    return await prisma.culturalAdaptation.findMany({
      where: { questionId },
    });
  }

  private getAdaptiveLogic(category: string): any {
    // Return adaptive logic for dynamic question flow
    return {
      skipIf: `layer1.${category} === 'minimal'`,
      expandIf: `layer1.${category} === 'severe'`,
      timeLimit: category === 'spiritual' ? 300 : 180, // seconds
    };
  }

  private getSkipConditions(questionId: string, skipCategories: string[]): any {
    return {
      skipCategories,
      customConditions: [],
    };
  }

  private async generateGapQuestions(gap: any): Promise<any[]> {
    // Generate questions to fill detected information gaps
    return await aiService.generateGapFillingQuestions(gap);
  }

  private async generateClarificationQuestions(
    inconsistency: any
  ): Promise<any[]> {
    // Generate questions to clarify inconsistencies
    return await aiService.generateClarificationQuestions(inconsistency);
  }

  private async handleAssessmentError(
    error: any,
    layer: string
  ): Promise<void> {
    await metricsCollector.recordError('assessment_flow', {
      layer,
      error: error.message,
      sessionId: this.session.id,
    });
  }

  private async handleResponseError(error: any): Promise<void> {
    await metricsCollector.recordError('response_processing', {
      error: error.message,
      sessionId: this.session.id,
    });
  }
}

export { AssessmentFlowManager };
```
