# Self-Ruqya Diagnosis Module

## 1. API Endpoints

**`POST /ruqya/session/start`**

- **Description:** Starts a new self-diagnosis session.
- **Request Body:** `{ "userId": "..." }`
- **Response Body:** `{ "sessionId": "...", "step": "readiness", "questions": [...] }`

**`POST /ruqya/session/:sessionId/submit`**

- **Description:** Submits responses for a step in the diagnosis.
- **Request Body:** `{ "step": "readiness", "responses": {...} }`
- **Response Body:** `{ "nextStep": "education", "content": "..." }`

## 2. Backend Implementation (`ruqya.service.ts`)

```typescript
// apps/backend/src/services/ruqya.service.ts

import { Prisma } from '@prisma/client';
import { crisisDetectionService } from './crisis-detection.service';
import { emergencyService } from './emergency.service';
import { metricsCollector } from './metrics.service';
import { aiService } from './ai.service';

export interface RuqyaSessionConfig {
  userId: string;
  sessionType: 'diagnostic' | 'therapeutic' | 'educational';
  safetyLevel: 'basic' | 'enhanced' | 'supervised';
  emergencyContact?: string;
  medicalHistory?: any;
}

export interface RuqyaReaction {
  type: 'physical' | 'emotional' | 'spiritual' | 'behavioral';
  intensity: number; // 1-10 scale
  description: string;
  timestamp: Date;
  duration?: number; // in seconds
  triggers?: string[];
  safetyFlags?: string[];
}

export interface SafetyProtocol {
  preSessionChecks: string[];
  duringSessionMonitoring: string[];
  postSessionActions: string[];
  emergencyProcedures: string[];
}

export class RuqyaService {
  private safetyProtocols: Map<string, SafetyProtocol> = new Map();
  private activeSessionMonitoring: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.initializeSafetyProtocols();
  }

  async startSelfDiagnosisSession(config: RuqyaSessionConfig): Promise<{
    sessionId: string;
    step: string;
    questions: any[];
    safetyInstructions: string[];
    emergencyProcedures: string[];
  }> {
    try {
      // Pre-session safety assessment
      const safetyAssessment = await this.conductPreSessionSafetyCheck(config);

      if (!safetyAssessment.approved) {
        throw new Error(`Session not approved: ${safetyAssessment.reason}`);
      }

      // Create session in database
      const session = await prisma.selfDiagnosisSession.create({
        data: {
          userId: config.userId,
          sessionType: config.sessionType,
          safetyLevel: config.safetyLevel,
          emergencyContact: config.emergencyContact,
          medicalHistory: config.medicalHistory,
          status: 'active',
          startedAt: new Date(),
        },
      });

      // Initialize safety monitoring
      await this.initializeSessionMonitoring(session.id, config.safetyLevel);

      // Get readiness assessment questions
      const readinessQuestions = await this.getReadinessQuestions(config);
      const safetyInstructions = this.getSafetyInstructions(config.safetyLevel);
      const emergencyProcedures = this.getEmergencyProcedures();

      // Record session start metrics
      await metricsCollector.recordRuqyaSessionStart({
        sessionId: session.id,
        userId: config.userId,
        safetyLevel: config.safetyLevel,
        sessionType: config.sessionType,
      });

      return {
        sessionId: session.id,
        step: 'readiness',
        questions: readinessQuestions,
        safetyInstructions,
        emergencyProcedures,
      };
    } catch (error) {
      await this.handleRuqyaError(error, 'session_start', config.userId);
      throw error;
    }
  }

  async submitDiagnosisStep(
    sessionId: string,
    step: string,
    responses: any
  ): Promise<{
    nextStep: string | null;
    content?: any;
    reactions?: RuqyaReaction[];
    safetyAlert?: boolean;
    interventionRequired?: boolean;
  }> {
    try {
      const session = await this.getSession(sessionId);

      // Real-time safety monitoring during response processing
      const safetyCheck = await this.performRealTimeSafetyCheck(
        responses,
        step
      );

      if (safetyCheck.interventionRequired) {
        await this.triggerSafetyIntervention(sessionId, safetyCheck);
        return {
          nextStep: null,
          safetyAlert: true,
          interventionRequired: true,
        };
      }

      // Store responses
      await this.storeStepResponses(sessionId, step, responses);

      // Analyze reactions if present
      const reactions = await this.analyzeReactions(responses, step);

      // Determine next step based on current step and responses
      const nextStep = await this.determineNextStep(
        session,
        step,
        responses,
        reactions
      );

      // Update session monitoring based on reactions
      if (reactions.length > 0) {
        await this.updateSessionMonitoring(sessionId, reactions);
      }

      // Record step completion metrics
      await metricsCollector.recordRuqyaStepCompletion({
        sessionId,
        step,
        reactionCount: reactions.length,
        safetyFlags: reactions.flatMap((r) => r.safetyFlags || []),
      });

      return {
        nextStep,
        content: await this.getStepContent(nextStep, session),
        reactions,
        safetyAlert: safetyCheck.alertLevel > 0,
      };
    } catch (error) {
      await this.handleRuqyaError(error, 'step_submission', sessionId);
      throw error;
    }
  }

  async getDiagnosisResults(sessionId: string): Promise<{
    overallAssessment: any;
    detectedConditions: string[];
    reactionSummary: any;
    recommendations: string[];
    safetyReport: any;
    followUpRequired: boolean;
  }> {
    try {
      const session = await this.getSessionWithAllData(sessionId);

      // Compile all reactions
      const allReactions = await this.compileAllReactions(session);

      // AI-powered analysis of reactions
      const aiAnalysis = await aiService.analyzeRuqyaReactions(allReactions);

      // Generate comprehensive assessment
      const overallAssessment = await this.generateOverallAssessment(
        session,
        allReactions,
        aiAnalysis
      );

      // Detect potential spiritual conditions
      const detectedConditions = await this.detectSpiritualConditions(
        allReactions,
        aiAnalysis
      );

      // Create reaction summary
      const reactionSummary = this.createReactionSummary(allReactions);

      // Generate recommendations
      const recommendations = await this.generateRecommendations(
        overallAssessment,
        detectedConditions,
        session
      );

      // Compile safety report
      const safetyReport = await this.compileSafetyReport(sessionId);

      // Determine if follow-up is required
      const followUpRequired = this.assessFollowUpNeed(
        overallAssessment,
        detectedConditions,
        safetyReport
      );

      // Complete session
      await this.completeSession(sessionId, {
        overallAssessment,
        detectedConditions,
        reactionSummary,
        recommendations,
        safetyReport,
        followUpRequired,
      });

      return {
        overallAssessment,
        detectedConditions,
        reactionSummary,
        recommendations,
        safetyReport,
        followUpRequired,
      };
    } catch (error) {
      await this.handleRuqyaError(error, 'results_generation', sessionId);
      throw error;
    }
  }

  // Safety Protocol Methods
  private async conductPreSessionSafetyCheck(
    config: RuqyaSessionConfig
  ): Promise<{
    approved: boolean;
    reason?: string;
    recommendations?: string[];
  }> {
    const checks = [];

    // Check user's mental health status
    const mentalHealthStatus = await this.checkUserMentalHealthStatus(
      config.userId
    );
    if (
      mentalHealthStatus.crisisLevel === 'high' ||
      mentalHealthStatus.crisisLevel === 'critical'
    ) {
      return {
        approved: false,
        reason:
          'User currently in mental health crisis - ruqya session not recommended',
        recommendations: ['Seek immediate professional mental health support'],
      };
    }

    // Check for medical contraindications
    if (config.medicalHistory) {
      const medicalCheck = await this.checkMedicalContraindications(
        config.medicalHistory
      );
      if (!medicalCheck.approved) {
        return medicalCheck;
      }
    }

    // Verify emergency contact for enhanced/supervised sessions
    if (config.safetyLevel !== 'basic' && !config.emergencyContact) {
      return {
        approved: false,
        reason: 'Emergency contact required for enhanced safety sessions',
        recommendations: ['Provide emergency contact information'],
      };
    }

    return { approved: true };
  }

  private async initializeSessionMonitoring(
    sessionId: string,
    safetyLevel: string
  ): Promise<void> {
    const monitoringInterval = this.getMonitoringInterval(safetyLevel);

    const monitoringTimer = setInterval(async () => {
      await this.performPeriodicSafetyCheck(sessionId);
    }, monitoringInterval);

    this.activeSessionMonitoring.set(sessionId, monitoringTimer);

    // Set session timeout based on safety level
    const sessionTimeout = this.getSessionTimeout(safetyLevel);
    setTimeout(async () => {
      await this.handleSessionTimeout(sessionId);
    }, sessionTimeout);
  }

  private async performRealTimeSafetyCheck(
    responses: any,
    step: string
  ): Promise<{
    alertLevel: number;
    interventionRequired: boolean;
    flags: string[];
  }> {
    const flags = [];
    let alertLevel = 0;

    // Check for severe reactions
    if (responses.reactions) {
      for (const reaction of responses.reactions) {
        if (reaction.intensity > 8) {
          flags.push('severe_reaction');
          alertLevel = Math.max(alertLevel, 2);
        }

        if (reaction.type === 'physical' && reaction.intensity > 6) {
          flags.push('concerning_physical_reaction');
          alertLevel = Math.max(alertLevel, 1);
        }

        // Check for dangerous reactions
        if (this.isDangerousReaction(reaction)) {
          flags.push('dangerous_reaction');
          alertLevel = 3;
        }
      }
    }

    // Check response content for crisis indicators
    const crisisCheck = await crisisDetectionService.analyzeResponse(
      responses,
      step
    );
    if (crisisCheck.isCrisis) {
      flags.push('crisis_detected');
      alertLevel = Math.max(
        alertLevel,
        crisisCheck.level === 'critical' ? 3 : 2
      );
    }

    return {
      alertLevel,
      interventionRequired: alertLevel >= 3,
      flags,
    };
  }

  private async triggerSafetyIntervention(
    sessionId: string,
    safetyCheck: any
  ): Promise<void> {
    // Pause session immediately
    await this.pauseSession(sessionId, 'safety_intervention');

    // Activate emergency protocols
    await emergencyService.activateRuqyaEmergencyProtocol({
      sessionId,
      alertLevel: safetyCheck.alertLevel,
      flags: safetyCheck.flags,
      timestamp: new Date(),
    });

    // Notify emergency contact if available
    const session = await this.getSession(sessionId);
    if (session.emergencyContact) {
      await emergencyService.notifyEmergencyContact(
        session.emergencyContact,
        'ruqya_safety_intervention',
        { sessionId, flags: safetyCheck.flags }
      );
    }
  }

  private async analyzeReactions(
    responses: any,
    step: string
  ): Promise<RuqyaReaction[]> {
    if (!responses.reactions) return [];

    const analyzedReactions: RuqyaReaction[] = [];

    for (const reaction of responses.reactions) {
      const analyzed: RuqyaReaction = {
        type: reaction.type,
        intensity: reaction.intensity,
        description: reaction.description,
        timestamp: new Date(),
        duration: reaction.duration,
        triggers: reaction.triggers || [],
        safetyFlags: [],
      };

      // Add safety flags based on reaction characteristics
      if (reaction.intensity > 8) {
        analyzed.safetyFlags.push('high_intensity');
      }

      if (reaction.type === 'physical' && reaction.intensity > 6) {
        analyzed.safetyFlags.push('concerning_physical');
      }

      if (this.isDangerousReaction(reaction)) {
        analyzed.safetyFlags.push('dangerous');
      }

      // AI analysis for additional insights
      const aiInsights = await aiService.analyzeIndividualReaction(
        reaction,
        step
      );
      if (aiInsights.concernLevel > 7) {
        analyzed.safetyFlags.push('ai_flagged');
      }

      analyzedReactions.push(analyzed);
    }

    return analyzedReactions;
  }

  private async determineNextStep(
    session: any,
    currentStep: string,
    responses: any,
    reactions: RuqyaReaction[]
  ): Promise<string | null> {
    // Check if safety intervention is needed
    const highIntensityReactions = reactions.filter((r) => r.intensity > 8);
    if (highIntensityReactions.length > 0) {
      return 'safety_assessment';
    }

    // Standard flow progression
    switch (currentStep) {
      case 'readiness':
        if (this.isReadyForDiagnosis(responses)) {
          return 'education';
        } else {
          return 'preparation';
        }

      case 'preparation':
        return 'education';

      case 'education':
        return 'sihr_diagnosis';

      case 'sihr_diagnosis':
        return 'ayn_diagnosis';

      case 'ayn_diagnosis':
        return 'mass_diagnosis';

      case 'mass_diagnosis':
        return 'waswas_diagnosis';

      case 'waswas_diagnosis':
        return 'comprehensive_analysis';

      case 'comprehensive_analysis':
        return 'results';

      case 'safety_assessment':
        if (this.isSafeTocontinue(responses, reactions)) {
          return this.getPreviousStep(session);
        } else {
          return null; // End session
        }

      default:
        return null;
    }
  }

  // Helper Methods
  private initializeSafetyProtocols(): void {
    this.safetyProtocols.set('basic', {
      preSessionChecks: [
        'Verify user mental health status',
        'Confirm understanding of process',
        'Ensure private, safe environment',
      ],
      duringSessionMonitoring: [
        'Monitor reaction intensity',
        'Check for crisis indicators',
        'Validate response coherence',
      ],
      postSessionActions: [
        'Assess overall session safety',
        'Provide aftercare instructions',
        'Schedule follow-up if needed',
      ],
      emergencyProcedures: [
        'Immediate session termination',
        'Crisis intervention activation',
        'Professional referral if needed',
      ],
    });

    this.safetyProtocols.set('enhanced', {
      preSessionChecks: [
        'Comprehensive mental health screening',
        'Medical history review',
        'Emergency contact verification',
        'Supervised environment confirmation',
      ],
      duringSessionMonitoring: [
        'Real-time reaction analysis',
        'Continuous crisis detection',
        'Physiological monitoring if available',
        'Emergency contact standby',
      ],
      postSessionActions: [
        'Detailed safety assessment',
        'Comprehensive aftercare plan',
        'Mandatory follow-up scheduling',
        'Professional consultation if indicated',
      ],
      emergencyProcedures: [
        'Immediate intervention protocols',
        'Emergency contact notification',
        'Professional emergency services',
        'Comprehensive incident reporting',
      ],
    });
  }

  private getMonitoringInterval(safetyLevel: string): number {
    const intervals = {
      basic: 300000, // 5 minutes
      enhanced: 120000, // 2 minutes
      supervised: 60000, // 1 minute
    };
    return intervals[safetyLevel] || intervals.basic;
  }

  private getSessionTimeout(safetyLevel: string): number {
    const timeouts = {
      basic: 3600000, // 1 hour
      enhanced: 2700000, // 45 minutes
      supervised: 1800000, // 30 minutes
    };
    return timeouts[safetyLevel] || timeouts.basic;
  }

  private isDangerousReaction(reaction: any): boolean {
    const dangerousPatterns = [
      'loss of consciousness',
      'violent behavior',
      'self-harm',
      'uncontrollable movements',
      'breathing difficulties',
      'chest pain',
    ];

    return dangerousPatterns.some((pattern) =>
      reaction.description.toLowerCase().includes(pattern)
    );
  }

  private async getSession(sessionId: string): Promise<any> {
    return await prisma.selfDiagnosisSession.findUnique({
      where: { id: sessionId },
    });
  }

  private async handleRuqyaError(
    error: any,
    context: string,
    identifier: string
  ): Promise<void> {
    await metricsCollector.recordError('ruqya_service', {
      context,
      identifier,
      error: error.message,
      timestamp: new Date(),
    });
  }

  // Additional helper methods would be implemented here...
  private async getReadinessQuestions(
    config: RuqyaSessionConfig
  ): Promise<any[]> {
    // Implementation for readiness questions
    return [];
  }

  private getSafetyInstructions(safetyLevel: string): string[] {
    // Implementation for safety instructions
    return [];
  }

  private getEmergencyProcedures(): string[] {
    // Implementation for emergency procedures
    return [];
  }

  // ... other helper methods
}

export const ruqyaService = new RuqyaService();
```
