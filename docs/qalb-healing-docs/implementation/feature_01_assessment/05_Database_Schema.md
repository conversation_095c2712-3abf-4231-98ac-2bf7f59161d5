# Database Schema & Phased Implementation

## 1. Phased Implementation Plan

- **Phase 1 (2-3 months):** Core Progressive Disclosure & Enhanced Crisis Detection
- **Phase 2 (4-6 months):** Self-Ruqya Diagnosis & Initial Personalization
- **Phase 3 (12+ months):** Advanced Features (Zero-Knowledge, Internationalization)

## 2. Database Schema Changes

```prisma
// prisma/schema.prisma

model AssessmentSession {
  id        String   @id @default(cuid())
  userId    String
  // ... other fields
  currentLayer String? @default("layer_1")
  responsesJson Json?
}

model SelfDiagnosisSession {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  completedAt DateTime?
  readinessResponses Json?
  sihrReactions      Json?
  aynReactions       Json?
  massReactions      Json?
  waswasReactions    Json?
  results            Json?
}
```
