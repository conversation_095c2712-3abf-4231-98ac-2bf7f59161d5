# Manual Testing Steps for Onboarding (Feature 0)

This document provides step-by-step instructions to manually test all onboarding scenarios for Feature 0: Adaptive Onboarding & User Profiling.

## 1. Standard Onboarding Flow
- Launch the app and start onboarding as a new user.
- Answer all questions with typical, non-crisis responses.
- Verify adaptive question flow and progress bar.
- Complete onboarding and confirm profile is generated and user is welcomed.

## 2. Crisis Detection Flow
- Start onboarding and enter responses that include crisis keywords (e.g., "I want to die", "panic attack").
- Confirm that the crisis modal appears with appropriate Islamic comfort and emergency resources.
- Test all crisis levels (low, moderate, high, critical) by varying responses.
- Verify that the user can access emergency help and that the session is handled safely.

## 3. Emergency Skip
- During onboarding, use the emergency skip option.
- Provide a reason when prompted.
- Confirm that the session is paused/skipped and the reason is logged.
- Resume onboarding and verify progress is saved.

## 4. Incomplete Session Resume
- Start onboarding, answer a few questions, then close the app.
- Reopen the app and verify that onboarding resumes from the last step.

## 5. Profile Generation & Pathway Assignment
- Complete onboarding with different persona types (e.g., new Muslim, healthcare professional, spiritual optimizer).
- Confirm that the generated profile and recommended pathway match the input.

## 6. Islamic Content & Cultural Sensitivity
- Review all onboarding screens for correct Islamic greetings, terminology, and Quranic comfort.
- Test with different language settings (Arabic, transliteration, English).
- Check for cultural sensitivity in question phrasing.

## 7. Error Handling & Offline Support
- Disconnect from the internet during onboarding.
- Verify that offline support and error messages are shown.
- Reconnect and confirm data syncs correctly.

## 8. Accessibility
- Use screen reader and keyboard navigation to complete onboarding.
- Check color contrast and font sizes for readability.

## 9. Analytics & Monitoring
- Complete onboarding multiple times and verify analytics (completion, abandonment, crisis events) are logged in the dashboard.

---

Follow these steps for each new build to ensure onboarding is robust, safe, and user-friendly.
