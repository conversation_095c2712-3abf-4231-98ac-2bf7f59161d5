# Manual Testing Steps for Personalized Healing Journeys (Feature 2)

This document provides step-by-step instructions to manually test all journey scenarios for Feature 2: Personalized Healing Journeys.

## 1. Journey Creation Flow
- Complete onboarding and assessment, then proceed to journey creation.
- Select preferences and start a new journey.
- Verify that the journey is personalized based on assessment results.
- Confirm daily practices and progress tracking are available.

## 2. Daily Practice Completion
- Complete daily practices and mark them as done.
- Verify progress is updated and feedback is provided.
- Test reminders and notifications for daily tasks.

## 3. Journey Interruption & Resume
- Pause or exit a journey in progress.
- Reopen the app and verify that the journey resumes from the last step.
- Test journey interruption scenarios (e.g., crisis, user-initiated pause).

## 4. Community & Support Integration
- Access community features and support resources from the journey dashboard.
- Verify that support is contextually relevant to the journey.

## 5. Islamic Content & Cultural Sensitivity
- Review all journey screens for correct Islamic greetings, terminology, and Quranic comfort.
- Test with different language settings (Arabic, transliteration, English).
- Check for cultural sensitivity in journey recommendations.

## 6. Error Handling & Offline Support
- Disconnect from the internet during a journey.
- Verify that offline support and error messages are shown.
- Reconnect and confirm data syncs correctly.

## 7. Accessibility
- Use screen reader and keyboard navigation to complete journey tasks.
- Check color contrast and font sizes for readability.

## 8. Analytics & Monitoring
- Complete journeys multiple times and verify analytics (completion, abandonment, progress) are logged in the dashboard.

---

Follow these steps for each new build to ensure journeys are robust, safe, and user-friendly.
