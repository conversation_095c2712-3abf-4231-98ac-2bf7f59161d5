# Pending Testing Areas and Technical Debt

This document tracks areas in the Qalb Healing project that require further testing, clarification, or refactoring to improve code quality, test coverage, and overall robustness.

## I. Cross-Cutting Testing Concerns

### 1. End-to-End (E2E) Testing
*   **Status**: `apps/backend-e2e/` directory exists.
*   **Pending**:
    *   Review existing E2E test scenarios for coverage of critical user flows, especially for features like Assessment (Feature 01), Onboarding, and upcoming Healing Journeys.
    *   Expand E2E tests to include more edge cases and user personas.
    *   Ensure E2E tests validate the integration between frontend (if applicable to test scope), backend, and AI services.

### 2. Unit Tests for Helper/Utility Functions
*   **Backend (`apps/backend/src/...`)**:
    *   **`utils/`**: While assessment-related helpers in `assessmentUserProfile.helper.ts` have improved coverage, a general review of other utilities for testability and coverage is pending.
    *   **`mappers/`**: Other mappers besides `aiDiagnosis.mapper.ts` might need unit tests.
    *   **`presenters/`**: Other presenters besides `diagnosisDelivery.presenter.ts` might need unit tests.
*   **AI Service (`apps/ai-service/ai_service/...`)**:
    *   Review for any common utility functions or static methods within processors that are complex enough to warrant dedicated unit tests.

### 3. TypeScript AI Components (`apps/ai-service/__tests__`)
*   **Status**: A suite of TypeScript/NestJS style tests exists.
*   **Pending**:
    *   **Clarification Needed**: Determine the exact purpose, current operational status, and relevance of these TypeScript components and tests in relation to the primary Python-based AI services.
    *   If active and relevant, conduct a full review of their test coverage.

## II. Feature-Specific Pending Items

### Feature: Understanding Your Inner Landscape / Assessment (Corresponds to old "Feature 01")

*   **AI Service (`apps/ai-service`)**:
    *   **Adaptive Diagnosis Structure Clarification Follow-up**:
        *   **Context**: Current AI (`SpiritualAnalysisProcessor`) returns a unified JSON structure with content adaptation. Feature documentation had illustrative examples of structurally different JSON outputs per user type.
        *   **Decision Point (Action for PO/Architect)**: Confirm if the current unified structure is sufficient, with the backend's presentation layer handling any UI-specific structural variations. (Decision was made to keep unified structure from AI, backend to adapt for UI if needed).
        *   **Pending (If distinct AI output structures were *later* required despite current decision)**: Major refactor of `SpiritualAnalysisProcessor` and its tests would be needed. (Low priority given current decision).
    *   **Depth of Content Adaptation (Following current unified structure approach)**:
        *   **`islamic_insights`**: Currently mostly diagnosis-driven. Enhance `spiritual_analysis.py` to provide more tailored insights based on `user_type` (e.g., simpler for new Muslims, scholarly for Imams) and add corresponding tests.
        *   **`educational_content`**: Beyond the new_muslim intro and primary layer context, the main five-layer explanation is static. Enhance `spiritual_analysis.py` to adapt this more deeply for other user knowledge levels (e.g., "Advanced User" vs "New User" examples from feature doc beyond just the intro) and add tests.
    *   **`symptom_analyzer.py` (`apps/ai-service/ai_service/processors/`)**:
        *   **Role Clarification**: Its exact role in conjunction with `SpiritualAnalysisProcessor` for the main assessment diagnosis needs to be definitively clear. It seems `SpiritualAnalysisProcessor` handles the core Feature 01 diagnosis logic.
        *   **Test Review**: If deemed actively used and important for Feature 01 by extension, a deeper review of its tests (`test_symptom_analyzer.py`) for full coverage of its OpenAI interaction (prompt variations, response parsing for different valid/malformed AI outputs) and its specific fallback mechanisms might be useful. (Current coverage is good but can always be deepened for LLM-based components).
    *   **`crisis_analysis.py` Endpoint (`apps/ai-service/ai_service/endpoints/`)**:
        *   **String Conversion of `request.response`**: The endpoint converts `response: Dict[str, Any]` to a single string. While basic tests for this were added, more exhaustive testing of how various complex/nested dictionary structures for `request.response` translate to the string and how that impacts keyword detection would improve robustness.
*   **Backend (`apps/backend`)**:
    *   **`assessmentUserProfile.helper.ts` Discrepancy**: The logic in `determineUserTypeFromProfile` in this backend helper (e.g., for 'new_muslim' identification) differs from the `determine_user_type` method in the Python AI's `SpiritualAnalysisProcessor` and `WelcomeGeneratorProcessor`.
        *   **Pending**: Clarify if these user typing mechanisms should be aligned or if they serve distinct documented purposes. If alignment is needed, refactor one and update its tests.

### Feature: Onboarding (Corresponds to old "Feature 0")
*   **AI Service (`apps/ai-service`)**:
    *   **`profile_generation.py` Endpoint**:
        *   **Placeholder Crisis Logic**: The crisis detection within this endpoint is a simple placeholder.
            *   **Pending**: Decide if this should be integrated with the main `crisis_analysis.py` endpoint/logic or if its current placeholder status is acceptable for onboarding profile generation. If integration is needed, refactor and update tests.
        *   **Authentication**: Clarify if this endpoint requires authentication; tests currently run without explicit auth mocking.
        *   **Input Validation for `responses` values**: Deeper testing for unexpected *values* within the `responses: Dict[str, Any]` if not fully covered by Pydantic's structural validation.
*   **Backend (`apps/backend`)**:
    *   Review and ensure comprehensive test coverage for onboarding related controllers and services.

### Feature: Personalized Healing Journeys (Corresponds to old "Feature 02")
*   **AI Service (`apps/ai-service`)**:
    *   Endpoints related to journey generation (e.g., `/journey/generate-parameters`, `/journey/generate-content` in `ai_service/main.py`).
    *   Processor: `ai_service/processors/journey_generation.py`.
    *   **Pending**: Full review of implementation and comprehensive testing (unit tests for processor, integration tests for endpoints).
*   **Backend (`apps/backend`)**:
    *   Services and controllers related to managing and delivering healing journeys.
    *   **Pending**: Full review and comprehensive testing.

## III. Other Services/Modules Pending Review

*   **AI Service (`apps/ai-service`)**:
    *   `ai_service/processors/content_recommender.py` and its endpoint `/recommend-content`.
    *   Any other processors or endpoints not yet covered.
*   **Backend (`apps/backend`)**:
    *   Review other services (e.g., `UserService`, any notification services, etc.) and their controllers for test coverage.

This document should be updated as items are addressed or new areas are identified.
