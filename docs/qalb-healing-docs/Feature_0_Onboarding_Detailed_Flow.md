# Feature 0: Adaptive Onboarding & User Profiling - Detailed Flow

This document outlines the detailed flows for the adaptive onboarding feature (Feature 0) in the Qalb Healing platform. It covers both frontend (mobile app) and backend interactions.

## 1. Overall Goal

The primary goal of the onboarding process is to:
- Gather essential information about the user's current state, awareness, preferences, and context.
- Detect any immediate crisis indicators.
- Generate a comprehensive user profile using AI-assisted analysis.
- Recommend a personalized healing pathway and configure initial features.
- Provide clear next steps for the user.

## 2. Core Onboarding Flow (Step-by-Step AI-Integrated)

This is the main, recommended flow that utilizes adaptive questioning and AI for profile generation.

### 2.1. Starting the Onboarding Process

1.  **Trigger (Frontend - Mobile App)**:
    *   User initiates the onboarding process (e.g., after signup, or by selecting an "Start Onboarding" option).
    *   The `OnboardingScreen` (`apps/mobile-app-v3/src/app/onboarding/index.tsx`) is displayed.

2.  **API Call (Frontend to Backend)**:
    *   The `initializeOnboarding` function in `OnboardingScreen` is called.
    *   It invokes `onboardingService.startOnboarding(deviceInfo)` from `apps/mobile-app-v3/src/services/onboarding.service.ts`.
    *   This service makes a `POST` request to the backend: `POST /api/onboarding/start`.
    *   The request includes `deviceInfo` and the user's authentication token in the headers.

3.  **Backend Processing (`POST /api/onboarding/start`)**:
    *   Handled by `startOnboarding` in `apps/backend/src/controllers/onboarding.controller.ts`.
    *   The controller calls `onboardingService.startOnboarding(userId, deviceInfo)` from `apps/backend/src/services/onboarding.service.ts`.
    *   **Backend Service (`apps/backend/src/services/onboarding.service.ts`)**:
        *   A new `OnboardingSession` is created in the database (via Prisma: `prisma.onboardingSession.create()`) with a unique `sessionId`, `userId`, `startedAt`, `currentStep: 'welcome'`, and `deviceInfo`.
        *   It then calls its internal `getNextQuestion(sessionId, {})` method.
        *   `determineNextStep('welcome', {})` resolves to the first actual question step (e.g., `'mental_health_awareness'`).
        *   `getQuestionForStep(...)` retrieves the question definition for this first step.
        *   The service returns the created session object and the first question structure.
    *   The controller formats this into the API response.

4.  **Response to Frontend**:
    *   Backend responds with `201 Created`. Payload includes:
        ```json
        {
          "status": "success",
          "data": {
            "session": { "sessionId": "...", "startedAt": "...", "currentStep": "welcome" },
            "question": { // Wrapper for the first question
              "step": "actual_first_step_id", // e.g., mental_health_awareness
              "question": { /* Definition of the first question */ },
              "progress": 0
            }
          }
        }
        ```

5.  **Frontend State Update**:
    *   `OnboardingScreen` receives the response.
    *   It updates its state with `sessionId`, `currentQuestion` (extracting the actual question from `response.data.question.question`), and `progress`.
    *   The UI transitions from loading to displaying the first question (e.g., a welcome message or the first interactive question via `WelcomeScreen` or `OnboardingQuestion` component).

### 2.2. Responding to a Question

1.  **User Interaction (Frontend)**:
    *   User interacts with the displayed question in `OnboardingQuestion.tsx` (or `WelcomeScreen.tsx`).
    *   An option is selected, text is input, etc.
    *   The component calls its `onResponse` prop, which triggers `handleResponse(userAnswer)` in `OnboardingScreen.tsx`.

2.  **API Call (Frontend to Backend)**:
    *   `handleResponse` in `OnboardingScreen.tsx` sets `isLoading: true`.
    *   It calls `onboardingService.submitResponse({ sessionId, stepId, response: userAnswer, timeSpent })` from the frontend service.
    *   This service makes a `POST` request to `POST /api/onboarding/respond`.
    *   Payload includes `sessionId`, `stepId` (current question ID), the `userAnswer`, and `timeSpent`.

3.  **Backend Processing (`POST /api/onboarding/respond`)**:
    *   Handled by `submitResponse` in `apps/backend/src/controllers/onboarding.controller.ts`.
    *   The controller calls `onboardingService.submitResponse(...)` from the backend service.
    *   **Backend Service (`apps/backend/src/services/onboarding.service.ts`)**:
        *   Retrieves the current `OnboardingSession` using `getSession(sessionId)`.
        *   Performs crisis analysis on the `userAnswer` via `CrisisDetectionService.analyzeResponse()`.
            *   **If Crisis Detected**:
                *   The service immediately returns a crisis structure (e.g., `{ type: 'crisis_detected', level: 'high', ... }`).
                *   The controller logs this via `CrisisDetectionService.logCrisisEvent()` (which might save a `CrisisEvent` to DB via Prisma).
                *   Backend responds with `status: 'crisis_detected'` and crisis details.
            *   **If No Crisis**:
                *   A new `OnboardingStep` record is created (with stepId, userAnswer, timeSpent).
                *   The `OnboardingSession` in the database is updated (via `prisma.onboardingSession.update()`): `steps` array is appended, `totalTimeSpent` updated, and `currentStep` is advanced based on `determineNextStep(stepId, userAnswer)`.
                *   The service then calls its internal `getNextQuestion(sessionId, allCurrentResponses)`.
                    *   If `determineNextStep` returns a new step ID: `getQuestionForStep` retrieves the next question. Backend responds with `status: 'continue'` and the next question data.
                    *   If `determineNextStep` returns `null` (signifying end of questions): The `completeOnboarding(sessionId)` flow is triggered (see section 2.3).

4.  **Response to Frontend & State Update**:
    *   **Crisis Detected**:
        *   `OnboardingScreen`'s `handleResponse` receives the crisis payload.
        *   Sets `state.crisisDetected = true`, `state.crisisData`, `state.isLoading = false`.
        *   UI displays `CrisisModal`.
    *   **Continue to Next Question**:
        *   `OnboardingScreen`'s `handleResponse` receives the next question payload.
        *   Updates `state.currentQuestion` (with `result.question.question`), `state.progress` (with `result.question.progress`), sets `state.isLoading = false`.
        *   UI displays the new question.
    *   **Onboarding Completed**: (See section 2.3)

### 2.3. Completing Onboarding (via Step-by-Step Flow)

1.  **Trigger (Backend)**:
    *   Occurs within the backend's `onboardingService.submitResponse()` when `determineNextStep()` returns `null`, indicating no more predefined questions.
    *   The `completeOnboarding(sessionId)` method in the backend service is called.

2.  **Backend Processing (`completeOnboarding` in backend service)**:
    *   Retrieves the full `OnboardingSession` and all collected `responses`.
    *   Calls `generateUserProfile(sessionId, userId, allResponses)`:
        *   This method calls `AIService.generateProfileFromOnboarding(allResponses, userId, sessionId)`.
        *   The `AIService` (Python backend) processes the responses and returns structured profile data, a recommended pathway, and personalization settings.
        *   The backend service maps these AI results to the `UserProfile` model structure and `FeatureAccessibility`.
    *   Determines a final `recommendedPathway` (preferring AI's, with a fallback).
    *   Generates `nextSteps` and `warnings` based on the final profile and pathway.
    *   Saves the generated `UserProfile` data to the `UserProfileDetailed` table (via `prisma.userProfileDetailed.upsert()`), linking it to the `userId`. The full profile object is typically stored in a JSON `profileData` field.
    *   Marks the `OnboardingSession` as complete in the database (sets `completedAt`, `currentStep = 'complete'`).
    *   The service returns the `ProfileGenerationResult` (profile, pathway, featureConfig, nextSteps, warnings).

3.  **API Response (from `POST /api/onboarding/respond`)**:
    *   The backend controller receives the `ProfileGenerationResult`.
    *   Responds to the frontend with `status: 'completed'` and the `ProfileGenerationResult` as data.
        ```json
        {
          "status": "completed",
          "data": {
            "profile": { /* UserProfile object */ },
            "recommendedPathway": "...",
            "featureConfiguration": { /* FeatureAccessibility object */ },
            "nextSteps": ["...", "..."],
            "warnings": ["..."]
          }
        }
        ```

4.  **Frontend State Update & Navigation**:
    *   `OnboardingScreen`'s `handleResponse` receives the completion payload.
    *   Sets `state.isComplete = true`, `state.isLoading = false`.
    *   Calls its internal `handleOnboardingComplete(completionData)`:
        *   Saves the received profile locally (e.g., using `authService.updateUserProfile` which might update context/AsyncStorage).
        *   Uses `expo-router` (`router.replace(...)`) to navigate the user to the appropriate next screen based on `completionData.recommendedPathway` (e.g., main dashboard, emergency screen).

## 3. Other Onboarding-Related Flows

### 3.1. Resuming an Incomplete Onboarding Session

1.  **Trigger (Frontend)**: User returns to the app and has an incomplete session, or explicitly chooses to resume.
2.  **API Call**: `POST /api/onboarding/resume` with `sessionId`.
    *   Frontend's `onboardingService.resumeOnboarding(sessionId)` handles this.
3.  **Backend Processing (`resumeOnboarding` controller & service)**:
    *   Backend service retrieves the session by `sessionId`.
    *   Calls `getNextQuestion(sessionId, allCurrentResponsesFromSession)` to determine the current state and next question.
4.  **Response**: Returns `{ status: 'success', data: { sessionId, question: nextQuestion } }`.
5.  **Frontend**: Updates `OnboardingScreen` state with the `sessionId` and `currentQuestion` to continue.

### 3.2. Skipping Onboarding (Emergency Bypass)

1.  **Trigger (Frontend)**: User clicks a "Skip" or "Emergency" button on the `OnboardingScreen`.
2.  **Frontend Logic (`handleSkipOnboarding` in `OnboardingScreen`)**:
    *   Shows an alert to confirm skipping.
    *   If confirmed, calls `onboardingService.skipOnboarding(reason)`.
3.  **API Call**: `POST /api/onboarding/skip` with an optional `reason`.
4.  **Backend Processing (`skipOnboarding` controller)**:
    *   Currently, the backend controller has mock logic that directly constructs a minimal profile and recommended pathway. **It does not save this minimal profile to the database via this flow.**
    *   It directly returns a success response with this locally constructed minimal profile data.
    *   *Note: For persistence, this backend endpoint would need to call a service method to create/upsert a minimal `UserProfileDetailed` record.*
5.  **Response**: Returns `{ status: 'success', data: { profile: minimalProfile, recommendedPathway: ..., nextSteps: ... } }`.
6.  **Frontend**: Calls `handleOnboardingComplete(result.data)` to process this minimal profile, save it locally, and navigate.

### 3.3. Getting Session-Specific Onboarding Status

1.  **API Call**: `GET /api/onboarding/session-status/:sessionId`.
2.  **Backend**: `getSessionStatus` controller retrieves the `OnboardingSession` from DB via Prisma (`prisma.onboardingSession.findUnique()`) and returns its details (current step, progress, etc.).
3.  **Frontend**: `onboardingService.getOnboardingStatus(sessionId)` can be used to fetch this.

### 3.4. Getting Overall User Onboarding Completion Status

1.  **API Call**: `GET /api/onboarding/status`.
2.  **Backend**: `getOnboardingStatus` controller checks the `UserProfileDetailed` table for the authenticated user (via `prisma.userProfileDetailed.findUnique()`).
    *   If a profile exists and its `completionStatus` is 'complete', it returns `onboardingCompleted: true` and the profile.
    *   Otherwise, it returns `onboardingCompleted: false`.
3.  **Frontend**: Used to determine if the user should be directed to onboarding or main app.

### 3.5. Legacy `POST /api/onboarding/submit`

1.  **Purpose**: Appears to be a legacy or alternative way to submit a full set of onboarding data (`personalInfo`, `preferences`) in one go, rather than step-by-step.
2.  **API Call**: `POST /api/onboarding/submit` with user's auth token and the data.
3.  **Backend Processing (`submitOnboarding` controller)**:
    *   Takes `personalInfo` and `preferences` from the request body.
    *   Constructs a data object to be stored in the `profileData` JSON field of the `UserProfileDetailed` model.
    *   Uses `prisma.userProfileDetailed.upsert()` to save this data.
4.  **Response**: Returns the created/updated profile data.
5.  **Frontend**: This flow might be used if a simplified, non-AI-driven onboarding form exists.

### 3.6. Updating Onboarding Preferences / Profile (Post-Onboarding)

*   **`PUT /api/onboarding/update` (Controller: `updateOnboarding`)**:
    *   Intended for updating specific preferences (e.g., `timeAvailability`, `learningStyle`).
    *   Fetches existing `UserProfileDetailed.profileData`, merges new preferences, and updates via `prisma.userProfileDetailed.update()`.
*   **`PUT /api/onboarding/profile` (Controller: `updateProfile`)**:
    *   A more general profile update mechanism.
    *   Takes `profileUpdates` object and merges it into the existing `UserProfileDetailed.profileData` JSON field.

## 4. Key Data Models Involved (Backend - Prisma)

*   **`OnboardingSession`**: Stores state for an active onboarding attempt (sessionId, userId, steps taken, currentStep, timestamps, etc.).
*   **`UserProfileDetailed`**: Stores the comprehensive user profile generated after onboarding (or via direct submission). Linked to the main `Profile` (auth user) by `userId`. Contains a `profileData` JSON field holding the detailed `UserProfile` structure.
*   **`CrisisEvent`**: Logged if crisis is detected during onboarding.

## 5. Error Handling

*   Both frontend and backend services/controllers implement try-catch blocks.
*   Backend uses `AppError` for standardized error responses.
*   Frontend services typically throw errors which are caught by UI components to display alerts.
*   `isLoading` state in the frontend UI is critical for preventing multiple submissions and providing user feedback.

This document provides a high-level overview. Specific implementation details for question logic, AI interaction, and error handling nuances reside within the respective service and controller files.
