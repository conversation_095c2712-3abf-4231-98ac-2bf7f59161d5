# Emergency (Sakina) Mode System Documentation

## Overview
The Emergency (Sakina) Mode System provides immediate spiritual and emotional support during moments of crisis. It offers a structured, research-backed intervention sequence that combines Islamic practices with modern stress-reduction techniques to help users rapidly transition from distress to a state of calm and tranquility.

## System Architecture

### Components
1. **Activation Layer**
   - One-tap entry from home screen or widget
   - Crisis detection based on user input
   - Urgent access prioritization

2. **Guidance Layer**
   - Guided breathing sequence
   - Synchronized dhikr overlay
   - <PERSON>u<PERSON>yah verses audio
   - Dua prompts

3. **Support Layer**
   - Emergency contact options
   - Helpline integration
   - Community support access
   - Post-session recommendations

4. **Tracking Layer**
   - Session recording
   - Effectiveness measurement
   - Trigger pattern analysis
   - Journal integration

### Data Flow
```
User Activation → Session Initialization → Guided Intervention → 
Effectiveness Feedback → Session Storage → Analysis → Recommendations
```

## Core Features

### 1. Session Management
```javascript
// Example of session initialization
const initializeEmergencySession = async (userId, triggerType) => {
  const session = {
    userId,
    triggerType,
    startTime: new Date(),
    status: 'active',
    interventions: []
  };
  
  return await storeSession(session);
};
```

### 2. Guided Breathing
- Dynamic pacing based on detected stress level
- Visual and audio guidance
- Haptic feedback synchronization
- Progressive relaxation sequencing

### 3. Dhikr Overlay
```javascript
// Example dhikr content structure
const dhikrSequence = {
  primary: {
    arabic: "سُبْحَانَ اللهِ",
    transliteration: "SubhanAllah",
    meaning: "Glory be to Allah",
    repetitions: 33
  },
  secondary: {
    arabic: "ٱلْحَمْدُ لِلَّٰهِ",
    transliteration: "Alhamdulillah",
    meaning: "All praise is due to Allah",
    repetitions: 33
  },
  tertiary: {
    arabic: "اللهُ أَكْبَرُ",
    transliteration: "Allahu Akbar",
    meaning: "Allah is the Greatest",
    repetitions: 34
  }
};
```

### 4. Ruqyah Recitation
- Authenticated Quranic verses
- Prioritized short surahs (Al-Falaq, An-Nas, Al-Ikhlas)
- Professional audio recitations
- Optional translation overlay

### 5. Emergency Support
- Integrated crisis helplines
- Trusted contacts access
- Community support connection
- Professional referral resources

## Data Models

### Emergency Sessions
```sql
create table public.emergency_sessions (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid references auth.users not null,
    trigger_type text not null,
    symptoms jsonb,
    start_time timestamp with time zone not null,
    end_time timestamp with time zone,
    status text not null,
    effectiveness_rating integer,
    feedback text,
    created_at timestamp with time zone default now() not null
);
```

### Emergency Content
```sql
create table public.emergency_content (
    id uuid primary key default uuid_generate_v4(),
    content_type text not null,
    title text not null,
    arabic_text text,
    translation text,
    audio_url text,
    display_order integer,
    duration_seconds integer,
    status text default 'active',
    created_at timestamp with time zone default now() not null
);
```

### User Session Interactions
```sql
create table public.emergency_session_interactions (
    id uuid primary key default uuid_generate_v4(),
    session_id uuid references public.emergency_sessions not null,
    interaction_type text not null,
    content_id uuid references public.emergency_content,
    start_time timestamp with time zone not null,
    duration_seconds integer,
    completed boolean,
    created_at timestamp with time zone default now() not null
);
```

## API Endpoints

### Session Management
```http
POST /api/emergency/start
Request Body:
{
  "triggerType": "anxiety",
  "currentSymptoms": ["racing_thoughts", "rapid_heartbeat"]
}

Response:
{
  "status": "success",
  "data": {
    "sessionId": "uuid",
    "breathingExercise": {...},
    "dhikrContent": {...},
    "ruqyahVerses": [...],
    "duaPrompt": {...},
    "helplineInfo": {...}
  }
}
```

### Guided Breathing
```http
GET /api/emergency/breathing
Query Parameters:
- intensity: "low" | "medium" | "high"

Response:
{
  "status": "success",
  "data": {
    "pattern": "4-7-8",
    "durationSeconds": 180,
    "visualGuide": "url_to_animation",
    "audioGuide": "url_to_audio"
  }
}
```

### Dhikr Content
```http
GET /api/emergency/dhikr
Response:
{
  "status": "success",
  "data": {
    "primary": {
      "arabic": "سُبْحَانَ اللهِ",
      "transliteration": "SubhanAllah",
      "meaning": "Glory be to Allah",
      "repetitions": 33
    },
    "secondary": {...},
    "tertiary": {...},
    "audio": "url_to_audio"
  }
}
```

### Ruqyah Verses
```http
GET /api/emergency/ruqyah
Response:
{
  "status": "success",
  "data": [
    {
      "surah": "Al-Falaq",
      "arabic": "قُلْ أَعُوذُ بِرَبِّ ٱلْفَلَقِ...",
      "translation": "Say: I seek refuge with the Lord of the Dawn...",
      "audioUrl": "url_to_audio"
    },
    {...}
  ]
}
```

### Session Update
```http
PATCH /api/emergency/sessions/:id
Request Body:
{
  "status": "completed",
  "effectivenessRating": 4,
  "feedback": "Helped calm my anxiety"
}

Response:
{
  "status": "success",
  "data": {
    "session": {...},
    "insights": {...},
    "followUpRecommendations": [...]
  }
}
```

### Save to Journal
```http
POST /api/emergency/sessions/:id/save
Request Body:
{
  "notes": "I felt much calmer after using Sakina Mode",
  "tags": ["anxiety", "breathing", "evening"]
}

Response:
{
  "status": "success",
  "message": "Session saved to journal successfully",
  "data": {
    "journalEntry": {...}
  }
}
```

## Security Implementation

### Access Control
- JWT-based authentication for all endpoints
- Emergency mode accessibility without full login
- Rate limiting to prevent abuse
- Session invalidation after completion

### Data Protection
```sql
-- Row-level security policy
create policy "Users can only view their own emergency sessions"
  on public.emergency_sessions
  for select
  using (auth.uid() = user_id);

create policy "Users can only update their own emergency sessions"
  on public.emergency_sessions
  for update
  using (auth.uid() = user_id);
```

### Privacy Considerations
- Emergency data segregated from regular app data
- Optional anonymization of crisis details
- Configurable data retention policies
- GDPR/HIPAA-compliant storage practices

## Performance Optimization

### Caching Strategy
```javascript
// Example caching implementation for emergency content
const getEmergencyContent = async (contentType) => {
  const cacheKey = `emergency:content:${contentType}`;
  
  // Try to get from cache first
  const cachedContent = await cache.get(cacheKey);
  if (cachedContent) {
    return JSON.parse(cachedContent);
  }
  
  // Fetch from database if not in cache
  const content = await fetchContentFromDb(contentType);
  
  // Store in cache with 1-hour expiry
  await cache.set(cacheKey, JSON.stringify(content), 3600);
  
  return content;
};
```

### Critical Path Optimization
1. **Preloading**: Essential assets cached on app startup
2. **Progressive Loading**: Non-critical content loaded asynchronously
3. **Offline Support**: Core emergency features available without internet
4. **Asset Optimization**: Compressed audio and optimized animations

### Response Time Requirements
- Initial session start: < 500ms
- Breathing guidance: < 100ms
- Content delivery: < 300ms
- Session updates: < 500ms

## Integration Points

### N8n Workflow Integration
```javascript
// Example of n8n workflow trigger for emergency analysis
const processEmergencySession = async (sessionId) => {
  return await triggerN8nWorkflow('analyze-emergency-session', {
    sessionId,
    timestamp: new Date()
  });
};
```

### Mobile App Integration
- Lock screen widget support
- Emergency mode gesture activation
- Haptic feedback integration
- Background audio capabilities

### External Services
1. **Crisis Helplines**: Country-specific emergency numbers
2. **Community Support**: Optional connection to community circles
3. **Analytics**: Anonymous usage reporting for research
4. **Content Delivery Network**: Optimized global distribution

## Error Handling

### Error Types
- `EMERGENCY_001`: Session initialization failed
- `EMERGENCY_002`: Content delivery failed
- `EMERGENCY_003`: Session update failed
- `EMERGENCY_004`: Offline mode errors

### Fallback Mechanism
```javascript
// Example of fallback mechanism for content delivery
const getEmergencyRuqyah = async (userId) => {
  try {
    // Try to get personalized content
    const personalized = await getPersonalizedRuqyah(userId);
    return personalized;
  } catch (error) {
    logger.warn(`Failed to get personalized ruqyah: ${error.message}`);
    
    // Fall back to default content
    return getDefaultRuqyah();
  }
};
```

### Graceful Degradation
- Default content when personalization fails
- Text alternatives when audio unavailable
- Simple breathing pattern when animations fail
- Cached emergency contacts when network unavailable

## Monitoring and Analytics

### Key Metrics
1. **Usage Patterns**: Frequency, duration, time of day
2. **Effectiveness**: Self-reported ratings, session completion
3. **Triggers**: Common reported causes
4. **Conversion**: Percentage of sessions leading to journal entries

### Alerting
- Unusual usage patterns
- High frequency of sessions
- Low effectiveness ratings
- Technical failures

## Best Practices

### Implementation Guidelines
1. **Accessibility First**: Support for vision/hearing impaired users
2. **Cultural Sensitivity**: Appropriate content for diverse backgrounds
3. **Psychological Safety**: Avoid potentially triggering content
4. **Research-Backed**: Interventions based on evidence

### User Experience
- Minimal user input required during distress
- Clear, simple instructions
- Calming visual design
- Progress indication without pressure

## Future Enhancements

### Planned Features
1. **AI-driven Personalization**: Learning from effectiveness patterns
2. **Biometric Integration**: Heart rate and breathing monitoring
3. **Voice Guidance**: Optional voice-guided meditation
4. **Peer Support**: Opt-in community alert to trusted friends
5. **Advanced Analytics**: Pattern recognition for preventative intervention

### Research Directions
- Effectiveness measurement methodology
- Cross-cultural intervention adaptation
- Long-term impact assessment
- Integration with clinical support structures

