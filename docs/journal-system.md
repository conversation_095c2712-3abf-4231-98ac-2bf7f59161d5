# Journal System Documentation

## Overview
The Journal System serves as a comprehensive platform for users to document their spiritual, emotional, and healing journey. It combines traditional journaling practices with Islamic mindfulness principles, enabling users to track their emotional states, identify patterns, and connect their daily experiences to the five layers of healing (Jism, Nafs, Aql, Qalb, and Ruh).

## System Architecture

### Components
1. **Entry Management Layer**
   - Private journal entries
   - Emotion and mood tracking
   - Layer-specific tagging
   - Media attachment support

2. **Reflection Layer**
   - Guided reflection prompts
   - Quranic and prophetic inspirations
   - Layer-specific prompts
   - Gratitude practices

3. **Analytics Layer**
   - Mood pattern recognition
   - Layer focus visualization
   - Streak and consistency tracking
   - Insight generation

4. **Integration Layer**
   - Emergency session recording
   - Journey milestone integration
   - Community sharing (optional)
   - Ruqya experience documentation

### Data Flow
```
User Input → Entry Processing → Metadata Extraction → 
Storage → Analysis → Pattern Recognition → Insights
```

## Core Features

### 1. Entry Management
```javascript
// Example journal entry structure
const journalEntry = {
  userId: 'user-uuid',
  title: 'Morning Reflection',
  content: 'Today I felt a deep connection during Fajr prayer...',
  entryType: 'reflection', // reflection, gratitude, milestone, emergency
  mood: 'peaceful',
  tags: ['prayer', 'morning', 'connection'],
  layers: ['Qalb', 'Ruh'],
  relatedData: {}, // For integrations with other systems
  entryDate: new Date()
};
```

### 2. Mood and Emotion Tracking
- Comprehensive emotion vocabulary
- Islamic perspective on emotions
- Contextual tracking (prayer times, activities)
- Visual representation through time

### 3. Guided Reflection Prompts
```javascript
// Example of personalized reflection prompts
const reflectionPrompts = [
  {
    text: "How did today's dhikr practice affect your heart?",
    category: "spiritual_practice",
    layer: "Qalb",
    tags: ["dhikr", "reflection", "heart"]
  },
  {
    text: "Reflect on a moment when you felt Allah's mercy today.",
    category: "gratitude",
    layer: "Ruh",
    tags: ["mercy", "gratitude", "awareness"]
  },
  {
    text: "What thoughts created distance between you and Allah today?",
    category: "improvement",
    layer: "Aql",
    tags: ["thoughts", "mindfulness", "awareness"]
  }
];
```

### 4. Analytics and Insights
- Mood correlation with practices
- Layer focus distribution
- Consistency and streak tracking
- Pattern identification

### 5. Integration with Other Systems
- Emergency (Sakina) mode session recording
- Journey milestone documentation
- Ruqya experience integration
- Community circle sharing (opt-in)

## Data Models

### Journal Entries
```sql
create table public.user_journal_entries (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid references auth.users not null,
    title text not null,
    content text not null,
    entry_type text not null,
    mood text,
    tags text[] default '{}',
    layers text[] default '{}',
    related_data jsonb default '{}',
    entry_date timestamp with time zone not null,
    updated_at timestamp with time zone,
    created_at timestamp with time zone default now() not null
);
```

### Reflection Prompts
```sql
create table public.reflection_prompts (
    id uuid primary key default uuid_generate_v4(),
    text text not null,
    category text not null,
    layer text,
    tags text[] default '{}',
    focus_area text[] default '{}',
    display_order integer,
    status text default 'active',
    created_at timestamp with time zone default now() not null
);
```

### Journal Analytics
```sql
create table public.journal_analytics (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid references auth.users not null,
    metric_name text not null,
    metric_value jsonb not null,
    calculation_date timestamp with time zone not null,
    period_start date,
    period_end date,
    created_at timestamp with time zone default now() not null
);
```

## API Endpoints

### Entry Management
```http
POST /api/journal/entries
Request Body:
{
  "title": "Evening Reflection",
  "content": "Today I felt a strong connection during dhikr...",
  "entryType": "reflection",
  "mood": "peaceful",
  "tags": ["dhikr", "evening", "connection"],
  "layers": ["Qalb"]
}

Response:
{
  "status": "success",
  "data": {
    "entry": {
      "id": "uuid",
      "title": "Evening Reflection",
      "content": "Today I felt a strong connection during dhikr...",
      "entryType": "reflection",
      "mood": "peaceful",
      "tags": ["dhikr", "evening", "connection"],
      "layers": ["Qalb"],
      "entryDate": "2025-05-26T13:45:26.563Z"
    }
  }
}
```

### Retrieving Entries
```http
GET /api/journal/entries
Query Parameters:
- entryType: "reflection" | "gratitude" | "milestone" | "emergency"
- mood: string
- tags: string[]
- layers: string[]
- startDate: ISO date string
- endDate: ISO date string
- page: number
- limit: number
- sortBy: "date" | "mood" | "type"
- sortDirection: "asc" | "desc"

Response:
{
  "status": "success",
  "data": {
    "entries": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 45,
      "pages": 5
    }
  }
}
```

### Getting Reflection Prompts
```http
GET /api/journal/prompts
Query Parameters:
- category: string
- layer: string

Response:
{
  "status": "success",
  "data": {
    "prompts": [
      {
        "id": "uuid",
        "text": "How did today's dhikr practice affect your heart?",
        "category": "spiritual_practice",
        "layer": "Qalb",
        "tags": ["dhikr", "reflection", "heart"]
      },
      ...
    ]
  }
}
```

### Saving Prompted Reflection
```http
POST /api/journal/reflections
Request Body:
{
  "promptId": "uuid",
  "content": "During dhikr today, I felt a sense of peace...",
  "mood": "peaceful",
  "layers": ["Qalb", "Ruh"]
}

Response:
{
  "status": "success",
  "data": {
    "entry": {...},
    "message": "Reflection saved successfully"
  }
}
```

### Getting Journal Analytics
```http
GET /api/journal/analytics
Query Parameters:
- timeframe: "7days" | "30days" | "90days" | "all"

Response:
{
  "status": "success",
  "data": {
    "entryCount": 45,
    "moodFrequency": {
      "peaceful": 15,
      "grateful": 10,
      "reflective": 8,
      "challenged": 7,
      "inspired": 5
    },
    "layerFrequency": {
      "Qalb": 25,
      "Ruh": 18,
      "Nafs": 12,
      "Aql": 10,
      "Jism": 5
    },
    "tagFrequency": {...},
    "entryTypeFrequency": {...},
    "timeDistribution": {...},
    "streaks": {
      "current": 12,
      "longest": 30
    }
  }
}
```

## Security Implementation

### Access Control
- JWT-based authentication for all endpoints
- Row-level security policies
- Encrypted content storage
- Privacy controls for shared content

### Data Protection
```sql
-- Row-level security policy
create policy "Users can only view their own journal entries"
  on public.user_journal_entries
  for select
  using (auth.uid() = user_id);

create policy "Users can only update their own journal entries"
  on public.user_journal_entries
  for update
  using (auth.uid() = user_id);

create policy "Users can only delete their own journal entries"
  on public.user_journal_entries
  for delete
  using (auth.uid() = user_id);
```

### Privacy Considerations
- Optional end-to-end encryption for sensitive entries
- Selective sharing controls
- Data export functionality
- Right to be forgotten implementation

## Performance Optimization

### Indexing Strategy
```sql
-- Optimize query performance with indexes
create index idx_journal_entries_user_id_date 
  on public.user_journal_entries(user_id, entry_date);

create index idx_journal_entries_user_id_type
  on public.user_journal_entries(user_id, entry_type);
  
create index idx_journal_entries_user_id_mood
  on public.user_journal_entries(user_id, mood);
```

### Caching Strategy
```javascript
// Example caching implementation for reflection prompts
const getReflectionPrompts = async (userId, category, layer) => {
  const cacheKey = `prompts:${category || 'all'}:${layer || 'all'}`;
  
  // Try to get from cache first
  const cachedPrompts = await cache.get(cacheKey);
  if (cachedPrompts) {
    return JSON.parse(cachedPrompts);
  }
  
  // Fetch from database if not in cache
  const prompts = await fetchPromptsFromDb(category, layer);
  
  // Personalize prompts based on user data
  const personalizedPrompts = await personalizePrompts(prompts, userId);
  
  // Store in cache with 24-hour expiry
  await cache.set(cacheKey, JSON.stringify(personalizedPrompts), 86400);
  
  return personalizedPrompts;
};
```

### Pagination and Query Optimization
- Limit-offset pagination for large journal collections
- Cursor-based pagination for analytics data
- Optimized queries for frequent operations
- Materialized views for complex analytics

## Integration Points

### Emergency System Integration
```javascript
// Example of emergency session journal integration
const saveEmergencySessionToJournal = async (sessionId, userId, notes) => {
  // Get emergency session details
  const session = await getEmergencySession(sessionId);
  
  // Create journal entry from session
  return await createJournalEntry({
    userId,
    title: 'Sakina Mode Session',
    content: notes || 'Emergency session completed',
    entryType: 'emergency_session',
    mood: 'reflective',
    tags: ['emergency', 'sakina'],
    layers: [],
    relatedData: {
      sessionId,
      sessionDetails: session
    }
  });
};
```

### Journey System Integration
```javascript
// Example of journey milestone integration
const recordJourneyMilestone = async (journeyId, userId, milestone) => {
  // Get milestone details
  const milestoneDetails = await getJourneyMilestone(journeyId, milestone.id);
  
  // Create journal entry for milestone
  return await createJournalEntry({
    userId,
    title: `Journey Milestone: ${milestone.title}`,
    content: milestone.reflection || 'Milestone completed',
    entryType: 'journey_milestone',
    mood: milestone.mood,
    tags: ['journey', 'milestone', ...milestone.tags],
    layers: milestone.layers,
    relatedData: {
      journeyId,
      milestoneId: milestone.id,
      milestoneDetails
    }
  });
};
```

### Community System Integration
- Optional sharing of journal entries with Heart Circles
- Anonymized sharing options
- Control over shared content visibility
- Reflection prompt collaboration

## Error Handling

### Error Types
- `JOURNAL_001`: Entry creation failed
- `JOURNAL_002`: Entry retrieval failed
- `JOURNAL_003`: Analytics calculation failed
- `JOURNAL_004`: Integration error

### Graceful Degradation
```javascript
// Example of fallback mechanism for analytics
const getJournalAnalytics = async (userId, timeframe) => {
  try {
    // Try to get complete analytics
    const fullAnalytics = await calculateCompleteAnalytics(userId, timeframe);
    return fullAnalytics;
  } catch (error) {
    logger.warn(`Failed to calculate complete analytics: ${error.message}`);
    
    // Fall back to basic analytics
    return await calculateBasicAnalytics(userId, timeframe);
  }
};
```

### Error Response Format
```javascript
{
  "status": "error",
  "code": "JOURNAL_001",
  "message": "Failed to create journal entry",
  "details": {
    "field": "content",
    "issue": "Content exceeds maximum length"
  }
}
```

## Monitoring and Analytics

### Key Metrics
1. **Usage Patterns**: Entry frequency, types, time of day
2. **Content Analysis**: Common themes, language patterns
3. **Emotional Trends**: Mood progression over time
4. **Layer Focus**: Distribution of attention across healing layers

### Insight Generation
- Mood correlations with practices
- Pattern identification in spiritual struggles
- Progress visualization through layers
- Consistency and engagement metrics

## Best Practices

### Implementation Guidelines
1. **Privacy First**: Secure handling of sensitive personal content
2. **Intuitive UX**: Simple, distraction-free writing interface
3. **Meaningful Prompts**: Evidence-based reflection questions
4. **Consistent Structure**: Standardized tagging and categorization

### Content Guidelines
- Culturally sensitive reflection prompts
- Trauma-informed language
- Islamic scholarly validation of prompts
- Inclusive language across diverse backgrounds

## Future Enhancements

### Planned Features
1. **AI-assisted Reflection**: Smart prompt suggestions based on user patterns
2. **Voice Journaling**: Audio entry recording and transcription
3. **Mood Visualization**: Advanced emotional pattern visualization
4. **Cross-system Insights**: Correlations between journal entries and healing progress
5. **Collaborative Reflections**: Guided group reflections in Heart Circles

### Research Directions
- Impact of journaling on spiritual well-being
- Correlation between journaling frequency and healing outcomes
- Effectiveness of different prompt types across layers
- Cultural adaptation of reflection practices

