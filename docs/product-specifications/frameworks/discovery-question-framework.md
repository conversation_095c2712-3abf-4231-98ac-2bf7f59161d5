# Discovery Question Framework
## <PERSON>alb Healing Islamic Wellness Platform

---

## Purpose
This framework provides structured questions for initial discovery sessions with stakeholders and users to gather comprehensive requirements for product specifications. The questions are tailored for the Islamic wellness context and the 5-layer soul model.

---

## Question Categories

### 1. Problem Understanding Questions

#### 1.1 Core Problem Identification
- What specific problem are we trying to solve with this feature?
- How does this problem manifest in the daily lives of our Muslim users?
- Which layer(s) of the soul (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>) does this problem primarily affect?
- What are the root causes of this problem from an Islamic perspective?
- How does this problem differ across different Muslim communities or cultures?

#### 1.2 Current State Analysis
- How do users currently address this problem without our platform?
- What existing Islamic resources or practices do they use?
- What are the limitations of current solutions?
- What workarounds have users developed?
- How much time/effort do users currently spend on this problem?

#### 1.3 Impact Assessment
- What happens if this problem remains unsolved?
- How does this problem affect the user's spiritual journey?
- What is the emotional/psychological impact on users?
- How does this problem affect their relationship with <PERSON> (SWT)?
- What are the broader community implications?

### 2. User and Stakeholder Questions

#### 2.1 User Persona Deep Dive
- Who is most affected by this problem?
- What are their demographics (age, location, Islamic practice level)?
- What is their level of Islamic knowledge?
- How comfortable are they with technology?
- What are their daily Islamic practices?
- What are their mental health awareness levels?
- What cultural considerations are important for this user group?

#### 2.2 User Journey and Context
- When do users typically encounter this problem?
- What triggers this problem for users?
- What is the user's emotional state when facing this problem?
- What other activities are they doing when this problem occurs?
- Who else is involved in their support system?
- What Islamic resources do they typically turn to first?

#### 2.3 User Goals and Motivations
- What do users ultimately want to achieve?
- How does solving this problem align with their Islamic goals?
- What would success look like from their perspective?
- What are their fears or concerns about addressing this problem?
- What motivates them to seek help?
- How important is privacy/confidentiality for this issue?

### 3. Islamic Context Questions

#### 3.1 Religious Framework
- How does this feature align with Islamic principles and teachings?
- What Quranic verses or Hadith are relevant to this problem?
- How do different Islamic schools of thought view this issue?
- What are the Islamic guidelines for addressing this type of problem?
- How can we ensure the solution respects Islamic values?

#### 3.2 Spiritual Healing Approach
- Which spiritual practices are most effective for this problem?
- How can we incorporate Quranic healing into the solution?
- What role should Duas (supplications) play?
- How can we integrate the Names of Allah (Asma ul Husna)?
- What is the role of Ruqyah in addressing this issue?
- How can we incorporate Islamic mindfulness and meditation?

#### 3.3 Cultural Sensitivity
- How does this problem vary across different Muslim cultures?
- What cultural taboos or sensitivities should we be aware of?
- How do family dynamics affect this issue in different cultures?
- What language considerations are important?
- How do gender considerations affect the approach?

### 4. Solution Exploration Questions

#### 4.1 Desired Outcomes
- What would the ideal solution look like?
- How would users know the solution is working?
- What changes would users expect to see in their lives?
- How quickly would users expect to see results?
- What would make users feel confident in the solution?

#### 4.2 Feature Requirements
- What specific functionality would be most helpful?
- How should users interact with this feature?
- What information would users need to provide?
- What feedback would users want to receive?
- How often would users want to engage with this feature?
- What customization options are important?

#### 4.3 Integration Needs
- How should this feature connect with other platform features?
- What external resources or services should be integrated?
- How should this feature work with users' existing Islamic practices?
- What data should be shared between features?
- How should this feature support community aspects?

### 5. Technical and Practical Questions

#### 5.1 Technical Requirements
- What are the performance expectations?
- What devices/platforms must be supported?
- What accessibility requirements are needed?
- What are the security and privacy requirements?
- What offline capabilities are needed?
- What integration points are required?

#### 5.2 Content Requirements
- What Islamic content is needed?
- What languages must be supported?
- What scholarly validation is required?
- What multimedia content is needed (audio, video, images)?
- How should content be personalized?
- What content update frequency is needed?

#### 5.3 Operational Requirements
- What support resources will users need?
- What training or onboarding is required?
- How should user feedback be collected?
- What monitoring and analytics are needed?
- What maintenance requirements exist?

### 6. Success and Validation Questions

#### 6.1 Success Metrics
- How will we measure success for this feature?
- What metrics are most important to users?
- What metrics are most important to the business?
- How will we measure Islamic spiritual progress?
- What leading indicators should we track?
- What lagging indicators are important?

#### 6.2 Validation Approach
- How can we test this solution with users?
- What would convince users to adopt this feature?
- How can we validate Islamic content accuracy?
- What feedback mechanisms are needed?
- How can we measure user satisfaction?
- What would indicate the feature is not working?

### 7. Constraints and Risks Questions

#### 7.1 Constraints
- What budget constraints exist?
- What timeline constraints are there?
- What technical constraints must be considered?
- What resource constraints exist?
- What regulatory constraints apply?
- What Islamic guidelines constrain the solution?

#### 7.2 Risk Assessment
- What could go wrong with this feature?
- What are the biggest risks to user adoption?
- What are the technical risks?
- What are the Islamic content risks?
- What are the cultural sensitivity risks?
- How can we mitigate these risks?

---

## Question Customization by Feature Type

### Assessment Features
**Additional Focus Areas:**
- Diagnostic accuracy and Islamic framework alignment
- Privacy and confidentiality concerns
- Integration with Islamic healing approaches
- Cultural variations in symptom expression

**Key Questions:**
- How do Islamic concepts of mental health differ from Western approaches?
- What role should family/community play in the assessment?
- How can we ensure assessments respect Islamic values?

### Content Features (Quranic verses, Duas)
**Additional Focus Areas:**
- Scholarly authenticity and verification
- Translation accuracy and cultural appropriateness
- Personalization and relevance
- Audio/visual presentation requirements

**Key Questions:**
- What sources are considered authentic and trustworthy?
- How should content be organized and categorized?
- What personalization factors are most important?

### Crisis/Emergency Features
**Additional Focus Areas:**
- Immediate response requirements
- Safety and risk assessment
- Professional intervention protocols
- Islamic crisis support approaches

**Key Questions:**
- What constitutes a crisis in the Islamic context?
- How quickly must the system respond?
- What professional resources should be integrated?

### Community Features
**Additional Focus Areas:**
- Community building and moderation
- Privacy and anonymity balance
- Islamic community guidelines
- Cross-cultural community dynamics

**Key Questions:**
- How can we foster authentic Islamic community?
- What moderation guidelines align with Islamic principles?
- How can we ensure safe and supportive interactions?

---

## Discovery Session Structure

### 1. Opening (10 minutes)
- Introductions and context setting
- Explanation of Islamic wellness platform vision
- Overview of discovery session goals
- Consent for recording/notes

### 2. Problem Exploration (20 minutes)
- Core problem identification questions
- Current state analysis
- Impact assessment

### 3. User Context Deep Dive (20 minutes)
- User persona questions
- User journey exploration
- Islamic context questions

### 4. Solution Exploration (15 minutes)
- Desired outcomes discussion
- Feature requirements exploration
- Integration needs

### 5. Technical and Practical (10 minutes)
- Technical requirements
- Content requirements
- Operational needs

### 6. Success and Validation (10 minutes)
- Success metrics discussion
- Validation approach
- Constraints and risks

### 7. Closing (5 minutes)
- Summary of key insights
- Next steps
- Follow-up questions

---

## Documentation Template

### Discovery Session Notes
**Date**: [Date]
**Participants**: [List of participants and roles]
**Feature/Topic**: [What was discussed]
**Session Type**: [Stakeholder/User/Expert interview]

#### Key Insights
1. **Problem Understanding**
   - [Key insights about the problem]

2. **User Needs**
   - [Key insights about user needs]

3. **Islamic Context**
   - [Key insights about Islamic requirements]

4. **Solution Requirements**
   - [Key insights about solution needs]

5. **Success Criteria**
   - [Key insights about success metrics]

#### Action Items
- [ ] [Action item 1]
- [ ] [Action item 2]

#### Follow-up Questions
- [Questions that need further exploration]

#### Quotes
- "[Significant quotes from participants]"

---

## Best Practices

### Before the Session
1. Review existing documentation about the feature/problem
2. Customize questions based on participant expertise
3. Prepare Islamic context background if needed
4. Set up recording/note-taking tools
5. Share agenda with participants

### During the Session
1. Start with open-ended questions
2. Listen actively and ask follow-up questions
3. Respect Islamic sensitivities and cultural context
4. Take detailed notes on both explicit and implicit needs
5. Clarify technical terms and Islamic concepts

### After the Session
1. Document insights within 24 hours
2. Share notes with participants for validation
3. Identify patterns across multiple sessions
4. Update questions based on learnings
5. Plan follow-up sessions if needed

---

## Review and Updates

This question framework should be updated:
- After each major discovery session
- When new feature types are introduced
- Based on feedback from stakeholders
- When Islamic guidance is updated

**Last Updated**: [Date]
**Next Review**: [Date]