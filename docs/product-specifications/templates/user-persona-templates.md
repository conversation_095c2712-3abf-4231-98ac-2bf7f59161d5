# User Persona Templates
## <PERSON>alb Healing Islamic Wellness Platform

---

## Purpose
These templates provide structured formats for creating detailed user personas specific to the Islamic wellness context, incorporating the 5-layer soul model and cultural considerations.

---

## Primary Persona Template

### Persona Name: [Give the persona a name]

#### Basic Demographics
- **Age**: [Age range]
- **Location**: [Geographic location/region]
- **Gender**: [Gender identity]
- **Occupation**: [Job/profession]
- **Education Level**: [Educational background]
- **Family Status**: [Single, married, children, etc.]
- **Income Level**: [Income bracket]

#### Islamic Practice Profile
- **Practice Level**: [Practicing, Learning, Cultural, New Muslim]
- **Islamic Knowledge**: [Beginner, Intermediate, Advanced, Scholar]
- **Daily Prayers**: [5 times, sometimes, rarely, learning]
- **Quran Engagement**: [Daily reading, weekly, occasional, learning Arabic]
- **Community Involvement**: [Active in mosque, occasional attendance, private practice]
- **Islamic Education**: [Formal Islamic education, self-taught, family tradition]
- **Preferred Islamic Resources**: [Books, online content, scholars, community]

#### Mental Health & Wellness Context
- **Mental Health Awareness**: [High, Medium, Low]
- **Previous Help-Seeking**: [Professional therapy, Islamic counseling, family/friends, none]
- **Stigma Concerns**: [High concern about mental health stigma, moderate, low]
- **Wellness Practices**: [Exercise, meditation, Islamic practices, none]
- **Support System**: [Strong family support, community support, limited support]
- **Crisis Experience**: [Has experienced crisis, witnessed in others, no direct experience]

#### Technology Profile
- **Device Usage**: [Smartphone primary, tablet, computer, multiple devices]
- **Tech Comfort Level**: [High, Medium, Low]
- **App Usage Patterns**: [Heavy user, moderate, minimal]
- **Preferred Platforms**: [iOS, Android, web]
- **Digital Privacy Concerns**: [High, Medium, Low]
- **Online Islamic Content**: [Regularly consumes, occasionally, prefers offline]

#### Cultural Context
- **Cultural Background**: [Arab, South Asian, Southeast Asian, African, Western Convert, etc.]
- **Language Preferences**: [Primary language, Arabic proficiency, translation needs]
- **Cultural Values**: [Traditional, modern, balanced approach]
- **Family Dynamics**: [Extended family influence, nuclear family, individual autonomy]
- **Community Expectations**: [High community involvement expected, moderate, individual choice]

#### Goals and Motivations
**Primary Goals:**
1. [Goal 1 - related to Islamic spiritual growth]
2. [Goal 2 - related to mental wellness]
3. [Goal 3 - related to daily life improvement]

**Islamic Spiritual Goals:**
- [Specific spiritual objectives]
- [Relationship with Allah (SWT) goals]
- [Islamic knowledge goals]

**Mental Wellness Goals:**
- [Emotional well-being objectives]
- [Stress management goals]
- [Relationship improvement goals]

**Life Goals:**
- [Career/education goals]
- [Family goals]
- [Community contribution goals]

#### Pain Points and Challenges
**Islamic Context Challenges:**
- [Challenges related to Islamic practice]
- [Balancing religious and modern life]
- [Finding authentic Islamic guidance]

**Mental Health Challenges:**
- [Specific mental health concerns]
- [Barriers to seeking help]
- [Stigma-related challenges]

**Technology Challenges:**
- [Digital literacy barriers]
- [Privacy concerns]
- [Information overload]

**Cultural Challenges:**
- [Cultural adaptation issues]
- [Language barriers]
- [Community pressure]

#### Preferred Solutions and Features
**Islamic Content Preferences:**
- [Quranic verses, Hadith, scholarly guidance]
- [Audio recitations, written text, video content]
- [Personalized vs. general content]

**Interaction Preferences:**
- [Self-guided vs. community support]
- [Anonymous vs. identified participation]
- [Structured programs vs. flexible access]

**Communication Style:**
- [Direct vs. gentle approach]
- [Detailed explanations vs. simple guidance]
- [Formal vs. conversational tone]

#### User Journey Touchpoints
**Discovery Phase:**
- How they typically learn about new resources
- What influences their decision to try something new
- Who they consult before making decisions

**Onboarding Phase:**
- What information they're comfortable sharing
- How much guidance they need to get started
- What would make them feel confident in the platform

**Regular Usage Phase:**
- How often they would engage with the platform
- What would keep them coming back
- How they prefer to track progress

**Crisis/Emergency Phase:**
- How they typically handle crisis situations
- Who they turn to for immediate help
- What immediate support they would need

#### Success Metrics for This Persona
**Engagement Metrics:**
- [How to measure their engagement]
- [Frequency of use indicators]
- [Feature adoption metrics]

**Outcome Metrics:**
- [How to measure their progress]
- [Islamic spiritual growth indicators]
- [Mental wellness improvement signs]

**Satisfaction Metrics:**
- [How to measure their satisfaction]
- [Feedback collection methods]
- [Retention indicators]

#### Quotes and Insights
**Direct Quotes:**
- "[Quote about their main challenge]"
- "[Quote about their goals]"
- "[Quote about their preferences]"

**Key Insights:**
- [Important insight about their behavior]
- [Critical need or concern]
- [Unique characteristic of this persona]

---

## Secondary Persona Template (Simplified)

### Persona Name: [Name]

#### Quick Profile
- **Demographics**: [Age, location, occupation]
- **Islamic Practice**: [Practice level and engagement]
- **Tech Comfort**: [Technology usage level]
- **Primary Need**: [Main problem they're trying to solve]

#### Key Characteristics
- [3-5 key characteristics that differentiate this persona]

#### Primary Goals
1. [Goal 1]
2. [Goal 2]
3. [Goal 3]

#### Main Pain Points
- [Pain point 1]
- [Pain point 2]
- [Pain point 3]

#### Preferred Features
- [Feature preference 1]
- [Feature preference 2]
- [Feature preference 3]

---

## Persona Validation Checklist

### Research Validation
- [ ] Based on actual user research data
- [ ] Validated through user interviews
- [ ] Confirmed through surveys or analytics
- [ ] Reviewed by Islamic content experts
- [ ] Validated by cultural consultants

### Completeness Check
- [ ] All demographic information included
- [ ] Islamic practice context thoroughly covered
- [ ] Mental health context addressed
- [ ] Technology profile complete
- [ ] Cultural considerations included
- [ ] Goals and motivations clearly defined
- [ ] Pain points comprehensively listed
- [ ] Preferred solutions identified

### Islamic Authenticity
- [ ] Islamic practice levels accurately represented
- [ ] Cultural sensitivities respected
- [ ] Religious diversity within Islam acknowledged
- [ ] Scholarly input incorporated where relevant

### Actionability
- [ ] Specific enough to guide design decisions
- [ ] Clear implications for feature development
- [ ] Measurable success criteria defined
- [ ] Realistic and achievable goals

---

## Persona Usage Guidelines

### When Creating Product Specifications
1. **Reference Primary Persona**: Always reference the primary persona for the feature
2. **Consider Secondary Personas**: Evaluate how the feature affects secondary personas
3. **Cultural Adaptation**: Consider how features need to adapt for different cultural contexts
4. **Islamic Sensitivity**: Ensure all features respect the Islamic values of target personas

### When Designing Features
1. **User Journey Mapping**: Use persona touchpoints to map user journeys
2. **Feature Prioritization**: Prioritize features based on persona goals and pain points
3. **Content Strategy**: Align content with persona Islamic knowledge levels and preferences
4. **Interaction Design**: Design interactions that match persona comfort levels

### When Testing and Validation
1. **Recruit Representative Users**: Use personas to recruit appropriate test participants
2. **Scenario Development**: Create test scenarios based on persona contexts
3. **Success Criteria**: Use persona success metrics to evaluate feature performance
4. **Feedback Interpretation**: Interpret feedback through the lens of persona characteristics

---

## Example Personas for Qalb Healing

### Primary Persona: Aisha - The Seeking Professional
- **Age**: 28-35
- **Location**: Urban area (US/UK/Canada)
- **Islamic Practice**: Practicing, seeking deeper connection
- **Tech Comfort**: High
- **Primary Need**: Balancing career stress with Islamic spiritual growth
- **Key Challenge**: Finding authentic Islamic mental health resources that understand her modern lifestyle

### Secondary Persona: Omar - The Traditional Learner
- **Age**: 45-55
- **Location**: Suburban/Rural area
- **Islamic Practice**: Traditional, community-focused
- **Tech Comfort**: Medium
- **Primary Need**: Supporting family members with mental health challenges
- **Key Challenge**: Understanding mental health through Islamic lens while respecting traditional approaches

### Secondary Persona: Fatima - The New Muslim
- **Age**: 22-30
- **Location**: Various
- **Islamic Practice**: Learning, enthusiastic
- **Tech Comfort**: High
- **Primary Need**: Learning Islamic approaches to wellness while dealing with life transitions
- **Key Challenge**: Finding beginner-friendly Islamic content that addresses modern mental health concerns

---

## Review and Updates

Personas should be reviewed and updated:
- **Quarterly**: Based on new user research data
- **After Major Features**: When significant new features are launched
- **Cultural Feedback**: When cultural consultants provide new insights
- **User Feedback**: When user feedback indicates persona gaps

**Last Updated**: [Date]
**Next Review**: [Date]