# Acceptance Criteria Template
## Qalb Healing Islamic Wellness Platform

---

## Purpose
This template provides a standardized approach for writing comprehensive acceptance criteria using the Given-When-Then format, specifically tailored for Islamic wellness features and cultural considerations.

---

## Given-When-Then Format

### Basic Structure
**Given** [initial condition or context]  
**When** [action is performed or event occurs]  
**Then** [expected outcome or result]

### Enhanced Structure for Islamic Context
**Given** [initial condition including Islamic context]  
**When** [action that respects Islamic principles]  
**Then** [outcome that provides Islamic value]  
**And** [additional Islamic or cultural consideration]

---

## Acceptance Criteria Template

### Feature: [Feature Name]
### User Story: [User Story ID and Title]

#### Scenario 1: [Primary Happy Path]
**Given** [user context and initial state]  
**When** [primary user action]  
**Then** [expected successful outcome]  
**And** [additional success criteria]

#### Scenario 2: [Alternative Path]
**Given** [alternative user context]  
**When** [alternative user action]  
**Then** [expected alternative outcome]  
**And** [additional considerations]

#### Scenario 3: [Error/Edge Case]
**Given** [error condition or edge case]  
**When** [action that triggers error]  
**Then** [expected error handling]  
**And** [user guidance provided]

#### Scenario 4: [Islamic Content Validation]
**Given** [Islamic content is displayed]  
**When** [user interacts with content]  
**Then** [content meets Islamic authenticity standards]  
**And** [cultural sensitivity is maintained]

#### Scenario 5: [Accessibility]
**Given** [user with accessibility needs]  
**When** [user attempts to use feature]  
**Then** [feature is accessible and usable]  
**And** [accessibility standards are met]

---

## Acceptance Criteria Categories

### 1. Functional Criteria

#### Core Functionality
- **Given** [basic setup condition]
- **When** [core action is performed]
- **Then** [primary function works as expected]

#### Input Validation
- **Given** [user provides input]
- **When** [input is submitted]
- **Then** [input is validated appropriately]
- **And** [feedback is provided for invalid input]

#### Data Processing
- **Given** [data is provided to the system]
- **When** [processing is triggered]
- **Then** [data is processed correctly]
- **And** [results are accurate and timely]

#### Integration Points
- **Given** [system needs to integrate with external service]
- **When** [integration is triggered]
- **Then** [integration works seamlessly]
- **And** [error handling is appropriate]

### 2. Islamic Content Criteria

#### Content Authenticity
- **Given** [Islamic content is displayed]
- **When** [user views the content]
- **Then** [content is from verified Islamic sources]
- **And** [scholarly validation is evident]

#### Arabic Text Handling
- **Given** [Arabic text is displayed]
- **When** [user views Arabic content]
- **Then** [text is displayed correctly (right-to-left)]
- **And** [transliteration and translation are provided]

#### Cultural Sensitivity
- **Given** [content addresses cultural topics]
- **When** [user from different cultural background views content]
- **Then** [content is respectful and inclusive]
- **And** [cultural variations are acknowledged]

#### Scholarly References
- **Given** [Islamic guidance is provided]
- **When** [user seeks source information]
- **Then** [proper citations and references are available]
- **And** [scholarly credentials are transparent]

### 3. User Experience Criteria

#### Usability
- **Given** [user is new to the feature]
- **When** [user attempts to use the feature]
- **Then** [feature is intuitive and easy to use]
- **And** [help is available when needed]

#### Personalization
- **Given** [user has provided personal preferences]
- **When** [feature is used]
- **Then** [experience is personalized appropriately]
- **And** [personalization respects Islamic values]

#### Feedback and Guidance
- **Given** [user performs an action]
- **When** [action is completed]
- **Then** [appropriate feedback is provided]
- **And** [next steps are clear]

#### Error Handling
- **Given** [an error occurs]
- **When** [user encounters the error]
- **Then** [error message is clear and helpful]
- **And** [recovery path is provided]

### 4. Technical Criteria

#### Performance
- **Given** [feature is accessed]
- **When** [user performs actions]
- **Then** [response times meet requirements]
- **And** [system remains responsive]

#### Security
- **Given** [user data is involved]
- **When** [data is processed or stored]
- **Then** [security measures are in place]
- **And** [privacy is protected]

#### Offline Capability
- **Given** [user has no internet connection]
- **When** [user attempts to use feature]
- **Then** [offline functionality works as designed]
- **And** [sync occurs when connection is restored]

#### Cross-Platform Compatibility
- **Given** [feature is accessed on different platforms]
- **When** [user uses the feature]
- **Then** [functionality is consistent across platforms]
- **And** [platform-specific optimizations are applied]

### 5. Accessibility Criteria

#### Screen Reader Support
- **Given** [user relies on screen reader]
- **When** [user navigates the feature]
- **Then** [all content is accessible via screen reader]
- **And** [navigation is logical and clear]

#### Visual Accessibility
- **Given** [user has visual impairments]
- **When** [user views the interface]
- **Then** [text contrast meets accessibility standards]
- **And** [font sizes are adjustable]

#### Motor Accessibility
- **Given** [user has motor impairments]
- **When** [user interacts with the interface]
- **Then** [all functions are accessible via keyboard]
- **And** [touch targets are appropriately sized]

#### Cognitive Accessibility
- **Given** [user has cognitive differences]
- **When** [user uses the feature]
- **Then** [interface is clear and not overwhelming]
- **And** [complex processes are broken into simple steps]

---

## Detailed Examples

### Example 1: Islamic Assessment Feature

#### Feature: Initial Islamic Mental Health Assessment
#### User Story: US-001 - Complete Islamic-based mental health assessment

##### Scenario 1: Successful Assessment Completion
**Given** I am a registered user who has completed onboarding  
**When** I start the Islamic mental health assessment  
**Then** I should see a welcome screen explaining the Islamic approach  
**And** I should see questions that use Islamic terminology appropriately  
**And** I should be able to progress through all assessment sections  
**And** I should receive results mapped to the 5-layer soul model

##### Scenario 2: Assessment with Islamic Context Preferences
**Given** I have indicated my Islamic knowledge level as "beginner"  
**When** I take the assessment  
**Then** Islamic terms should be explained in simple language  
**And** I should see transliterations for Arabic terms  
**And** I should have access to explanatory tooltips

##### Scenario 3: Assessment Interruption and Resume
**Given** I have started the assessment but not completed it  
**When** I close the app and return later  
**Then** I should be able to resume from where I left off  
**And** my previous answers should be saved  
**And** I should see my progress indicator

##### Scenario 4: Cultural Sensitivity in Questions
**Given** I have indicated my cultural background  
**When** I view assessment questions  
**Then** questions should be culturally appropriate  
**And** examples should be relevant to my cultural context  
**And** no questions should conflict with Islamic values

##### Scenario 5: Accessibility for Screen Readers
**Given** I am using a screen reader  
**When** I navigate the assessment  
**Then** all questions should be readable by screen reader  
**And** answer options should be clearly announced  
**And** progress should be communicated audibly

### Example 2: Daily Quranic Verse Feature

#### Feature: Personalized Daily Quranic Verses
#### User Story: US-025 - Receive daily Quranic verse for healing

##### Scenario 1: Daily Verse Delivery
**Given** I have an active healing journey  
**When** I open the app in the morning  
**Then** I should see a new Quranic verse relevant to my journey  
**And** the verse should include Arabic text, transliteration, and translation  
**And** I should see a brief reflection or commentary

##### Scenario 2: Verse Personalization
**Given** my assessment indicates anxiety as a primary concern  
**When** I receive my daily verse  
**Then** the verse should be relevant to finding peace and tranquility  
**And** the commentary should address anxiety from an Islamic perspective  
**And** practical application guidance should be provided

##### Scenario 3: Multiple Language Support
**Given** I have selected Urdu as my preferred language  
**When** I view the daily verse  
**Then** the translation should be in Urdu  
**And** the commentary should be in Urdu  
**And** Arabic text and transliteration should still be displayed

##### Scenario 4: Offline Access
**Given** I have no internet connection  
**When** I open the app to view my daily verse  
**Then** I should still see today's verse (pre-cached)  
**And** I should be able to read the full content offline  
**And** I should see an indicator that I'm offline

##### Scenario 5: Scholarly Authenticity
**Given** I view any Quranic verse in the app  
**When** I look for source information  
**Then** I should see the Surah and Ayah reference  
**And** I should see the translation source  
**And** I should have access to scholarly commentary sources

---

## Acceptance Criteria Checklist

### Completeness Check
- [ ] All happy path scenarios covered
- [ ] Alternative paths identified
- [ ] Error cases addressed
- [ ] Edge cases considered
- [ ] Islamic content validation included
- [ ] Cultural sensitivity addressed
- [ ] Accessibility requirements covered

### Islamic Context Check
- [ ] Islamic terminology used appropriately
- [ ] Cultural diversity considered
- [ ] Scholarly validation requirements specified
- [ ] Arabic text handling addressed
- [ ] Prayer times/Islamic calendar considered (if relevant)
- [ ] 5-layer soul model integration (if relevant)

### Technical Check
- [ ] Performance requirements specified
- [ ] Security requirements included
- [ ] Offline functionality addressed (if relevant)
- [ ] Cross-platform compatibility covered
- [ ] Integration points tested
- [ ] Data validation requirements clear

### User Experience Check
- [ ] User feedback mechanisms specified
- [ ] Error messages are helpful
- [ ] Navigation is intuitive
- [ ] Personalization is appropriate
- [ ] Help and guidance available
- [ ] User privacy respected

### Quality Check
- [ ] Criteria are testable
- [ ] Criteria are specific and measurable
- [ ] Criteria are achievable
- [ ] Criteria cover all user types
- [ ] Criteria align with user story goals

---

## Common Patterns for Islamic Features

### Pattern 1: Islamic Content Display
```
Given [Islamic content is displayed]
When [user views the content]
Then [Arabic text is properly formatted (RTL)]
And [transliteration is provided]
And [accurate translation is shown]
And [source citation is available]
```

### Pattern 2: Cultural Sensitivity
```
Given [user from specific cultural background]
When [user interacts with feature]
Then [content is culturally appropriate]
And [cultural variations are respected]
And [no cultural assumptions are made]
```

### Pattern 3: Islamic Knowledge Levels
```
Given [user has indicated Islamic knowledge level]
When [Islamic content is presented]
Then [complexity matches user's level]
And [explanations are provided as needed]
And [user can access more detailed information]
```

### Pattern 4: Privacy and Modesty
```
Given [feature involves personal information]
When [user provides or views personal data]
Then [Islamic privacy principles are respected]
And [user has control over information sharing]
And [modesty guidelines are followed]
```

### Pattern 5: Scholarly Validation
```
Given [Islamic guidance is provided]
When [user seeks verification]
Then [scholarly sources are cited]
And [validation process is transparent]
And [multiple scholarly opinions are acknowledged when relevant]
```

---

## Review and Validation Process

### Internal Review
1. **Product Manager Review**: Ensures business requirements are met
2. **Engineering Review**: Confirms technical feasibility
3. **UX Review**: Validates user experience considerations
4. **QA Review**: Ensures testability and completeness

### Islamic Content Review
1. **Islamic Scholar Review**: Validates religious accuracy
2. **Cultural Consultant Review**: Ensures cultural sensitivity
3. **Community Feedback**: Gathers input from target communities

### Accessibility Review
1. **Accessibility Expert Review**: Ensures compliance with standards
2. **User Testing**: Tests with users who have disabilities
3. **Assistive Technology Testing**: Tests with screen readers, etc.

---

## Best Practices

### Writing Effective Criteria
1. **Be Specific**: Avoid vague terms like "should work well"
2. **Be Testable**: Each criterion should be verifiable
3. **Be Complete**: Cover all important scenarios
4. **Be Realistic**: Ensure criteria are achievable
5. **Be User-Focused**: Write from user's perspective

### Islamic Context Considerations
1. **Respect Diversity**: Consider different Islamic practices
2. **Ensure Authenticity**: Validate all Islamic content
3. **Cultural Sensitivity**: Respect cultural variations
4. **Scholarly Input**: Include Islamic scholars in validation
5. **Community Feedback**: Gather input from Muslim communities

### Technical Considerations
1. **Performance**: Specify measurable performance criteria
2. **Security**: Include appropriate security measures
3. **Accessibility**: Ensure inclusive design
4. **Offline**: Consider offline scenarios for Islamic content
5. **Integration**: Test all integration points

---

## Review and Updates

This template should be updated:
- **After each sprint retrospective** based on testing feedback
- **When new Islamic content guidelines** are established
- **When accessibility standards** are updated
- **When new technical requirements** emerge

**Last Updated**: [Date]
**Next Review**: [Date]