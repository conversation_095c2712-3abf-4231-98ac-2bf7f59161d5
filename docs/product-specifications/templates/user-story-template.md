# User Story Template
## Q<PERSON>b Healing Islamic Wellness Platform

---

## Purpose
This template provides a standardized format for writing user stories that incorporate Islamic wellness context, the 5-layer soul model, and cultural considerations specific to the Qalb Healing platform.

---

## Standard User Story Format

### Basic Structure
**As a** [user type/persona]  
**I want** [functionality/capability]  
**So that** [benefit/value/outcome]

### Enhanced Structure for Islamic Context
**As a** [user type/persona with Islamic context]  
**I want** [functionality that respects Islamic principles]  
**So that** [benefit that aligns with Islamic wellness goals]  
**And** [additional Islamic spiritual benefit]

---

## User Story Template

### Story ID: [US-XXX]

#### Story Title
[Descriptive title that captures the essence of the story]

#### User Story
**As a** [specific user persona]  
**I want** [specific functionality]  
**So that** [primary benefit]  
**And** [Islamic spiritual benefit, if applicable]

#### Story Details

##### User Context
- **Persona**: [Primary persona this story serves]
- **Islamic Practice Level**: [Practicing, Learning, Cultural, New Muslim]
- **Soul Layer Focus**: [Jism, Nafs, Aql, Qalb, Ruh - which layer(s) this story primarily addresses]
- **Cultural Context**: [Relevant cultural considerations]
- **Usage Scenario**: [When/where the user would need this functionality]

##### Acceptance Criteria
**Given** [initial condition/context]  
**When** [action is performed]  
**Then** [expected outcome]

**Given** [alternative condition]  
**When** [alternative action]  
**Then** [alternative outcome]

[Additional Given-When-Then scenarios as needed]

##### Islamic Content Requirements
- **Quranic References**: [Any Quranic verses that should be included]
- **Hadith References**: [Relevant Hadith or Prophetic traditions]
- **Scholarly Validation**: [Level of Islamic scholarly review needed]
- **Cultural Sensitivity**: [Specific cultural considerations]
- **Language Requirements**: [Arabic text, translations, transliterations needed]

##### Technical Requirements
- **Platform**: [Mobile app, web, both]
- **Performance**: [Response time, load time requirements]
- **Offline Capability**: [What should work offline]
- **Integration**: [Other features/systems this connects to]
- **Security**: [Privacy, data protection requirements]

##### Definition of Done
- [ ] Functionality implemented and tested
- [ ] Islamic content validated by scholars
- [ ] Cultural sensitivity reviewed
- [ ] Accessibility requirements met
- [ ] Performance requirements met
- [ ] User testing completed with target persona
- [ ] Documentation updated

#### Story Metadata

##### Priority and Estimation
- **Priority**: [High/Medium/Low]
- **Story Points**: [Fibonacci scale: 1, 2, 3, 5, 8, 13, 21]
- **Business Value**: [High/Medium/Low]
- **Risk Level**: [High/Medium/Low]
- **Complexity**: [High/Medium/Low]

##### Dependencies
- **Depends On**: [Other stories that must be completed first]
- **Blocks**: [Stories that are blocked by this one]
- **Related Stories**: [Stories that are related but not dependent]

##### Epic and Theme
- **Epic**: [Which epic this story belongs to]
- **Theme**: [Broader theme or initiative]
- **Feature**: [Specific feature this story contributes to]

#### Validation and Testing

##### User Testing Approach
- **Test Users**: [Which personas should test this]
- **Test Scenarios**: [Key scenarios to test]
- **Success Criteria**: [How to measure success]
- **Islamic Content Validation**: [How Islamic content will be validated]

##### Metrics and Analytics
- **Success Metrics**: [How success will be measured]
- **Analytics Events**: [What events should be tracked]
- **A/B Testing**: [If A/B testing is planned]

#### Notes and Assumptions
- **Assumptions**: [Key assumptions made]
- **Constraints**: [Known constraints or limitations]
- **Open Questions**: [Questions that need to be resolved]
- **Research Needed**: [Additional research required]

---

## User Story Examples

### Example 1: Assessment Feature

#### Story ID: US-001

#### Story Title
Islamic Mental Health Assessment - Initial Screening

#### User Story
**As a** practicing Muslim professional dealing with work stress  
**I want** to complete an Islamic-based mental health assessment  
**So that** I can understand my current state across the 5 layers of my soul  
**And** receive personalized Islamic healing recommendations

#### Story Details

##### User Context
- **Persona**: Aisha - The Seeking Professional
- **Islamic Practice Level**: Practicing
- **Soul Layer Focus**: All layers (comprehensive assessment)
- **Cultural Context**: Modern Muslim balancing career and faith
- **Usage Scenario**: Evening after work, seeking private guidance

##### Acceptance Criteria
**Given** I am a new user who has completed basic onboarding  
**When** I start the Islamic mental health assessment  
**Then** I should see questions that respect Islamic terminology and concepts

**Given** I am completing the assessment  
**When** I answer questions about my spiritual state  
**Then** the questions should reference Islamic concepts like Taqwa, Sabr, and Tawakkul

**Given** I complete the assessment  
**When** I submit my responses  
**Then** I should receive results mapped to the 5-layer soul model with relevant Quranic verses

##### Islamic Content Requirements
- **Quranic References**: Verses about self-reflection and spiritual health
- **Hadith References**: Prophetic guidance on mental and spiritual wellness
- **Scholarly Validation**: High - assessment framework needs scholarly approval
- **Cultural Sensitivity**: Questions must be culturally appropriate across Muslim communities
- **Language Requirements**: Arabic terms with English explanations and transliterations

##### Technical Requirements
- **Platform**: Mobile app primary, web secondary
- **Performance**: Assessment should load within 2 seconds
- **Offline Capability**: Assessment should be completable offline
- **Integration**: Results integrate with journey recommendations and progress tracking
- **Security**: High - personal mental health data requires encryption

##### Definition of Done
- [x] Assessment questions developed and validated
- [x] Islamic content reviewed by scholars
- [x] Cultural sensitivity review completed
- [x] Mobile and web interfaces implemented
- [x] Results algorithm implemented
- [x] User testing completed with target personas
- [x] Privacy and security measures implemented

#### Story Metadata

##### Priority and Estimation
- **Priority**: High
- **Story Points**: 13
- **Business Value**: High
- **Risk Level**: Medium
- **Complexity**: High

##### Dependencies
- **Depends On**: US-000 (User onboarding), US-002 (Islamic content framework)
- **Blocks**: US-010 (Personalized recommendations), US-015 (Progress tracking)
- **Related Stories**: US-003 (Crisis detection), US-005 (Journey creation)

##### Epic and Theme
- **Epic**: Islamic Mental Health Assessment
- **Theme**: Core Platform Functionality
- **Feature**: Assessment and Diagnosis

### Example 2: Content Feature

#### Story ID: US-025

#### Story Title
Daily Quranic Verse for Healing

#### User Story
**As a** Muslim seeking daily spiritual nourishment  
**I want** to receive a personalized Quranic verse each day based on my current healing journey  
**So that** I can reflect on Allah's guidance relevant to my situation  
**And** strengthen my connection with the Quran in my healing process

#### Story Details

##### User Context
- **Persona**: Multiple personas (primary: Aisha, secondary: Omar)
- **Islamic Practice Level**: Practicing to Learning
- **Soul Layer Focus**: Qalb (heart) and Ruh (spirit)
- **Cultural Context**: Universal across Muslim cultures
- **Usage Scenario**: Morning routine, daily reflection time

##### Acceptance Criteria
**Given** I have completed my assessment and have an active healing journey  
**When** I open the app each day  
**Then** I should see a new Quranic verse relevant to my current healing focus

**Given** I receive a daily verse  
**When** I view the verse  
**Then** I should see Arabic text, transliteration, translation, and brief reflection

**Given** I want to reflect on the verse  
**When** I tap on the verse  
**Then** I should see expanded commentary and practical application guidance

##### Islamic Content Requirements
- **Quranic References**: Curated verses for healing and spiritual growth
- **Hadith References**: Related Prophetic traditions where applicable
- **Scholarly Validation**: Medium - verse selection and commentary need review
- **Cultural Sensitivity**: Translations appropriate for diverse Muslim communities
- **Language Requirements**: Arabic, transliteration, multiple language translations

##### Technical Requirements
- **Platform**: Mobile app with push notification capability
- **Performance**: Content should load instantly from cache
- **Offline Capability**: Daily verses should be available offline
- **Integration**: Connects with journey progress and reflection journal
- **Security**: Standard - no personal data in verse content

##### Definition of Done
- [ ] Verse curation algorithm implemented
- [ ] Content database with scholarly-approved verses created
- [ ] Multi-language support implemented
- [ ] Daily notification system implemented
- [ ] Reflection and journaling integration completed
- [ ] User testing with diverse cultural groups completed

#### Story Metadata

##### Priority and Estimation
- **Priority**: Medium
- **Story Points**: 8
- **Business Value**: High
- **Risk Level**: Low
- **Complexity**: Medium

##### Dependencies
- **Depends On**: US-001 (Assessment), US-010 (Journey framework)
- **Blocks**: None
- **Related Stories**: US-030 (Reflection journal), US-035 (Progress tracking)

##### Epic and Theme
- **Epic**: Daily Islamic Content
- **Theme**: Spiritual Nourishment
- **Feature**: Quranic Integration

---

## Story Writing Guidelines

### Islamic Context Guidelines
1. **Always consider the 5-layer soul model** when writing stories
2. **Include Islamic terminology** appropriately and with explanations
3. **Respect cultural diversity** within the Muslim community
4. **Ensure scholarly validation** requirements are specified
5. **Consider prayer times and Islamic calendar** in timing-related stories

### Technical Guidelines
1. **Specify platform requirements** clearly
2. **Include performance expectations** for user experience
3. **Consider offline scenarios** for Islamic content
4. **Address privacy and security** especially for personal data
5. **Plan for accessibility** across different user abilities

### User Experience Guidelines
1. **Write from the user's perspective** using their language
2. **Focus on user value** rather than system functionality
3. **Consider the emotional context** of Islamic wellness
4. **Include cultural sensitivity** requirements
5. **Plan for user testing** with appropriate personas

### Quality Guidelines
1. **Make stories testable** with clear acceptance criteria
2. **Keep stories independent** and deliverable
3. **Size stories appropriately** for sprint completion
4. **Include all necessary context** for development team
5. **Plan for Islamic content validation** in timeline

---

## Story Review Checklist

### Content Review
- [ ] Story follows standard format
- [ ] User value is clearly articulated
- [ ] Islamic context is appropriately included
- [ ] Cultural sensitivity is addressed
- [ ] Acceptance criteria are testable

### Technical Review
- [ ] Technical requirements are specified
- [ ] Performance expectations are clear
- [ ] Security requirements are included
- [ ] Integration points are identified
- [ ] Platform requirements are specified

### Islamic Content Review
- [ ] Islamic content requirements are specified
- [ ] Scholarly validation level is appropriate
- [ ] Cultural considerations are included
- [ ] Language requirements are clear
- [ ] Soul layer focus is identified

### Process Review
- [ ] Priority is assigned appropriately
- [ ] Story points are estimated
- [ ] Dependencies are identified
- [ ] Epic/theme assignment is correct
- [ ] Definition of done is complete

---

## Review and Updates

This template should be updated:
- **After each sprint retrospective** based on team feedback
- **When new Islamic content guidelines** are established
- **When new personas are created** or existing ones are updated
- **When technical architecture changes** affect story requirements

**Last Updated**: [Date]
**Next Review**: [Date]