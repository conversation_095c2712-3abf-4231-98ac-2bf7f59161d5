# 🚀 Deployment Guide - <PERSON><PERSON><PERSON> Healing

This guide covers deploying the Qalb Healing platform to production environments.

## 📋 Prerequisites

### Required Services

1. **Database**: Supabase (PostgreSQL)
2. **Backend Hosting**: Railway, Render, or AWS
3. **AI Service**: Railway, Render, or Google Cloud Run
4. **Mobile App**: Expo Application Services (EAS)
5. **CDN**: Cloudflare (optional)
6. **Monitoring**: Sentry (optional)

### Environment Setup

1. **Node.js**: 18+ LTS
2. **Python**: 3.10+
3. **Docker**: Latest stable
4. **Expo CLI**: Latest

## 🗄️ Database Setup (Supabase)

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Note down the project URL and anon key

### 2. Run Database Migrations

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref your-project-ref

# Run migrations
supabase db push
```

### 3. Set Up Database Schema

Execute the SQL files in order:

```bash
# From apps/backend/src/db/schemas/
1. symptoms.sql
2. content.sql
3. journeys.sql
4. analytics.sql
5. ruqya.sql
```

### 4. Configure Row Level Security (RLS)

Enable RLS for all tables and set appropriate policies for user data protection.

## 🖥️ Backend Deployment

### Option 1: Railway

1. **Connect Repository**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login and deploy
   railway login
   railway init
   railway up
   ```

2. **Environment Variables**
   ```bash
   railway variables set SUPABASE_URL=your_url
   railway variables set SUPABASE_ANON_KEY=your_key
   railway variables set JWT_SECRET=your_secret
   ```

### Option 2: Render

1. **Create Web Service**
   - Connect GitHub repository
   - Set build command: `npm run build:backend`
   - Set start command: `npm run start:backend`

2. **Environment Variables**
   Set in Render dashboard:
   - `SUPABASE_URL`
   - `SUPABASE_ANON_KEY`
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `JWT_SECRET`
   - `ENCRYPTION_KEY`

### Option 3: Docker Deployment

```dockerfile
# Dockerfile for backend
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY apps/backend/package*.json ./apps/backend/

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY apps/backend ./apps/backend
COPY libs ./libs

# Build application
RUN npm run build:backend

EXPOSE 3000

CMD ["npm", "run", "start:backend"]
```

## 🤖 AI Service Deployment

### Option 1: Railway (Python)

```bash
# Create railway.toml
[build]
builder = "NIXPACKS"

[deploy]
startCommand = "cd apps/ai-service && python ai_service/main.py"
```

### Option 2: Google Cloud Run

```dockerfile
# Dockerfile for AI service
FROM python:3.10-slim

WORKDIR /app

# Copy requirements
COPY apps/ai-service/requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY apps/ai-service .

# Expose port
EXPOSE 8000

# Start application
CMD ["python", "ai_service/main.py"]
```

Deploy to Cloud Run:
```bash
# Build and deploy
gcloud run deploy qalb-healing-ai \
  --source apps/ai-service \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Option 3: Render (Python)

1. **Create Web Service**
   - Set build command: `cd apps/ai-service && pip install -r requirements.txt`
   - Set start command: `cd apps/ai-service && python ai_service/main.py`

## 📱 Mobile App Deployment

### 1. Configure EAS

```bash
# Install EAS CLI
npm install -g eas-cli

# Login to Expo
eas login

# Configure project
cd apps/mobile-app
eas build:configure
```

### 2. Update App Configuration

```json
// apps/mobile-app/app.json
{
  "expo": {
    "name": "Qalb Healing",
    "slug": "qalb-healing",
    "version": "1.0.0",
    "extra": {
      "apiUrl": "https://your-backend-url.com/api",
      "aiServiceUrl": "https://your-ai-service-url.com"
    }
  }
}
```

### 3. Build and Submit

```bash
# Build for iOS and Android
eas build --platform all

# Submit to app stores
eas submit --platform ios
eas submit --platform android
```

## 🔧 Environment Variables

### Backend (.env)

```bash
# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Security
JWT_SECRET=your_jwt_secret_min_32_chars
ENCRYPTION_KEY=your_encryption_key_32_chars

# External Services
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_key

# Server
PORT=3000
NODE_ENV=production
```

### AI Service (.env)

```bash
# AI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000

# Service Configuration
AI_SERVICE_PORT=8000
LOG_LEVEL=INFO

# Database (if needed)
DATABASE_URL=your_database_url
```

## 🔍 Health Checks

### Backend Health Check

```bash
GET /health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": {
    "database": "connected",
    "redis": "connected"
  }
}
```

### AI Service Health Check

```bash
GET /health
```

## 📊 Monitoring

### 1. Sentry Setup

```bash
# Install Sentry
npm install @sentry/node @sentry/tracing

# Configure in backend
import * as Sentry from "@sentry/node";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
});
```

### 2. Logging

- **Backend**: Winston with structured logging
- **AI Service**: Python logging with JSON format
- **Mobile App**: Expo logging and crash reporting

## 🔒 Security Checklist

- [ ] HTTPS enabled for all services
- [ ] Environment variables secured
- [ ] Database RLS policies configured
- [ ] API rate limiting enabled
- [ ] CORS properly configured
- [ ] JWT tokens with proper expiration
- [ ] Input validation on all endpoints
- [ ] SQL injection protection
- [ ] XSS protection headers

## 🚦 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy Backend

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm run test:backend
        
      - name: Build
        run: npm run build:backend
        
      - name: Deploy to Railway
        run: railway up --service backend
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
```

## 🤲 Final Du'a

*"Rabbana atina fi'd-dunya hasanatan wa fi'l-akhirati hasanatan wa qina 'adhab an-nar"*

May Allah (SWT) bless this deployment and make it a source of healing for the ummah.

---

For support, please refer to the troubleshooting guide or contact the development team.
