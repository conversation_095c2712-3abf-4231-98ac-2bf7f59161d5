{"version": "2.0.0", "tasks": [{"label": "Start Backend (Debug Mode)", "type": "shell", "command": "npm run serve:backend:debug", "isBackground": true, "problemMatcher": {"owner": "typescript", "pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "Debugger listening on"}}, "presentation": {"reveal": "always", "panel": "new", "group": "<PERSON><PERSON><PERSON>"}}, {"label": "Start AI Service (Debug Mode)", "type": "shell", "command": "npm run start:ai-service:debug", "isBackground": true, "problemMatcher": {"owner": "python", "pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "<PERSON><PERSON><PERSON> running on"}}, "presentation": {"reveal": "always", "panel": "new", "group": "<PERSON><PERSON><PERSON>"}}, {"label": "Start Mobile App (Debug Mode)", "type": "shell", "command": "npm run serve:mobile-app-v3:debug", "isBackground": true, "problemMatcher": {"owner": "javascript", "pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "Metro waiting on"}}, "presentation": {"reveal": "always", "panel": "new", "group": "<PERSON><PERSON><PERSON>"}}, {"label": "Start All Services", "dependsOn": ["Start Backend (Debug Mode)", "Start AI Service (Debug Mode)", "Start Mobile App (Debug Mode)"], "problemMatcher": []}]}