{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "attach",
      "address": "localhost",
      "port": 9229,
      "restart": true,
      "skipFiles": [
        "<node_internals>/**"
      ],
      "outFiles": [
        "${workspaceFolder}/apps/backend/dist/**/*.js"
      ],
      "sourceMaps": true,
      "presentation": {
        "hidden": false,
        "group": "Qalb Healing",
        "order": 1
      }
    },
    {
      "name": "Debug AI Service",
      "type": "python",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5678
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/apps/ai-service",
          "remoteRoot": "."
        }
      ],
      "justMyCode": false,
      "presentation": {
        "hidden": false,
        "group": "Qalb Healing",
        "order": 2
      }
    },
    {
      "name": "Debug Mobile App (Expo)",
      "type": "reactnative",
      "request": "attach",
      "platform": "expo",
      "presentation": {
        "hidden": false,
        "group": "Qalb Healing",
        "order": 3
      }
    },
    {
      "name": "Debug Mobile App (iOS)",
      "type": "reactnative",
      "request": "attach",
      "platform": "ios",
      "sourceMaps": true,
      "presentation": {
        "hidden": false,
        "group": "Qalb Healing",
        "order": 4
      }
    },
    {
      "name": "Debug Mobile App (Android)",
      "type": "reactnative",
      "request": "attach",
      "platform": "android",
      "sourceMaps": true,
      "presentation": {
        "hidden": false,
        "group": "Qalb Healing",
        "order": 5
      }
    }
  ],
  "compounds": [
    {
      "name": "Debug All",
      "configurations": ["Debug Backend", "Debug AI Service", "Debug Mobile App (Expo)"],
      "presentation": {
        "hidden": false,
        "group": "Compound",
        "order": 1
      }
    },
    {
      "name": "Backend + AI",
      "configurations": ["Debug Backend", "Debug AI Service"],
      "presentation": {
        "hidden": false,
        "group": "Compound",
        "order": 2
      }
    }
  ]
}
