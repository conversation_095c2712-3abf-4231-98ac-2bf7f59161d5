/**
 * Test script to verify the onboarding session fix
 * This script simulates the issue and tests the fix
 */

const API_BASE_URL = 'http://localhost:3000'; // Adjust as needed

async function testOnboardingFix() {
  console.log('🧪 Testing Onboarding Session Fix...\n');

  // Mock user token - replace with a real token for testing
  const mockToken = 'your-test-token-here';
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${mockToken}`,
  };

  try {
    console.log('1️⃣ Testing initial onboarding start...');
    
    // First, try to start onboarding
    const startResponse = await fetch(`${API_BASE_URL}/api/onboarding/start`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        deviceInfo: {
          platform: 'web',
          browser: 'test'
        }
      })
    });

    const startData = await startResponse.json();
    console.log('Start response status:', startResponse.status);
    console.log('Start response:', JSON.stringify(startData, null, 2));

    if (startResponse.status === 409 && startData.status === 'already_completed') {
      console.log('✅ SUCCESS: Onboarding already completed detected correctly!');
      console.log('📋 Available actions:', startData.data.actions);
      
      // Test restart functionality
      console.log('\n2️⃣ Testing onboarding restart...');
      const restartResponse = await fetch(`${API_BASE_URL}/api/onboarding/restart`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          deviceInfo: {
            platform: 'web',
            browser: 'test'
          }
        })
      });

      const restartData = await restartResponse.json();
      console.log('Restart response status:', restartResponse.status);
      console.log('Restart response:', JSON.stringify(restartData, null, 2));

      if (restartResponse.status === 201 && restartData.status === 'success') {
        console.log('✅ SUCCESS: Onboarding restart works correctly!');
      } else {
        console.log('❌ FAILED: Onboarding restart did not work as expected');
      }

    } else if (startResponse.status === 201 && startData.status === 'success') {
      console.log('✅ SUCCESS: New onboarding session started correctly!');
      console.log('📝 Session ID:', startData.data.session?.sessionId);
      console.log('❓ First question:', startData.data.question?.title);
    } else {
      console.log('❌ UNEXPECTED: Received unexpected response');
    }

  } catch (error) {
    console.error('❌ ERROR during testing:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('💡 TIP: Make sure the backend server is running on', API_BASE_URL);
    }
    
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      console.log('💡 TIP: Update the mockToken variable with a valid authentication token');
    }
  }

  console.log('\n🏁 Test completed!');
}

// Instructions for manual testing
console.log(`
📋 MANUAL TESTING INSTRUCTIONS:

1. Make sure your backend server is running
2. Update the API_BASE_URL if needed (currently: ${API_BASE_URL})
3. Replace 'your-test-token-here' with a valid user token
4. Run this script: node test-onboarding-fix.js

Expected behavior:
- If user has completed onboarding: Should get 409 status with 'already_completed'
- If user hasn't completed onboarding: Should get 201 status with first question
- Restart should always work and start fresh onboarding

🌐 You can also test manually by:
1. Complete onboarding for a user
2. Visit /onboarding URL again
3. Should see completion screen instead of starting from ruqya question
`);

// Run the test
testOnboardingFix();