# 🤝 Contributing to <PERSON><PERSON><PERSON> <PERSON><PERSON>

<PERSON><PERSON><PERSON> for your interest in contributing to Qalb Healing! This project is developed for the service of <PERSON> (SWT) and the Muslim ummah.

## 🕌 Islamic Guidelines

### Core Principles

1. **Authenticity**: All Islamic content must be verified by qualified scholars
2. **Accuracy**: Quranic verses and Hadith must be properly cited and authentic
3. **Respect**: Maintain the sanctity of Islamic texts and teachings
4. **Benefit**: Contributions should genuinely benefit the Muslim community

### Content Requirements

- **Quranic Verses**: Include Arabic text, transliteration, and verified translation
- **Hadith**: Provide authentic chain of narration and source
- **Islamic Practices**: Ensure practices align with Sunnah and scholarly consensus
- **Scholarly Review**: Major content additions should be reviewed by qualified scholars

## 🛠️ Technical Guidelines

### Code Standards

- **TypeScript**: Use strict typing for all new code
- **Testing**: Include tests for new features
- **Documentation**: Update relevant documentation
- **Linting**: Follow ESLint and Prettier configurations

### Commit Messages

Use conventional commit format:
```
feat(mobile): add dhikr counter component
fix(backend): resolve authentication issue
docs(readme): update installation instructions
```

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

## 🚀 Development Setup

### 1. Fork and Clone

```bash
git clone https://github.com/your-username/qalb-healing-workspace.git
cd qalb-healing-workspace
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set Up Environment

```bash
# Copy environment templates
cp apps/backend/.env.example apps/backend/.env
cp apps/ai-service/.env.example apps/ai-service/.env

# Set up AI service conda environment
conda create -n qalb-healing-ai python=3.10 -y
cd apps/ai-service && ./run.sh
```

### 4. Run Tests

```bash
npm run test
```

## 📝 Pull Request Process

### Before Submitting

1. **Test**: Ensure all tests pass
2. **Lint**: Run linting and fix any issues
3. **Documentation**: Update relevant docs
4. **Islamic Review**: For content changes, indicate scholarly verification

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Islamic content addition

## Islamic Content Verification
- [ ] Quranic verses verified with authentic sources
- [ ] Hadith checked for authenticity
- [ ] Scholarly review completed (if applicable)

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new features

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
```

## 🎯 Areas for Contribution

### High Priority

1. **Islamic Content Expansion**
   - More Names of Allah with benefits
   - Additional Quranic verses for healing
   - Authentic Hadith compilation

2. **Mobile App Features**
   - Prayer time integration
   - Qibla direction
   - Islamic calendar

3. **AI Service Enhancement**
   - Better Islamic context understanding
   - Multilingual support (Arabic, Urdu, etc.)

4. **Testing Coverage**
   - Unit tests for all components
   - Integration tests
   - E2E testing

### Medium Priority

1. **Performance Optimization**
2. **Accessibility Improvements**
3. **Internationalization**
4. **Documentation Enhancement**

## 🔍 Code Review Process

### Reviewers Will Check

1. **Islamic Authenticity**: Content accuracy and appropriateness
2. **Code Quality**: Adherence to standards and best practices
3. **Testing**: Adequate test coverage
4. **Documentation**: Clear and updated docs

### Review Criteria

- ✅ Islamic content is authentic and properly sourced
- ✅ Code follows TypeScript best practices
- ✅ Tests are comprehensive and passing
- ✅ Documentation is clear and updated
- ✅ No breaking changes without proper migration

## 🤲 Du'a for Contributors

*"Rabbana taqabbal minna innaka anta as-samee'u al-'aleem"*

*"Our Lord, accept this from us. Indeed, You are the Hearing, the Knowing."* (Quran 2:127)

---

May Allah (SWT) reward all contributors for their efforts in serving the ummah. Barakallahu feekum!
