const nxPreset = require('@nx/jest/preset').default;

// The standard pattern for React Native projects.
// We want to ensure these modules ARE transformed by Babel.
const reactNativeTransformIgnorePatterns = [
  "node_modules/(?!((jest-)?react-native|@react-native|@react-native(-community)?|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg))"
];

module.exports = { 
  ...nxPreset,
  // If nxPreset already has transformIgnorePatterns, we might need to merge carefully.
  // For now, this will override it if it exists, or add it if it doesn't.
  // A more robust merge would check typeof nxPreset.transformIgnorePatterns
  transformIgnorePatterns: reactNativeTransformIgnorePatterns,
};
